{"version": 3, "sources": ["../../src/server/get-app-route-from-entrypoint.ts"], "sourcesContent": ["import matchBundle from './match-bundle'\n\n// matches app/:path*.js\nconst APP_ROUTE_NAME_REGEX = /^app[/\\\\](.*)$/\n\nexport default function getAppRouteFromEntrypoint(entryFile: string) {\n  const pagePath = matchBundle(APP_ROUTE_NAME_REGEX, entryFile)\n  if (typeof pagePath === 'string' && !pagePath) {\n    return '/'\n  }\n\n  if (!pagePath) {\n    return null\n  }\n\n  return pagePath\n}\n"], "names": ["getAppRouteFromEntrypoint", "APP_ROUTE_NAME_REGEX", "entryFile", "pagePath", "matchBundle"], "mappings": ";;;;+BAKA;;;eAAwBA;;;oEALA;;;;;;AAExB,wBAAwB;AACxB,MAAMC,uBAAuB;AAEd,SAASD,0BAA0BE,SAAiB;IACjE,MAAMC,WAAWC,IAAAA,oBAAW,EAACH,sBAAsBC;IACnD,IAAI,OAAOC,aAAa,YAAY,CAACA,UAAU;QAC7C,OAAO;IACT;IAEA,IAAI,CAACA,UAAU;QACb,OAAO;IACT;IAEA,OAAOA;AACT"}