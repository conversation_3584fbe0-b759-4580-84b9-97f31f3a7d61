{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/terminal/editor-link.tsx"], "sourcesContent": ["import { useOpenInEditor } from '../../utils/use-open-in-editor'\n\ntype EditorLinkProps = {\n  file: string\n  isSourceFile: boolean\n  location?: {\n    line: number\n    column: number\n  }\n}\nexport function EditorLink({ file, location }: EditorLinkProps) {\n  const open = useOpenInEditor({\n    file,\n    lineNumber: location?.line ?? 1,\n    column: location?.column ?? 0,\n  })\n\n  return (\n    <div\n      data-with-open-in-editor-link\n      data-with-open-in-editor-link-import-trace\n      tabIndex={10}\n      role={'link'}\n      onClick={open}\n      title={'Click to open in your editor'}\n    >\n      {file}\n      {location ? `:${location.line}:${location.column}` : null}\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      >\n        <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n        <polyline points=\"15 3 21 3 21 9\"></polyline>\n        <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n      </svg>\n    </div>\n  )\n}\n\nexport const EDITOR_LINK_STYLES = `\n  [data-with-open-in-editor-link] svg {\n    width: auto;\n    height: var(--size-14);\n    margin-left: 8px;\n  }\n  [data-with-open-in-editor-link] {\n    cursor: pointer;\n  }\n  [data-with-open-in-editor-link]:hover {\n    text-decoration: underline dotted;\n  }\n  [data-with-open-in-editor-link-import-trace] {\n    margin-left: 16px;\n  }\n`\n"], "names": ["EDITOR_LINK_STYLES", "EditorLink", "file", "location", "open", "useOpenInEditor", "lineNumber", "line", "column", "div", "data-with-open-in-editor-link", "data-with-open-in-editor-link-import-trace", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2"], "mappings": ";;;;;;;;;;;;;;;IA6CaA,kBAAkB;eAAlBA;;IAnCGC,UAAU;eAAVA;;;;iCAVgB;AAUzB,SAASA,WAAW,KAAmC;IAAnC,IAAA,EAAEC,IAAI,EAAEC,QAAQ,EAAmB,GAAnC;QAGXA,gBACJA;IAHV,MAAMC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BH;QACAI,YAAYH,CAAAA,iBAAAA,4BAAAA,SAAUI,IAAI,YAAdJ,iBAAkB;QAC9BK,QAAQL,CAAAA,mBAAAA,4BAAAA,SAAUK,MAAM,YAAhBL,mBAAoB;IAC9B;IAEA,qBACE,sBAACM;QACCC,+BAA6B;QAC7BC,4CAA0C;QAC1CC,UAAU;QACVC,MAAM;QACNC,SAASV;QACTW,OAAO;;YAENb;YACAC,WAAW,AAAC,MAAGA,SAASI,IAAI,GAAC,MAAGJ,SAASK,MAAM,GAAK;0BACrD,sBAACQ;gBACCC,OAAM;gBACNC,SAAQ;gBACRC,MAAK;gBACLC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;kCAEf,qBAACC;wBAAKC,GAAE;;kCACR,qBAACC;wBAASC,QAAO;;kCACjB,qBAACpB;wBAAKqB,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;;;;;;AAIzC;AAEO,MAAM/B,qBAAsB"}