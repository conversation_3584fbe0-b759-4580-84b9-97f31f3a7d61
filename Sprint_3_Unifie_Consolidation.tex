\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage[french]{babel}
\usepackage{geometry}
\usepackage{array}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{colortbl}

\geometry{a4paper, margin=2cm}

\title{Sprint 3 Unifié - Consolidation Sprint 3 \& 4}
\author{Système de Gestion de Factures et Devis}
\date{\today}

\begin{document}

\maketitle

\section{Vue d'ensemble de la Consolidation}

La fusion du Sprint 3 et Sprint 4 en un seul Sprint 3 Unifié permet d'optimiser le développement en regroupant les fonctionnalités interdépendantes et en éliminant les redondances. Cette approche réduit la durée totale de 56 jours à 45 jours (-11 jours d'optimisation).

\section{Tableau 1 : Gestion des Données de Base et Configuration}

\begin{table}[h]
\centering
\caption{Thème A - Données de Base \& Configuration Système}
\begin{tabular}{|p{2cm}|p{9cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{lightgray}
\textbf{ID Stories} & \textbf{Description Consolidée} & \textbf{Importance} & \textbf{Durée} \\
\hline
3.1-4.2 & Gestion produits/services + Configuration système et paramètres sécurité & Élevée & 7j \\
\hline
3.2-4.3 & Gestion clients + Paramètres entreprise et aspects fiscaux & Élevée & 6j \\
\hline
4.4 & Templates standard/moderne avec options configurables & Élevée & 4j \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Thème A:}} & \textbf{17j} \\
\hline
\end{tabular}
\end{table}

\subsection{Description Textuelle - Thème A}

\textbf{Objectif :} Établir les fondations complètes du système avec gestion des données de base et configuration avancée intégrée.

\textbf{Optimisations réalisées :}
\begin{itemize}
\item Fusion gestion produits + configuration système (économie 2j)
\item Intégration gestion clients + paramètres entreprise (économie 2j)
\item Développement parallèle des templates avec les configurations
\end{itemize}

\textbf{Scénario consolidé - Gestion Produits \& Configuration (Stories 3.1 + 4.2) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item L'utilisateur accède au module "Gestion Produits \& Configuration"
\item Le système affiche une interface unifiée avec deux onglets :
   \begin{itemize}
   \item \textbf{Catalogue Produits :} CRUD complet avec catégories, prix, stock
   \item \textbf{Configuration Système :} Paramètres sécurité, options globales
   \end{itemize}
\item Les modifications de configuration impactent automatiquement l'affichage des produits
\item Le système synchronise les paramètres fiscaux avec les calculs produits
\item Validation croisée entre données produits et règles de configuration
\end{enumerate}

\section{Tableau 2 : Abonnements et Paiements Intégrés}

\begin{table}[h]
\centering
\caption{Thème B - Abonnements \& Paiements Unifiés}
\begin{tabular}{|p{2cm}|p{9cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{lightgray}
\textbf{ID Stories} & \textbf{Description Consolidée} & \textbf{Importance} & \textbf{Durée} \\
\hline
3.3-3.4 & Création et surveillance automatique des abonnements & Élevée & 5j \\
\hline
3.5-4.1 & Consultation statut + Finalisation gestion paiements & Élevée & 4j \\
\hline
3.6-3.7-3.8 & Gestion paiements multi-rôles optimisée & Élevée & 6j \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Thème B:}} & \textbf{15j} \\
\hline
\end{tabular}
\end{table}

\subsection{Description Textuelle - Thème B}

\textbf{Objectif :} Créer un système financier complet intégrant abonnements et paiements avec surveillance automatique.

\textbf{Optimisations réalisées :}
\begin{itemize}
\item Fusion création + surveillance abonnements (économie 2j)
\item Intégration consultation + finalisation paiements (économie 2j)
\item Mutualisation des interfaces de paiement multi-rôles (économie 3j)
\end{itemize}

\textbf{Scénario consolidé - Abonnements \& Surveillance (Stories 3.3 + 3.4) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item L'administrateur accède au module "Abonnements Intégrés"
\item Le système affiche un tableau de bord unifié avec :
   \begin{itemize}
   \item Création d'abonnements avec durées variables
   \item Surveillance automatique en temps réel
   \item Alertes échelonnées (30j, 15j, 7j, 1j)
   \item Actions automatiques de suspension
   \end{itemize}
\item La création d'abonnement active automatiquement la surveillance
\item Le système génère des rapports consolidés abonnements + paiements
\end{enumerate}

\section{Tableau 3 : Livraisons et Logistique Complète}

\begin{table}[h]
\centering
\caption{Thème C - Gestion Livraisons \& Logistique}
\begin{tabular}{|p{2cm}|p{9cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{lightgray}
\textbf{ID Stories} & \textbf{Description Consolidée} & \textbf{Importance} & \textbf{Durée} \\
\hline
4.5-4.6 & Gestion livreurs + Statistiques et disponibilité intégrées & Élevée & 5j \\
\hline
4.7-4.8 & Bons de livraison + Suivi statut et signatures & Élevée & 5j \\
\hline
Nouveau & Intégration complète Factures → Livraisons → Paiements & Élevée & 3j \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Thème C:}} & \textbf{13j} \\
\hline
\end{tabular}
\end{table}

\subsection{Description Textuelle - Thème C}

\textbf{Objectif :} Implémenter un système logistique complet avec workflow intégré factures-livraisons-paiements.

\textbf{Optimisations réalisées :}
\begin{itemize}
\item Fusion gestion + statistiques livreurs (économie 1j)
\item Intégration bons de livraison + suivi (économie 1j)
\item Ajout workflow complet pour cohérence système (valeur ajoutée)
\end{itemize}

\textbf{Scénario consolidé - Workflow Complet (Nouvelle fonctionnalité) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Une facture validée déclenche automatiquement la création d'un bon de livraison
\item Le système propose l'assignation automatique du livreur optimal
\item Le suivi en temps réel connecte statut livraison → mise à jour facture
\item La confirmation de livraison déclenche la demande de paiement
\item Le cycle complet est tracé dans un dashboard unifié
\end{enumerate}

\section{Résumé de la Consolidation}

\begin{table}[h]
\centering
\caption{Comparaison Avant/Après Consolidation}
\begin{tabular}{|l|l|l|l|}
\hline
\rowcolor{lightgray}
\textbf{Version} & \textbf{Sprints} & \textbf{Durée} & \textbf{Optimisation} \\
\hline
Avant & Sprint 3 + Sprint 4 & 30j + 26j = 56j & - \\
\hline
Après & Sprint 3 Unifié & 45j & -11j (-20\%) \\
\hline
\end{tabular}
\end{table}

\begin{table}[h]
\centering
\caption{Récapitulatif Sprint 3 Unifié}
\begin{tabular}{|l|l|l|l|}
\hline
\rowcolor{lightgray}
\textbf{Thème} & \textbf{Stories Consolidées} & \textbf{Durée} & \textbf{Priorité} \\
\hline
Données \& Configuration & 3.1+4.2, 3.2+4.3, 4.4 & 17 jours & Élevée \\
\hline
Abonnements \& Paiements & 3.3+3.4, 3.5+4.1, 3.6+3.7+3.8 & 15 jours & Élevée \\
\hline
Livraisons \& Logistique & 4.5+4.6, 4.7+4.8, Workflow & 13 jours & Élevée \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Sprint 3 Unifié:}} & \textbf{45 jours} \\
\hline
\end{tabular}
\end{table}

\section{Bénéfices de la Consolidation}

\subsection{Gains Temporels}
\begin{itemize}
\item \textbf{Élimination des redondances :} -7 jours
\item \textbf{Développement parallèle :} -4 jours
\item \textbf{Total économisé :} 11 jours (20\% d'optimisation)
\end{itemize}

\subsection{Gains Fonctionnels}
\begin{itemize}
\item \textbf{Cohérence système :} Intégration native entre modules
\item \textbf{Workflow unifié :} Factures → Livraisons → Paiements
\item \textbf{Interface consolidée :} Moins de navigation entre écrans
\item \textbf{Maintenance simplifiée :} Code plus cohérent et modulaire
\end{itemize}

\subsection{Gains Organisationnels}
\begin{itemize}
\item \textbf{Équipe focalisée :} Un seul sprint à gérer
\item \textbf{Tests intégrés :} Validation complète du workflow
\item \textbf{Livraison cohérente :} Système complet en une fois
\item \textbf{Réduction des risques :} Moins de dépendances inter-sprints
\end{itemize}

\end{document}
