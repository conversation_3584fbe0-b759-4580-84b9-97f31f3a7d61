{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/middleware-route-matcher.ts"], "sourcesContent": ["import type { BaseNextRequest } from '../../../../server/base-http'\nimport type { MiddlewareMatcher } from '../../../../build/analysis/get-page-static-info'\nimport type { Params } from '../../../../server/request/params'\nimport { matchHas } from './prepare-destination'\n\nexport interface MiddlewareRouteMatch {\n  (\n    pathname: string | null | undefined,\n    request: BaseNextRequest,\n    query: Params\n  ): boolean\n}\n\nexport function getMiddlewareRouteMatcher(\n  matchers: MiddlewareMatcher[]\n): MiddlewareRouteMatch {\n  return (\n    pathname: string | null | undefined,\n    req: BaseNextRequest,\n    query: Params\n  ) => {\n    for (const matcher of matchers) {\n      const routeMatch = new RegExp(matcher.regexp).exec(pathname!)\n      if (!routeMatch) {\n        continue\n      }\n\n      if (matcher.has || matcher.missing) {\n        const hasParams = matchHas(req, query, matcher.has, matcher.missing)\n        if (!hasParams) {\n          continue\n        }\n      }\n\n      return true\n    }\n\n    return false\n  }\n}\n"], "names": ["getMiddlewareRouteMatcher", "matchers", "pathname", "req", "query", "matcher", "routeMatch", "RegExp", "regexp", "exec", "has", "missing", "hasParams", "matchHas"], "mappings": ";;;;+BAagBA;;;eAAAA;;;oCAVS;AAUlB,SAASA,0BACdC,QAA6B;IAE7B,OAAO,CACLC,UACAC,KACAC;QAEA,KAAK,MAAMC,WAAWJ,SAAU;YAC9B,MAAMK,aAAa,IAAIC,OAAOF,QAAQG,MAAM,EAAEC,IAAI,CAACP;YACnD,IAAI,CAACI,YAAY;gBACf;YACF;YAEA,IAAID,QAAQK,GAAG,IAAIL,QAAQM,OAAO,EAAE;gBAClC,MAAMC,YAAYC,IAAAA,4BAAQ,EAACV,KAAKC,OAAOC,QAAQK,GAAG,EAAEL,QAAQM,OAAO;gBACnE,IAAI,CAACC,WAAW;oBACd;gBACF;YACF;YAEA,OAAO;QACT;QAEA,OAAO;IACT;AACF"}