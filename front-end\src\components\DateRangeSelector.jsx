import React, { useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>u,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  Typography,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import fr from 'date-fns/locale/fr';

const DateRangeSelector = ({ onDateRangeChange, initialRange = 'month' }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedRange, setSelectedRange] = useState(initialRange);
  const [customDialogOpen, setCustomDialogOpen] = useState(false);
  const [startDate, setStartDate] = useState(new Date(new Date().setMonth(new Date().getMonth() - 1)));
  const [endDate, setEndDate] = useState(new Date());

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRangeSelect = (range) => {
    if (range === 'custom') {
      setCustomDialogOpen(true);
    } else {
      setSelectedRange(range);
      onDateRangeChange(range);
    }
    handleClose();
  };

  const handleCustomDialogClose = () => {
    setCustomDialogOpen(false);
  };

  const handleCustomRangeApply = () => {
    setSelectedRange('custom');
    onDateRangeChange('custom', { startDate, endDate });
    setCustomDialogOpen(false);
  };

  const getRangeLabel = () => {
    switch (selectedRange) {
      case 'today':
        return 'Aujourd\'hui';
      case 'week':
        return 'Cette semaine';
      case 'month':
        return 'Ce mois';
      case 'quarter':
        return 'Ce trimestre';
      case 'year':
        return 'Cette année';
      case 'custom':
        return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
      default:
        return 'Période';
    }
  };

  return (
    <>
      <Button
        variant="outlined"
        size="small"
        onClick={handleClick}
        startIcon={<CalendarTodayIcon />}
        endIcon={<KeyboardArrowDownIcon />}
        sx={{
          borderRadius: 2,
          textTransform: 'none',
          fontWeight: 500,
          px: 2,
          py: 0.75,
          borderColor: 'divider',
          '&:hover': {
            borderColor: 'primary.main',
          }
        }}
      >
        {getRangeLabel()}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 2,
          sx: {
            mt: 1,
            borderRadius: 2,
            minWidth: 180,
          }
        }}
      >
        <MenuItem onClick={() => handleRangeSelect('today')}>Aujourd'hui</MenuItem>
        <MenuItem onClick={() => handleRangeSelect('week')}>Cette semaine</MenuItem>
        <MenuItem onClick={() => handleRangeSelect('month')}>Ce mois</MenuItem>
        <MenuItem onClick={() => handleRangeSelect('quarter')}>Ce trimestre</MenuItem>
        <MenuItem onClick={() => handleRangeSelect('year')}>Cette année</MenuItem>
        <Divider />
        <MenuItem onClick={() => handleRangeSelect('custom')}>Période personnalisée</MenuItem>
      </Menu>

      <Dialog open={customDialogOpen} onClose={handleCustomDialogClose} maxWidth="xs" fullWidth>
        <DialogTitle>Sélectionner une période</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
              <Typography variant="subtitle2" gutterBottom>Date de début</Typography>
              <DatePicker
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth size="small" margin="dense" />}
              />
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>Date de fin</Typography>
              <DatePicker
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => <TextField {...params} fullWidth size="small" margin="dense" />}
              />
            </LocalizationProvider>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCustomDialogClose}>Annuler</Button>
          <Button onClick={handleCustomRangeApply} variant="contained" color="primary">Appliquer</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DateRangeSelector;
