const mongoose = require('mongoose');
const { Schema } = mongoose;

// Responsable Template Schema - Simplified customizations with colors and logos only
const responsableTemplateSchema = new Schema({
    // Reference to the enterprise/responsable
    entrepriseId: { type: Schema.Types.ObjectId, ref: 'User', required: true },

    // Template assignments and customizations
    factureTemplate: {
        baseTemplateId: { type: Schema.Types.ObjectId, ref: 'BaseTemplate', required: true },
        color: { type: String, default: '#f57c00' }, // Primary color
        logo: { type: String, default: null }, // Logo file path
        isActive: { type: Boolean, default: true }
    },

    devisTemplate: {
        baseTemplateId: { type: Schema.Types.ObjectId, ref: 'BaseTemplate', required: true },
        color: { type: String, default: '#f57c00' }, // Primary color
        logo: { type: String, default: null }, // Logo file path
        isActive: { type: <PERSON>ole<PERSON>, default: true }
    },



    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

// Middleware pour mettre à jour automatiquement la date de modification
responsableTemplateSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Index pour optimiser les requêtes par entreprise
responsableTemplateSchema.index({ entrepriseId: 1 });

module.exports = mongoose.model('ResponsableTemplate', responsableTemplateSchema);
