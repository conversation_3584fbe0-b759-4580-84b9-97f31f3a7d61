{"name": "wide-align", "version": "1.1.5", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": "<PERSON> <<EMAIL>> (http://re-becca.org/)", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/iarna/wide-align"}, "//": "But not version 5 of string-width, as that's ESM only", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}, "devDependencies": {"tap": "*"}, "files": ["align.js"]}