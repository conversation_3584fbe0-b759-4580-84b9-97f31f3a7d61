import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  ButtonGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tab,
  Tabs,
  CircularProgress,
  useTheme,
  Snackbar,
  Alert,
  Paper,
  IconButton,
  Tooltip,
  Avatar,
  Chip,
  Stack,
  TextField,
  InputAdornment,
  Fade,
  Zoom,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Legend,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
} from 'recharts';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Receipt as ReceiptIcon,
  AttachMoney as MoneyIcon,
  FilterAlt as FilterIcon,
  Refresh as RefreshIcon,
  CalendarToday as CalendarIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  MoreVert as MoreVertIcon,
  GetApp as GetAppIcon,
  TableChart as TableChartIcon,
  ShowChart as ShowChartIcon,
  PieChart as PieChartIcon,
  BarChart as BarChartIcon,
  Search as SearchIcon,
  Info as InfoIcon,
  Help as HelpIcon,
  Inventory as InventoryIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import analyticsService from '../services/analyticsService';
import PowerBIExport from '../components/PowerBIExport';
import SimpleDateFilter from '../components/SimpleDateFilter';
import { formatCurrency, formatDate } from '../utils/formatters';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
};

const cardVariants = {
  hidden: { scale: 0.95, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  },
  hover: {
    scale: 1.02,
    boxShadow: "0px 8px 25px rgba(0, 0, 0, 0.1)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

const PowerBIDashboard = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('monthly');
  const [tabValue, setTabValue] = useState(0);
  const [dashboardData, setDashboardData] = useState(null);
  const [revenueData, setRevenueData] = useState([]);
  const [topClients, setTopClients] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [paymentStatus, setPaymentStatus] = useState([]);
  const [kpiData, setKpiData] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [chartType, setChartType] = useState('area');
  const [searchQuery, setSearchQuery] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null
  });

  // Chart colors
  const CHART_COLORS = {
    revenue: theme.palette.primary.main,
    invoices: theme.palette.secondary.main,
    clients: theme.palette.success.main,
    products: theme.palette.info.main,
    paid: theme.palette.success.main,
    pending: theme.palette.warning.main,
    overdue: theme.palette.error.main,
    canceled: theme.palette.grey[500],
    draft: theme.palette.grey[400],
    gradient: {
      start: theme.palette.primary.light,
      end: theme.palette.primary.dark
    },
  };

  // Function to fetch all dashboard data
  const fetchAllData = useCallback(async () => {
    try {
      setRefreshing(true);

      // Fetch all data in parallel
      const [
        dashboardResponse,
        revenueResponse,
        topClientsResponse,
        topProductsResponse,
        paymentStatusResponse,
        kpiResponse,
      ] = await Promise.all([
        analyticsService.getDashboardData(period),
        analyticsService.getRevenueData(period),
        analyticsService.getTopClients(10, period === 'all-time' ? 'all-time' : period),
        analyticsService.getTopProducts(10, period === 'all-time' ? 'all-time' : period),
        analyticsService.getPaymentStatus(period === 'all-time' ? 'all-time' : period),
        analyticsService.getKPI(),
      ]);

      setDashboardData(dashboardResponse);
      setRevenueData(revenueResponse);
      setTopClients(topClientsResponse);
      setTopProducts(topProductsResponse);
      setPaymentStatus(paymentStatusResponse);
      setKpiData(kpiResponse);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setNotification({
        open: true,
        message: 'Erreur lors de la récupération des données',
        severity: 'error'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [period]);

  // Initial data fetch
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Auto-refresh data periodically (every 5 minutes)
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      fetchAllData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(refreshInterval);
    };
  }, [fetchAllData]);

  const handlePeriodChange = (event) => {
    setPeriod(event.target.value);
    fetchAllData();
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Function to handle chart type change
  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  // Function to handle search
  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
    // Implement search functionality here
  };

  // Function to handle date range change
  const handleDateRangeChange = (type, date) => {
    setDateRange(prev => ({
      ...prev,
      [type]: date
    }));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAllData().finally(() => {
      setRefreshing(false);
      setNotification({
        open: true,
        message: 'Données actualisées avec succès',
        severity: 'success'
      });
    });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // KPI Card Component
  const KpiCard = ({ title, value, subtitle, icon, color, trend, trendValue }) => {
    const isTrendPositive = trendValue > 0;
    const trendIcon = isTrendPositive ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />;
    const trendColor = isTrendPositive ? 'success' : 'error';

    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <Card
          elevation={0}
          sx={{
            height: '100%',
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette[color].light, 0.2)} 0%, ${alpha(theme.palette[color].main, 0.05)} 100%)`,
            border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette[color].main, 0.03)} 100%)`,
              zIndex: 0
            }
          }}
        >
          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  backgroundColor: alpha(theme.palette[color].main, 0.15),
                  color: theme.palette[color].main,
                  mr: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette[color].main, 0.2)}`
                }}
              >
                {icon}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="500" color="text.primary">
                  {title}
                </Typography>
                {trend && (
                  <Chip
                    icon={trendIcon}
                    label={`${Math.abs(trendValue).toFixed(1)}%`}
                    size="small"
                    color={trendColor}
                    sx={{
                      height: 24,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      mt: 0.5
                    }}
                  />
                )}
              </Box>
            </Box>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                color: theme.palette[color].dark,
                textShadow: `0 2px 4px ${alpha(theme.palette[color].main, 0.2)}`
              }}
            >
              {value}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                opacity: 0.8,
                maxWidth: '90%'
              }}
            >
              {subtitle}
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Skip loading state and render the dashboard immediately
  // This removes the loading spinner and message

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 2, sm: 3 },
        background: `linear-gradient(180deg, ${alpha(theme.palette.background.default, 0.6)} 0%, ${theme.palette.background.default} 100%)`,
        minHeight: '100vh'
      }}
    >
      <Container maxWidth="xl">
        {/* Header */}
        <Box
          component={motion.div}
          variants={itemVariants}
          sx={{
            mb: 4,
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', md: 'center' }
          }}
        >
          <Box>
            <Typography
              variant="h3"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}
            >
              Tableau de Bord Analytique
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
              Visualisez et analysez vos données commerciales
            </Typography>
          </Box>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mt: { xs: 2, md: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            <TextField
              placeholder="Rechercher..."
              size="small"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                sx: { borderRadius: 2 }
              }}
              sx={{ width: { xs: '100%', sm: 220 } }}
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              disabled={refreshing}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                minWidth: 130
              }}
            >
              {refreshing ? 'Actualisation...' : 'Actualiser'}
            </Button>

            <SimpleDateFilter
              onDateRangeChange={(rangeId, dateRange) => {
                // Map the new range format to the existing period format
                let newPeriod;
                switch(rangeId) {
                  case 'thisMonth':
                    newPeriod = 'monthly';
                    break;
                  case 'thisQuarter':
                    newPeriod = 'quarterly';
                    break;
                  case 'thisYear':
                    newPeriod = 'yearly';
                    break;
                  case 'allTime':
                    newPeriod = 'all-time';
                    break;
                  default:
                    // For custom ranges or other predefined ranges, use monthly as default
                    newPeriod = 'custom';
                }
                setPeriod(newPeriod);
                fetchAllData();
              }}
              onRefresh={handleRefresh}
              initialRange={
                period === 'monthly' ? 'thisMonth' :
                period === 'quarterly' ? 'thisQuarter' :
                period === 'yearly' ? 'thisYear' :
                period === 'all-time' ? 'allTime' : 'thisMonth'
              }
              showRefreshButton={false}
            />
          </Stack>
        </Box>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert
            onClose={handleCloseNotification}
            severity={notification.severity}
            sx={{
              width: '100%',
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              borderRadius: 2
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>

      {/* KPI Cards */}
      <Grid
        container
        spacing={3}
        sx={{ mb: 4 }}
        component={motion.div}
        variants={itemVariants}
      >
        <Grid item xs={12} sm={6} md={3}>
          <KpiCard
            title="Chiffre d'affaires"
            value={formatCurrency(kpiData?.revenue?.currentMonth || 0)}
            subtitle={`${kpiData?.revenue?.monthGrowth > 0 ? '+' : ''}${kpiData?.revenue?.monthGrowth?.toFixed(1)}% vs mois précédent`}
            icon={<MoneyIcon />}
            color="primary"
            trend={true}
            trendValue={kpiData?.revenue?.monthGrowth || 0}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <KpiCard
            title="Factures"
            value={dashboardData?.invoices?.total || 0}
            subtitle={`${dashboardData?.invoices?.byStatus?.paid || 0} payées • ${dashboardData?.invoices?.byStatus?.sent || 0} en attente`}
            icon={<ReceiptIcon />}
            color="secondary"
            trend={true}
            trendValue={5.2}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <KpiCard
            title="Clients"
            value={dashboardData?.clients?.total || 0}
            subtitle={`${dashboardData?.clients?.new || 0} nouveaux clients`}
            icon={<PeopleIcon />}
            color="info"
            trend={true}
            trendValue={kpiData?.clients?.growth || 0}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <KpiCard
            title="Valeur moyenne"
            value={formatCurrency(kpiData?.invoices?.avgValueCurrentMonth || 0)}
            subtitle="Valeur moyenne des factures"
            icon={<TrendingUpIcon />}
            color="success"
            trend={true}
            trendValue={3.8}
          />
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid
        container
        spacing={3}
        component={motion.div}
        variants={itemVariants}
      >
        {/* Left Column - Charts */}
        <Grid item xs={12} lg={8}>
          <motion.div variants={cardVariants}>
            <Card
              elevation={0}
              sx={{
                mb: 3,
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                overflow: 'hidden'
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h5" fontWeight="600">
                    Évolution du chiffre d'affaires
                  </Typography>
                }
                action={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ButtonGroup
                      variant="outlined"
                      size="small"
                      aria-label="chart type"
                      sx={{ mr: 2, borderRadius: 2, overflow: 'hidden' }}
                    >
                      <Tooltip title="Graphique en aires">
                        <Button
                          onClick={() => setChartType('area')}
                          color={chartType === 'area' ? 'primary' : 'inherit'}
                          sx={{
                            borderRadius: '8px 0 0 8px',
                            backgroundColor: chartType === 'area' ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                          }}
                        >
                          <ShowChartIcon fontSize="small" />
                        </Button>
                      </Tooltip>
                      <Tooltip title="Graphique en barres">
                        <Button
                          onClick={() => setChartType('bar')}
                          color={chartType === 'bar' ? 'primary' : 'inherit'}
                          sx={{
                            borderRadius: '0 8px 8px 0',
                            backgroundColor: chartType === 'bar' ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                          }}
                        >
                          <BarChartIcon fontSize="small" />
                        </Button>
                      </Tooltip>
                    </ButtonGroup>

                    <Tabs
                      value={tabValue}
                      onChange={handleTabChange}
                      aria-label="chart tabs"
                      sx={{
                        '& .MuiTabs-indicator': {
                          height: 3,
                          borderRadius: 1.5
                        },
                        '& .MuiTab-root': {
                          textTransform: 'none',
                          fontWeight: 500,
                          minWidth: 100
                        }
                      }}
                    >
                      <Tab label="Revenus" />
                      <Tab label="Factures" />
                    </Tabs>
                  </Box>
                }
                sx={{
                  px: 3,
                  pt: 3,
                  pb: 2,
                  '& .MuiCardHeader-action': {
                    m: 0,
                    alignSelf: 'center'
                  }
                }}
              />
              <Divider />
              <CardContent sx={{ height: 400, p: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  {tabValue === 0 ? (
                    chartType === 'area' ? (
                      <AreaChart data={revenueData}>
                        <defs>
                          <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={CHART_COLORS.revenue} stopOpacity={0.8} />
                            <stop offset="95%" stopColor={CHART_COLORS.revenue} stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                        <XAxis
                          dataKey="date"
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <YAxis
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                          tickFormatter={(value) => `${new Intl.NumberFormat('fr-FR', { notation: 'compact', compactDisplay: 'short' }).format(value)} €`}
                        />
                        <RechartsTooltip
                          formatter={(value) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value)}
                          labelFormatter={(label) => `Date: ${label}`}
                          contentStyle={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 8,
                            boxShadow: theme.shadows[3],
                            padding: '10px 14px'
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="revenue"
                          stroke={CHART_COLORS.revenue}
                          strokeWidth={3}
                          fillOpacity={1}
                          fill="url(#colorRevenue)"
                          activeDot={{ r: 8, fill: CHART_COLORS.revenue, strokeWidth: 0 }}
                          name="Revenus"
                        />
                      </AreaChart>
                    ) : (
                      <BarChart data={revenueData}>
                        <defs>
                          <linearGradient id="barRevenue" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor={CHART_COLORS.revenue} stopOpacity={1} />
                            <stop offset="100%" stopColor={alpha(CHART_COLORS.revenue, 0.6)} stopOpacity={1} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                        <XAxis
                          dataKey="date"
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <YAxis
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                          tickFormatter={(value) => `${new Intl.NumberFormat('fr-FR', { notation: 'compact', compactDisplay: 'short' }).format(value)} €`}
                        />
                        <RechartsTooltip
                          formatter={(value) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value)}
                          labelFormatter={(label) => `Date: ${label}`}
                          contentStyle={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 8,
                            boxShadow: theme.shadows[3],
                            padding: '10px 14px'
                          }}
                        />
                        <Bar
                          dataKey="revenue"
                          fill="url(#barRevenue)"
                          radius={[4, 4, 0, 0]}
                          name="Revenus"
                          barSize={30}
                        />
                      </BarChart>
                    )
                  ) : (
                    chartType === 'bar' ? (
                      <BarChart data={revenueData}>
                        <defs>
                          <linearGradient id="barInvoices" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor={CHART_COLORS.invoices} stopOpacity={1} />
                            <stop offset="100%" stopColor={alpha(CHART_COLORS.invoices, 0.6)} stopOpacity={1} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                        <XAxis
                          dataKey="date"
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <YAxis
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <RechartsTooltip
                          formatter={(value) => `${value} factures`}
                          labelFormatter={(label) => `Date: ${label}`}
                          contentStyle={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 8,
                            boxShadow: theme.shadows[3],
                            padding: '10px 14px'
                          }}
                        />
                        <Bar
                          dataKey="invoiceCount"
                          fill="url(#barInvoices)"
                          radius={[4, 4, 0, 0]}
                          name="Factures"
                          barSize={30}
                        />
                      </BarChart>
                    ) : (
                      <LineChart data={revenueData}>
                        <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                        <XAxis
                          dataKey="date"
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <YAxis
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <RechartsTooltip
                          formatter={(value) => `${value} factures`}
                          labelFormatter={(label) => `Date: ${label}`}
                          contentStyle={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 8,
                            boxShadow: theme.shadows[3],
                            padding: '10px 14px'
                          }}
                        />
                        <Line
                          type="monotone"
                          dataKey="invoiceCount"
                          stroke={CHART_COLORS.invoices}
                          strokeWidth={3}
                          dot={{ r: 4, strokeWidth: 0 }}
                          activeDot={{ r: 8, fill: CHART_COLORS.invoices, strokeWidth: 0 }}
                          name="Factures"
                        />
                      </LineChart>
                    )
                  )}
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Top Clients
                      </Typography>
                    }
                    action={
                      <Tooltip title="Voir tous les clients">
                        <IconButton size="small">
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    }
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ height: 300, overflow: 'auto', px: 2 }}>
                    {topClients.map((client, index) => (
                      <Box
                        key={client.clientId}
                        component={motion.div}
                        whileHover={{
                          backgroundColor: alpha(theme.palette.primary.main, 0.05),
                          x: 4
                        }}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          py: 1.5,
                          px: 2,
                          borderRadius: 2,
                          mb: 1,
                          transition: 'all 0.2s ease'
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: index === 0
                                ? theme.palette.primary.main
                                : index === 1
                                  ? theme.palette.secondary.main
                                  : theme.palette.info.main,
                              color: 'white',
                              fontWeight: 'bold',
                              mr: 2,
                              fontSize: '1rem'
                            }}
                          >
                            {index + 1}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="500">
                              {client.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                              <ReceiptIcon sx={{ fontSize: 12, mr: 0.5 }} />
                              {client.invoiceCount} factures
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="subtitle1" fontWeight="bold" color="primary.main">
                            {formatCurrency(client.revenue)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {client.lastInvoiceDate ? formatDate(client.lastInvoiceDate) : 'N/A'}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Top Produits/Services
                      </Typography>
                    }
                    action={
                      <Tooltip title="Voir tous les produits">
                        <IconButton size="small">
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    }
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ height: 300, overflow: 'auto', px: 2 }}>
                    {topProducts.map((product, index) => (
                      <Box
                        key={product.name}
                        component={motion.div}
                        whileHover={{
                          backgroundColor: alpha(theme.palette.secondary.main, 0.05),
                          x: 4
                        }}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          py: 1.5,
                          px: 2,
                          borderRadius: 2,
                          mb: 1,
                          transition: 'all 0.2s ease'
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: index === 0
                                ? theme.palette.secondary.main
                                : index === 1
                                  ? theme.palette.success.main
                                  : theme.palette.warning.main,
                              color: 'white',
                              fontWeight: 'bold',
                              mr: 2,
                              fontSize: '1rem'
                            }}
                          >
                            {index + 1}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="500" noWrap sx={{ maxWidth: 150 }}>
                              {product.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                              <InventoryIcon sx={{ fontSize: 12, mr: 0.5 }} />
                              {product.quantity || product.totalQuantity || 0} unités
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="subtitle1" fontWeight="bold" color="secondary.main">
                            {formatCurrency(product.revenue || product.totalRevenue || 0)}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Grid>

        {/* Right Column - Status and Export */}
        <Grid item xs={12} lg={4}>
          <motion.div variants={cardVariants}>
            <Card
              elevation={0}
              sx={{
                mb: 3,
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                overflow: 'hidden'
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h6" fontWeight="600">
                    Statut des paiements
                  </Typography>
                }
                action={
                  <Tooltip title="Voir les détails">
                    <IconButton size="small">
                      <PieChartIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                }
                sx={{ px: 3, pt: 3, pb: 2 }}
              />
              <Divider />
              <CardContent sx={{ height: 320, p: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={paymentStatus}
                      cx="50%"
                      cy="50%"
                      innerRadius={70}
                      outerRadius={90}
                      paddingAngle={3}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      labelLine={false}
                      animationDuration={1000}
                      animationBegin={200}
                    >
                      {paymentStatus.map((entry, index) => {
                        let color;
                        switch (entry.name) {
                          case 'Payée':
                            color = CHART_COLORS.paid;
                            break;
                          case 'En attente':
                            color = CHART_COLORS.pending;
                            break;
                          case 'Brouillon':
                            color = CHART_COLORS.draft;
                            break;
                          case 'Annulée':
                            color = CHART_COLORS.canceled;
                            break;
                          default:
                            color = theme.palette.grey[500];
                        }
                        return (
                          <Cell
                            key={`cell-${index}`}
                            fill={color}
                            stroke={theme.palette.background.paper}
                            strokeWidth={2}
                          />
                        );
                      })}
                    </Pie>
                    <RechartsTooltip
                      formatter={(value, name, props) => [
                        `${value} factures (${(value / paymentStatus.reduce((sum, item) => sum + item.value, 0) * 100).toFixed(0)}%)`,
                        `${formatCurrency(props.payload.amount)}`,
                      ]}
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: theme.shadows[3],
                        padding: '10px 14px'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Power BI Export Component */}
          <motion.div variants={cardVariants}>
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                overflow: 'hidden',
                background: `linear-gradient(135deg, ${alpha(theme.palette.info.light, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h6" fontWeight="600">
                    Exporter les données pour Power BI
                  </Typography>
                }
                subheader="Exportez vos données au format CSV pour les importer dans Power BI"
                sx={{ px: 3, pt: 3, pb: 2 }}
              />
              <Divider />
              <CardContent sx={{ p: 3 }}>
                <PowerBIExport />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
      </Container>
    </Box>
  );
};

export default PowerBIDashboard;
