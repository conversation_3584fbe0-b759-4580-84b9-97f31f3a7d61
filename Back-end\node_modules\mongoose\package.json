{"name": "mongoose", "description": "Mongoose MongoDB ODM", "version": "8.15.0", "author": "<PERSON> <<EMAIL>>", "keywords": ["mongodb", "document", "model", "schema", "database", "odm", "data", "datastore", "query", "nosql", "orm", "db"], "type": "commonjs", "license": "MIT", "dependencies": {"bson": "^6.10.3", "kareem": "2.6.3", "mongodb": "~6.16.0", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "17.1.3"}, "devDependencies": {"@babel/core": "7.27.1", "@babel/preset-env": "7.27.1", "@mongodb-js/mongodb-downloader": "^0.3.9", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "acquit": "1.3.0", "acquit-ignore": "0.2.1", "acquit-require": "0.1.1", "ajv": "8.17.1", "assert-browserify": "2.0.0", "babel-loader": "8.2.5", "broken-link-checker": "^0.7.8", "buffer": "^5.6.0", "cheerio": "1.0.0", "crypto-browserify": "3.12.1", "dox": "1.0.0", "eslint": "8.57.1", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-mocha-no-only": "1.2.0", "express": "^4.19.2", "fs-extra": "~11.3.0", "highlight.js": "11.11.1", "lodash.isequal": "4.5.0", "lodash.isequalwith": "4.4.0", "markdownlint-cli2": "^0.17.1", "marked": "15.0.11", "mkdirp": "^3.0.1", "mocha": "11.2.2", "moment": "2.30.1", "mongodb-memory-server": "10.1.4", "mongodb-runner": "^5.8.2", "ncp": "^2.0.0", "nyc": "15.1.0", "pug": "3.0.3", "q": "1.5.1", "sinon": "20.0.0", "stream-browserify": "3.0.0", "tsd": "0.32.0", "typescript": "5.8.3", "uuid": "11.1.0", "webpack": "5.99.7"}, "directories": {"lib": "./lib/mongoose"}, "scripts": {"docs:clean": "npm run docs:clean:stable", "docs:clean:stable": "rimraf index.html && rimraf -rf ./docs/*.html  && rimraf -rf ./docs/api && rimraf -rf ./docs/tutorials/*.html && rimraf -rf ./docs/typescript/*.html && rimraf -rf ./docs/*.html && rimraf -rf ./docs/source/_docs && rimraf -rf ./tmp", "docs:clean:5x": "rimraf index.html && rimraf -rf ./docs/5.x && rimraf -rf ./docs/source/_docs && rimraf -rf ./tmp", "docs:clean:6x": "rimraf index.html && rimraf -rf ./docs/6.x && rimraf -rf ./docs/source/_docs && rimraf -rf ./tmp", "docs:copy:tmp": "mkdirp ./tmp/docs/css && mkdirp ./tmp/docs/js && mkdirp ./tmp/docs/images && mkdirp ./tmp/docs/tutorials && mkdirp ./tmp/docs/typescript && mkdirp ./tmp/docs/api && ncp ./docs/css ./tmp/docs/css --filter=.css$ && ncp ./docs/js ./tmp/docs/js --filter=.js$ && ncp ./docs/images ./tmp/docs/images && ncp ./docs/tutorials ./tmp/docs/tutorials && ncp ./docs/typescript ./tmp/docs/typescript && ncp ./docs/api ./tmp/docs/api && cp index.html ./tmp && cp docs/*.html ./tmp/docs/", "docs:copy:tmp:5x": "rimraf ./docs/5.x && ncp ./tmp ./docs/5.x", "docs:copy:tmp:6x": "rimraf ./docs/6.x && ncp ./tmp ./docs/6.x", "docs:generate": "node ./scripts/website.js", "docs:generate:sponsorData": "node ./scripts/loadSponsorData.js", "docs:test": "npm run docs:generate", "docs:view": "node ./scripts/static.js", "docs:prepare:publish:stable": "git checkout gh-pages && git merge master && npm run docs:generate", "docs:prepare:publish:5x": "git checkout 5.x && git merge 5.x && npm run docs:clean:stable && npm run docs:generate && npm run docs:copy:tmp && git checkout gh-pages && npm run docs:copy:tmp:5x", "docs:prepare:publish:6x": "git checkout 6.x && git merge 6.x && npm run docs:clean:stable && env DOCS_DEPLOY=true npm run docs:generate && mv ./docs/6.x ./tmp && git checkout gh-pages && npm run docs:copy:tmp:6x", "docs:prepare:publish:7x": "env DOCS_DEPLOY=true npm run docs:generate && git checkout gh-pages && rimraf ./docs/7.x && mv ./tmp ./docs/7.x", "docs:check-links": "blc http://127.0.0.1:8089 -ro", "lint": "eslint .", "lint-js": "eslint . --ext .js --ext .cjs", "lint-ts": "eslint . --ext .ts", "lint-md": "markdownlint-cli2 \"**/*.md\" \"#node_modules\" \"#benchmarks\"", "build-browser": "(rm ./dist/* || true) && node ./scripts/build-browser.js", "prepublishOnly": "npm run build-browser", "release": "git pull && git push origin master --tags && npm publish", "release-5x": "git pull origin 5.x && git push origin 5.x && git push origin 5.x --tags && npm publish --tag 5x", "release-6x": "git pull origin 6.x && git push origin 6.x && git push origin 6.x --tags && npm publish --tag 6x", "mongo": "node ./tools/repl.js", "publish-7x": "npm publish --tag 7x", "test": "mocha --exit ./test/*.test.js", "test-deno": "deno run --allow-env --allow-read --allow-net --allow-run --allow-sys --allow-write ./test/deno.mjs", "test-rs": "START_REPLICA_SET=1 mocha --timeout 30000 --exit ./test/*.test.js", "test-tsd": "node ./test/types/check-types-filename && tsd", "setup-test-encryption": "node scripts/setup-encryption-tests.js", "test-encryption": "mocha --exit ./test/encryption/*.test.js", "tdd": "mocha ./test/*.test.js --inspect --watch --recursive --watch-files ./**/*.{js,ts}", "test-coverage": "nyc --reporter=html --reporter=text npm test", "ts-benchmark": "cd ./benchmarks/typescript/simple && npm install && npm run benchmark | node ../../../scripts/tsc-diagnostics-check"}, "main": "./index.js", "types": "./types/index.d.ts", "engines": {"node": ">=16.20.1"}, "bugs": {"url": "https://github.com/Automattic/mongoose/issues/new"}, "repository": {"type": "git", "url": "git://github.com/Automattic/mongoose.git"}, "homepage": "https://mongoosejs.com", "browser": "./dist/browser.umd.js", "config": {"mongodbMemoryServer": {"disablePostinstall": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}, "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": false, "strict": true, "allowSyntheticDefaultImports": true, "strictPropertyInitialization": false, "noImplicitAny": false, "strictNullChecks": true, "module": "commonjs", "target": "ES2017"}}}