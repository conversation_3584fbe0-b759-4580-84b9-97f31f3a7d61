import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Box,
  Avatar,
  IconButton,
  Tab,
  Tabs,
  useTheme,
  CircularProgress,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Business as BusinessIcon,
  PhotoCamera as PhotoCameraIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Language as LanguageIcon,
  Category as CategoryIcon,
  Notes as NotesIcon,
  Person as PersonIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import userService from '../services/userService';
import axios from 'axios';

const ClientForm = ({ open, onClose, client, onSave, onUploadLogo }) => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [vendeurs, setVendeurs] = useState([]);
  const [loadingVendeurs, setLoadingVendeurs] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [createUserAccount, setCreateUserAccount] = useState(true);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Référence pour le champ de nom
  const nomInputRef = React.useRef(null);

  const [formData, setFormData] = useState({
    nom: client?.nom || '',
    adresse: client?.adresse || '',
    contact: client?.contact || '',
    email: client?.email || '',
    cin: client?.cin || '',
    actif: client?.actif !== undefined ? client?.actif : true,
    vendeurId: client?.vendeurId || '',
    motDePasse: '',
  });

  // État pour les erreurs de validation
  const [validationErrors, setValidationErrors] = useState({
    cin: ''
  });

  // Fetch vendeurs associated with the current entreprise
  useEffect(() => {
    const fetchVendeurs = async () => {
      if (currentUser && (currentUser.role === 'ENTREPRISE' || currentUser.role === 'ADMIN')) {
        try {
          setLoadingVendeurs(true);
          const entrepriseId = currentUser._id || currentUser.id;
          const vendeursList = await userService.getVendeursByEntreprise(entrepriseId);
          setVendeurs(vendeursList);
        } catch (error) {
          console.error('Error fetching vendeurs:', error);
        } finally {
          setLoadingVendeurs(false);
        }
      }
    };

    if (open) {
      fetchVendeurs();
    }
  }, [currentUser, open]);

  useEffect(() => {
    if (client) {
      setFormData({
        nom: client.nom || '',
        adresse: client.adresse || '',
        contact: client.contact || '',
        email: client.email || '',
        cin: client.cin || '',
        actif: client.actif !== undefined ? client.actif : true,
        vendeurId: client.vendeurId || '',
      });

      // Set logo preview if client has a logo
      if (client.logo) {
        setLogoPreview(client.logo);
      }
    } else {
      // Réinitialiser le formulaire quand on ouvre la fenêtre pour un nouveau client
      setFormData({
        nom: '',
        adresse: '',
        contact: '',
        email: '',
        cin: '',
        actif: true,
        vendeurId: currentUser?.role === 'VENDEUR' ? (currentUser._id || currentUser.id) : '',
        motDePasse: '',
      });
      setLogoPreview(null);
    }
  }, [client, open, currentUser]);

  // Focus sur le champ de nom lorsque la fenêtre s'ouvre
  useEffect(() => {
    if (open && nomInputRef.current) {
      setTimeout(() => {
        nomInputRef.current.focus();
      }, 100);
    }
  }, [open]);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    const newValue = e.target.type === 'checkbox' ? checked : value;
    setFormData((prev) => ({ ...prev, [name]: newValue }));

    // Validation spécifique pour le CIN
    if (name === 'cin') {
      // Vérifier que le CIN contient exactement 8 chiffres
      const cinRegex = /^\d{8}$/;
      if (!cinRegex.test(value)) {
        setValidationErrors(prev => ({
          ...prev,
          cin: 'Le CIN doit contenir exactement 8 chiffres'
        }));
      } else {
        setValidationErrors(prev => ({
          ...prev,
          cin: ''
        }));
      }
    }
  };

  const handleLogoChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogoFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setLogoPreview(event.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Fonction handleTabChange supprimée car non utilisée

  const handleSubmit = async () => {
    try {
      // Vérifier la validation du CIN
      const cinRegex = /^\d{8}$/;
      if (!cinRegex.test(formData.cin)) {
        setValidationErrors(prev => ({
          ...prev,
          cin: 'Le CIN doit contenir exactement 8 chiffres'
        }));

        // Afficher une notification d'erreur
        setNotification({
          open: true,
          message: 'Le CIN doit contenir exactement 8 chiffres',
          severity: 'error'
        });

        return; // Arrêter la soumission du formulaire
      }

      // Créer une copie des données du formulaire
      const clientData = { ...formData };

      // Vérifier si un client avec cet email existe déjà (pour les nouveaux clients)
      if (!client && clientData.email) {
        try {
          console.log('Vérification si un client avec cet email existe déjà:', clientData.email);
          const checkResponse = await axios.get(`/api/clients/check-email?email=${encodeURIComponent(clientData.email)}`);

          if (checkResponse.data.exists) {
            console.log('Un client avec cet email existe déjà:', checkResponse.data.client);

            // Afficher une notification à l'utilisateur
            setNotification({
              open: true,
              message: 'Un client avec cet email existe déjà. Les informations seront mises à jour.',
              severity: 'info'
            });
          }
        } catch (error) {
          console.error('Erreur lors de la vérification de l\'email:', error);
          // Continuer malgré l'erreur
        }
      }

      // Si l'utilisateur est un vendeur, assigner automatiquement son ID comme vendeurId
      if (currentUser && currentUser.role === 'VENDEUR') {
        clientData.vendeurId = currentUser._id || currentUser.id;
        console.log('Assignation automatique du vendeur:', clientData.vendeurId);
      }
      // Si vendeurId est une chaîne vide, la définir à null pour la base de données
      else if (clientData.vendeurId === '') {
        clientData.vendeurId = null;
        console.log('Aucun vendeur assigné - Client lié directement à l\'entreprise');
      }

      // Ajouter les informations pour la création du compte utilisateur
      if (createUserAccount) {
        // Vérifier que le mot de passe est fourni
        if (!clientData.motDePasse) {
          setNotification({
            open: true,
            message: 'Veuillez fournir un mot de passe pour le compte utilisateur',
            severity: 'error'
          });
          return;
        }

        // Ajouter un flag pour indiquer qu'il faut créer un compte utilisateur
        clientData.createUserAccount = true;

        // Ajouter le responsable actuel aux responsables du client si c'est un responsable d'entreprise
        if (currentUser && currentUser.role === 'RESPONSABLE') {
          clientData.responsables = [currentUser._id || currentUser.id];
        }
      } else {
        // Ne pas envoyer le mot de passe si on ne crée pas de compte
        delete clientData.motDePasse;
      }

      // Enregistrer les données du client
      const savedClient = await onSave(clientData);
      console.log('Client enregistré avec succès:', savedClient);

      // Puis télécharger le logo si un nouveau a été sélectionné
      if (logoFile && savedClient && savedClient._id) {
        await onUploadLogo(savedClient._id, logoFile);
        console.log('Logo téléchargé avec succès');
      }

      // Afficher une notification de succès
      setNotification({
        open: true,
        message: client ? 'Client mis à jour avec succès' : 'Client créé avec succès',
        severity: 'success'
      });

      onClose();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du client:', error);

      // Afficher une notification d'erreur
      setNotification({
        open: true,
        message: error.response?.data?.error || 'Une erreur est survenue lors de l\'enregistrement du client',
        severity: 'error'
      });
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{
        pb: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: `1px solid ${theme.palette.divider}`,
        bgcolor: theme.palette.primary.light,
        color: theme.palette.primary.contrastText
      }}>
        <Typography variant="h6" component="div" fontWeight="bold">
          {client ? 'Modifier Client' : 'Nouveau Client'}
        </Typography>
      </DialogTitle>

      {/* Removed tabs as requested */}

      <DialogContent sx={{ pt: 3, bgcolor: theme.palette.background.default }}>
        {/* Client information */}
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar
                src={logoPreview}
                sx={{
                  width: 120,
                  height: 120,
                  mb: 2,
                  border: `1px solid ${theme.palette.divider}`
                }}
              >
                {!logoPreview && formData.nom.charAt(0)}
              </Avatar>

              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="logo-upload"
                type="file"
                onChange={handleLogoChange}
              />
              <label htmlFor="logo-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<PhotoCameraIcon />}
                  size="small"
                >
                  Photo
                </Button>
              </label>

              <FormControlLabel
                control={
                  <Switch
                    checked={formData.actif}
                    onChange={handleChange}
                    name="actif"
                    color="primary"
                  />
                }
                label="Client actif"
                sx={{ mt: 2 }}
              />
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography
                    variant="subtitle2"
                    fontWeight={600}
                    sx={{
                      mb: 1,
                      color: theme.palette.text.primary,
                      fontSize: '0.9rem'
                    }}
                  >
                    Nom du client*
                  </Typography>
                  <TextField
                    name="nom"
                    placeholder="Entrez le nom du client"
                    value={formData.nom}
                    onChange={handleChange}
                    fullWidth
                    required
                    autoFocus
                    inputRef={nomInputRef}
                    variant="outlined"
                    InputProps={{
                      startAdornment: <BusinessIcon color="primary" sx={{ mr: 1 }} />
                    }}
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: 2,
                        }
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography
                    variant="subtitle2"
                    fontWeight={600}
                    sx={{
                      mb: 1,
                      color: theme.palette.text.primary,
                      fontSize: '0.9rem'
                    }}
                  >
                    Adresse du client*
                  </Typography>
                  <TextField
                    name="adresse"
                    placeholder="Entrez l'adresse complète"
                    value={formData.adresse}
                    onChange={handleChange}
                    fullWidth
                    required
                    multiline
                    rows={2}
                    variant="outlined"
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: 2,
                        }
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography
                    variant="subtitle2"
                    fontWeight={600}
                    sx={{
                      mb: 1,
                      color: theme.palette.text.primary,
                      fontSize: '0.9rem'
                    }}
                  >
                    Téléphone*
                  </Typography>
                  <TextField
                    name="contact"
                    placeholder="Numéro de téléphone"
                    value={formData.contact}
                    onChange={handleChange}
                    fullWidth
                    required
                    variant="outlined"
                    InputProps={{
                      startAdornment: <PhoneIcon color="primary" sx={{ mr: 1 }} />
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: 2,
                        }
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography
                    variant="subtitle2"
                    fontWeight={600}
                    sx={{
                      mb: 1,
                      color: theme.palette.text.primary,
                      fontSize: '0.9rem'
                    }}
                  >
                    Email*
                  </Typography>
                  <TextField
                    name="email"
                    placeholder="Adresse email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    fullWidth
                    required
                    variant="outlined"
                    InputProps={{
                      startAdornment: <EmailIcon color="primary" sx={{ mr: 1 }} />
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: 2,
                        }
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography
                    variant="subtitle2"
                    fontWeight={600}
                    sx={{
                      mb: 1,
                      color: theme.palette.text.primary,
                      fontSize: '0.9rem'
                    }}
                  >
                    Numéro CIN*
                  </Typography>
                  <TextField
                    name="cin"
                    placeholder="Numéro de Carte d'Identité Nationale (8 chiffres)"
                    value={formData.cin}
                    onChange={handleChange}
                    fullWidth
                    required
                    variant="outlined"
                    error={!!validationErrors.cin}
                    helperText={validationErrors.cin}
                    InputProps={{
                      startAdornment: <PersonIcon color="primary" sx={{ mr: 1 }} />
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: 2,
                        }
                      }
                    }}
                  />
                </Grid>

                {/* Option pour créer un compte utilisateur */}
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={createUserAccount}
                        onChange={(e) => setCreateUserAccount(e.target.checked)}
                        name="createUserAccount"
                        color="primary"
                      />
                    }
                    label="Créer un compte utilisateur pour ce client"
                    sx={{ mb: 2 }}
                  />
                </Grid>

                {/* Champ mot de passe (visible uniquement si createUserAccount est activé) */}
                {createUserAccount && (
                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle2"
                      fontWeight={600}
                      sx={{
                        mb: 1,
                        color: theme.palette.text.primary,
                        fontSize: '0.9rem'
                      }}
                    >
                      Mot de passe*
                    </Typography>
                    <TextField
                      name="motDePasse"
                      placeholder="Mot de passe pour le compte utilisateur"
                      value={formData.motDePasse}
                      onChange={handleChange}
                      fullWidth
                      required
                      variant="outlined"
                      type={showPassword ? 'text' : 'password'}
                      InputProps={{
                        startAdornment: <LockIcon color="primary" sx={{ mr: 1 }} />,
                        endAdornment: (
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        )
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                          '&:hover fieldset': {
                            borderColor: theme.palette.primary.main,
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.palette.primary.main,
                            borderWidth: 2,
                          }
                        }
                      }}
                    />
                  </Grid>
                )}

                {/* Afficher le sélecteur de vendeur seulement si l'utilisateur n'est pas un vendeur */}
                {currentUser && currentUser.role !== 'VENDEUR' && (
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel id="vendeur-select-label">Vendeur assigné (optionnel)</InputLabel>
                      <Select
                        labelId="vendeur-select-label"
                        id="vendeur-select"
                        name="vendeurId"
                        value={formData.vendeurId}
                        onChange={handleChange}
                        label="Vendeur assigné (optionnel)"
                        startAdornment={<PersonIcon color="action" sx={{ mr: 1 }} />}
                        disabled={loadingVendeurs}
                      >
                        <MenuItem value="">
                          <em>Aucun vendeur assigné - Lier directement à l'entreprise</em>
                        </MenuItem>
                        {vendeurs.map((vendeur) => (
                          <MenuItem key={vendeur._id} value={vendeur._id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar
                                src={vendeur.profileImage ? `http://localhost:5000${vendeur.profileImage}` : ''}
                                sx={{ width: 24, height: 24 }}
                              >
                                {!vendeur.profileImage && vendeur.nom.charAt(0)}
                              </Avatar>
                              <Typography>{vendeur.nom}</Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {loadingVendeurs && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <CircularProgress size={16} sx={{ mr: 1 }} />
                          <Typography variant="caption">Chargement des vendeurs...</Typography>
                        </Box>
                      )}
                    </FormControl>
                  </Grid>
                )}

                {/* Removed Catégorie and Site Web fields as requested */}
              </Grid>
            </Grid>
          </Grid>
        {/* Removed supplementary details and notes tabs as requested */}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
        <Button onClick={onClose} variant="outlined">
          Annuler
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          sx={{ px: 3 }}
        >
          Enregistrer
        </Button>
      </DialogActions>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Dialog>
  );
};

export default ClientForm;