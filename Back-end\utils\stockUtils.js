// backend/utils/stockUtils.js
const Produit = require('../models/ProduitModel');
const Facture = require('../models/FactureModel');
const StockHistory = require('../models/StockHistoryModel');

/**
 * Met à jour le stock des produits d'une facture
 * @param {string} factureId - ID de la facture
 * @param {boolean} isCancel - Indique si c'est une annulation (réaugmenter le stock)
 * @returns {Promise<Object>} - Résultat de l'opération
 */
const updateStockFromInvoice = async (factureId, isCancel = false) => {
  try {
    // Récupérer la facture avec les produits
    const facture = await Facture.findById(factureId).populate({
      path: 'lignes.produit',
      model: 'Produit'
    });

    if (!facture) {
      return { success: false, message: 'Facture non trouvée' };
    }

    // Si le stock a déjà été ajusté et ce n'est pas une annulation, ne rien faire
    if (facture.stockAjuste && !isCancel) {
      return { success: true, message: 'Stock déjà ajusté pour cette facture' };
    }

    // Parcourir chaque ligne de la facture
    const updatedProducts = [];
    const lowStockProducts = [];

    for (const ligne of facture.lignes) {
      // Vérifier si la ligne a un produit associé
      if (ligne.produit && ligne.produit._id) {
        // Vérifier si le produit gère le stock
        const produit = await Produit.findById(ligne.produit._id);

        if (produit && produit.gestionStock) {
          // Calculer la nouvelle quantité en stock
          let nouvelleQuantite;

          if (isCancel) {
            // Si c'est une annulation, on réaugmente le stock
            nouvelleQuantite = produit.quantiteStock + ligne.quantite;
          } else {
            // Sinon, on diminue le stock
            nouvelleQuantite = produit.quantiteStock - ligne.quantite;
          }

          // Calculer les quantités avant et après
          const quantiteAvant = produit.quantiteStock;
          const nouvelleQuantiteStock = Math.max(0, nouvelleQuantite); // Éviter les stocks négatifs
          const difference = isCancel ? ligne.quantite : -ligne.quantite;

          // Mettre à jour le stock
          produit.quantiteStock = nouvelleQuantiteStock;

          // Mettre à jour les statistiques de vente si ce n'est pas une annulation
          if (!isCancel) {
            produit.statistiques.nombreVentes = (produit.statistiques.nombreVentes || 0) + ligne.quantite;
            produit.statistiques.chiffreAffaires = (produit.statistiques.chiffreAffaires || 0) + ligne.montantHT;
            produit.statistiques.derniereVente = new Date();
          }

          await produit.save();

          // Marquer la ligne comme ajustée
          ligne.stockAjuste = !isCancel;

          // Enregistrer l'historique du stock
          const stockHistory = new StockHistory({
            produitId: produit._id,
            factureId: facture._id,
            quantiteAvant: quantiteAvant,
            quantiteApres: nouvelleQuantiteStock,
            difference: difference,
            typeOperation: isCancel ? 'RETOUR' : 'VENTE',
            motif: isCancel ? 'Annulation de facture' : 'Vente via facture'
          });

          await stockHistory.save();

          updatedProducts.push({
            id: produit._id,
            nom: produit.nom,
            quantiteAvant: quantiteAvant,
            quantiteApres: nouvelleQuantiteStock,
            difference: difference
          });

          // Vérifier si le stock est bas
          if (produit.quantiteStock <= produit.seuilAlerte) {
            lowStockProducts.push({
              id: produit._id,
              nom: produit.nom,
              quantiteStock: produit.quantiteStock,
              seuilAlerte: produit.seuilAlerte
            });
          }
        }
      }
    }

    // Marquer la facture comme ajustée
    facture.stockAjuste = !isCancel;
    await facture.save();

    return {
      success: true,
      message: isCancel ? 'Stock réaugmenté avec succès' : 'Stock diminué avec succès',
      updatedProducts,
      lowStockProducts
    };
  } catch (error) {
    console.error('Erreur lors de la mise à jour du stock:', error);
    return { success: false, message: error.message, error };
  }
};

/**
 * Ajuste manuellement le stock d'un produit
 * @param {string} productId - ID du produit
 * @param {number} newQuantity - Nouvelle quantité en stock
 * @param {string} motif - Motif de l'ajustement
 * @param {string} userId - ID de l'utilisateur qui fait l'ajustement
 * @returns {Promise<Object>} - Résultat de l'opération
 */
const adjustProductStock = async (productId, newQuantity, motif = 'Ajustement manuel', userId = null) => {
  try {
    // Récupérer le produit
    const produit = await Produit.findById(productId);

    if (!produit) {
      return { success: false, message: 'Produit non trouvé' };
    }

    if (!produit.gestionStock) {
      return { success: false, message: 'La gestion de stock n\'est pas activée pour ce produit' };
    }

    // Calculer la différence
    const quantiteAvant = produit.quantiteStock;
    const difference = newQuantity - quantiteAvant;

    // Mettre à jour le stock
    produit.quantiteStock = Math.max(0, newQuantity); // Éviter les stocks négatifs
    produit.dateMiseAJour = Date.now();

    await produit.save();

    // Enregistrer l'historique du stock
    const stockHistory = new StockHistory({
      produitId: produit._id,
      quantiteAvant: quantiteAvant,
      quantiteApres: produit.quantiteStock,
      difference: difference,
      typeOperation: 'AJUSTEMENT',
      motif: motif,
      utilisateurId: userId
    });

    await stockHistory.save();

    // Vérifier si le stock est bas
    const isLowStock = produit.gestionStock && produit.quantiteStock <= produit.seuilAlerte;

    return {
      success: true,
      message: 'Stock ajusté avec succès',
      produit,
      stockHistory,
      alerte: isLowStock ? 'Stock bas' : null
    };
  } catch (error) {
    console.error('Erreur lors de l\'ajustement du stock:', error);
    return { success: false, message: error.message, error };
  }
};

/**
 * Récupère l'historique des mouvements de stock d'un produit
 * @param {string} productId - ID du produit
 * @param {Object} options - Options de filtrage
 * @returns {Promise<Array>} - Historique des mouvements de stock
 */
const getProductStockHistory = async (productId, options = {}) => {
  try {
    const { limit = 20, skip = 0, startDate, endDate, typeOperation } = options;

    // Construire la requête
    let query = { produitId: productId };

    if (startDate && endDate) {
      query.date = { $gte: new Date(startDate), $lte: new Date(endDate) };
    } else if (startDate) {
      query.date = { $gte: new Date(startDate) };
    } else if (endDate) {
      query.date = { $lte: new Date(endDate) };
    }

    if (typeOperation) {
      query.typeOperation = typeOperation;
    }

    // Exécuter la requête
    const history = await StockHistory.find(query)
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit)
      .populate('factureId', 'numero')
      .populate('utilisateurId', 'nom prenom email');

    return history;
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique des stocks:', error);
    throw error;
  }
};

module.exports = {
  updateStockFromInvoice,
  adjustProductStock,
  getProductStockHistory
};
