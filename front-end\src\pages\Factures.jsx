import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Tooltip,
  Grid,
  Snackbar,
  Alert,
  Divider,
  useTheme,
  alpha,
  Card,
  CardHeader,
  Stack,
  Badge,
  Avatar,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  PictureAsPdf as PdfIcon,
  Paid as PaidIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Payment as PaymentIcon,
  FilterList as FilterListIcon,
  ReceiptLong as ReceiptIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import FactureCard from '../components/FactureCard';
import EmailDialog from '../components/EmailDialog';
import factureService from '../services/factureService';
import clientService from '../services/clientService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';
import { useAuth } from '../contexts/AuthContext';

const statuts = [
  { value: 'all', label: 'Tous les statuts' },
  { value: 'PAID', label: 'Payée' },
  { value: 'SENT', label: 'Envoyée' },
  { value: 'DRAFT', label: 'Brouillon' },
  { value: 'ACCEPTED', label: 'Acceptée' },
  { value: 'REJECTED', label: 'Refusée' },
  { value: 'CANCELED', label: 'Annulée' }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

const Factures = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [factures, setFactures] = useState([]);
  const [clients, setClients] = useState([]);
  const [filteredFactures, setFilteredFactures] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statutFilter, setStatutFilter] = useState('all');
  const [clientFilter, setClientFilter] = useState('all');
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openEmailDialog, setOpenEmailDialog] = useState(false);
  const [selectedFacture, setSelectedFacture] = useState(null);
  const [viewMode, setViewMode] = useState('list');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Get status color with proper opacity for backgrounds
  const getStatusColor = (status) => {
    switch(status) {
      case 'PAID': return alpha(theme.palette.success.main, 0.1);
      case 'DRAFT': return alpha(theme.palette.info.main, 0.1);
      case 'ACCEPTED': return alpha(theme.palette.success.main, 0.1);
      case 'REJECTED': return alpha(theme.palette.error.main, 0.1);
      case 'CANCELED': return alpha(theme.palette.error.main, 0.1);
      default: return alpha(theme.palette.warning.main, 0.1);
    }
  };

  // Get status text color
  const getStatusTextColor = (status) => {
    switch(status) {
      case 'PAID': return theme.palette.success.main;
      case 'DRAFT': return theme.palette.info.main;
      case 'ACCEPTED': return theme.palette.success.main;
      case 'REJECTED': return theme.palette.error.main;
      case 'CANCELED': return theme.palette.error.main;
      default: return theme.palette.warning.main;
    }
  };

  // Déterminer le préfixe de route en fonction du rôle de l'utilisateur
  const getRoutePrefix = () => {
    if (!currentUser) return '/';

    switch (currentUser.role) {
      case 'ADMIN':
        return '/admin';
      case 'VENDEUR':
        return '/vendeur';
      case 'RESPONSABLE':
        return '/responsable';
      case 'ENTREPRISE':
        return '/entreprise';
      default:
        return '/client';
    }
  };

  useEffect(() => {
    fetchFactures();
    fetchClients();
  }, []);

  useEffect(() => {
    filterFactures();
  }, [factures, searchTerm, statutFilter, clientFilter]);

  const fetchFactures = async () => {
    try {
      const data = await factureService.getFactures();
      console.log('Fetched factures:', data); // Debug log
      setFactures(data);
    } catch (error) {
      console.error('Error fetching factures:', error);
    }
  };

  const fetchClients = async () => {
    try {
      const data = await clientService.getClients();
      setClients(data);
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const filterFactures = () => {
    let filtered = [...factures];

    if (searchTerm) {
      filtered = filtered.filter(
        (facture) =>
          facture.numero.toLowerCase().includes(searchTerm.toLowerCase()) || // Fixed field name
          (facture.clientId &&
            facture.clientId.nom.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statutFilter !== 'all') {
      filtered = filtered.filter((facture) => facture.statut === statutFilter);
    }

    if (clientFilter !== 'all') {
      filtered = filtered.filter((facture) => facture.clientId?._id === clientFilter);
    }

    setFilteredFactures(filtered);
  };

  const handleOpenEdit = (facture) => {
    const routePrefix = getRoutePrefix();
    navigate(`${routePrefix}/factures/${facture._id}`);
  };

  const handleOpenCreate = () => {
    const routePrefix = getRoutePrefix();
    navigate(`${routePrefix}/factures/new`);
  };

  const handleOpenDeleteDialog = (facture) => {
    setSelectedFacture(facture);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedFacture(null);
  };

  const handleDelete = async () => {
    try {
      await factureService.deleteFacture(selectedFacture._id);
      fetchFactures();
      handleCloseDeleteDialog();
    } catch (error) {
      console.error('Error deleting facture:', error);
    }
  };

  const handlePayment = async (id) => {
    try {
      await factureService.registerPayment(id);
      fetchFactures();
    } catch (error) {
      console.error('Error registering payment:', error);
    }
  };

  const handleOpenEmailDialog = (facture) => {
    setSelectedFacture(facture);
    setOpenEmailDialog(true);
  };

  const handleCloseEmailDialog = () => {
    setOpenEmailDialog(false);
  };

  const handleSendEmail = async (emailData) => {
    try {
      if (!selectedFacture) return;

      await factureService.sendEmail(selectedFacture._id, emailData);

      setSnackbar({
        open: true,
        message: 'Email envoyé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error sending email:', error);
      setSnackbar({
        open: true,
        message: `Erreur lors de l'envoi de l'email: ${error.message}`,
        severity: 'error'
      });
      throw error;
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleGeneratePdf = async (id) => {
    try {
      await factureService.generatePdf(id);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  const handlePrint = async (id) => {
    try {
      await factureService.printFacture(id);
    } catch (error) {
      console.error('Error printing:', error);
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header Section with Background */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={0}
        sx={{
          mb: 4,
          p: 3,
          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                width: 48,
                height: 48,
                mr: 2
              }}
            >
              <ReceiptIcon fontSize="medium" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                Gestion des Factures
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Créez, modifiez et gérez vos factures
              </Typography>
            </Box>
          </Box>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenCreate}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
              }
            }}
          >
            Nouvelle Facture
          </Button>
        </Box>
      </Card>

      {/* Search and Filter Section */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <TextField
            variant="outlined"
            placeholder="Rechercher par numéro, client..."
            size="small"
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              maxWidth: { xs: '100%', sm: 400 },
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.common.white, 0.9)
              }
            }}
          />

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ flexGrow: 0 }}>
            <TextField
              select
              label="Statut"
              variant="outlined"
              size="small"
              value={statutFilter}
              onChange={(e) => setStatutFilter(e.target.value)}
              sx={{
                minWidth: 150,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            >
              {statuts.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>

            <TextField
              select
              label="Client"
              variant="outlined"
              size="small"
              value={clientFilter}
              onChange={(e) => setClientFilter(e.target.value)}
              sx={{
                minWidth: 180,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            >
              <MenuItem value="all">Tous les clients</MenuItem>
              {clients.map((client) => (
                <MenuItem key={client._id} value={client._id}>
                  {client.nom}
                </MenuItem>
              ))}
            </TextField>

            <Stack direction="row" spacing={1}>
              <Button
                variant={viewMode === 'list' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('list')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Liste
              </Button>
              <Button
                variant={viewMode === 'card' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('card')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Cartes
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Card>

      {/* Table Section */}
      {viewMode === 'list' ? (
        <Card
          component={motion.div}
          variants={itemVariants}
          elevation={1}
          sx={{
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Numéro</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Client</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Montant</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Statut</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredFactures.map((facture) => (
                  <TableRow
                    key={facture._id}
                    hover
                    sx={{
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.03),
                        cursor: 'pointer'
                      }
                    }}
                  >
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {facture.numero || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2">
                        {facture.clientId ? facture.clientId.nom : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2">
                        {formatDate(facture.dateEmission)}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(facture.total)}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Chip
                        label={formatStatut(facture.statut)}
                        color={
                          facture.statut === 'PAID' ? 'success' :
                          facture.statut === 'DRAFT' ? 'info' : 'warning'
                        }
                        size="small"
                        sx={{
                          fontWeight: 'medium',
                          borderRadius: 1,
                          px: 1
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{ py: 1.5 }}>
                      <Stack direction="row" spacing={0.5}>
                        <Tooltip title="Modifier">
                          <IconButton
                            onClick={() => handleOpenEdit(facture)}
                            color="primary"
                            size="small"
                            sx={{
                              backgroundColor: alpha(theme.palette.primary.main, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.2) }
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Supprimer">
                          <IconButton
                            onClick={() => handleOpenDeleteDialog(facture)}
                            color="error"
                            size="small"
                            sx={{
                              backgroundColor: alpha(theme.palette.error.main, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.2) }
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        {facture.statut !== 'PAID' && (
                          <Tooltip title="Enregistrer paiement">
                            <IconButton
                              onClick={() => handlePayment(facture._id)}
                              color="success"
                              size="small"
                              sx={{
                                backgroundColor: alpha(theme.palette.success.main, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.success.main, 0.2) }
                              }}
                            >
                              <PaidIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}

                        <Tooltip title="Générer PDF">
                          <IconButton
                            onClick={() => handleGeneratePdf(facture._id)}
                            size="small"
                            sx={{
                              color: theme.palette.text.secondary,
                              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                            }}
                          >
                            <PdfIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Envoyer par email">
                          <IconButton
                            onClick={() => handleOpenEmailDialog(facture)}
                            size="small"
                            sx={{
                              color: theme.palette.info.main,
                              backgroundColor: alpha(theme.palette.info.main, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.info.main, 0.2) }
                            }}
                          >
                            <EmailIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Imprimer">
                          <IconButton
                            onClick={() => handlePrint(facture._id)}
                            size="small"
                            sx={{
                              color: theme.palette.text.secondary,
                              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                            }}
                          >
                            <PrintIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      ) : (
        <Box component={motion.div} variants={itemVariants}>
          <Grid container spacing={3}>
            {filteredFactures.map((facture) => (
              <Grid item xs={12} sm={6} md={4} key={facture._id} component={motion.div} variants={itemVariants}>
                <FactureCard
                  facture={facture}
                  onEdit={() => handleOpenEdit(facture)}
                  onDelete={() => handleOpenDeleteDialog(facture)}
                  onPdf={() => handleGeneratePdf(facture._id)}
                  onEmail={() => handleOpenEmailDialog(facture)}
                  onPrint={() => handlePrint(facture._id)}
                  onPayment={facture.statut !== 'PAID' ? () => handlePayment(facture._id) : null}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Empty state when no invoices */}
      {filteredFactures.length === 0 && (
        <Card
          component={motion.div}
          variants={itemVariants}
          sx={{
            p: 5,
            textAlign: 'center',
            borderRadius: 2,
            backgroundColor: alpha(theme.palette.background.paper, 0.8),
            border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`
          }}
        >
          <Box sx={{ mb: 2 }}>
            <Avatar
              sx={{
                width: 70,
                height: 70,
                margin: '0 auto',
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main
              }}
            >
              <ReceiptIcon sx={{ fontSize: 40 }} />
            </Avatar>
          </Box>
          <Typography variant="h6" gutterBottom>
            Aucune facture trouvée
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400, mx: 'auto' }}>
            Aucune facture ne correspond à vos critères de recherche. Essayez de modifier vos filtres ou créez une nouvelle facture.
          </Typography>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenCreate}
            sx={{ borderRadius: 2 }}
          >
            Créer une facture
          </Button>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ bgcolor: alpha(theme.palette.error.main, 0.1), color: theme.palette.error.main, mr: 2 }}>
              <DeleteIcon />
            </Avatar>
            <Typography variant="h6">
              Confirmer la suppression
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mt: 1 }}>
            Êtes-vous sûr de vouloir supprimer la facture <strong>{selectedFacture?.numero}</strong> ?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Cette action est irréversible et toutes les données associées seront définitivement supprimées.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseDeleteDialog}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 14px 0 rgba(244,67,54,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(244,67,54,0.23)'
              }
            }}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Email Dialog */}
      <EmailDialog
        open={openEmailDialog}
        onClose={handleCloseEmailDialog}
        onSend={handleSendEmail}
        documentType="facture"
        documentNumber={selectedFacture?.numero}
        recipientEmail={selectedFacture?.clientId?.email}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{
            width: '100%',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Factures;