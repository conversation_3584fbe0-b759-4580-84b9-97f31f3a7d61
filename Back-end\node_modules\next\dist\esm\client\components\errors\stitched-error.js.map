{"version": 3, "sources": ["../../../../src/client/components/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../lib/is-error'\nimport { copyNextErrorCode } from '../../../lib/error-telemetry-utils'\n\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame'\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\n  `(at ${REACT_ERROR_STACK_BOTTOM_FRAME} )|(${REACT_ERROR_STACK_BOTTOM_FRAME}\\\\@)`\n)\n\nexport function getReactStitchedError<T = unknown>(err: T): Error | T {\n  const isErrorInstance = isError(err)\n  const originStack = isErrorInstance ? err.stack || '' : ''\n  const originMessage = isErrorInstance ? err.message : ''\n  const stackLines = originStack.split('\\n')\n  const indexOfSplit = stackLines.findIndex((line) =>\n    REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line)\n  )\n  const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n  let newStack = isOriginalReactError\n    ? stackLines.slice(0, indexOfSplit).join('\\n')\n    : originStack\n\n  const newError = new Error(originMessage)\n  // Copy all enumerable properties, e.g. digest\n  Object.assign(newError, err)\n  copyNextErrorCode(err, newError)\n  newError.stack = newStack\n\n  // Avoid duplicate overriding stack frames\n  appendOwnerStack(newError)\n\n  return newError\n}\n\nfunction appendOwnerStack(error: Error) {\n  if (!React.captureOwnerStack) {\n    return\n  }\n  let stack = error.stack || ''\n  // This module is only bundled in development mode so this is safe.\n  const ownerStack = React.captureOwnerStack()\n  // Avoid duplicate overriding stack frames\n  if (ownerStack && stack.endsWith(ownerStack) === false) {\n    stack += ownerStack\n    // Override stack\n    error.stack = stack\n  }\n}\n"], "names": ["React", "isError", "copyNextErrorCode", "REACT_ERROR_STACK_BOTTOM_FRAME", "REACT_ERROR_STACK_BOTTOM_FRAME_REGEX", "RegExp", "getReactStitchedError", "err", "isErrorInstance", "originStack", "stack", "originMessage", "message", "stackLines", "split", "indexOfSplit", "findIndex", "line", "test", "isOriginalReactError", "newStack", "slice", "join", "newError", "Error", "Object", "assign", "appendOwnerStack", "error", "captureOwnerStack", "ownerStack", "endsWith"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,iBAAiB,QAAQ,qCAAoC;AAEtE,MAAMC,iCAAiC;AACvC,MAAMC,uCAAuC,IAAIC,OAC/C,AAAC,SAAMF,iCAA+B,SAAMA,iCAA+B;AAG7E,OAAO,SAASG,sBAAmCC,GAAM;IACvD,MAAMC,kBAAkBP,QAAQM;IAChC,MAAME,cAAcD,kBAAkBD,IAAIG,KAAK,IAAI,KAAK;IACxD,MAAMC,gBAAgBH,kBAAkBD,IAAIK,OAAO,GAAG;IACtD,MAAMC,aAAaJ,YAAYK,KAAK,CAAC;IACrC,MAAMC,eAAeF,WAAWG,SAAS,CAAC,CAACC,OACzCb,qCAAqCc,IAAI,CAACD;IAE5C,MAAME,uBAAuBJ,gBAAgB,EAAE,mCAAmC;;IAClF,IAAIK,WAAWD,uBACXN,WAAWQ,KAAK,CAAC,GAAGN,cAAcO,IAAI,CAAC,QACvCb;IAEJ,MAAMc,WAAW,qBAAwB,CAAxB,IAAIC,MAAMb,gBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;IACxC,8CAA8C;IAC9Cc,OAAOC,MAAM,CAACH,UAAUhB;IACxBL,kBAAkBK,KAAKgB;IACvBA,SAASb,KAAK,GAAGU;IAEjB,0CAA0C;IAC1CO,iBAAiBJ;IAEjB,OAAOA;AACT;AAEA,SAASI,iBAAiBC,KAAY;IACpC,IAAI,CAAC5B,MAAM6B,iBAAiB,EAAE;QAC5B;IACF;IACA,IAAInB,QAAQkB,MAAMlB,KAAK,IAAI;IAC3B,mEAAmE;IACnE,MAAMoB,aAAa9B,MAAM6B,iBAAiB;IAC1C,0CAA0C;IAC1C,IAAIC,cAAcpB,MAAMqB,QAAQ,CAACD,gBAAgB,OAAO;QACtDpB,SAASoB;QACT,iBAAiB;QACjBF,MAAMlB,KAAK,GAAGA;IAChB;AACF"}