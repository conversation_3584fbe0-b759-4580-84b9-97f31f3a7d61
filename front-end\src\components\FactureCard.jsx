import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  <PERSON>,
  Divider,
  Box,
  Stack,
  IconButton,
  useTheme,
  alpha,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  PictureAsPdf as PdfIcon,
  Paid as PaidIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  Receipt as ReceiptIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import StatutBadge from './StatutBadge';
import { formatDate, formatCurrency } from '../utils/formatters';

const FactureCard = ({
  facture,
  onEdit,
  onDelete,
  onPdf,
  onEmail,
  onPrint,
  onPayment,
  isEntreprise = false
}) => {
  const theme = useTheme();

  // Get status color
  const getStatusColor = (status) => {
    switch(status) {
      case 'PAID': return theme.palette.success.main;
      case 'DRAFT': return theme.palette.info.main;
      default: return theme.palette.warning.main;
    }
  };

  // Get status background color with opacity
  const getStatusBgColor = (status) => {
    return alpha(getStatusColor(status), 0.1);
  };

  return (
    <Card
      component={motion.div}
      whileHover={{
        y: -5,
        boxShadow: '0 10px 30px rgba(0,0,0,0.12)',
        transition: { duration: 0.3 }
      }}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: getStatusColor(facture.statut)
        }
      }}
    >
      <Box sx={{
        p: 2,
        pb: 1,
        background: alpha(getStatusColor(facture.statut), 0.05),
        borderBottom: `1px solid ${alpha(getStatusColor(facture.statut), 0.1)}`
      }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                width: 32,
                height: 32,
                mr: 1.5,
                bgcolor: getStatusBgColor(facture.statut),
                color: getStatusColor(facture.statut)
              }}
            >
              <ReceiptIcon fontSize="small" />
            </Avatar>
            <Typography variant="h6" fontWeight="bold">
              {facture.numero || facture.numéro}
            </Typography>
          </Box>
          <Chip
            label={facture.statut === 'PAID' ? 'Payée' :
                  facture.statut === 'DRAFT' ? 'Brouillon' : 'Envoyée'}
            size="small"
            sx={{
              fontWeight: 'medium',
              bgcolor: getStatusBgColor(facture.statut),
              color: getStatusColor(facture.statut),
              borderRadius: 1
            }}
          />
        </Box>
      </Box>

      <CardContent sx={{ flexGrow: 1, p: 2 }}>
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <PersonIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
            <Typography variant="body2" color="text.primary">
              <span style={{ fontWeight: 500 }}>Client:</span> {facture.clientId?.nom || 'Non spécifié'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <CalendarIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
            <Typography variant="body2" color="text.primary">
              <span style={{ fontWeight: 500 }}>Émis le:</span> {formatDate(facture.dateEmission || facture.dateÉmission)}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TimeIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
            <Typography variant="body2" color="text.primary">
              <span style={{ fontWeight: 500 }}>Échéance:</span> {formatDate(new Date(facture.dateEmission || facture.dateÉmission).setDate(
                new Date(facture.dateEmission || facture.dateÉmission).getDate() + 30
              ))}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{
          p: 1.5,
          borderRadius: 1,
          bgcolor: alpha(theme.palette.primary.main, 0.03),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.08)}`
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2" color="text.secondary">Montant HT:</Typography>
            <Typography variant="body2" sx={{ fontSize: '0.9rem', fontWeight: 'normal' }}>
              {formatCurrency(facture.total)}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2" color="text.secondary">TVA (20%):</Typography>
            <Typography variant="body2" sx={{ fontSize: '0.9rem', fontWeight: 'normal' }}>
              {formatCurrency(facture.total * 0.2)}
            </Typography>
          </Box>

          <Divider sx={{ my: 1, borderStyle: 'dashed' }} />

          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <Typography variant="body1" fontWeight="medium">Total TTC:</Typography>
            <Typography variant="body1" sx={{ fontSize: '0.9rem', fontWeight: 'normal', color: 'primary.main' }}>
              {formatCurrency(facture.total * 1.2)}
            </Typography>
          </Box>
        </Box>
      </CardContent>

      <Box sx={{ p: 2, pt: 0, bgcolor: alpha(theme.palette.background.default, 0.5) }}>
        {!isEntreprise ? (
          <>
            <Stack direction="row" spacing={1} justifyContent="space-between" sx={{ mb: 1.5 }}>
              <Tooltip title="Modifier">
                <IconButton
                  size="small"
                  onClick={onEdit}
                  color="primary"
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.2) }
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              {onDelete && (
                <Tooltip title="Supprimer">
                  <IconButton
                    size="small"
                    onClick={onDelete}
                    color="error"
                    sx={{
                      bgcolor: alpha(theme.palette.error.main, 0.1),
                      '&:hover': { bgcolor: alpha(theme.palette.error.main, 0.2) }
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              {facture.statut !== 'PAID' && onPayment && (
                <Tooltip title="Enregistrer paiement">
                  <IconButton
                    size="small"
                    onClick={onPayment}
                    color="success"
                    sx={{
                      bgcolor: alpha(theme.palette.success.main, 0.1),
                      '&:hover': { bgcolor: alpha(theme.palette.success.main, 0.2) }
                    }}
                  >
                    <PaidIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Stack>

            <Stack direction="row" spacing={1}>
              <Button
                size="small"
                startIcon={<PdfIcon />}
                onClick={onPdf}
                variant="outlined"
                fullWidth
                sx={{
                  borderRadius: 1.5,
                  textTransform: 'none',
                  borderColor: alpha(theme.palette.text.primary, 0.2),
                  color: theme.palette.text.primary,
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                PDF
              </Button>
              <Button
                size="small"
                startIcon={<EmailIcon />}
                onClick={onEmail}
                variant="outlined"
                fullWidth
                sx={{
                  borderRadius: 1.5,
                  textTransform: 'none',
                  borderColor: alpha(theme.palette.info.main, 0.5),
                  color: theme.palette.info.main,
                  '&:hover': {
                    borderColor: theme.palette.info.main,
                    bgcolor: alpha(theme.palette.info.main, 0.05)
                  }
                }}
              >
                Email
              </Button>
              <Button
                size="small"
                startIcon={<PrintIcon />}
                onClick={onPrint}
                variant="outlined"
                fullWidth
                sx={{
                  borderRadius: 1.5,
                  textTransform: 'none',
                  borderColor: alpha(theme.palette.text.primary, 0.2),
                  color: theme.palette.text.primary,
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                Imprimer
              </Button>
            </Stack>
          </>
        ) : (
          <>
            <Stack direction="row" spacing={1} justifyContent="center" sx={{ mb: 1.5 }}>
              <Button
                size="small"
                onClick={onEdit}
                variant="contained"
                color="primary"
                fullWidth
                startIcon={<ReceiptIcon />}
                sx={{
                  borderRadius: 1.5,
                  textTransform: 'none',
                  boxShadow: '0 4px 10px rgba(0,0,0,0.1)'
                }}
              >
                Voir détails
              </Button>
            </Stack>

            <Stack direction="row" spacing={1}>
              <Button
                size="small"
                startIcon={<PdfIcon />}
                onClick={onPdf}
                variant="outlined"
                fullWidth
                sx={{
                  borderRadius: 1.5,
                  textTransform: 'none',
                  borderColor: alpha(theme.palette.text.primary, 0.2),
                  color: theme.palette.text.primary,
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                PDF
              </Button>
              <Button
                size="small"
                startIcon={<PrintIcon />}
                onClick={onPrint}
                variant="outlined"
                fullWidth
                sx={{
                  borderRadius: 1.5,
                  textTransform: 'none',
                  borderColor: alpha(theme.palette.text.primary, 0.2),
                  color: theme.palette.text.primary,
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                Imprimer
              </Button>

              {facture.statut !== 'PAID' && (
                <Button
                  size="small"
                  startIcon={<PaidIcon />}
                  onClick={onPayment}
                  variant="outlined"
                  color="success"
                  fullWidth
                  sx={{
                    borderRadius: 1.5,
                    textTransform: 'none',
                    borderColor: alpha(theme.palette.success.main, 0.5),
                    '&:hover': {
                      borderColor: theme.palette.success.main,
                      bgcolor: alpha(theme.palette.success.main, 0.05)
                    }
                  }}
                >
                  Payer
                </Button>
              )}
            </Stack>
          </>
        )}
      </Box>
    </Card>
  );
};

export default FactureCard;