{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["bailoutToClientRendering", "reason", "workStore", "workAsyncStorage", "getStore", "forceStatic", "isStaticGeneration", "BailoutToCSRError"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;8BAHkB;0CACD;AAE1B,SAASA,yBAAyBC,MAAc;IACrD,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAE3C,IAAIF,6BAAAA,UAAWG,WAAW,EAAE;IAE5B,IAAIH,6BAAAA,UAAWI,kBAAkB,EAAE,MAAM,qBAA6B,CAA7B,IAAIC,+BAAiB,CAACN,SAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAA4B;AACvE"}