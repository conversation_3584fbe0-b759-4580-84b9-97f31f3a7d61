\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage[french]{babel}
\usepackage{geometry}
\usepackage{array}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{colortbl}

\geometry{a4paper, margin=2cm}

\title{Sprint 3 Unifié - Consolidation Sprint 2 \& 3}
\author{Système de Gestion de Factures et Devis}
\date{\today}

\begin{document}

\maketitle

\section{Vue d'ensemble du Sprint 3 Unifié}

Le Sprint 3 unifié consolide les fonctionnalités essentielles des anciens Sprint 2 et Sprint 3 en quatre domaines stratégiques : la gestion des données de base, la création et gestion documentaire complète, la gestion des abonnements avec surveillance, et la gestion financière intégrée. Cette consolidation optimise le développement en regroupant les fonctionnalités interdépendantes.

\section{Tableau 1 : Gestion des Données de Base}

\begin{table}[h]
\centering
\caption{Thème A - Gestion des Données de Base}
\begin{tabular}{|p{3cm}|p{8cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{lightgray}
\textbf{ID Stories} & \textbf{Description} & \textbf{Importance} & \textbf{Durée} \\
\hline
3.1 & En tant que vendeur ou responsable, je veux consulter le catalogue de produits et services et gérer les produits. & Élevée & 5j \\
\hline
3.2 & En tant que responsable d'entreprise ou vendeur, je veux gérer les informations des clients. & Élevée & 4j \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Thème A:}} & \textbf{9j} \\
\hline
\end{tabular}
\end{table}

\subsection{Description Textuelle - Thème A}

\textbf{Objectif :} Établir les fondations de données nécessaires pour la création de documents commerciaux en permettant une gestion complète des produits et clients.

\textbf{Acteurs principaux :} Vendeur, Responsable d'entreprise

\textbf{Pré-conditions :}
\begin{itemize}
\item L'utilisateur doit être authentifié avec le rôle Vendeur ou Responsable
\item L'entreprise doit être configurée dans le système
\item Les autorisations d'accès aux modules produits et clients doivent être activées
\end{itemize}

\textbf{Scénario détaillé - Gestion des Produits (Story 3.1) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le vendeur/responsable se connecte au système avec ses identifiants
\item Le système affiche le tableau de bord avec accès au module "Produits"
\item L'utilisateur clique sur "Gestion des Produits" depuis le menu principal
\item Le système affiche la liste des produits existants avec les colonnes : Nom, Référence, Prix unitaire, Stock, Catégorie, Statut
\item L'utilisateur peut effectuer les actions suivantes :
   \begin{itemize}
   \item \textbf{Consulter :} Visualiser les détails d'un produit en cliquant sur sa ligne
   \item \textbf{Ajouter :} Cliquer sur "Nouveau Produit" et remplir le formulaire (nom, description, prix, TVA, stock, catégorie)
   \item \textbf{Modifier :} Sélectionner un produit et cliquer sur "Modifier" pour éditer ses informations
   \item \textbf{Supprimer :} Sélectionner un produit et cliquer sur "Supprimer" (avec confirmation)
   \item \textbf{Rechercher :} Utiliser la barre de recherche pour filtrer par nom, référence ou catégorie
   \end{itemize}
\item Le système sauvegarde automatiquement toute modification
\item Le système affiche un message de confirmation pour chaque action réussie
\end{enumerate}

\textbf{Scénario détaillé - Gestion des Clients (Story 3.2) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le responsable/vendeur accède au module "Clients" depuis le menu principal
\item Le système affiche la liste des clients avec : Nom, Email, Téléphone, Adresse, Statut, Date création
\item L'utilisateur peut gérer les clients avec les fonctionnalités suivantes :
   \begin{itemize}
   \item \textbf{Ajouter un client :} Formulaire avec champs obligatoires (nom, email, téléphone) et optionnels (adresse, notes)
   \item \textbf{Modifier les informations :} Édition de tous les champs client avec validation des données
   \item \textbf{Consulter l'historique :} Visualisation des factures et devis associés au client
   \item \textbf{Gérer le statut :} Activation/désactivation du compte client
   \item \textbf{Recherche avancée :} Filtrage par nom, email, statut, ou période de création
   \end{itemize}
\item Le système valide les données saisies (format email, numéro de téléphone)
\item Le système empêche la suppression d'un client ayant des documents associés
\item Le système affiche les informations de contact et l'historique commercial
\end{enumerate}

\textbf{Post-conditions :}
\begin{itemize}
\item Les données produits et clients sont mises à jour dans la base de données
\item Les informations sont disponibles pour la création de factures et devis
\item L'historique des modifications est enregistré pour audit
\end{itemize}

\textbf{Exceptions :}
\begin{itemize}
\item \textbf{Données invalides :} Le système affiche "Veuillez vérifier les champs obligatoires"
\item \textbf{Produit déjà existant :} Le système affiche "Cette référence produit existe déjà"
\item \textbf{Client avec email existant :} Le système affiche "Un client avec cet email existe déjà"
\end{itemize}

\section{Tableau 2 : Gestion des Abonnements et Surveillance}

\begin{table}[h]
\centering
\caption{Thème B - Gestion des Abonnements et Surveillance}
\begin{tabular}{|p{3cm}|p{8cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{lightgray}
\textbf{ID Stories} & \textbf{Description} & \textbf{Importance} & \textbf{Durée} \\
\hline
3.3 & En tant qu'administrateur, je souhaite créer des abonnements pour le responsable d'entreprise avec durées variables & Élevée & 4j \\
\hline
3.4 & En tant qu'administrateur, je souhaite surveiller et bloquer automatiquement les comptes expirés & Élevée & 3j \\
\hline
3.5 & En tant que responsable d'entreprise, je souhaite consulter le statut, demander un renouvellement et surveiller limites avec alertes automatiques & Élevée & 3j \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Thème B:}} & \textbf{10j} \\
\hline
\end{tabular}
\end{table}

\subsection{Description Textuelle - Thème B}

\textbf{Objectif :} Mettre en place un système complet de gestion des abonnements avec surveillance automatique et alertes proactives pour assurer la continuité de service.

\textbf{Acteurs principaux :} Administrateur, Responsable d'entreprise

\textbf{Pré-conditions :}
\begin{itemize}
\item L'administrateur doit avoir accès au panneau d'administration
\item Les entreprises doivent être enregistrées dans le système
\item Les paramètres de notification doivent être configurés
\end{itemize}

\textbf{Scénario détaillé - Création d'Abonnements (Story 3.3) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item L'administrateur se connecte au panneau d'administration
\item L'administrateur accède à la section "Gestion des Abonnements"
\item Le système affiche la liste des entreprises avec leur statut d'abonnement actuel
\item L'administrateur sélectionne une entreprise et clique sur "Créer Abonnement"
\item Le système affiche un formulaire avec les options suivantes :
   \begin{itemize}
   \item \textbf{Type d'abonnement :} Basique, Standard, Premium
   \item \textbf{Durée :} 1 mois, 3 mois, 6 mois, 12 mois, personnalisée
   \item \textbf{Date de début :} Sélection de date (par défaut : aujourd'hui)
   \item \textbf{Limites :} Nombre de factures, utilisateurs, stockage
   \item \textbf{Prix :} Calcul automatique selon la durée et le type
   \end{itemize}
\item L'administrateur remplit les informations et clique sur "Créer"
\item Le système génère l'abonnement et envoie une notification au responsable d'entreprise
\item Le système active automatiquement les fonctionnalités selon le type d'abonnement
\end{enumerate}

\textbf{Scénario détaillé - Surveillance Automatique (Story 3.4) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le système exécute une tâche automatique quotidienne à 00:00
\item Le système vérifie tous les abonnements et identifie ceux qui expirent dans :
   \begin{itemize}
   \item 30 jours : Première alerte
   \item 15 jours : Deuxième alerte
   \item 7 jours : Alerte urgente
   \item 1 jour : Alerte critique
   \item 0 jour : Expiration - blocage automatique
   \end{itemize}
\item Pour les comptes expirés, le système :
   \begin{itemize}
   \item Change le statut à "Suspendu"
   \item Bloque l'accès aux fonctionnalités principales
   \item Conserve les données en lecture seule
   \item Envoie une notification de suspension
   \end{itemize}
\item L'administrateur reçoit un rapport quotidien des actions effectuées
\item Le système maintient un historique des suspensions et réactivations
\end{enumerate}

\textbf{Scénario détaillé - Consultation et Renouvellement (Story 3.5) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le responsable d'entreprise accède à la section "Mon Abonnement"
\item Le système affiche un tableau de bord avec :
   \begin{itemize}
   \item Statut actuel (Actif, Expirant, Suspendu)
   \item Date d'expiration avec compte à rebours
   \item Utilisation actuelle vs limites (factures, utilisateurs, stockage)
   \item Historique des paiements et renouvellements
   \end{itemize}
\item Si l'abonnement expire dans moins de 30 jours, le système affiche une bannière d'alerte
\item Le responsable peut cliquer sur "Demander un Renouvellement"
\item Le système génère une demande de renouvellement envoyée à l'administrateur
\item Le responsable reçoit une confirmation de la demande avec un numéro de référence
\item Le système envoie des alertes automatiques par email selon les seuils configurés
\end{enumerate}

\textbf{Post-conditions :}
\begin{itemize}
\item Les abonnements sont créés et activés dans la base de données
\item Les comptes expirés sont automatiquement suspendus
\item Les demandes de renouvellement sont enregistrées et suivies
\item Les alertes sont envoyées selon la planification configurée
\end{itemize}

\textbf{Exceptions :}
\begin{itemize}
\item \textbf{Abonnement déjà actif :} Le système affiche "Un abonnement actif existe déjà"
\item \textbf{Dates invalides :} Le système affiche "La date de fin doit être postérieure à la date de début"
\item \textbf{Échec de notification :} Le système affiche "Alerte : Échec d'envoi de notification"
\end{itemize}

\section{Tableau 3 : Gestion des Paiements Multi-Rôles}

\begin{table}[h]
\centering
\caption{Thème C - Gestion des Paiements Multi-Rôles}
\begin{tabular}{|p{3cm}|p{8cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{lightgray}
\textbf{ID Stories} & \textbf{Description} & \textbf{Importance} & \textbf{Durée} \\
\hline
3.6 & En tant que responsable, je souhaite enregistrer les paiements reçus et suivre les factures payées/impayées & Élevée & 3j \\
\hline
3.7 & En tant que vendeur, je souhaite enregistrer les paiements reçus et suivre les factures payées/impayées & Élevée & 5j \\
\hline
3.8 & En tant que client, je souhaite enregistrer les paiements reçus et suivre les factures payées/impayées & Élevée & 3j \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Thème C:}} & \textbf{11j} \\
\hline
\end{tabular}
\end{table}

\subsection{Description Textuelle - Thème C}

\textbf{Objectif :} Implémenter un système complet de gestion des paiements permettant à tous les acteurs de suivre et gérer les transactions financières selon leurs rôles respectifs.

\textbf{Acteurs principaux :} Responsable d'entreprise, Vendeur, Client

\textbf{Pré-conditions :}
\begin{itemize}
\item Les utilisateurs doivent être authentifiés avec leurs rôles respectifs
\item Les factures doivent exister dans le système
\item Les méthodes de paiement doivent être configurées
\item Les autorisations de gestion financière doivent être activées
\end{itemize}

\textbf{Scénario détaillé - Gestion Paiements Responsable (Story 3.6) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le responsable accède au module "Gestion des Paiements" depuis son tableau de bord
\item Le système affiche une vue d'ensemble avec :
   \begin{itemize}
   \item Tableau des factures en attente de paiement
   \item Historique des paiements reçus
   \item Statistiques financières (montants, délais moyens)
   \item Alertes pour les factures en retard
   \end{itemize}
\item Pour enregistrer un paiement, le responsable :
   \begin{itemize}
   \item Sélectionne une facture impayée
   \item Clique sur "Enregistrer Paiement"
   \item Saisit : montant, date, méthode (espèces, chèque, virement, carte)
   \item Ajoute des notes optionnelles
   \item Télécharge une pièce justificative si nécessaire
   \end{itemize}
\item Le système met à jour automatiquement le statut de la facture
\item Le système génère un reçu de paiement
\item Le client reçoit une notification de confirmation de paiement
\item Le responsable peut générer des rapports de suivi des paiements
\end{enumerate}

\textbf{Scénario détaillé - Gestion Paiements Vendeur (Story 3.7) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le vendeur accède à ses factures depuis le menu "Mes Ventes"
\item Le système affiche les factures créées par le vendeur avec leur statut de paiement
\item Le vendeur peut :
   \begin{itemize}
   \item Visualiser les factures payées/impayées de ses clients
   \item Enregistrer les paiements reçus directement
   \item Suivre les délais de paiement de ses clients
   \item Générer des relances automatiques pour les impayés
   \item Consulter ses statistiques de vente et encaissement
   \end{itemize}
\item Pour un paiement partiel, le vendeur :
   \begin{itemize}
   \item Sélectionne la facture concernée
   \item Saisit le montant partiel reçu
   \item Le système calcule automatiquement le solde restant
   \item Le statut passe à "Partiellement payée"
   \end{itemize}
\item Le vendeur peut imprimer des reçus de paiement
\item Le système synchronise les données avec le responsable d'entreprise
\end{enumerate}

\textbf{Scénario détaillé - Consultation Paiements Client (Story 3.8) :}

\textbf{Scénario nominal :}
\begin{enumerate}
\item Le client se connecte à son espace personnel
\item Le système affiche ses factures avec les statuts :
   \begin{itemize}
   \item En attente de paiement (avec montant et échéance)
   \item Payées (avec date et méthode de paiement)
   \item En retard (avec pénalités éventuelles)
   \end{itemize}
\item Le client peut :
   \begin{itemize}
   \item Consulter le détail de chaque facture
   \item Télécharger les factures et reçus de paiement
   \item Voir l'historique complet de ses transactions
   \item Recevoir des notifications de nouvelles factures
   \item Accéder aux informations de paiement (RIB, références)
   \end{itemize}
\item Pour effectuer un paiement en ligne (si activé) :
   \begin{itemize}
   \item Le client clique sur "Payer en ligne"
   \item Le système redirige vers la plateforme de paiement sécurisée
   \item Après paiement, le statut est mis à jour automatiquement
   \item Le client reçoit une confirmation par email
   \end{itemize}
\item Le client peut contacter l'entreprise directement depuis l'interface
\end{enumerate}

\textbf{Post-conditions :}
\begin{itemize}
\item Les paiements sont enregistrés et les statuts des factures mis à jour
\item Les reçus de paiement sont générés automatiquement
\item Les notifications sont envoyées aux parties concernées
\item L'historique des transactions est maintenu pour audit
\item Les rapports financiers sont mis à jour en temps réel
\end{itemize}

\textbf{Exceptions :}
\begin{itemize}
\item \textbf{Montant invalide :} Le système affiche "Le montant doit être positif et inférieur au solde dû"
\item \textbf{Facture déjà payée :} Le système affiche "Cette facture est déjà entièrement payée"
\item \textbf{Droits insuffisants :} Le système affiche "Vous n'avez pas l'autorisation pour cette action"
\item \textbf{Échec de paiement en ligne :} Le système affiche "Échec du paiement, veuillez réessayer"
\end{itemize}

\section{Résumé du Sprint 3}

\begin{table}[h]
\centering
\caption{Récapitulatif Sprint 3 - Consolidation par Thèmes}
\begin{tabular}{|l|l|l|l|}
\hline
\rowcolor{lightgray}
\textbf{Thème} & \textbf{Stories} & \textbf{Durée} & \textbf{Priorité} \\
\hline
Gestion Données de Base & 3.1, 3.2 & 9 jours & Élevée \\
\hline
Abonnements \& Surveillance & 3.3, 3.4, 3.5 & 10 jours & Élevée \\
\hline
Paiements Multi-Rôles & 3.6, 3.7, 3.8 & 11 jours & Élevée \\
\hline
\multicolumn{3}{|r|}{\textbf{Total Sprint 3:}} & \textbf{30 jours} \\
\hline
\end{tabular}
\end{table}

Ce sprint établit les fondations essentielles du système en consolidant la gestion des données de base, en automatisant la surveillance des abonnements, et en implémentant un système de paiement complet pour tous les rôles utilisateurs.

\end{document}
