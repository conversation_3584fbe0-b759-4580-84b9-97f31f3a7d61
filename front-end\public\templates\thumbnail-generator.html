<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur de Miniatures de Templates</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
        }
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .preview {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            width: 300px;
        }
        .preview h3 {
            margin-top: 0;
        }
        canvas {
            width: 100%;
            border: 1px solid #eee;
        }
        .download-all {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .form-group {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Générateur de Miniatures de Templates</h1>
    
    <div class="controls">
        <div class="form-group">
            <label for="template-type">Type de document:</label>
            <select id="template-type">
                <option value="facture">Facture</option>
                <option value="devis">Devis</option>
                <option value="both">Les deux</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="template-layout">Layout:</label>
            <select id="template-layout">
                <option value="standard">Standard</option>
                <option value="modern">Moderne</option>
                <option value="elegant">Élégant</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="template-color">Couleur principale:</label>
            <input type="color" id="template-color" value="#3b82f6">
        </div>
        
        <div class="form-group">
            <label for="template-secondary-color">Couleur secondaire:</label>
            <input type="color" id="template-secondary-color" value="#f8fafc">
        </div>
        
        <button id="generate-btn">Générer la miniature</button>
        <button id="generate-all-btn">Générer toutes les combinaisons</button>
    </div>
    
    <div class="preview-container" id="preview-container">
        <!-- Previews will be added here -->
    </div>
    
    <button class="download-all" id="download-all-btn">Télécharger toutes les miniatures</button>
    
    <script>
        // Configuration
        const width = 300;
        const height = 200;
        const types = ['facture', 'devis', 'both'];
        const layouts = ['standard', 'modern', 'elegant'];
        
        // DOM elements
        const typeSelect = document.getElementById('template-type');
        const layoutSelect = document.getElementById('template-layout');
        const colorInput = document.getElementById('template-color');
        const secondaryColorInput = document.getElementById('template-secondary-color');
        const generateBtn = document.getElementById('generate-btn');
        const generateAllBtn = document.getElementById('generate-all-btn');
        const downloadAllBtn = document.getElementById('download-all-btn');
        const previewContainer = document.getElementById('preview-container');
        
        // Generate a single thumbnail
        function generateThumbnail(type, layout, color, secondaryColor) {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, width, height);
            
            // Header with template color
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, width, 40);
            
            // Document title
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(type === 'devis' ? 'DEVIS' : 'FACTURE', width / 2, 25);
            
            // Company and client info
            ctx.fillStyle = '#333333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('ÉMETTEUR', 20, 60);
            ctx.fillText('Votre Entreprise', 20, 75);
            
            ctx.textAlign = 'right';
            ctx.fillText('DESTINATAIRE', width - 20, 60);
            ctx.fillText('Client Exemple', width - 20, 75);
            
            // Table header
            ctx.fillStyle = color;
            ctx.fillRect(20, 100, width - 40, 25);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('Description', 30, 117);
            ctx.textAlign = 'center';
            ctx.fillText('Quantité', width / 2, 117);
            ctx.textAlign = 'right';
            ctx.fillText('Total', width - 30, 117);
            
            // Table rows
            ctx.fillStyle = '#333333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('Produit/Service 1', 30, 145);
            ctx.textAlign = 'center';
            ctx.fillText('1', width / 2, 145);
            ctx.textAlign = 'right';
            ctx.fillText('100.00 €', width - 30, 145);
            
            // Total
            ctx.fillStyle = color;
            ctx.fillRect(width - 120, 170, 100, 25);
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Total: 100.00 €', width - 70, 187);
            
            // Add layout-specific elements
            if (layout === 'modern') {
                // Add a modern touch - rounded corners for the header
                ctx.fillStyle = secondaryColor;
                ctx.fillRect(0, 40, width, 5);
            } else if (layout === 'elegant') {
                // Add an elegant touch - decorative line
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(20, 90);
                ctx.lineTo(width - 20, 90);
                ctx.stroke();
            }
            
            return {
                canvas,
                dataUrl: canvas.toDataURL('image/png'),
                filename: `template-${type}-${layout}.png`
            };
        }
        
        // Add a preview to the container
        function addPreview(type, layout, color, secondaryColor) {
            const thumbnail = generateThumbnail(type, layout, color, secondaryColor);
            
            const previewDiv = document.createElement('div');
            previewDiv.className = 'preview';
            previewDiv.innerHTML = `
                <h3>Template ${type} - ${layout}</h3>
                <canvas id="canvas-${type}-${layout}"></canvas>
                <p>Couleur: ${color}</p>
                <p>Couleur secondaire: ${secondaryColor}</p>
                <button class="download-btn" data-filename="${thumbnail.filename}">Télécharger</button>
            `;
            
            previewContainer.appendChild(previewDiv);
            
            // Draw on the canvas
            const canvas = previewDiv.querySelector(`#canvas-${type}-${layout}`);
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.onload = () => {
                ctx.drawImage(img, 0, 0);
            };
            img.src = thumbnail.dataUrl;
            
            // Add download handler
            const downloadBtn = previewDiv.querySelector('.download-btn');
            downloadBtn.addEventListener('click', () => {
                const link = document.createElement('a');
                link.download = thumbnail.filename;
                link.href = thumbnail.dataUrl;
                link.click();
            });
            
            return thumbnail;
        }
        
        // Generate a single thumbnail from form values
        generateBtn.addEventListener('click', () => {
            const type = typeSelect.value;
            const layout = layoutSelect.value;
            const color = colorInput.value;
            const secondaryColor = secondaryColorInput.value;
            
            addPreview(type, layout, color, secondaryColor);
        });
        
        // Generate all combinations
        generateAllBtn.addEventListener('click', () => {
            previewContainer.innerHTML = '';
            const thumbnails = [];
            
            types.forEach(type => {
                layouts.forEach(layout => {
                    const color = colorInput.value;
                    const secondaryColor = secondaryColorInput.value;
                    const thumbnail = addPreview(type, layout, color, secondaryColor);
                    thumbnails.push(thumbnail);
                });
            });
        });
        
        // Download all thumbnails
        downloadAllBtn.addEventListener('click', () => {
            const downloadButtons = document.querySelectorAll('.download-btn');
            downloadButtons.forEach(btn => {
                setTimeout(() => {
                    btn.click();
                }, 100);
            });
        });
        
        // Initialize with a default preview
        addPreview('facture', 'standard', '#3b82f6', '#f8fafc');
    </script>
</body>
</html>
