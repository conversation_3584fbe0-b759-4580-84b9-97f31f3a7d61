const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const Facture = require('../models/FactureModel');
const Devis = require('../models/DevisModel');
const Client = require('../models/ClientModel');
const User = require('../models/UserModel');
const Produit = require('../models/ProduitModel');
const mongoose = require('mongoose');

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

/**
 * Export dashboard data to CSV
 * @route GET /export
 * @param {string} format - Export format (csv, excel, pdf)
 * @param {string} type - Data type (vendor-dashboard, enterprise-dashboard, admin-dashboard)
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 */
router.get('/export', verifyToken, async (req, res) => {
  try {
    const { format = 'csv', type, userId, startDate, endDate } = req.query;

    // Parse dates
    const start = startDate ? new Date(startDate) : new Date(new Date().setMonth(new Date().getMonth() - 1));
    const end = endDate ? new Date(endDate) : new Date();

    // Validate user permissions
    if (req.userRole !== 'ADMIN' && req.userId !== userId) {
      return res.status(403).json({ error: 'Accès non autorisé' });
    }

    let data = [];
    let filename = '';

    // Get data based on type
    switch (type) {
      case 'vendor-dashboard':
        data = await getVendorDashboardData(userId, start, end);
        filename = `vendor-dashboard-${format === 'csv' ? 'csv' : format === 'excel' ? 'xlsx' : 'pdf'}`;
        break;
      case 'enterprise-dashboard':
        data = await getEnterpriseDashboardData(userId, start, end);
        filename = `enterprise-dashboard-${format === 'csv' ? 'csv' : format === 'excel' ? 'xlsx' : 'pdf'}`;
        break;
      case 'admin-dashboard':
        data = await getAdminDashboardData(start, end);
        filename = `admin-dashboard-${format === 'csv' ? 'csv' : format === 'excel' ? 'xlsx' : 'pdf'}`;
        break;
      default:
        return res.status(400).json({ error: 'Type de données non valide' });
    }

    // Export based on format
    switch (format) {
      case 'csv':
        return exportToCsv(res, data, filename);
      case 'excel':
        return exportToCsv(res, data, filename); // For now, use CSV for Excel too
      case 'pdf':
        return exportToCsv(res, data, filename); // For now, use CSV for PDF too
      default:
        return res.status(400).json({ error: 'Format non valide' });
    }
  } catch (error) {
    console.error('Error exporting data:', error);
    res.status(500).json({ error: 'Erreur lors de l\'exportation des données' });
  }
});

/**
 * Export data to CSV
 * @param {Object} res - Response object
 * @param {Array} data - Data to export
 * @param {string} filename - Filename
 */
function exportToCsv(res, data, filename) {
  // Create CSV header
  const headers = Object.keys(data[0] || {});
  let csv = headers.join(',') + '\n';

  // Add data rows
  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header];
      // Handle special cases (commas, quotes, etc.)
      if (value === null || value === undefined) return '';
      if (typeof value === 'string') return `"${value.replace(/"/g, '""')}"`;
      if (value instanceof Date) return value.toISOString();
      return value;
    });
    csv += values.join(',') + '\n';
  });

  // Set headers and send response
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
  res.status(200).send(csv);
}

/**
 * Get vendor dashboard data
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {Promise<Array>} Dashboard data
 */
async function getVendorDashboardData(userId, startDate, endDate) {
  // Get invoices for the vendor
  const invoices = await Facture.find({
    dateEmission: { $gte: startDate, $lte: endDate }
  }).populate('clientId');

  // Format invoice data for export
  return invoices.map(invoice => ({
    invoice_id: invoice._id.toString(),
    invoice_number: invoice.numero,
    date: invoice.dateEmission.toISOString().split('T')[0],
    client: invoice.clientId ? invoice.clientId.nom : 'Client inconnu',
    status: invoice.statut,
    total: invoice.total,
    tva: invoice.tauxTVA,
    total_ttc: invoice.total * (1 + invoice.tauxTVA / 100)
  }));
}

/**
 * Get enterprise dashboard data
 * @param {string} userId - User ID
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {Promise<Array>} Dashboard data
 */
async function getEnterpriseDashboardData(userId, startDate, endDate) {
  // Get invoices for the enterprise
  const invoices = await Facture.find({
    dateEmission: { $gte: startDate, $lte: endDate }
  }).populate('clientId');

  // Format invoice data for export
  return invoices.map(invoice => ({
    invoice_id: invoice._id.toString(),
    invoice_number: invoice.numero,
    date: invoice.dateEmission.toISOString().split('T')[0],
    client: invoice.clientId ? invoice.clientId.nom : 'Client inconnu',
    status: invoice.statut,
    total: invoice.total,
    tva: invoice.tauxTVA,
    total_ttc: invoice.total * (1 + invoice.tauxTVA / 100)
  }));
}

/**
 * Get admin dashboard data
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {Promise<Array>} Dashboard data
 */
async function getAdminDashboardData(startDate, endDate) {
  // Get all invoices
  const invoices = await Facture.find({
    dateEmission: { $gte: startDate, $lte: endDate }
  }).populate('clientId');

  // Format invoice data for export
  return invoices.map(invoice => ({
    invoice_id: invoice._id.toString(),
    invoice_number: invoice.numero,
    date: invoice.dateEmission.toISOString().split('T')[0],
    client: invoice.clientId ? invoice.clientId.nom : 'Client inconnu',
    status: invoice.statut,
    total: invoice.total,
    tva: invoice.tauxTVA,
    total_ttc: invoice.total * (1 + invoice.tauxTVA / 100)
  }));
}

/**
 * Export invoice data for Power BI
 * @route GET /analytics/export/invoices
 * @param {Date} startDate - Start date (optional)
 * @param {Date} endDate - End date (optional)
 */
router.get('/analytics/export/invoices', verifyToken, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Build query filter
    let filter = {};

    // Add date filter if provided
    if (startDate || endDate) {
      filter.dateEmission = {};
      if (startDate) filter.dateEmission.$gte = new Date(startDate);
      if (endDate) filter.dateEmission.$lte = new Date(endDate);
    }

    // Add user role-based filters
    if (req.userRole === 'VENDEUR') {
      filter.vendeurId = new mongoose.Types.ObjectId(req.userId);
    } else if (req.userRole === 'ENTREPRISE') {
      filter.entrepriseId = new mongoose.Types.ObjectId(req.userId);
    }

    // Get invoices with populated client data
    const invoices = await Facture.find(filter)
      .populate('clientId')
      .populate('produits.produitId')
      .sort({ dateEmission: -1 });

    // Format data for export
    const formattedData = invoices.map(invoice => {
      const totalTTC = invoice.total * (1 + invoice.tauxTVA / 100);

      return {
        id: invoice._id.toString(),
        numero: invoice.numero,
        dateEmission: invoice.dateEmission.toISOString().split('T')[0],
        client: invoice.clientId ? invoice.clientId.nom : 'Client inconnu',
        statut: invoice.statut,
        total: invoice.total.toFixed(2),
        tauxTVA: invoice.tauxTVA.toFixed(2),
        totalTTC: totalTTC.toFixed(2),
        notes: invoice.notes || '',
        createdAt: invoice.createdAt ? invoice.createdAt.toISOString().split('T')[0] : '',
        produits: invoice.produits ? invoice.produits.length : 0,
        vendeur: invoice.vendeurId ? invoice.vendeurId.toString() : '',
        entreprise: invoice.entrepriseId ? invoice.entrepriseId.toString() : ''
      };
    });

    res.status(200).json(formattedData);
  } catch (error) {
    console.error('Error exporting invoice data:', error);
    res.status(500).json({ error: 'Error exporting invoice data', message: error.message });
  }
});

/**
 * Export client data for Power BI
 * @route GET /analytics/export/clients
 */
router.get('/analytics/export/clients', verifyToken, async (req, res) => {
  try {
    // Build query filter based on user role
    let filter = {};
    if (req.userRole === 'VENDEUR') {
      filter.vendeurId = mongoose.Types.ObjectId(req.userId);
    } else if (req.userRole === 'ENTREPRISE') {
      filter.entrepriseId = mongoose.Types.ObjectId(req.userId);
    }

    // Get clients
    const clients = await Client.find(filter).sort({ createdAt: -1 });

    // For each client, get invoice statistics
    const clientsWithStats = await Promise.all(clients.map(async (client) => {
      // Get all invoices for this client
      const invoices = await Facture.find({ clientId: client._id });

      // Calculate statistics
      const invoiceCount = invoices.length;
      const paidInvoices = invoices.filter(inv => inv.statut === 'PAID');
      const paidInvoiceCount = paidInvoices.length;
      const totalRevenue = paidInvoices.reduce((sum, inv) => sum + inv.total * (1 + inv.tauxTVA / 100), 0);

      return {
        id: client._id.toString(),
        nom: client.nom,
        email: client.email || '',
        telephone: client.telephone || '',
        adresse: client.adresse || '',
        contact: client.contact || '',
        invoiceCount,
        paidInvoiceCount,
        totalRevenue: totalRevenue.toFixed(2),
        createdAt: client.createdAt ? client.createdAt.toISOString().split('T')[0] : '',
        type: client.type || 'Standard',
        vendeur: client.vendeurId ? client.vendeurId.toString() : '',
        entreprise: client.entrepriseId ? client.entrepriseId.toString() : ''
      };
    }));

    res.status(200).json(clientsWithStats);
  } catch (error) {
    console.error('Error exporting client data:', error);
    res.status(500).json({ error: 'Error exporting client data', message: error.message });
  }
});

/**
 * Export product data for Power BI
 * @route GET /analytics/export/products
 */
router.get('/analytics/export/products', verifyToken, async (req, res) => {
  try {
    // Build query filter based on user role
    let filter = {};
    if (req.userRole === 'VENDEUR') {
      filter.vendeurId = mongoose.Types.ObjectId(req.userId);
    } else if (req.userRole === 'ENTREPRISE') {
      filter.entrepriseId = mongoose.Types.ObjectId(req.userId);
    }

    // Get products
    const products = await Produit.find(filter).sort({ nom: 1 });

    // For each product, get usage statistics from invoices
    const productsWithStats = await Promise.all(products.map(async (product) => {
      // Find all invoices that contain this product
      const invoices = await Facture.find({
        'produits.produitId': product._id
      });

      // Calculate statistics
      let totalQuantity = 0;
      let totalRevenue = 0;
      const invoiceCount = invoices.length;
      const paidInvoiceCount = invoices.filter(inv => inv.statut === 'PAID').length;

      // Calculate total quantity and revenue
      invoices.forEach(invoice => {
        const productEntry = invoice.produits.find(p =>
          p.produitId && p.produitId.toString() === product._id.toString()
        );

        if (productEntry) {
          totalQuantity += productEntry.quantite || 0;
          totalRevenue += (productEntry.prix * productEntry.quantite) || 0;
        }
      });

      // Calculate average unit price
      const averageUnitPrice = totalQuantity > 0 ? totalRevenue / totalQuantity : product.prix;

      return {
        id: product._id.toString(),
        name: product.nom,
        description: product.description || '',
        price: product.prix.toFixed(2),
        totalQuantity,
        totalRevenue: totalRevenue.toFixed(2),
        invoiceCount,
        paidInvoiceCount,
        averageUnitPrice: averageUnitPrice.toFixed(2),
        category: product.categorie || 'Non catégorisé',
        createdAt: product.createdAt ? product.createdAt.toISOString().split('T')[0] : '',
        vendeur: product.vendeurId ? product.vendeurId.toString() : '',
        entreprise: product.entrepriseId ? product.entrepriseId.toString() : ''
      };
    }));

    res.status(200).json(productsWithStats);
  } catch (error) {
    console.error('Error exporting product data:', error);
    res.status(500).json({ error: 'Error exporting product data', message: error.message });
  }
});

module.exports = router;
