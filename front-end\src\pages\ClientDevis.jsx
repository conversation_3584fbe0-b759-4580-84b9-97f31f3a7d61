import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  useTheme,
  alpha,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Avatar
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Visibility as VisibilityIcon,
  PictureAsPdf as PdfIcon,
  Print as PrintIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import devisService from '../services/devisService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';
import DevisCard from '../components/DevisCard';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

const ClientDevis = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [devis, setDevis] = useState([]);
  const [filteredDevis, setFilteredDevis] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    action: null,
    devisId: null
  });

  useEffect(() => {
    const fetchDevis = async () => {
      try {
        setLoading(true);
        const response = await devisService.getDevis();
        console.log('Devis récupérés du serveur:', response);
        console.log('ID client connecté:', currentUser.clientId);

        // Afficher les détails de chaque devis pour le débogage
        response.forEach((devis, index) => {
          console.log(`Devis ${index + 1}:`, devis);
          if (devis.clientId) {
            const clientIdStr = devis.clientId._id ? devis.clientId._id.toString() : devis.clientId.toString();
            console.log(`  - ID client du devis ${index + 1}:`, clientIdStr);
            console.log(`  - Nom client du devis ${index + 1}:`, devis.clientId.nom || 'Non défini');
          } else {
            console.log(`  - Devis ${index + 1} n'a pas de clientId`);
          }
        });

        console.log('ID client connecté (toString):', currentUser.clientId ? currentUser.clientId.toString() : 'Non défini');

        // Filtrer les devis pour n'afficher que ceux du client connecté
        // Utiliser toString() pour comparer les IDs comme des chaînes de caractères
        console.log('Type de currentUser.clientId:', typeof currentUser.clientId);
        console.log('Valeur de currentUser.clientId:', currentUser.clientId);

        console.log('Email de l\'utilisateur connecté:', currentUser.email);

        const clientDevis = response.filter(devis => {
          if (!devis.clientId) {
            console.log('Devis sans clientId ignoré');
            return false;
          }

          // Afficher plus d'informations sur le devis et son clientId
          console.log('Structure de devis.clientId:', devis.clientId);

          // Vérifier si clientId est un objet avec _id ou directement un ID
          let devisClientId;
          let devisClientEmail = '';

          if (typeof devis.clientId === 'object' && devis.clientId !== null) {
            devisClientId = devis.clientId._id ? devis.clientId._id.toString() : '';
            devisClientEmail = devis.clientId.email || '';
            console.log('devisClientId est un objet avec _id:', devisClientId);
            console.log('Email du client du devis:', devisClientEmail);
          } else {
            devisClientId = devis.clientId.toString();
            console.log('devisClientId est une chaîne ou un ID:', devisClientId);
          }

          let userClientId;
          if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
            userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
            console.log('userClientId est un objet avec _id:', userClientId);
          } else {
            userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
            console.log('userClientId est une chaîne ou un ID:', userClientId);
          }

          console.log('Comparaison ID:', devisClientId, '===', userClientId, devisClientId === userClientId);
          console.log('Comparaison Email:', devisClientEmail, '===', currentUser.email, devisClientEmail === currentUser.email);

          // Vérifier si l'ID correspond OU si l'email correspond
          const matchById = devisClientId === userClientId;
          const matchByEmail = devisClientEmail && currentUser.email && devisClientEmail.toLowerCase() === currentUser.email.toLowerCase();

          const match = matchById || matchByEmail;
          console.log('Résultat de la comparaison (ID ou Email):', match);

          return match;
        });

        console.log('Devis filtrés pour le client:', clientDevis);
        setDevis(clientDevis);
        setFilteredDevis(clientDevis);
        setLoading(false);

        // Si aucun devis n'est trouvé, afficher un message plus détaillé
        if (clientDevis.length === 0) {
          console.log('AUCUN DEVIS TROUVÉ POUR CE CLIENT. Vérifiez que:');
          console.log('1. Le client connecté a bien un ID:', currentUser.clientId);
          console.log('2. Les devis dans la base de données sont bien associés à ce client');
          console.log('3. Les IDs sont bien au format string lors de la comparaison');
        }
      } catch (error) {
        console.error('Erreur lors du chargement des devis:', error);
        setLoading(false);
      }
    };

    if (currentUser && currentUser.clientId) {
      fetchDevis();
    } else {
      console.error('Utilisateur non connecté ou sans clientId');
    }
  }, [currentUser]);

  useEffect(() => {
    applyFilters();
  }, [searchTerm, statusFilter, dateFilter, devis]);

  const applyFilters = () => {
    let filtered = [...devis];

    // Filtre par recherche
    if (searchTerm) {
      filtered = filtered.filter(devis =>
        devis.numéro.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (devis.clientId && devis.clientId.nom.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(devis => devis.statut === statusFilter);
    }

    // Filtre par date
    if (dateFilter !== 'all') {
      const now = new Date();
      const startDate = new Date();

      switch (dateFilter) {
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          break;
      }

      filtered = filtered.filter(devis => {
        const devisDate = new Date(devis.dateCréation);
        return devisDate >= startDate && devisDate <= now;
      });
    }

    setFilteredDevis(filtered);
  };

  const handleViewDevis = (id) => {
    navigate(`/client/devis/${id}`);
  };

  const handleGeneratePdf = async (id) => {
    try {
      await devisService.generatePdf(id);
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
    }
  };

  const handlePrint = async (id) => {
    try {
      await devisService.printDevis(id);
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
    }
  };

  const handleAcceptDevis = async (id) => {
    setConfirmDialog({
      open: true,
      title: 'Accepter le devis',
      message: 'Êtes-vous sûr de vouloir accepter ce devis ? Cette action ne peut pas être annulée.',
      action: 'accept',
      devisId: id
    });
  };

  const handleRejectDevis = async (id) => {
    setConfirmDialog({
      open: true,
      title: 'Refuser le devis',
      message: 'Êtes-vous sûr de vouloir refuser ce devis ? Cette action ne peut pas être annulée.',
      action: 'reject',
      devisId: id
    });
  };

  const handleConfirmAction = async () => {
    try {
      const { action, devisId } = confirmDialog;

      if (action === 'accept') {
        console.log(`Acceptation du devis ${devisId}`);
        await devisService.acceptDevis(devisId);
      } else if (action === 'reject') {
        console.log(`Refus du devis ${devisId}`);
        await devisService.rejectDevis(devisId);
      }

      // Rafraîchir les données
      const response = await devisService.getDevis();
      console.log('Devis récupérés après action:', response);

      // Filtrer les devis pour n'afficher que ceux du client connecté
      console.log('Type de currentUser.clientId (après action):', typeof currentUser.clientId);
      console.log('Valeur de currentUser.clientId (après action):', currentUser.clientId);

      const clientDevis = response.filter(devis => {
        if (!devis.clientId) {
          console.log('Devis sans clientId ignoré (après action)');
          return false;
        }

        // Afficher plus d'informations sur le devis et son clientId
        console.log('Structure de devis.clientId (après action):', devis.clientId);

        // Vérifier si clientId est un objet avec _id ou directement un ID
        let devisClientId;
        if (typeof devis.clientId === 'object' && devis.clientId !== null) {
          devisClientId = devis.clientId._id ? devis.clientId._id.toString() : '';
          console.log('devisClientId est un objet avec _id (après action):', devisClientId);
        } else {
          devisClientId = devis.clientId.toString();
          console.log('devisClientId est une chaîne ou un ID (après action):', devisClientId);
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
          userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
          console.log('userClientId est un objet avec _id (après action):', userClientId);
        } else {
          userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
          console.log('userClientId est une chaîne ou un ID (après action):', userClientId);
        }

        console.log('Comparaison (après action):', devisClientId, '===', userClientId, devisClientId === userClientId);

        // Essayer une comparaison plus souple si les deux IDs sont non vides
        const match = devisClientId === userClientId;
        console.log('Résultat de la comparaison (après action):', match);

        return match;
      });

      console.log('Devis filtrés après action:', clientDevis);
      setDevis(clientDevis);
      setFilteredDevis(clientDevis);

      setConfirmDialog({ ...confirmDialog, open: false });
    } catch (error) {
      console.error('Erreur lors de l\'action sur le devis:', error);
      setConfirmDialog({ ...confirmDialog, open: false });
    }
  };

  const handleCloseDialog = () => {
    setConfirmDialog({ ...confirmDialog, open: false });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header Section with Background */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={0}
        sx={{
          mb: 4,
          p: 3,
          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                width: 48,
                height: 48,
                mr: 2
              }}
            >
              <DescriptionIcon fontSize="medium" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                Mes Devis
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Consultez et gérez vos devis
              </Typography>
            </Box>
          </Box>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/client/devis/demande')}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              fontWeight: 'bold',
              boxShadow: '0 4px 10px rgba(58, 110, 165, 0.2)',
              '&:hover': {
                boxShadow: '0 6px 15px rgba(58, 110, 165, 0.3)',
                transform: 'translateY(-2px)'
              },
              transition: 'all 0.2s ease-in-out'
            }}
          >
            Demander un devis
          </Button>
        </Box>
      </Card>

      {/* Search and Filter Section */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <TextField
            variant="outlined"
            placeholder="Rechercher par numéro..."
            size="small"
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              maxWidth: { xs: '100%', sm: 400 },
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.common.white, 0.9)
              }
            }}
          />

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <FormControl size="small">
              <InputLabel>Statut</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Statut"
                sx={{
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              >
                <MenuItem value="all">Tous les statuts</MenuItem>
                <MenuItem value="DRAFT">Brouillon</MenuItem>
                <MenuItem value="SENT">Envoyé</MenuItem>
                <MenuItem value="PENDING">En attente</MenuItem>
                <MenuItem value="ACCEPTED">Accepté</MenuItem>
                <MenuItem value="REJECTED">Refusé</MenuItem>
                <MenuItem value="EXPIRED">Expiré</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small">
              <InputLabel>Période</InputLabel>
              <Select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                label="Période"
                sx={{
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              >
                <MenuItem value="all">Toutes les périodes</MenuItem>
                <MenuItem value="month">Dernier mois</MenuItem>
                <MenuItem value="quarter">Dernier trimestre</MenuItem>
                <MenuItem value="year">Dernière année</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant={viewMode === 'list' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('list')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Liste
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('grid')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Cartes
              </Button>
            </Box>
          </Box>
        </Box>
      </Card>

      {/* Table Section */}
      {viewMode === 'list' ? (
        <Card
          component={motion.div}
          variants={itemVariants}
          elevation={1}
          sx={{
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Numéro</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Montant</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Statut</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredDevis.length > 0 ? (
                  filteredDevis.map((devis) => (
                    <TableRow
                      key={devis._id}
                      hover
                      sx={{
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.03),
                          cursor: 'pointer'
                        }
                      }}
                    >
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {devis.numéro || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="body2">
                          {formatDate(devis.dateCréation)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(devis.total)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Chip
                          label={formatStatut(devis.statut)}
                          color={
                            devis.statut === 'ACCEPTED' || devis.statut === 'ACCEPTÉ' ? 'success' :
                            devis.statut === 'REJECTED' || devis.statut === 'REFUSÉ' ? 'error' :
                            devis.statut === 'EXPIRED' || devis.statut === 'EXPIRÉ' ? 'error' :
                            devis.statut === 'DRAFT' || devis.statut === 'BROUILLON' ? 'info' : 'warning'
                          }
                          size="small"
                          sx={{
                            fontWeight: 'medium',
                            borderRadius: 1,
                            px: 1
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 1.5 }}>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          {(devis.statut === 'PENDING' || devis.statut === 'SENT' || devis.statut === 'ENVOYÉ') && (
                            <>
                              <Tooltip title="Accepter">
                                <IconButton
                                  onClick={() => handleAcceptDevis(devis._id)}
                                  color="success"
                                  size="small"
                                  sx={{
                                    backgroundColor: alpha(theme.palette.success.main, 0.1),
                                    '&:hover': { backgroundColor: alpha(theme.palette.success.main, 0.2) }
                                  }}
                                >
                                  <CheckCircleIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Refuser">
                                <IconButton
                                  onClick={() => handleRejectDevis(devis._id)}
                                  color="error"
                                  size="small"
                                  sx={{
                                    backgroundColor: alpha(theme.palette.error.main, 0.1),
                                    '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.2) }
                                  }}
                                >
                                  <CancelIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                          <Tooltip title="Voir détails">
                            <IconButton
                              onClick={() => handleViewDevis(devis._id)}
                              color="primary"
                              size="small"
                              sx={{
                                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.2) }
                              }}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Générer PDF">
                            <IconButton
                              onClick={() => handleGeneratePdf(devis._id)}
                              size="small"
                              sx={{
                                color: theme.palette.text.secondary,
                                backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                              }}
                            >
                              <PdfIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Imprimer">
                            <IconButton
                              onClick={() => handlePrint(devis._id)}
                              size="small"
                              sx={{
                                color: theme.palette.text.secondary,
                                backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                              }}
                            >
                              <PrintIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        Aucun devis trouvé
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      ) : (
        <Box component={motion.div} variants={itemVariants}>
          <Grid container spacing={3}>
            {filteredDevis.length > 0 ? (
              filteredDevis.map((devis) => (
                <Grid item xs={12} sm={6} md={4} key={devis._id} component={motion.div} variants={itemVariants}>
                  <DevisCard
                    devis={devis}
                    onEdit={null}
                    onDelete={null}
                    onConvert={null}
                    onPdf={() => handleGeneratePdf(devis._id)}
                    onEmail={null}
                    onPrint={() => handlePrint(devis._id)}
                    isClient={true}
                    onAccept={(devis.statut === 'PENDING' || devis.statut === 'SENT' || devis.statut === 'ENVOYÉ') ?
                      () => handleAcceptDevis(devis._id) : null}
                    onReject={(devis.statut === 'PENDING' || devis.statut === 'SENT' || devis.statut === 'ENVOYÉ') ?
                      () => handleRejectDevis(devis._id) : null}
                    onClick={() => handleViewDevis(devis._id)}
                  />
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Card
                  component={motion.div}
                  variants={itemVariants}
                  sx={{
                    p: 5,
                    textAlign: 'center',
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`
                  }}
                >
                  <Box sx={{ mb: 2 }}>
                    <Avatar
                      sx={{
                        width: 70,
                        height: 70,
                        margin: '0 auto',
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main
                      }}
                    >
                      <DescriptionIcon sx={{ fontSize: 40 }} />
                    </Avatar>
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    Aucun devis trouvé
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400, mx: 'auto' }}>
                    Aucun devis ne correspond à vos critères de recherche.
                  </Typography>
                </Card>
              </Grid>
            )}
          </Grid>
        </Box>
      )}

      {/* Dialogue de confirmation */}
      <Dialog
        open={confirmDialog.open}
        onClose={handleCloseDialog}
      >
        <DialogTitle>{confirmDialog.title}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {confirmDialog.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmAction} color="primary" variant="contained" autoFocus>
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClientDevis;
