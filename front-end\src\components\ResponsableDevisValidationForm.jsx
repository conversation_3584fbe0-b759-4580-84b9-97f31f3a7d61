import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Divider,
  IconButton,
  Chip,
  Snackbar,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress
} from '@mui/material';
import {
  Check as CheckIcon,
  Close as CloseIcon,
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  ShoppingCart as ShoppingCartIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import devisService from '../services/devisService';
import { formatCurrency, formatDate, formatStatut } from '../utils/formatters';

const ResponsableDevisValidationForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [devis, setDevis] = useState(null);
  const [commentaires, setCommentaires] = useState('');
  
  const [openApproveDialog, setOpenApproveDialog] = useState(false);
  const [openRejectDialog, setOpenRejectDialog] = useState(false);
  
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  useEffect(() => {
    const fetchDevis = async () => {
      try {
        setLoading(true);
        const data = await devisService.getDevisById(id);
        setDevis(data);
      } catch (error) {
        console.error('Erreur lors du chargement du devis:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors du chargement du devis',
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchDevis();
    }
  }, [id]);

  const handleApprove = async () => {
    try {
      setLoading(true);
      await devisService.validerDemandeDevis(id, commentaires);
      
      setSnackbar({
        open: true,
        message: 'Devis validé avec succès',
        severity: 'success'
      });
      
      // Fermer le dialogue
      setOpenApproveDialog(false);
      
      // Rediriger vers la liste des devis après un court délai
      setTimeout(() => {
        navigate('/responsable/devis');
      }, 2000);
      
    } catch (error) {
      console.error('Erreur lors de la validation du devis:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la validation du devis',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    try {
      if (!commentaires.trim()) {
        setSnackbar({
          open: true,
          message: 'Veuillez fournir un motif de rejet',
          severity: 'error'
        });
        return;
      }
      
      setLoading(true);
      await devisService.rejeterDemandeDevis(id, commentaires);
      
      setSnackbar({
        open: true,
        message: 'Devis rejeté avec succès',
        severity: 'success'
      });
      
      // Fermer le dialogue
      setOpenRejectDialog(false);
      
      // Rediriger vers la liste des devis après un court délai
      setTimeout(() => {
        navigate('/responsable/devis');
      }, 2000);
      
    } catch (error) {
      console.error('Erreur lors du rejet du devis:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors du rejet du devis',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!devis) {
    return (
      <Box sx={{ mt: 4 }}>
        <Alert severity="warning">Devis non trouvé</Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/responsable/devis')}
          sx={{ mt: 2 }}
        >
          Retour aux devis
        </Button>
      </Box>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ mb: 4 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/responsable/devis')}
          sx={{ mb: 2 }}
        >
          Retour aux devis
        </Button>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Validation du devis {devis.numéro}
          </Typography>
          
          <Chip 
            label={formatStatut(devis.statut)} 
            color={
              devis.statut === 'WAITING_APPROVAL' ? 'warning' :
              devis.statut === 'APPROVED_INTERNAL' ? 'success' :
              devis.statut === 'REJECTED' ? 'error' : 'default'
            }
          />
        </Box>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          Veuillez examiner cette demande de devis et décider de la valider ou de la rejeter.
        </Typography>
      </Box>
      
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonIcon sx={{ mr: 1 }} />
            Informations client
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2">Nom</Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {devis.clientId?.nom || 'Non spécifié'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2">Email</Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {devis.clientId?.email || 'Non spécifié'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2">Adresse</Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {devis.clientId?.adresse || 'Non spécifiée'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2">Téléphone</Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {devis.clientId?.telephone || 'Non spécifié'}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <ShoppingCartIcon sx={{ mr: 1 }} />
            Produits et services demandés
          </Typography>
          
          {devis.lignes && devis.lignes.length > 0 ? (
            <TableContainer component={Paper} elevation={0} sx={{ mb: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell align="center">Quantité</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {devis.lignes.map((ligne, index) => (
                    <TableRow key={index}>
                      <TableCell>{ligne.description}</TableCell>
                      <TableCell align="center">{ligne.quantite}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
              <Typography>Aucun produit spécifié</Typography>
            </Box>
          )}
        </CardContent>
      </Card>
      
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <DescriptionIcon sx={{ mr: 1 }} />
            Notes du client
          </Typography>
          
          <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
            <Typography variant="body1">
              {devis.notes || 'Aucune note fournie par le client.'}
            </Typography>
          </Paper>
          
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mt: 3 }}>
            <MoneyIcon sx={{ mr: 1 }} />
            Vos commentaires
          </Typography>
          
          <TextField
            label="Commentaires (obligatoire en cas de rejet)"
            multiline
            rows={4}
            value={commentaires}
            onChange={(e) => setCommentaires(e.target.value)}
            placeholder="Ajoutez vos commentaires concernant cette demande de devis..."
            fullWidth
            sx={{ mb: 2 }}
          />
        </CardContent>
      </Card>
      
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button
          variant="outlined"
          color="error"
          startIcon={<CloseIcon />}
          onClick={() => setOpenRejectDialog(true)}
          disabled={loading || devis.statut !== 'WAITING_APPROVAL'}
        >
          Rejeter
        </Button>
        <Button
          variant="contained"
          color="success"
          startIcon={<CheckIcon />}
          onClick={() => setOpenApproveDialog(true)}
          disabled={loading || devis.statut !== 'WAITING_APPROVAL'}
        >
          Valider
        </Button>
      </Box>
      
      {/* Dialogue de confirmation pour la validation */}
      <Dialog
        open={openApproveDialog}
        onClose={() => setOpenApproveDialog(false)}
      >
        <DialogTitle>Confirmer la validation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir valider ce devis ? 
            Un vendeur pourra ensuite le traiter et l'envoyer au client.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenApproveDialog(false)}>Annuler</Button>
          <Button onClick={handleApprove} color="success" variant="contained">
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Dialogue de confirmation pour le rejet */}
      <Dialog
        open={openRejectDialog}
        onClose={() => setOpenRejectDialog(false)}
      >
        <DialogTitle>Confirmer le rejet</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir rejeter ce devis ? 
            Cette action est définitive et le client en sera informé.
          </DialogContentText>
          {!commentaires.trim() && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Vous devez fournir un motif de rejet dans les commentaires.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenRejectDialog(false)}>Annuler</Button>
          <Button 
            onClick={handleReject} 
            color="error" 
            variant="contained"
            disabled={!commentaires.trim()}
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </motion.div>
  );
};

export default ResponsableDevisValidationForm;
