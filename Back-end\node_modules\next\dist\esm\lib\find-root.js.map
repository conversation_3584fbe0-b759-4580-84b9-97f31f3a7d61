{"version": 3, "sources": ["../../src/lib/find-root.ts"], "sourcesContent": ["import { dirname } from 'path'\nimport findUp from 'next/dist/compiled/find-up'\n\nexport function findRootLockFile(cwd: string) {\n  return findUp.sync(\n    [\n      'pnpm-lock.yaml',\n      'package-lock.json',\n      'yarn.lock',\n      'bun.lock',\n      'bun.lockb',\n    ],\n    {\n      cwd,\n    }\n  )\n}\n\nexport function findRootDir(cwd: string) {\n  const lockFile = findRootLockFile(cwd)\n  return lockFile ? dirname(lockFile) : undefined\n}\n"], "names": ["dirname", "findUp", "findRootLockFile", "cwd", "sync", "findRootDir", "lockFile", "undefined"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAM;AAC9B,OAAOC,YAAY,6BAA4B;AAE/C,OAAO,SAASC,iBAAiBC,GAAW;IAC1C,OAAOF,OAAOG,IAAI,CAChB;QACE;QACA;QACA;QACA;QACA;KACD,EACD;QACED;IACF;AAEJ;AAEA,OAAO,SAASE,YAAYF,GAAW;IACrC,MAAMG,WAAWJ,iBAAiBC;IAClC,OAAOG,WAAWN,QAAQM,YAAYC;AACxC"}