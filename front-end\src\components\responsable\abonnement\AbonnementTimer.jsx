import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Paper,
  Alert,
  useTheme
} from '@mui/material';
import { AccessTime as AccessTimeIcon } from '@mui/icons-material';
import abonnementService from '../../../services/abonnementService';

const AbonnementTimer = ({ abonnementId, onExpire }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isExpiring, setIsExpiring] = useState(false);
  const [isExpired, setIsExpired] = useState(false);
  const [dateFin, setDateFin] = useState(null);

  // Fonction pour récupérer les détails de l'abonnement
  const fetchAbonnementDetails = useCallback(async () => {
    try {
      const data = await abonnementService.getAbonnementById(abonnementId);
      
      if (data && data.dateFin) {
        const finDate = new Date(data.dateFin);
        setDateFin(finDate);
        
        // Calculer le temps restant
        updateRemainingTime(finDate);
      } else {
        setError('Impossible de récupérer la date de fin de l\'abonnement');
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des détails de l\'abonnement:', err);
      setError(err.message || 'Erreur lors de la récupération des détails de l\'abonnement');
    } finally {
      setLoading(false);
    }
  }, [abonnementId]);

  // Fonction pour mettre à jour le temps restant
  const updateRemainingTime = useCallback((endDate) => {
    const now = new Date();
    const diff = endDate - now; // différence en millisecondes
    
    if (diff <= 0) {
      // L'abonnement a expiré
      setMinutes(0);
      setSeconds(0);
      setIsExpired(true);
      if (onExpire) onExpire();
      return;
    }
    
    // Convertir en minutes et secondes
    const totalSeconds = Math.floor(diff / 1000);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    
    setMinutes(mins);
    setSeconds(secs);
    
    // Définir si l'abonnement est sur le point d'expirer (moins de 2 minutes)
    setIsExpiring(mins < 2);
  }, [onExpire]);

  // Récupérer les détails de l'abonnement au chargement du composant
  useEffect(() => {
    fetchAbonnementDetails();
  }, [fetchAbonnementDetails]);

  // Mettre à jour le compteur chaque seconde
  useEffect(() => {
    if (!dateFin) return;
    
    const intervalId = setInterval(() => {
      updateRemainingTime(dateFin);
    }, 1000);
    
    return () => clearInterval(intervalId);
  }, [dateFin, updateRemainingTime]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 2 }}>
        <CircularProgress size={20} sx={{ mr: 1 }} />
        <Typography variant="body2">Chargement du temps restant...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  // Déterminer la couleur en fonction du temps restant
  let timerColor = theme.palette.success.main; // Vert par défaut
  
  if (isExpired) {
    timerColor = theme.palette.error.main; // Rouge si expiré
  } else if (isExpiring) {
    timerColor = theme.palette.warning.main; // Orange si moins de 2 minutes
  }

  return (
    <Box sx={{ mt: 2 }}>
      {isExpired ? (
        <Alert severity="error" sx={{ mb: 2 }}>
          Votre abonnement a expiré. Veuillez contacter l'administrateur pour le renouveler.
        </Alert>
      ) : isExpiring ? (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Attention : Votre abonnement expire dans moins de 2 minutes.
        </Alert>
      ) : null}
      
      <Paper 
        elevation={3} 
        sx={{ 
          p: 2, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderRadius: 2,
          border: `1px solid ${timerColor}`,
          backgroundColor: theme.palette.mode === 'dark' 
            ? theme.palette.background.paper 
            : theme.palette.background.default
        }}
      >
        <AccessTimeIcon sx={{ color: timerColor, mr: 1, fontSize: '1.5rem' }} />
        <Typography 
          variant="h4" 
          component="div" 
          fontWeight="bold"
          sx={{ color: timerColor }}
        >
          {String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
        </Typography>
      </Paper>
    </Box>
  );
};

export default AbonnementTimer;
