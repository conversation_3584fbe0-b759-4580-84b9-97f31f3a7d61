import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Grid,
  Divider,
  useTheme,
  Tooltip,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  InputAdornment,
  Tabs,
  Tab,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  AttachMoney as MoneyIcon,
  AccountBalance as BankIcon,
  CreditCard as CardIcon,
  Money as CashIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  PendingActions as PendingIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import factureService from '../services/factureService';
import paiementService from '../services/paiementService';
import { formatCurrency, formatDate } from '../utils/formatters';
import PaiementModal from '../components/PaiementModal';
import PaiementReceiptDialog from '../components/PaiementReceiptDialog';

const VendeurPaiements = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [paiements, setPaiements] = useState([]);
  const [filteredPaiements, setFilteredPaiements] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modeFilter, setModeFilter] = useState('all');
  const [tabValue, setTabValue] = useState(0);
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [selectedFacture, setSelectedFacture] = useState(null);
  const [factures, setFactures] = useState([]);
  const [error, setError] = useState('');
  const [openReceiptDialog, setOpenReceiptDialog] = useState(false);
  const [selectedPaiement, setSelectedPaiement] = useState(null);
  const [entrepriseInfo, setEntrepriseInfo] = useState(null);
  const [stats, setStats] = useState({
    totalPaiements: 0,
    totalMontant: 0,
    paiementsByMode: {
      BANK_TRANSFER: 0,
      CHECK: 0,
      CASH: 0,
    },
    paiementsByStatus: {
      COMPLETED: 0,
      PENDING: 0,
      FAILED: 0,
      REFUNDED: 0,
    },
    recentPaiements: [],
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Fetch payments and unpaid invoices
        const [paiementsData, facturesData] = await Promise.all([
          paiementService.getPaiements(),
          factureService.getFactures({ statut: 'SENT' }),
        ]);

        setPaiements(paiementsData);
        setFilteredPaiements(paiementsData);
        setFactures(facturesData);

        // Calculate payment statistics
        calculateStats(paiementsData);

        setLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setError('Erreur lors du chargement des données. Veuillez réessayer.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const calculateStats = (paiementsData) => {
    const totalMontant = paiementsData.reduce((sum, p) => sum + p.montant, 0);

    // Count payments by mode
    const paiementsByMode = {
      BANK_TRANSFER: 0,
      CHECK: 0,
      CASH: 0,
    };

    // Count payments by status
    const paiementsByStatus = {
      COMPLETED: 0,
      PENDING: 0,
      FAILED: 0,
      REFUNDED: 0,
    };

    paiementsData.forEach(p => {
      paiementsByMode[p.modePaiement] += 1;
      paiementsByStatus[p.statut] += 1;
    });

    // Get 5 most recent payments
    const recentPaiements = [...paiementsData]
      .sort((a, b) => new Date(b.datePaiement) - new Date(a.datePaiement))
      .slice(0, 5);

    setStats({
      totalPaiements: paiementsData.length,
      totalMontant,
      paiementsByMode,
      paiementsByStatus,
      recentPaiements,
    });
  };

  useEffect(() => {
    filterPaiements();
  }, [searchTerm, statusFilter, modeFilter, paiements, tabValue]);

  const filterPaiements = () => {
    let filtered = [...paiements];

    // Filter by tab (All, Today, This Week, This Month)
    if (tabValue === 1) { // Today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      filtered = filtered.filter(p => new Date(p.datePaiement) >= today);
    } else if (tabValue === 2) { // This Week
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      weekStart.setHours(0, 0, 0, 0);
      filtered = filtered.filter(p => new Date(p.datePaiement) >= weekStart);
    } else if (tabValue === 3) { // This Month
      const monthStart = new Date();
      monthStart.setDate(1);
      monthStart.setHours(0, 0, 0, 0);
      filtered = filtered.filter(p => new Date(p.datePaiement) >= monthStart);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(p =>
        p.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.factureId?.numero?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(p => p.statut === statusFilter);
    }

    // Filter by payment mode
    if (modeFilter !== 'all') {
      filtered = filtered.filter(p => p.modePaiement === modeFilter);
    }

    setFilteredPaiements(filtered);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleViewFacture = (id) => {
    navigate(`/vendeur/factures/${id}`);
  };

  const handleOpenPaymentDialog = (facture) => {
    setSelectedFacture(facture);
    setOpenPaymentDialog(true);
  };

  const handleClosePaymentDialog = () => {
    setOpenPaymentDialog(false);
    setSelectedFacture(null);
    setError('');
  };

  const handleSavePayment = async (paiementData) => {
    try {
      await paiementService.payerFacture(selectedFacture._id, paiementData);

      // Refresh data
      const [paiementsData, facturesData] = await Promise.all([
        paiementService.getPaiements(),
        factureService.getFactures({ statut: 'SENT' }),
      ]);

      setPaiements(paiementsData);
      setFactures(facturesData);
      calculateStats(paiementsData);

      handleClosePaymentDialog();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du paiement:', error);
      setError('Erreur lors de l\'enregistrement du paiement. Veuillez réessayer.');
    }
  };

  const handleViewReceipt = async (paiement) => {
    try {
      // Get the facture details for this payment
      const facture = await factureService.getFactureById(paiement.factureId);

      // Get company info from settings or use default
      // In a real app, you would fetch this from your settings/parameters
      const entreprise = {
        nom: 'Votre Entreprise',
        adresse: '123 Rue Principale',
        codePostal: '75000',
        ville: 'Paris',
        email: '<EMAIL>',
        telephone: '+33 1 23 45 67 89',
      };

      setSelectedPaiement(paiement);
      setEntrepriseInfo(entreprise);
      setOpenReceiptDialog(true);
    } catch (error) {
      console.error('Erreur lors de la récupération des détails du reçu:', error);
      setError('Erreur lors de la récupération des détails du reçu. Veuillez réessayer.');
    }
  };

  const handleCloseReceiptDialog = () => {
    setOpenReceiptDialog(false);
    setSelectedPaiement(null);
  };

  const getPaymentModeIcon = (mode) => {
    switch (mode) {
      case 'BANK_TRANSFER':
        return <BankIcon />;
      case 'CHECK':
        return <ReceiptIcon />;
      case 'CASH':
        return <CashIcon />;
      default:
        return <MoneyIcon />;
    }
  };

  const getPaymentModeLabel = (mode) => {
    switch (mode) {
      case 'BANK_TRANSFER':
        return 'Virement bancaire';
      case 'CHECK':
        return 'Chèque';
      case 'CASH':
        return 'Espèces';
      default:
        return mode;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon />;
      case 'PENDING':
        return <PendingIcon />;
      case 'FAILED':
        return <CancelIcon />;
      case 'REFUNDED':
        return <MoneyIcon />;
      default:
        return <PendingIcon />;
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'Complété';
      case 'PENDING':
        return 'En attente';
      case 'FAILED':
        return 'Échoué';
      case 'REFUNDED':
        return 'Remboursé';
      default:
        return status;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'FAILED':
        return 'error';
      case 'REFUNDED':
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        Gestion des Paiements
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6} lg={3}>
          <Card
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            elevation={2}
            sx={{
              height: '100%',
              borderRadius: 2,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Typography variant="h6">Total des paiements</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                {formatCurrency(stats.totalMontant)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {stats.totalPaiements} paiements enregistrés
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            elevation={2}
            sx={{
              height: '100%',
              borderRadius: 2,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.success.main, mr: 2 }}>
                  <CheckCircleIcon />
                </Avatar>
                <Typography variant="h6">Paiements complétés</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                {stats.paiementsByStatus.COMPLETED || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {stats.paiementsByStatus.COMPLETED ?
                  Math.round((stats.paiementsByStatus.COMPLETED / stats.totalPaiements) * 100) : 0}% du total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            elevation={2}
            sx={{
              height: '100%',
              borderRadius: 2,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.warning.main, mr: 2 }}>
                  <PendingIcon />
                </Avatar>
                <Typography variant="h6">Paiements en attente</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                {stats.paiementsByStatus.PENDING || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {stats.paiementsByStatus.PENDING ?
                  Math.round((stats.paiementsByStatus.PENDING / stats.totalPaiements) * 100) : 0}% du total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            elevation={2}
            sx={{
              height: '100%',
              borderRadius: 2,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: theme.palette.info.main, mr: 2 }}>
                  <BankIcon />
                </Avatar>
                <Typography variant="h6">Virements bancaires</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                {stats.paiementsByMode.BANK_TRANSFER || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {stats.paiementsByMode.BANK_TRANSFER ?
                  Math.round((stats.paiementsByMode.BANK_TRANSFER / stats.totalPaiements) * 100) : 0}% du total
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs and Filters */}
      <Paper
        elevation={2}
        sx={{
          p: 2,
          mb: 3,
          borderRadius: 2,
          background: theme.palette.background.paper
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab label="Tous" />
              <Tab label="Aujourd'hui" />
              <Tab label="Cette semaine" />
              <Tab label="Ce mois" />
            </Tabs>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                placeholder="Rechercher..."
                variant="outlined"
                size="small"
                fullWidth
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
                <InputLabel id="status-filter-label">Statut</InputLabel>
                <Select
                  labelId="status-filter-label"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Statut"
                >
                  <MenuItem value="all">Tous</MenuItem>
                  <MenuItem value="COMPLETED">Complété</MenuItem>
                  <MenuItem value="PENDING">En attente</MenuItem>
                  <MenuItem value="FAILED">Échoué</MenuItem>
                  <MenuItem value="REFUNDED">Remboursé</MenuItem>
                </Select>
              </FormControl>
              <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
                <InputLabel id="mode-filter-label">Mode de paiement</InputLabel>
                <Select
                  labelId="mode-filter-label"
                  value={modeFilter}
                  onChange={(e) => setModeFilter(e.target.value)}
                  label="Mode de paiement"
                >
                  <MenuItem value="all">Tous</MenuItem>
                  <MenuItem value="BANK_TRANSFER">Virement bancaire</MenuItem>
                  <MenuItem value="CHECK">Chèque</MenuItem>
                  <MenuItem value="CASH">Espèces</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Payments Table */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Paper
            elevation={2}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              height: '100%'
            }}
          >
            <TableContainer sx={{ maxHeight: 600 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Référence</TableCell>
                    <TableCell>Facture</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Montant</TableCell>
                    <TableCell>Mode</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredPaiements.length > 0 ? (
                    filteredPaiements.map((paiement) => (
                      <TableRow
                        key={paiement._id}
                        hover
                        sx={{
                          '&:hover': {
                            backgroundColor: `${theme.palette.action.hover} !important`,
                            cursor: 'pointer'
                          }
                        }}
                      >
                        <TableCell>{paiement.reference || '-'}</TableCell>
                        <TableCell>
                          {paiement.factureId?.numero || '-'}
                        </TableCell>
                        <TableCell>{formatDate(paiement.datePaiement)}</TableCell>
                        <TableCell>{formatCurrency(paiement.montant)}</TableCell>
                        <TableCell>
                          <Chip
                            icon={getPaymentModeIcon(paiement.modePaiement)}
                            label={getPaymentModeLabel(paiement.modePaiement)}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            icon={getStatusIcon(paiement.statut)}
                            label={getStatusLabel(paiement.statut)}
                            color={getStatusColor(paiement.statut)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Voir la facture">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleViewFacture(paiement.factureId?._id)}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Voir le reçu">
                            <IconButton
                              size="small"
                              color="info"
                              onClick={() => handleViewReceipt(paiement)}
                            >
                              <ReceiptIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Télécharger le reçu">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleViewReceipt(paiement)}
                            >
                              <DownloadIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        Aucun paiement trouvé
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Grid container spacing={3} direction="column">
            {/* Unpaid Invoices */}
            <Grid item xs={12}>
              <Card
                elevation={2}
                sx={{
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <CardHeader
                  title="Factures à payer"
                  titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
                  action={
                    <Tooltip title="Voir toutes les factures">
                      <IconButton size="small" onClick={() => navigate('/vendeur/factures')}>
                        <MoreVertIcon />
                      </IconButton>
                    </Tooltip>
                  }
                />
                <Divider />
                <CardContent sx={{ p: 0 }}>
                  <List sx={{ p: 0 }}>
                    {factures.length > 0 ? (
                      factures.slice(0, 5).map((facture) => (
                        <React.Fragment key={facture._id}>
                          <ListItem
                            secondaryAction={
                              <Tooltip title="Enregistrer un paiement">
                                <IconButton
                                  edge="end"
                                  color="success"
                                  onClick={() => handleOpenPaymentDialog(facture)}
                                >
                                  <PaymentIcon />
                                </IconButton>
                              </Tooltip>
                            }
                          >
                            <Box sx={{ mr: 2 }}>
                              <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                                <ReceiptIcon />
                              </Avatar>
                            </Box>
                            <Box>
                              <Typography variant="subtitle2">
                                {facture.numero}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {formatCurrency(facture.total)} - {formatDate(facture.dateEmission)}
                              </Typography>
                            </Box>
                          </ListItem>
                          <Divider component="li" />
                        </React.Fragment>
                      ))
                    ) : (
                      <ListItem>
                        <Typography variant="body2" color="text.secondary" sx={{ py: 2 }}>
                          Aucune facture en attente de paiement
                        </Typography>
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* Recent Payments */}
            <Grid item xs={12}>
              <Card
                elevation={2}
                sx={{
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <CardHeader
                  title="Paiements récents"
                  titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
                />
                <Divider />
                <CardContent sx={{ p: 0 }}>
                  <List sx={{ p: 0 }}>
                    {stats.recentPaiements.length > 0 ? (
                      stats.recentPaiements.map((paiement) => (
                        <React.Fragment key={paiement._id}>
                          <ListItem>
                            <Box sx={{ mr: 2 }}>
                              <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                                {getPaymentModeIcon(paiement.modePaiement)}
                              </Avatar>
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography variant="subtitle2">
                                {paiement.factureId?.numero || 'Facture inconnue'}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {formatDate(paiement.datePaiement)}
                              </Typography>
                            </Box>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="bold">
                                {formatCurrency(paiement.montant)}
                              </Typography>
                              <Chip
                                label={getStatusLabel(paiement.statut)}
                                color={getStatusColor(paiement.statut)}
                                size="small"
                                sx={{ height: 20, fontSize: '0.7rem' }}
                              />
                            </Box>
                          </ListItem>
                          <Divider component="li" />
                        </React.Fragment>
                      ))
                    ) : (
                      <ListItem>
                        <Typography variant="body2" color="text.secondary" sx={{ py: 2 }}>
                          Aucun paiement récent
                        </Typography>
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Payment Dialog */}
      {selectedFacture && (
        <PaiementModal
          open={openPaymentDialog}
          onClose={handleClosePaymentDialog}
          facture={selectedFacture}
          onSave={handleSavePayment}
        />
      )}

      {/* Receipt Dialog */}
      {selectedPaiement && (
        <PaiementReceiptDialog
          open={openReceiptDialog}
          onClose={handleCloseReceiptDialog}
          paiement={selectedPaiement}
          facture={selectedPaiement.factureId}
          entreprise={entrepriseInfo}
        />
      )}
    </Box>
  );
};

export default VendeurPaiements;
