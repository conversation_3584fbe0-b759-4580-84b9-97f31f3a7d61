const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../models/UserModel');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const nodemailer = require('nodemailer');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/profiles';
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, 'profile-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 2 * 1024 * 1024 }, // Limit file size to 2MB
  fileFilter: (req, file, cb) => {
    const fileTypes = /jpeg|jpg|png/;
    const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = fileTypes.test(file.mimetype);
    if (extname && mimetype) {
      return cb(null, true);
    }
    cb(new Error('Seuls les fichiers JPEG et PNG sont autorisés'));
  },
});

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';
const JWT_EXPIRATION = '24h';

// Configuration du transporteur d'email
const transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',
  port: 465,
  secure: true, // true pour 465, false pour les autres ports
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>', // Remplacer par votre email
    pass: process.env.EMAIL_PASSWORD || 'votre-mot-de-passe-app', // Remplacer par votre mot de passe d'application
  },
  tls: {
    rejectUnauthorized: false // Désactive la vérification du certificat SSL
  }
});

// Fonction pour envoyer un email
const sendEmail = async (options) => {
  const mailOptions = {
    from: `"BENYOUNES WEB" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
    to: options.to,
    subject: options.subject,
    html: options.html,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Email envoyé avec succès:', info.messageId);
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'envoi de l\'email:', error);
    // Afficher plus de détails sur l'erreur pour faciliter le débogage
    if (error.response) {
      console.error('Détails de l\'erreur SMTP:', error.response);
    }
    return false;
  }
};

// Middleware to log all requests to this router
router.use((req, res, next) => {
  console.log(`AuthRoute accessed: ${req.method} ${req.url}`);
  next();
});

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // Continue without setting userId (for public routes)
      return next();
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        // Continue without setting userId (invalid token)
        return next();
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    // Continue without setting userId
    next();
  }
};

// Inscription d'un nouvel utilisateur
router.post('/register', verifyToken, async (req, res) => {
  try {
    const {
      nom,
      email,
      motDePasse,
      role,
      // Admin specific fields
      nomEntreprise,
      adresseEntreprise,
      telephoneEntreprise,
      numeroFiscal,
      // Client specific fields
      adresse,
      telephone,
      contact,
      // Relations
      clientId,
      createdBy
    } = req.body;

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: 'Cet email est déjà utilisé' });
    }

    // Hasher le mot de passe
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(motDePasse, salt);

    // Créer un nouvel utilisateur avec les champs communs
    const userData = {
      nom,
      email,
      motDePasse: hashedPassword,
      role: role || 'CLIENT', // Par défaut, les nouveaux utilisateurs sont des clients
    };

    // Set the createdBy field based on the authenticated user or the provided value
    if (req.userId) {
      // If the request is authenticated, use the authenticated user's ID
      userData.createdBy = req.userId;
      console.log(`Setting createdBy to authenticated user: ${req.userId}`);
    } else if (createdBy) {
      // If createdBy is explicitly provided in the request body
      userData.createdBy = createdBy;
      console.log(`Setting createdBy to provided value: ${createdBy}`);
    }

    // Ajouter les champs spécifiques selon le rôle
    if (role === 'ADMIN') {
      Object.assign(userData, {
        nomEntreprise,
        adresseEntreprise,
        telephoneEntreprise,
        numeroFiscal
      });

    } else if (role === 'VENDEUR') {
      Object.assign(userData, {
        adresse,
        telephone,
        contact
      });
    } else if (role === 'RESPONSABLE') {
      Object.assign(userData, {
        nomEntreprise,
        adresseEntreprise,
        telephoneEntreprise,
        numeroFiscal,
        adresse,
        telephone,
        contact
      });
    } else if (role === 'CLIENT') {
      // Vérifier si le CIN est fourni pour les clients
      if (!req.body.cin) {
        return res.status(400).json({ error: 'Le numéro CIN est obligatoire pour les clients' });
      }

      Object.assign(userData, {
        adresse,
        telephone,
        contact,
        cin: req.body.cin
      });

      // Pour les clients, associer à des responsables d'entreprise si spécifié
      if (req.body.responsables && req.body.responsables.length > 0) {
        userData.responsables = req.body.responsables;
      }

      // Vérifier si un client avec cet email ou CIN existe déjà
      const Client = require('../models/ClientModel');
      let existingClient = await Client.findOne({
        $or: [
          { email: email },
          { cin: req.body.cin }
        ]
      });

      if (existingClient) {
        console.log(`Client existant trouvé avec l'email ${email} ou CIN ${req.body.cin} pour l'utilisateur CLIENT, utilisation de ce client`);
        userData.responsableId = existingClient._id;

        // Mettre à jour le client avec les responsables si spécifiés
        if (req.body.responsables && req.body.responsables.length > 0) {
          // Ajouter les nouveaux responsables sans dupliquer
          const updatedResponsables = [...new Set([
            ...(existingClient.responsables || []).map(id => id.toString()),
            ...req.body.responsables
          ])];

          await Client.findByIdAndUpdate(existingClient._id, {
            responsables: updatedResponsables
          });
        }
      } else {
        // Créer un nouveau client
        const newClient = new Client({
          nom: nom,
          adresse: adresse || 'Adresse non spécifiée',
          email: email,
          contact: contact || nom,
          telephone: telephone || '',
          cin: req.body.cin,
          responsables: req.body.responsables || []
        });

        await newClient.save();
        userData.responsableId = newClient._id;
      }
    }

    const newUser = new User(userData);

    // Sauvegarder l'utilisateur
    await newUser.save();

    // Générer un token JWT
    const token = jwt.sign(
      { userId: newUser._id, role: newUser.role },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRATION }
    );

    // Prepare user data with role-specific fields
    const userResponseData = {
      id: newUser._id,
      nom: newUser.nom,
      email: newUser.email,
      role: newUser.role,
      dateCreation: newUser.dateCreation
    };

    // Add role-specific fields
    if (newUser.role === 'ADMIN') {
      Object.assign(userResponseData, {
        nomEntreprise: newUser.nomEntreprise,
        adresseEntreprise: newUser.adresseEntreprise,
        telephoneEntreprise: newUser.telephoneEntreprise,
        numeroFiscal: newUser.numeroFiscal
      });

    } else if (newUser.role === 'RESPONSABLE') {
      Object.assign(userResponseData, {
        nomEntreprise: newUser.nomEntreprise,
        adresseEntreprise: newUser.adresseEntreprise,
        telephoneEntreprise: newUser.telephoneEntreprise,
        numeroFiscal: newUser.numeroFiscal,
        adresse: newUser.adresse,
        telephone: newUser.telephone,
        contact: newUser.contact
      });
    } else if (newUser.role === 'CLIENT') {
      Object.assign(userResponseData, {
        adresse: newUser.adresse,
        telephone: newUser.telephone,
        contact: newUser.contact,
        cin: newUser.cin,
        responsableId: newUser.responsableId
      });
    }

    res.status(201).json({
      message: 'Utilisateur créé avec succès',
      token,
      user: userResponseData,
    });
  } catch (error) {
    console.error("Erreur lors de l'inscription:", error.message);
    res.status(500).json({ error: 'Erreur serveur lors de l\'inscription', details: error.message });
  }
});

// Connexion d'un utilisateur
router.post('/login', async (req, res) => {
  try {
    const { email, motDePasse } = req.body;

    // Vérifier si l'utilisateur existe par email ou CIN
    const user = await User.findOne({
      $or: [
        { email: email },
        { cin: email } // Permettre la connexion avec le CIN
      ]
    });
    if (!user) {
      return res.status(400).json({ error: 'Email/CIN ou mot de passe incorrect' });
    }

    // Vérifier le mot de passe
    const validPassword = await bcrypt.compare(motDePasse, user.motDePasse);
    if (!validPassword) {
      return res.status(400).json({ error: 'Mot de passe incorrect' });
    }

    // Le type de compte est automatiquement détecté à partir du rôle de l'utilisateur

    // Si l'utilisateur est un responsable d'entreprise, vérifier s'il a un responsableId
    if (user.role === 'RESPONSABLE' && !user.responsableId) {
      // Essayer de trouver un client avec le même email
      const Client = require('../models/ClientModel');
      const client = await Client.findOne({ email: user.email });

      if (client) {
        // Mettre à jour l'utilisateur avec l'ID du client
        user.responsableId = client._id;
        await user.save();
        console.log('Updated user with responsableId:', client._id);
      } else {
        console.log('No client found for responsable user with email:', user.email);
      }
    }

    // Générer un token JWT
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRATION }
    );

    // Prepare user data with role-specific fields
    const userData = {
      id: user._id,
      nom: user.nom,
      email: user.email,
      role: user.role,
      dateCreation: user.dateCreation,
      profileImage: user.profileImage
    };

    // Add role-specific fields
    if (user.role === 'ADMIN') {
      Object.assign(userData, {
        nomEntreprise: user.nomEntreprise,
        adresseEntreprise: user.adresseEntreprise,
        telephoneEntreprise: user.telephoneEntreprise,
        numeroFiscal: user.numeroFiscal
      });

    } else if (user.role === 'VENDEUR') {
      Object.assign(userData, {
        adresse: user.adresse,
        telephone: user.telephone,
        contact: user.contact
      });
    } else if (user.role === 'RESPONSABLE') {
      Object.assign(userData, {
        nomEntreprise: user.nomEntreprise,
        adresseEntreprise: user.adresseEntreprise,
        telephoneEntreprise: user.telephoneEntreprise,
        numeroFiscal: user.numeroFiscal,
        adresse: user.adresse,
        telephone: user.telephone,
        contact: user.contact,
        responsableId: user.responsableId // Ajouter l'ID du responsable
      });
    } else if (user.role === 'CLIENT') {
      // Pour les utilisateurs de type CLIENT, vérifier s'ils ont un responsableId
      if (user.responsableId) {
        console.log('Client user has responsableId:', user.responsableId);
        Object.assign(userData, {
          adresse: user.adresse,
          telephone: user.telephone,
          contact: user.contact,
          cin: user.cin,
          entreprises: user.entreprises, // Ajouter les entreprises associées
          clientId: user.responsableId // Ajouter l'ID client pour les utilisateurs de type CLIENT
        });
      } else {
        console.log('Client user has no responsableId, trying to find a client with matching email or CIN');
        // Essayer de trouver un client avec le même email ou CIN
        const Client = require('../models/ClientModel');
        const client = await Client.findOne({
          $or: [
            { email: user.email },
            { cin: user.cin }
          ]
        });

        if (client) {
          console.log('Found client with matching email or CIN:', client._id);
          // Mettre à jour l'utilisateur avec l'ID du client
          user.responsableId = client._id;
          await user.save();
          console.log('Updated client user with responsableId:', client._id);

          Object.assign(userData, {
            adresse: user.adresse,
            telephone: user.telephone,
            contact: user.contact,
            cin: user.cin,
            entreprises: user.entreprises,
            clientId: client._id // Ajouter l'ID client trouvé
          });
        } else {
          console.log('No client found for client user with email:', user.email);
          // Créer un nouveau client pour cet utilisateur
          if (!user.cin) {
            return res.status(400).json({ error: 'Le numéro CIN est obligatoire pour les clients' });
          }

          const newClient = new Client({
            nom: user.nom,
            adresse: user.adresse || 'Adresse non spécifiée',
            email: user.email,
            contact: user.contact || user.nom,
            telephone: user.telephone || '',
            cin: user.cin
          });

          await newClient.save();
          console.log('Created new client for user:', newClient._id);

          // Mettre à jour l'utilisateur avec l'ID du nouveau client
          user.responsableId = newClient._id;
          await user.save();

          Object.assign(userData, {
            adresse: user.adresse,
            telephone: user.telephone,
            contact: user.contact,
            cin: user.cin,
            responsables: user.responsables,
            clientId: newClient._id // Ajouter l'ID du nouveau client
          });
        }
      }
    }

    res.status(200).json({
      message: 'Connexion réussie',
      token,
      user: userData,
    });
  } catch (error) {
    console.error('Erreur lors de la connexion:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la connexion', details: error.message });
  }
});

// Vérification du token
router.get('/verify-token', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];

    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      res.status(200).json({ valid: true, userId: decoded.userId, role: decoded.role });
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
});

// Obtenir le profil de l'utilisateur connecté
router.get('/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];

    jwt.verify(token, JWT_SECRET, async (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      const user = await User.findById(decoded.userId).select('-motDePasse');
      if (!user) {
        return res.status(404).json({ error: 'Utilisateur non trouvé' });
      }

      // Return user data with role-specific fields
      const userData = {
        id: user._id,
        nom: user.nom,
        email: user.email,
        role: user.role,
        dateCreation: user.dateCreation,
        profileImage: user.profileImage
      };

      // Add role-specific fields
      if (user.role === 'ADMIN') {
        Object.assign(userData, {
          nomEntreprise: user.nomEntreprise,
          adresseEntreprise: user.adresseEntreprise,
          telephoneEntreprise: user.telephoneEntreprise,
          numeroFiscal: user.numeroFiscal
        });

      } else if (user.role === 'ENTREPRISE') {
        Object.assign(userData, {
          nomEntreprise: user.nomEntreprise,
          adresseEntreprise: user.adresseEntreprise,
          telephoneEntreprise: user.telephoneEntreprise,
          numeroFiscal: user.numeroFiscal,
          adresse: user.adresse,
          telephone: user.telephone,
          contact: user.contact,
          entrepriseId: user.entrepriseId // Ajouter l'ID de l'entreprise
        });
      } else if (user.role === 'VENDEUR') {
        Object.assign(userData, {
          adresse: user.adresse,
          telephone: user.telephone,
          contact: user.contact
        });
      } else if (user.role === 'CLIENT') {
        // Pour les utilisateurs de type CLIENT, vérifier s'ils ont un responsableId
        if (user.responsableId) {
          console.log('Client user has responsableId in profile:', user.responsableId);
          Object.assign(userData, {
            adresse: user.adresse,
            telephone: user.telephone,
            contact: user.contact,
            responsables: user.responsables, // Ajouter les responsables associés
            clientId: user.responsableId // Ajouter l'ID client pour les utilisateurs de type CLIENT
          });
        } else {
          console.log('Client user has no responsableId in profile, trying to find a client with matching email');
          // Essayer de trouver un client avec le même email
          const Client = require('../models/ClientModel');
          const client = await Client.findOne({ email: user.email });

          if (client) {
            console.log('Found client with matching email in profile:', client._id);
            // Mettre à jour l'utilisateur avec l'ID du client
            user.entrepriseId = client._id;
            await user.save();
            console.log('Updated client user with entrepriseId in profile:', client._id);

            Object.assign(userData, {
              adresse: user.adresse,
              telephone: user.telephone,
              contact: user.contact,
              entreprises: user.entreprises,
              clientId: client._id // Ajouter l'ID client trouvé
            });
          } else {
            console.log('No client found for client user with email in profile:', user.email);
            // Créer un nouveau client pour cet utilisateur
            const newClient = new Client({
              nom: user.nom,
              adresse: user.adresse || 'Adresse non spécifiée',
              email: user.email,
              contact: user.contact || user.nom,
              telephone: user.telephone || ''
            });

            await newClient.save();
            console.log('Created new client for user in profile:', newClient._id);

            // Mettre à jour l'utilisateur avec l'ID du nouveau client
            user.entrepriseId = newClient._id;
            await user.save();

            Object.assign(userData, {
              adresse: user.adresse,
              telephone: user.telephone,
              contact: user.contact,
              entreprises: user.entreprises,
              clientId: newClient._id // Ajouter l'ID du nouveau client
            });
          }
        }
      }

      res.status(200).json(userData);
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération du profil' });
  }
});

// Upload profile picture
router.post('/upload-profile-image', upload.single('profileImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Aucune image n\'a été téléchargée' });
    }

    // Verify token to get user ID
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (err) {
      return res.status(401).json({ error: 'Token invalide' });
    }

    // Find user
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Save profile image path to user
    const profileImagePath = `/uploads/profiles/${req.file.filename}`;
    user.profileImage = profileImagePath;
    await user.save();

    res.status(200).json({
      message: 'Image de profil téléchargée avec succès',
      profileImage: profileImagePath
    });
  } catch (error) {
    console.error('Erreur lors du téléchargement de l\'image de profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors du téléchargement de l\'image de profil' });
  }
});

// Mettre à jour le profil de l'utilisateur
router.put('/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];

    jwt.verify(token, JWT_SECRET, async (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      const user = await User.findById(decoded.userId);
      if (!user) {
        return res.status(404).json({ error: 'Utilisateur non trouvé' });
      }

      // Champs communs à mettre à jour
      const { nom, email } = req.body;

      // Vérifier si l'email est déjà utilisé par un autre utilisateur
      if (email && email !== user.email) {
        const existingUser = await User.findOne({ email });
        if (existingUser && existingUser._id.toString() !== user._id.toString()) {
          return res.status(400).json({ error: 'Cet email est déjà utilisé par un autre utilisateur' });
        }
      }

      // Mettre à jour les champs communs
      if (nom) user.nom = nom;
      if (email) user.email = email;
      if (req.body.profileImage) user.profileImage = req.body.profileImage;

      // Mettre à jour les champs spécifiques selon le rôle
      if (user.role === 'ADMIN') {
        const { nomEntreprise, adresseEntreprise, telephoneEntreprise, numeroFiscal } = req.body;
        if (nomEntreprise) user.nomEntreprise = nomEntreprise;
        if (adresseEntreprise) user.adresseEntreprise = adresseEntreprise;
        if (telephoneEntreprise) user.telephoneEntreprise = telephoneEntreprise;
        if (numeroFiscal) user.numeroFiscal = numeroFiscal;

      } else if (user.role === 'ENTREPRISE') {
        const { nomEntreprise, adresseEntreprise, telephoneEntreprise, numeroFiscal, adresse, telephone, contact } = req.body;
        if (nomEntreprise) user.nomEntreprise = nomEntreprise;
        if (adresseEntreprise) user.adresseEntreprise = adresseEntreprise;
        if (telephoneEntreprise) user.telephoneEntreprise = telephoneEntreprise;
        if (numeroFiscal) user.numeroFiscal = numeroFiscal;
        if (adresse) user.adresse = adresse;
        if (telephone) user.telephone = telephone;
        if (contact) user.contact = contact;
      } else if (user.role === 'VENDEUR') {
        const { adresse, telephone, contact } = req.body;
        if (adresse) user.adresse = adresse;
        if (telephone) user.telephone = telephone;
        if (contact) user.contact = contact;
      } else if (user.role === 'CLIENT') {
        const { adresse, telephone, contact, responsables } = req.body;
        if (adresse) user.adresse = adresse;
        if (telephone) user.telephone = telephone;
        if (contact) user.contact = contact;
        if (responsables) user.responsables = responsables;
      }

      // Sauvegarder les modifications
      await user.save();

      // Préparer la réponse avec les données mises à jour
      const userData = {
        id: user._id,
        nom: user.nom,
        email: user.email,
        role: user.role,
        dateCreation: user.dateCreation,
        profileImage: user.profileImage
      };

      // Ajouter les champs spécifiques selon le rôle
      if (user.role === 'ADMIN') {
        Object.assign(userData, {
          nomEntreprise: user.nomEntreprise,
          adresseEntreprise: user.adresseEntreprise,
          telephoneEntreprise: user.telephoneEntreprise,
          numeroFiscal: user.numeroFiscal
        });

      } else if (user.role === 'ENTREPRISE') {
        Object.assign(userData, {
          nomEntreprise: user.nomEntreprise,
          adresseEntreprise: user.adresseEntreprise,
          telephoneEntreprise: user.telephoneEntreprise,
          numeroFiscal: user.numeroFiscal,
          adresse: user.adresse,
          telephone: user.telephone,
          contact: user.contact,
          entrepriseId: user.entrepriseId // Ajouter l'ID de l'entreprise
        });
      } else if (user.role === 'VENDEUR') {
        Object.assign(userData, {
          adresse: user.adresse,
          telephone: user.telephone,
          contact: user.contact
        });
      } else if (user.role === 'CLIENT') {
        // Pour les utilisateurs de type CLIENT, vérifier s'ils ont un entrepriseId
        if (user.entrepriseId) {
          console.log('Client user has entrepriseId in profile update:', user.entrepriseId);
          Object.assign(userData, {
            adresse: user.adresse,
            telephone: user.telephone,
            contact: user.contact,
            entreprises: user.entreprises, // Ajouter les entreprises associées
            clientId: user.entrepriseId // Ajouter l'ID client pour les utilisateurs de type CLIENT
          });
        } else {
          console.log('Client user has no entrepriseId in profile update, trying to find a client with matching email');
          // Essayer de trouver un client avec le même email
          const Client = require('../models/ClientModel');
          const client = await Client.findOne({ email: user.email });

          if (client) {
            console.log('Found client with matching email in profile update:', client._id);
            // Mettre à jour l'utilisateur avec l'ID du client
            user.entrepriseId = client._id;
            await user.save();
            console.log('Updated client user with entrepriseId in profile update:', client._id);

            Object.assign(userData, {
              adresse: user.adresse,
              telephone: user.telephone,
              contact: user.contact,
              entreprises: user.entreprises,
              clientId: client._id // Ajouter l'ID client trouvé
            });
          } else {
            console.log('No client found for client user with email in profile update:', user.email);
            // Créer un nouveau client pour cet utilisateur
            const newClient = new Client({
              nom: user.nom,
              adresse: user.adresse || 'Adresse non spécifiée',
              email: user.email,
              contact: user.contact || user.nom,
              telephone: user.telephone || ''
            });

            await newClient.save();
            console.log('Created new client for user in profile update:', newClient._id);

            // Mettre à jour l'utilisateur avec l'ID du nouveau client
            user.entrepriseId = newClient._id;
            await user.save();

            Object.assign(userData, {
              adresse: user.adresse,
              telephone: user.telephone,
              contact: user.contact,
              entreprises: user.entreprises,
              clientId: newClient._id // Ajouter l'ID du nouveau client
            });
          }
        }
      }

      res.status(200).json({ message: 'Profil mis à jour avec succès', user: userData });
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du profil' });
  }
});

// Change password
router.put('/change-password', async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Mot de passe actuel et nouveau requis' });
    }

    // Verify token to get user ID
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (err) {
      return res.status(401).json({ error: 'Token invalide' });
    }

    // Find user
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Verify current password
    const validPassword = await bcrypt.compare(currentPassword, user.motDePasse);
    if (!validPassword) {
      return res.status(400).json({ error: 'Mot de passe actuel incorrect' });
    }

    // Validate new password
    if (newPassword.length < 8) {
      return res.status(400).json({ error: 'Le nouveau mot de passe doit contenir au moins 8 caractères' });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    user.motDePasse = hashedPassword;
    await user.save();

    res.status(200).json({ message: 'Mot de passe changé avec succès' });
  } catch (error) {
    console.error('Erreur lors du changement de mot de passe:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors du changement de mot de passe' });
  }
});

// Delete account
router.delete('/delete-account', async (req, res) => {
  try {
    // Verify token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (err) {
      return res.status(401).json({ error: 'Token invalide' });
    }

    // Find and delete user
    const user = await User.findByIdAndDelete(decoded.userId);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Optionally, clean up related data (uncomment if needed)
    // await Parametres.deleteMany({ /* criteria */ });

    res.status(200).json({ message: 'Compte supprimé avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression du compte:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la suppression du compte' });
  }
});

// Générer et envoyer un code OTP pour réinitialiser le mot de passe
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    // Vérifier si l'utilisateur existe
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ error: 'Aucun utilisateur trouvé avec cet email' });
    }

    // Générer un code OTP à 6 chiffres
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // Afficher le code OTP dans la console pour faciliter les tests
    console.log(`Code OTP généré pour ${user.email}: ${otp}`);

    // Stocker l'OTP dans la base de données
    user.resetOTP = otp;
    user.resetOTPExpires = Date.now() + 600000; // Expire dans 10 minutes
    await user.save();

    // Contenu de l'email
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Réinitialisation de mot de passe</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; padding: 20px 0; border-bottom: 1px solid #eee; }
          .content { padding: 20px 0; }
          .code-container { margin: 25px 0; padding: 20px; background-color: #f5f7fa; border-radius: 8px; text-align: center; }
          .code { font-size: 32px; font-weight: bold; letter-spacing: 8px; color: #3f51b5; }
          .footer { padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777; text-align: center; }
          .note { font-size: 14px; color: #666; margin-top: 20px; }
          .warning { color: #e53935; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Réinitialisation de votre mot de passe</h1>
        </div>
        <div class="content">
          <p>Bonjour,</p>
          <p>Vous avez demandé une réinitialisation de mot de passe pour votre compte.</p>
          <p>Voici votre code de vérification à usage unique :</p>

          <div class="code-container">
            <div class="code">${otp}</div>
          </div>

          <p>Saisissez ce code sur la page de vérification pour continuer le processus de réinitialisation.</p>
          <p class="note"><strong>Important :</strong> Ce code expirera dans 10 minutes.</p>
          <p class="warning">Si vous n'avez pas demandé cette réinitialisation, veuillez ignorer cet email et sécuriser votre compte.</p>
        </div>
        <div class="footer">
          <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
          <p>&copy; ${new Date().getFullYear()} BENYOUNES WEB - Tous droits réservés</p>
        </div>
      </body>
      </html>
    `;

    // Envoyer l'email avec le code OTP
    const emailSent = await sendEmail({
      to: user.email,
      subject: 'Code de réinitialisation de mot de passe',
      html: emailContent,
    });

    if (!emailSent) {
      // En cas d'échec d'envoi d'email, on conserve le code OTP pour permettre les tests
      // mais on informe l'utilisateur du problème
      console.error(`Échec d'envoi d'email à ${user.email}, mais le code OTP ${otp} est toujours valide pour les tests`);

      return res.status(500).json({
        error: 'Erreur lors de l\'envoi de l\'email',
        message: 'Un problème est survenu lors de l\'envoi de l\'email. Veuillez réessayer ou contacter le support.',
        // En environnement de développement uniquement, on peut renvoyer le code pour les tests
        ...(process.env.NODE_ENV === 'development' && { testCode: otp })
      });
    }

    // Afficher également le code dans la console pour le débogage
    console.log(`Email envoyé à ${user.email} avec le code OTP: ${otp}`);

    res.status(200).json({
      message: 'Code de réinitialisation envoyé avec succès',
      email: user.email // Renvoyer l'email pour la prochaine étape
    });
  } catch (error) {
    console.error('Erreur lors de la demande de réinitialisation de mot de passe:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la demande de réinitialisation de mot de passe' });
  }
});

// Vérifier le code OTP
router.post('/verify-otp', async (req, res) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({ error: 'Email et code OTP requis' });
    }

    // Chercher l'utilisateur avec cet email
    const user = await User.findOne({
      email,
      resetOTP: otp,
      resetOTPExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({ error: 'Code invalide ou expiré' });
    }

    res.status(200).json({
      message: 'Code valide',
      email: user.email // Renvoyer l'email pour la prochaine étape
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du code OTP:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du code OTP' });
  }
});

// Réinitialiser le mot de passe avec OTP
router.post('/reset-password-otp', async (req, res) => {
  try {
    const { email, otp, password } = req.body;

    // Valider les entrées
    if (!email || !otp || !password) {
      return res.status(400).json({ error: 'Email, code OTP et nouveau mot de passe requis' });
    }

    // Valider le nouveau mot de passe
    if (password.length < 8) {
      return res.status(400).json({ error: 'Le mot de passe doit contenir au moins 8 caractères' });
    }

    // Chercher l'utilisateur avec cet email et ce code OTP
    const user = await User.findOne({
      email,
      resetOTP: otp,
      resetOTPExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({ error: 'Code invalide ou expiré' });
    }

    // Hasher le nouveau mot de passe
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Mettre à jour le mot de passe et supprimer les champs de réinitialisation
    user.motDePasse = hashedPassword;
    user.resetOTP = undefined;
    user.resetOTPExpires = undefined;
    await user.save();

    // Générer un nouveau token JWT pour l'utilisateur
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRATION }
    );

    // Préparer les données utilisateur pour la réponse
    const userData = {
      id: user._id,
      nom: user.nom,
      email: user.email,
      role: user.role,
    };

    res.status(200).json({
      message: 'Mot de passe réinitialisé avec succès',
      token,
      user: userData,
    });
  } catch (error) {
    console.error('Erreur lors de la réinitialisation du mot de passe:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la réinitialisation du mot de passe' });
  }
});

module.exports = router;