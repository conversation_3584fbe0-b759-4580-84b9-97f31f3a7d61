{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/container/build-error.tsx"], "sourcesContent": ["import React, { useCallback, useMemo } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { Terminal } from '../components/terminal'\nimport { ErrorOverlayLayout } from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport type { ErrorBaseProps } from '../components/errors/error-overlay/error-overlay'\n\nexport interface BuildErrorProps extends ErrorBaseProps {\n  message: string\n}\n\nconst getErrorTextFromBuildErrorMessage = (multiLineMessage: string) => {\n  const lines = multiLineMessage.split('\\n')\n  // The multi-line build error message looks like:\n  // <file path>:<line number>:<column number>\n  // <error message>\n  // <error code frame of compiler or bundler>\n  // e.g.\n  // ./path/to/file.js:1:1\n  // SyntaxError: ...\n  // > 1 | con st foo =\n  // ...\n  return stripAnsi(lines[1] || '')\n}\n\nexport const BuildError: React.FC<BuildErrorProps> = function BuildError({\n  message,\n  ...props\n}) {\n  const noop = useCallback(() => {}, [])\n  const error = new Error(message)\n  const formattedMessage = useMemo(\n    () => getErrorTextFromBuildErrorMessage(message) || 'Failed to compile',\n    [message]\n  )\n\n  return (\n    <ErrorOverlayLayout\n      errorType=\"Build Error\"\n      errorMessage={formattedMessage}\n      onClose={noop}\n      error={error}\n      footerMessage=\"This error occurred during the build process and can only be dismissed by fixing the error.\"\n      {...props}\n    >\n      <Terminal content={message} />\n    </ErrorOverlayLayout>\n  )\n}\n\nexport const styles = ``\n"], "names": ["React", "useCallback", "useMemo", "stripAnsi", "Terminal", "ErrorOverlayLayout", "getErrorTextFromBuildErrorMessage", "multiLineMessage", "lines", "split", "BuildError", "message", "props", "noop", "error", "Error", "formattedMessage", "errorType", "errorMessage", "onClose", "footerMessage", "content", "styles"], "mappings": ";AAAA,OAAOA,SAASC,WAAW,EAAEC,OAAO,QAAQ,QAAO;AACnD,OAAOC,eAAe,gCAA+B;AACrD,SAASC,QAAQ,QAAQ,yBAAwB;AACjD,SAASC,kBAAkB,QAAQ,iEAAgE;AAOnG,MAAMC,oCAAoC,CAACC;IACzC,MAAMC,QAAQD,iBAAiBE,KAAK,CAAC;IACrC,iDAAiD;IACjD,4CAA4C;IAC5C,kBAAkB;IAClB,4CAA4C;IAC5C,OAAO;IACP,wBAAwB;IACxB,mBAAmB;IACnB,qBAAqB;IACrB,MAAM;IACN,OAAON,UAAUK,KAAK,CAAC,EAAE,IAAI;AAC/B;AAEA,OAAO,MAAME,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEC,OAAO,EACP,GAAGC,OACJ,GAHwE;IAIvE,MAAMC,OAAOZ,YAAY,KAAO,GAAG,EAAE;IACrC,MAAMa,QAAQ,qBAAkB,CAAlB,IAAIC,MAAMJ,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/B,MAAMK,mBAAmBd,QACvB,IAAMI,kCAAkCK,YAAY,qBACpD;QAACA;KAAQ;IAGX,qBACE,KAACN;QACCY,WAAU;QACVC,cAAcF;QACdG,SAASN;QACTC,OAAOA;QACPM,eAAc;QACb,GAAGR,KAAK;kBAET,cAAA,KAACR;YAASiB,SAASV;;;AAGzB,EAAC;AAED,OAAO,MAAMW,SAAU,GAAC"}