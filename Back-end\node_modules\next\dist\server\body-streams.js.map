{"version": 3, "sources": ["../../src/server/body-streams.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { Readable } from 'stream'\nimport { PassThrough } from 'stream'\n\nexport function requestToBodyStream(\n  context: { ReadableStream: typeof ReadableStream },\n  KUint8Array: typeof Uint8Array,\n  stream: Readable\n) {\n  return new context.ReadableStream({\n    start: async (controller) => {\n      for await (const chunk of stream) {\n        controller.enqueue(new KUint8Array(chunk))\n      }\n      controller.close()\n    },\n  })\n}\n\nfunction replaceRequestBody<T extends IncomingMessage>(\n  base: T,\n  stream: Readable\n): T {\n  for (const key in stream) {\n    let v = stream[key as keyof Readable] as any\n    if (typeof v === 'function') {\n      v = v.bind(base)\n    }\n    base[key as keyof T] = v\n  }\n\n  return base\n}\n\nexport interface CloneableBody {\n  finalize(): Promise<void>\n  cloneBodyStream(): Readable\n}\n\nexport function getCloneableBody<T extends IncomingMessage>(\n  readable: T\n): CloneableBody {\n  let buffered: Readable | null = null\n\n  const endPromise = new Promise<void | { error?: unknown }>(\n    (resolve, reject) => {\n      readable.on('end', resolve)\n      readable.on('error', reject)\n    }\n  ).catch((error) => {\n    return { error }\n  })\n\n  return {\n    /**\n     * Replaces the original request body if necessary.\n     * This is done because once we read the body from the original request,\n     * we can't read it again.\n     */\n    async finalize(): Promise<void> {\n      if (buffered) {\n        const res = await endPromise\n\n        if (res && typeof res === 'object' && res.error) {\n          throw res.error\n        }\n        replaceRequestBody(readable, buffered)\n        buffered = readable\n      }\n    },\n    /**\n     * Clones the body stream\n     * to pass into a middleware\n     */\n    cloneBodyStream() {\n      const input = buffered ?? readable\n      const p1 = new PassThrough()\n      const p2 = new PassThrough()\n      input.on('data', (chunk) => {\n        p1.push(chunk)\n        p2.push(chunk)\n      })\n      input.on('end', () => {\n        p1.push(null)\n        p2.push(null)\n      })\n      buffered = p2\n      return p1\n    },\n  }\n}\n"], "names": ["getCloneableBody", "requestToBodyStream", "context", "KUint8Array", "stream", "ReadableStream", "start", "controller", "chunk", "enqueue", "close", "replaceRequestBody", "base", "key", "v", "bind", "readable", "buffered", "endPromise", "Promise", "resolve", "reject", "on", "catch", "error", "finalize", "res", "cloneBodyStream", "input", "p1", "PassThrough", "p2", "push"], "mappings": ";;;;;;;;;;;;;;;IAuCgBA,gBAAgB;eAAhBA;;IAnCAC,mBAAmB;eAAnBA;;;wBAFY;AAErB,SAASA,oBACdC,OAAkD,EAClDC,WAA8B,EAC9BC,MAAgB;IAEhB,OAAO,IAAIF,QAAQG,cAAc,CAAC;QAChCC,OAAO,OAAOC;YACZ,WAAW,MAAMC,SAASJ,OAAQ;gBAChCG,WAAWE,OAAO,CAAC,IAAIN,YAAYK;YACrC;YACAD,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,SAASC,mBACPC,IAAO,EACPR,MAAgB;IAEhB,IAAK,MAAMS,OAAOT,OAAQ;QACxB,IAAIU,IAAIV,MAAM,CAACS,IAAsB;QACrC,IAAI,OAAOC,MAAM,YAAY;YAC3BA,IAAIA,EAAEC,IAAI,CAACH;QACb;QACAA,IAAI,CAACC,IAAe,GAAGC;IACzB;IAEA,OAAOF;AACT;AAOO,SAASZ,iBACdgB,QAAW;IAEX,IAAIC,WAA4B;IAEhC,MAAMC,aAAa,IAAIC,QACrB,CAACC,SAASC;QACRL,SAASM,EAAE,CAAC,OAAOF;QACnBJ,SAASM,EAAE,CAAC,SAASD;IACvB,GACAE,KAAK,CAAC,CAACC;QACP,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAO;QACL;;;;KAIC,GACD,MAAMC;YACJ,IAAIR,UAAU;gBACZ,MAAMS,MAAM,MAAMR;gBAElB,IAAIQ,OAAO,OAAOA,QAAQ,YAAYA,IAAIF,KAAK,EAAE;oBAC/C,MAAME,IAAIF,KAAK;gBACjB;gBACAb,mBAAmBK,UAAUC;gBAC7BA,WAAWD;YACb;QACF;QACA;;;KAGC,GACDW;YACE,MAAMC,QAAQX,YAAYD;YAC1B,MAAMa,KAAK,IAAIC,mBAAW;YAC1B,MAAMC,KAAK,IAAID,mBAAW;YAC1BF,MAAMN,EAAE,CAAC,QAAQ,CAACd;gBAChBqB,GAAGG,IAAI,CAACxB;gBACRuB,GAAGC,IAAI,CAACxB;YACV;YACAoB,MAAMN,EAAE,CAAC,OAAO;gBACdO,GAAGG,IAAI,CAAC;gBACRD,GAAGC,IAAI,CAAC;YACV;YACAf,WAAWc;YACX,OAAOF;QACT;IACF;AACF"}