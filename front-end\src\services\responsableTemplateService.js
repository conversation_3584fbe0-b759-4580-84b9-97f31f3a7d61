import { api } from './api';

/**
 * Service for managing responsable template customizations
 */
export const responsableTemplateService = {
  /**
   * Get current template settings for the responsable/enterprise
   * @returns {Promise<Object>} Template settings object
   */
  getTemplateSettings: async () => {
    try {
      const response = await api.get('/responsable-templates');
      return response.data;
    } catch (error) {
      console.error('Error fetching template settings:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la récupération des paramètres de template');
    }
  },

  /**
   * Get template settings for a specific document type
   * @param {string} type - Document type (facture/devis)
   * @returns {Promise<Object>} Template settings for the specified type
   */
  getTemplateSettingsByType: async (type) => {
    try {
      const response = await api.get(`/responsable-templates/${type}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching template settings by type:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la récupération des paramètres de template');
    }
  },

  /**
   * Update template settings
   * @param {FormData|Object} data - Template settings data (FormData for file uploads)
   * @returns {Promise<Object>} Updated template settings
   */
  updateTemplateSettings: async (data) => {
    try {
      const config = {};

      // If data is FormData (for file uploads), set appropriate headers
      if (data instanceof FormData) {
        config.headers = {
          'Content-Type': 'multipart/form-data'
        };
      }

      const response = await api.put('/responsable-templates', data, config);
      return response.data;
    } catch (error) {
      console.error('Error updating template settings:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la mise à jour des paramètres de template');
    }
  },

  /**
   * Upload logo for a specific template type
   * @param {string} type - Template type (facture/devis)
   * @param {File} logoFile - Logo file to upload
   * @returns {Promise<Object>} Updated template settings
   */
  uploadLogo: async (type, logoFile) => {
    try {
      const formData = new FormData();
      formData.append(`${type}Logo`, logoFile);

      const response = await api.put('/responsable-templates', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading logo:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors du téléchargement du logo');
    }
  },

  /**
   * Update colors for a specific template type
   * @param {string} type - Template type (facture/devis)
   * @param {Object} colors - Color settings { color }
   * @returns {Promise<Object>} Updated template settings
   */
  updateColors: async (type, colors) => {
    try {
      const updateData = {
        [`${type}Template`]: colors
      };

      const response = await api.put('/responsable-templates', {
        templateData: JSON.stringify(updateData)
      });

      return response.data;
    } catch (error) {
      console.error('Error updating colors:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la mise à jour des couleurs');
    }
  },

  /**
   * Update base template assignment
   * @param {string} type - Template type (facture/devis)
   * @param {string} baseTemplateId - ID of the base template to assign
   * @returns {Promise<Object>} Updated template settings
   */
  updateBaseTemplate: async (type, baseTemplateId) => {
    try {
      const updateData = {
        [`${type}Template`]: {
          baseTemplateId
        }
      };

      const response = await api.put('/responsable-templates', {
        templateData: JSON.stringify(updateData)
      });

      return response.data;
    } catch (error) {
      console.error('Error updating base template:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la mise à jour du template de base');
    }
  },



  /**
   * Get template preview data
   * @param {string} type - Template type (facture/devis)
   * @returns {Promise<Object>} Template preview data
   */
  getTemplatePreview: async (type) => {
    try {
      const settings = await responsableTemplateService.getTemplateSettingsByType(type);
      return {
        ...settings,
        previewUrl: `/api/responsable-templates/${type}/preview`
      };
    } catch (error) {
      console.error('Error getting template preview:', error);
      throw new Error('Erreur lors de la génération de l\'aperçu');
    }
  }
};
