# 🗃️ Database Seeding Guide

This guide explains how to populate your MongoDB database with realistic sample data for testing the invoice and quote management system.

## 📋 Overview

The seeding script creates comprehensive sample data including:

### 👥 Users (7 total)
- **1 Admin**: System administrator
- **2 Responsables**: Company managers with active subscriptions  
- **2 Vendeurs**: Sales representatives assigned to responsables
- **2 Clients**: Customer users

### 🏢 Clients (5 total)
- Various companies with different statuses (active/inactive)
- Complete contact information and CIN numbers
- Realistic Tunisian business addresses

### 📦 Products (3 total)
- **Services**: Website creation, mobile app development
- **Physical Products**: Dell laptops with stock management
- Different categories and pricing in Tunisian Dinars

### 📋 Devis/Quotes (4 total)
- **SENT**: Quote sent to client, awaiting response
- **ACCEPTED**: Quote accepted by client
- **DRAFT**: Quote in preparation
- **REJECTED**: Quote rejected by client
- Complete workflow with responsable validation

### 🧾 Factures/Invoices (6 total)
- **PAID**: Fully paid invoices with payment details
- **SENT**: Invoices sent to clients
- **ACCEPTED**: Invoices accepted by clients
- **DRAFT**: Draft invoices
- **REJECTED**: Rejected invoices with reasons
- **PARTIALLY_PAID**: Invoices with partial payments
- Linked to corresponding quotes where applicable

## 🚀 How to Run

### Prerequisites
1. **MongoDB running** on localhost:27017
2. **Database name**: `test`
3. **Node.js installed**
4. **All dependencies installed** (`npm install`)

### Running the Seeder

```bash
# Navigate to the Back-end directory
cd Back-end

# Run the seeding script
npm run seed
```

### Alternative Commands
```bash
# Long form
npm run seed-database

# Direct execution
node seedDatabase.js
```

### Expected Output
```
🚀 Starting database seeding...

✅ MongoDB connected successfully
🧹 Database cleared successfully

📊 Creating sample data...

👥 Creating users...
✅ Created 7 users with relationships
🏢 Creating client companies...
✅ Created 5 client companies
📦 Creating products...
✅ Created 3 products
📋 Creating quotes (devis)...
✅ Created 4 quotes with different statuses
🧾 Creating invoices (factures)...
✅ Created 6 invoices with different statuses

🎉 Database seeding completed successfully!

📈 Summary:
   👥 Users: 7
   🏢 Clients: 5
   📦 Products: 3
   📋 Quotes: 4
   🧾 Invoices: 6

🔑 Test Accounts:
   Admin: <EMAIL> / Admin123
   Responsable 1: <EMAIL> / 112233
   Responsable 2: <EMAIL> / 112233
   Vendeur 1: <EMAIL> / 112233
   Vendeur 2: <EMAIL> / 112233

📊 Status Distribution:
   Devis: DRAFT(1), SENT(1), ACCEPTED(1), REJECTED(1)
   Factures: DRAFT(1), SENT(1), ACCEPTED(1), PAID(1), REJECTED(1), PARTIALLY_PAID(1)

💰 Financial Summary:
   Total Quotes Value: 25,440 DT
   Total Invoices Value: 18,660 DT

✨ Ready to test! Start your server and explore the data.

🔌 Database connection closed
```

## 🔑 Test Accounts

You can log in with these accounts to test different roles:

| Role | Email | Password | Company |
|------|-------|----------|---------|
| **Admin** | <EMAIL> | Admin123 | System |
| **Responsable 1** | <EMAIL> | 112233 | TechSolutions |
| **Responsable 2** | <EMAIL> | 112233 | InnovatePlus |
| **Vendeur 1** | <EMAIL> | 112233 | TechSolutions |
| **Vendeur 2** | <EMAIL> | 112233 | InnovatePlus |

## 🔗 Business Relationships
- **Ahmed Khelifi** (Vendeur) works under **Mohamed Ben Ahmed** (Responsable)
- **Leila Mansouri** (Vendeur) works under **Fatma Trabelsi** (Responsable)
- Invoices and quotes are properly assigned to these relationships

## 📊 Sample Data Details

### Status Distribution

**Devis Statuses:**
- 1 SENT (awaiting client response)
- 1 ACCEPTED (ready for invoicing)
- 1 DRAFT (in preparation)
- 1 REJECTED (declined by client)

**Facture Statuses:**
- 1 PAID (completed transaction)
- 1 SENT (awaiting client action)
- 1 ACCEPTED (approved by client)
- 1 DRAFT (in preparation)
- 1 REJECTED (declined by client)
- 1 PARTIALLY_PAID (partial payment received)

### Financial Data
- Realistic pricing in Tunisian Dinars
- 20% TVA applied consistently
- Payment methods: Bank transfer, Check, Cash
- Stock management for physical products

## 🛠️ Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   ```
   ❌ MongoDB connection error: connect ECONNREFUSED
   ```
   **Solution**: Ensure MongoDB is running on localhost:27017
   - Windows: Start MongoDB service or run `mongod`
   - macOS: `brew services start mongodb-community`
   - Linux: `sudo systemctl start mongod`

2. **Duplicate Key Errors**
   ```
   E11000 duplicate key error
   ```
   **Solution**: The script clears existing data before seeding. If you get this error, manually clear the collections:
   ```javascript
   // In MongoDB shell or Compass
   use test
   db.users.deleteMany({})
   db.clients.deleteMany({})
   db.produits.deleteMany({})
   db.devis.deleteMany({})
   db.factures.deleteMany({})
   ```

3. **Missing Dependencies**
   ```
   Cannot find module 'bcrypt'
   ```
   **Solution**: Run `npm install` to ensure all packages are installed

### Manual Database Cleanup
If needed, you can manually clear the database:

```bash
# Using MongoDB shell
mongo test --eval "
  db.users.deleteMany({});
  db.clients.deleteMany({});
  db.produits.deleteMany({});
  db.devis.deleteMany({});
  db.factures.deleteMany({});
"
```

## 🎯 Next Steps

After seeding:
1. **Start your backend server**: `npm start` or `node server.js`
2. **Start your frontend**: `npm start` (in frontend directory)
3. **Log in** with any of the test accounts
4. **Explore** the different features with realistic data
5. **Test** invoice creation, quote management, and user workflows

The seeded data provides a comprehensive testing environment that mirrors real-world usage patterns and business scenarios.

## 🔄 Re-running the Seeder

You can run the seeder multiple times. It will:
- Clear all existing data
- Create fresh sample data
- Maintain consistent relationships

This is useful for:
- Resetting test data
- Testing with clean data
- Demonstrating the system to stakeholders
