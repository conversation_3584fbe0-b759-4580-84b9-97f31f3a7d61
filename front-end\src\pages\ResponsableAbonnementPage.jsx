import React from 'react';
import {
  Box,
  Container,
  Typography,
  Divider,
  Paper,
  useTheme
} from '@mui/material';
import { motion } from 'framer-motion';
import { 
  SubscriptionsOutlined as SubscriptionsIcon
} from '@mui/icons-material';
import AbonnementStatus from '../components/responsable/abonnement/AbonnementStatus';
import AbonnementRenewal from '../components/responsable/abonnement/AbonnementRenewal';

const ResponsableAbonnementPage = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <Container maxWidth="lg">
      <Box
        component={motion.div}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        sx={{ py: 4 }}
      >
        {/* Header */}
        <Box
          component={motion.div}
          variants={itemVariants}
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 4
          }}
        >
          <SubscriptionsIcon
            sx={{
              fontSize: 40,
              color: theme.palette.primary.main,
              mr: 2
            }}
          />
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold">
              Gestion de l'abonnement
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Consultez et gérez votre abonnement
            </Typography>
          </Box>
        </Box>

        {/* Statut de l'abonnement */}
        <Box component={motion.div} variants={itemVariants} sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" fontWeight="600" sx={{ mb: 2 }}>
            Statut de l'abonnement
          </Typography>
          <AbonnementStatus />
        </Box>

        <Divider sx={{ my: 4 }} />

        {/* Demande de renouvellement */}
        <Box component={motion.div} variants={itemVariants}>
          <Typography variant="h5" component="h2" fontWeight="600" sx={{ mb: 2 }}>
            Demander un renouvellement
          </Typography>
          <AbonnementRenewal />
        </Box>
      </Box>
    </Container>
  );
};

export default ResponsableAbonnementPage;
