{"version": 3, "sources": ["../../src/lib/mime-type.ts"], "sourcesContent": ["/**\n * Map of images extensions to MIME types\n */\nexport const imageExtMimeTypeMap = {\n  jpeg: 'image/jpeg',\n  png: 'image/png',\n  ico: 'image/x-icon',\n  svg: 'image/svg+xml',\n} as const\n"], "names": ["imageExtMimeTypeMap", "jpeg", "png", "ico", "svg"], "mappings": "AAAA;;CAEC;;;;+BACYA;;;eAAAA;;;AAAN,MAAMA,sBAAsB;IACjCC,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,KAAK;AACP"}