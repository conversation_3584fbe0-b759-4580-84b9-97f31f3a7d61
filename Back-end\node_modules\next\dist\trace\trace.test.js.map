{"version": 3, "sources": ["../../src/trace/trace.test.ts"], "sourcesContent": ["import { mkdtemp, readFile } from 'fs/promises'\nimport { join } from 'path'\nimport { tmpdir } from 'os'\nimport { setGlobal } from './shared'\nimport {\n  clearTraceEvents,\n  exportTraceState,\n  flushAllTraces,\n  getTraceEvents,\n  initializeTraceState,\n  recordTraceEvents,\n  trace,\n} from './trace'\n\ndescribe('Trace', () => {\n  beforeEach(() => {\n    initializeTraceState({\n      lastId: 0,\n      shouldSaveTraceEvents: true,\n    })\n    clearTraceEvents()\n  })\n\n  describe('Tracer', () => {\n    it('traces a block of code', async () => {\n      const tmpDir = await mkdtemp(join(tmpdir(), 'json-reporter'))\n      setGlobal('distDir', tmpDir)\n      setGlobal('phase', 'anything')\n\n      const root = trace('root-span', undefined, {\n        'some-tag': 'some-value',\n      })\n      root.traceChild('child-span').traceFn(() => null)\n      await root.traceChild('async-child-span').traceAsyncFn(async () => {\n        const delayedPromise = new Promise((resolve) => {\n          setTimeout(resolve, 100)\n        })\n        await delayedPromise\n      })\n      root.stop()\n      const traceEvents = getTraceEvents()\n      expect(traceEvents.length).toEqual(3)\n      expect(traceEvents[0].name).toEqual('child-span')\n      expect(traceEvents[1].name).toEqual('async-child-span')\n      expect(traceEvents[2].name).toEqual('root-span')\n\n      // Check that the serialized .next/trace file looks correct.\n      await flushAllTraces()\n      const traceFilename = join(tmpDir, 'trace')\n      const serializedTraces = JSON.parse(\n        await readFile(traceFilename, 'utf-8')\n      )\n      expect(serializedTraces).toMatchObject([\n        {\n          id: 2,\n          name: 'child-span',\n          parentId: 1,\n          startTime: expect.any(Number),\n          timestamp: expect.any(Number),\n          duration: expect.any(Number),\n          tags: {},\n        },\n        {\n          id: 3,\n          name: 'async-child-span',\n          parentId: 1,\n          startTime: expect.any(Number),\n          timestamp: expect.any(Number),\n          duration: expect.any(Number),\n          tags: {},\n        },\n        {\n          id: 1,\n          name: 'root-span',\n          startTime: expect.any(Number),\n          timestamp: expect.any(Number),\n          duration: expect.any(Number),\n          tags: {\n            'some-tag': 'some-value',\n          },\n        },\n      ])\n    })\n  })\n\n  describe('Worker', () => {\n    it('exports and initializes trace state', () => {\n      const root = trace('root-span')\n      expect(root.getId()).toEqual(1)\n      const traceState = exportTraceState()\n      expect(traceState.lastId).toEqual(1)\n      initializeTraceState({\n        lastId: 101,\n      })\n      const span = trace('another-span')\n      expect(span.getId()).toEqual(102)\n    })\n\n    it('trace data is serializable to a worker', async () => {\n      const root = trace('root-span')\n      root.traceChild('child-span').traceFn(() => null)\n      root.stop()\n      const traceEvents = getTraceEvents()\n      expect(traceEvents.length).toEqual(2)\n      // This is a proxy check to make sure the object would be serializable\n      // to a worker. It will fail if the data contains some unserializable\n      // objects like BigInt.\n      const clone = JSON.parse(JSON.stringify(traceEvents))\n      expect(clone).toEqual(traceEvents)\n    })\n\n    it('correctly reports trace data from multiple workers', () => {\n      // This test simulates workers creating traces and propagating them\n      // back to the main process for recording. It doesn't use\n      // actual workers since they are more difficult to set up in tests.\n      initializeTraceState({\n        lastId: 5,\n        defaultParentSpanId: 1,\n        shouldSaveTraceEvents: true,\n      })\n      const worker1Span = trace('worker1')\n      worker1Span.traceChild('webpack-compilation1').traceFn(() => null)\n      worker1Span.stop()\n      const worker1Traces = getTraceEvents()\n      expect(worker1Traces.length).toEqual(2)\n\n      // Repeat for a second worker.\n      clearTraceEvents()\n      initializeTraceState({\n        lastId: 10,\n        defaultParentSpanId: 1,\n        shouldSaveTraceEvents: true,\n      })\n      const worker2Span = trace('worker2')\n      worker2Span.traceChild('webpack-compilation2').traceFn(() => null)\n      worker2Span.stop()\n      const worker2Traces = getTraceEvents()\n      expect(worker2Traces.length).toEqual(2)\n\n      // Now simulate the traces in the main process and record the traces\n      // from each worker.\n      clearTraceEvents()\n      initializeTraceState({\n        lastId: 0,\n        shouldSaveTraceEvents: true,\n      })\n      const root = trace('next-build')\n      root.traceChild('some-child-span').traceFn(() => null)\n      recordTraceEvents(worker1Traces)\n      expect(exportTraceState().lastId).toEqual(8)\n      recordTraceEvents(worker2Traces)\n      expect(exportTraceState().lastId).toEqual(13)\n      root.traceChild('another-child-span').traceFn(() => null)\n      root.stop()\n\n      // Check that the final output looks correct.\n      const allTraces = getTraceEvents()\n      expect(allTraces.length).toEqual(7)\n      const firstSpan = allTraces[0]\n      expect(firstSpan.name).toEqual('some-child-span')\n      expect(firstSpan.id).toEqual(2)\n      expect(firstSpan.parentId).toEqual(1)\n\n      const worker1Child = allTraces[1]\n      expect(worker1Child.name).toEqual('webpack-compilation1')\n      expect(worker1Child.id).toEqual(7)\n      expect(worker1Child.parentId).toEqual(6)\n      const worker1Root = allTraces[2]\n      expect(worker1Root.name).toEqual('worker1')\n      expect(worker1Root.id).toEqual(6)\n      expect(worker1Root.parentId).toEqual(1)\n\n      const worker2Child = allTraces[3]\n      expect(worker2Child.name).toEqual('webpack-compilation2')\n      expect(worker2Child.id).toEqual(12)\n      expect(worker2Child.parentId).toEqual(11)\n      const worker2Root = allTraces[4]\n      expect(worker2Root.name).toEqual('worker2')\n      expect(worker2Root.id).toEqual(11)\n      expect(worker2Root.parentId).toEqual(1)\n\n      const lastChildSpan = allTraces[5]\n      expect(lastChildSpan.name).toEqual('another-child-span')\n      expect(lastChildSpan.id).toEqual(14)\n      expect(lastChildSpan.parentId).toEqual(1)\n\n      const rootSpan = allTraces[6]\n      expect(rootSpan.name).toEqual('next-build')\n      expect(rootSpan.id).toEqual(1)\n      expect(rootSpan.parentId).toBeUndefined()\n    })\n  })\n})\n"], "names": ["describe", "beforeEach", "initializeTraceState", "lastId", "shouldSaveTraceEvents", "clearTraceEvents", "it", "tmpDir", "mkdtemp", "join", "tmpdir", "setGlobal", "root", "trace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "traceAsyncFn", "delayedPromise", "Promise", "resolve", "setTimeout", "stop", "traceEvents", "getTraceEvents", "expect", "length", "toEqual", "name", "flushAllTraces", "traceFilename", "serializedTraces", "JSON", "parse", "readFile", "toMatchObject", "id", "parentId", "startTime", "any", "Number", "timestamp", "duration", "tags", "getId", "traceState", "exportTraceState", "span", "clone", "stringify", "defaultParentSpanId", "worker1Span", "worker1Traces", "worker2Span", "worker2Traces", "recordTraceEvents", "allTraces", "firstSpan", "worker1<PERSON><PERSON>d", "worker1Root", "worker2<PERSON><PERSON>d", "worker2Root", "lastChildSpan", "rootSpan", "toBeUndefined"], "mappings": ";;;;0BAAkC;sBACb;oBACE;wBACG;uBASnB;AAEPA,SAAS,SAAS;IAChBC,WAAW;QACTC,IAAAA,2BAAoB,EAAC;YACnBC,QAAQ;YACRC,uBAAuB;QACzB;QACAC,IAAAA,uBAAgB;IAClB;IAEAL,SAAS,UAAU;QACjBM,GAAG,0BAA0B;YAC3B,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACC,IAAAA,UAAI,EAACC,IAAAA,UAAM,KAAI;YAC5CC,IAAAA,iBAAS,EAAC,WAAWJ;YACrBI,IAAAA,iBAAS,EAAC,SAAS;YAEnB,MAAMC,OAAOC,IAAAA,YAAK,EAAC,aAAaC,WAAW;gBACzC,YAAY;YACd;YACAF,KAAKG,UAAU,CAAC,cAAcC,OAAO,CAAC,IAAM;YAC5C,MAAMJ,KAAKG,UAAU,CAAC,oBAAoBE,YAAY,CAAC;gBACrD,MAAMC,iBAAiB,IAAIC,QAAQ,CAACC;oBAClCC,WAAWD,SAAS;gBACtB;gBACA,MAAMF;YACR;YACAN,KAAKU,IAAI;YACT,MAAMC,cAAcC,IAAAA,qBAAc;YAClCC,OAAOF,YAAYG,MAAM,EAAEC,OAAO,CAAC;YACnCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YACpCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YACpCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YAEpC,4DAA4D;YAC5D,MAAME,IAAAA,qBAAc;YACpB,MAAMC,gBAAgBrB,IAAAA,UAAI,EAACF,QAAQ;YACnC,MAAMwB,mBAAmBC,KAAKC,KAAK,CACjC,MAAMC,IAAAA,kBAAQ,EAACJ,eAAe;YAEhCL,OAAOM,kBAAkBI,aAAa,CAAC;gBACrC;oBACEC,IAAI;oBACJR,MAAM;oBACNS,UAAU;oBACVC,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM,CAAC;gBACT;gBACA;oBACEP,IAAI;oBACJR,MAAM;oBACNS,UAAU;oBACVC,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM,CAAC;gBACT;gBACA;oBACEP,IAAI;oBACJR,MAAM;oBACNU,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM;wBACJ,YAAY;oBACd;gBACF;aACD;QACH;IACF;IAEA3C,SAAS,UAAU;QACjBM,GAAG,uCAAuC;YACxC,MAAMM,OAAOC,IAAAA,YAAK,EAAC;YACnBY,OAAOb,KAAKgC,KAAK,IAAIjB,OAAO,CAAC;YAC7B,MAAMkB,aAAaC,IAAAA,uBAAgB;YACnCrB,OAAOoB,WAAW1C,MAAM,EAAEwB,OAAO,CAAC;YAClCzB,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;YACV;YACA,MAAM4C,OAAOlC,IAAAA,YAAK,EAAC;YACnBY,OAAOsB,KAAKH,KAAK,IAAIjB,OAAO,CAAC;QAC/B;QAEArB,GAAG,0CAA0C;YAC3C,MAAMM,OAAOC,IAAAA,YAAK,EAAC;YACnBD,KAAKG,UAAU,CAAC,cAAcC,OAAO,CAAC,IAAM;YAC5CJ,KAAKU,IAAI;YACT,MAAMC,cAAcC,IAAAA,qBAAc;YAClCC,OAAOF,YAAYG,MAAM,EAAEC,OAAO,CAAC;YACnC,sEAAsE;YACtE,qEAAqE;YACrE,uBAAuB;YACvB,MAAMqB,QAAQhB,KAAKC,KAAK,CAACD,KAAKiB,SAAS,CAAC1B;YACxCE,OAAOuB,OAAOrB,OAAO,CAACJ;QACxB;QAEAjB,GAAG,sDAAsD;YACvD,mEAAmE;YACnE,yDAAyD;YACzD,mEAAmE;YACnEJ,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACR+C,qBAAqB;gBACrB9C,uBAAuB;YACzB;YACA,MAAM+C,cAActC,IAAAA,YAAK,EAAC;YAC1BsC,YAAYpC,UAAU,CAAC,wBAAwBC,OAAO,CAAC,IAAM;YAC7DmC,YAAY7B,IAAI;YAChB,MAAM8B,gBAAgB5B,IAAAA,qBAAc;YACpCC,OAAO2B,cAAc1B,MAAM,EAAEC,OAAO,CAAC;YAErC,8BAA8B;YAC9BtB,IAAAA,uBAAgB;YAChBH,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACR+C,qBAAqB;gBACrB9C,uBAAuB;YACzB;YACA,MAAMiD,cAAcxC,IAAAA,YAAK,EAAC;YAC1BwC,YAAYtC,UAAU,CAAC,wBAAwBC,OAAO,CAAC,IAAM;YAC7DqC,YAAY/B,IAAI;YAChB,MAAMgC,gBAAgB9B,IAAAA,qBAAc;YACpCC,OAAO6B,cAAc5B,MAAM,EAAEC,OAAO,CAAC;YAErC,oEAAoE;YACpE,oBAAoB;YACpBtB,IAAAA,uBAAgB;YAChBH,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACRC,uBAAuB;YACzB;YACA,MAAMQ,OAAOC,IAAAA,YAAK,EAAC;YACnBD,KAAKG,UAAU,CAAC,mBAAmBC,OAAO,CAAC,IAAM;YACjDuC,IAAAA,wBAAiB,EAACH;YAClB3B,OAAOqB,IAAAA,uBAAgB,IAAG3C,MAAM,EAAEwB,OAAO,CAAC;YAC1C4B,IAAAA,wBAAiB,EAACD;YAClB7B,OAAOqB,IAAAA,uBAAgB,IAAG3C,MAAM,EAAEwB,OAAO,CAAC;YAC1Cf,KAAKG,UAAU,CAAC,sBAAsBC,OAAO,CAAC,IAAM;YACpDJ,KAAKU,IAAI;YAET,6CAA6C;YAC7C,MAAMkC,YAAYhC,IAAAA,qBAAc;YAChCC,OAAO+B,UAAU9B,MAAM,EAAEC,OAAO,CAAC;YACjC,MAAM8B,YAAYD,SAAS,CAAC,EAAE;YAC9B/B,OAAOgC,UAAU7B,IAAI,EAAED,OAAO,CAAC;YAC/BF,OAAOgC,UAAUrB,EAAE,EAAET,OAAO,CAAC;YAC7BF,OAAOgC,UAAUpB,QAAQ,EAAEV,OAAO,CAAC;YAEnC,MAAM+B,eAAeF,SAAS,CAAC,EAAE;YACjC/B,OAAOiC,aAAa9B,IAAI,EAAED,OAAO,CAAC;YAClCF,OAAOiC,aAAatB,EAAE,EAAET,OAAO,CAAC;YAChCF,OAAOiC,aAAarB,QAAQ,EAAEV,OAAO,CAAC;YACtC,MAAMgC,cAAcH,SAAS,CAAC,EAAE;YAChC/B,OAAOkC,YAAY/B,IAAI,EAAED,OAAO,CAAC;YACjCF,OAAOkC,YAAYvB,EAAE,EAAET,OAAO,CAAC;YAC/BF,OAAOkC,YAAYtB,QAAQ,EAAEV,OAAO,CAAC;YAErC,MAAMiC,eAAeJ,SAAS,CAAC,EAAE;YACjC/B,OAAOmC,aAAahC,IAAI,EAAED,OAAO,CAAC;YAClCF,OAAOmC,aAAaxB,EAAE,EAAET,OAAO,CAAC;YAChCF,OAAOmC,aAAavB,QAAQ,EAAEV,OAAO,CAAC;YACtC,MAAMkC,cAAcL,SAAS,CAAC,EAAE;YAChC/B,OAAOoC,YAAYjC,IAAI,EAAED,OAAO,CAAC;YACjCF,OAAOoC,YAAYzB,EAAE,EAAET,OAAO,CAAC;YAC/BF,OAAOoC,YAAYxB,QAAQ,EAAEV,OAAO,CAAC;YAErC,MAAMmC,gBAAgBN,SAAS,CAAC,EAAE;YAClC/B,OAAOqC,cAAclC,IAAI,EAAED,OAAO,CAAC;YACnCF,OAAOqC,cAAc1B,EAAE,EAAET,OAAO,CAAC;YACjCF,OAAOqC,cAAczB,QAAQ,EAAEV,OAAO,CAAC;YAEvC,MAAMoC,WAAWP,SAAS,CAAC,EAAE;YAC7B/B,OAAOsC,SAASnC,IAAI,EAAED,OAAO,CAAC;YAC9BF,OAAOsC,SAAS3B,EAAE,EAAET,OAAO,CAAC;YAC5BF,OAAOsC,SAAS1B,QAAQ,EAAE2B,aAAa;QACzC;IACF;AACF"}