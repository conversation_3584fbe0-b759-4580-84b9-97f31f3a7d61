{"name": "brotli", "version": "1.3.3", "description": "A port of the Brotli compression algorithm as used in WOFF2", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/devongovett/brotli.js.git"}, "keywords": ["compress", "decompress", "encode", "decode"], "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/devongovett/brotli.js/issues"}, "homepage": "https://github.com/devongovett/brotli.js", "dependencies": {"base64-js": "^1.1.2"}, "devDependencies": {"mocha": "^2.2.1"}, "browser": {"./dec/dictionary-data.js": "./dec/dictionary-browser.js"}, "scripts": {"test": "mocha", "prepublish": "make"}}