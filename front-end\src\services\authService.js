import axios from "axios";

const API_URL = "http://localhost:5000/api"; // Matches backend port

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Support CORS with credentials
});

// Add token to requests if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      // Let AuthContext handle redirection
    }
    return Promise.reject(error);
  }
);

// Authentication service functions
const authService = {
  // Verify token validity
  verifyToken: async () => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        localStorage.removeItem("user");
        return false;
      }

      const response = await api.get("/auth/verify-token");
      if (response.status === 200) {
        return true;
      } else {
        localStorage.removeItem("token");
        localStorage.removeItem("user");
        return false;
      }
    } catch (error) {
      console.error("Token verification failed:", error.response?.data || error.message);
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      return false;
    }
  },

  // Login user - sans paramètre de rôle (détection automatique)
  login: async (email, motDePasse) => {
    try {
      // Envoyer seulement email et mot de passe, le rôle est détecté automatiquement
      const loginData = { email, motDePasse };

      const response = await api.post("/auth/login", loginData);
      if (response.data.token) {
        localStorage.setItem("token", response.data.token);
        localStorage.setItem("user", JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: "Erreur de connexion" };
    }
  },

  // Register user
  register: async (userData) => {
    try {
      const response = await api.post("/auth/register", userData);
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: "Erreur d'inscription" };
    }
  },

  // Logout user
  logout: () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
  },

  // Get current user
  getCurrentUser: () => {
    const userStr = localStorage.getItem("user");
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!localStorage.getItem("token");
  },

  // Fetch user profile
  fetchUserProfile: async () => {
    try {
      // Try the new profile endpoint first
      try {
        const response = await api.get("/profile");
        console.log('Profile response:', response.data);
        return response.data;
      } catch (profileError) {
        // Fall back to the auth endpoint if profile endpoint fails
        console.log('Profile endpoint failed, falling back to auth endpoint:', profileError);
        const response = await api.get("/auth/profile");
        console.log('Auth profile response:', response.data);
        return response.data;
      }
    } catch (error) {
      throw error.response?.data || { error: "Erreur lors de la récupération du profil" };
    }
  },

  // Update user profile
  updateProfile: async (profileData) => {
    try {
      console.log('Updating profile with data:', profileData);

      // Make sure we have all required fields
      const requiredFields = ['nom', 'email'];
      requiredFields.forEach(field => {
        if (!profileData[field]) {
          throw { error: `Le champ ${field} est requis` };
        }
      });

      // Try the new profile endpoint first
      let response;
      try {
        response = await api.put("/profile", profileData);
        console.log('Profile update successful:', response.data);
      } catch (profileError) {
        // Fall back to the auth endpoint if profile endpoint fails
        console.log('Profile endpoint failed, falling back to auth endpoint:', profileError);
        response = await api.put("/auth/profile", profileData);
        console.log('Auth profile update successful:', response.data);
      }

      console.log('Profile update response:', response.data);

      // Update local storage with new user data
      if (response.data && response.data.user) {
        // Use the response data from the server to ensure we have the latest data
        localStorage.setItem("user", JSON.stringify(response.data.user));
      } else {
        // Fallback to the data we sent if the server doesn't return user data
        const currentUser = JSON.parse(localStorage.getItem("user") || '{}');
        const updatedUser = { ...currentUser, ...profileData };
        localStorage.setItem("user", JSON.stringify(updatedUser));
      }

      return response.data;
    } catch (error) {
      console.error('Profile update error:', error);
      throw error.response?.data || error || { error: "Erreur lors de la mise à jour du profil" };
    }
  },

  // Change password
  changePassword: async (currentPassword, newPassword) => {
    try {
      const response = await api.put("/auth/change-password", { currentPassword, newPassword });
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: "Erreur lors du changement de mot de passe" };
    }
  },

  // Upload profile image
  uploadProfileImage: async (imageFile) => {
    try {
      console.log('Uploading profile image...');
      const formData = new FormData();
      formData.append('profileImage', imageFile);

      // Create a custom axios instance for this request to handle FormData
      const customApi = axios.create({
        baseURL: API_URL,
        withCredentials: true,
      });

      // Add token to request
      const token = localStorage.getItem("token");
      if (token) {
        customApi.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      }

      // Log the request details for debugging
      console.log('Upload URL:', `${API_URL}/profile/upload-image`);
      console.log('Token present:', !!token);

      // Use the new profile endpoint
      const response = await customApi.post("/profile/upload-image", formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      console.log('Upload response:', response.data);

      // Update local storage with new profile image
      if (response.data && response.data.profileImage) {
        const currentUser = JSON.parse(localStorage.getItem("user") || '{}');
        currentUser.profileImage = response.data.profileImage;
        localStorage.setItem("user", JSON.stringify(currentUser));
      }

      return response.data;
    } catch (error) {
      console.error('Upload error:', error);
      throw error.response?.data || { error: "Erreur lors du téléchargement de l'image de profil" };
    }
  },

  // Demander un code OTP pour réinitialiser le mot de passe
  forgotPassword: async (email) => {
    try {
      const response = await api.post("/auth/forgot-password", { email });
      return response.data;
    } catch (error) {
      // Si l'erreur contient un code de test (pour le développement), on le transmet
      if (error.response?.data?.testCode) {
        throw {
          ...error.response.data,
          testCode: error.response.data.testCode
        };
      }
      throw error.response?.data || { error: "Erreur lors de la demande de réinitialisation du mot de passe" };
    }
  },

  // Vérifier le code OTP
  verifyOTP: async (email, otp) => {
    try {
      const response = await api.post("/auth/verify-otp", { email, otp });
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: "Code invalide ou expiré" };
    }
  },

  // Réinitialiser le mot de passe avec OTP
  resetPasswordWithOTP: async (email, otp, password) => {
    try {
      const response = await api.post("/auth/reset-password-otp", { email, otp, password });

      // Si la réinitialisation réussit et renvoie un token, le stocker
      if (response.data.token) {
        localStorage.setItem("token", response.data.token);
        localStorage.setItem("user", JSON.stringify(response.data.user));

        // Configurer l'en-tête d'autorisation pour les futures requêtes
        api.defaults.headers.common["Authorization"] = `Bearer ${response.data.token}`;

        console.log("Authentification réussie après réinitialisation du mot de passe");
      }

      return response.data;
    } catch (error) {
      console.error("Erreur lors de la réinitialisation du mot de passe:", error);
      throw error.response?.data || { error: "Erreur lors de la réinitialisation du mot de passe" };
    }
  },
};

export default authService;