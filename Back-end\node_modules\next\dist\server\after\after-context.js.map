{"version": 3, "sources": ["../../../src/server/after/after-context.ts"], "sourcesContent": ["import PromiseQueue from 'next/dist/compiled/p-queue'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { AfterCallback, AfterTask } from './after'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { withExecuteRevalidates } from '../revalidation-utils'\nimport { bindSnapshot } from '../app-render/async-local-storage'\nimport {\n  workUnitAsyncStorage,\n  type WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\n\nexport type AfterContextOpts = {\n  waitUntil: RequestLifecycleOpts['waitUntil'] | undefined\n  onClose: RequestLifecycleOpts['onClose']\n  onTaskError: RequestLifecycleOpts['onAfterTaskError'] | undefined\n}\n\nexport class AfterContext {\n  private waitUntil: RequestLifecycleOpts['waitUntil'] | undefined\n  private onClose: RequestLifecycleOpts['onClose']\n  private onTaskError: RequestLifecycleOpts['onAfterTaskError'] | undefined\n\n  private runCallbacksOnClosePromise: Promise<void> | undefined\n  private callbackQueue: PromiseQueue\n  private workUnitStores = new Set<WorkUnitStore>()\n\n  constructor({ waitUntil, onClose, onTaskError }: AfterContextOpts) {\n    this.waitUntil = waitUntil\n    this.onClose = onClose\n    this.onTaskError = onTaskError\n\n    this.callbackQueue = new PromiseQueue()\n    this.callbackQueue.pause()\n  }\n\n  public after(task: AfterTask): void {\n    if (isThenable(task)) {\n      if (!this.waitUntil) {\n        errorWaitUntilNotAvailable()\n      }\n      this.waitUntil(\n        task.catch((error) => this.reportTaskError('promise', error))\n      )\n    } else if (typeof task === 'function') {\n      // TODO(after): implement tracing\n      this.addCallback(task)\n    } else {\n      throw new Error('`after()`: Argument must be a promise or a function')\n    }\n  }\n\n  private addCallback(callback: AfterCallback) {\n    // if something is wrong, throw synchronously, bubbling up to the `after` callsite.\n    if (!this.waitUntil) {\n      errorWaitUntilNotAvailable()\n    }\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      this.workUnitStores.add(workUnitStore)\n    }\n\n    const afterTaskStore = afterTaskAsyncStorage.getStore()\n\n    // This is used for checking if request APIs can be called inside `after`.\n    // Note that we need to check the phase in which the *topmost* `after` was called (which should be \"action\"),\n    // not the current phase (which might be \"after\" if we're in a nested after).\n    // Otherwise, we might allow `after(() => headers())`, but not `after(() => after(() => headers()))`.\n    const rootTaskSpawnPhase = afterTaskStore\n      ? afterTaskStore.rootTaskSpawnPhase // nested after\n      : workUnitStore?.phase // topmost after\n\n    // this should only happen once.\n    if (!this.runCallbacksOnClosePromise) {\n      this.runCallbacksOnClosePromise = this.runCallbacksOnClose()\n      this.waitUntil(this.runCallbacksOnClosePromise)\n    }\n\n    // Bind the callback to the current execution context (i.e. preserve all currently available ALS-es).\n    // We do this because we want all of these to be equivalent in every regard except timing:\n    //   after(() => x())\n    //   after(x())\n    //   await x()\n    const wrappedCallback = bindSnapshot(async () => {\n      try {\n        await afterTaskAsyncStorage.run({ rootTaskSpawnPhase }, () =>\n          callback()\n        )\n      } catch (error) {\n        this.reportTaskError('function', error)\n      }\n    })\n\n    this.callbackQueue.add(wrappedCallback)\n  }\n\n  private async runCallbacksOnClose() {\n    await new Promise<void>((resolve) => this.onClose!(resolve))\n    return this.runCallbacks()\n  }\n\n  private async runCallbacks(): Promise<void> {\n    if (this.callbackQueue.size === 0) return\n\n    for (const workUnitStore of this.workUnitStores) {\n      workUnitStore.phase = 'after'\n    }\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) {\n      throw new InvariantError('Missing workStore in AfterContext.runCallbacks')\n    }\n\n    return withExecuteRevalidates(workStore, () => {\n      this.callbackQueue.start()\n      return this.callbackQueue.onIdle()\n    })\n  }\n\n  private reportTaskError(taskKind: 'promise' | 'function', error: unknown) {\n    // TODO(after): this is fine for now, but will need better intergration with our error reporting.\n    // TODO(after): should we log this if we have a onTaskError callback?\n    console.error(\n      taskKind === 'promise'\n        ? `A promise passed to \\`after()\\` rejected:`\n        : `An error occurred in a function passed to \\`after()\\`:`,\n      error\n    )\n    if (this.onTaskError) {\n      // this is very defensive, but we really don't want anything to blow up in an error handler\n      try {\n        this.onTaskError?.(error)\n      } catch (handlerError) {\n        console.error(\n          new InvariantError(\n            '`onTaskError` threw while handling an error thrown from an `after` task',\n            {\n              cause: handlerError,\n            }\n          )\n        )\n      }\n    }\n  }\n}\n\nfunction errorWaitUntilNotAvailable(): never {\n  throw new Error(\n    '`after()` will not work correctly, because `waitUntil` is not available in the current environment.'\n  )\n}\n"], "names": ["AfterContext", "constructor", "waitUntil", "onClose", "onTaskError", "workUnitStores", "Set", "callback<PERSON><PERSON><PERSON>", "PromiseQueue", "pause", "after", "task", "isThenable", "errorWaitUntilNotAvailable", "catch", "error", "reportTaskError", "addCallback", "Error", "callback", "workUnitStore", "workUnitAsyncStorage", "getStore", "add", "afterTaskStore", "afterTaskAsyncStorage", "rootTaskSpawnPhase", "phase", "runCallbacksOnClosePromise", "runCallbacksOnClose", "wrappedCallback", "bindSnapshot", "run", "Promise", "resolve", "runCallbacks", "size", "workStore", "workAsyncStorage", "InvariantError", "withExecuteRevalidates", "start", "onIdle", "taskKind", "console", "handlerError", "cause"], "mappings": ";;;;+BAoBaA;;;eAAAA;;;+DApBY;gCAGM;4BACJ;0CACM;mCACM;mCACV;8CAItB;+CAC+B;;;;;;AAQ/B,MAAMA;IASXC,YAAY,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EAAoB,CAAE;aAF3DC,iBAAiB,IAAIC;QAG3B,IAAI,CAACJ,SAAS,GAAGA;QACjB,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,WAAW,GAAGA;QAEnB,IAAI,CAACG,aAAa,GAAG,IAAIC,eAAY;QACrC,IAAI,CAACD,aAAa,CAACE,KAAK;IAC1B;IAEOC,MAAMC,IAAe,EAAQ;QAClC,IAAIC,IAAAA,sBAAU,EAACD,OAAO;YACpB,IAAI,CAAC,IAAI,CAACT,SAAS,EAAE;gBACnBW;YACF;YACA,IAAI,CAACX,SAAS,CACZS,KAAKG,KAAK,CAAC,CAACC,QAAU,IAAI,CAACC,eAAe,CAAC,WAAWD;QAE1D,OAAO,IAAI,OAAOJ,SAAS,YAAY;YACrC,iCAAiC;YACjC,IAAI,CAACM,WAAW,CAACN;QACnB,OAAO;YACL,MAAM,qBAAgE,CAAhE,IAAIO,MAAM,wDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;IACF;IAEQD,YAAYE,QAAuB,EAAE;QAC3C,mFAAmF;QACnF,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;YACnBW;QACF;QAEA,MAAMO,gBAAgBC,kDAAoB,CAACC,QAAQ;QACnD,IAAIF,eAAe;YACjB,IAAI,CAACf,cAAc,CAACkB,GAAG,CAACH;QAC1B;QAEA,MAAMI,iBAAiBC,oDAAqB,CAACH,QAAQ;QAErD,0EAA0E;QAC1E,6GAA6G;QAC7G,6EAA6E;QAC7E,qGAAqG;QACrG,MAAMI,qBAAqBF,iBACvBA,eAAeE,kBAAkB,CAAC,eAAe;WACjDN,iCAAAA,cAAeO,KAAK,CAAC,gBAAgB;;QAEzC,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAACC,0BAA0B,EAAE;YACpC,IAAI,CAACA,0BAA0B,GAAG,IAAI,CAACC,mBAAmB;YAC1D,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAAC0B,0BAA0B;QAChD;QAEA,qGAAqG;QACrG,0FAA0F;QAC1F,qBAAqB;QACrB,eAAe;QACf,cAAc;QACd,MAAME,kBAAkBC,IAAAA,+BAAY,EAAC;YACnC,IAAI;gBACF,MAAMN,oDAAqB,CAACO,GAAG,CAAC;oBAAEN;gBAAmB,GAAG,IACtDP;YAEJ,EAAE,OAAOJ,OAAO;gBACd,IAAI,CAACC,eAAe,CAAC,YAAYD;YACnC;QACF;QAEA,IAAI,CAACR,aAAa,CAACgB,GAAG,CAACO;IACzB;IAEA,MAAcD,sBAAsB;QAClC,MAAM,IAAII,QAAc,CAACC,UAAY,IAAI,CAAC/B,OAAO,CAAE+B;QACnD,OAAO,IAAI,CAACC,YAAY;IAC1B;IAEA,MAAcA,eAA8B;QAC1C,IAAI,IAAI,CAAC5B,aAAa,CAAC6B,IAAI,KAAK,GAAG;QAEnC,KAAK,MAAMhB,iBAAiB,IAAI,CAACf,cAAc,CAAE;YAC/Ce,cAAcO,KAAK,GAAG;QACxB;QAEA,MAAMU,YAAYC,0CAAgB,CAAChB,QAAQ;QAC3C,IAAI,CAACe,WAAW;YACd,MAAM,qBAAoE,CAApE,IAAIE,8BAAc,CAAC,mDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAmE;QAC3E;QAEA,OAAOC,IAAAA,yCAAsB,EAACH,WAAW;YACvC,IAAI,CAAC9B,aAAa,CAACkC,KAAK;YACxB,OAAO,IAAI,CAAClC,aAAa,CAACmC,MAAM;QAClC;IACF;IAEQ1B,gBAAgB2B,QAAgC,EAAE5B,KAAc,EAAE;QACxE,iGAAiG;QACjG,qEAAqE;QACrE6B,QAAQ7B,KAAK,CACX4B,aAAa,YACT,CAAC,yCAAyC,CAAC,GAC3C,CAAC,sDAAsD,CAAC,EAC5D5B;QAEF,IAAI,IAAI,CAACX,WAAW,EAAE;YACpB,2FAA2F;YAC3F,IAAI;gBACF,IAAI,CAACA,WAAW,oBAAhB,IAAI,CAACA,WAAW,MAAhB,IAAI,EAAeW;YACrB,EAAE,OAAO8B,cAAc;gBACrBD,QAAQ7B,KAAK,CACX,qBAKC,CALD,IAAIwB,8BAAc,CAChB,2EACA;oBACEO,OAAOD;gBACT,IAJF,qBAAA;2BAAA;gCAAA;kCAAA;gBAKA;YAEJ;QACF;IACF;AACF;AAEA,SAAShC;IACP,MAAM,qBAEL,CAFK,IAAIK,MACR,wGADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF"}