import React from 'react';
import { Grid, Card, CardMedia, CardActionArea, Typography } from '@mui/material';

const templates = [
  {
    id: 1,
    name: 'Classique',
    thumbnail: '/assets/templates/classic.png',
  },
  {
    id: 2,
    name: 'Moderne',
    thumbnail: '/assets/templates/modern.png',
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    thumbnail: '/assets/templates/minimal.png',
  },
];

const TemplateSelector = ({ onSelect }) => {
  return (
    <Grid container spacing={3}>
      {templates.map((template) => (
        <Grid item xs={12} sm={6} md={4} key={template.id}>
          <Card>
            <CardActionArea onClick={() => onSelect(template.id)}>
              <CardMedia
                component="img"
                height="200"
                image={template.thumbnail}
                alt={template.name}
              />
              <Typography variant="h6" align="center" sx={{ p: 2 }}>
                {template.name}
              </Typography>
            </CardActionArea>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default TemplateSelector;