import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  TextField,
  Button,
  MenuItem,
  Typography,
  Card,
  CardContent,
  FormControlLabel,
  Switch,
  Divider
} from '@mui/material';
import livreurService from '../services/livreurService';

const LivreurForm = ({ livreur, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    telephone: '',
    email: '',
    cin: '',
    adresse: '',
    vehicule: {
      type: 'Voiture',
      marque: '',
      modele: '',
      immatriculation: '',
      couleur: ''
    },
    statut: 'ACTIF',
    disponible: true,
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // Initialiser le formulaire avec les données du livreur si en mode édition
  useEffect(() => {
    if (livreur) {
      setFormData({
        nom: livreur.nom || '',
        prenom: livreur.prenom || '',
        telephone: livreur.telephone || '',
        email: livreur.email || '',
        cin: livreur.cin || '',
        adresse: livreur.adresse || '',
        vehicule: {
          type: livreur.vehicule?.type || 'Voiture',
          marque: livreur.vehicule?.marque || '',
          modele: livreur.vehicule?.modele || '',
          immatriculation: livreur.vehicule?.immatriculation || '',
          couleur: livreur.vehicule?.couleur || ''
        },
        statut: livreur.statut || 'ACTIF',
        disponible: livreur.disponible !== undefined ? livreur.disponible : true,
        notes: livreur.notes || ''
      });
    }
  }, [livreur]);

  // Gérer les changements de champs
  const handleChange = (field, value) => {
    if (field.startsWith('vehicule.')) {
      const vehiculeField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        vehicule: {
          ...prev.vehicule,
          [vehiculeField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Effacer l'erreur pour ce champ
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Valider et soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Valider les données
    const validation = livreurService.validateLivreurData(formData);
    
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Informations personnelles */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Informations personnelles
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Nom *"
                    value={formData.nom}
                    onChange={(e) => handleChange('nom', e.target.value)}
                    error={!!errors.nom}
                    helperText={errors.nom}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Prénom *"
                    value={formData.prenom}
                    onChange={(e) => handleChange('prenom', e.target.value)}
                    error={!!errors.prenom}
                    helperText={errors.prenom}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Téléphone *"
                    value={formData.telephone}
                    onChange={(e) => handleChange('telephone', e.target.value)}
                    error={!!errors.telephone}
                    helperText={errors.telephone}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleChange('email', e.target.value)}
                    error={!!errors.email}
                    helperText={errors.email}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="CIN"
                    value={formData.cin}
                    onChange={(e) => handleChange('cin', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Adresse"
                    multiline
                    rows={2}
                    value={formData.adresse}
                    onChange={(e) => handleChange('adresse', e.target.value)}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Informations véhicule */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Informations véhicule
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    select
                    label="Type de véhicule"
                    value={formData.vehicule.type}
                    onChange={(e) => handleChange('vehicule.type', e.target.value)}
                  >
                    {livreurService.getVehiculeTypes().map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Marque"
                    value={formData.vehicule.marque}
                    onChange={(e) => handleChange('vehicule.marque', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Modèle"
                    value={formData.vehicule.modele}
                    onChange={(e) => handleChange('vehicule.modele', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Immatriculation"
                    value={formData.vehicule.immatriculation}
                    onChange={(e) => handleChange('vehicule.immatriculation', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Couleur"
                    value={formData.vehicule.couleur}
                    onChange={(e) => handleChange('vehicule.couleur', e.target.value)}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Statut et disponibilité */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Statut et disponibilité
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    select
                    label="Statut"
                    value={formData.statut}
                    onChange={(e) => handleChange('statut', e.target.value)}
                  >
                    {livreurService.getStatutOptions().map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.disponible}
                        onChange={(e) => handleChange('disponible', e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Disponible pour les livraisons"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Notes */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Notes et commentaires
              </Typography>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => handleChange('notes', e.target.value)}
                placeholder="Informations supplémentaires sur le livreur..."
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Boutons d'action */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
            <Button
              variant="outlined"
              onClick={onCancel}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
            >
              {loading ? 'Enregistrement...' : (livreur ? 'Modifier' : 'Créer')}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LivreurForm;
