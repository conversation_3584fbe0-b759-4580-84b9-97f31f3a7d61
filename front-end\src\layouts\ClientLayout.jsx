import React from 'react';
import { Box } from '@mui/material';
import ClientSidebar from '../components/ClientSidebar';
import ClientTopBar from '../components/ClientTopBar';
import { useAuth } from '../contexts/AuthContext';

const ClientLayout = ({ children, darkMode, toggleDarkMode }) => {
  const { currentUser } = useAuth();

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: (theme) => theme.palette.background.default
    }}>
      <ClientSidebar darkMode={darkMode} />
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <ClientTopBar
          title="Dashboard Entreprise"
          darkMode={darkMode}
          toggleDarkMode={toggleDarkMode}
          user={currentUser}
        />
        <Box sx={{
          flexGrow: 1,
          p: 3,
          overflow: 'auto',
          backgroundColor: (theme) => theme.palette.background.default
        }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default ClientLayout;
