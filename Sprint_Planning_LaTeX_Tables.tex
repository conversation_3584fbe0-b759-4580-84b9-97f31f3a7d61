\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[french]{babel}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{colortbl}
\usepackage{array}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{multirow}

\geometry{margin=2cm}

% Définition des couleurs
\definecolor{headerblue}{RGB}{52, 152, 219}
\definecolor{lightblue}{RGB}{174, 214, 241}
\definecolor{criticalred}{RGB}{231, 76, 60}
\definecolor{highyellow}{RGB}{241, 196, 15}
\definecolor{mediumgreen}{RGB}{46, 204, 113}
\definecolor{lowgray}{RGB}{149, 165, 166}

\title{\textbf{PLANIFICATION SPRINTS - SYSTÈME GESTION FACTURES}}
\author{Projet de Gestion de Factures et Devis}
\date{\today}

\begin{document}

\maketitle

\section{SPRINT 1 : FONDATIONS ET AUTHENTIFICATION (Jours 1-30)}

\begin{longtable}{|p{1cm}|p{4cm}|p{6cm}|p{2cm}|p{1.5cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & 
\textcolor{white}{\textbf{FONCTIONNALITÉ}} & 
\textcolor{white}{\textbf{DESCRIPTION}} & 
\textcolor{white}{\textbf{PRIORITÉ}} & 
\textcolor{white}{\textbf{DURÉE}} \\
\hline

\rowcolor{lightblue}
S1.1 & Configuration Environnement & Setup MongoDB, Node.js, React.js, structure projet & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 3j \\
\hline

\rowcolor{lightblue}
S1.2 & Base de Données MongoDB & Création collections : users, entreprises, products, invoices, quotes & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 4j \\
\hline

\rowcolor{lightblue}
S1.3 & Système d'Authentification JWT & Login/logout, génération tokens, middleware protection routes & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 5j \\
\hline

\rowcolor{lightblue}
S1.4 & Détection Automatique Rôles & Identification auto : ADMIN, RESPONSABLE, VENDEUR, CLIENT & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 3j \\
\hline

S1.5 & Pages de Connexion & Interface login responsive avec validation & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 3j \\
\hline

S1.6 & Récupération Mot de Passe & Système OTP par email, reset password sécurisé & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 4j \\
\hline

S1.7 & Gestion Sessions Sécurisées & Expiration auto, déconnexion inactivité, protection CSRF & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 3j \\
\hline

S1.8 & Comptes par Défaut & Admin : <EMAIL>, comptes test configurés & 
\cellcolor{mediumgreen}\textbf{MOYENNE} & 2j \\
\hline

S1.9 & Redirections Automatiques & Navigation auto vers dashboards selon rôle & 
\cellcolor{mediumgreen}\textbf{MOYENNE} & 3j \\
\hline

\multicolumn{4}{|r|}{\textbf{TOTAL SPRINT 1 :}} & \textbf{30j} \\
\hline
\end{longtable}

\section{SPRINT 2 : GESTION UTILISATEURS ET ENTREPRISES (Jours 31-60)}

\begin{longtable}{|p{1cm}|p{4cm}|p{6cm}|p{2cm}|p{1.5cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & 
\textcolor{white}{\textbf{FONCTIONNALITÉ}} & 
\textcolor{white}{\textbf{DESCRIPTION}} & 
\textcolor{white}{\textbf{PRIORITÉ}} & 
\textcolor{white}{\textbf{DURÉE}} \\
\hline

\rowcolor{lightblue}
S2.1 & Dashboard Administrateur & Interface admin avec métriques globales, analytics & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 4j \\
\hline

\rowcolor{lightblue}
S2.2 & Gestion Utilisateurs Admin & CRUD complet : responsables, vendeurs, clients & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 5j \\
\hline

\rowcolor{lightblue}
S2.3 & Gestion Entreprises & Création/modification entreprises, infos fiscales & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 4j \\
\hline

\rowcolor{lightblue}
S2.4 & Système d'Abonnements & Création abonnements : test (10min, 1j, 7j), prod (3m, 6m, 1an) & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 5j \\
\hline

S2.5 & Surveillance Abonnements & Countdown temps réel, alertes expiration français & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 4j \\
\hline

S2.6 & Blocage Automatique & Suspension comptes expirés, statuts ACTIF/SUSPENDU/EXPIRÉ & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 3j \\
\hline

S2.7 & Dashboard Responsable & Interface responsable avec métriques entreprise & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 3j \\
\hline

S2.8 & Gestion Équipe Responsable & CRUD vendeurs, assignation clients, permissions & 
\cellcolor{mediumgreen}\textbf{MOYENNE} & 2j \\
\hline

\multicolumn{4}{|r|}{\textbf{TOTAL SPRINT 2 :}} & \textbf{30j} \\
\hline
\end{longtable}

\section{SPRINT 3 : DOCUMENTS COMMERCIAUX ET FONCTIONNALITÉS AVANCÉES (Jours 61-90)}

\begin{longtable}{|p{1cm}|p{4cm}|p{6cm}|p{2cm}|p{1.5cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & 
\textcolor{white}{\textbf{FONCTIONNALITÉ}} & 
\textcolor{white}{\textbf{DESCRIPTION}} & 
\textcolor{white}{\textbf{PRIORITÉ}} & 
\textcolor{white}{\textbf{DURÉE}} \\
\hline

\rowcolor{lightblue}
S3.1 & Gestion Produits/Services & CRUD catalogue, prix, descriptions, catégories & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 4j \\
\hline

\rowcolor{lightblue}
S3.2 & Système Templates & Templates base (Standard/Moderne), personnalisation logo/couleurs & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 5j \\
\hline

\rowcolor{lightblue}
S3.3 & Création Devis & Interface création, statuts BROUILLON/ENVOYÉ, validation responsable & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 5j \\
\hline

\rowcolor{lightblue}
S3.4 & Création Factures & Interface création, numérotation auto, calculs TVA & 
\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 4j \\
\hline

S3.5 & Génération PDF & Export PDF personnalisé, templates avec branding & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 4j \\
\hline

S3.6 & Conversion Devis→Facture & Workflow automatique après acceptation client & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 3j \\
\hline

S3.7 & Interface Client & Dashboard client, consultation documents, acceptation devis & 
\cellcolor{highyellow}\textbf{ÉLEVÉE} & 3j \\
\hline

S3.8 & Gestion Paiements & Enregistrement paiements, modes (chèque, virement, espèces) & 
\cellcolor{mediumgreen}\textbf{MOYENNE} & 2j \\
\hline

\multicolumn{4}{|r|}{\textbf{TOTAL SPRINT 3 :}} & \textbf{30j} \\
\hline
\end{longtable}

\section{RÉSUMÉ GLOBAL DES SPRINTS}

\begin{table}[h!]
\centering
\begin{tabular}{|p{3cm}|p{2cm}|p{4cm}|p{4cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{SPRINT}} & 
\textcolor{white}{\textbf{DURÉE}} & 
\textcolor{white}{\textbf{OBJECTIF PRINCIPAL}} & 
\textcolor{white}{\textbf{LIVRABLES CLÉS}} \\
\hline

\rowcolor{lightblue}
\textbf{Sprint 1} & 30 jours & Fondations et Authentification & 
Système auth complet, Base données, Protection routes \\
\hline

\textbf{Sprint 2} & 30 jours & Gestion Utilisateurs et Entreprises & 
Interface admin, Abonnements, Dashboard responsable \\
\hline

\rowcolor{lightblue}
\textbf{Sprint 3} & 30 jours & Documents Commerciaux & 
Devis/Factures, Templates PDF, Interface client \\
\hline

\multicolumn{2}{|r|}{\textbf{TOTAL PROJET :}} & \multicolumn{2}{|l|}{\textbf{90 JOURS}} \\
\hline
\end{tabular}
\end{table}

\section{RÉPARTITION DES PRIORITÉS}

\begin{table}[h!]
\centering
\begin{tabular}{|p{2cm}|p{2cm}|p{2cm}|p{2cm}|p{2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{PRIORITÉ}} & 
\textcolor{white}{\textbf{SPRINT 1}} & 
\textcolor{white}{\textbf{SPRINT 2}} & 
\textcolor{white}{\textbf{SPRINT 3}} & 
\textcolor{white}{\textbf{TOTAL}} \\
\hline

\cellcolor{criticalred}\textcolor{white}{\textbf{CRITIQUE}} & 15j & 18j & 18j & 51j \\
\hline

\cellcolor{highyellow}\textbf{ÉLEVÉE} & 10j & 10j & 10j & 30j \\
\hline

\cellcolor{mediumgreen}\textbf{MOYENNE} & 5j & 2j & 2j & 9j \\
\hline

\textbf{TOTAL} & \textbf{30j} & \textbf{30j} & \textbf{30j} & \textbf{90j} \\
\hline
\end{tabular}
\end{table}

\end{document}
