{"version": 3, "sources": ["../../../src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the cookies object.\n        return makeDynamicallyTrackedExoticCookies(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how cookies has worked in PPR without dynamicIO.\n        postponeWithTracking(\n          workStore.route,\n          callingExpression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We track dynamic access here so we don't need to wrap the cookies in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration(\n          callingExpression,\n          workStore,\n          workUnitStore\n        )\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using cookies inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  // cookies is being called in a dynamic context\n\n  const requestStore = getExpectedRequestStore(callingExpression)\n\n  let underlyingCookies: ReadonlyRequestCookies\n\n  if (areCookiesMutableInCurrentPhase(requestStore)) {\n    // We can't conditionally return different types here based on the context.\n    // To avoid confusion, we always return the readonly type here.\n    underlyingCookies =\n      requestStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n  } else {\n    underlyingCookies = requestStore.cookies\n  }\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticCookiesWithDevWarnings(\n      underlyingCookies,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticCookies(underlyingCookies)\n  }\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeDynamicallyTrackedExoticCookies(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`cookies()[Symbol.iterator]()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    size: {\n      get() {\n        const expression = '`cookies().size`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = new Promise<ReadonlyRequestCookies>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingCookies))\n  )\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n"], "names": ["areCookiesMutableInCurrentPhase", "RequestCookiesAdapter", "RequestCookies", "workAsyncStorage", "workUnitAsyncStorage", "postponeWithTracking", "abortAndThrowOnSynchronousRequestDataAccess", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "trackSynchronousRequestDataAccessInDev", "getExpectedRequestStore", "StaticGenBailoutError", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "scheduleImmediate", "isRequestAPICallableInsideAfter", "cookies", "callingExpression", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "type", "dynamicShouldError", "makeDynamicallyTrackedExoticCookies", "dynamicTracking", "requestStore", "userspaceMutableCookies", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticCookiesWithDevWarnings", "seal", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "renderSignal", "set", "Object", "defineProperties", "Symbol", "iterator", "value", "expression", "error", "createCookiesAccessError", "size", "arguments", "length", "describeNameArg", "getAll", "has", "arg", "delete", "clear", "toString", "cachedCookies", "Promise", "resolve", "bind", "polyfilledResponseCookiesIterator", "polyfilledResponseCookiesClear", "syncIODev", "apply", "call", "writable", "name", "prerenderPhase", "warnForSyncAccess", "prefix", "map", "c", "values", "returnable", "cookie"], "mappings": "AAAA,SAGEA,+BAA+B,EAC/BC,qBAAqB,QAChB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,oBAAoB,EACpBC,2CAA2C,EAC3CC,gCAAgC,EAChCC,+BAA+B,EAC/BC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,uBAAuB,QAAQ,iDAAgD;AACxF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SAASC,+BAA+B,QAAQ,UAAS;AAyBzD,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYf,iBAAiBgB,QAAQ;IAC3C,MAAMC,gBAAgBhB,qBAAqBe,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACN,mCACD;YACA,MAAM,qBAGL,CAHK,IAAIO,MACR,wDAAwD;YACxD,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,yOAAyO,CAAC,GAF/P,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,2BAA2BF;QACpC;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcQ,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIH,cAAcQ,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIL,UAAUW,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIlB,sBACR,CAAC,MAAM,EAAEO,UAAUK,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,IAAIA,cAAcQ,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOE,oCACLZ,UAAUK,KAAK,EACfH;YAEJ,OAAO,IAAIA,cAAcQ,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3EvB,qBACEa,UAAUK,KAAK,EACfN,mBACAG,cAAcW,eAAe;YAEjC,OAAO,IAAIX,cAAcQ,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,uEAAuE;gBACvE,uCAAuC;gBACvCrB,iCACEU,mBACAC,WACAE;YAEJ;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFZ,gCAAgCU,WAAWE;IAC7C;IAEA,+CAA+C;IAE/C,MAAMY,eAAetB,wBAAwBO;IAE7C,IAAIQ;IAEJ,IAAIzB,gCAAgCgC,eAAe;QACjD,2EAA2E;QAC3E,+DAA+D;QAC/DP,oBACEO,aAAaC,uBAAuB;IACxC,OAAO;QACLR,oBAAoBO,aAAahB,OAAO;IAC1C;IAEA,IAAIkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAAClB,6BAAAA,UAAWmB,iBAAiB,GAAE;QAC3E,OAAOC,0CACLb,mBACAP,6BAAAA,UAAWK,KAAK;IAEpB,OAAO;QACL,OAAOI,2BAA2BF;IACpC;AACF;AAEA,SAASC;IACP,OAAOzB,sBAAsBsC,IAAI,CAAC,IAAIrC,eAAe,IAAIsC,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASZ,oCACPP,KAAa,EACboB,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUlC,mBACd+B,eAAeI,YAAY,EAC3B;IAEFN,cAAcO,GAAG,CAACL,gBAAgBG;IAElCG,OAAOC,gBAAgB,CAACJ,SAAS;QAC/B,CAACK,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMC,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAc,MAAM;YACJZ;gBACE,MAAMS,aAAa;gBACnB,MAAMC,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAE,KAAK;YACHQ,OAAO,SAASR;gBACd,IAAIS;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMH,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAkB,QAAQ;YACNR,OAAO,SAASQ;gBACd,IAAIP;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACA,MAAMH,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAmB,KAAK;YACHT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMH,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAK,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACL,MAAMS,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPT,aAAa,CAAC,gBAAgB,EAAEM,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLT,aAAa;oBACf;gBACF;gBACA,MAAMC,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAqB,QAAQ;YACNX,OAAO;gBACL,IAAIC;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO,IAAII,UAAUC,MAAM,KAAK,GAAG;oBACjCL,aAAa,CAAC,mBAAmB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLJ,aAAa,CAAC,mBAAmB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACA,MAAMH,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAsB,OAAO;YACLZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnB,MAAMC,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;QACAuB,UAAU;YACRb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMC,QAAQC,yBAAyBjC,OAAO+B;gBAC9ChD,4CACEiB,OACA+B,YACAC,OACAZ;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAASnB,2BACPF,iBAAyC;IAEzC,MAAM0C,gBAAgB1B,cAAcI,GAAG,CAACpB;IACxC,IAAI0C,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMrB,UAAUsB,QAAQC,OAAO,CAAC5C;IAChCgB,cAAcO,GAAG,CAACvB,mBAAmBqB;IAErCG,OAAOC,gBAAgB,CAACJ,SAAS;QAC/B,CAACK,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO5B,iBAAiB,CAAC0B,OAAOC,QAAQ,CAAC,GACrC3B,iBAAiB,CAAC0B,OAAOC,QAAQ,CAAC,CAACkB,IAAI,CAAC7C,qBAExC,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjE8C,kCAAkCD,IAAI,CAAC7C;QAC7C;QACAgC,MAAM;YACJZ;gBACE,OAAOpB,kBAAkBgC,IAAI;YAC/B;QACF;QACAZ,KAAK;YACHQ,OAAO5B,kBAAkBoB,GAAG,CAACyB,IAAI,CAAC7C;QACpC;QACAoC,QAAQ;YACNR,OAAO5B,kBAAkBoC,MAAM,CAACS,IAAI,CAAC7C;QACvC;QACAqC,KAAK;YACHT,OAAO5B,kBAAkBqC,GAAG,CAACQ,IAAI,CAAC7C;QACpC;QACAuB,KAAK;YACHK,OAAO5B,kBAAkBuB,GAAG,CAACsB,IAAI,CAAC7C;QACpC;QACAuC,QAAQ;YACNX,OAAO5B,kBAAkBuC,MAAM,CAACM,IAAI,CAAC7C;QACvC;QACAwC,OAAO;YACLZ,OACE,yFAAyF;YACzF,OAAO5B,kBAAkBwC,KAAK,KAAK,aAE/BxC,kBAAkBwC,KAAK,CAACK,IAAI,CAAC7C,qBAE7B,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjE+C,+BAA+BF,IAAI,CAAC7C,mBAAmBqB;QAC/D;QACAoB,UAAU;YACRb,OAAO5B,kBAAkByC,QAAQ,CAACI,IAAI,CAAC7C;QACzC;IACF;IAEA,OAAOqB;AACT;AAEA,SAASR,0CACPb,iBAAyC,EACzCF,KAAc;IAEd,MAAM4C,gBAAgB1B,cAAcI,GAAG,CAACpB;IACxC,IAAI0C,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMrB,UAAU,IAAIsB,QAAgC,CAACC,UACnDvD,kBAAkB,IAAMuD,QAAQ5C;IAElCgB,cAAcO,GAAG,CAACvB,mBAAmBqB;IAErCG,OAAOC,gBAAgB,CAACJ,SAAS;QAC/B,CAACK,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnBmB,UAAUlD,OAAO+B;gBACjB,OAAO7B,iBAAiB,CAAC0B,OAAOC,QAAQ,CAAC,GACrC3B,iBAAiB,CAAC0B,OAAOC,QAAQ,CAAC,CAACsB,KAAK,CACtCjD,mBACAiC,aAGF,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEa,kCAAkCI,IAAI,CAAClD;YAC7C;YACAmD,UAAU;QACZ;QACAnB,MAAM;YACJZ;gBACE,MAAMS,aAAa;gBACnBmB,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkBgC,IAAI;YAC/B;QACF;QACAZ,KAAK;YACHQ,OAAO,SAASR;gBACd,IAAIS;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAe,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkBoB,GAAG,CAAC6B,KAAK,CAACjD,mBAAmBiC;YACxD;YACAkB,UAAU;QACZ;QACAf,QAAQ;YACNR,OAAO,SAASQ;gBACd,IAAIP;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACAe,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkBoC,MAAM,CAACa,KAAK,CACnCjD,mBACAiC;YAEJ;YACAkB,UAAU;QACZ;QACAd,KAAK;YACHT,OAAO,SAASR;gBACd,IAAIS;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAe,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkBqC,GAAG,CAACY,KAAK,CAACjD,mBAAmBiC;YACxD;YACAkB,UAAU;QACZ;QACA5B,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO;oBACL,MAAMS,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPT,aAAa,CAAC,gBAAgB,EAAEM,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLT,aAAa;oBACf;gBACF;gBACAmB,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkBuB,GAAG,CAAC0B,KAAK,CAACjD,mBAAmBiC;YACxD;YACAkB,UAAU;QACZ;QACAZ,QAAQ;YACNX,OAAO;gBACL,IAAIC;gBACJ,IAAII,UAAUC,MAAM,KAAK,GAAG;oBAC1BL,aAAa;gBACf,OAAO,IAAII,UAAUC,MAAM,KAAK,GAAG;oBACjCL,aAAa,CAAC,mBAAmB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLJ,aAAa,CAAC,mBAAmB,EAAEM,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACAe,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkBuC,MAAM,CAACU,KAAK,CACnCjD,mBACAiC;YAEJ;YACAkB,UAAU;QACZ;QACAX,OAAO;YACLZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnBmB,UAAUlD,OAAO+B;gBACjB,mFAAmF;gBACnF,OAAO,OAAO7B,kBAAkBwC,KAAK,KAAK,aAEtCxC,kBAAkBwC,KAAK,CAACS,KAAK,CAACjD,mBAAmBiC,aAEjD,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEc,+BAA+BG,IAAI,CAAClD,mBAAmBqB;YAC7D;YACA8B,UAAU;QACZ;QACAV,UAAU;YACRb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBmB,UAAUlD,OAAO+B;gBACjB,OAAO7B,kBAAkByC,QAAQ,CAACQ,KAAK,CACrCjD,mBACAiC;YAEJ;YACAkB,UAAU;QACZ;IACF;IAEA,OAAO9B;AACT;AAEA,SAASc,gBAAgBG,GAAY;IACnC,OAAO,OAAOA,QAAQ,YACpBA,QAAQ,QACR,OAAO,AAACA,IAAYc,IAAI,KAAK,WAC3B,CAAC,CAAC,EAAE,AAACd,IAAYc,IAAI,CAAC,CAAC,CAAC,GACxB,OAAOd,QAAQ,WACb,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GACV;AACR;AAEA,SAASU,UAAUlD,KAAyB,EAAE+B,UAAkB;IAC9D,MAAMlC,gBAAgBhB,qBAAqBe,QAAQ;IACnD,IACEC,iBACAA,cAAcQ,IAAI,KAAK,aACvBR,cAAc0D,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAM9C,eAAeZ;QACrBX,uCAAuCuB;IACzC;IACA,gCAAgC;IAChC+C,kBAAkBxD,OAAO+B;AAC3B;AAEA,MAAMyB,oBAAoBlE,4CACxB2C;AAGF,SAASA,yBACPjC,KAAyB,EACzB+B,UAAkB;IAElB,MAAM0B,SAASzD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAG0D,OAAO,KAAK,EAAE1B,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASiB;IAGP,OAAO,IAAI,CAACV,MAAM,GACfoB,GAAG,CAAC,CAACC,IAAM;YAACA,EAAEL,IAAI;YAAEK;SAAE,EACtBC,MAAM;AACX;AAEA,SAASX,+BAEPY,UAA2C;IAE3C,KAAK,MAAMC,UAAU,IAAI,CAACxB,MAAM,GAAI;QAClC,IAAI,CAACG,MAAM,CAACqB,OAAOR,IAAI;IACzB;IACA,OAAOO;AACT"}