{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/overlay/body-locker.ts"], "sourcesContent": ["let previousBodyPaddingRight: string | undefined\nlet previousBodyOverflowSetting: string | undefined\n\nlet activeLocks = 0\n\nexport function lock() {\n  setTimeout(() => {\n    if (activeLocks++ > 0) {\n      return\n    }\n\n    const scrollBarGap =\n      window.innerWidth - document.documentElement.clientWidth\n\n    if (scrollBarGap > 0) {\n      previousBodyPaddingRight = document.body.style.paddingRight\n      document.body.style.paddingRight = `${scrollBarGap}px`\n    }\n\n    previousBodyOverflowSetting = document.body.style.overflow\n    document.body.style.overflow = 'hidden'\n  })\n}\n\nexport function unlock() {\n  setTimeout(() => {\n    if (activeLocks === 0 || --activeLocks !== 0) {\n      return\n    }\n\n    if (previousBodyPaddingRight !== undefined) {\n      document.body.style.paddingRight = previousBodyPaddingRight\n      previousBodyPaddingRight = undefined\n    }\n\n    if (previousBodyOverflowSetting !== undefined) {\n      document.body.style.overflow = previousBodyOverflowSetting\n      previousBodyOverflowSetting = undefined\n    }\n  })\n}\n"], "names": ["lock", "unlock", "previousBodyPaddingRight", "previousBodyOverflowSetting", "activeLocks", "setTimeout", "scrollBarGap", "window", "innerWidth", "document", "documentElement", "clientWidth", "body", "style", "paddingRight", "overflow", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAKgBA,IAAI;eAAJA;;IAmBAC,MAAM;eAANA;;;AAxBhB,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,cAAc;AAEX,SAASJ;IACdK,WAAW;QACT,IAAID,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAME,eACJC,OAAOC,UAAU,GAAGC,SAASC,eAAe,CAACC,WAAW;QAE1D,IAAIL,eAAe,GAAG;YACpBJ,2BAA2BO,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY;YAC3DL,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAG,AAAC,KAAER,eAAa;QACrD;QAEAH,8BAA8BM,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ;QAC1DN,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG;IACjC;AACF;AAEO,SAASd;IACdI,WAAW;QACT,IAAID,gBAAgB,KAAK,EAAEA,gBAAgB,GAAG;YAC5C;QACF;QAEA,IAAIF,6BAA6Bc,WAAW;YAC1CP,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAGZ;YACnCA,2BAA2Bc;QAC7B;QAEA,IAAIb,gCAAgCa,WAAW;YAC7CP,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAGZ;YAC/BA,8BAA8Ba;QAChC;IACF;AACF"}