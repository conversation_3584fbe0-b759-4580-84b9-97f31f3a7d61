{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "sourcesContent": ["import type {\n  AlternateLinkDescriptor,\n  ResolvedAlternateURLs,\n} from '../types/alternative-urls-types'\nimport type {\n  Metadata,\n  ResolvedMetadata,\n  Viewport,\n} from '../types/metadata-interface'\nimport type { ResolvedVerification } from '../types/metadata-types'\nimport type {\n  FieldResolver,\n  FieldResolverExtraArgs,\n  MetadataContext,\n} from '../types/resolvers'\nimport { resolveAsArrayOrUndefined } from '../generate/utils'\nimport { resolveAbsoluteUrlWithPathname } from './resolve-url'\n\nfunction resolveAlternateUrl(\n  url: string | URL,\n  metadataBase: URL | null,\n  metadataContext: MetadataContext\n) {\n  // If alter native url is an URL instance,\n  // we treat it as a URL base and resolve with current pathname\n  if (url instanceof URL) {\n    const newUrl = new URL(metadataContext.pathname, url)\n    url.searchParams.forEach((value, key) =>\n      newUrl.searchParams.set(key, value)\n    )\n    url = newUrl\n  }\n  return resolveAbsoluteUrlWithPathname(url, metadataBase, metadataContext)\n}\n\nexport const resolveThemeColor: FieldResolver<'themeColor', Viewport> = (\n  themeColor\n) => {\n  if (!themeColor) return null\n  const themeColorDescriptors: Viewport['themeColor'] = []\n\n  resolveAsArrayOrUndefined(themeColor)?.forEach((descriptor) => {\n    if (typeof descriptor === 'string')\n      themeColorDescriptors.push({ color: descriptor })\n    else if (typeof descriptor === 'object')\n      themeColorDescriptors.push({\n        color: descriptor.color,\n        media: descriptor.media,\n      })\n  })\n\n  return themeColorDescriptors\n}\n\nfunction resolveUrlValuesOfObject(\n  obj:\n    | Record<\n        string,\n        string | URL | AlternateLinkDescriptor[] | null | undefined\n      >\n    | null\n    | undefined,\n  metadataBase: ResolvedMetadata['metadataBase'],\n  metadataContext: MetadataContext\n): null | Record<string, AlternateLinkDescriptor[]> {\n  if (!obj) return null\n\n  const result: Record<string, AlternateLinkDescriptor[]> = {}\n  for (const [key, value] of Object.entries(obj)) {\n    if (typeof value === 'string' || value instanceof URL) {\n      result[key] = [\n        {\n          url: resolveAlternateUrl(value, metadataBase, metadataContext),\n        },\n      ]\n    } else {\n      result[key] = []\n      value?.forEach((item, index) => {\n        const url = resolveAlternateUrl(item.url, metadataBase, metadataContext)\n        result[key][index] = {\n          url,\n          title: item.title,\n        }\n      })\n    }\n  }\n  return result\n}\n\nfunction resolveCanonicalUrl(\n  urlOrDescriptor: string | URL | null | AlternateLinkDescriptor | undefined,\n  metadataBase: URL | null,\n  metadataContext: MetadataContext\n): null | AlternateLinkDescriptor {\n  if (!urlOrDescriptor) return null\n\n  const url =\n    typeof urlOrDescriptor === 'string' || urlOrDescriptor instanceof URL\n      ? urlOrDescriptor\n      : urlOrDescriptor.url\n\n  // Return string url because structureClone can't handle URL instance\n  return {\n    url: resolveAlternateUrl(url, metadataBase, metadataContext),\n  }\n}\n\nexport const resolveAlternates: FieldResolverExtraArgs<\n  'alternates',\n  [ResolvedMetadata['metadataBase'], MetadataContext]\n> = (alternates, metadataBase, context) => {\n  if (!alternates) return null\n\n  const canonical = resolveCanonicalUrl(\n    alternates.canonical,\n    metadataBase,\n    context\n  )\n  const languages = resolveUrlValuesOfObject(\n    alternates.languages,\n    metadataBase,\n    context\n  )\n  const media = resolveUrlValuesOfObject(\n    alternates.media,\n    metadataBase,\n    context\n  )\n  const types = resolveUrlValuesOfObject(\n    alternates.types,\n    metadataBase,\n    context\n  )\n\n  const result: ResolvedAlternateURLs = {\n    canonical,\n    languages,\n    media,\n    types,\n  }\n\n  return result\n}\n\nconst robotsKeys = [\n  'noarchive',\n  'nosnippet',\n  'noimageindex',\n  'nocache',\n  'notranslate',\n  'indexifembedded',\n  'nositelinkssearchbox',\n  'unavailable_after',\n  'max-video-preview',\n  'max-image-preview',\n  'max-snippet',\n] as const\nconst resolveRobotsValue: (robots: Metadata['robots']) => string | null = (\n  robots\n) => {\n  if (!robots) return null\n  if (typeof robots === 'string') return robots\n\n  const values: string[] = []\n\n  if (robots.index) values.push('index')\n  else if (typeof robots.index === 'boolean') values.push('noindex')\n\n  if (robots.follow) values.push('follow')\n  else if (typeof robots.follow === 'boolean') values.push('nofollow')\n\n  for (const key of robotsKeys) {\n    const value = robots[key]\n    if (typeof value !== 'undefined' && value !== false) {\n      values.push(typeof value === 'boolean' ? key : `${key}:${value}`)\n    }\n  }\n\n  return values.join(', ')\n}\n\nexport const resolveRobots: FieldResolver<'robots'> = (robots) => {\n  if (!robots) return null\n  return {\n    basic: resolveRobotsValue(robots),\n    googleBot:\n      typeof robots !== 'string' ? resolveRobotsValue(robots.googleBot) : null,\n  }\n}\n\nconst VerificationKeys = ['google', 'yahoo', 'yandex', 'me', 'other'] as const\nexport const resolveVerification: FieldResolver<'verification'> = (\n  verification\n) => {\n  if (!verification) return null\n  const res: ResolvedVerification = {}\n\n  for (const key of VerificationKeys) {\n    const value = verification[key]\n    if (value) {\n      if (key === 'other') {\n        res.other = {}\n        for (const otherKey in verification.other) {\n          const otherValue = resolveAsArrayOrUndefined(\n            verification.other[otherKey]\n          )\n          if (otherValue) res.other[otherKey] = otherValue\n        }\n      } else res[key] = resolveAsArrayOrUndefined(value) as (string | number)[]\n    }\n  }\n  return res\n}\n\nexport const resolveAppleWebApp: FieldResolver<'appleWebApp'> = (appWebApp) => {\n  if (!appWebApp) return null\n  if (appWebApp === true) {\n    return {\n      capable: true,\n    }\n  }\n\n  const startupImages = appWebApp.startupImage\n    ? resolveAsArrayOrUndefined(appWebApp.startupImage)?.map((item) =>\n        typeof item === 'string' ? { url: item } : item\n      )\n    : null\n\n  return {\n    capable: 'capable' in appWebApp ? !!appWebApp.capable : true,\n    title: appWebApp.title || null,\n    startupImage: startupImages,\n    statusBarStyle: appWebApp.statusBarStyle || 'default',\n  }\n}\n\nexport const resolveAppLinks: FieldResolver<'appLinks'> = (appLinks) => {\n  if (!appLinks) return null\n  for (const key in appLinks) {\n    // @ts-ignore // TODO: type infer\n    appLinks[key] = resolveAsArrayOrUndefined(appLinks[key])\n  }\n  return appLinks as ResolvedMetadata['appLinks']\n}\n\nexport const resolveItunes: FieldResolverExtraArgs<\n  'itunes',\n  [ResolvedMetadata['metadataBase'], MetadataContext]\n> = (itunes, metadataBase, context) => {\n  if (!itunes) return null\n  return {\n    appId: itunes.appId,\n    appArgument: itunes.appArgument\n      ? resolveAlternateUrl(itunes.appArgument, metadataBase, context)\n      : undefined,\n  }\n}\n\nexport const resolveFacebook: FieldResolver<'facebook'> = (facebook) => {\n  if (!facebook) return null\n  return {\n    appId: facebook.appId,\n    admins: resolveAsArrayOrUndefined(facebook.admins),\n  }\n}\n\nexport const resolvePagination: FieldResolverExtraArgs<\n  'pagination',\n  [ResolvedMetadata['metadataBase'], MetadataContext]\n> = (pagination, metadataBase, context) => {\n  return {\n    previous: pagination?.previous\n      ? resolveAlternateUrl(pagination.previous, metadataBase, context)\n      : null,\n    next: pagination?.next\n      ? resolveAlternateUrl(pagination.next, metadataBase, context)\n      : null,\n  }\n}\n"], "names": ["resolveAlternates", "resolveAppLinks", "resolveAppleWebApp", "resolveFacebook", "resolveItunes", "resolvePagination", "resolveRobots", "resolveThemeColor", "resolveVerification", "resolveAlternateUrl", "url", "metadataBase", "metadataContext", "URL", "newUrl", "pathname", "searchParams", "for<PERSON>ach", "value", "key", "set", "resolveAbsoluteUrlWithPathname", "themeColor", "resolveAsArrayOrUndefined", "themeColorDescriptors", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "alternates", "context", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "basic", "googleBot", "VerificationKeys", "verification", "res", "other", "otherKey", "otherValue", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "appLinks", "itunes", "appId", "appArgument", "undefined", "facebook", "admins", "pagination", "previous", "next"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IA2GaA,iBAAiB;eAAjBA;;IAiIAC,eAAe;eAAfA;;IAtBAC,kBAAkB;eAAlBA;;IA4CAC,eAAe;eAAfA;;IAbAC,aAAa;eAAbA;;IAqBAC,iBAAiB;eAAjBA;;IArFAC,aAAa;eAAbA;;IAlJAC,iBAAiB;eAAjBA;;IA4JAC,mBAAmB;eAAnBA;;;uBAhL6B;4BACK;AAE/C,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,eAAgC;IAEhC,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtB,MAAMC,SAAS,IAAID,IAAID,gBAAgBG,QAAQ,EAAEL;QACjDA,IAAIM,YAAY,CAACC,OAAO,CAAC,CAACC,OAAOC,MAC/BL,OAAOE,YAAY,CAACI,GAAG,CAACD,KAAKD;QAE/BR,MAAMI;IACR;IACA,OAAOO,IAAAA,0CAA8B,EAACX,KAAKC,cAAcC;AAC3D;AAEO,MAAML,oBAA2D,CACtEe;QAKAC;IAHA,IAAI,CAACD,YAAY,OAAO;IACxB,MAAME,wBAAgD,EAAE;KAExDD,6BAAAA,IAAAA,gCAAyB,EAACD,gCAA1BC,2BAAuCN,OAAO,CAAC,CAACQ;QAC9C,IAAI,OAAOA,eAAe,UACxBD,sBAAsBE,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BD,sBAAsBE,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOJ;AACT;AAEA,SAASK,yBACPC,GAMa,EACbnB,YAA8C,EAC9CC,eAAgC;IAEhC,IAAI,CAACkB,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACZ,KAAKD,MAAM,IAAIc,OAAOC,OAAO,CAACH,KAAM;QAC9C,IAAI,OAAOZ,UAAU,YAAYA,iBAAiBL,KAAK;YACrDkB,MAAM,CAACZ,IAAI,GAAG;gBACZ;oBACET,KAAKD,oBAAoBS,OAAOP,cAAcC;gBAChD;aACD;QACH,OAAO;YACLmB,MAAM,CAACZ,IAAI,GAAG,EAAE;YAChBD,yBAAAA,MAAOD,OAAO,CAAC,CAACiB,MAAMC;gBACpB,MAAMzB,MAAMD,oBAAoByB,KAAKxB,GAAG,EAAEC,cAAcC;gBACxDmB,MAAM,CAACZ,IAAI,CAACgB,MAAM,GAAG;oBACnBzB;oBACA0B,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOL;AACT;AAEA,SAASM,oBACPC,eAA0E,EAC1E3B,YAAwB,EACxBC,eAAgC;IAEhC,IAAI,CAAC0B,iBAAiB,OAAO;IAE7B,MAAM5B,MACJ,OAAO4B,oBAAoB,YAAYA,2BAA2BzB,MAC9DyB,kBACAA,gBAAgB5B,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEO,MAAMZ,oBAGT,CAACuC,YAAY5B,cAAc6B;IAC7B,IAAI,CAACD,YAAY,OAAO;IAExB,MAAME,YAAYJ,oBAChBE,WAAWE,SAAS,EACpB9B,cACA6B;IAEF,MAAME,YAAYb,yBAChBU,WAAWG,SAAS,EACpB/B,cACA6B;IAEF,MAAMZ,QAAQC,yBACZU,WAAWX,KAAK,EAChBjB,cACA6B;IAEF,MAAMG,QAAQd,yBACZU,WAAWI,KAAK,EAChBhC,cACA6B;IAGF,MAAMT,SAAgC;QACpCU;QACAC;QACAd;QACAe;IACF;IAEA,OAAOZ;AACT;AAEA,MAAMa,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOX,KAAK,EAAEY,OAAOrB,IAAI,CAAC;SACzB,IAAI,OAAOoB,OAAOX,KAAK,KAAK,WAAWY,OAAOrB,IAAI,CAAC;IAExD,IAAIoB,OAAOE,MAAM,EAAED,OAAOrB,IAAI,CAAC;SAC1B,IAAI,OAAOoB,OAAOE,MAAM,KAAK,WAAWD,OAAOrB,IAAI,CAAC;IAEzD,KAAK,MAAMP,OAAOyB,WAAY;QAC5B,MAAM1B,QAAQ4B,MAAM,CAAC3B,IAAI;QACzB,IAAI,OAAOD,UAAU,eAAeA,UAAU,OAAO;YACnD6B,OAAOrB,IAAI,CAAC,OAAOR,UAAU,YAAYC,MAAM,GAAGA,IAAI,CAAC,EAAED,OAAO;QAClE;IACF;IAEA,OAAO6B,OAAOE,IAAI,CAAC;AACrB;AAEO,MAAM3C,gBAAyC,CAACwC;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLI,OAAOL,mBAAmBC;QAC1BK,WACE,OAAOL,WAAW,WAAWD,mBAAmBC,OAAOK,SAAS,IAAI;IACxE;AACF;AAEA,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AAC9D,MAAM5C,sBAAqD,CAChE6C;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMnC,OAAOiC,iBAAkB;QAClC,MAAMlC,QAAQmC,YAAY,CAAClC,IAAI;QAC/B,IAAID,OAAO;YACT,IAAIC,QAAQ,SAAS;gBACnBmC,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAalC,IAAAA,gCAAyB,EAC1C8B,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACnC,IAAI,GAAGI,IAAAA,gCAAyB,EAACL;QAC9C;IACF;IACA,OAAOoC;AACT;AAEO,MAAMpD,qBAAmD,CAACwD;QAS3DnC;IARJ,IAAI,CAACmC,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCtC,6BAAAA,IAAAA,gCAAyB,EAACmC,UAAUG,YAAY,sBAAhDtC,2BAAmDuC,GAAG,CAAC,CAAC5B,OACtD,OAAOA,SAAS,WAAW;YAAExB,KAAKwB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACLyB,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxDvB,OAAOsB,UAAUtB,KAAK,IAAI;QAC1ByB,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF;AAEO,MAAM9D,kBAA6C,CAAC+D;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAM7C,OAAO6C,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAAC7C,IAAI,GAAGI,IAAAA,gCAAyB,EAACyC,QAAQ,CAAC7C,IAAI;IACzD;IACA,OAAO6C;AACT;AAEO,MAAM5D,gBAGT,CAAC6D,QAAQtD,cAAc6B;IACzB,IAAI,CAACyB,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B1D,oBAAoBwD,OAAOE,WAAW,EAAExD,cAAc6B,WACtD4B;IACN;AACF;AAEO,MAAMjE,kBAA6C,CAACkE;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,OAAO;QACLH,OAAOG,SAASH,KAAK;QACrBI,QAAQ/C,IAAAA,gCAAyB,EAAC8C,SAASC,MAAM;IACnD;AACF;AAEO,MAAMjE,oBAGT,CAACkE,YAAY5D,cAAc6B;IAC7B,OAAO;QACLgC,UAAUD,CAAAA,8BAAAA,WAAYC,QAAQ,IAC1B/D,oBAAoB8D,WAAWC,QAAQ,EAAE7D,cAAc6B,WACvD;QACJiC,MAAMF,CAAAA,8BAAAA,WAAYE,IAAI,IAClBhE,oBAAoB8D,WAAWE,IAAI,EAAE9D,cAAc6B,WACnD;IACN;AACF"}