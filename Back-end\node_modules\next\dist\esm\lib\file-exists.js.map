{"version": 3, "sources": ["../../src/lib/file-exists.ts"], "sourcesContent": ["import { existsSync, promises } from 'fs'\nimport isError from './is-error'\n\nexport enum FileType {\n  File = 'file',\n  Directory = 'directory',\n}\n\nexport async function fileExists(\n  fileName: string,\n  type?: FileType\n): Promise<boolean> {\n  try {\n    if (type === FileType.File) {\n      const stats = await promises.stat(fileName)\n      return stats.isFile()\n    } else if (type === FileType.Directory) {\n      const stats = await promises.stat(fileName)\n      return stats.isDirectory()\n    }\n\n    return existsSync(fileName)\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'ENAMETOOLONG')\n    ) {\n      return false\n    }\n    throw err\n  }\n}\n"], "names": ["existsSync", "promises", "isError", "FileType", "fileExists", "fileName", "type", "stats", "stat", "isFile", "isDirectory", "err", "code"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,KAAI;AACzC,OAAOC,aAAa,aAAY;AAEhC,OAAO,IAAA,AAAKC,kCAAAA;;;WAAAA;MAGX;AAED,OAAO,eAAeC,WACpBC,QAAgB,EAChBC,IAAe;IAEf,IAAI;QACF,IAAIA,iBAAwB;YAC1B,MAAMC,QAAQ,MAAMN,SAASO,IAAI,CAACH;YAClC,OAAOE,MAAME,MAAM;QACrB,OAAO,IAAIH,sBAA6B;YACtC,MAAMC,QAAQ,MAAMN,SAASO,IAAI,CAACH;YAClC,OAAOE,MAAMG,WAAW;QAC1B;QAEA,OAAOV,WAAWK;IACpB,EAAE,OAAOM,KAAK;QACZ,IACET,QAAQS,QACPA,CAAAA,IAAIC,IAAI,KAAK,YAAYD,IAAIC,IAAI,KAAK,cAAa,GACpD;YACA,OAAO;QACT;QACA,MAAMD;IACR;AACF"}