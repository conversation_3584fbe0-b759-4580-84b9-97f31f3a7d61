{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/app-dev-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../shared'\nimport type { GlobalErrorComponent } from '../../error-boundary'\n\nimport { useCallback, useEffect, useState } from 'react'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\nimport { FontStyles } from '../font/font-styles'\nimport { DevOverlay } from '../ui/dev-overlay'\nimport { handleClientError } from '../../errors/use-error-handler'\nimport { isNextRouterError } from '../../is-next-router-error'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../../../shared/lib/errors/constants'\n\nfunction readSsrError(): (Error & { digest?: string }) | null {\n  if (typeof document === 'undefined') {\n    return null\n  }\n\n  const ssrErrorTemplateTag = document.querySelector(\n    'template[data-next-error-message]'\n  )\n  if (ssrErrorTemplateTag) {\n    const message: string = ssrErrorTemplateTag.getAttribute(\n      'data-next-error-message'\n    )!\n    const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack')\n    const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest')\n    const error = new Error(message)\n    if (digest) {\n      ;(error as any).digest = digest\n    }\n    // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n    if (isNextRouterError(error)) {\n      return null\n    }\n    error.stack = stack || ''\n    return error\n  }\n\n  return null\n}\n\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors({\n  onBlockingError,\n}: {\n  onBlockingError: () => void\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to read during render. The attributes will be gone after commit.\n    const ssrError = readSsrError()\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      if (ssrError !== null) {\n        // TODO(veil): Produces wrong Owner Stack\n        // TODO(veil): Mark as recoverable error\n        // TODO(veil): console.error\n        handleClientError(ssrError)\n\n        // If it's missing root tags, we can't recover, make it blocking.\n        if (ssrError.digest === MISSING_ROOT_TAGS_ERROR) {\n          onBlockingError()\n        }\n      }\n    }, [ssrError, onBlockingError])\n  }\n\n  return null\n}\n\nexport function AppDevOverlay({\n  state,\n  globalError,\n  children,\n}: {\n  state: OverlayState\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  children: React.ReactNode\n}) {\n  const [isErrorOverlayOpen, setIsErrorOverlayOpen] = useState(false)\n  const openOverlay = useCallback(() => {\n    setIsErrorOverlayOpen(true)\n  }, [])\n\n  return (\n    <>\n      <AppDevOverlayErrorBoundary\n        globalError={globalError}\n        onError={setIsErrorOverlayOpen}\n      >\n        <ReplaySsrOnlyErrors onBlockingError={openOverlay} />\n        {children}\n      </AppDevOverlayErrorBoundary>\n      <>\n        {/* Fonts can only be loaded outside the Shadow DOM. */}\n        <FontStyles />\n        <DevOverlay\n          state={state}\n          isErrorOverlayOpen={isErrorOverlayOpen}\n          setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n        />\n      </>\n    </>\n  )\n}\n"], "names": ["useCallback", "useEffect", "useState", "AppDevOverlayErrorBoundary", "FontStyles", "DevOverlay", "handleClientError", "isNextRouterError", "MISSING_ROOT_TAGS_ERROR", "readSsrError", "document", "ssrErrorTemplateTag", "querySelector", "message", "getAttribute", "stack", "digest", "error", "Error", "ReplaySsrOnlyErrors", "onBlockingError", "process", "env", "NODE_ENV", "ssrError", "AppDevOverlay", "state", "globalError", "children", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "openOverlay", "onError"], "mappings": ";AAGA,SAASA,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,QAAO;AACxD,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,iBAAiB,QAAQ,iCAAgC;AAClE,SAASC,iBAAiB,QAAQ,6BAA4B;AAC9D,SAASC,uBAAuB,QAAQ,0CAAyC;AAEjF,SAASC;IACP,IAAI,OAAOC,aAAa,aAAa;QACnC,OAAO;IACT;IAEA,MAAMC,sBAAsBD,SAASE,aAAa,CAChD;IAEF,IAAID,qBAAqB;QACvB,MAAME,UAAkBF,oBAAoBG,YAAY,CACtD;QAEF,MAAMC,QAAQJ,oBAAoBG,YAAY,CAAC;QAC/C,MAAME,SAASL,oBAAoBG,YAAY,CAAC;QAChD,MAAMG,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;QAC/B,IAAIG,QAAQ;;YACRC,MAAcD,MAAM,GAAGA;QAC3B;QACA,yFAAyF;QACzF,IAAIT,kBAAkBU,QAAQ;YAC5B,OAAO;QACT;QACAA,MAAMF,KAAK,GAAGA,SAAS;QACvB,OAAOE;IACT;IAEA,OAAO;AACT;AAEA,uDAAuD;AACvD,yDAAyD;AACzD,sFAAsF;AACtF,SAASE,oBAAoB,KAI5B;IAJ4B,IAAA,EAC3BC,eAAe,EAGhB,GAJ4B;IAK3B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,wEAAwE;QACxE,MAAMC,WAAWf;QACjB,sDAAsD;QACtDR,UAAU;YACR,IAAIuB,aAAa,MAAM;gBACrB,yCAAyC;gBACzC,wCAAwC;gBACxC,4BAA4B;gBAC5BlB,kBAAkBkB;gBAElB,iEAAiE;gBACjE,IAAIA,SAASR,MAAM,KAAKR,yBAAyB;oBAC/CY;gBACF;YACF;QACF,GAAG;YAACI;YAAUJ;SAAgB;IAChC;IAEA,OAAO;AACT;AAEA,OAAO,SAASK,cAAc,KAQ7B;IAR6B,IAAA,EAC5BC,KAAK,EACLC,WAAW,EACXC,QAAQ,EAKT,GAR6B;IAS5B,MAAM,CAACC,oBAAoBC,sBAAsB,GAAG5B,SAAS;IAC7D,MAAM6B,cAAc/B,YAAY;QAC9B8B,sBAAsB;IACxB,GAAG,EAAE;IAEL,qBACE;;0BACE,MAAC3B;gBACCwB,aAAaA;gBACbK,SAASF;;kCAET,KAACX;wBAAoBC,iBAAiBW;;oBACrCH;;;0BAEH;;kCAEE,KAACxB;kCACD,KAACC;wBACCqB,OAAOA;wBACPG,oBAAoBA;wBACpBC,uBAAuBA;;;;;;AAKjC"}