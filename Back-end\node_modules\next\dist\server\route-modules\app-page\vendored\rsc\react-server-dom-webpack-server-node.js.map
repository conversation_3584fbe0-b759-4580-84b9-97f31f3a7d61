{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-node.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactServerDOMWebpackServerNode\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackServerNode"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,YACD,CAACC,+BAA+B"}