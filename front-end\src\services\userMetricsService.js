import api from './api';
import { downloadCSV } from '../utils/csvExporter';

const userMetricsService = {
  // Get user distribution by role
  getUsersByRole: async () => {
    try {
      const response = await api.get('/user-metrics/by-role');
      return response.data;
    } catch (error) {
      console.error('Error fetching users by role:', error);
      // Retourner des données vides en cas d'erreur
      return {
        chartData: [
          { name: 'Administrateurs', value: 0, color: '#FDCB6E' },
          { name: 'Vendeurs', value: 0, color: '#6C5CE7' },
          { name: 'Entreprises', value: 0, color: '#00B894' }
        ],
        totalUsers: 0
      };
    }
  },

  // Get top performing vendors
  getTopVendors: async (limit = 5, period = 'all-time') => {
    try {
      const params = { limit, period };
      const response = await api.get('/user-metrics/top-vendors', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching top vendors:', error);
      // Return empty array instead of throwing error
      if (error.response?.status === 403) {
        return [];
      }
      return [];
    }
  },

  // Get user growth metrics
  getUserGrowth: async (months = 6) => {
    try {
      const params = { months };
      const response = await api.get('/user-metrics/user-growth', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user growth:', error);
      // Retourner des données vides en cas d'erreur
      return {
        data: Array(months).fill().map((_, i) => {
          const date = new Date();
          date.setMonth(date.getMonth() - (months - i - 1));
          return {
            date: date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
            users: 0,
            growth: 0
          };
        }),
        totalGrowth: 0
      };
    }
  },

  // Get user activity metrics
  getUserActivity: async (days = 30) => {
    try {
      const params = { days };
      const response = await api.get('/user-metrics/user-activity', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user activity:', error);
      // Retourner des données vides en cas d'erreur
      return {
        data: Array(days).fill().map((_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - (days - i - 1));
          return {
            date: date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' }),
            activeUsers: 0,
            actions: 0
          };
        }),
        totalActiveUsers: 0,
        averageActions: 0
      };
    }
  },

  // Get comprehensive user metrics for admin dashboard
  getUserMetrics: async () => {
    try {
      const response = await api.get('/user-metrics/admin-stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching user metrics:', error);
      // Retourner des données vides en cas d'erreur
      return {
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        newUsers: 0,
        userGrowth: 0,
        usersByRole: {
          admin: 0,
          vendeur: 0,
          entreprise: 0
        },
        userActivity: {
          lastWeek: 0,
          lastMonth: 0
        }
      };
    }
  },

  // Export user data
  exportUserData: async () => {
    try {
      const response = await api.get('/user-metrics/export/users', { params: { format: 'csv' } });

      // Download the CSV file
      downloadCSV(response.data, `user_data_${new Date().toISOString().split('T')[0]}.csv`);
      return true;
    } catch (error) {
      console.error('Error exporting user data:', error);
      // Retourner un tableau vide en cas d'erreur
      downloadCSV([], `user_data_${new Date().toISOString().split('T')[0]}.csv`);
      return false;
    }
  },

  // Export vendor performance data
  exportVendorPerformance: async (period = 'monthly') => {
    try {
      const params = { period, format: 'csv' };
      const response = await api.get('/user-metrics/export/vendor-performance', { params });

      // Download the CSV file
      downloadCSV(response.data, `vendor_performance_${period}_${new Date().toISOString().split('T')[0]}.csv`);
      return true;
    } catch (error) {
      console.error('Error exporting vendor performance data:', error);
      // Retourner un tableau vide en cas d'erreur
      downloadCSV([], `vendor_performance_${period}_${new Date().toISOString().split('T')[0]}.csv`);
      return false;
    }
  },
};

export default userMetricsService;
