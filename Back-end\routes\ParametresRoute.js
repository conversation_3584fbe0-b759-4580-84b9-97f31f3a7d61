const express = require('express');
const router = express.Router();
const Parametres = require('../models/ParametresModel');
const multer = require('multer');
const path = require('path');
const sharp = require('sharp');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 2 * 1024 * 1024 }, // Limit file size to 2MB
  fileFilter: (req, file, cb) => {
    const fileTypes = /jpeg|jpg|png/;
    const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = fileTypes.test(file.mimetype);
    if (extname && mimetype) {
      return cb(null, true);
    }
    cb(new Error('Seuls les fichiers JPEG et PNG sont autorisés'));
  },
});

// GET /parametres: Fetch all parametres data
router.get('/parametres', async (req, res) => {
  try {
    const parametres = await Parametres.find();
    if (!parametres || parametres.length === 0) {
      return res.status(404).json({ message: 'Aucune donnée de paramètres trouvée' });
    }
    console.log('Fetched parametres:', parametres[0]);
    res.status(200).json(parametres[0]);
  } catch (error) {
    console.error('Error fetching parametres data:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des données de paramètres' });
  }
});

// POST /parametres: Create or update parametres data with optional logo upload
router.post('/parametres', upload.single('logo'), async (req, res) => {
  try {
    console.log('POST /parametres - Request body:', req.body);
    console.log('POST /parametres - Uploaded file:', req.file);

    const {
      // General settings
      defaultTaxRate,
      // Email settings
      emailSignature,
      // Invoice settings
      invoicePrefix, quotePrefix, paymentDelay,
      // Other settings
      notifications, rappelsPaiement, theme
    } = req.body;

    let logoFile = req.body.logo;

    // Validate image content if a file was uploaded
    if (req.file) {
      try {
        await sharp(req.file.path).metadata(); // Throws if not a valid image
        logoFile = `/uploads/${req.file.filename}`;
      } catch (error) {
        return res.status(400).json({ error: 'Fichier image invalide' });
      }
    }

    // Convert string values to appropriate types
    const parametresData = {
      // General settings
      logo: logoFile || '',
      defaultCurrency: 'TND', // Always use TND
      defaultTaxRate: defaultTaxRate ? Number(defaultTaxRate) : 19,

      // Email settings
      emailSignature: emailSignature || 'Cordialement,\nL\'équipe de Mon Entreprise',

      // Invoice settings
      invoicePrefix: invoicePrefix || 'FACT-',
      quotePrefix: quotePrefix || 'DEV-',
      paymentDelay: paymentDelay ? Number(paymentDelay) : 30,

      // Other settings
      notifications: notifications === 'true' || notifications === true,
      rappelsPaiement: rappelsPaiement === 'true' || rappelsPaiement === true,
      theme: theme || 'light',

      // Update timestamp
      updatedAt: new Date()
    };

    console.log('POST /parametres - Data to save:', parametresData);

    let updatedParametres;
    const existingParametres = await Parametres.findOne();

    if (existingParametres) {
      updatedParametres = await Parametres.findOneAndUpdate(
        {},
        { $set: parametresData },
        { new: true, runValidators: true }
      );
    } else {
      updatedParametres = new Parametres(parametresData);
      await updatedParametres.save();
    }

    console.log('POST /parametres - Response data:', updatedParametres);
    res.status(200).json(updatedParametres);
  } catch (error) {
    console.error('Error updating parametres data:', error.message);
    res.status(500).json({ error: 'Erreur lors de la mise à jour des données de paramètres' });
  }
});

// PUT /parametres: Update specific parametres fields (no file upload)
router.put('/parametres', async (req, res) => {
  try {
    console.log('PUT /parametres - Request body:', req.body);

    const {
      // General settings
      logo, defaultTaxRate,
      // Email settings
      emailSignature,
      // Invoice settings
      invoicePrefix, quotePrefix, paymentDelay,
      // Other settings
      notifications, rappelsPaiement, theme
    } = req.body;

    // Validate input and build update object
    const parametresData = {};

    // General settings
    if (logo !== undefined) parametresData.logo = logo || '';
    parametresData.defaultCurrency = 'TND'; // Always use TND
    if (defaultTaxRate !== undefined) parametresData.defaultTaxRate = Number(defaultTaxRate);

    // Email settings
    if (emailSignature !== undefined) parametresData.emailSignature = emailSignature;

    // Invoice settings
    if (invoicePrefix !== undefined) parametresData.invoicePrefix = invoicePrefix;
    if (quotePrefix !== undefined) parametresData.quotePrefix = quotePrefix;
    if (paymentDelay !== undefined) parametresData.paymentDelay = Number(paymentDelay);

    // Other settings
    if (notifications !== undefined) parametresData.notifications = notifications === 'true' || notifications === true;
    if (rappelsPaiement !== undefined) parametresData.rappelsPaiement = rappelsPaiement === 'true' || rappelsPaiement === true;
    if (theme !== undefined) parametresData.theme = theme || 'light';

    // Always update the timestamp
    parametresData.updatedAt = new Date();

    if (Object.keys(parametresData).length === 0) {
      return res.status(400).json({ error: 'Aucune donnée valide fournie pour la mise à jour' });
    }

    const existingParametres = await Parametres.findOne();
    let updatedParametres;

    if (existingParametres) {
      updatedParametres = await Parametres.findOneAndUpdate(
        {},
        { $set: parametresData },
        { new: true, runValidators: true }
      );
    } else {
      // Create new if none exists
      updatedParametres = new Parametres({ ...parametresData, createdAt: new Date() });
      await updatedParametres.save();
    }

    console.log('PUT /parametres - Response data:', updatedParametres);
    res.status(200).json(updatedParametres);
  } catch (error) {
    console.error('Error updating parametres data:', error.message);
    res.status(500).json({ error: 'Erreur lors de la mise à jour des données de paramètres' });
  }
});

module.exports = router;