{"version": 3, "sources": ["../../src/lib/recursive-copy.ts"], "sourcesContent": ["import path from 'path'\nimport type { Dirent, Stats } from 'fs'\nimport { promises, constants } from 'fs'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport isError from './is-error'\n\nconst COPYFILE_EXCL = constants.COPYFILE_EXCL\n\nexport async function recursiveCopy(\n  source: string,\n  dest: string,\n  {\n    concurrency = 32,\n    overwrite = false,\n    filter = () => true,\n  }: {\n    concurrency?: number\n    overwrite?: boolean\n    filter?(filePath: string): boolean\n  } = {}\n): Promise<void> {\n  const cwdPath = process.cwd()\n  const from = path.resolve(cwdPath, source)\n  const to = path.resolve(cwdPath, dest)\n\n  const sema = new Sema(concurrency)\n\n  // deep copy the file/directory\n  async function _copy(item: string, lstats?: Stats | Dirent): Promise<void> {\n    const target = item.replace(from, to)\n\n    await sema.acquire()\n\n    if (!lstats) {\n      // after lock on first run\n      lstats = await promises.lstat(from)\n    }\n\n    // readdir & lstat do not follow symbolic links\n    // if part is a symbolic link, follow it with stat\n    let isFile = lstats.isFile()\n    let isDirectory = lstats.isDirectory()\n    if (lstats.isSymbolicLink()) {\n      const stats = await promises.stat(item)\n      isFile = stats.isFile()\n      isDirectory = stats.isDirectory()\n    }\n\n    if (isDirectory) {\n      try {\n        await promises.mkdir(target, { recursive: true })\n      } catch (err) {\n        // do not throw `folder already exists` errors\n        if (isError(err) && err.code !== 'EEXIST') {\n          throw err\n        }\n      }\n      sema.release()\n      const files = await promises.readdir(item, { withFileTypes: true })\n      await Promise.all(\n        files.map((file) => _copy(path.join(item, file.name), file))\n      )\n    } else if (\n      isFile &&\n      // before we send the path to filter\n      // we remove the base path (from) and replace \\ by / (windows)\n      filter(item.replace(from, '').replace(/\\\\/g, '/'))\n    ) {\n      await promises\n        .copyFile(item, target, overwrite ? undefined : COPYFILE_EXCL)\n        .catch((err) => {\n          // if overwrite is false we shouldn't fail on EEXIST\n          if (err.code !== 'EEXIST') {\n            throw err\n          }\n        })\n      sema.release()\n    } else {\n      sema.release()\n    }\n  }\n\n  await _copy(from)\n}\n"], "names": ["path", "promises", "constants", "<PERSON><PERSON>", "isError", "COPYFILE_EXCL", "recursiveCopy", "source", "dest", "concurrency", "overwrite", "filter", "cwdPath", "process", "cwd", "from", "resolve", "to", "sema", "_copy", "item", "lstats", "target", "replace", "acquire", "lstat", "isFile", "isDirectory", "isSymbolicLink", "stats", "stat", "mkdir", "recursive", "err", "code", "release", "files", "readdir", "withFileTypes", "Promise", "all", "map", "file", "join", "name", "copyFile", "undefined", "catch"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,SAASC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AACxC,SAASC,IAAI,QAAQ,gCAA+B;AACpD,OAAOC,aAAa,aAAY;AAEhC,MAAMC,gBAAgBH,UAAUG,aAAa;AAE7C,OAAO,eAAeC,cACpBC,MAAc,EACdC,IAAY,EACZ,EACEC,cAAc,EAAE,EAChBC,YAAY,KAAK,EACjBC,SAAS,IAAM,IAAI,EAKpB,GAAG,CAAC,CAAC;IAEN,MAAMC,UAAUC,QAAQC,GAAG;IAC3B,MAAMC,OAAOf,KAAKgB,OAAO,CAACJ,SAASL;IACnC,MAAMU,KAAKjB,KAAKgB,OAAO,CAACJ,SAASJ;IAEjC,MAAMU,OAAO,IAAIf,KAAKM;IAEtB,+BAA+B;IAC/B,eAAeU,MAAMC,IAAY,EAAEC,MAAuB;QACxD,MAAMC,SAASF,KAAKG,OAAO,CAACR,MAAME;QAElC,MAAMC,KAAKM,OAAO;QAElB,IAAI,CAACH,QAAQ;YACX,0BAA0B;YAC1BA,SAAS,MAAMpB,SAASwB,KAAK,CAACV;QAChC;QAEA,+CAA+C;QAC/C,kDAAkD;QAClD,IAAIW,SAASL,OAAOK,MAAM;QAC1B,IAAIC,cAAcN,OAAOM,WAAW;QACpC,IAAIN,OAAOO,cAAc,IAAI;YAC3B,MAAMC,QAAQ,MAAM5B,SAAS6B,IAAI,CAACV;YAClCM,SAASG,MAAMH,MAAM;YACrBC,cAAcE,MAAMF,WAAW;QACjC;QAEA,IAAIA,aAAa;YACf,IAAI;gBACF,MAAM1B,SAAS8B,KAAK,CAACT,QAAQ;oBAAEU,WAAW;gBAAK;YACjD,EAAE,OAAOC,KAAK;gBACZ,8CAA8C;gBAC9C,IAAI7B,QAAQ6B,QAAQA,IAAIC,IAAI,KAAK,UAAU;oBACzC,MAAMD;gBACR;YACF;YACAf,KAAKiB,OAAO;YACZ,MAAMC,QAAQ,MAAMnC,SAASoC,OAAO,CAACjB,MAAM;gBAAEkB,eAAe;YAAK;YACjE,MAAMC,QAAQC,GAAG,CACfJ,MAAMK,GAAG,CAAC,CAACC,OAASvB,MAAMnB,KAAK2C,IAAI,CAACvB,MAAMsB,KAAKE,IAAI,GAAGF;QAE1D,OAAO,IACLhB,UACA,oCAAoC;QACpC,8DAA8D;QAC9Df,OAAOS,KAAKG,OAAO,CAACR,MAAM,IAAIQ,OAAO,CAAC,OAAO,OAC7C;YACA,MAAMtB,SACH4C,QAAQ,CAACzB,MAAME,QAAQZ,YAAYoC,YAAYzC,eAC/C0C,KAAK,CAAC,CAACd;gBACN,oDAAoD;gBACpD,IAAIA,IAAIC,IAAI,KAAK,UAAU;oBACzB,MAAMD;gBACR;YACF;YACFf,KAAKiB,OAAO;QACd,OAAO;YACLjB,KAAKiB,OAAO;QACd;IACF;IAEA,MAAMhB,MAAMJ;AACd"}