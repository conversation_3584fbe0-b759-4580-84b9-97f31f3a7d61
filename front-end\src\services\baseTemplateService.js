import { api } from './api';

/**
 * Service for managing base templates (Admin only)
 */
export const baseTemplateService = {
  /**
   * Get all base templates
   * @param {string} type - Optional filter by type (facture/devis)
   * @returns {Promise<Array>} Array of base templates
   */
  getAllTemplates: async (type = null) => {
    try {
      const params = type ? { type } : {};
      const response = await api.get('/base-templates', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching base templates:', error);
      throw new Error('Erreur lors de la récupération des templates de base');
    }
  },

  /**
   * Get a specific base template by ID
   * @param {string} id - Template ID
   * @returns {Promise<Object>} Base template object
   */
  getTemplateById: async (id) => {
    try {
      const response = await api.get(`/base-templates/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching base template:', error);
      throw new Error('Erreur lors de la récupération du template de base');
    }
  },

  /**
   * Get templates by type
   * @param {string} type - Template type (facture/devis)
   * @returns {Promise<Array>} Array of templates for the specified type
   */
  getTemplatesByType: async (type) => {
    try {
      const response = await api.get(`/base-templates/type/${type}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching templates by type:', error);
      throw new Error('Erreur lors de la récupération des templates par type');
    }
  },

  /**
   * Create a new base template (Admin only)
   * @param {Object} templateData - Template data
   * @returns {Promise<Object>} Created template
   */
  createTemplate: async (templateData) => {
    try {
      const response = await api.post('/base-templates', templateData);
      return response.data;
    } catch (error) {
      console.error('Error creating base template:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la création du template de base');
    }
  },

  /**
   * Update a base template (Admin only)
   * @param {string} id - Template ID
   * @param {Object} templateData - Updated template data
   * @returns {Promise<Object>} Updated template
   */
  updateTemplate: async (id, templateData) => {
    try {
      const response = await api.put(`/base-templates/${id}`, templateData);
      return response.data;
    } catch (error) {
      console.error('Error updating base template:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la mise à jour du template de base');
    }
  },

  /**
   * Delete a base template (Admin only)
   * @param {string} id - Template ID
   * @returns {Promise<void>}
   */
  deleteTemplate: async (id) => {
    try {
      await api.delete(`/base-templates/${id}`);
    } catch (error) {
      console.error('Error deleting base template:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Erreur lors de la suppression du template de base');
    }
  },

  /**
   * Get template structure for preview
   * @param {string} id - Template ID
   * @returns {Promise<Object>} Template structure data
   */
  getTemplateStructure: async (id) => {
    try {
      const response = await api.get(`/base-templates/${id}/structure`);
      return response.data;
    } catch (error) {
      console.error('Error fetching template structure:', error);
      throw new Error('Erreur lors de la récupération de la structure du template');
    }
  }
};
