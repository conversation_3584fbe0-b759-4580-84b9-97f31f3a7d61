import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  useTheme,
  alpha,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Avatar
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Visibility as VisibilityIcon,
  PictureAsPdf as PdfIcon,
  Print as PrintIcon,
  Receipt as ReceiptIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import factureService from '../services/factureService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';
import FactureCard from '../components/FactureCard';

const ClientFactures = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [factures, setFactures] = useState([]);
  const [filteredFactures, setFilteredFactures] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [viewMode, setViewMode] = useState('list'); // 'grid' or 'list'

  useEffect(() => {
    const fetchFactures = async () => {
      try {
        setLoading(true);
        const response = await factureService.getFactures();
        console.log('Factures récupérées du serveur:', response);
        console.log('ID client connecté:', currentUser.clientId);

        // Afficher les détails de chaque facture pour le débogage
        response.forEach((facture, index) => {
          console.log(`Facture ${index + 1}:`, facture);
          if (facture.clientId) {
            const clientIdStr = facture.clientId._id ? facture.clientId._id.toString() : facture.clientId.toString();
            console.log(`  - ID client de la facture ${index + 1}:`, clientIdStr);
            console.log(`  - Nom client de la facture ${index + 1}:`, facture.clientId.nom || 'Non défini');
          } else {
            console.log(`  - Facture ${index + 1} n'a pas de clientId`);
          }
        });

        console.log('ID client connecté (toString):', currentUser.clientId ? currentUser.clientId.toString() : 'Non défini');

        // Filtrer les factures pour n'afficher que celles du client connecté
        // Utiliser soit l'ID, soit l'email pour faire la correspondance
        console.log('Type de currentUser.clientId:', typeof currentUser.clientId);
        console.log('Valeur de currentUser.clientId:', currentUser.clientId);
        console.log('Email de l\'utilisateur connecté:', currentUser.email);

        const clientFactures = response.filter(facture => {
          if (!facture.clientId) {
            console.log('Facture sans clientId ignorée');
            return false;
          }

          // Afficher plus d'informations sur la facture et son clientId
          console.log('Structure de facture.clientId:', facture.clientId);

          // Vérifier si clientId est un objet avec _id ou directement un ID
          let factureClientId;
          let factureClientEmail = '';

          if (typeof facture.clientId === 'object' && facture.clientId !== null) {
            factureClientId = facture.clientId._id ? facture.clientId._id.toString() : '';
            factureClientEmail = facture.clientId.email || '';
            console.log('factureClientId est un objet avec _id:', factureClientId);
            console.log('Email du client de la facture:', factureClientEmail);
          } else {
            factureClientId = facture.clientId.toString();
            console.log('factureClientId est une chaîne ou un ID:', factureClientId);
          }

          let userClientId;
          if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
            userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
            console.log('userClientId est un objet avec _id:', userClientId);
          } else {
            userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
            console.log('userClientId est une chaîne ou un ID:', userClientId);
          }

          console.log('Comparaison ID:', factureClientId, '===', userClientId, factureClientId === userClientId);
          console.log('Comparaison Email:', factureClientEmail, '===', currentUser.email, factureClientEmail === currentUser.email);

          // Vérifier si l'ID correspond OU si l'email correspond
          const matchById = factureClientId === userClientId;
          const matchByEmail = factureClientEmail && currentUser.email && factureClientEmail.toLowerCase() === currentUser.email.toLowerCase();

          const match = matchById || matchByEmail;
          console.log('Résultat de la comparaison (ID ou Email):', match);

          return match;
        });

        console.log('Factures filtrées pour le client:', clientFactures);
        setFactures(clientFactures);
        setFilteredFactures(clientFactures);
        setLoading(false);

        // Si aucune facture n'est trouvée, afficher un message plus détaillé
        if (clientFactures.length === 0) {
          console.log('AUCUNE FACTURE TROUVÉE POUR CE CLIENT. Vérifiez que:');
          console.log('1. Le client connecté a bien un ID:', currentUser.clientId);
          console.log('2. Les factures dans la base de données sont bien associées à ce client');
          console.log('3. Les IDs sont bien au format string lors de la comparaison');
        }
      } catch (error) {
        console.error('Erreur lors du chargement des factures:', error);
        setLoading(false);
      }
    };

    if (currentUser && currentUser.clientId) {
      fetchFactures();
    } else {
      console.error('Utilisateur non connecté ou sans clientId');
      setLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    applyFilters();
  }, [searchTerm, statusFilter, dateFilter, factures]);

  const applyFilters = () => {
    let filtered = [...factures];

    // Filtre par recherche
    if (searchTerm) {
      filtered = filtered.filter(facture =>
        facture.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (facture.clientId && facture.clientId.nom.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      filtered = filtered.filter(facture => facture.statut === statusFilter);
    }

    // Filtre par date
    if (dateFilter !== 'all') {
      const now = new Date();
      const startDate = new Date();

      switch (dateFilter) {
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          break;
      }

      filtered = filtered.filter(facture => {
        const factureDate = new Date(facture.dateEmission);
        return factureDate >= startDate && factureDate <= now;
      });
    }

    setFilteredFactures(filtered);
  };

  const handleViewFacture = (id) => {
    navigate(`/client/factures/${id}`);
  };

  const handleGeneratePdf = async (id) => {
    try {
      await factureService.generatePdf(id);
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
    }
  };

  const handlePrint = async (id) => {
    try {
      await factureService.printFacture(id);
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header Section with Background */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={0}
        sx={{
          mb: 4,
          p: 3,
          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                width: 48,
                height: 48,
                mr: 2
              }}
            >
              <ReceiptIcon fontSize="medium" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                Mes Factures
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Consultez et gérez vos factures
              </Typography>
            </Box>
          </Box>
        </Box>
      </Card>

      {/* Search and Filter Section */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <TextField
            variant="outlined"
            placeholder="Rechercher par numéro..."
            size="small"
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              maxWidth: { xs: '100%', sm: 400 },
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.common.white, 0.9)
              }
            }}
          />

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <FormControl size="small">
              <InputLabel>Statut</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Statut"
                sx={{
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              >
                <MenuItem value="all">Tous les statuts</MenuItem>
                <MenuItem value="DRAFT">Brouillon</MenuItem>
                <MenuItem value="SENT">Envoyée</MenuItem>
                <MenuItem value="PAID">Payée</MenuItem>
                <MenuItem value="ACCEPTED">Acceptée</MenuItem>
                <MenuItem value="REJECTED">Refusée</MenuItem>
                <MenuItem value="CANCELED">Annulée</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small">
              <InputLabel>Période</InputLabel>
              <Select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                label="Période"
                sx={{
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              >
                <MenuItem value="all">Toutes les périodes</MenuItem>
                <MenuItem value="month">Dernier mois</MenuItem>
                <MenuItem value="quarter">Dernier trimestre</MenuItem>
                <MenuItem value="year">Dernière année</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant={viewMode === 'list' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('list')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Liste
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('grid')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Cartes
              </Button>
            </Box>
          </Box>
        </Box>
      </Card>

      {/* Table Section */}
      {viewMode === 'list' ? (
        <Card
          component={motion.div}
          variants={itemVariants}
          elevation={1}
          sx={{
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Numéro</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Montant</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Statut</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredFactures.length > 0 ? (
                  filteredFactures.map((facture) => (
                    <TableRow
                      key={facture._id}
                      hover
                      sx={{
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.03),
                          cursor: 'pointer'
                        }
                      }}
                    >
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {facture.numero || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="body2">
                          {formatDate(facture.dateEmission)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(facture.total)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Chip
                          label={formatStatut(facture.statut)}
                          color={
                            facture.statut === 'PAID' || facture.statut === 'ACCEPTED' ? 'success' :
                            facture.statut === 'DRAFT' ? 'info' :
                            facture.statut === 'REJECTED' || facture.statut === 'CANCELED' ? 'error' :
                            'warning'
                          }
                          size="small"
                          sx={{
                            fontWeight: 'medium',
                            borderRadius: 1,
                            px: 1
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 1.5 }}>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="Voir détails">
                            <IconButton
                              onClick={() => handleViewFacture(facture._id)}
                              color="primary"
                              size="small"
                              sx={{
                                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.2) }
                              }}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Générer PDF">
                            <IconButton
                              onClick={() => handleGeneratePdf(facture._id)}
                              size="small"
                              sx={{
                                color: theme.palette.text.secondary,
                                backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                              }}
                            >
                              <PdfIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Imprimer">
                            <IconButton
                              onClick={() => handlePrint(facture._id)}
                              size="small"
                              sx={{
                                color: theme.palette.text.secondary,
                                backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                              }}
                            >
                              <PrintIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        Aucune facture trouvée
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      ) : (
        <Box component={motion.div} variants={itemVariants}>
          <Grid container spacing={3}>
            {filteredFactures.length > 0 ? (
              filteredFactures.map((facture) => (
                <Grid item xs={12} sm={6} md={4} key={facture._id} component={motion.div} variants={itemVariants}>
                  <FactureCard
                    facture={facture}
                    onEdit={null}
                    onDelete={null}
                    onPdf={() => handleGeneratePdf(facture._id)}
                    onEmail={null}
                    onPrint={() => handlePrint(facture._id)}
                    onPayment={null}
                    isClient={true}
                    onClick={() => handleViewFacture(facture._id)}
                  />
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Card
                  component={motion.div}
                  variants={itemVariants}
                  sx={{
                    p: 5,
                    textAlign: 'center',
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`
                  }}
                >
                  <Box sx={{ mb: 2 }}>
                    <Avatar
                      sx={{
                        width: 70,
                        height: 70,
                        margin: '0 auto',
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main
                      }}
                    >
                      <ReceiptIcon sx={{ fontSize: 40 }} />
                    </Avatar>
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    Aucune facture trouvée
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400, mx: 'auto' }}>
                    Aucune facture ne correspond à vos critères de recherche.
                  </Typography>
                </Card>
              </Grid>
            )}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default ClientFactures;
