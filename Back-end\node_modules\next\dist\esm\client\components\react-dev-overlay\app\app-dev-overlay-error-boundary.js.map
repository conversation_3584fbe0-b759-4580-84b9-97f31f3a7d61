{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.tsx"], "sourcesContent": ["import { PureComponent } from 'react'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport {\n  ErrorBoundary,\n  type GlobalErrorComponent,\n  GlobalError as DefaultGlobalError,\n} from '../../error-boundary'\n\ntype AppDevOverlayErrorBoundaryProps = {\n  children: React.ReactNode\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  onError: (value: boolean) => void\n}\n\ntype AppDevOverlayErrorBoundaryState = {\n  isReactError: boolean\n  reactError: unknown\n}\n\nfunction ErroredHtml({\n  globalError: [GlobalError, globalErrorStyles],\n  error,\n}: {\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  error: unknown\n}) {\n  if (!error) {\n    return (\n      <html>\n        <head />\n        <body />\n      </html>\n    )\n  }\n  return (\n    <ErrorBoundary errorComponent={DefaultGlobalError}>\n      {globalErrorStyles}\n      <GlobalError error={error} />\n    </ErrorBoundary>\n  )\n}\n\nexport class AppDevOverlayErrorBoundary extends PureComponent<\n  AppDevOverlayErrorBoundaryProps,\n  AppDevOverlayErrorBoundaryState\n> {\n  state = { isReactError: false, reactError: null }\n\n  static getDerivedStateFromError(error: Error) {\n    if (!error.stack) {\n      return { isReactError: false, reactError: null }\n    }\n\n    RuntimeErrorHandler.hadRuntimeError = true\n\n    return {\n      isReactError: true,\n      reactError: error,\n    }\n  }\n\n  componentDidCatch() {\n    this.props.onError(this.state.isReactError)\n  }\n\n  render() {\n    const { children, globalError } = this.props\n    const { isReactError, reactError } = this.state\n\n    const fallback = (\n      <ErroredHtml globalError={globalError} error={reactError} />\n    )\n\n    return isReactError ? fallback : children\n  }\n}\n"], "names": ["PureComponent", "RuntimeError<PERSON>andler", "Error<PERSON>ou<PERSON><PERSON>", "GlobalError", "DefaultGlobalError", "ErroredHtml", "globalError", "globalErrorStyles", "error", "html", "head", "body", "errorComponent", "AppDevOverlayErrorBoundary", "getDerivedStateFromError", "stack", "isReactError", "reactError", "hadRuntimeError", "componentDidCatch", "props", "onError", "state", "render", "children", "fallback"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,QAAO;AACrC,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SACEC,aAAa,EAEbC,eAAeC,kBAAkB,QAC5B,uBAAsB;AAa7B,SAASC,YAAY,KAMpB;IANoB,IAAA,EACnBC,aAAa,CAACH,aAAaI,kBAAkB,EAC7CC,KAAK,EAIN,GANoB;IAOnB,IAAI,CAACA,OAAO;QACV,qBACE,MAACC;;8BACC,KAACC;8BACD,KAACC;;;IAGP;IACA,qBACE,MAACT;QAAcU,gBAAgBR;;YAC5BG;0BACD,KAACJ;gBAAYK,OAAOA;;;;AAG1B;AAEA,OAAO,MAAMK,mCAAmCb;IAM9C,OAAOc,yBAAyBN,KAAY,EAAE;QAC5C,IAAI,CAACA,MAAMO,KAAK,EAAE;YAChB,OAAO;gBAAEC,cAAc;gBAAOC,YAAY;YAAK;QACjD;QAEAhB,oBAAoBiB,eAAe,GAAG;QAEtC,OAAO;YACLF,cAAc;YACdC,YAAYT;QACd;IACF;IAEAW,oBAAoB;QAClB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,KAAK,CAACN,YAAY;IAC5C;IAEAO,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAElB,WAAW,EAAE,GAAG,IAAI,CAACc,KAAK;QAC5C,MAAM,EAAEJ,YAAY,EAAEC,UAAU,EAAE,GAAG,IAAI,CAACK,KAAK;QAE/C,MAAMG,yBACJ,KAACpB;YAAYC,aAAaA;YAAaE,OAAOS;;QAGhD,OAAOD,eAAeS,WAAWD;IACnC;;QAhCK,qBAILF,QAAQ;YAAEN,cAAc;YAAOC,YAAY;QAAK;;AA6BlD"}