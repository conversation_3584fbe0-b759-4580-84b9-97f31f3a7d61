"use client"

import { useState } from "react"
import { TextField, InputAdornment, Button } from "@mui/material"
import { Lock } from "@mui/icons-material"

const PasswordField = ({ label, name, value, onChange, ...props }) => {
  const [showPassword, setShowPassword] = useState(false)

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword)
  }

  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }

  return (
    <TextField
      fullWidth
      margin="normal"
      name={name}
      label={label || "Password"}
      type={showPassword ? "text" : "password"}
      value={value}
      onChange={onChange}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <Lock color="action" />
          </InputAdornment>
        ),
        endAdornment: (
          <InputAdornment position="end">
            <Button onClick={handleClickShowPassword} onMouseDown={handleMouseDownPassword} sx={{ color: "#0062cc" }}>
              {showPassword ? "HIDE" : "SHOW"}
            </Button>
          </InputAdornment>
        ),
      }}
      sx={{ bgcolor: "grey.100", borderRadius: 1 }}
      {...props}
    />
  )
}

export default PasswordField

