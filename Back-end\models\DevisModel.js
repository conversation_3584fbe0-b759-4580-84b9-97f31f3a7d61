const mongoose = require('mongoose');
const { Schema } = mongoose;

const ligneDevisSchema = new Schema({
    produit: { type: Schema.Types.ObjectId, ref: 'Produit', required: false },
    description: { type: String },
    quantite: { type: Number, required: true },
    prixUnitaire: { type: Number, required: true },
    montantHT: { type: Number, required: true },
    montantTTC: { type: Number, required: true }
});

const devisSchema = new Schema({
    // Informations de base
    numéro: {
        type: String,
        required: [true, 'Le numéro est obligatoire'],
        unique: true
    },
    clientId: {
        type: Schema.Types.ObjectId,
        ref: 'Client',
        required: [true, 'Le client est obligatoire']
    },
    vendeurId: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    responsableId: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    dateCréation: {
        type: Date,
        default: Date.now
    },
    dateValidite: Date,

    // Contenu du devis
    lignes: [ligneDevisSchema],
    notes: { type: String },

    // Statut et workflow
    statut: {
        type: String,
        enum: [
            'DRAFT', 'WAITING_APPROVAL', 'APPROVED_INTERNAL', 'SENT', 'PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED'
        ],
        default: 'DRAFT'
    },

    // Validation responsable
    validationResponsable: {
        statut: {
            type: String,
            enum: ['PENDING', 'APPROVED', 'REJECTED'],
            default: 'PENDING'
        },
        date: Date,
        commentaires: String,
        validéPar: {
            type: Schema.Types.ObjectId,
            ref: 'users'
        }
    },

    // Réponse client
    reponseClient: {
        date: Date,
        commentaires: String
    },

    // Montants
    total: { type: Number, required: true },
    montantHT: { type: Number },
    montantTTC: { type: Number },
    tauxTVA: { type: Number, required: true },

    // Origine
    estDemandeClient: {
        type: Boolean,
        default: false
    }
}, { timestamps: true });

module.exports = mongoose.model('Devis', devisSchema);