const mongoose = require('mongoose');
const { Schema } = mongoose;

const userSchema = new Schema({
    // Common fields
    nom: { type: String, required: true },
    prenom: { type: String }, // Champ prénom
    email: { type: String, required: true, unique: true },
    motDePasse: { type: String, required: true },
    role: { type: String, enum: ['ADMIN', 'RESPONSABLE', 'VENDEUR', 'CLIENT'], required: true },
    dateCreation: { type: Date, default: Date.now },
    profileImage: { type: String }, // Profile picture URL

    // Password reset fields (OTP method)
    resetOTP: { type: String },
    resetOTPExpires: { type: Date },

    // Client specific fields
    adresse: { type: String },
    telephone: { type: String },
    contact: { type: String },
    cin: { type: String }, // Numéro de Carte d'Identité Nationale pour les clients

    // Relations
    responsables: [{ type: Schema.Types.ObjectId, ref: 'users' }], // Relation 1,N avec Responsable d'entreprise (utilisateurs avec rôle RESPONSABLE)

    // Champ pour suivre qui a créé cet utilisateur (Admin ou Responsable)
    createdBy: { type: Schema.Types.ObjectId, ref: 'users' },

    // Champs pour l'abonnement (pour les utilisateurs RESPONSABLE)
    abonnementActif: { type: Boolean, default: false },
    abonnementId: { type: Schema.Types.ObjectId, ref: 'Abonnement' }
});
const UserModel = mongoose.model('users',userSchema);
module.exports = UserModel;