/**
 * Utility functions for template management
 */

/**
 * Generate a thumbnail URL for a template
 * @param {Object} template - The template object
 * @returns {string} - The thumbnail URL
 */
export const generateThumbnailUrl = (template) => {
  if (!template) return '/templates/default-thumbnail.png';
  
  const type = template.type?.toLowerCase() || 'facture';
  const layout = template.layout || 'standard';
  
  return template.thumbnail || `/templates/thumbnails/template-${type}-${layout}.png`;
};

/**
 * Generate a full image URL for a template
 * @param {Object} template - The template object
 * @returns {string} - The image URL
 */
export const generateImageUrl = (template) => {
  if (!template) return '/templates/default-template.png';
  
  const type = template.type?.toLowerCase() || 'facture';
  
  return template.image || `/templates/template-${type}.png`;
};

/**
 * Create a canvas preview of a template
 * @param {Object} template - The template object
 * @param {number} width - The width of the thumbnail
 * @param {number} height - The height of the thumbnail
 * @returns {Promise<string>} - A promise that resolves to a data URL of the thumbnail
 */
export const createTemplateThumbnail = (template, width = 300, height = 200) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);
    
    // Header with template color
    ctx.fillStyle = template.color || '#3b82f6';
    ctx.fillRect(0, 0, width, 40);
    
    // Document title
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(template.type === 'Devis' ? 'DEVIS' : 'FACTURE', width / 2, 25);
    
    // Company and client info
    ctx.fillStyle = '#333333';
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('ÉMETTEUR', 20, 60);
    ctx.fillText('Votre Entreprise', 20, 75);
    
    ctx.textAlign = 'right';
    ctx.fillText('DESTINATAIRE', width - 20, 60);
    ctx.fillText('Client Exemple', width - 20, 75);
    
    // Table header
    ctx.fillStyle = template.color || '#3b82f6';
    ctx.fillRect(20, 100, width - 40, 25);
    
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Description', 30, 117);
    ctx.textAlign = 'center';
    ctx.fillText('Quantité', width / 2, 117);
    ctx.textAlign = 'right';
    ctx.fillText('Total', width - 30, 117);
    
    // Table rows
    ctx.fillStyle = '#333333';
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Produit/Service 1', 30, 145);
    ctx.textAlign = 'center';
    ctx.fillText('1', width / 2, 145);
    ctx.textAlign = 'right';
    ctx.fillText('100.00 €', width - 30, 145);
    
    // Total
    ctx.fillStyle = template.color || '#3b82f6';
    ctx.fillRect(width - 120, 170, 100, 25);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Total: 100.00 €', width - 70, 187);
    
    // Convert to data URL
    resolve(canvas.toDataURL('image/png'));
  });
};

/**
 * Get a template style class based on the template type and layout
 * @param {Object} template - The template object
 * @returns {string} - CSS class name
 */
export const getTemplateStyleClass = (template) => {
  if (!template) return 'template-standard';
  
  const type = template.type?.toLowerCase() || 'facture';
  const layout = template.layout || 'standard';
  
  return `template-${type}-${layout}`;
};
