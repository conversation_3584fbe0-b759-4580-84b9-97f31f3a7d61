import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  Button,
  CircularProgress,
  Alert,
  useTheme,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Chip,
  Avatar,
  Stack,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Menu,
  MenuItem,
  FormControl,
  Select,
  Snackbar
} from '@mui/material';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import {
  Refresh as RefreshIcon,
  DownloadOutlined as DownloadIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  FileDownload as FileDownloadIcon,
  DateRange as DateRangeIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import { useAuth } from '../contexts/AuthContext';
import SmartDateFilter from '../components/SmartDateFilter';
import { formatCurrency } from '../utils/formatters';
import analyticsService from '../services/analyticsService';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { fr } from 'date-fns/locale';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
};

const cardVariants = {
  hidden: { scale: 0.95, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  },
  hover: {
    scale: 1.02,
    boxShadow: "0px 8px 25px rgba(0, 0, 0, 0.1)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

const VendeurBIDashboard = () => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month');
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState(null);
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [dateRangeDialogOpen, setDateRangeDialogOpen] = useState(false);
  const [startDate, setStartDate] = useState(new Date(new Date().setMonth(new Date().getMonth() - 1)));
  const [endDate, setEndDate] = useState(new Date());
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [searchQuery, setSearchQuery] = useState('');
  const [exportType, setExportType] = useState('invoices');
  const [exportStartDate, setExportStartDate] = useState(null);
  const [exportEndDate, setExportEndDate] = useState(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Initialize sales data with mock values for recharts
  const [salesData, setSalesData] = useState([
    { month: 'Jan', factures: 0, devis: 0 },
    { month: 'Fév', factures: 0, devis: 0 },
    { month: 'Mar', factures: 0, devis: 0 },
    { month: 'Avr', factures: 0, devis: 0 },
    { month: 'Mai', factures: 0, devis: 0 },
    { month: 'Juin', factures: 0, devis: 0 },
    { month: 'Juil', factures: 0, devis: 0 },
    { month: 'Août', factures: 0, devis: 0 },
    { month: 'Sep', factures: 0, devis: 0 },
    { month: 'Oct', factures: 0, devis: 0 },
    { month: 'Nov', factures: 0, devis: 0 },
    { month: 'Déc', factures: 0, devis: 0 }
  ]);

  // Initialize category data with mock values for recharts
  const [categoryData, setCategoryData] = useState([
    { name: 'Produits', value: 0 },
    { name: 'Services', value: 0 },
    { name: 'Autres', value: 0 }
  ]);

  // Initialize client distribution data with mock values for recharts
  const [clientDistData, setClientDistData] = useState([
    { name: 'Entreprises', value: 0 },
    { name: 'Particuliers', value: 0 }
  ]);

  // Initialize table data with empty arrays
  const [tableData, setTableData] = useState({
    recentInvoices: [],
    recentQuotes: [],
    topClients: []
  });

  // Initialize stats with zeros
  const [stats, setStats] = useState({
    factures: 0,
    devis: 0,
    clients: 0,
    totalRevenue: 0,
    paidInvoices: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
    acceptedDevis: 0
  });

  // Initialize product stats with empty values
  const [productStats, setProductStats] = useState({
    topSellingProduct: {
      name: '',
      sales: 0,
      category: ''
    },
    mostProfitableProduct: {
      name: '',
      margin: 0,
      category: ''
    },
    topCategory: {
      name: '',
      percentage: 0
    },
    lowStock: {
      count: 0,
      products: []
    }
  });

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setTimeRange(range);
    setLoading(true);
    fetchDashboardData(range);
  };

  // Refresh data
  const handleRefresh = () => {
    setLoading(true);
    fetchDashboardData(timeRange);
  };

  // Handle export menu
  const handleExportClick = (event) => {
    setExportAnchorEl(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };

  // Handle date range dialog
  const handleDateRangeOpen = () => {
    setDateRangeDialogOpen(true);
    handleExportClose();
  };

  const handleDateRangeClose = () => {
    setDateRangeDialogOpen(false);
  };

  const handleDateRangeApply = () => {
    // Apply the selected date range
    const customRange = {
      startDate,
      endDate
    };
    fetchDashboardData('custom', customRange);
    setDateRangeDialogOpen(false);
  };

  // Export data to CSV
  const exportToCSV = (type) => {
    // Implementation omitted for brevity
    handleExportClose();
  };

  // Fetch dashboard data from the API
  const fetchDashboardData = async (dateRange, customRange = null) => {
    try {
      const data = await analyticsService.getVendeurDashboardStats(dateRange, customRange);
      setStats(data);

      // Update sales data for recharts
      if (data.salesByMonth && data.salesByMonth.invoices) {
        const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
        const newSalesData = months.map((month, index) => ({
          month,
          factures: data.salesByMonth.invoices[index] || 0,
          devis: data.salesByMonth.quotes[index] || 0
        }));
        setSalesData(newSalesData);
      }

      // Update category data for recharts
      if (data.categoryAnalysis) {
        const categories = ['Produits', 'Services', 'Autres'];
        const newCategoryData = categories.map((name, index) => ({
          name,
          value: data.categoryAnalysis[index] || 0
        }));
        setCategoryData(newCategoryData);
      }

      // Update client distribution data for recharts
      if (data.clientDistribution) {
        const clientTypes = ['Entreprises', 'Particuliers'];
        const newClientDistData = clientTypes.map((name, index) => ({
          name,
          value: data.clientDistribution[index] || 0
        }));
        setClientDistData(newClientDistData);
      }

      // Update table data
      if (data.recentInvoices || data.recentQuotes || data.topClients) {
        setTableData({
          recentInvoices: data.recentInvoices || [],
          recentQuotes: data.recentQuotes || [],
          topClients: data.topClients || []
        });
      }

      // Update product stats from API data
      if (data.productStats) {
        setProductStats(data.productStats);
      } else if (data.products && data.productCategories) {
        // Format product stats from direct API response
        const formattedProductStats = {
          topSellingProduct: {
            name: data.products.topSelling?.name || 'Aucun produit',
            sales: data.products.topSelling?.sales || 0,
            category: data.products.topSelling?.category || 'Produits'
          },
          mostProfitableProduct: {
            name: data.products.mostProfitable?.name || 'Aucun produit',
            margin: data.products.mostProfitable?.margin || 0,
            category: data.products.mostProfitable?.category || 'Produits'
          },
          topCategory: {
            name: 'Produits',
            percentage: data.productCategories.products || 0
          },
          lowStock: {
            count: data.products.lowStock?.length || 0,
            products: data.products.lowStock?.map(p => p.name) || []
          }
        };
        setProductStats(formattedProductStats);
      }

      setNotification({
        open: true,
        message: 'Données chargées avec succès',
        severity: 'success'
      });
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Erreur lors du chargement des données. Veuillez réessayer.');
      setNotification({
        open: true,
        message: 'Erreur lors du chargement des données',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle export dialog
  const handleOpenExportDialog = () => {
    setExportDialogOpen(true);
  };

  const handleCloseExportDialog = () => {
    setExportDialogOpen(false);
  };

  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  useEffect(() => {
    // Fetch data when component mounts
    fetchDashboardData(timeRange);
  }, []);

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.info.main,
    theme.palette.warning.main,
  ];

  // KPI Card Component
  const KpiCard = ({ title, value, subtitle, icon, color, trend, trendValue }) => {
    const isTrendPositive = trendValue > 0;
    const trendIcon = isTrendPositive ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />;
    const trendColor = isTrendPositive ? 'success' : 'error';

    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <Card
          elevation={0}
          sx={{
            height: '100%',
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette[color].light, 0.2)} 0%, ${alpha(theme.palette[color].main, 0.05)} 100%)`,
            border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette[color].main, 0.03)} 100%)`,
              zIndex: 0
            }
          }}
        >
          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  backgroundColor: alpha(theme.palette[color].main, 0.15),
                  color: theme.palette[color].main,
                  mr: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette[color].main, 0.2)}`
                }}
              >
                {icon}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="500" color="text.primary">
                  {title}
                </Typography>
                {trend && (
                  <Chip
                    icon={trendIcon}
                    label={`${Math.abs(trendValue).toFixed(1)}%`}
                    size="small"
                    color={trendColor}
                    sx={{
                      height: 24,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      mt: 0.5
                    }}
                  />
                )}
              </Box>
            </Box>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                color: theme.palette[color].dark,
                textShadow: `0 2px 4px ${alpha(theme.palette[color].main, 0.2)}`
              }}
            >
              {value}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                opacity: 0.8,
                maxWidth: '90%'
              }}
            >
              {subtitle}
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Export Dialog Component
  const ExportDialog = () => {
    return (
      <Dialog open={exportDialogOpen} onClose={handleCloseExportDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Exporter les données pour Power BI</Typography>
            <IconButton edge="end" color="inherit" onClick={handleCloseExportDialog} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Exportez vos données au format CSV pour les importer dans Power BI
          </Typography>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Sélectionnez le type de données à exporter
            </Typography>
            <FormControl fullWidth variant="outlined">
              <Select
                value={exportType}
                onChange={(e) => setExportType(e.target.value)}
                displayEmpty
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="invoices">Factures</MenuItem>
                <MenuItem value="quotes">Devis</MenuItem>
                <MenuItem value="clients">Clients</MenuItem>
                <MenuItem value="sales">Ventes par mois</MenuItem>
                <MenuItem value="categories">Catégories</MenuItem>
                <MenuItem value="all">Toutes les données</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={6}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Date de début
                </Typography>
                <DatePicker
                  value={exportStartDate}
                  onChange={(newValue) => setExportStartDate(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      variant="outlined"
                      placeholder="Date de début"
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <InputAdornment position="start">
                            <DateRangeIcon fontSize="small" />
                          </InputAdornment>
                        ),
                        sx: { borderRadius: 2 }
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Date de fin
                </Typography>
                <DatePicker
                  value={exportEndDate}
                  onChange={(newValue) => setExportEndDate(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      variant="outlined"
                      placeholder="Date de fin"
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <InputAdornment position="start">
                            <DateRangeIcon fontSize="small" />
                          </InputAdornment>
                        ),
                        sx: { borderRadius: 2 }
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </LocalizationProvider>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              Les données exportées peuvent être importées directement dans Power BI pour créer des tableaux de bord interactifs.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseExportDialog}
            variant="outlined"
            startIcon={<CloseIcon />}
            sx={{ borderRadius: 2 }}
          >
            Annuler
          </Button>
          <Button
            onClick={() => exportToCSV(exportType)}
            variant="contained"
            color="primary"
            startIcon={<FileDownloadIcon />}
            sx={{ borderRadius: 2 }}
          >
            Exporter les données
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '80vh'
        }}
      >
        <CircularProgress size={60} thickness={4} />
        <Typography variant="h6" sx={{ mt: 2, fontWeight: 500 }}>
          Chargement des données analytiques...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 2, sm: 3 },
        background: `linear-gradient(180deg, ${alpha(theme.palette.background.default, 0.6)} 0%, ${theme.palette.background.default} 100%)`,
        minHeight: '100vh'
      }}
    >
      <Container maxWidth="xl">
        {/* Header */}
        <Box
          component={motion.div}
          variants={itemVariants}
          sx={{
            mb: 4,
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', md: 'center' }
          }}
        >
          <Box>
            <Typography
              variant="h3"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}
            >
              Tableau de Bord Vendeur
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
              Bienvenue, {currentUser?.nom || 'Vendeur'}. Visualisez vos performances de vente et suivez vos clients.
            </Typography>
          </Box>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mt: { xs: 2, md: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            <TextField
              placeholder="Rechercher..."
              size="small"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                sx: { borderRadius: 2 }
              }}
              sx={{ width: { xs: '100%', sm: 220 } }}
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                minWidth: 130
              }}
            >
              Actualiser
            </Button>

            <SmartDateFilter
              onDateRangeChange={handleDateRangeChange}
              onRefresh={handleRefresh}
              initialRange={
                timeRange === 'month' ? 'thisMonth' :
                timeRange === 'quarter' ? 'thisQuarter' :
                timeRange === 'year' ? 'thisYear' :
                timeRange === 'all-time' ? 'allTime' : 'thisMonth'
              }
              showRefreshButton={false}
            />

            <Button
              variant="outlined"
              color="primary"
              onClick={handleOpenExportDialog}
              startIcon={<FileDownloadIcon />}
              sx={{
                borderRadius: 2,
                minWidth: 130
              }}
            >
              Exporter
            </Button>
          </Stack>
        </Box>

        {/* Dashboard Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* KPI Cards */}
          <Grid
            container
            spacing={3}
            sx={{ mb: 4 }}
            component={motion.div}
            variants={itemVariants}
          >
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Factures"
                value={stats.factures}
                subtitle={`${stats.paidInvoices} factures payées`}
                icon={<ReceiptIcon />}
                color="primary"
                trend={true}
                trendValue={5.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Devis"
                value={stats.devis}
                subtitle={`${stats.acceptedDevis} devis acceptés`}
                icon={<DescriptionIcon />}
                color="secondary"
                trend={true}
                trendValue={3.8}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Clients"
                value={stats.clients}
                subtitle={`${tableData.topClients.length} clients actifs`}
                icon={<PeopleIcon />}
                color="info"
                trend={true}
                trendValue={7.5}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Revenus"
                value={`${formatCurrency(stats.totalRevenue)} DT`}
                subtitle={`${stats.paidInvoices} factures payées`}
                icon={<MoneyIcon />}
                color="success"
                trend={true}
                trendValue={12.3}
              />
            </Grid>
          </Grid>

          {/* Main Content */}
          <Grid
            container
            spacing={3}
            component={motion.div}
            variants={itemVariants}
          >
            {/* Left Column - Charts */}
            <Grid item xs={12} lg={8}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    mb: 3,
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h5" fontWeight="600">
                        Ventes par mois
                      </Typography>
                    }
                    action={
                      <Tooltip title="Exporter les données">
                        <IconButton onClick={() => exportToCSV('sales')}>
                          <FileDownloadIcon />
                        </IconButton>
                      </Tooltip>
                    }
                    sx={{
                      px: 3,
                      pt: 3,
                      pb: 2,
                      '& .MuiCardHeader-action': {
                        m: 0,
                        alignSelf: 'center'
                      }
                    }}
                  />
                  <Divider />
                  <CardContent sx={{ height: 400, p: 3 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={salesData}>
                        <defs>
                          <linearGradient id="colorFactures" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8} />
                            <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0} />
                          </linearGradient>
                          <linearGradient id="colorDevis" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={theme.palette.secondary.main} stopOpacity={0.8} />
                            <stop offset="95%" stopColor={theme.palette.secondary.main} stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                        <XAxis
                          dataKey="month"
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <YAxis
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                          tickFormatter={(value) => `${value}`}
                        />
                        <RechartsTooltip
                          formatter={(value) => [`${value}`, '']}
                          labelFormatter={(label) => `Mois: ${label}`}
                          contentStyle={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 8,
                            boxShadow: theme.shadows[3],
                            padding: '10px 14px'
                          }}
                        />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="factures"
                          stroke={theme.palette.primary.main}
                          strokeWidth={3}
                          fillOpacity={1}
                          fill="url(#colorFactures)"
                          activeDot={{ r: 8, fill: theme.palette.primary.main, strokeWidth: 0 }}
                          name="Factures"
                        />
                        <Area
                          type="monotone"
                          dataKey="devis"
                          stroke={theme.palette.secondary.main}
                          strokeWidth={3}
                          fillOpacity={1}
                          fill="url(#colorDevis)"
                          activeDot={{ r: 8, fill: theme.palette.secondary.main, strokeWidth: 0 }}
                          name="Devis"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </motion.div>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <motion.div variants={cardVariants}>
                    <Card
                      elevation={0}
                      sx={{
                        height: '100%',
                        borderRadius: 3,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                        overflow: 'hidden'
                      }}
                    >
                      <CardHeader
                        title={
                          <Typography variant="h6" fontWeight="600">
                            Analyse par catégorie
                          </Typography>
                        }
                        action={
                          <Tooltip title="Exporter les données">
                            <IconButton onClick={() => exportToCSV('categories')}>
                              <FileDownloadIcon />
                            </IconButton>
                          </Tooltip>
                        }
                        sx={{ px: 3, pt: 3, pb: 2 }}
                      />
                      <Divider />
                      <CardContent sx={{ height: 300, p: 3 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={categoryData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={90}
                              paddingAngle={3}
                              dataKey="value"
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              labelLine={false}
                            >
                              {categoryData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Legend
                              verticalAlign="bottom"
                              height={36}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} md={6}>
                  <motion.div variants={cardVariants}>
                    <Card
                      elevation={0}
                      sx={{
                        height: '100%',
                        borderRadius: 3,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                        overflow: 'hidden'
                      }}
                    >
                      <CardHeader
                        title={
                          <Typography variant="h6" fontWeight="600">
                            Distribution des clients
                          </Typography>
                        }
                        action={
                          <Tooltip title="Exporter les données">
                            <IconButton onClick={() => exportToCSV('clientDistribution')}>
                              <FileDownloadIcon />
                            </IconButton>
                          </Tooltip>
                        }
                        sx={{ px: 3, pt: 3, pb: 2 }}
                      />
                      <Divider />
                      <CardContent sx={{ height: 300, p: 3 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={clientDistData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={90}
                              paddingAngle={3}
                              dataKey="value"
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              labelLine={false}
                            >
                              {clientDistData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Legend
                              verticalAlign="bottom"
                              height={36}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>
            </Grid>

            {/* Right Column - Tables */}
            <Grid item xs={12} lg={4}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    mb: 3,
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Factures récentes
                      </Typography>
                    }
                    action={
                      <Tooltip title="Exporter les données">
                        <IconButton onClick={() => exportToCSV('invoices')}>
                          <FileDownloadIcon />
                        </IconButton>
                      </Tooltip>
                    }
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ p: 0 }}>
                    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {tableData.recentInvoices && tableData.recentInvoices.length > 0 ? (
                        <Box sx={{ p: 2 }}>
                          {tableData.recentInvoices.map((invoice, index) => (
                            <Box
                              key={index}
                              sx={{
                                p: 2,
                                borderBottom: index < tableData.recentInvoices.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                                '&:hover': {
                                  bgcolor: alpha(theme.palette.primary.main, 0.05)
                                }
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="subtitle1" fontWeight="medium">
                                  {invoice.id}
                                </Typography>
                                <Chip
                                  label={invoice.statut === 'PAID' ? 'Payée' : invoice.statut === 'PENDING' ? 'En attente' : 'En retard'}
                                  size="small"
                                  color={invoice.statut === 'PAID' ? 'success' : invoice.statut === 'PENDING' ? 'warning' : 'error'}
                                  sx={{ height: 24 }}
                                />
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                  {invoice.client}
                                </Typography>
                                <Typography variant="body2" fontWeight="bold">
                                  {formatCurrency(invoice.montant)} DT
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary">
                                Date: {invoice.date}
                              </Typography>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{
                          height: 200,
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          bgcolor: 'background.paper',
                          borderRadius: 1,
                          p: 2
                        }}>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Aucune facture récente à afficher
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden',
                    bgcolor: alpha(theme.palette.primary.light, 0.05)
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Meilleurs clients
                      </Typography>
                    }
                    action={
                      <Tooltip title="Exporter les données">
                        <IconButton onClick={() => exportToCSV('clients')}>
                          <FileDownloadIcon />
                        </IconButton>
                      </Tooltip>
                    }
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ p: 0 }}>
                    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {tableData.topClients && tableData.topClients.length > 0 ? (
                        <Box sx={{ p: 2 }}>
                          {tableData.topClients.map((client, index) => (
                            <Box
                              key={index}
                              sx={{
                                p: 2,
                                borderBottom: index < tableData.topClients.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                                '&:hover': {
                                  bgcolor: alpha(theme.palette.primary.main, 0.05)
                                }
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="subtitle1" fontWeight="medium">
                                  {client.nom}
                                </Typography>
                                <Typography variant="body2" fontWeight="bold">
                                  {formatCurrency(client.montantTotal)} DT
                                </Typography>
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                  {client.email}
                                </Typography>
                                <Chip
                                  label={`${client.facturesCount} factures`}
                                  size="small"
                                  color="primary"
                                  sx={{ height: 24, fontSize: '0.7rem' }}
                                />
                              </Box>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{
                          height: 200,
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          bgcolor: 'background.paper',
                          borderRadius: 1,
                          p: 2
                        }}>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Aucun client à afficher
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Notifications */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
            {notification.message}
          </Alert>
        </Snackbar>

        {/* Export Dialog */}
        <ExportDialog />
      </Container>
    </Box>
  );
}

export default VendeurBIDashboard;
