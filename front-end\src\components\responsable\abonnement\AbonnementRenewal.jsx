import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
  Alert,
  Snackbar,
  CircularProgress,
  useTheme
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  Autorenew as AutorenewIcon,
  Send as SendIcon
} from '@mui/icons-material';
import abonnementService from '../../../services/abonnementService';

const AbonnementRenewal = () => {
  const theme = useTheme();
  const [duree, setDuree] = useState('1_AN');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  const handleDureeChange = (event) => {
    setDuree(event.target.value);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await abonnementService.demanderRenouvellement({ duree });
      setSuccess(true);
      setSnackbarMessage('Votre demande de renouvellement a été envoyée avec succès');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    } catch (err) {
      console.error('Erreur lors de la demande de renouvellement:', err);
      setError(err.message || 'Une erreur est survenue lors de la demande de renouvellement');
      setSnackbarMessage(err.message || 'Une erreur est survenue lors de la demande de renouvellement');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Card
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 3,
          borderRadius: 2,
          boxShadow: theme.shadows[3],
          overflow: 'hidden'
        }}
      >
        <Box
          sx={{
            p: 2,
            backgroundColor: theme.palette.primary.light,
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <AutorenewIcon sx={{ color: theme.palette.primary.dark, mr: 1 }} />
          <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: theme.palette.primary.dark }}>
            Demander un renouvellement
          </Typography>
        </Box>
        <CardContent>
          {success ? (
            <Alert severity="success" sx={{ mb: 2 }}>
              Votre demande de renouvellement a été envoyée avec succès. Un administrateur la traitera prochainement.
            </Alert>
          ) : (
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="body1" paragraph>
                    Choisissez la durée souhaitée pour le renouvellement de votre abonnement.
                    Une fois votre demande envoyée, un administrateur la traitera dans les plus brefs délais.
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="duree-label">Durée de renouvellement</InputLabel>
                    <Select
                      labelId="duree-label"
                      id="duree"
                      value={duree}
                      label="Durée de renouvellement"
                      onChange={handleDureeChange}
                    >
                      <MenuItem value="10_MIN">10 minutes (test)</MenuItem>
                      <MenuItem value="1_JOUR">1 jour</MenuItem>
                      <MenuItem value="7_JOURS">7 jours</MenuItem>
                      <MenuItem value="3_MOIS">3 mois</MenuItem>
                      <MenuItem value="6_MOIS">6 mois</MenuItem>
                      <MenuItem value="1_AN">1 an</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                    sx={{ mt: 2 }}
                  >
                    {loading ? 'Envoi en cours...' : 'Envoyer la demande'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          )}
        </CardContent>
      </Card>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AbonnementRenewal;
