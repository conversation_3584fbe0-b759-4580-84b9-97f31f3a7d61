const mongoose = require('mongoose');
const BaseTemplate = require('../models/BaseTemplateModel');

// Connect to MongoDB
require('../connectdb.js');

const defaultTemplates = [
  // Facture Templates
  {
    name: 'Facture Standard',
    type: 'facture',
    layout: 'standard',
    isActive: true
  },
  {
    name: 'Facture Moderne',
    type: 'facture',
    layout: 'moderne',
    isActive: true
  },
  // Devis Templates
  {
    name: 'Devis Standard',
    type: 'devis',
    layout: 'standard',
    isActive: true
  },
  {
    name: 'Devis Moderne',
    type: 'devis',
    layout: 'moderne',
    isActive: true
  }
];

const seedBaseTemplates = async () => {
  try {
    console.log('🌱 Starting simplified base templates seeding...');

    // Clear existing templates for fresh start
    await BaseTemplate.deleteMany({});
    console.log('🗑️ Cleared existing base templates');

    // Create default templates
    const createdTemplates = await BaseTemplate.insertMany(defaultTemplates);

    console.log('🎉 Base templates seeding completed successfully!');
    console.log(`📊 Created ${createdTemplates.length} base templates:`);

    createdTemplates.forEach(template => {
      console.log(`   ✅ ${template.name} (${template.type} - ${template.layout})`);
    });

    console.log('\n📋 Template Summary:');
    console.log('   • Standard: Logo à droite, en-têtes colorés, tableaux rayés');
    console.log('   • Moderne: Logo à gauche, design minimal, tableaux bordés');

  } catch (error) {
    console.error('❌ Error seeding base templates:', error);
  } finally {
    // Close the connection
    mongoose.connection.close();
  }
};

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedBaseTemplates();
}

module.exports = seedBaseTemplates;
