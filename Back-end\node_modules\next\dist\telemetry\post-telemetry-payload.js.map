{"version": 3, "sources": ["../../src/telemetry/post-telemetry-payload.ts"], "sourcesContent": ["import retry from 'next/dist/compiled/async-retry'\n\ninterface Payload {\n  meta: { [key: string]: unknown }\n\n  context: {\n    anonymousId: string\n    projectId: string\n    sessionId: string\n  }\n\n  events: Array<{\n    eventName: string\n    fields: object\n  }>\n}\n\nexport function postNextTelemetryPayload(payload: Payload, signal?: any) {\n  if (!signal && 'timeout' in AbortSignal) {\n    signal = AbortSignal.timeout(5000)\n  }\n  return (\n    retry(\n      () =>\n        fetch('https://telemetry.nextjs.org/api/v1/record', {\n          method: 'POST',\n          body: JSON.stringify(payload),\n          headers: { 'content-type': 'application/json' },\n          signal,\n        }).then((res) => {\n          if (!res.ok) {\n            const err = new Error(res.statusText)\n            ;(err as any).response = res\n            throw err\n          }\n        }),\n      { minTimeout: 500, retries: 1, factor: 1 }\n    )\n      .catch(() => {\n        // We swallow errors when telemetry cannot be sent\n      })\n      // Ensure promise is voided\n      .then(\n        () => {},\n        () => {}\n      )\n  )\n}\n"], "names": ["postNextTelemetryPayload", "payload", "signal", "AbortSignal", "timeout", "retry", "fetch", "method", "body", "JSON", "stringify", "headers", "then", "res", "ok", "err", "Error", "statusText", "response", "minTimeout", "retries", "factor", "catch"], "mappings": ";;;;+BAiBgBA;;;eAAAA;;;mEAjBE;;;;;;AAiBX,SAASA,yBAAyBC,OAAgB,EAAEC,MAAY;IACrE,IAAI,CAACA,UAAU,aAAaC,aAAa;QACvCD,SAASC,YAAYC,OAAO,CAAC;IAC/B;IACA,OACEC,IAAAA,mBAAK,EACH,IACEC,MAAM,8CAA8C;YAClDC,QAAQ;YACRC,MAAMC,KAAKC,SAAS,CAACT;YACrBU,SAAS;gBAAE,gBAAgB;YAAmB;YAC9CT;QACF,GAAGU,IAAI,CAAC,CAACC;YACP,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAMC,MAAM,qBAAyB,CAAzB,IAAIC,MAAMH,IAAII,UAAU,GAAxB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwB;gBAClCF,IAAYG,QAAQ,GAAGL;gBACzB,MAAME;YACR;QACF,IACF;QAAEI,YAAY;QAAKC,SAAS;QAAGC,QAAQ;IAAE,GAExCC,KAAK,CAAC;IACL,kDAAkD;IACpD,EACA,2BAA2B;KAC1BV,IAAI,CACH,KAAO,GACP,KAAO;AAGf"}