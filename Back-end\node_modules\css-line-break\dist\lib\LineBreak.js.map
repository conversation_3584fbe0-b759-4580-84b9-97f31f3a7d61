{"version": 3, "file": "LineBreak.js", "sourceRoot": "", "sources": ["../../src/LineBreak.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,+BAA2C;AAC3C,mDAAoD;AACpD,+BAAmD;AAEtC,QAAA,sBAAsB,GAAG,EAAE,CAAC;AAEzC,uCAAuC;AACvC,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,8BAA8B;AAC5C,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,wDAAwD;AACtE,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,8BAA8B;AAC5C,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,2EAA2E;AACzF,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,8BAA8B;AAC5C,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,oCAAoC;AAClD,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,yCAAyC;AACvD,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,+BAA+B;AAC7C,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,yCAAyC;AACvD,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,8BAA8B;AAC7C,IAAM,GAAG,GAAG,EAAE,CAAC,CAAC,+CAA+C;AAC/D,sBAAsB;AACtB,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,mEAAmE;AAClF,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,kEAAkE;AACjF,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,mEAAmE;AAClF,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,mFAAmF;AAClG,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,0EAA0E;AACzF,wCAAwC;AACxC,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,iDAAiD;AAChE,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,0CAA0C;AACzD,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,8BAA8B;AAC7C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,8CAA8C;AAC7D,kBAAkB;AAClB,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+CAA+C;AAC9D,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,uDAAuD;AACtE,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+CAA+C;AAC9D,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,iDAAiD;AAChE,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,mDAAmD;AAClE,mBAAmB;AACnB,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+DAA+D;AAC9E,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,iFAAiF;AAChG,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,oDAAoD;AACnE,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,8CAA8C;AAC7D,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,0CAA0C;AACzD,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,uEAAuE;AACtF,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,yDAAyD;AACxE,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,+BAA+B;AAC9C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,wEAAwE;AACvF,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,iGAAiG;AAChH,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,2EAA2E;AAE1F,IAAM,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAElB,QAAA,OAAO,GAA4B;IAC5C,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,GAAG,KAAA;IACH,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;IACF,EAAE,IAAA;CACL,CAAC;AAEW,QAAA,eAAe,GAAG,GAAG,CAAC;AACtB,QAAA,iBAAiB,GAAG,GAAG,CAAC;AACxB,QAAA,aAAa,GAAG,GAAG,CAAC;AACpB,QAAA,WAAW,GAAG,4BAAoB,CAAC,uBAAM,EAAE,2BAAU,CAAC,CAAC;AAEpE,IAAM,WAAW,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC7B,IAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,IAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACvB,IAAM,cAAc,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,IAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnD,IAAM,qBAAqB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACnD,IAAM,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAEjB,IAAM,4BAA4B,GAAG,UACxC,UAAoB,EACpB,SAA4B;IAA5B,0BAAA,EAAA,oBAA4B;IAE5B,IAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,IAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAM,UAAU,GAAc,EAAE,CAAC;IACjC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;QAChC,IAAI,SAAS,GAAG,mBAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,SAAS,GAAG,8BAAsB,EAAE;YACpC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,SAAS,IAAI,8BAAsB,CAAC;SACvC;aAAM;YACH,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YACvD,uCAAuC;YACvC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5D,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzB;SACJ;QAED,IAAI,SAAS,KAAK,EAAE,IAAI,SAAS,KAAK,GAAG,EAAE;YACvC,wDAAwD;YACxD,IAAI,KAAK,KAAK,CAAC,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzB;YAED,oGAAoG;YACpG,gFAAgF;YAChF,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC9B,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBAClC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3B;YACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACzB;QAED,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEpB,IAAI,SAAS,KAAK,EAAE,EAAE;YAClB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACvD;QAED,IAAI,SAAS,KAAK,EAAE,EAAE;YAClB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACzB;QAED,IAAI,SAAS,KAAK,EAAE,EAAE;YAClB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACzB;QAED,wGAAwG;QACxG,4GAA4G;QAC5G,iFAAiF;QACjF,IAAI,SAAS,KAAK,EAAE,EAAE;YAClB,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,OAAO,CAAC,EAAE;gBAClG,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzB;iBAAM;gBACH,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzB;SACJ;QAED,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACxC,CAAC,CAAC;AAvEW,QAAA,4BAA4B,gCAuEvC;AAEF,IAAM,0BAA0B,GAAG,UAC/B,CAAoB,EACpB,CAAS,EACT,YAAoB,EACpB,UAAoB;IAEpB,IAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;IACzC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;QAC9D,IAAI,CAAC,GAAG,YAAY,CAAC;QACrB,OAAO,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE;YAC3B,CAAC,EAAE,CAAC;YACJ,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAEzB,IAAI,IAAI,KAAK,CAAC,EAAE;gBACZ,OAAO,IAAI,CAAC;aACf;YAED,IAAI,IAAI,KAAK,EAAE,EAAE;gBACb,MAAM;aACT;SACJ;KACJ;IAED,IAAI,OAAO,KAAK,EAAE,EAAE;QAChB,IAAI,CAAC,GAAG,YAAY,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,EAAE;YACV,CAAC,EAAE,CAAC;YACJ,IAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAE3B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBACxD,IAAI,CAAC,GAAG,YAAY,CAAC;gBACrB,OAAO,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE;oBAC3B,CAAC,EAAE,CAAC;oBACJ,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAEzB,IAAI,IAAI,KAAK,CAAC,EAAE;wBACZ,OAAO,IAAI,CAAC;qBACf;oBAED,IAAI,IAAI,KAAK,EAAE,EAAE;wBACb,MAAM;qBACT;iBACJ;aACJ;YAED,IAAI,IAAI,KAAK,EAAE,EAAE;gBACb,MAAM;aACT;SACJ;KACJ;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,IAAM,yBAAyB,GAAG,UAAC,YAAoB,EAAE,UAAoB;IACzE,IAAI,CAAC,GAAG,YAAY,CAAC;IACrB,OAAO,CAAC,IAAI,CAAC,EAAE;QACX,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,IAAI,KAAK,EAAE,EAAE;YACb,CAAC,EAAE,CAAC;SACP;aAAM;YACH,OAAO,IAAI,CAAC;SACf;KACJ;IACD,OAAO,CAAC,CAAC;AACb,CAAC,CAAC;AAIF,IAAM,iBAAiB,GAAG,UACtB,UAAoB,EACpB,UAAoB,EACpB,QAAkB,EAClB,KAAa,EACb,eAA2B;IAE3B,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAiB,CAAC;KAC5B;IAED,IAAI,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;IAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;QAC1E,OAAO,yBAAiB,CAAC;KAC5B;IAED,IAAI,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC;IACnC,IAAI,UAAU,GAAG,YAAY,GAAG,CAAC,CAAC;IAClC,IAAI,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;IAEvC,2CAA2C;IAC3C,8EAA8E;IAC9E,IAAI,MAAM,GAAG,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;IAElC,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,OAAO,yBAAiB,CAAC;KAC5B;IAED,IAAI,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1C,OAAO,uBAAe,CAAC;KAC1B;IAED,4CAA4C;IAC5C,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,yBAAiB,CAAC;KAC5B;IAED,sDAAsD;IACtD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC5B,OAAO,yBAAiB,CAAC;KAC5B;IAED,qGAAqG;IACrG,IAAI,yBAAyB,CAAC,YAAY,EAAE,UAAU,CAAC,KAAK,EAAE,EAAE;QAC5D,OAAO,qBAAa,CAAC;KACxB;IAED,+CAA+C;IAC/C,IAAI,mBAAW,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,GAAG,EAAE;QACnD,OAAO,yBAAiB,CAAC;KAC5B;IAED,aAAa;IACb,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,EAAE,CAAC,IAAI,mBAAW,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,KAAK,GAAG,EAAE;QACvF,OAAO,yBAAiB,CAAC;KAC5B;IAED,wEAAwE;IACxE,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,OAAO,yBAAiB,CAAC;KAC5B;IAED,uDAAuD;IACvD,IAAI,OAAO,KAAK,EAAE,EAAE;QAChB,OAAO,yBAAiB,CAAC;KAC5B;IAED,0FAA0F;IAC1F,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,EAAE;QACrD,OAAO,yBAAiB,CAAC;KAC5B;IAED,wEAAwE;IACxE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3C,OAAO,yBAAiB,CAAC;KAC5B;IAED,kDAAkD;IAClD,IAAI,yBAAyB,CAAC,YAAY,EAAE,UAAU,CAAC,KAAK,EAAE,EAAE;QAC5D,OAAO,yBAAiB,CAAC;KAC5B;IAED,+DAA+D;IAC/D,IAAI,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;QAC9D,OAAO,yBAAiB,CAAC;KAC5B;IAED,wGAAwG;IACxG,IAAI,0BAA0B,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;QACpE,OAAO,yBAAiB,CAAC;KAC5B;IAED,+DAA+D;IAC/D,IAAI,0BAA0B,CAAC,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;QAC9D,OAAO,yBAAiB,CAAC;KAC5B;IAED,2BAA2B;IAC3B,IAAI,OAAO,KAAK,EAAE,EAAE;QAChB,OAAO,qBAAa,CAAC;KACxB;IAED,oEAAoE;IACpE,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,OAAO,yBAAiB,CAAC;KAC5B;IAED,6CAA6C;IAC7C,IAAI,IAAI,KAAK,EAAE,IAAI,OAAO,KAAK,EAAE,EAAE;QAC/B,OAAO,qBAAa,CAAC;KACxB;IAED,wIAAwI;IACxI,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,EAAE;QACrD,OAAO,yBAAiB,CAAC;KAC5B;IAED,2CAA2C;IAC3C,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QACjD,OAAO,yBAAiB,CAAC;KAC5B;IAED,wDAAwD;IACxD,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,OAAO,yBAAiB,CAAC;KAC5B;IAED,qCAAqC;IACrC,IAAI,IAAI,KAAK,EAAE,EAAE;QACb,OAAO,yBAAiB,CAAC;KAC5B;IAED,gDAAgD;IAChD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC,EAAE;QAC9G,OAAO,yBAAiB,CAAC;KAC5B;IAED,2GAA2G;IAC3G,IACI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC,EACvD;QACE,OAAO,yBAAiB,CAAC;KAC5B;IAED,uGAAuG;IACvG,IACI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5E,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAC9E;QACE,OAAO,yBAAiB,CAAC;KAC5B;IAED,gFAAgF;IAChF;IACI,8BAA8B;IAC9B,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1F,mBAAmB;QACnB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;QACjD,sBAAsB;QACtB,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACvD;QACE,OAAO,yBAAiB,CAAC;KAC5B;IAED,gDAAgD;IAChD,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3C,IAAI,SAAS,GAAG,YAAY,CAAC;QAC7B,OAAO,SAAS,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,IAAI,KAAK,EAAE,EAAE;gBACb,OAAO,yBAAiB,CAAC;aAC5B;iBAAM,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,SAAS,EAAE,CAAC;aACf;iBAAM;gBACH,MAAM;aACT;SACJ;KACJ;IAED,6CAA6C;IAC7C,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/B,IAAI,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC;QAC9E,OAAO,SAAS,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,IAAI,KAAK,EAAE,EAAE;gBACb,OAAO,yBAAiB,CAAC;aAC5B;iBAAM,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,SAAS,EAAE,CAAC;aACf;iBAAM;gBACH,MAAM;aACT;SACJ;KACJ;IAED,uCAAuC;IACvC,IACI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC,EACnD;QACE,OAAO,yBAAiB,CAAC;KAC5B;IAED,qDAAqD;IACrD,IACI,CAAC,qBAAqB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAChF,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,CAAC,EAChE;QACE,OAAO,yBAAiB,CAAC;KAC5B;IAED,gDAAgD;IAChD,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACzE,OAAO,yBAAiB,CAAC;KAC5B;IAED,0EAA0E;IAC1E,IAAI,OAAO,KAAK,EAAE,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACpD,OAAO,yBAAiB,CAAC;KAC5B;IAED,sGAAsG;IACtG,IACI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,KAAK,EAAE;QACX,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,CAAC,EACjE;QACE,OAAO,yBAAiB,CAAC;KAC5B;IAED,yGAAyG;IACzG,kDAAkD;IAClD,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC/B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,EAAE;YACV,CAAC,EAAE,CAAC;YACJ,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;gBACtB,KAAK,EAAE,CAAC;aACX;iBAAM;gBACH,MAAM;aACT;SACJ;QACD,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;YACjB,OAAO,yBAAiB,CAAC;SAC5B;KACJ;IAED,kEAAkE;IAClE,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,OAAO,yBAAiB,CAAC;KAC5B;IAED,OAAO,qBAAa,CAAC;AACzB,CAAC,CAAC;AAEK,IAAM,gBAAgB,GAAG,UAAC,UAAoB,EAAE,KAAa;IAChE,wCAAwC;IACxC,IAAI,KAAK,KAAK,CAAC,EAAE;QACb,OAAO,yBAAiB,CAAC;KAC5B;IAED,uCAAuC;IACvC,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;QAC5B,OAAO,uBAAe,CAAC;KAC1B;IAEK,IAAA,KAAwB,oCAA4B,CAAC,UAAU,CAAC,EAA/D,OAAO,QAAA,EAAE,UAAU,QAA4C,CAAC;IAEvE,OAAO,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACrE,CAAC,CAAC;AAdW,QAAA,gBAAgB,oBAc3B;AAUF,IAAM,mBAAmB,GAAG,UAAC,UAAoB,EAAE,OAAkB;IACjE,IAAI,CAAC,OAAO,EAAE;QACV,OAAO,GAAG,EAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAC,CAAC;KACxD;IACG,IAAA,KAAyC,oCAA4B,CAAC,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,EAAnG,QAAQ,QAAA,EAAE,UAAU,QAAA,EAAE,cAAc,QAA+D,CAAC;IAEzG,IAAI,OAAO,CAAC,SAAS,KAAK,WAAW,IAAI,OAAO,CAAC,SAAS,KAAK,YAAY,EAAE;QACzE,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAA/C,CAA+C,CAAC,CAAC;KAC1F;IAED,IAAM,oBAAoB,GACtB,OAAO,CAAC,SAAS,KAAK,UAAU;QAC5B,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,UAAC,YAAY,EAAE,CAAC;YAC/B,OAAO,YAAY,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;QAC9E,CAAC,CAAC;QACJ,CAAC,CAAC,SAAS,CAAC;IAEpB,OAAO,CAAC,QAAQ,EAAE,UAAU,EAAE,oBAAoB,CAAC,CAAC;AACxD,CAAC,CAAC;AAEK,IAAM,wBAAwB,GAAG,UAAC,GAAW,EAAE,OAAkB;IACpE,IAAM,UAAU,GAAG,mBAAY,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,MAAM,GAAG,yBAAiB,CAAC;IACzB,IAAA,KAA+C,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,EAAtF,QAAQ,QAAA,EAAE,UAAU,QAAA,EAAE,oBAAoB,QAA4C,CAAC;IAE9F,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,CAAC;QAC5B,MAAM;YACF,oBAAa,CAAC,SAAS,CAAC;gBACxB,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC;oBACvB,CAAC,CAAC,uBAAe;oBACjB,CAAC,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAChG,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAdW,QAAA,wBAAwB,4BAcnC;AAEF;IAMI,eAAY,UAAoB,EAAE,SAAiB,EAAE,KAAa,EAAE,GAAW;QAC3E,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,SAAS,KAAK,uBAAe,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACnB,CAAC;IAED,qBAAK,GAAL;QACI,OAAO,oBAAa,eAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;IACzE,CAAC;IACL,YAAC;AAAD,CAAC,AAhBD,IAgBC;AAgBM,IAAM,WAAW,GAAG,UAAC,GAAW,EAAE,OAAkB;IACvD,IAAM,UAAU,GAAG,mBAAY,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAA,KAA+C,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,EAAtF,QAAQ,QAAA,EAAE,UAAU,QAAA,EAAE,oBAAoB,QAA4C,CAAC;IAC9F,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IACjC,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,OAAO;QACH,IAAI,EAAE;YACF,IAAI,SAAS,IAAI,MAAM,EAAE;gBACrB,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;aACpC;YACD,IAAI,SAAS,GAAG,yBAAiB,CAAC;YAClC,OACI,SAAS,GAAG,MAAM;gBAClB,CAAC,SAAS,GAAG,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,oBAAoB,CAAC,CAAC;oBAChG,yBAAiB,EACvB,GAAE;YAEJ,IAAI,SAAS,KAAK,yBAAiB,IAAI,SAAS,KAAK,MAAM,EAAE;gBACzD,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACnE,OAAO,GAAG,SAAS,CAAC;gBACpB,OAAO,EAAC,KAAK,OAAA,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;aAC/B;YAED,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;QACrC,CAAC;KACJ,CAAC;AACN,CAAC,CAAC;AA5BW,QAAA,WAAW,eA4BtB"}