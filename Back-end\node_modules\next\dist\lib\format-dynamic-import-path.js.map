{"version": 3, "sources": ["../../src/lib/format-dynamic-import-path.ts"], "sourcesContent": ["import path from 'path'\nimport { pathToFileURL } from 'url'\n\n/**\n * The path for a dynamic route must be URLs with a valid scheme.\n *\n * When an absolute Windows path is passed to it, it interprets the beginning of the path as a protocol (`C:`).\n * Therefore, it is important to always construct a complete path.\n * @param dir File directory\n * @param filePath Absolute or relative path\n */\nexport const formatDynamicImportPath = (dir: string, filePath: string) => {\n  const absoluteFilePath = path.isAbsolute(filePath)\n    ? filePath\n    : path.join(dir, filePath)\n  const formattedFilePath = pathToFileURL(absoluteFilePath).toString()\n\n  return formattedFilePath\n}\n"], "names": ["formatDynamicImportPath", "dir", "filePath", "absoluteFilePath", "path", "isAbsolute", "join", "formattedFilePath", "pathToFileURL", "toString"], "mappings": ";;;;+BAWaA;;;eAAAA;;;6DAXI;qBACa;;;;;;AAUvB,MAAMA,0BAA0B,CAACC,KAAaC;IACnD,MAAMC,mBAAmBC,aAAI,CAACC,UAAU,CAACH,YACrCA,WACAE,aAAI,CAACE,IAAI,CAACL,KAAKC;IACnB,MAAMK,oBAAoBC,IAAAA,kBAAa,EAACL,kBAAkBM,QAAQ;IAElE,OAAOF;AACT"}