import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Avatar,
  Divider,
  Grid,
  Paper,
  CircularProgress,
  IconButton,
  Alert,
  Snackbar,
  Container,
  Chip,
  InputAdornment,
  useTheme,
  alpha,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  PhotoCamera,
  Save,
  Person,
  Email,
  Phone,
  Business,
  LocationOn,
  Edit,
  Lock,
  Badge,
  Security,
  AccountCircle,
  CheckCircle
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/authService';
import clientService from '../services/clientService';

const ClientProfile = () => {
  const theme = useTheme();
  const { currentUser, refreshUserProfile } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [clientData, setClientData] = useState(null);
  const [profileData, setProfileData] = useState({
    nom: '',
    email: '',
    telephone: '',
    adresse: '',
    contact: ''
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [changePasswordOpen, setChangePasswordOpen] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Chargement des données du profil client...');

        // Récupérer les données du client
        if (currentUser && currentUser.clientId) {
          // Extraire l'ID client, qui peut être un objet ou une chaîne
          let clientId;
          if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
            clientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
            console.log('clientId est un objet avec _id:', clientId);
          } else {
            clientId = currentUser.clientId ? currentUser.clientId.toString() : '';
            console.log('clientId est une chaîne ou un ID:', clientId);
          }

          if (clientId) {
            console.log('Récupération des données du client avec ID:', clientId);
            const clientResponse = await clientService.getClientById(clientId);
            console.log('Données du client récupérées:', clientResponse);
            setClientData(clientResponse);

            // Pré-remplir le formulaire avec les données du client
            const profileFormData = {
              nom: clientResponse.nom || '',
              email: clientResponse.email || '',
              telephone: clientResponse.telephone || '',
              adresse: clientResponse.adresse || '',
              contact: clientResponse.contact || ''
            };
            console.log('Données du formulaire pré-remplies:', profileFormData);
            setProfileData(profileFormData);
          } else {
            console.warn('ID client invalide:', currentUser.clientId);
            enqueueSnackbar('ID client invalide', { variant: 'error' });
          }
        } else {
          console.warn('Aucun ID client trouvé dans les données utilisateur:', currentUser);
        }

        setLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        console.error('Détails de l\'erreur:', error.response?.data || error.message);
        enqueueSnackbar('Erreur lors du chargement des données', { variant: 'error' });
        setLoading(false);
      }
    };

    if (currentUser) {
      fetchData();
    } else {
      console.warn('Aucun utilisateur connecté');
    }
  }, [currentUser, enqueueSnackbar]);

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveProfile = async () => {
    try {
      setSaving(true);
      console.log('Début de la mise à jour du profil');
      console.log('Données du client:', clientData);
      console.log('Données du profil à envoyer:', {
        nom: profileData.nom,
        email: profileData.email,
        telephone: profileData.telephone,
        adresse: profileData.adresse,
        contact: profileData.contact
      });

      // Mettre à jour les données du client via l'API
      // Cette fonction est appelée par le bouton "Enregistrer les modifications" en haut à droite
      if (clientData) {
        console.log('ID du client à mettre à jour:', clientData._id);

        const updatedClientData = await clientService.updateClient(clientData._id, {
          nom: profileData.nom,
          email: profileData.email,
          telephone: profileData.telephone,
          adresse: profileData.adresse,
          contact: profileData.contact
        });

        console.log('Réponse de la mise à jour:', updatedClientData);

        // Rafraîchir les données du client après la mise à jour
        // Utiliser l'ID du client qui vient d'être mis à jour au lieu de currentUser.clientId
        console.log('Récupération des données mises à jour du client avec ID:', clientData._id);
        const updatedClient = await clientService.getClientById(clientData._id);
        console.log('Données mises à jour du client:', updatedClient);

        // Mettre à jour les données du client dans l'état
        setClientData(updatedClient);

        // Mettre à jour également les données du formulaire pour refléter les changements
        setProfileData({
          nom: updatedClient.nom || '',
          email: updatedClient.email || '',
          telephone: updatedClient.telephone || '',
          adresse: updatedClient.adresse || '',
          contact: updatedClient.contact || ''
        });

        // Afficher une notification de succès
        enqueueSnackbar('Profil mis à jour avec succès', { variant: 'success' });
      } else {
        console.error('Aucune donnée client disponible pour la mise à jour');
        enqueueSnackbar('Erreur: Aucune donnée client disponible', { variant: 'error' });
      }

      setSaving(false);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      console.error('Détails de l\'erreur:', error.response?.data || error.message);
      enqueueSnackbar('Erreur lors de la mise à jour du profil', { variant: 'error' });
      setSaving(false);
    }
  };

  const handleOpenChangePassword = () => {
    setChangePasswordOpen(true);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordError('');
  };

  const handleCloseChangePassword = () => {
    setChangePasswordOpen(false);
  };

  const handleChangePassword = async () => {
    try {
      setPasswordError('');

      // Vérifier que les mots de passe correspondent
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        setPasswordError('Les mots de passe ne correspondent pas');
        return;
      }

      // Vérifier que le mot de passe est assez long
      if (passwordData.newPassword.length < 6) {
        setPasswordError('Le mot de passe doit contenir au moins 6 caractères');
        return;
      }

      // Changer le mot de passe
      await authService.changePassword(
        passwordData.currentPassword,
        passwordData.newPassword
      );

      setChangePasswordOpen(false);
      enqueueSnackbar('Mot de passe changé avec succès', { variant: 'success' });
    } catch (error) {
      console.error('Erreur lors du changement de mot de passe:', error);
      setPasswordError(error.response?.data?.error || 'Erreur lors du changement de mot de passe');
    }
  };

  const handleUploadProfileImage = async (e) => {
    try {
      const file = e.target.files[0];
      if (!file) return;

      await authService.uploadProfileImage(file);
      await refreshUserProfile();

      enqueueSnackbar('Photo de profil mise à jour avec succès', { variant: 'success' });
    } catch (error) {
      console.error('Erreur lors du téléchargement de l\'image:', error);
      enqueueSnackbar('Erreur lors du téléchargement de l\'image', { variant: 'error' });
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <Container maxWidth="lg">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 4,
          flexWrap: 'wrap',
          gap: 2
        }}>
          <motion.div variants={itemVariants}>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 700,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: { xs: 1, md: 0 }
              }}
            >
              Mon Profil Client
            </Typography>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Button
              variant="contained"
              color="primary"
              startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <Save />}
              onClick={handleSaveProfile}
              disabled={saving}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                boxShadow: 4,
                transition: 'all 0.3s ease'
              }}
            >
              {saving ? 'Enregistrement...' : 'Enregistrer les modifications'}
            </Button>
          </motion.div>
        </Box>

        <Grid container spacing={3}>
          {/* Informations personnelles */}
          <Grid item xs={12} md={8} component={motion.div} variants={itemVariants}>
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                overflow: 'hidden',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                      mr: 2,
                      width: 48,
                      height: 48,
                      boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                    }}
                  >
                    <Person />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Informations personnelles
                  </Typography>
                </Box>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Nom"
                    name="nom"
                    value={profileData.nom}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Badge fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    name="email"
                    value={profileData.email}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Téléphone"
                    name="telephone"
                    value={profileData.telephone}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Phone fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Adresse"
                    name="adresse"
                    value={profileData.adresse}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationOn fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Contact"
                    name="contact"
                    value={profileData.contact}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>


            </CardContent>
          </Card>
        </Grid>

        {/* Photo de profil et sécurité */}
        <Grid item xs={12} md={4} component={motion.div} variants={itemVariants}>
          <Card
            elevation={0}
            sx={{
              borderRadius: 3,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
              mb: 3,
              overflow: 'hidden',
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '4px',
                background: `linear-gradient(90deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})`
              }
            }}
          >
            <CardContent sx={{ textAlign: 'center', position: 'relative' }}>
              <Box
                sx={{
                  position: 'absolute',
                  top: -30,
                  left: -30,
                  width: 200,
                  height: 200,
                  borderRadius: '50%',
                  background: alpha(theme.palette.secondary.main, 0.03),
                  zIndex: 0
                }}
              />
              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, justifyContent: 'center' }}>
                  <Avatar
                    sx={{
                      bgcolor: alpha(theme.palette.secondary.main, 0.1),
                      color: theme.palette.secondary.main,
                      mr: 1.5,
                      width: 36,
                      height: 36,
                      boxShadow: `0 4px 12px ${alpha(theme.palette.secondary.main, 0.2)}`
                    }}
                  >
                    <AccountCircle />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Photo de profil
                  </Typography>
                </Box>

                <Box sx={{ position: 'relative', display: 'inline-block' }}>
                  <Avatar
                    src={currentUser?.profileImage}
                    alt={currentUser?.nom}
                    sx={{
                      width: 120,
                      height: 120,
                      mx: 'auto',
                      mb: 2,
                      boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                      border: `4px solid ${alpha(theme.palette.background.paper, 0.8)}`
                    }}
                  />
                  <label htmlFor="profile-image-upload">
                    <input
                      accept="image/*"
                      id="profile-image-upload"
                      type="file"
                      style={{ display: 'none' }}
                      onChange={handleUploadProfileImage}
                    />
                    <IconButton
                      component="span"
                      sx={{
                        position: 'absolute',
                        bottom: 10,
                        right: 0,
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          bgcolor: theme.palette.primary.dark,
                          transform: 'scale(1.05)'
                        }
                      }}
                    >
                      <PhotoCamera />
                    </IconButton>
                  </label>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
                  Cliquez sur l'icône pour changer votre photo de profil
                </Typography>
              </Box>
            </CardContent>
          </Card>

          <Card
            elevation={0}
            sx={{
              borderRadius: 3,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
              overflow: 'hidden',
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '4px',
                background: `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    bgcolor: alpha(theme.palette.error.main, 0.1),
                    color: theme.palette.error.main,
                    mr: 2,
                    width: 48,
                    height: 48,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.2)}`
                  }}
                >
                  <Security />
                </Avatar>
                <Typography variant="h6" fontWeight="bold">
                  Sécurité
                </Typography>
              </Box>

              <Button
                fullWidth
                variant="contained"
                color="error"
                startIcon={<Lock />}
                onClick={handleOpenChangePassword}
                sx={{
                  mb: 2,
                  borderRadius: 2,
                  py: 1.2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.3)}`,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: `0 6px 16px ${alpha(theme.palette.error.main, 0.4)}`
                  }
                }}
              >
                Changer le mot de passe
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Dialogue de changement de mot de passe */}
      <Dialog
        open={changePasswordOpen}
        onClose={handleCloseChangePassword}
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            maxWidth: 500
          }
        }}
      >
        <DialogTitle sx={{
          pb: 1,
          pt: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 1.5
        }}>
          <Avatar sx={{
            bgcolor: alpha(theme.palette.error.main, 0.1),
            color: theme.palette.error.main
          }}>
            <Lock />
          </Avatar>
          <Typography variant="h5" fontWeight="bold">
            Changer le mot de passe
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          {passwordError && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
              }}
            >
              {passwordError}
            </Alert>
          )}

          <TextField
            fullWidth
            margin="dense"
            label="Mot de passe actuel"
            type="password"
            name="currentPassword"
            value={passwordData.currentPassword}
            onChange={handlePasswordChange}
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock fontSize="small" color="action" />
                </InputAdornment>
              ),
            }}
          />

          <TextField
            fullWidth
            margin="dense"
            label="Nouveau mot de passe"
            type="password"
            name="newPassword"
            value={passwordData.newPassword}
            onChange={handlePasswordChange}
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Security fontSize="small" color="action" />
                </InputAdornment>
              ),
            }}
          />

          <TextField
            fullWidth
            margin="dense"
            label="Confirmer le nouveau mot de passe"
            type="password"
            name="confirmPassword"
            value={passwordData.confirmPassword}
            onChange={handlePasswordChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <CheckCircle fontSize="small" color="action" />
                </InputAdornment>
              ),
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseChangePassword}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleChangePassword}
            variant="contained"
            color="error"
            sx={{
              borderRadius: 2,
              px: 3,
              boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.3)}`,
              '&:hover': {
                boxShadow: `0 6px 16px ${alpha(theme.palette.error.main, 0.4)}`
              }
            }}
          >
            Changer
          </Button>
        </DialogActions>
      </Dialog>
      </motion.div>
    </Container>
  );
};

export default ClientProfile;
