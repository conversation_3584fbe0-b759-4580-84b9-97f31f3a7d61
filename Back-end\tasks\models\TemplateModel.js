const mongoose = require('mongoose');
const { Schema } = mongoose;

// Simplified Template Schema - Keep only essential fields
const templateSchema = new Schema({
    name: { type: String, required: true },
    type: { type: String, required: true, enum: ['facture', 'devis'] },
    layout: {
        type: String,
        enum: ['standard', 'moderne'],
        default: 'standard'
    },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

// Middleware pour mettre à jour automatiquement la date de modification
templateSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Middleware pour s'assurer qu'il n'y a qu'un seul template par défaut par type
templateSchema.pre('save', async function(next) {
  if (this.isDefault) {
    try {
      // Si ce template est défini comme défaut, désactiver l'attribut default pour les autres templates du même type
      await this.constructor.updateMany(
        { type: this.type, _id: { $ne: this._id }, isDefault: true },
        { $set: { isDefault: false } }
      );

      // Si le type est 'Both', désactiver aussi les templates par défaut pour 'Facture' et 'Devis'
      if (this.type === 'Both') {
        await this.constructor.updateMany(
          { type: { $in: ['Facture', 'Devis'] }, isDefault: true },
          { $set: { isDefault: false } }
        );
      }

      // Si le type est 'Facture' ou 'Devis', désactiver les templates par défaut pour 'Both'
      if (this.type === 'Facture' || this.type === 'Devis') {
        await this.constructor.updateMany(
          { type: 'Both', isDefault: true },
          { $set: { isDefault: false } }
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  } else {
    next();
  }
});

module.exports = mongoose.model('Template', templateSchema);