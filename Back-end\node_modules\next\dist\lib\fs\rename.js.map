{"version": 3, "sources": ["../../../src/lib/fs/rename.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) 2015 - present Microsoft Corporation\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n */\n\n// This file is based on https://github.com/microsoft/vscode/blob/f860fcf11022f10a992440fd54c6e45674e39617/src/vs/base/node/pfs.ts\n// See the LICENSE at the top of the file\n\nimport { rename as fsRename, stat } from 'node:fs/promises'\n\n/**\n * A drop-in replacement for `fs.rename` that:\n * - allows to move across multiple disks\n * - attempts to retry the operation for certain error codes on Windows\n */\nexport async function rename(\n  source: string,\n  target: string,\n  windowsRetryTimeout: number | false = 60000 /* matches graceful-fs */\n): Promise<void> {\n  if (source === target) {\n    return // simulate node.js behaviour here and do a no-op if paths match\n  }\n\n  if (process.platform === 'win32' && typeof windowsRetryTimeout === 'number') {\n    // On Windows, a rename can fail when either source or target\n    // is locked by AV software. We do leverage graceful-fs to iron\n    // out these issues, however in case the target file exists,\n    // graceful-fs will immediately return without retry for fs.rename().\n    await renameWithRetry(source, target, Date.now(), windowsRetryTimeout)\n  } else {\n    await fsRename(source, target)\n  }\n}\n\nasync function renameWithRetry(\n  source: string,\n  target: string,\n  startTime: number,\n  retryTimeout: number,\n  attempt = 0\n): Promise<void> {\n  try {\n    return await fsRename(source, target)\n  } catch (error: any) {\n    if (\n      error.code !== 'EACCES' &&\n      error.code !== 'EPERM' &&\n      error.code !== 'EBUSY'\n    ) {\n      throw error // only for errors we think are temporary\n    }\n\n    if (Date.now() - startTime >= retryTimeout) {\n      console.error(\n        `[node.js fs] rename failed after ${attempt} retries with error: ${error}`\n      )\n\n      throw error // give up after configurable timeout\n    }\n\n    if (attempt === 0) {\n      let abortRetry = false\n      try {\n        const statTarget = await stat(target)\n        if (!statTarget.isFile()) {\n          abortRetry = true // if target is not a file, EPERM error may be raised and we should not attempt to retry\n        }\n      } catch (e) {\n        // Ignore\n      }\n\n      if (abortRetry) {\n        throw error\n      }\n    }\n\n    // Delay with incremental backoff up to 100ms\n    await timeout(Math.min(100, attempt * 10))\n\n    // Attempt again\n    return renameWithRetry(source, target, startTime, retryTimeout, attempt + 1)\n  }\n}\n\nconst timeout = (millis: number) =>\n  new Promise((resolve) => setTimeout(resolve, millis))\n"], "names": ["rename", "source", "target", "windowsRetryTimeout", "process", "platform", "renameWithRetry", "Date", "now", "fsRename", "startTime", "retryTimeout", "attempt", "error", "code", "console", "abortRetry", "statTarget", "stat", "isFile", "e", "timeout", "Math", "min", "millis", "Promise", "resolve", "setTimeout"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,kIAAkI;AAClI,yCAAyC;;;;;+BASnBA;;;eAAAA;;;0BAPmB;AAOlC,eAAeA,OACpBC,MAAc,EACdC,MAAc,EACdC,sBAAsC,MAAM,uBAAuB,GAAxB;IAE3C,IAAIF,WAAWC,QAAQ;QACrB,QAAO,gEAAgE;IACzE;IAEA,IAAIE,QAAQC,QAAQ,KAAK,WAAW,OAAOF,wBAAwB,UAAU;QAC3E,6DAA6D;QAC7D,+DAA+D;QAC/D,4DAA4D;QAC5D,qEAAqE;QACrE,MAAMG,gBAAgBL,QAAQC,QAAQK,KAAKC,GAAG,IAAIL;IACpD,OAAO;QACL,MAAMM,IAAAA,gBAAQ,EAACR,QAAQC;IACzB;AACF;AAEA,eAAeI,gBACbL,MAAc,EACdC,MAAc,EACdQ,SAAiB,EACjBC,YAAoB,EACpBC,UAAU,CAAC;IAEX,IAAI;QACF,OAAO,MAAMH,IAAAA,gBAAQ,EAACR,QAAQC;IAChC,EAAE,OAAOW,OAAY;QACnB,IACEA,MAAMC,IAAI,KAAK,YACfD,MAAMC,IAAI,KAAK,WACfD,MAAMC,IAAI,KAAK,SACf;YACA,MAAMD,MAAM,yCAAyC;;QACvD;QAEA,IAAIN,KAAKC,GAAG,KAAKE,aAAaC,cAAc;YAC1CI,QAAQF,KAAK,CACX,CAAC,iCAAiC,EAAED,QAAQ,qBAAqB,EAAEC,OAAO;YAG5E,MAAMA,MAAM,qCAAqC;;QACnD;QAEA,IAAID,YAAY,GAAG;YACjB,IAAII,aAAa;YACjB,IAAI;gBACF,MAAMC,aAAa,MAAMC,IAAAA,cAAI,EAAChB;gBAC9B,IAAI,CAACe,WAAWE,MAAM,IAAI;oBACxBH,aAAa,KAAK,wFAAwF;;gBAC5G;YACF,EAAE,OAAOI,GAAG;YACV,SAAS;YACX;YAEA,IAAIJ,YAAY;gBACd,MAAMH;YACR;QACF;QAEA,6CAA6C;QAC7C,MAAMQ,QAAQC,KAAKC,GAAG,CAAC,KAAKX,UAAU;QAEtC,gBAAgB;QAChB,OAAON,gBAAgBL,QAAQC,QAAQQ,WAAWC,cAAcC,UAAU;IAC5E;AACF;AAEA,MAAMS,UAAU,CAACG,SACf,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF"}