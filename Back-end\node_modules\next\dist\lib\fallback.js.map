{"version": 3, "sources": ["../../src/lib/fallback.ts"], "sourcesContent": ["/**\n * Describes the different fallback modes that a given page can have.\n */\nexport const enum FallbackMode {\n  /**\n   * A BLOCKING_STATIC_RENDER fallback will block the request until the page is\n   * generated. No fallback page will be rendered, and users will have to wait\n   * to render the page.\n   */\n  BLOCKING_STATIC_RENDER = 'BLOCKING_STATIC_RENDER',\n\n  /**\n   * When set to PRERENDER, a fallback page will be sent to users in place of\n   * forcing them to wait for the page to be generated. This allows the user to\n   * see a rendered page earlier.\n   */\n  PRERENDER = 'PRERENDER',\n\n  /**\n   * When set to NOT_FOUND, pages that are not already prerendered will result\n   * in a not found response.\n   */\n  NOT_FOUND = 'NOT_FOUND',\n}\n\n/**\n * The fallback value returned from the `getStaticPaths` function.\n */\nexport type GetStaticPathsFallback = boolean | 'blocking'\n\n/**\n * Parses the fallback field from the prerender manifest.\n *\n * @param fallbackField The fallback field from the prerender manifest.\n * @returns The fallback mode.\n */\nexport function parseFallbackField(\n  fallbackField: string | boolean | null | undefined\n): FallbackMode | undefined {\n  if (typeof fallbackField === 'string') {\n    return FallbackMode.PRERENDER\n  } else if (fallbackField === null) {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else if (fallbackField === false) {\n    return FallbackMode.NOT_FOUND\n  } else if (fallbackField === undefined) {\n    return undefined\n  } else {\n    throw new Error(\n      `Invalid fallback option: ${fallbackField}. Fallback option must be a string, null, undefined, or false.`\n    )\n  }\n}\n\nexport function fallbackModeToFallbackField(\n  fallback: FallbackMode,\n  page: string | undefined\n): string | false | null {\n  switch (fallback) {\n    case FallbackMode.BLOCKING_STATIC_RENDER:\n      return null\n    case FallbackMode.NOT_FOUND:\n      return false\n    case FallbackMode.PRERENDER:\n      if (!page) {\n        throw new Error(\n          `Invariant: expected a page to be provided when fallback mode is \"${fallback}\"`\n        )\n      }\n\n      return page\n    default:\n      throw new Error(`Invalid fallback mode: ${fallback}`)\n  }\n}\n\n/**\n * Parses the fallback from the static paths result.\n *\n * @param result The result from the static paths function.\n * @returns The fallback mode.\n */\nexport function parseStaticPathsResult(\n  result: GetStaticPathsFallback\n): FallbackMode {\n  if (result === true) {\n    return FallbackMode.PRERENDER\n  } else if (result === 'blocking') {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else {\n    return FallbackMode.NOT_FOUND\n  }\n}\n"], "names": ["FallbackMode", "fallbackModeToFallbackField", "parseFallbackField", "parseStaticPathsResult", "fallback<PERSON><PERSON>", "undefined", "Error", "fallback", "page", "result"], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;IACiBA,YAAY;eAAZA;;IAmDFC,2BAA2B;eAA3BA;;IAlBAC,kBAAkB;eAAlBA;;IA8CAC,sBAAsB;eAAtBA;;;AA/ET,IAAA,AAAWH,sCAAAA;IAChB;;;;GAIC;IAGD;;;;GAIC;IAGD;;;GAGC;WAlBeA;;AAiCX,SAASE,mBACdE,aAAkD;IAElD,IAAI,OAAOA,kBAAkB,UAAU;QACrC;IACF,OAAO,IAAIA,kBAAkB,MAAM;QACjC;IACF,OAAO,IAAIA,kBAAkB,OAAO;QAClC;IACF,OAAO,IAAIA,kBAAkBC,WAAW;QACtC,OAAOA;IACT,OAAO;QACL,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,yBAAyB,EAAEF,cAAc,8DAA8D,CAAC,GADrG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEO,SAASH,4BACdM,QAAsB,EACtBC,IAAwB;IAExB,OAAQD;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,IAAI,CAACC,MAAM;gBACT,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,iEAAiE,EAAEC,SAAS,CAAC,CAAC,GAD3E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOC;QACT;YACE,MAAM,qBAA+C,CAA/C,IAAIF,MAAM,CAAC,uBAAuB,EAAEC,UAAU,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF;AAQO,SAASJ,uBACdM,MAA8B;IAE9B,IAAIA,WAAW,MAAM;QACnB;IACF,OAAO,IAAIA,WAAW,YAAY;QAChC;IACF,OAAO;QACL;IACF;AACF"}