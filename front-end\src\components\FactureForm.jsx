import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Autocomplete,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Snackbar,
  Alert,
  Tooltip,
  Chip,
  alpha,
  useTheme,
  Switch,
  Avatar,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Email as EmailIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  ShoppingCart as ShoppingCartIcon,
  Notes as NotesIcon,
  PhotoCamera as PhotoCameraIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { getCompanyData } from '../services/entrepriseService';
import factureService from '../services/factureService';
import clientService from '../services/clientService';
import produitService from '../services/produitService';
import { getUsersByRole } from '../services/userService';
import { useAuth } from '../contexts/AuthContext';
import DocumentPreview from '../components/DocumentPreview';
import EmailDialog from '../components/EmailDialog';
import { formatStatut, formatCurrency as formatCurrencyUtil, getDefaultCurrency, normalizeStatus } from '../utils/formatters';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 12
    }
  }
};

const FactureForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const { currentUser } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [companyData, setCompanyData] = useState(null);
  const [clients, setClients] = useState([]);
  const [produits, setProduits] = useState([]);
  const [vendeurs, setVendeurs] = useState([]);
  const [loadingVendeurs, setLoadingVendeurs] = useState(false);
  const [openClientDialog, setOpenClientDialog] = useState(false);
  const [openProduitDialog, setOpenProduitDialog] = useState(false);
  const [openEmailDialog, setOpenEmailDialog] = useState(false);
  const [produitTabValue, setProduitTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  const [facture, setFacture] = useState({
    clientId: null,
    dateEmission: new Date().toISOString().split('T')[0],
    lignes: [],
    notes: '',
    statut: 'DRAFT',
    total: 0,
    tauxTVA: 20,
  });

  const [newClient, setNewClient] = useState({
    nom: '',
    adresse: '',
    email: '', // Champ visible maintenant, pas besoin de valeur par défaut
    contact: 'Contact principal', // Champ requis par le modèle
    actif: true,
    vendeurId: null, // Null au lieu de chaîne vide pour éviter l'erreur de cast
    logo: null, // Pour stocker le fichier d'image
    cin: '', // Numéro de Carte d'Identité Nationale
    motDePasse: '', // Mot de passe pour le compte utilisateur
    createUserAccount: true // Option pour créer un compte utilisateur
  });

  const [showPassword, setShowPassword] = useState(false);

  const [clientLogoPreview, setClientLogoPreview] = useState(null);

  const [newProduit, setNewProduit] = useState({
    nom: '',
    description: '',
    descriptionDetaille: '',
    prix: 0,
    category: 'Non classé',
    unite: 'unité',
    gestionStock: false,
    quantiteStock: 0,
    seuilAlerte: 5,
    image: null,
  });

  const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = String(d.getFullYear()).slice(-2);
    return `${day}/${month}/${year}`;
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Récupérer les paramètres depuis le localStorage
        let defaultTaxRate = 20;
        try {
          const appSettings = localStorage.getItem('appSettings');
          if (appSettings) {
            const parsedSettings = JSON.parse(appSettings);
            if (parsedSettings.defaultTaxRate) {
              defaultTaxRate = parsedSettings.defaultTaxRate;
              console.log('Loaded tax rate from localStorage:', defaultTaxRate);
            }
          }
        } catch (localStorageError) {
          console.error('Error loading settings from localStorage:', localStorageError);
        }

        // Fetch company data
        const company = await getCompanyData();
        if (company && company.length > 0) {
          setCompanyData(company[0]);
          setFacture((prev) => ({
            ...prev,
            tauxTVA: defaultTaxRate, // Use tax rate from localStorage
          }));
        } else {
          // Handle missing company data by setting the localStorage tax rate
          setCompanyData(null);
          setFacture((prev) => ({
            ...prev,
            tauxTVA: defaultTaxRate,
          }));
        }

        // Fetch clients
        const clientsData = await clientService.getClients();
        if (!clientsData) {
          throw new Error('Erreur lors de la récupération des clients');
        }
        setClients(clientsData || []);

        // Fetch products
        const produitsData = await produitService.getProduits();
        if (!produitsData) {
          throw new Error('Erreur lors de la récupération des produits');
        }
        setProduits(produitsData || []);

        // Fetch facture data if editing
        if (id) {
          const factureData = await factureService.getFactureById(id);
          if (!factureData) {
            throw new Error('Erreur lors de la récupération de la facture');
          }
          setFacture({
            ...factureData,
            clientId: factureData.clientId,
            dateEmission: new Date(factureData.dateEmission).toISOString().split('T')[0],
            total: factureData.total || 0,
            lignes: factureData.lignes || [],
            notes: factureData.notes || '',
            statut: factureData.statut || 'DRAFT',
            tauxTVA: factureData.tauxTVA || (company && company.length > 0 ? company[0].tauxTVA : 20),
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setSnackbar({
          open: true,
          message: `Erreur lors du chargement des données: ${error.message}`,
          severity: 'error',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Fetch vendeurs when dialog opens
  useEffect(() => {
    const fetchVendeurs = async () => {
      if (openClientDialog) {
        try {
          setLoadingVendeurs(true);
          // Get vendeurs based on user role
          if (currentUser && currentUser.role === 'ADMIN') {
            // Admin can see all vendeurs
            const vendeursList = await getUsersByRole('VENDEUR');
            setVendeurs(vendeursList);
          } else if (currentUser && currentUser.role === 'RESPONSABLE') {
            // Responsable can only see their vendeurs
            const responsableId = currentUser._id || currentUser.id;
            console.log('Fetching vendeurs for responsable:', responsableId);
            try {
              // Utiliser une API spécifique pour récupérer les vendeurs d'un responsable
              const vendeursList = await getUsersByRole('VENDEUR');
              console.log('All vendeurs:', vendeursList);

              // Filtrer les vendeurs qui ont ce responsable dans leur liste de responsables
              const filteredVendeurs = vendeursList.filter(vendeur =>
                vendeur.responsables &&
                (Array.isArray(vendeur.responsables) ?
                  vendeur.responsables.some(id => id === responsableId || id.toString() === responsableId.toString()) :
                  vendeur.responsables === responsableId || vendeur.responsables.toString() === responsableId.toString())
              );
              console.log('Filtered vendeurs for responsable:', filteredVendeurs);
              setVendeurs(filteredVendeurs);
            } catch (error) {
              console.error('Error fetching vendeurs for responsable:', error);
              setVendeurs([]);
            }
          } else {
            // Vendeur can't assign other vendeurs
            setVendeurs([]);
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des vendeurs:', error);
          setSnackbar({
            open: true,
            message: 'Erreur lors de la récupération des vendeurs',
            severity: 'error',
          });
        } finally {
          setLoadingVendeurs(false);
        }
      }
    };

    fetchVendeurs();
  }, [openClientDialog, currentUser]);

  useEffect(() => {
    // Calculate total HT from all lines - ensure we're working with numbers
    const totalHT = facture.lignes.reduce(
      (sum, ligne) => {
        const quantite = parseFloat(ligne.quantite) || 0;
        const prixUnitaire = parseFloat(ligne.prixUnitaire) || 0;
        return sum + (quantite * prixUnitaire);
      },
      0
    );

    // Calculate TVA amount based on total HT and TVA rate
    const tauxTVA = parseFloat(facture.tauxTVA) || 0;
    const montantTVA = totalHT * (tauxTVA / 100);

    // Calculate total TTC (HT + TVA)
    const totalTTC = totalHT + montantTVA;

    // Update the facture state with calculated values - store as numbers
    setFacture((prev) => ({
      ...prev,
      totalHT: Number(totalHT.toFixed(2)),
      montantTVA: Number(montantTVA.toFixed(2)),
      total: Number(totalTTC.toFixed(2)),
    }));
  }, [facture.lignes, facture.tauxTVA]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Pour le champ statut, normaliser la valeur
    if (name === 'statut') {
      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus(value);
      console.log('Setting status to:', value, '(normalized to:', normalizedStatus, ')');

      setFacture((prev) => ({
        ...prev,
        [name]: normalizedStatus,
      }));
    } else {
      setFacture((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleClientChange = (event, newValue) => {
    setFacture((prev) => ({
      ...prev,
      clientId: newValue,
    }));
  };

  const handleAddLigne = () => {
    setFacture((prev) => ({
      ...prev,
      lignes: [
        ...prev.lignes,
        {
          produit: null,
          description: '',
          quantite: 1,
          prixUnitaire: 0,
          montantHT: 0,
          montantTTC: 0,
        },
      ],
    }));
  };

  const handleRemoveLigne = (index) => {
    setFacture((prev) => ({
      ...prev,
      lignes: prev.lignes.filter((_, i) => i !== index),
    }));
  };

  const handleLigneChange = (index, field, value) => {
    setFacture((prev) => {
      const newLignes = [...prev.lignes];
      newLignes[index] = {
        ...newLignes[index],
        [field]: value,
      };

      // If product is selected, update description and price
      if (field === 'produit' && value) {
        newLignes[index].description = value.description || value.nom;
        newLignes[index].prixUnitaire = value.prix || 0;
      }

      // Calculate montantHT and montantTTC - ensure we're working with numbers
      const quantite = parseFloat(newLignes[index].quantite) || 0;
      const prixUnitaire = parseFloat(newLignes[index].prixUnitaire) || 0;
      const tauxTVA = parseFloat(prev.tauxTVA) || 0;

      // Calculate HT amount (quantity * unit price) - round to 2 decimal places
      const montantHT = Number((quantite * prixUnitaire).toFixed(2));
      newLignes[index].montantHT = montantHT;

      // Calculate TTC amount (HT + TVA) - round to 2 decimal places
      const montantTTC = Number((montantHT * (1 + tauxTVA / 100)).toFixed(2));
      newLignes[index].montantTTC = montantTTC;

      return { ...prev, lignes: newLignes };
    });
  };

  const validateForm = () => {
    if (!facture.clientId) {
      setSnackbar({
        open: true,
        message: 'Veuillez sélectionner un client',
        severity: 'error',
      });
      return false;
    }
    if (!facture.dateEmission) {
      setSnackbar({
        open: true,
        message: "Veuillez sélectionner une date d'émission",
        severity: 'error',
      });
      return false;
    }
    if (facture.lignes.length === 0) {
      setSnackbar({
        open: true,
        message: 'Veuillez ajouter au moins une ligne de produit ou service',
        severity: 'error',
      });
      return false;
    }
    if (facture.tauxTVA < 0) {
      setSnackbar({
        open: true,
        message: 'Le taux de TVA doit être supérieur ou égal à 0',
        severity: 'error',
      });
      return false;
    }
    for (let i = 0; i < facture.lignes.length; i++) {
      const ligne = facture.lignes[i];
      if (!ligne.quantite || ligne.quantite <= 0) {
        setSnackbar({
          open: true,
          message: `La quantité de la ligne ${i + 1} doit être supérieure à 0`,
          severity: 'error',
        });
        return false;
      }
      if (!ligne.prixUnitaire || ligne.prixUnitaire < 0) {
        setSnackbar({
          open: true,
          message: `Le prix unitaire de la ligne ${i + 1} doit être supérieur ou égal à 0`,
          severity: 'error',
        });
        return false;
      }
      if (!ligne.description) {
        setSnackbar({
          open: true,
          message: `La description de la ligne ${i + 1} est requise`,
          severity: 'error',
        });
        return false;
      }
    }
    return true;
  };

  const handleSave = async (asDraft = true) => {
    if (!validateForm()) return;

    setSaving(true);
    try {
      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus(facture.statut);
      console.log('Saving facture with status:', facture.statut, '(normalized to:', normalizedStatus, ')');

      const factureToSave = {
        ...facture,
        statut: normalizedStatus, // Use the normalized statut
        clientId: facture.clientId?._id || facture.clientId,
        dateEmission: new Date(facture.dateEmission),
        lignes: facture.lignes.map((ligne) => ({
          produit: ligne.produit?._id || ligne.produit,
          description: ligne.description,
          quantite: ligne.quantite,
          prixUnitaire: ligne.prixUnitaire,
          montantHT: ligne.montantHT,
          montantTTC: ligne.montantTTC,
        })),
      };

      let result;
      if (id) {
        console.log('Updating facture with ID:', id, 'Data:', factureToSave);
        result = await factureService.updateFacture(id, factureToSave);
        console.log('Update result:', result);
      } else {
        console.log('Creating new facture:', factureToSave);
        result = await factureService.createFacture(factureToSave);
        console.log('Create result:', result);
      }

      if (!result || !result._id) {
        throw new Error('Failed to retrieve facture ID after save');
      }

      if (!asDraft) {
        const factureId = id || result._id;
        if (!factureId) {
          throw new Error('Facture ID not found for finalization');
        }
        const currentFacture = await factureService.getFactureById(factureId);
        // Normaliser le statut pour la comparaison
        const normalizedCurrentStatus = normalizeStatus(currentFacture.statut);
        console.log('Current facture status:', currentFacture.statut, '(normalized to:', normalizedCurrentStatus, ')');

        if (normalizedCurrentStatus === 'DRAFT') {
          console.log('Finalizing facture with ID:', factureId);
          const finalizeResult = await factureService.finalizeFacture(factureId);
          console.log('Finalize result:', finalizeResult);
          setSnackbar({
            open: true,
            message: 'Facture finalisée avec succès',
            severity: 'success',
          });
        } else {
          setSnackbar({
            open: true,
            message: `Facture enregistrée avec le statut ${formatStatut(currentFacture.statut)}`,
            severity: 'success',
          });
        }
      } else {
        setSnackbar({
          open: true,
          message: 'Facture enregistrée comme brouillon',
          severity: 'success',
        });
      }

      // Déterminer le préfixe de route en fonction du rôle de l'utilisateur
      const routePrefix = currentUser?.role === 'ADMIN' ? '/admin' :
                         currentUser?.role === 'VENDEUR' ? '/vendeur' :
                         currentUser?.role === 'RESPONSABLE' ? '/responsable' : '';
      setTimeout(() => navigate(`${routePrefix}/factures`), 3000);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
      if (error.response?.status === 401) {
        setSnackbar({
          open: true,
          message: 'Session expirée. Veuillez vous reconnecter.',
          severity: 'error',
        });
        setTimeout(() => navigate('/login'), 2000);
      } else {
        setSnackbar({
          open: true,
          message: `Erreur lors de la sauvegarde de la facture: ${error.response?.data?.error || error.message}`,
          severity: 'error',
        });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleCreateClient = async () => {
    if (!newClient.nom || !newClient.adresse) {
      setSnackbar({
        open: true,
        message: 'Veuillez remplir tous les champs obligatoires pour le client',
        severity: 'error',
      });
      return;
    }

    // Vérifier que le CIN contient exactement 8 chiffres
    if (newClient.cin) {
      const cinRegex = /^\d{8}$/;
      if (!cinRegex.test(newClient.cin)) {
        setSnackbar({
          open: true,
          message: 'Le CIN doit contenir exactement 8 chiffres',
          severity: 'error',
        });
        return;
      }
    } else {
      setSnackbar({
        open: true,
        message: 'Le numéro CIN est obligatoire',
        severity: 'error',
      });
      return;
    }

    // Vérifier que le mot de passe est fourni si createUserAccount est activé
    if (newClient.createUserAccount && !newClient.motDePasse) {
      setSnackbar({
        open: true,
        message: 'Veuillez fournir un mot de passe pour le compte utilisateur',
        severity: 'error',
      });
      return;
    }

    try {
      // Créer le client sans le logo d'abord
      const clientData = { ...newClient };
      delete clientData.logo; // Supprimer le logo de l'objet client

      // Si l'utilisateur est un vendeur, assigner automatiquement son ID comme vendeurId
      if (currentUser && currentUser.role === 'VENDEUR') {
        clientData.vendeurId = currentUser._id || currentUser.id;
        console.log('Assignation automatique du vendeur:', clientData.vendeurId);
      }
      // Sinon, gérer correctement le vendeurId (null si vide)
      else if (!clientData.vendeurId) {
        clientData.vendeurId = null;
      }

      // S'assurer que les champs requis sont présents
      if (!clientData.email) {
        clientData.email = '<EMAIL>'; // Valeur par défaut si le champ est vide
      }

      if (!clientData.contact) {
        clientData.contact = 'Contact principal';
      }

      // Ajouter le responsable actuel aux responsables du client si c'est un responsable d'entreprise
      if (currentUser && currentUser.role === 'RESPONSABLE') {
        clientData.responsables = [currentUser._id || currentUser.id];
      }

      console.log('Données client à envoyer:', clientData);

      const result = await clientService.createClient(clientData);

      // Si un logo a été sélectionné, l'uploader
      if (newClient.logo) {
        try {
          await clientService.uploadClientLogo(result._id, newClient.logo);
          // Mettre à jour le résultat avec le logo
          result.logo = clientLogoPreview;
        } catch (logoError) {
          console.error('Erreur lors de l\'upload du logo:', logoError);
          // Continuer même si l'upload du logo échoue
        }
      }

      setClients((prev) => [...prev, result]);
      setFacture((prev) => ({
        ...prev,
        clientId: result,
      }));
      setOpenClientDialog(false);

      // Réinitialiser le formulaire
      setNewClient({
        nom: '',
        adresse: '',
        email: '',
        contact: 'Contact principal',
        actif: true,
        vendeurId: null,
        logo: null,
        cin: '',
        motDePasse: '',
        createUserAccount: true
      });
      setClientLogoPreview(null);

      setSnackbar({
        open: true,
        message: 'Client créé avec succès',
        severity: 'success',
      });
    } catch (error) {
      console.error('Erreur lors de la création du client:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la création du client: ' + error.message,
        severity: 'error',
      });
    }
  };

  const handleClientLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewClient(prev => ({ ...prev, logo: file }));

      // Créer une prévisualisation
      const reader = new FileReader();
      reader.onloadend = () => {
        setClientLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCreateProduit = async () => {
    if (!newProduit.nom || newProduit.prix <= 0) {
      setSnackbar({
        open: true,
        message: 'Veuillez remplir tous les champs obligatoires pour le produit',
        severity: 'error',
      });
      return;
    }

    try {
      const result = await produitService.createProduit(newProduit);
      setProduits((prev) => [...prev, result]);
      setOpenProduitDialog(false);
      setProduitTabValue(0); // Réinitialiser l'onglet
      setNewProduit({
        nom: '',
        description: '',
        descriptionDetaille: '',
        prix: 0,
        category: 'Non classé',
        unite: 'unité',
        gestionStock: false,
        quantiteStock: 0,
        seuilAlerte: 5,
        image: null,
      });
      setSnackbar({
        open: true,
        message: 'Produit créé avec succès',
        severity: 'success',
      });
    } catch (error) {
      console.error('Erreur lors de la création du produit:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la création du produit',
        severity: 'error',
      });
    }
  };

  const handleOpenEmailDialog = () => {
    if (!id || facture.statut === 'DRAFT') return;
    setOpenEmailDialog(true);
  };

  const handleCloseEmailDialog = () => {
    setOpenEmailDialog(false);
  };

  const handleSendEmail = async (emailData) => {
    try {
      if (!id) return;

      await factureService.sendEmail(id, emailData);

      setSnackbar({
        open: true,
        message: 'Email envoyé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      setSnackbar({
        open: true,
        message: `Erreur lors de l'envoi de l'email: ${error.message}`,
        severity: 'error'
      });
      throw error;
    }
  };

  // Utiliser directement la fonction formatCurrency importée

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress size={60} thickness={4} />
      </Box>
    );
  }

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 1, md: 3 },
        backgroundColor: alpha(theme.palette.background.default, 0.7)
      }}
    >
      {/* Header Section */}
      <Paper
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          p: 2,
          mb: 3,
          borderRadius: 2,
          background: `linear-gradient(to right, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.main, 0.8)})`,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              onClick={() => {
                const routePrefix = currentUser?.role === 'ADMIN' ? '/admin' :
                                   currentUser?.role === 'VENDEUR' ? '/vendeur' :
                                   currentUser?.role === 'RESPONSABLE' ? '/responsable' : '';
                navigate(`${routePrefix}/factures`);
              }}
              sx={{
                mr: 2,
                color: 'white',
                backgroundColor: alpha('#fff', 0.2),
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.3),
                }
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: 'white' }}>
                {id ? 'Modifier la facture' : 'Nouvelle facture'}
              </Typography>
              <Typography variant="subtitle1" sx={{ color: alpha('#fff', 0.9), mt: 0.5 }}>
                {id ? 'Modifiez les détails de votre facture' : 'Créez une nouvelle facture pour votre client'}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={() => handleSave(true)}
              disabled={saving}
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: alpha('#fff', 0.1),
                }
              }}
            >
              Enregistrer comme brouillon
            </Button>
            <Button
              variant="contained"
              startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
              onClick={() => handleSave(false)}
              disabled={saving || !facture.clientId}
              sx={{
                backgroundColor: 'white',
                color: theme.palette.primary.main,
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.9),
                }
              }}
            >
              {saving ? 'Enregistrement...' : 'Finaliser la facture'}
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Tabs Section */}
      <Paper
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          mb: 3,
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
          sx={{
            '& .MuiTab-root': {
              py: 2,
              fontSize: '1rem',
              fontWeight: 500,
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.05),
              },
            },
            '& .Mui-selected': {
              fontWeight: 600,
            }
          }}
        >
          <Tab
            icon={<InfoIcon sx={{ mr: 1 }} />}
            label="Informations"
            iconPosition="start"
          />
          <Tab
            icon={<VisibilityIcon sx={{ mr: 1 }} />}
            label="Aperçu"
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Informations générales
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Date d'émission
                    </Typography>
                    <TextField
                      name="dateEmission"
                      type="date"
                      value={facture.dateEmission}
                      onChange={handleChange}
                      fullWidth
                      variant="outlined"
                      InputLabelProps={{ shrink: true }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mt: 1,
                      color: theme.palette.text.secondary
                    }}>
                      <InfoIcon fontSize="small" sx={{ mr: 1, color: theme.palette.info.main }} />
                      <Typography variant="body2">
                        Date formatée: {formatDate(facture.dateEmission)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Client
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Autocomplete
                        options={clients}
                        getOptionLabel={(option) => option.nom || ''}
                        value={facture.clientId}
                        onChange={handleClientChange}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Sélectionnez un client"
                            fullWidth
                            required
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white',
                                borderRadius: 1.5,
                              }
                            }}
                          />
                        )}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                              <Typography variant="body1" fontWeight={500}>
                                {option.nom}
                              </Typography>
                              {option.email && (
                                <Typography variant="body2" color="text.secondary">
                                  {option.email}
                                </Typography>
                              )}
                            </Box>
                          </li>
                        )}
                        sx={{ flex: 1 }}
                        isOptionEqualToValue={(option, value) => option._id === (value?._id || value)}
                      />
                      <Tooltip title="Ajouter un nouveau client">
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={() => setOpenClientDialog(true)}
                          sx={{
                            height: 56,
                            minWidth: 56,
                            width: 56,
                            borderRadius: '50%'
                          }}
                        >
                          <AddIcon />
                        </Button>
                      </Tooltip>
                    </Box>
                    {!facture.clientId && (
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mt: 1,
                        color: theme.palette.warning.main
                      }}>
                        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Veuillez sélectionner un client pour la facture
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ShoppingCartIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Produits et services
                </Typography>
                <Box sx={{ flexGrow: 1 }} />
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddLigne}
                  sx={{
                    borderRadius: 8,
                    px: 2,
                    py: 1,
                    boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                    '&:hover': {
                      boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
                    }
                  }}
                >
                  Ajouter une ligne
                </Button>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <TableContainer sx={{
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                mb: 3,
                overflow: 'hidden'
              }}>
                <Table>
                  <TableHead sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>Produit/Service</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Quantité</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Prix unitaire HT</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Total HT</TableCell>
                      <TableCell width="50"></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {facture.lignes.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 2 }}>
                            <ShoppingCartIcon
                              sx={{
                                fontSize: 48,
                                color: alpha(theme.palette.text.secondary, 0.5),
                                mb: 2
                              }}
                            />
                            <Typography variant="body1" color="text.secondary" gutterBottom>
                              Aucun produit ou service ajouté
                            </Typography>
                            <Button
                              variant="outlined"
                              startIcon={<AddIcon />}
                              onClick={handleAddLigne}
                              sx={{ mt: 1 }}
                            >
                              Ajouter votre premier produit
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : (
                      facture.lignes.map((ligne, index) => (
                        <TableRow
                          key={index}
                          sx={{
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.02)
                            },
                            transition: 'background-color 0.2s'
                          }}
                        >
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Autocomplete
                                options={produits}
                                getOptionLabel={(option) => option.nom || ''}
                                value={ligne.produit}
                                onChange={(e, newValue) => handleLigneChange(index, 'produit', newValue)}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    size="small"
                                    fullWidth
                                    placeholder="Sélectionner un produit"
                                    sx={{
                                      '& .MuiOutlinedInput-root': {
                                        backgroundColor: 'white',
                                        borderRadius: 1.5,
                                      }
                                    }}
                                  />
                                )}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                      <Typography variant="body1" fontWeight={500}>
                                        {option.nom}
                                      </Typography>
                                      <Typography variant="body2" color="text.secondary">
                                        {formatCurrencyUtil(option.prix || 0)}
                                      </Typography>
                                    </Box>
                                  </li>
                                )}
                                sx={{ width: 200 }}
                                isOptionEqualToValue={(option, value) => option._id === (value?._id || value)}
                              />
                              {index === 0 && (
                                <Tooltip title="Ajouter un nouveau produit">
                                  <IconButton
                                    size="small"
                                    onClick={() => setOpenProduitDialog(true)}
                                    sx={{
                                      color: theme.palette.primary.main,
                                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                      '&:hover': {
                                        backgroundColor: alpha(theme.palette.primary.main, 0.2),
                                      }
                                    }}
                                  >
                                    <AddIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <TextField
                              value={ligne.description || ''}
                              onChange={(e) => handleLigneChange(index, 'description', e.target.value)}
                              size="small"
                              fullWidth
                              placeholder="Description du produit"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: 1.5,
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <TextField
                              type="number"
                              value={ligne.quantite || 1}
                              onChange={(e) =>
                                handleLigneChange(index, 'quantite', parseFloat(e.target.value) || 0)
                              }
                              size="small"
                              InputProps={{
                                inputProps: { min: 0, step: 1 },
                                sx: { textAlign: 'center' }
                              }}
                              sx={{
                                width: 80,
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: 1.5,
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <TextField
                              type="number"
                              value={ligne.prixUnitaire || 0}
                              onChange={(e) =>
                                handleLigneChange(index, 'prixUnitaire', parseFloat(e.target.value) || 0)
                              }
                              size="small"
                              InputProps={{
                                inputProps: { min: 0, step: 0.01 },
                                sx: { textAlign: 'right' }
                              }}
                              sx={{
                                width: 100,
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: 1.5,
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body1" fontWeight={500}>
                              {formatCurrencyUtil(ligne.montantHT || 0)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Tooltip title="Supprimer cette ligne">
                              <IconButton
                                size="small"
                                onClick={() => handleRemoveLigne(index)}
                                sx={{
                                  color: theme.palette.error.main,
                                  '&:hover': {
                                    backgroundColor: alpha(theme.palette.error.main, 0.1),
                                  }
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box
                sx={{
                  mt: 3,
                  display: 'flex',
                  justifyContent: 'flex-end',
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  borderRadius: 2,
                  p: 2,
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Grid container spacing={2} sx={{ maxWidth: 400 }}>
                  <Grid item xs={7}>
                    <Typography variant="body1" color="text.secondary">Total HT:</Typography>
                  </Grid>
                  <Grid item xs={5} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1" fontWeight={500}>
                      {formatCurrencyUtil(
                        facture.lignes.reduce((sum, ligne) => sum + (ligne.montantHT || 0), 0)
                      )}
                    </Typography>
                  </Grid>
                  <Grid item xs={7}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body1" color="text.secondary" sx={{ mr: 1 }}>
                        TVA:
                      </Typography>
                      <TextField
                        type="number"
                        name="tauxTVA"
                        value={facture.tauxTVA}
                        onChange={handleChange}
                        size="small"
                        InputProps={{
                          inputProps: { min: 0, max: 100, step: 0.1 },
                          sx: { textAlign: 'center' }
                        }}
                        sx={{
                          width: 60,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                          }
                        }}
                      />
                      <Typography variant="body1" color="text.secondary" sx={{ ml: 1 }}>
                        %
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={5} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1" fontWeight={500}>
                      {formatCurrencyUtil(
                        facture.lignes.reduce((sum, ligne) => sum + (ligne.montantHT || 0), 0) *
                          (facture.tauxTVA / 100)
                      )}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>
                  <Grid item xs={7}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Total TTC:
                    </Typography>
                  </Grid>
                  <Grid item xs={5} sx={{ textAlign: 'right' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                      {formatCurrencyUtil(facture.total)}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Paper>

            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotesIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Notes et conditions
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <TextField
                  name="notes"
                  label="Notes pour le client"
                  multiline
                  rows={4}
                  value={facture.notes || ''}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  placeholder="Ex: Merci pour votre confiance. Paiement attendu sous 30 jours."
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: 1.5,
                    }
                  }}
                />
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mt: 2,
                  color: theme.palette.text.secondary
                }}>
                  <InfoIcon fontSize="small" sx={{ mr: 1, color: theme.palette.info.main }} />
                  <Typography variant="body2">
                    Ces notes seront visibles sur la facture envoyée au client
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ReceiptIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Statut et actions
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  mb: 3
                }}
              >
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                  Statut de la facture
                </Typography>
                <FormControl fullWidth>
                  <Select
                    name="statut"
                    value={facture.statut}
                    onChange={handleChange}
                    displayEmpty
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Chip
                          label={formatStatut(selected)}
                          size="small"
                          sx={{
                            mr: 1,
                            backgroundColor:
                              selected === 'DRAFT' ? alpha(theme.palette.info.main, 0.1) :
                              selected === 'SENT' ? alpha(theme.palette.warning.main, 0.1) :
                              selected === 'PAID' ? alpha(theme.palette.success.main, 0.1) :
                              alpha(theme.palette.error.main, 0.1),
                            color:
                              selected === 'DRAFT' ? theme.palette.info.main :
                              selected === 'SENT' ? theme.palette.warning.main :
                              selected === 'PAID' ? theme.palette.success.main :
                              theme.palette.error.main,
                            fontWeight: 600
                          }}
                        />
                      </Box>
                    )}
                  >
                    <MenuItem value="DRAFT">
                      <Chip
                        label="Brouillon"
                        size="small"
                        sx={{
                          backgroundColor: alpha(theme.palette.info.main, 0.1),
                          color: theme.palette.info.main
                        }}
                      />
                    </MenuItem>
                    <MenuItem value="SENT">
                      <Chip
                        label="Envoyée"
                        size="small"
                        sx={{
                          backgroundColor: alpha(theme.palette.warning.main, 0.1),
                          color: theme.palette.warning.main
                        }}
                      />
                    </MenuItem>
                    <MenuItem value="PAID">
                      <Chip
                        label="Payée"
                        size="small"
                        sx={{
                          backgroundColor: alpha(theme.palette.success.main, 0.1),
                          color: theme.palette.success.main
                        }}
                      />
                    </MenuItem>
                    <MenuItem value="CANCELED">
                      <Chip
                        label="Annulée"
                        size="small"
                        sx={{
                          backgroundColor: alpha(theme.palette.error.main, 0.1),
                          color: theme.palette.error.main
                        }}
                      />
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 2, color: theme.palette.text.secondary }}>
                Actions disponibles
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<VisibilityIcon />}
                  onClick={() => setTabValue(1)}
                  fullWidth
                  sx={{
                    borderRadius: 8,
                    py: 1.2,
                    borderColor: theme.palette.primary.main,
                    color: theme.palette.primary.main,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.05),
                      borderColor: theme.palette.primary.main,
                    }
                  }}
                >
                  Prévisualiser la facture
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  fullWidth
                  disabled={!id}
                  onClick={() => id && factureService.generatePdf(id)}
                  sx={{
                    borderRadius: 8,
                    py: 1.2,
                    borderColor: theme.palette.info.main,
                    color: theme.palette.info.main,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.info.main, 0.05),
                      borderColor: theme.palette.info.main,
                    },
                    '&.Mui-disabled': {
                      borderColor: alpha(theme.palette.info.main, 0.3),
                      color: alpha(theme.palette.info.main, 0.3),
                    }
                  }}
                >
                  Télécharger en PDF
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<EmailIcon />}
                  fullWidth
                  disabled={!id || facture.statut === 'DRAFT'}
                  onClick={handleOpenEmailDialog}
                  sx={{
                    borderRadius: 8,
                    py: 1.2,
                    borderColor: theme.palette.success.main,
                    color: theme.palette.success.main,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.success.main, 0.05),
                      borderColor: theme.palette.success.main,
                    },
                    '&.Mui-disabled': {
                      borderColor: alpha(theme.palette.success.main, 0.3),
                      color: alpha(theme.palette.success.main, 0.3),
                    }
                  }}
                >
                  Envoyer par email
                </Button>
              </Box>
            </Paper>

            {facture.clientId && (
              <Paper
                component={motion.div}
                variants={itemVariants}
                elevation={2}
                sx={{
                  p: 3,
                  borderRadius: 2,
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PersonIcon
                    sx={{
                      mr: 2,
                      color: theme.palette.primary.main,
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      p: 1,
                      borderRadius: '50%',
                      fontSize: 32
                    }}
                  />
                  <Typography variant="h6" fontWeight={600}>
                    Informations client
                  </Typography>
                </Box>
                <Divider sx={{ mb: 3 }} />

                <Box
                  sx={{
                    p: 3,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.default, 0.5),
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main, mb: 1 }}>
                    {facture.clientId.nom}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <Box
                      component="span"
                      sx={{
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        p: 0.5,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 1.5,
                        mt: 0.5
                      }}
                    >
                      <InfoIcon fontSize="small" />
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Adresse
                      </Typography>
                      <Typography variant="body2">{facture.clientId.adresse || 'Non spécifiée'}</Typography>
                    </Box>
                  </Box>

                  {facture.clientId.email && (
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        component="span"
                        sx={{
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          p: 0.5,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 1.5,
                          mt: 0.5
                        }}
                      >
                        <EmailIcon fontSize="small" />
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Email
                        </Typography>
                        <Typography variant="body2">{facture.clientId.email}</Typography>
                      </Box>
                    </Box>
                  )}

                  {facture.clientId.contact && (
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Box
                        component="span"
                        sx={{
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          p: 0.5,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 1.5,
                          mt: 0.5
                        }}
                      >
                        <InfoIcon fontSize="small" />
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Téléphone
                        </Typography>
                        <Typography variant="body2">{facture.clientId.contact}</Typography>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Paper>
            )}
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Box
          component={motion.div}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <DocumentPreview
            type="facture"
            companyData={companyData}
            document={facture}
            onEmail={handleOpenEmailDialog}
          />
        </Box>
      )}

      {/* Dialog pour ajouter un nouveau client */}
      <Dialog
        open={openClientDialog}
        onClose={() => setOpenClientDialog(false)}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          }
        }}
      >
        <DialogTitle sx={{ pb: 1, pt: 3 }}>
          <Typography variant="h6" component="span" fontWeight={600}>
            Nouveau Client
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box sx={{
                width: 120,
                height: 120,
                borderRadius: '50%',
                bgcolor: 'grey.200',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 2,
                overflow: 'hidden',
                position: 'relative'
              }}>
                {clientLogoPreview ? (
                  <img
                    src={clientLogoPreview}
                    alt="Logo du client"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  <PersonIcon sx={{ fontSize: 60, color: 'grey.400' }} />
                )}
              </Box>

              <Button
                variant="outlined"
                component="label"
                size="small"
                startIcon={<PhotoCameraIcon />}
                sx={{ borderRadius: 2 }}
              >
                {clientLogoPreview ? 'Changer la photo' : 'Ajouter une photo'}
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={handleClientLogoChange}
                />
              </Button>

              <Box sx={{ mt: 3, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ mr: 1 }}>Client actif</Typography>
                <Switch
                  checked={newClient.actif}
                  onChange={(e) => setNewClient(prev => ({ ...prev, actif: e.target.checked }))}
                  color="primary"
                />
              </Box>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Nom
                  </Typography>
                  <TextField
                    name="nom"
                    placeholder="Nom complet du client ou de l'entreprise"
                    value={newClient.nom}
                    onChange={(e) => setNewClient((prev) => ({ ...prev, nom: e.target.value }))}
                    fullWidth
                    required
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Adresse du client
                  </Typography>
                  <TextField
                    name="adresse"
                    placeholder="Adresse complète"
                    value={newClient.adresse}
                    onChange={(e) => setNewClient((prev) => ({ ...prev, adresse: e.target.value }))}
                    fullWidth
                    multiline
                    rows={2}
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Téléphone
                  </Typography>
                  <TextField
                    name="contact"
                    placeholder="Please fill in this field."
                    value={newClient.contact}
                    onChange={(e) => setNewClient((prev) => ({ ...prev, contact: e.target.value }))}
                    fullWidth
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                {/* Afficher le sélecteur de vendeur seulement si l'utilisateur n'est pas un vendeur */}
                {currentUser && currentUser.role !== 'VENDEUR' && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Vendeur assigné
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        value={newClient.vendeurId || ''}
                        onChange={(e) => setNewClient(prev => ({ ...prev, vendeurId: e.target.value || null }))}
                        displayEmpty
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                          }
                        }}
                      >
                        <MenuItem value="">
                          <em>Aucun vendeur assigné</em>
                        </MenuItem>
                        {loadingVendeurs ? (
                          <MenuItem disabled>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <CircularProgress size={20} sx={{ mr: 1 }} />
                              Chargement des vendeurs...
                            </Box>
                          </MenuItem>
                        ) : vendeurs.length > 0 ? (
                          vendeurs.map((vendeur) => (
                            <MenuItem key={vendeur._id} value={vendeur._id}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Avatar
                                  src={vendeur.profileImage ? `http://localhost:5000${vendeur.profileImage}` : ''}
                                  sx={{ width: 24, height: 24 }}
                                >
                                  {!vendeur.profileImage && (vendeur.nom ? vendeur.nom.charAt(0) : 'V')}
                                </Avatar>
                                {vendeur.nom || vendeur.email || 'Vendeur sans nom'}
                              </Box>
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem disabled>
                            <em>Aucun vendeur disponible</em>
                          </MenuItem>
                        )}
                      </Select>
                    </FormControl>
                    {vendeurs.length === 0 && !loadingVendeurs && (
                      <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', color: theme.palette.info.main }}>
                        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Aucun vendeur n'est disponible pour l'assignation
                        </Typography>
                      </Box>
                    )}
                  </Grid>
                )}
                {/* Si l'utilisateur est un vendeur, afficher un message informatif */}
                {currentUser && currentUser.role === 'VENDEUR' && (
                  <Grid item xs={12}>
                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', color: theme.palette.info.main, p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1 }}>
                      <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="body2">
                        Ce client sera automatiquement assigné à votre compte vendeur
                      </Typography>
                    </Box>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Email
                  </Typography>
                  <TextField
                    name="email"
                    placeholder="<EMAIL>"
                    value={newClient.email}
                    onChange={(e) => setNewClient((prev) => ({ ...prev, email: e.target.value }))}
                    fullWidth
                    type="email"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Numéro CIN
                  </Typography>
                  <TextField
                    name="cin"
                    placeholder="Numéro de Carte d'Identité Nationale (8 chiffres)"
                    value={newClient.cin}
                    onChange={(e) => setNewClient((prev) => ({ ...prev, cin: e.target.value }))}
                    fullWidth
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Switch
                      checked={newClient.createUserAccount}
                      onChange={(e) => setNewClient(prev => ({ ...prev, createUserAccount: e.target.checked }))}
                      color="primary"
                    />
                    <Typography variant="body1" sx={{ ml: 1 }}>
                      Créer un compte utilisateur pour ce client
                    </Typography>
                  </Box>
                </Grid>

                {newClient.createUserAccount && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Mot de passe
                    </Typography>
                    <TextField
                      name="motDePasse"
                      placeholder="Mot de passe pour le compte utilisateur"
                      value={newClient.motDePasse}
                      onChange={(e) => setNewClient((prev) => ({ ...prev, motDePasse: e.target.value }))}
                      fullWidth
                      type={showPassword ? 'text' : 'password'}
                      variant="outlined"
                      InputProps={{
                        endAdornment: (
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        )
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>
                )}

                {/* Champ contact caché mais nécessaire pour la validation */}
                <Grid item xs={12} sx={{ display: 'none' }}>
                  <TextField
                    name="contact"
                    value={newClient.contact}
                    onChange={(e) => setNewClient(prev => ({ ...prev, contact: e.target.value }))}
                    fullWidth
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setOpenClientDialog(false)}
            variant="outlined"
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.secondary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                backgroundColor: alpha(theme.palette.grey[400], 0.05),
              }
            }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateClient}
            disabled={!newClient.nom || !newClient.adresse || !newClient.contact}
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
              }
            }}
          >
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog pour ajouter un nouveau produit */}
      <Dialog
        open={openProduitDialog}
        onClose={() => setOpenProduitDialog(false)}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          }
        }}
      >
        <DialogTitle sx={{ pb: 1, pt: 3 }}>
          <Typography variant="h6" component="span" fontWeight={600}>
            Nouveau Produit
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Tabs
            value={produitTabValue}
            onChange={(e, newValue) => setProduitTabValue(newValue)}
            sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Informations générales" />
            <Tab label="Description détaillée" />
            <Tab label="Gestion des stocks" />
          </Tabs>

          {produitTabValue === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Nom du produit
                    </Typography>
                    <TextField
                      name="nom"
                      placeholder="Nom du produit ou service"
                      value={newProduit.nom}
                      onChange={(e) => setNewProduit((prev) => ({ ...prev, nom: e.target.value }))}
                      fullWidth
                      required
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Description courte
                    </Typography>
                    <TextField
                      name="description"
                      placeholder="Brève description pour les listes et aperçus"
                      value={newProduit.description}
                      onChange={(e) => setNewProduit((prev) => ({ ...prev, description: e.target.value }))}
                      fullWidth
                      multiline
                      rows={2}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Catégorie
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        value={newProduit.category}
                        onChange={(e) => setNewProduit(prev => ({ ...prev, category: e.target.value }))}
                        displayEmpty
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                          }
                        }}
                      >
                        <MenuItem value="Non classé">Non classé</MenuItem>
                        <MenuItem value="Service">Service</MenuItem>
                        <MenuItem value="Produit physique">Produit physique</MenuItem>
                        <MenuItem value="Logiciel">Logiciel</MenuItem>
                        <MenuItem value="Abonnement">Abonnement</MenuItem>
                        <MenuItem value="Formation">Formation</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Prix unitaire
                    </Typography>
                    <TextField
                      name="prix"
                      placeholder="0.00"
                      type="number"
                      value={newProduit.prix}
                      onChange={(e) =>
                        setNewProduit((prev) => ({ ...prev, prix: parseFloat(e.target.value) || 0 }))
                      }
                      fullWidth
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: (
                          <Typography variant="body1" color="text.secondary" sx={{ mr: 1 }}>
                            TND
                          </Typography>
                        ),
                      }}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                    {newProduit.prix <= 0 && (
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mt: 1,
                        color: theme.palette.warning.main
                      }}>
                        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Le prix doit être supérieur à 0
                        </Typography>
                      </Box>
                    )}
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Unité
                    </Typography>
                    <TextField
                      name="unite"
                      placeholder="unité"
                      value={newProduit.unite}
                      onChange={(e) => setNewProduit((prev) => ({ ...prev, unite: e.target.value }))}
                      fullWidth
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Box sx={{
                  width: 200,
                  height: 200,
                  bgcolor: 'grey.100',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 2,
                  borderRadius: 2
                }}>
                  <Typography variant="body2" color="text.secondary">Aucune image</Typography>
                </Box>

                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<ShoppingCartIcon />}
                  sx={{ borderRadius: 2 }}
                >
                  Ajouter une image
                </Button>
              </Grid>
            </Grid>
          )}

          {produitTabValue === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                  Description détaillée
                </Typography>
                <TextField
                  name="descriptionDetaille"
                  placeholder="Description complète du produit ou service"
                  value={newProduit.descriptionDetaille}
                  onChange={(e) => setNewProduit((prev) => ({ ...prev, descriptionDetaille: e.target.value }))}
                  fullWidth
                  multiline
                  rows={8}
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: 1.5,
                    }
                  }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Cette description détaillée sera visible sur les fiches produit et les documents détaillés.
                </Typography>
              </Grid>
            </Grid>
          )}

          {produitTabValue === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Switch
                    checked={newProduit.gestionStock}
                    onChange={(e) => setNewProduit(prev => ({ ...prev, gestionStock: e.target.checked }))}
                    color="primary"
                  />
                  <Typography variant="subtitle1" sx={{ ml: 1 }}>
                    Activer la gestion des stocks
                  </Typography>
                </Box>

                {newProduit.gestionStock && (
                  <>
                    <Grid container spacing={3}>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                          Quantité en stock
                        </Typography>
                        <TextField
                          name="quantiteStock"
                          type="number"
                          value={newProduit.quantiteStock}
                          onChange={(e) => setNewProduit((prev) => ({ ...prev, quantiteStock: parseInt(e.target.value) || 0 }))}
                          fullWidth
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                          }}
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                              borderRadius: 1.5,
                            }
                          }}
                        />
                      </Grid>

                      <Grid item xs={6}>
                        <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                          Seuil d'alerte
                        </Typography>
                        <TextField
                          name="seuilAlerte"
                          type="number"
                          value={newProduit.seuilAlerte}
                          onChange={(e) => setNewProduit((prev) => ({ ...prev, seuilAlerte: parseInt(e.target.value) || 0 }))}
                          fullWidth
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                          }}
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                              borderRadius: 1.5,
                            }
                          }}
                        />
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          Vous recevrez une alerte lorsque le stock sera inférieur à ce seuil.
                        </Typography>
                      </Grid>
                    </Grid>
                  </>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => {
              setOpenProduitDialog(false);
              setProduitTabValue(0);
            }}
            variant="outlined"
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.secondary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                backgroundColor: alpha(theme.palette.grey[400], 0.05),
              }
            }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateProduit}
            disabled={!newProduit.nom || newProduit.prix <= 0}
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
              }
            }}
          >
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        TransitionComponent={(props) => (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            {...props}
          />
        )}
      >
        <Alert
          onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          variant="filled"
          elevation={6}
          sx={{
            width: '100%',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            '& .MuiAlert-icon': {
              fontSize: '1.5rem'
            }
          }}
        >
          <Box sx={{ ml: 1 }}>
            <Typography variant="subtitle2" fontWeight={600}>
              {snackbar.severity === 'success' ? 'Succès!' :
               snackbar.severity === 'error' ? 'Erreur!' :
               snackbar.severity === 'warning' ? 'Attention!' : 'Information'}
            </Typography>
            <Typography variant="body2">
              {snackbar.message}
            </Typography>
          </Box>
        </Alert>
      </Snackbar>

      {/* Email Dialog */}
      <EmailDialog
        open={openEmailDialog}
        onClose={handleCloseEmailDialog}
        onSend={handleSendEmail}
        documentType="facture"
        documentNumber={facture.numero}
        recipientEmail={facture.clientId?.email}
      />
    </Box>
  );
};

export default FactureForm;