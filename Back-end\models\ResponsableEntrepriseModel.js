const mongoose = require('mongoose');
const { Schema } = mongoose;

const responsableEntrepriseSchema = new Schema({
  // Informations de base
  nom: { type: String, required: true },
  raisonSociale: { type: String },
  numeroFiscal: { type: String }, // Numéro fiscal (required in the form)

  // Coordonnées
  adresse: { type: String }, // Adresse de l'entreprise (required in the form)
  pays: { type: String, default: 'France' },
  telephone: { type: String }, // Téléphone de l'entreprise (required in the form)
  email: { type: String },

  // Paramètres de facturation (keeping these as they're essential for business logic)
  prefixeFacture: { type: String, default: 'FACT-' },
  prefixeDevis: { type: String, default: 'DEV-' },
  prochainNumeroFacture: { type: Number, default: 1 },
  prochainNumeroDevis: { type: Number, default: 1 },
  tauxTVA: { type: Number, default: 20 },
  delaiPaiement: { type: Number, default: 30 },

  // Personnalisation visuelle (keeping these as they're used in templates)
  couleurPrincipale: { type: String, default: '#1976D2' },
  logo: { type: String },

  // Métadonnées
  createdAt: { type: Date, default: Date.now },
  // Champ pour suivre qui a créé ce responsable (Admin)
  createdBy: { type: Schema.Types.ObjectId, ref: 'users' },
  // Référence au responsable utilisateur
  responsableId: { type: Schema.Types.ObjectId, ref: 'users' }
});

module.exports = mongoose.model('ResponsableEntreprise', responsableEntrepriseSchema);
