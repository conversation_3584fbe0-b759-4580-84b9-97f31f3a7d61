{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/pages-filename-normalizer.ts"], "sourcesContent": ["import { SERVER_DIRECTORY } from '../../../../shared/lib/constants'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\n\nexport class PagesFilenameNormalizer extends PrefixingNormalizer {\n  constructor(distDir: string) {\n    super(distDir, SERVER_DIRECTORY)\n  }\n\n  public normalize(manifestFilename: string): string {\n    return super.normalize(manifestFilename)\n  }\n}\n"], "names": ["PagesFilenameNormalizer", "PrefixingNormalizer", "constructor", "distDir", "SERVER_DIRECTORY", "normalize", "manifestFilename"], "mappings": ";;;;+BAGaA;;;eAAAA;;;2BAHoB;qCACG;AAE7B,MAAMA,gCAAgCC,wCAAmB;IAC9DC,YAAYC,OAAe,CAAE;QAC3B,KAAK,CAACA,SAASC,2BAAgB;IACjC;IAEOC,UAAUC,gBAAwB,EAAU;QACjD,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF"}