{"version": 3, "sources": ["../../../src/lib/metadata/clone-metadata.ts"], "sourcesContent": ["import type {\n  ResolvedMetadata,\n  ResolvedViewport,\n} from './types/metadata-interface'\n\nconst TYPE_URL = '__METADATA_URL'\n\nfunction replacer(_key: string, val: any) {\n  // clone URL as string but recover it as URL\n  if (val instanceof URL) {\n    return { _type: TYPE_URL, value: val.href }\n  }\n  return val\n}\n\nfunction reviver(_key: string, val: any) {\n  if (typeof val === 'object' && val !== null && val._type === TYPE_URL) {\n    return new URL(val.value)\n  }\n  return val\n}\n\nexport function cloneMetadata<T extends ResolvedMetadata | ResolvedViewport>(\n  metadata: T\n): T {\n  const jsonString = JSON.stringify(metadata, replacer)\n  return JSON.parse(jsonString, reviver)\n}\n"], "names": ["cloneMetadata", "TYPE_URL", "replacer", "_key", "val", "URL", "_type", "value", "href", "reviver", "metadata", "jsonString", "JSON", "stringify", "parse"], "mappings": ";;;;+BAsBgBA;;;eAAAA;;;AAjBhB,MAAMC,WAAW;AAEjB,SAASC,SAASC,IAAY,EAAEC,GAAQ;IACtC,4CAA4C;IAC5C,IAAIA,eAAeC,KAAK;QACtB,OAAO;YAAEC,OAAOL;YAAUM,OAAOH,IAAII,IAAI;QAAC;IAC5C;IACA,OAAOJ;AACT;AAEA,SAASK,QAAQN,IAAY,EAAEC,GAAQ;IACrC,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQA,IAAIE,KAAK,KAAKL,UAAU;QACrE,OAAO,IAAII,IAAID,IAAIG,KAAK;IAC1B;IACA,OAAOH;AACT;AAEO,SAASJ,cACdU,QAAW;IAEX,MAAMC,aAAaC,KAAKC,SAAS,CAACH,UAAUR;IAC5C,OAAOU,KAAKE,KAAK,CAACH,YAAYF;AAChC"}