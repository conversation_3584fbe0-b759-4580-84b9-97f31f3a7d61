// backend/models/ClientActivityModel.js
const mongoose = require('mongoose');
const { Schema } = mongoose;

const clientActivitySchema = new Schema({
  clientId: {
    type: Schema.Types.ObjectId,
    ref: 'Client',
    required: true
  },
  type: {
    type: String,
    enum: ['NOTE', 'APPEL', 'REUNION', 'EMAIL', 'FACTURE', 'DEVIS', 'PAIEMENT'],
    required: true
  },
  titre: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  date: {
    type: Date,
    default: Date.now
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'users'
  },
  documentId: {
    type: Schema.Types.ObjectId
  },
  documentType: {
    type: String,
    enum: ['FACTURE', 'DEVIS', 'PAIEMENT']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('ClientActivity', clientActivitySchema);
