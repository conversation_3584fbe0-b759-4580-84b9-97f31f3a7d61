{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/stop-icon.tsx"], "sourcesContent": ["export function StopIcon(props: React.SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M14.5 8C14.5 11.5899 11.5899 14.5 8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C11.5899 1.5 14.5 4.41015 14.5 8ZM16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM5 7.25H4.25V8.75H5H11H11.75V7.25H11H5Z\"\n        fill=\"#666\"\n      />\n    </svg>\n  )\n}\n"], "names": ["StopIcon", "props", "svg", "xmlns", "width", "height", "viewBox", "path", "fillRule", "clipRule", "d", "fill"], "mappings": ";AAAA,OAAO,SAASA,SAASC,KAAoC;IAC3D,qBACE,KAACC;QACCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACP,GAAGL,KAAK;kBAET,cAAA,KAACM;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFC,MAAK;;;AAIb"}