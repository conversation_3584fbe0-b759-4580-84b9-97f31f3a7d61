const mongoose = require('mongoose');
const { Schema } = mongoose;

// Base Template Schema - <PERSON><PERSON> creates these (simplified structure)
const baseTemplateSchema = new Schema({
    name: { type: String, required: true },
    type: { type: String, required: true, enum: ['facture', 'devis'] },
    layout: {
        type: String,
        enum: ['standard', 'moderne'],
        default: 'standard'
    },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

// Middleware pour mettre à jour automatiquement la date de modification
baseTemplateSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

module.exports = mongoose.model('BaseTemplate', baseTemplateSchema);
