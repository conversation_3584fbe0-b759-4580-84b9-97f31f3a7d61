@startuml InvoiceSystemClassDiagram
!theme plain
skinparam linetype ortho
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}

' Enumerations (positioned separately)
enum UserRole {
    ADMIN
    COMPANY_MANAGER
    SELLER
    CLIENT
}

enum InvoiceStatus {
    DRAFT
    SENT
    PAID
    CANCELED
    ACCEPTED
    REJECTED
    PARTIALLY_PAID
}

enum QuoteStatus {
    DRAFT
    WAITING_APPROVAL
    APPROVED_INTERNAL
    SENT
    PENDING
    ACCEPTED
    REJECTED
    EXPIRED
}

enum PaymentMethod {
    CHECK
    BANK_TRANSFER
    CASH
}

enum PaymentStatus {
    PENDING
    COMPLETED
    FAILED
    REFUNDED
}

enum SubscriptionStatus {
    ACTIVE
    SUSPENDED
    EXPIRED
    CANCELED
}

enum SubscriptionDuration {
    TEN_MINUTES
    ONE_DAY
    SEVEN_DAYS
    THREE_MONTHS
    SIX_MONTHS
    ONE_YEAR
}

enum ProductCategory {
    SERVICE
    PHYSICAL_PRODUCT
    SOFTWARE
    SUBSCRIPTION
    TRAINING
    UNCATEGORIZED
}

enum DeliveryStatus {
    PENDING
    IN_TRANSIT
    DELIVERED
    FAILED
    RETURNED
}

' Main Classes
class User {
    - firstName: String
    - lastName: String
    - email: String
    - password: String
    - role: UserRole
    - isActive: Boolean
    - companyId: ObjectId
    - createdBy: ObjectId
    - createdAt: Date
    - lastLogin: Date
    --
    + authenticate(email: String, password: String): Boolean
    + updateProfile(userData: User): Boolean
    + changePassword(newPassword: String): Boolean
    + resetPassword(email: String): Boolean
    + activateAccount(): Boolean
    + deactivateAccount(): Boolean
    + assignRole(role: UserRole): Boolean
    + managePermissions(): Boolean
}

class Admin {
    - companyName: String
    - companyAddress: String
    - companyPhone: String
    - taxNumber: String
    --
    + createUser(userData: User): User
    + deleteUser(id): Boolean
    + updateUser(id, userData): Boolean
    + createCompanyAccount(data): CompanyManager
    + assignSubscription(managerId, subscriptionId): Boolean
    + reactivateAccount(userId): Boolean
    + suspendAccount(userId): Boolean
    + generateReport(type, parameters): Report
    + configureSystem(settings): Boolean
}

class Seller {
    - name: String
    - legalName: String
    - taxNumber: String
    - address: String
    - country: String
    - phone: String
    - email: String
    - website: String
    - contactName: String
    - contactEmail: String
    - contactPhone: String
    - logo: String
    - companyManagerId: ObjectId
    - createdBy: ObjectId
    - createdAt: Date
    --
    + createCompany(companyData: Company): Boolean
    + updateCompany(companyData: Company): Boolean
    + getCompanyByManager(managerId: String): Company
    + uploadLogo(logoFile: File): Boolean
    + manageTeam(): Boolean
    + viewAnalytics(): Object
    + manageSubscription(): Boolean
}

class Company {
    - name: String
    - legalName: String
    - taxNumber: String
    - vatNumber: String
    - address: String
    - country: String
    - phone: String
    - email: String
    - website: String
    - contactName: String
    - contactEmail: String
    - contactPhone: String
    - logo: String
    - companyManagerId: ObjectId
    - createdBy: ObjectId
    - createdAt: Date
    --
    + createCompany(companyData: Company): Boolean
    + updateCompany(companyData: Company): Boolean
    + getCompanyByManager(managerId: String): Company
    + uploadLogo(logoFile: File): Boolean
    + manageTeam(): Boolean
    + viewAnalytics(): Object
    + manageSubscription(): Boolean
}

class Client {
    - name: String
    - address: String
    - email: String
    - contact: String
    - phone: String
    - nationalId: String
    - logo: String
    - sellerId: ObjectId
    - companyManagers: ObjectId[]
    - createdAt: Date
    - updatedAt: Date
    --
    + createClient(clientData: Client): Boolean
    + updateClient(clientData: Client): Boolean
    + getClient(id: String): Client
    + deleteClient(id: String): Boolean
    + assignToSeller(sellerId: String): Boolean
    + assignToCompanyManager(managerId: String): Boolean
    + viewInvoices(): Invoice[]
    + viewQuotes(): Quote[]
    + makePayment(paymentData: Payment): Boolean
    + downloadDocument(documentId: String): File
}

class Product {
    - name: String
    - description: String
    - price: Number
    - category: ProductCategory
    - stockQuantity: Number
    - minStockLevel: Number
    - isActive: Boolean
    - sellerId: ObjectId
    - companyManagerId: ObjectId
    - createdAt: Date
    - updatedAt: Date
    --
    + createProduct(productData: Product): Boolean
    + updateProduct(productData: Product): Boolean
    + deleteProduct(id: String): Boolean
    + adjustStock(quantity: Number): Boolean
    + checkStockLevel(): Boolean
    + activateProduct(): Boolean
    + deactivateProduct(): Boolean
}

class Invoice {
    - number: String
    - clientId: ObjectId
    - sellerId: ObjectId
    - companyManagerId: ObjectId
    - issueDate: Date
    - lines: LineSchema[]
    - notes: String
    - status: InvoiceStatus
    - total: Number
    - totalExcludingTax: Number
    - taxAmount: Number
    - taxRate: Number
    - quoteId: ObjectId
    - stockAdjusted: Boolean
    - createdAt: Date
    --
    + createInvoice(invoiceData: Invoice): Boolean
    + updateInvoice(invoiceData: Invoice): Boolean
    + calculateTax(): Number
    + calculateTotal(): Number
    + generatePDF(): String
    + sendByEmail(email: String): Boolean
    + convertFromQuote(quoteId: String): Boolean
    + adjustStock(): Boolean
}

class Quote {
    - number: String
    - clientId: ObjectId
    - sellerId: ObjectId
    - companyManagerId: ObjectId
    - creationDate: Date
    - validityDate: Date
    - lines: LineSchema[]
    - notes: String
    - status: QuoteStatus
    - total: Number
    - totalExcludingTax: Number
    - totalIncludingTax: Number
    - taxRate: Number
    - managerValidation: Object
    - clientResponse: Object
    - isClientRequest: Boolean
    - createdAt: Date
    --
    + createQuote(quoteData: Quote): Boolean
    + updateQuote(quoteData: Quote): Boolean
    + calculateTax(): Number
    + calculateTotal(): Number
    + generatePDF(): String
    + sendByEmail(email: String): Boolean
    + convertToInvoice(): Invoice
    + approveByManager(): Boolean
    + rejectByManager(reason: String): Boolean
}

class Payment {
    - invoiceId: ObjectId
    - amount: Number
    - paymentDate: Date
    - method: PaymentMethod
    - status: PaymentStatus
    - reference: String
    - notes: String
    - createdAt: Date
    --
    + processPayment(paymentData: Payment): Boolean
    + updatePaymentStatus(status: PaymentStatus): Boolean
    + generateReceipt(): String
    + refundPayment(amount: Number): Boolean
    + validatePayment(): Boolean
}

class DeliveryPerson {
    - name: String
    - phone: String
    - email: String
    - vehicleInfo: String
    - isActive: Boolean
    - companyId: ObjectId
    - createdAt: Date
    --
    + createDeliveryPerson(deliveryPersonData: DeliveryPerson): Boolean
    + updateDeliveryPerson(deliveryPersonData: DeliveryPerson): Boolean
    + assignDelivery(deliveryNoteId: String): Boolean
    + updateDeliveryStatus(status: DeliveryStatus): Boolean
    + activateDeliveryPerson(): Boolean
    + deactivateDeliveryPerson(): Boolean
}

class DeliveryNote {
    - number: String
    - clientId: ObjectId
    - sellerId: ObjectId
    - companyManagerId: ObjectId
    - deliveryPersonId: ObjectId
    - deliveryDate: Date
    - lines: LineSchema[]
    - notes: String
    - status: DeliveryStatus
    - createdAt: Date
    --
    + createDeliveryNote(deliveryNoteData: DeliveryNote): Boolean
    + updateDeliveryNote(deliveryNoteData: DeliveryNote): Boolean
    + assignDeliveryPerson(deliveryPersonId: String): Boolean
    + updateDeliveryStatus(status: DeliveryStatus): Boolean
    + generatePDF(): String
    + sendByEmail(email: String): Boolean
}

class Subscription {
    - userId: ObjectId
    - planName: String
    - startDate: Date
    - endDate: Date
    - status: SubscriptionStatus
    - duration: SubscriptionDuration
    - price: Number
    - isAutoRenew: Boolean
    - createdAt: Date
    --
    + createSubscription(subscriptionData: Subscription): Boolean
    + updateSubscription(subscriptionData: Subscription): Boolean
    + renewSubscription(): Boolean
    + cancelSubscription(): Boolean
    + checkExpiry(): Boolean
    + suspendSubscription(): Boolean
    + activateSubscription(): Boolean
}

class BaseTemplate {
    - name: String
    - type: String
    - layout: String
    - isActive: Boolean
    - createdAt: Date
    --
    + createTemplate(templateData: BaseTemplate): Boolean
    + updateTemplate(templateData: BaseTemplate): Boolean
    + activateTemplate(): Boolean
    + deactivateTemplate(): Boolean
}

class CompanyTemplate {
    - companyId: ObjectId
    - invoiceTemplate: Object
    - quoteTemplate: Object
    - createdAt: Date
    - updatedAt: Date
    --
    + createCustomization(customizationData: CompanyTemplate): Boolean
    + updateCustomization(customizationData: CompanyTemplate): Boolean
    + applyTemplate(templateId: String, type: String): Boolean
    + uploadLogo(logoFile: File): Boolean
}

class Parameters {
    - defaultCurrency: String
    - defaultTaxRate: Number
    - emailSignature: String
    - invoicePrefix: String
    - quotePrefix: String
    - paymentDelay: Number
    - createdAt: Date
    - updatedAt: Date
    --
    + updateParameters(parameterData: Parameters): Boolean
    + getSystemParameters(): Parameters
    + resetToDefaults(): Boolean
}

class Report {
    - title: String
    - type: String
    - generatedBy: ObjectId
    - dateRange: Object
    - data: Object
    - createdAt: Date
    --
    + generateReport(reportData: Report): Boolean
    + exportToPDF(): String
    + exportToCSV(): String
    + scheduleReport(): Boolean
}

class LineSchema {
    - productId: ObjectId
    - productName: String
    - description: String
    - quantity: Number
    - unitPrice: Number
    - totalPrice: Number
    - taxRate: Number
    --
    + calculateLineTotal(): Number
    + applyDiscount(percentage: Number): Boolean
    + updateQuantity(newQuantity: Number): Boolean
}

' Relationships with Cardinalities

' User relationships
User "1" -- "1" Company : creates
User "1" -- "0..*" User : manages
User "1" -- "0..1" Subscription : has
User "1" -- "0..*" Report : generates

' Admin relationships
Admin "1" -- "0..*" User : creates
Admin "1" -- "0..*" Company : manages
Admin "1" -- "0..*" Subscription : assigns
Admin "1" -- "0..*" Parameters : configures

' Seller relationships
Seller "1" -- "0..*" Client : manages
Seller "1" -- "0..*" Product : creates
Seller "1" -- "0..*" Invoice : generates
Seller "1" -- "0..*" Quote : creates
Seller "1" -- "0..*" DeliveryNote : creates

' Company relationships
Company "1" -- "0..*" Client : manages
Company "1" -- "0..*" Product : owns
Company "1" -- "0..*" Invoice : generates
Company "1" -- "0..*" Quote : creates
Company "1" -- "0..*" DeliveryPerson : manages
Company "1" -- "0..*" DeliveryNote : creates
Company "1" -- "1" CompanyTemplate : customizes

' Client relationships
Client "1" -- "0..*" Invoice : receives
Client "1" -- "0..*" Quote : receives
Client "0..*" -- "0..*" User : managed by

' Business document relationships
Quote "1" -- "0..1" Invoice : converts to
Invoice "1" -- "0..*" Payment : receives

' Delivery relationships
DeliveryPerson "1" -- "0..*" DeliveryNote : assigned to
Client "1" -- "0..*" DeliveryNote : receives

' Template relationships
BaseTemplate "1" -- "0..*" CompanyTemplate : customized by

' Line item relationships
Invoice "1" -- "0..*" LineSchema : contains
Quote "1" -- "0..*" LineSchema : contains
DeliveryNote "1" -- "0..*" LineSchema : contains
LineSchema "0..*" -- "1" Product : references

@enduml
