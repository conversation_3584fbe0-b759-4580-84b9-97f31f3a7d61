import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  LocalShipping as TruckIcon
} from '@mui/icons-material';
import bonLivraisonService from '../services/bonLivraisonService';

const BonLivraisonViewDialog = ({ open, onClose, bonLivraison }) => {
  if (!bonLivraison) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <AssignmentIcon color="primary" />
          <Typography variant="h6">
            Détails du bon de livraison N° {bonLivraison.numero}
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            {/* Informations générales */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    Informations générales
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Numéro
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {bonLivraison.numero}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Date de livraison
                      </Typography>
                      <Typography variant="body1">
                        {new Date(bonLivraison.dateLivraison).toLocaleDateString('fr-FR')}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Statut
                      </Typography>
                      <Chip
                        label={bonLivraisonService.getStatutLabel(bonLivraison.statut)}
                        color={bonLivraisonService.getStatutColor(bonLivraison.statut)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Montant total
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {bonLivraisonService.formatMontant(bonLivraison.montantTotal)}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Informations client */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    Client
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Nom
                  </Typography>
                  <Typography variant="body1" fontWeight="bold" gutterBottom>
                    {bonLivraison.clientId?.nom || 'Non spécifié'}
                  </Typography>

                  {bonLivraison.clientId?.email && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {bonLivraison.clientId.email}
                      </Typography>
                    </>
                  )}

                  {bonLivraison.clientId?.telephone && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Téléphone
                      </Typography>
                      <Typography variant="body1">
                        {bonLivraison.clientId.telephone}
                      </Typography>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Informations livreur */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    <TruckIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Livreur
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Nom
                  </Typography>
                  <Typography variant="body1" fontWeight="bold" gutterBottom>
                    {bonLivraison.livreurId?.prenom} {bonLivraison.livreurId?.nom}
                  </Typography>

                  {bonLivraison.livreurId?.telephone && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Téléphone
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {bonLivraison.livreurId.telephone}
                      </Typography>
                    </>
                  )}

                  {bonLivraison.livreurId?.vehicule && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Véhicule
                      </Typography>
                      <Typography variant="body1">
                        {bonLivraison.livreurId.vehicule?.type || 'Non spécifié'}
                        {bonLivraison.livreurId.vehicule?.marque ? ` - ${bonLivraison.livreurId.vehicule.marque}` : ''}
                        {bonLivraison.livreurId.vehicule?.modele ? ` ${bonLivraison.livreurId.vehicule.modele}` : ''}
                        {bonLivraison.livreurId.vehicule?.immatriculation ? ` (${bonLivraison.livreurId.vehicule.immatriculation})` : ''}
                      </Typography>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Facture liée */}
            {bonLivraison.factureId && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">
                      Facture liée
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Numéro de facture
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {bonLivraison.factureId?.numero || bonLivraison.factureId}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Adresse de livraison */}
            {bonLivraison.adresseLivraison?.adresse && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">
                      Adresse de livraison
                    </Typography>
                    <Typography variant="body1">
                      {bonLivraison.adresseLivraison.adresse}
                    </Typography>
                    {bonLivraison.adresseLivraison.ville && (
                      <Typography variant="body1">
                        {bonLivraison.adresseLivraison.codePostal} {bonLivraison.adresseLivraison.ville}
                      </Typography>
                    )}
                    {bonLivraison.adresseLivraison.instructions && (
                      <>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          Instructions
                        </Typography>
                        <Typography variant="body1">
                          {bonLivraison.adresseLivraison.instructions}
                        </Typography>
                      </>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Notes */}
            {bonLivraison.notes && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">
                      Notes
                    </Typography>
                    <Typography variant="body1">
                      {bonLivraison.notes}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Observations du livreur */}
            {bonLivraison.observationsLivreur && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">
                      Observations du livreur
                    </Typography>
                    <Typography variant="body1">
                      {bonLivraison.observationsLivreur}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BonLivraisonViewDialog;
