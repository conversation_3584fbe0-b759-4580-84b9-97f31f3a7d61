{"name": "@types/react-loadable", "version": "5.5.11", "description": "TypeScript definitions for react-loadable", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-loadable", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Jessidhia", "url": "https://github.com/Jessidhia"}, {"name": "Oden S.", "githubUsername": "odensc", "url": "https://github.com/odensc"}, {"name": "<PERSON>", "githubUsername": "ianks", "url": "https://github.com/ianks"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tlaziuk"}, {"name": "<PERSON>", "githubUsername": "iMobs", "url": "https://github.com/iMobs"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-loadable"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/webpack": "^4"}, "typesPublisherContentHash": "ec9de5b249a760990e75a8db79b96a62fe8fc62c5d2679b46579a3d8a31d9a52", "typeScriptVersion": "4.5"}