// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`metadata browser animated.png 1`] = `
Object {
  "animation": Object {
    "frames": Array [
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          125,
          210,
          203,
          106,
          84,
          65,
          24,
          4,
          224,
          239,
          204,
          104,
          28,
          21,
          98,
          2,
          154,
          11,
          222,
          2,
          26,
          66,
          12,
          10,
          9,
          134,
          16,
          209,
          149,
          193,
          119,
          80,
          119,
          190,
          129,
          11,
          31,
          192,
          55,
          240,
          61,
          124,
          5,
          151,
          130,
          217,
          153,
          224,
          194,
          24,
          7,
          23,
          138,
          10,
          34,
          94,
          32,
          142,
          56,
          224,
          101,
          209,
          117,
          146,
          113,
          102,
          146,
          130,
          3,
          221,
          244,
          223,
          85,
          117,
          170,
          154,
          65,
          52,
          122,
          214,
          183,
          240,
          24,
          205,
          33,
          115,
          3,
          195,
          48,
          141,
          71,
          88,
          200,
          254,
          60,
          110,
          100,
          125,
          56,
          132,
          23,
          15,
          34,
          248,
          131,
          107,
          120,
          144,
          125,
          11,
          93,
          252,
          14,
          233,
          4,
          118,
          122,
          47,
          212,
          214,
          166,
          113,
          12,
          159,
          240,
          30,
          247,
          240,
          18,
          231,
          176,
          136,
          167,
          184,
          132,
          87,
          216,
          142,
          155,
          147,
          232,
          52,
          227,
          226,
          62,
          238,
          226,
          91,
          134,
          151,
          226,
          230,
          73,
          246,
          103,
          241,
          3,
          235,
          56,
          133,
          235,
          152,
          67,
          187,
          138,
          131,
          25,
          220,
          198,
          114,
          20,
          30,
          134,
          224,
          87,
          143,
          211,
          70,
          136,
          103,
          241,
          5,
          155,
          113,
          251,
          31,
          174,
          226,
          206,
          144,
          108,
          106,
          44,
          68,
          121,
          23,
          21,
          214,
          48,
          137,
          191,
          9,
          235,
          25,
          222,
          238,
          67,
          112,
          28,
          151,
          49,
          146,
          187,
          157,
          67,
          9,
          99,
          34,
          4,
          93,
          7,
          116,
          30,
          103,
          163,
          56,
          18,
          130,
          1,
          167,
          167,
          113,
          37,
          135,
          195,
          48,
          147,
          153,
          93,
          212,
          106,
          45,
          172,
          96,
          85,
          169,
          115,
          91,
          9,
          177,
          31,
          75,
          17,
          24,
          197,
          87,
          116,
          155,
          81,
          187,
          25,
          230,
          23,
          74,
          85,
          139,
          56,
          129,
          14,
          142,
          98,
          74,
          9,
          120,
          93,
          121,
          72,
          115,
          184,
          128,
          118,
          237,
          160,
          178,
          247,
          72,
          198,
          149,
          58,
          223,
          225,
          12,
          230,
          241,
          33,
          202,
          29,
          108,
          229,
          108,
          7,
          159,
          235,
          16,
          218,
          248,
          168,
          164,
          187,
          18,
          123,
          175,
          19,
          86,
          43,
          4,
          111,
          226,
          108,
          28,
          223,
          235,
          223,
          236,
          79,
          113,
          44,
          151,
          158,
          43,
          173,
          52,
          242,
          85,
          216,
          192,
          79,
          165,
          242,
          125,
          81,
          69,
          177,
          55,
          180,
          53,
          123,
          173,
          140,
          232,
          171,
          249,
          31,
          210,
          209,
          71,
          210,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          125,
          210,
          77,
          111,
          204,
          81,
          24,
          5,
          240,
          223,
          204,
          127,
          90,
          45,
          29,
          98,
          188,
          52,
          88,
          144,
          145,
          136,
          168,
          4,
          155,
          46,
          42,
          33,
          209,
          136,
          21,
          9,
          43,
          95,
          192,
          74,
          72,
          108,
          248,
          42,
          62,
          128,
          79,
          99,
          33,
          145,
          144,
          104,
          53,
          105,
          66,
          27,
          137,
          137,
          182,
          40,
          245,
          210,
          81,
          139,
          123,
          254,
          50,
          198,
          196,
          217,
          220,
          231,
          62,
          247,
          220,
          243,
          188,
          242,
          55,
          118,
          161,
          133,
          102,
          238,
          215,
          241,
          0,
          85,
          238,
          7,
          134,
          248,
          127,
          136,
          53,
          58,
          120,
          132,
          83,
          249,
          116,
          19,
          243,
          177,
          59,
          184,
          131,
          123,
          163,
          4,
          234,
          115,
          11,
          39,
          112,
          27,
          125,
          188,
          194,
          120,
          236,
          171,
          184,
          143,
          181,
          65,
          129,
          10,
          13,
          116,
          177,
          31,
          239,
          34,
          114,
          3,
          75,
          184,
          140,
          79,
          241,
          221,
          69,
          15,
          15,
          243,
          231,
          56,
          62,
          86,
          17,
          185,
          133,
          179,
          216,
          139,
          69,
          180,
          49,
          137,
          39,
          248,
          146,
          168,
          215,
          240,
          24,
          31,
          112,
          9,
          135,
          176,
          222,
          72,
          38,
          39,
          83,
          235,
          65,
          188,
          198,
          115,
          188,
          193,
          183,
          188,
          183,
          113,
          30,
          71,
          149,
          38,
          175,
          224,
          45,
          86,
          37,
          157,
          26,
          243,
          184,
          24,
          210,
          48,
          154,
          17,
          233,
          98,
          95,
          237,
          108,
          225,
          2,
          38,
          146,
          210,
          106,
          82,
          238,
          143,
          16,
          248,
          133,
          23,
          9,
          48,
          165,
          140,
          116,
          173,
          153,
          135,
          233,
          212,
          217,
          77,
          35,
          119,
          70,
          8,
          72,
          144,
          207,
          225,
          193,
          177,
          186,
          190,
          74,
          105,
          90,
          7,
          51,
          254,
          221,
          143,
          26,
          135,
          49,
          27,
          254,
          148,
          24,
          63,
          176,
          59,
          170,
          179,
          248,
          138,
          159,
          41,
          101,
          16,
          109,
          156,
          78,
          182,
          211,
          216,
          196,
          102,
          11,
          99,
          152,
          11,
          97,
          25,
          239,
          147,
          218,
          68,
          68,
          154,
          137,
          118,
          4,
          11,
          216,
          86,
          118,
          96,
          12,
          235,
          85,
          234,
          253,
          174,
          140,
          102,
          89,
          217,
          133,
          51,
          241,
          79,
          38,
          234,
          82,
          178,
          219,
          194,
          51,
          101,
          161,
          182,
          209,
          107,
          134,
          184,
          162,
          76,
          160,
          254,
          176,
          131,
          167,
          137,
          220,
          79,
          227,
          22,
          112,
          46,
          190,
          158,
          178,
          230,
          253,
          225,
          102,
          85,
          202,
          104,
          95,
          42,
          211,
          105,
          97,
          79,
          222,
          22,
          177,
          145,
          70,
          254,
          23,
          237,
          1,
          123,
          6,
          87,
          34,
          212,
          80,
          102,
          63,
          62,
          72,
          254,
          13,
          202,
          226,
          83,
          36,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          117,
          211,
          75,
          111,
          204,
          97,
          20,
          6,
          240,
          223,
          127,
          102,
          234,
          58,
          116,
          220,
          162,
          117,
          173,
          186,
          196,
          53,
          70,
          162,
          98,
          97,
          65,
          34,
          54,
          68,
          108,
          44,
          172,
          248,
          0,
          62,
          130,
          175,
          97,
          225,
          123,
          116,
          97,
          193,
          154,
          173,
          72,
          234,
          146,
          150,
          16,
          65,
          245,
          66,
          90,
          18,
          109,
          98,
          88,
          188,
          207,
          36,
          147,
          49,
          61,
          155,
          247,
          118,
          206,
          115,
          206,
          121,
          206,
          243,
          242,
          191,
          141,
          160,
          202,
          254,
          54,
          238,
          100,
          95,
          161,
          129,
          225,
          94,
          231,
          90,
          95,
          240,
          73,
          220,
          192,
          241,
          156,
          143,
          225,
          50,
          214,
          227,
          8,
          30,
          102,
          29,
          8,
          80,
          97,
          17,
          59,
          48,
          145,
          108,
          29,
          124,
          194,
          10,
          238,
          226,
          18,
          190,
          247,
          198,
          214,
          115,
          56,
          24,
          231,
          133,
          100,
          59,
          129,
          117,
          216,
          136,
          189,
          152,
          195,
          3,
          60,
          194,
          99,
          28,
          192,
          40,
          86,
          27,
          216,
          138,
          163,
          216,
          142,
          105,
          188,
          193,
          30,
          124,
          192,
          79,
          76,
          226,
          34,
          158,
          226,
          9,
          174,
          227,
          22,
          218,
          184,
          41,
          165,
          142,
          196,
          233,
          30,
          174,
          4,
          96,
          168,
          143,
          159,
          54,
          238,
          99,
          6,
          175,
          3,
          162,
          158,
          94,
          150,
          211,
          255,
          231,
          56,
          207,
          224,
          79,
          31,
          192,
          55,
          156,
          193,
          95,
          92,
          11,
          72,
          85,
          225,
          116,
          122,
          250,
          138,
          93,
          120,
          134,
          37,
          131,
          237,
          42,
          126,
          167,
          237,
          69,
          12,
          213,
          176,
          45,
          21,
          236,
          195,
          172,
          50,
          133,
          181,
          236,
          121,
          130,
          103,
          147,
          108,
          185,
          251,
          176,
          33,
          235,
          89,
          236,
          95,
          35,
          184,
          194,
          97,
          133,
          159,
          102,
          238,
          54,
          117,
          199,
          184,
          19,
          231,
          148,
          209,
          12,
          99,
          94,
          225,
          160,
          147,
          247,
          110,
          165,
          163,
          184,
          16,
          176,
          85,
          44,
          117,
          73,
          108,
          43,
          243,
          254,
          130,
          151,
          138,
          34,
          91,
          1,
          105,
          97,
          75,
          178,
          207,
          199,
          231,
          16,
          198,
          240,
          174,
          222,
          147,
          97,
          26,
          175,
          2,
          212,
          198,
          11,
          156,
          10,
          64,
          7,
          227,
          248,
          133,
          183,
          10,
          225,
          11,
          248,
          81,
          83,
          198,
          242,
          62,
          151,
          13,
          156,
          87,
          68,
          52,
          167,
          72,
          184,
          137,
          41,
          69,
          194,
          99,
          216,
          157,
          253,
          71,
          116,
          250,
          63,
          83,
          75,
          81,
          223,
          84,
          178,
          110,
          86,
          36,
          45,
          173,
          173,
          244,
          240,
          50,
          208,
          26,
          138,
          164,
          107,
          33,
          106,
          66,
          81,
          104,
          247,
          123,
          55,
          251,
          3,
          254,
          1,
          211,
          69,
          82,
          64,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          117,
          211,
          95,
          75,
          21,
          81,
          20,
          5,
          240,
          223,
          157,
          185,
          94,
          181,
          130,
          76,
          80,
          83,
          3,
          35,
          162,
          50,
          81,
          72,
          11,
          122,
          233,
          193,
          192,
          36,
          250,
          2,
          37,
          190,
          251,
          234,
          7,
          232,
          155,
          68,
          16,
          65,
          31,
          194,
          103,
          161,
          71,
          73,
          72,
          252,
          67,
          150,
          22,
          145,
          136,
          152,
          47,
          55,
          45,
          252,
          211,
          195,
          217,
          19,
          211,
          245,
          222,
          13,
          3,
          103,
          207,
          156,
          181,
          246,
          58,
          235,
          172,
          201,
          157,
          175,
          74,
          105,
          253,
          28,
          87,
          241,
          57,
          250,
          12,
          103,
          229,
          205,
          89,
          3,
          248,
          38,
          158,
          160,
          35,
          250,
          126,
          244,
          197,
          250,
          58,
          94,
          198,
          187,
          150,
          4,
          117,
          244,
          96,
          34,
          250,
          179,
          210,
          196,
          23,
          184,
          141,
          147,
          70,
          130,
          10,
          122,
          81,
          195,
          15,
          172,
          98,
          24,
          131,
          248,
          19,
          128,
          251,
          120,
          128,
          215,
          216,
          13,
          85,
          253,
          144,
          135,
          220,
          169,
          144,
          120,
          16,
          36,
          215,
          112,
          136,
          21,
          124,
          197,
          61,
          84,
          241,
          46,
          142,
          248,
          42,
          246,
          47,
          20,
          74,
          6,
          240,
          12,
          179,
          120,
          24,
          155,
          203,
          102,
          102,
          232,
          196,
          219,
          80,
          248,
          6,
          35,
          13,
          199,
          87,
          193,
          173,
          102,
          31,
          162,
          218,
          49,
          143,
          199,
          141,
          160,
          113,
          92,
          196,
          41,
          126,
          199,
          132,
          122,
          11,
          146,
          49,
          76,
          227,
          40,
          158,
          237,
          42,
          186,
          209,
          21,
          4,
          71,
          216,
          104,
          1,
          38,
          25,
          59,
          39,
          25,
          94,
          195,
          98,
          161,
          162,
          56,
          239,
          0,
          134,
          90,
          128,
          115,
          204,
          132,
          226,
          162,
          207,
          243,
          104,
          46,
          72,
          215,
          52,
          30,
          204,
          95,
          154,
          16,
          84,
          37,
          231,
          7,
          3,
          188,
          135,
          227,
          60,
          0,
          79,
          165,
          0,
          45,
          225,
          35,
          30,
          73,
          1,
          58,
          65,
          155,
          100,
          238,
          16,
          222,
          75,
          55,
          114,
          87,
          202,
          206,
          102,
          161,
          224,
          20,
          31,
          240,
          13,
          55,
          112,
          7,
          155,
          161,
          168,
          11,
          191,
          48,
          42,
          101,
          100,
          67,
          10,
          83,
          29,
          251,
          89,
          76,
          89,
          195,
          79,
          92,
          150,
          66,
          179,
          133,
          239,
          210,
          213,
          213,
          176,
          142,
          253,
          32,
          108,
          195,
          14,
          62,
          113,
          254,
          95,
          232,
          11,
          194,
          229,
          232,
          203,
          6,
          47,
          227,
          18,
          174,
          52,
          241,
          231,
          95,
          101,
          49,
          177,
          168,
          73,
          201,
          220,
          162,
          58,
          252,
          159,
          80,
          127,
          1,
          189,
          154,
          70,
          180,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          117,
          211,
          207,
          110,
          77,
          81,
          20,
          6,
          240,
          159,
          158,
          171,
          66,
          35,
          218,
          170,
          170,
          106,
          36,
          68,
          252,
          109,
          210,
          208,
          164,
          12,
          68,
          27,
          17,
          34,
          6,
          30,
          128,
          169,
          129,
          87,
          48,
          240,
          0,
          30,
          195,
          131,
          212,
          180,
          41,
          98,
          130,
          104,
          110,
          194,
          136,
          84,
          132,
          212,
          173,
          91,
          138,
          150,
          193,
          254,
          174,
          156,
          123,
          115,
          187,
          146,
          147,
          181,
          87,
          246,
          58,
          107,
          125,
          235,
          251,
          214,
          166,
          219,
          246,
          96,
          95,
          206,
          187,
          113,
          25,
          179,
          181,
          251,
          163,
          61,
          249,
          6,
          122,
          226,
          17,
          44,
          96,
          63,
          254,
          98,
          28,
          99,
          185,
          59,
          141,
          59,
          56,
          213,
          175,
          192,
          174,
          248,
          31,
          56,
          152,
          228,
          63,
          137,
          171,
          124,
          243,
          104,
          224,
          123,
          45,
          95,
          149,
          224,
          8,
          246,
          226,
          107,
          226,
          115,
          88,
          197,
          9,
          180,
          113,
          44,
          157,
          159,
          161,
          153,
          38,
          231,
          241,
          177,
          10,
          138,
          121,
          76,
          133,
          131,
          85,
          12,
          6,
          193,
          107,
          252,
          196,
          239,
          228,
          189,
          199,
          21,
          60,
          200,
          152,
          203,
          85,
          102,
          109,
          99,
          34,
          72,
          182,
          240,
          22,
          159,
          50,
          194,
          26,
          214,
          3,
          253,
          54,
          110,
          98,
          17,
          79,
          211,
          172,
          139,
          200,
          217,
          192,
          222,
          201,
          174,
          43,
          202,
          12,
          231,
          191,
          70,
          21,
          72,
          83,
          152,
          169,
          117,
          91,
          223,
          161,
          64,
          27,
          143,
          113,
          8,
          143,
          176,
          86,
          41,
          50,
          77,
          224,
          3,
          38,
          241,
          38,
          243,
          247,
          179,
          133,
          52,
          185,
          134,
          21,
          217,
          153,
          206,
          226,
          28,
          192,
          97,
          76,
          171,
          201,
          84,
          179,
          33,
          220,
          194,
          221,
          196,
          227,
          20,
          25,
          41,
          178,
          156,
          193,
          69,
          124,
          193,
          47,
          133,
          192,
          142,
          85,
          152,
          195,
          67,
          92,
          82,
          84,
          122,
          135,
          86,
          149,
          202,
          87,
          227,
          95,
          165,
          192,
          180,
          34,
          223,
          152,
          66,
          214,
          73,
          92,
          192,
          19,
          156,
          197,
          253,
          240,
          241,
          178,
          161,
          16,
          246,
          34,
          190,
          149,
          196,
          225,
          160,
          26,
          201,
          56,
          77,
          108,
          224,
          27,
          238,
          225,
          134,
          178,
          108,
          155,
          157,
          17,
          90,
          216,
          84,
          200,
          156,
          193,
          103,
          60,
          175,
          33,
          89,
          73,
          193,
          227,
          41,
          214,
          196,
          18,
          182,
          123,
          31,
          211,
          64,
          186,
          52,
          19,
          15,
          42,
          219,
          185,
          173,
          168,
          179,
          129,
          209,
          62,
          4,
          119,
          217,
          80,
          237,
          60,
          151,
          145,
          58,
          197,
          71,
          149,
          7,
          245,
          223,
          254,
          1,
          206,
          6,
          82,
          113,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          117,
          211,
          77,
          75,
          21,
          113,
          20,
          6,
          240,
          159,
          119,
          174,
          22,
          146,
          152,
          90,
          208,
          139,
          73,
          132,
          25,
          25,
          21,
          145,
          133,
          68,
          180,
          210,
          93,
          109,
          219,
          183,
          109,
          221,
          7,
          232,
          115,
          180,
          110,
          211,
          23,
          104,
          81,
          8,
          70,
          80,
          20,
          45,
          162,
          160,
          133,
          98,
          81,
          139,
          44,
          130,
          74,
          172,
          172,
          174,
          93,
          93,
          252,
          159,
          11,
          211,
          85,
          15,
          204,
          204,
          153,
          249,
          159,
          231,
          57,
          207,
          121,
          25,
          182,
          218,
          48,
          122,
          226,
          95,
          198,
          133,
          248,
          77,
          28,
          192,
          238,
          122,
          112,
          163,
          11,
          124,
          12,
          211,
          56,
          84,
          35,
          27,
          140,
          127,
          56,
          103,
          131,
          117,
          64,
          157,
          160,
          7,
          43,
          24,
          192,
          120,
          50,
          174,
          227,
          7,
          42,
          156,
          196,
          46,
          124,
          77,
          124,
          213,
          185,
          53,
          48,
          129,
          54,
          190,
          5,
          56,
          138,
          141,
          124,
          171,
          66,
          62,
          133,
          39,
          248,
          130,
          179,
          56,
          142,
          229,
          42,
          146,
          206,
          96,
          18,
          189,
          248,
          156,
          76,
          111,
          241,
          19,
          239,
          177,
          31,
          191,
          146,
          224,
          42,
          46,
          134,
          124,
          161,
          66,
          43,
          160,
          127,
          56,
          145,
          231,
          107,
          172,
          230,
          106,
          37,
          235,
          26,
          174,
          96,
          31,
          22,
          48,
          143,
          149,
          10,
          123,
          82,
          231,
          26,
          62,
          68,
          250,
          39,
          91,
          237,
          119,
          200,
          214,
          241,
          48,
          184,
          191,
          77,
          156,
          194,
          17,
          124,
          140,
          212,
          7,
          219,
          128,
          69,
          217,
          8,
          142,
          226,
          14,
          222,
          97,
          181,
          145,
          38,
          46,
          41,
          243,
          125,
          163,
          140,
          110,
          39,
          123,
          161,
          76,
          232,
          158,
          210,
          236,
          167,
          245,
          195,
          6,
          46,
          41,
          187,
          176,
          157,
          245,
          227,
          38,
          174,
          69,
          9,
          244,
          86,
          113,
          198,
          149,
          173,
          27,
          168,
          213,
          217,
          138,
          108,
          56,
          168,
          140,
          122,
          18,
          183,
          48,
          134,
          63,
          88,
          234,
          236,
          193,
          84,
          130,
          23,
          177,
          140,
          115,
          202,
          236,
          43,
          204,
          134,
          124,
          58,
          210,
          95,
          97,
          6,
          67,
          152,
          107,
          42,
          93,
          127,
          25,
          198,
          239,
          202,
          238,
          143,
          224,
          89,
          74,
          106,
          43,
          205,
          189,
          142,
          199,
          184,
          143,
          71,
          249,
          214,
          238,
          174,
          115,
          24,
          55,
          112,
          58,
          239,
          51,
          202,
          236,
          225,
          57,
          238,
          42,
          255,
          196,
          127,
          141,
          171,
          91,
          159,
          178,
          121,
          139,
          202,
          86,
          246,
          215,
          98,
          110,
          99,
          111,
          20,
          239,
          104,
          205,
          144,
          8,
          193,
          121,
          165,
          113,
          29,
          27,
          235,
          6,
          108,
          2,
          239,
          163,
          84,
          143,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          125,
          210,
          79,
          75,
          85,
          81,
          20,
          5,
          240,
          223,
          125,
          215,
          63,
          89,
          10,
          134,
          102,
          214,
          115,
          16,
          161,
          53,
          8,
          250,
          71,
          56,
          139,
          40,
          36,
          4,
          3,
          27,
          41,
          84,
          227,
          102,
          13,
          251,
          20,
          126,
          9,
          231,
          126,
          135,
          6,
          6,
          53,
          169,
          97,
          4,
          17,
          65,
          18,
          61,
          20,
          139,
          87,
          190,
          192,
          74,
          125,
          14,
          246,
          214,
          244,
          234,
          107,
          193,
          225,
          238,
          115,
          247,
          57,
          123,
          175,
          179,
          246,
          42,
          29,
          70,
          137,
          110,
          108,
          163,
          192,
          20,
          6,
          209,
          200,
          252,
          137,
          204,
          237,
          163,
          86,
          41,
          48,
          129,
          25,
          156,
          204,
          253,
          32,
          78,
          101,
          124,
          30,
          15,
          112,
          230,
          127,
          5,
          86,
          209,
          135,
          235,
          104,
          99,
          39,
          87,
          129,
          27,
          248,
          141,
          102,
          149,
          114,
          45,
          59,
          111,
          101,
          114,
          11,
          87,
          176,
          142,
          97,
          252,
          68,
          63,
          198,
          177,
          156,
          251,
          115,
          24,
          197,
          247,
          189,
          55,
          223,
          193,
          37,
          252,
          197,
          74,
          82,
          255,
          129,
          15,
          88,
          195,
          16,
          126,
          225,
          11,
          174,
          98,
          18,
          189,
          248,
          84,
          36,
          147,
          1,
          92,
          195,
          5,
          124,
          197,
          11,
          71,
          209,
          133,
          89,
          156,
          198,
          123,
          188,
          197,
          102,
          245,
          80,
          61,
          139,
          28,
          135,
          34,
          187,
          215,
          171,
          85,
          239,
          38,
          131,
          118,
          138,
          244,
          186,
          67,
          129,
          54,
          54,
          146,
          69,
          79,
          22,
          108,
          116,
          161,
          37,
          148,
          110,
          227,
          79,
          198,
          157,
          176,
          141,
          111,
          66,
          252,
          66,
          8,
          125,
          8,
          151,
          197,
          4,
          142,
          67,
          13,
          143,
          112,
          235,
          224,
          207,
          50,
          191,
          117,
          220,
          22,
          163,
          106,
          9,
          229,
          139,
          100,
          69,
          40,
          222,
          141,
          121,
          60,
          196,
          69,
          49,
          145,
          102,
          41,
          236,
          121,
          95,
          140,
          240,
          21,
          62,
          99,
          90,
          24,
          103,
          76,
          184,
          114,
          14,
          79,
          241,
          92,
          76,
          105,
          58,
          115,
          47,
          101,
          167,
          145,
          236,
          32,
          41,
          62,
          198,
          77,
          124,
          204,
          75,
          247,
          196,
          232,
          102,
          242,
          204,
          136,
          48,
          211,
          17,
          140,
          226,
          137,
          176,
          50,
          97,
          170,
          103,
          25,
          47,
          226,
          13,
          206,
          86,
          133,
          57,
          136,
          254,
          124,
          255,
          187,
          212,
          167,
          199,
          63,
          179,
          44,
          228,
          19,
          171,
          119,
          58,
          162,
          196,
          146,
          208,
          167,
          83,
          67,
          187,
          125,
          78,
          72,
          159,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          125,
          210,
          187,
          107,
          84,
          97,
          16,
          5,
          240,
          95,
          118,
          215,
          168,
          108,
          204,
          70,
          178,
          26,
          140,
          137,
          160,
          162,
          24,
          140,
          18,
          98,
          169,
          32,
          233,
          212,
          54,
          104,
          45,
          22,
          254,
          33,
          118,
          150,
          86,
          150,
          118,
          86,
          98,
          37,
          130,
          189,
          157,
          133,
          96,
          19,
          66,
          108,
          140,
          224,
          11,
          223,
          175,
          136,
          36,
          196,
          226,
          59,
          87,
          238,
          94,
          92,
          7,
          46,
          51,
          115,
          191,
          153,
          51,
          103,
          30,
          12,
          202,
          78,
          236,
          71,
          27,
          45,
          156,
          197,
          57,
          140,
          228,
          125,
          95,
          35,
          94,
          171,
          225,
          143,
          227,
          60,
          166,
          176,
          141,
          49,
          236,
          137,
          125,
          40,
          96,
          51,
          255,
          2,
          168,
          244,
          231,
          232,
          147,
          169,
          250,
          189,
          246,
          126,
          26,
          155,
          216,
          168,
          49,
          210,
          142,
          115,
          52,
          213,
          63,
          38,
          233,
          84,
          236,
          126,
          252,
          126,
          232,
          175,
          226,
          13,
          38,
          195,
          232,
          83,
          213,
          235,
          60,
          142,
          39,
          232,
          45,
          126,
          229,
          255,
          10,
          190,
          6,
          100,
          11,
          223,
          146,
          184,
          136,
          46,
          214,
          43,
          42,
          19,
          53,
          128,
          31,
          120,
          130,
          159,
          233,
          189,
          106,
          161,
          139,
          57,
          28,
          193,
          211,
          48,
          249,
          2,
          59,
          208,
          75,
          224,
          2,
          166,
          13,
          151,
          89,
          28,
          80,
          134,
          59,
          138,
          94,
          7,
          75,
          161,
          55,
          137,
          23,
          216,
          245,
          31,
          128,
          119,
          88,
          198,
          235,
          176,
          93,
          175,
          134,
          56,
          134,
          231,
          216,
          155,
          65,
          109,
          14,
          1,
          56,
          140,
          247,
          202,
          80,
          95,
          42,
          75,
          248,
          187,
          194,
          94,
          232,
          45,
          97,
          247,
          16,
          128,
          51,
          184,
          172,
          28,
          28,
          65,
          24,
          197,
          65,
          101,
          117,
          243,
          248,
          141,
          19,
          161,
          187,
          145,
          184,
          150,
          114,
          27,
          51,
          121,
          91,
          12,
          203,
          87,
          237,
          84,
          91,
          8,
          208,
          227,
          208,
          187,
          137,
          103,
          169,
          216,
          141,
          190,
          128,
          135,
          248,
          160,
          204,
          107,
          26,
          107,
          21,
          147,
          126,
          0,
          224,
          110,
          128,
          46,
          226,
          6,
          238,
          135,
          225,
          61,
          92,
          75,
          204,
          20,
          142,
          41,
          27,
          28,
          144,
          75,
          161,
          126,
          61,
          254,
          29,
          220,
          78,
          11,
          87,
          3,
          54,
          91,
          79,
          232,
          52,
          0,
          182,
          113,
          11,
          15,
          226,
          111,
          41,
          147,
          239,
          224,
          145,
          114,
          238,
          227,
          205,
          170,
          77,
          153,
          168,
          217,
          203,
          184,
          18,
          123,
          36,
          223,
          192,
          157,
          252,
          1,
          59,
          247,
          67,
          135,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          125,
          211,
          73,
          106,
          21,
          97,
          20,
          5,
          224,
          175,
          94,
          85,
          212,
          16,
          53,
          54,
          121,
          96,
          140,
          10,
          118,
          4,
          17,
          17,
          21,
          130,
          130,
          3,
          39,
          110,
          64,
          112,
          226,
          10,
          4,
          39,
          138,
          59,
          112,
          15,
          110,
          192,
          129,
          35,
          65,
          208,
          236,
          33,
          32,
          111,
          164,
          32,
          40,
          34,
          68,
          8,
          9,
          233,
          144,
          104,
          98,
          196,
          68,
          29,
          252,
          231,
          65,
          249,
          162,
          94,
          40,
          254,
          230,
          54,
          255,
          61,
          231,
          220,
          98,
          167,
          117,
          81,
          101,
          63,
          133,
          155,
          232,
          228,
          59,
          134,
          145,
          118,
          112,
          103,
          32,
          249,
          8,
          174,
          97,
          50,
          231,
          17,
          236,
          77,
          220,
          88,
          124,
          99,
          255,
          42,
          80,
          225,
          11,
          54,
          82,
          96,
          23,
          126,
          224,
          107,
          124,
          231,
          80,
          99,
          161,
          157,
          91,
          103,
          51,
          137,
          209,
          86,
          129,
          113,
          236,
          207,
          121,
          95,
          214,
          203,
          232,
          97,
          17,
          103,
          112,
          2,
          203,
          53,
          26,
          156,
          199,
          233,
          4,
          111,
          96,
          19,
          179,
          248,
          140,
          79,
          185,
          95,
          195,
          42,
          174,
          226,
          44,
          182,
          48,
          215,
          39,
          171,
          9,
          65,
          151,
          210,
          226,
          187,
          4,
          183,
          109,
          56,
          254,
          81,
          44,
          225,
          61,
          214,
          106,
          28,
          196,
          47,
          252,
          196,
          71,
          108,
          99,
          222,
          78,
          219,
          194,
          122,
          96,
          191,
          14,
          193,
          219,
          13,
          78,
          133,
          131,
          5,
          69,
          194,
          153,
          191,
          36,
          183,
          187,
          24,
          194,
          109,
          172,
          96,
          79,
          141,
          195,
          152,
          83,
          152,
          94,
          84,
          180,
          127,
          155,
          142,
          6,
          237,
          27,
          78,
          226,
          141,
          162,
          210,
          108,
          219,
          121,
          8,
          211,
          120,
          164,
          104,
          95,
          13,
          36,
          55,
          184,
          136,
          11,
          129,
          13,
          85,
          95,
          198,
          123,
          120,
          22,
          156,
          61,
          133,
          147,
          205,
          96,
          238,
          183,
          62,
          161,
          168,
          113,
          69,
          33,
          114,
          55,
          214,
          235,
          96,
          122,
          136,
          151,
          120,
          140,
          87,
          120,
          128,
          227,
          88,
          86,
          100,
          171,
          112,
          3,
          31,
          210,
          126,
          183,
          15,
          189,
          74,
          7,
          7,
          18,
          180,
          130,
          251,
          184,
          27,
          162,
          110,
          229,
          126,
          26,
          215,
          149,
          185,
          120,
          18,
          126,
          186,
          88,
          234,
          228,
          176,
          154,
          228,
          113,
          220,
          193,
          139,
          188,
          52,
          31,
          110,
          122,
          202,
          108,
          76,
          40,
          179,
          64,
          153,
          5,
          205,
          0,
          81,
          53,
          158,
          227,
          105,
          94,
          62,
          170,
          252,
          15,
          117,
          138,
          12,
          227,
          187,
          255,
          216,
          80,
          159,
          221,
          172,
          83,
          45,
          24,
          20,
          117,
          254,
          176,
          223,
          49,
          211,
          84,
          232,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          133,
          211,
          207,
          139,
          79,
          81,
          24,
          6,
          240,
          207,
          157,
          251,
          157,
          175,
          201,
          152,
          98,
          97,
          52,
          148,
          168,
          47,
          165,
          111,
          72,
          136,
          5,
          11,
          63,
          118,
          146,
          5,
          69,
          182,
          172,
          148,
          178,
          241,
          63,
          88,
          219,
          250,
          3,
          88,
          89,
          90,
          248,
          23,
          204,
          196,
          66,
          68,
          216,
          136,
          137,
          205,
          72,
          136,
          49,
          227,
          107,
          113,
          158,
          91,
          103,
          198,
          224,
          169,
          211,
          189,
          239,
          185,
          239,
          251,
          156,
          247,
          121,
          207,
          115,
          89,
          137,
          6,
          19,
          85,
          124,
          24,
          39,
          170,
          184,
          143,
          177,
          186,
          96,
          69,
          128,
          205,
          56,
          135,
          109,
          137,
          215,
          99,
          42,
          239,
          83,
          249,
          54,
          248,
          23,
          193,
          2,
          190,
          96,
          127,
          226,
          81,
          150,
          236,
          181,
          248,
          80,
          23,
          180,
          121,
          14,
          210,
          222,
          231,
          16,
          12,
          241,
          35,
          123,
          61,
          124,
          199,
          1,
          204,
          225,
          29,
          54,
          97,
          7,
          22,
          58,
          130,
          67,
          216,
          155,
          130,
          183,
          24,
          199,
          18,
          94,
          166,
          96,
          67,
          136,
          158,
          229,
          176,
          227,
          216,
          136,
          87,
          77,
          8,
          198,
          115,
          234,
          48,
          29,
          60,
          192,
          79,
          127,
          226,
          20,
          102,
          240,
          6,
          79,
          240,
          173,
          141,
          140,
          17,
          230,
          67,
          180,
          21,
          79,
          43,
          237,
          53,
          126,
          225,
          117,
          58,
          89,
          66,
          211,
          195,
          189,
          180,
          180,
          152,
          117,
          29,
          203,
          107,
          20,
          83,
          134,
          124,
          12,
          71,
          149,
          11,
          248,
          212,
          195,
          29,
          60,
          84,
          238,
          127,
          34,
          154,
          255,
          134,
          101,
          124,
          196,
          186,
          16,
          124,
          93,
          157,
          112,
          18,
          55,
          146,
          176,
          22,
          134,
          216,
          173,
          24,
          14,
          101,
          178,
          221,
          135,
          155,
          56,
          130,
          71,
          97,
          31,
          139,
          102,
          41,
          104,
          21,
          51,
          13,
          176,
          11,
          143,
          241,
          94,
          18,
          111,
          99,
          22,
          103,
          20,
          247,
          221,
          194,
          101,
          108,
          199,
          78,
          236,
          195,
          121,
          76,
          98,
          26,
          103,
          113,
          65,
          101,
          251,
          25,
          108,
          169,
          100,
          220,
          87,
          188,
          113,
          17,
          215,
          20,
          107,
          95,
          197,
          193,
          228,
          244,
          67,
          212,
          116,
          86,
          158,
          87,
          44,
          58,
          141,
          43,
          145,
          49,
          155,
          182,
          251,
          202,
          96,
          159,
          99,
          79,
          14,
          91,
          84,
          134,
          57,
          234,
          102,
          208,
          161,
          197,
          11,
          220,
          173,
          180,
          119,
          3,
          155,
          83,
          28,
          57,
          233,
          63,
          168,
          127,
          176,
          211,
          184,
          84,
          197,
          205,
          170,
          92,
          191,
          1,
          251,
          74,
          70,
          68,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          125,
          211,
          79,
          111,
          140,
          81,
          20,
          6,
          240,
          223,
          204,
          59,
          211,
          50,
          45,
          161,
          218,
          40,
          147,
          148,
          160,
          81,
          105,
          68,
          19,
          33,
          44,
          176,
          147,
          176,
          99,
          199,
          198,
          66,
          124,
          12,
          223,
          199,
          206,
          198,
          194,
          198,
          66,
          196,
          66,
          68,
          74,
          194,
          170,
          73,
          165,
          138,
          134,
          180,
          197,
          162,
          227,
          79,
          70,
          89,
          188,
          207,
          212,
          219,
          50,
          78,
          114,
          115,
          115,
          238,
          185,
          231,
          185,
          207,
          121,
          206,
          185,
          108,
          182,
          6,
          70,
          209,
          140,
          63,
          133,
          139,
          40,
          226,
          239,
          218,
          114,
          95,
          125,
          139,
          63,
          136,
          179,
          56,
          16,
          127,
          103,
          146,
          234,
          24,
          193,
          41,
          28,
          253,
          23,
          64,
          35,
          251,
          26,
          126,
          96,
          58,
          103,
          29,
          252,
          68,
          23,
          147,
          1,
          235,
          84,
          1,
          10,
          212,
          112,
          3,
          167,
          241,
          12,
          43,
          56,
          142,
          175,
          216,
          158,
          248,
          54,
          76,
          224,
          35,
          94,
          229,
          124,
          18,
          171,
          69,
          88,
          204,
          224,
          54,
          174,
          224,
          13,
          118,
          132,
          205,
          203,
          0,
          125,
          194,
          80,
          98,
          227,
          41,
          115,
          44,
          128,
          96,
          119,
          146,
          239,
          98,
          22,
          231,
          49,
          92,
          97,
          90,
          67,
          43,
          15,
          93,
          143,
          22,
          227,
          104,
          214,
          48,
          16,
          129,
          62,
          224,
          50,
          214,
          241,
          20,
          203,
          254,
          182,
          118,
          180,
          249,
          146,
          53,
          212,
          192,
          173,
          212,
          124,
          12,
          15,
          176,
          208,
          39,
          25,
          150,
          112,
          41,
          241,
          54,
          150,
          234,
          17,
          110,
          12,
          119,
          2,
          244,
          184,
          79,
          50,
          236,
          193,
          92,
          74,
          94,
          80,
          138,
          187,
          49,
          52,
          35,
          56,
          20,
          70,
          3,
          125,
          0,
          246,
          227,
          130,
          63,
          109,
          87,
          164,
          230,
          131,
          184,
          138,
          155,
          120,
          143,
          239,
          74,
          229,
          187,
          149,
          228,
          209,
          48,
          152,
          72,
          185,
          235,
          88,
          233,
          181,
          241,
          26,
          78,
          224,
          30,
          30,
          41,
          7,
          105,
          48,
          20,
          91,
          161,
          60,
          29,
          218,
          189,
          150,
          238,
          197,
          187,
          2,
          191,
          34,
          202,
          67,
          101,
          11,
          219,
          202,
          54,
          173,
          229,
          149,
          147,
          120,
          17,
          128,
          86,
          238,
          44,
          99,
          21,
          159,
          123,
          163,
          252,
          90,
          57,
          36,
          195,
          56,
          163,
          28,
          158,
          251,
          97,
          209,
          193,
          55,
          60,
          199,
          17,
          236,
          83,
          182,
          240,
          45,
          186,
          27,
          98,
          196,
          26,
          202,
          191,
          240,
          164,
          82,
          127,
          51,
          107,
          17,
          243,
          161,
          255,
          95,
          171,
          126,
          217,
          195,
          56,
          23,
          45,
          100,
          223,
          212,
          161,
          223,
          134,
          255,
          81,
          171,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
      Object {
        "blendOp": 0,
        "data": Array [
          56,
          141,
          117,
          211,
          59,
          107,
          212,
          81,
          16,
          5,
          240,
          223,
          62,
          18,
          87,
          227,
          154,
          224,
          35,
          46,
          62,
          130,
          248,
          64,
          45,
          4,
          193,
          248,
          192,
          66,
          65,
          11,
          11,
          177,
          176,
          176,
          23,
          191,
          135,
          168,
          223,
          73,
          172,
          82,
          41,
          136,
          32,
          106,
          97,
          177,
          106,
          34,
          178,
          194,
          90,
          68,
          137,
          154,
          213,
          197,
          7,
          97,
          45,
          238,
          89,
          19,
          86,
          119,
          224,
          15,
          119,
          184,
          51,
          231,
          127,
          206,
          153,
          185,
          252,
          27,
          243,
          152,
          200,
          249,
          24,
          46,
          163,
          145,
          124,
          107,
          190,
          191,
          81,
          29,
          105,
          110,
          225,
          54,
          238,
          38,
          159,
          194,
          46,
          212,
          2,
          114,
          1,
          123,
          199,
          1,
          84,
          177,
          130,
          39,
          184,
          137,
          57,
          172,
          225,
          27,
          126,
          226,
          8,
          102,
          241,
          101,
          99,
          111,
          45,
          201,
          57,
          236,
          78,
          97,
          7,
          23,
          67,
          253,
          53,
          54,
          225,
          3,
          206,
          98,
          17,
          111,
          194,
          180,
          133,
          94,
          13,
          21,
          220,
          192,
          29,
          236,
          72,
          81,
          15,
          47,
          240,
          21,
          143,
          176,
          37,
          190,
          188,
          199,
          33,
          28,
          15,
          155,
          119,
          67,
          6,
          29,
          172,
          226,
          90,
          52,
          62,
          192,
          67,
          180,
          195,
          170,
          23,
          176,
          86,
          0,
          250,
          120,
          62,
          148,
          211,
          8,
          250,
          118,
          28,
          197,
          45,
          76,
          250,
          127,
          204,
          226,
          84,
          244,
          79,
          162,
          86,
          199,
          153,
          152,
          246,
          76,
          25,
          225,
          61,
          252,
          26,
          3,
          48,
          192,
          111,
          92,
          199,
          39,
          52,
          171,
          49,
          239,
          49,
          246,
          224,
          126,
          244,
          85,
          198,
          0,
          124,
          76,
          125,
          91,
          49,
          183,
          59,
          44,
          108,
          224,
          7,
          174,
          68,
          215,
          203,
          232,
          220,
          24,
          21,
          28,
          204,
          121,
          5,
          159,
          49,
          57,
          52,
          113,
          63,
          174,
          42,
          6,
          205,
          160,
          171,
          152,
          183,
          150,
          251,
          42,
          166,
          21,
          159,
          78,
          43,
          11,
          86,
          67,
          191,
          150,
          203,
          75,
          56,
          128,
          87,
          88,
          192,
          137,
          80,
          93,
          85,
          166,
          82,
          199,
          73,
          44,
          99,
          41,
          114,
          231,
          208,
          29,
          50,
          24,
          40,
          99,
          121,
          154,
          203,
          121,
          101,
          15,
          246,
          5,
          224,
          187,
          50,
          194,
          153,
          200,
          91,
          82,
          198,
          186,
          92,
          77,
          115,
          91,
          89,
          146,
          106,
          254,
          212,
          73,
          222,
          199,
          54,
          188,
          141,
          172,
          157,
          202,
          74,
          15,
          146,
          15,
          234,
          35,
          70,
          77,
          197,
          196,
          197,
          232,
          111,
          134,
          254,
          68,
          64,
          154,
          198,
          143,
          152,
          24,
          51,
          109,
          253,
          141,
          28,
          198,
          121,
          235,
          143,
          110,
          243,
          104,
          195,
          31,
          240,
          69,
          87,
          166,
        ],
        "delay": 70,
        "disposeOp": 1,
        "height": 16,
        "width": 16,
        "xOffset": 0,
        "yOffset": 0,
      },
    ],
    "numFrames": 12,
    "numPlays": Infinity,
  },
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 16,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 5255,
  "text": Object {},
  "transparency": Object {},
  "width": 16,
}
`;

exports[`metadata browser black-white.png 1`] = `
Object {
  "animation": null,
  "bits": 1,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 1,
  "pos": 160,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser grayscale-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 8,
  "pos": 134,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser grayscale-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 16,
  "pos": 163,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser grayscale-alpha-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 4,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 16,
  "pos": 122,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser grayscale-alpha-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceGray",
  "colorType": 4,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 2202,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser interlaced-grayscale-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 8,
  "pos": 250,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser interlaced-pallete-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 3,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [
    34,
    68,
    0,
    245,
    255,
    237,
    119,
    255,
    119,
    203,
    255,
    255,
    17,
    10,
    0,
    58,
    119,
    0,
    34,
    34,
    255,
    255,
    17,
    255,
    17,
    0,
    0,
    34,
    34,
    0,
    255,
    172,
    85,
    102,
    255,
    102,
    255,
    102,
    102,
    255,
    1,
    255,
    34,
    18,
    0,
    220,
    255,
    255,
    204,
    255,
    153,
    68,
    68,
    255,
    0,
    85,
    85,
    34,
    0,
    0,
    203,
    203,
    255,
    68,
    68,
    0,
    85,
    255,
    85,
    203,
    203,
    0,
    51,
    26,
    0,
    255,
    236,
    220,
    237,
    255,
    255,
    228,
    255,
    203,
    255,
    220,
    220,
    68,
    255,
    68,
    102,
    102,
    255,
    51,
    0,
    0,
    68,
    34,
    0,
    237,
    237,
    255,
    102,
    102,
    0,
    255,
    164,
    68,
    255,
    255,
    170,
    237,
    237,
    0,
    0,
    203,
    203,
    254,
    255,
    255,
    253,
    255,
    254,
    255,
    255,
    1,
    51,
    255,
    51,
    85,
    42,
    0,
    1,
    1,
    255,
    136,
    136,
    255,
    0,
    170,
    170,
    1,
    1,
    0,
    68,
    0,
    0,
    136,
    136,
    0,
    255,
    228,
    203,
    186,
    91,
    0,
    34,
    255,
    34,
    102,
    50,
    0,
    255,
    255,
    153,
    170,
    170,
    255,
    85,
    0,
    0,
    170,
    170,
    0,
    203,
    99,
    0,
    17,
    255,
    17,
    212,
    255,
    170,
    119,
    58,
    0,
    255,
    68,
    68,
    220,
    107,
    0,
    102,
    0,
    0,
    1,
    255,
    1,
    136,
    66,
    0,
    236,
    255,
    220,
    107,
    220,
    0,
    255,
    220,
    186,
    0,
    51,
    51,
    0,
    237,
    0,
    237,
    115,
    0,
    255,
    255,
    136,
    153,
    74,
    0,
    17,
    255,
    255,
    119,
    0,
    0,
    255,
    131,
    1,
    255,
    186,
    186,
    254,
    123,
    0,
    255,
    254,
    255,
    0,
    203,
    0,
    255,
    153,
    153,
    34,
    255,
    255,
    136,
    0,
    0,
    255,
    255,
    119,
    0,
    136,
    136,
    255,
    220,
    255,
    26,
    51,
    0,
    0,
    0,
    170,
    51,
    255,
    255,
    0,
    153,
    0,
    153,
    0,
    0,
    0,
    0,
    1,
    50,
    102,
    0,
    255,
    186,
    255,
    68,
    255,
    255,
    255,
    170,
    255,
    0,
    119,
    0,
    0,
    254,
    254,
    170,
    0,
    0,
    74,
    153,
    0,
    255,
    255,
    102,
    255,
    34,
    34,
    0,
    0,
    153,
    139,
    255,
    17,
    85,
    255,
    255,
    255,
    1,
    1,
    255,
    136,
    255,
    0,
    85,
    0,
    0,
    17,
    17,
    255,
    255,
    254,
    255,
    253,
    254,
    164,
    255,
    68,
    102,
    255,
    255,
    255,
    102,
    255,
    0,
    51,
    0,
    255,
    255,
    85,
    255,
    119,
    119,
    0,
    0,
    136,
    255,
    68,
    255,
    0,
    17,
    0,
    119,
    255,
    255,
    0,
    102,
    102,
    255,
    255,
    237,
    0,
    1,
    0,
    255,
    245,
    237,
    17,
    17,
    255,
    255,
    255,
    68,
    255,
    34,
    255,
    255,
    237,
    237,
    17,
    17,
    0,
    136,
    255,
    255,
    0,
    0,
    119,
    147,
    255,
    34,
    0,
    220,
    220,
    51,
    51,
    255,
    254,
    0,
    254,
    186,
    186,
    255,
    153,
    255,
    255,
    51,
    51,
    0,
    99,
    203,
    0,
    186,
    186,
    0,
    172,
    255,
    85,
    255,
    255,
    220,
    255,
    255,
    51,
    123,
    254,
    0,
    237,
    0,
    237,
    85,
    85,
    255,
    170,
    255,
    255,
    220,
    220,
    255,
    85,
    85,
    0,
    0,
    0,
    102,
    220,
    220,
    0,
    220,
    0,
    220,
    131,
    255,
    1,
    119,
    119,
    255,
    254,
    254,
    255,
    255,
    255,
    203,
    255,
    85,
    85,
    119,
    119,
    0,
    254,
    254,
    0,
    203,
    0,
    203,
    0,
    0,
    254,
    1,
    2,
    0,
    1,
    0,
    0,
    18,
    34,
    0,
    255,
    255,
    34,
    0,
    68,
    68,
    155,
    255,
    51,
    255,
    212,
    170,
    0,
    0,
    85,
    153,
    153,
    255,
    153,
    153,
    0,
    186,
    0,
    186,
    42,
    85,
    0,
    255,
    203,
    203,
    180,
    255,
    102,
    255,
    155,
    51,
    255,
    255,
    186,
    170,
    0,
    170,
    66,
    136,
    0,
    83,
    170,
    0,
    255,
    170,
    170,
    0,
    0,
    237,
    0,
    186,
    186,
    255,
    255,
    17,
    0,
    254,
    0,
    0,
    0,
    68,
    0,
    153,
    153,
    153,
    0,
    153,
    255,
    204,
    153,
    186,
    0,
    0,
    136,
    0,
    136,
    0,
    220,
    0,
    255,
    147,
    34,
    0,
    0,
    220,
    254,
    255,
    254,
    170,
    83,
    0,
    119,
    0,
    119,
    2,
    1,
    0,
    203,
    0,
    0,
    0,
    0,
    51,
    255,
    237,
    255,
    0,
    186,
    0,
    255,
    51,
    51,
    237,
    255,
    237,
    255,
    196,
    136,
    188,
    255,
    119,
    0,
    170,
    0,
    102,
    0,
    102,
    0,
    34,
    34,
    220,
    0,
    0,
    255,
    203,
    255,
    220,
    255,
    220,
    255,
    139,
    17,
    0,
    0,
    203,
    0,
    1,
    1,
    85,
    0,
    85,
    0,
    136,
    0,
    0,
    0,
    34,
    1,
    255,
    255,
    203,
    255,
    203,
    237,
    0,
    0,
    255,
    136,
    136,
    68,
    0,
    68,
    91,
    186,
    0,
    255,
    188,
    119,
    255,
    153,
    255,
    0,
    102,
    0,
    186,
    255,
    186,
    0,
    119,
    119,
    115,
    237,
    0,
    254,
    0,
    0,
    51,
    0,
    51,
    0,
    0,
    186,
    255,
    119,
    255,
    0,
    68,
    0,
    170,
    255,
    170,
    255,
    254,
    254,
    0,
    0,
    17,
    34,
    0,
    34,
    196,
    255,
    136,
    0,
    237,
    237,
    153,
    255,
    153,
    255,
    85,
    255,
    0,
    34,
    0,
    255,
    180,
    102,
    17,
    0,
    17,
    10,
    17,
    0,
    255,
    17,
    17,
    220,
    255,
    186,
    186,
    255,
    255,
    136,
    255,
    136,
    1,
    0,
    1,
    255,
    51,
    255,
  ],
  "pixelBitlength": 8,
  "pos": 1523,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser interlaced-rgb-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 24,
  "pos": 311,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser interlaced-rgb-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 48,
  "pos": 591,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser interlaced-rgb-alpha-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 357,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser rgb-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 24,
  "pos": 141,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser rgb-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 48,
  "pos": 298,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser rgb-alpha-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 180,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser rgb-alpha-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 64,
  "pos": 3431,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata browser transparent-black-grayscale-4bit.png 1`] = `
Object {
  "animation": null,
  "bits": 4,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 4,
  "pos": 425,
  "text": Object {},
  "transparency": Object {
    "grayscale": 0,
  },
  "width": 32,
}
`;

exports[`metadata browser transparent-white-grayscale-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 16,
  "pos": 1309,
  "text": Object {},
  "transparency": Object {
    "grayscale": 255,
  },
  "width": 32,
}
`;

exports[`metadata browser transparent-white-palette-8bit.png 1`] = `
Object {
  "animation": null,
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 3,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [
    255,
    255,
    255,
    128,
    86,
    86,
    181,
    181,
    184,
    168,
    66,
    66,
    159,
    159,
    159,
    177,
    32,
    32,
    139,
    21,
    21,
    157,
    157,
    157,
    27,
    27,
    89,
    155,
    155,
    155,
    0,
    0,
    132,
    153,
    153,
    153,
    143,
    167,
    143,
    151,
    151,
    151,
    149,
    149,
    149,
    147,
    147,
    147,
    41,
    41,
    86,
    145,
    145,
    145,
    0,
    0,
    155,
    143,
    143,
    143,
    139,
    149,
    139,
    46,
    46,
    167,
    141,
    141,
    141,
    128,
    0,
    0,
    139,
    139,
    139,
    185,
    0,
    0,
    137,
    137,
    137,
    12,
    12,
    213,
    120,
    117,
    117,
    135,
    135,
    135,
    0,
    0,
    178,
    133,
    133,
    133,
    165,
    0,
    0,
    222,
    0,
    0,
    129,
    129,
    129,
    127,
    127,
    127,
    0,
    0,
    158,
    125,
    125,
    125,
    0,
    0,
    201,
    123,
    123,
    123,
    121,
    121,
    121,
    55,
    55,
    86,
    119,
    119,
    119,
    117,
    117,
    117,
    115,
    115,
    115,
    72,
    169,
    72,
    142,
    0,
    0,
    2,
    2,
    100,
    0,
    0,
    98,
    86,
    137,
    86,
    40,
    40,
    124,
    83,
    139,
    83,
    137,
    137,
    143,
    103,
    103,
    103,
    101,
    101,
    101,
    93,
    109,
    93,
    19,
    229,
    19,
    134,
    38,
    38,
    111,
    45,
    45,
    68,
    145,
    68,
    97,
    97,
    97,
    59,
    157,
    59,
    68,
    137,
    68,
    61,
    147,
    61,
    0,
    0,
    164,
    0,
    243,
    0,
    0,
    241,
    0,
    89,
    89,
    89,
    87,
    87,
    87,
    85,
    85,
    85,
    83,
    83,
    83,
    52,
    133,
    52,
    81,
    81,
    81,
    36,
    151,
    36,
    79,
    79,
    79,
    58,
    58,
    65,
    16,
    16,
    186,
    178,
    15,
    15,
    0,
    199,
    0,
    0,
    197,
    0,
    252,
    252,
    252,
    0,
    195,
    0,
    4,
    4,
    151,
    0,
    193,
    0,
    45,
    119,
    45,
    250,
    250,
    250,
    0,
    191,
    0,
    0,
    0,
    104,
    0,
    189,
    0,
    218,
    212,
    212,
    16,
    16,
    123,
    9,
    173,
    9,
    248,
    248,
    248,
    0,
    185,
    0,
    0,
    183,
    0,
    156,
    156,
    161,
    246,
    246,
    246,
    12,
    161,
    12,
    0,
    179,
    0,
    0,
    177,
    0,
    16,
    145,
    16,
    0,
    171,
    0,
    242,
    242,
    242,
    0,
    169,
    0,
    0,
    167,
    0,
    238,
    238,
    238,
    236,
    236,
    236,
    0,
    151,
    0,
    234,
    234,
    234,
    0,
    0,
    107,
    0,
    141,
    0,
    0,
    139,
    0,
    0,
    137,
    0,
    0,
    135,
    0,
    49,
    49,
    49,
    25,
    25,
    42,
    7,
    7,
    64,
    18,
    18,
    174,
    9,
    9,
    238,
    211,
    214,
    211,
    204,
    204,
    204,
    147,
    0,
    0,
    163,
    42,
    42,
    198,
    198,
    198,
    196,
    196,
    196,
    204,
    0,
    0,
    211,
    10,
    10,
    129,
    107,
    107,
    120,
    62,
    62,
    3,
    3,
    109,
    0,
    0,
    159,
    10,
    10,
    86,
    70,
    70,
    72,
    65,
    65,
    77,
    115,
    93,
    93,
    81,
    7,
    7,
    168,
    168,
    168,
    237,
    237,
    239,
    160,
    160,
    160,
    158,
    158,
    158,
    156,
    156,
    156,
    0,
    0,
    185,
    154,
    154,
    154,
    178,
    0,
    0,
    152,
    152,
    152,
    235,
    0,
    0,
    150,
    150,
    150,
    158,
    0,
    0,
    148,
    148,
    148,
    19,
    19,
    28,
    146,
    146,
    146,
    144,
    144,
    144,
    142,
    142,
    142,
    0,
    0,
    145,
    138,
    138,
    138,
    136,
    136,
    136,
    118,
    162,
    118,
    133,
    136,
    133,
    134,
    134,
    134,
    132,
    132,
    132,
    120,
    15,
    15,
    130,
    130,
    130,
    126,
    130,
    126,
    126,
    126,
    126,
    124,
    124,
    124,
    122,
    122,
    122,
    74,
    192,
    74,
    118,
    118,
    118,
    116,
    116,
    116,
    114,
    114,
    114,
    112,
    112,
    112,
    152,
    0,
    0,
    110,
    110,
    110,
    106,
    112,
    106,
    122,
    102,
    102,
    106,
    106,
    106,
    132,
    0,
    0,
    68,
    162,
    68,
    75,
    150,
    75,
    97,
    100,
    97,
    98,
    98,
    98,
    0,
    244,
    0,
    56,
    152,
    56,
    92,
    92,
    92,
    90,
    90,
    90,
    0,
    230,
    0,
    2,
    2,
    93,
    66,
    120,
    66,
    86,
    86,
    86,
    0,
    0,
    240,
    46,
    148,
    46,
    71,
    104,
    71,
    49,
    49,
    96,
    0,
    216,
    0,
    82,
    82,
    82,
    80,
    80,
    80,
    0,
    206,
    0,
    33,
    152,
    33,
    20,
    20,
    109,
    0,
    200,
    0,
    76,
    76,
    76,
    253,
    253,
    253,
    0,
    198,
    0,
    0,
    0,
    157,
    111,
    107,
    107,
    234,
    14,
    14,
    72,
    72,
    72,
    0,
    188,
    0,
    52,
    102,
    52,
    2,
    2,
    245,
    83,
    83,
    96,
    0,
    176,
    0,
    0,
    174,
    0,
    183,
    0,
    0,
    0,
    164,
    0,
    239,
    239,
    239,
    0,
    162,
    0,
    143,
    79,
    79,
    149,
    52,
    52,
    0,
    152,
    0,
    0,
    150,
    0,
    0,
    146,
    0,
    231,
    231,
    231,
    0,
    140,
    0,
    227,
    227,
    227,
    0,
    128,
    0,
    146,
    6,
    6,
    1,
    1,
    111,
    100,
    86,
    89,
    0,
    0,
    100,
    78,
    78,
    107,
    207,
    207,
    207,
    221,
    221,
    224,
    0,
    0,
    123,
    201,
    201,
    201,
    22,
    22,
    65,
    33,
    33,
    89,
    87,
    87,
    89,
    68,
    68,
    120,
    191,
    191,
    191,
    235,
    221,
    221,
    45,
    45,
    84,
    10,
    10,
    96,
    0,
    0,
    255,
    191,
    125,
    125,
  ],
  "pixelBitlength": 8,
  "pos": 1492,
  "text": Object {},
  "transparency": Object {
    "indexed": Array [
      0,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
    ],
  },
  "width": 32,
}
`;

exports[`metadata browser transparent-white-rgb-16bit.png 1`] = `
Object {
  "animation": null,
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 48,
  "pos": 2037,
  "text": Object {},
  "transparency": Object {
    "rgb": Array [
      255,
      255,
      255,
      255,
      255,
      255,
    ],
  },
  "width": 32,
}
`;

exports[`metadata node animated.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 16,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 5255,
  "text": Object {},
  "transparency": Object {},
  "width": 16,
}
`;

exports[`metadata node black-white.png 1`] = `
Object {
  "bits": 1,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 1,
  "pos": 160,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node grayscale-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 8,
  "pos": 134,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node grayscale-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 16,
  "pos": 163,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node grayscale-alpha-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 4,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 16,
  "pos": 122,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node grayscale-alpha-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceGray",
  "colorType": 4,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 2202,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node interlaced-grayscale-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 8,
  "pos": 250,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node interlaced-pallete-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 3,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [
    34,
    68,
    0,
    245,
    255,
    237,
    119,
    255,
    119,
    203,
    255,
    255,
    17,
    10,
    0,
    58,
    119,
    0,
    34,
    34,
    255,
    255,
    17,
    255,
    17,
    0,
    0,
    34,
    34,
    0,
    255,
    172,
    85,
    102,
    255,
    102,
    255,
    102,
    102,
    255,
    1,
    255,
    34,
    18,
    0,
    220,
    255,
    255,
    204,
    255,
    153,
    68,
    68,
    255,
    0,
    85,
    85,
    34,
    0,
    0,
    203,
    203,
    255,
    68,
    68,
    0,
    85,
    255,
    85,
    203,
    203,
    0,
    51,
    26,
    0,
    255,
    236,
    220,
    237,
    255,
    255,
    228,
    255,
    203,
    255,
    220,
    220,
    68,
    255,
    68,
    102,
    102,
    255,
    51,
    0,
    0,
    68,
    34,
    0,
    237,
    237,
    255,
    102,
    102,
    0,
    255,
    164,
    68,
    255,
    255,
    170,
    237,
    237,
    0,
    0,
    203,
    203,
    254,
    255,
    255,
    253,
    255,
    254,
    255,
    255,
    1,
    51,
    255,
    51,
    85,
    42,
    0,
    1,
    1,
    255,
    136,
    136,
    255,
    0,
    170,
    170,
    1,
    1,
    0,
    68,
    0,
    0,
    136,
    136,
    0,
    255,
    228,
    203,
    186,
    91,
    0,
    34,
    255,
    34,
    102,
    50,
    0,
    255,
    255,
    153,
    170,
    170,
    255,
    85,
    0,
    0,
    170,
    170,
    0,
    203,
    99,
    0,
    17,
    255,
    17,
    212,
    255,
    170,
    119,
    58,
    0,
    255,
    68,
    68,
    220,
    107,
    0,
    102,
    0,
    0,
    1,
    255,
    1,
    136,
    66,
    0,
    236,
    255,
    220,
    107,
    220,
    0,
    255,
    220,
    186,
    0,
    51,
    51,
    0,
    237,
    0,
    237,
    115,
    0,
    255,
    255,
    136,
    153,
    74,
    0,
    17,
    255,
    255,
    119,
    0,
    0,
    255,
    131,
    1,
    255,
    186,
    186,
    254,
    123,
    0,
    255,
    254,
    255,
    0,
    203,
    0,
    255,
    153,
    153,
    34,
    255,
    255,
    136,
    0,
    0,
    255,
    255,
    119,
    0,
    136,
    136,
    255,
    220,
    255,
    26,
    51,
    0,
    0,
    0,
    170,
    51,
    255,
    255,
    0,
    153,
    0,
    153,
    0,
    0,
    0,
    0,
    1,
    50,
    102,
    0,
    255,
    186,
    255,
    68,
    255,
    255,
    255,
    170,
    255,
    0,
    119,
    0,
    0,
    254,
    254,
    170,
    0,
    0,
    74,
    153,
    0,
    255,
    255,
    102,
    255,
    34,
    34,
    0,
    0,
    153,
    139,
    255,
    17,
    85,
    255,
    255,
    255,
    1,
    1,
    255,
    136,
    255,
    0,
    85,
    0,
    0,
    17,
    17,
    255,
    255,
    254,
    255,
    253,
    254,
    164,
    255,
    68,
    102,
    255,
    255,
    255,
    102,
    255,
    0,
    51,
    0,
    255,
    255,
    85,
    255,
    119,
    119,
    0,
    0,
    136,
    255,
    68,
    255,
    0,
    17,
    0,
    119,
    255,
    255,
    0,
    102,
    102,
    255,
    255,
    237,
    0,
    1,
    0,
    255,
    245,
    237,
    17,
    17,
    255,
    255,
    255,
    68,
    255,
    34,
    255,
    255,
    237,
    237,
    17,
    17,
    0,
    136,
    255,
    255,
    0,
    0,
    119,
    147,
    255,
    34,
    0,
    220,
    220,
    51,
    51,
    255,
    254,
    0,
    254,
    186,
    186,
    255,
    153,
    255,
    255,
    51,
    51,
    0,
    99,
    203,
    0,
    186,
    186,
    0,
    172,
    255,
    85,
    255,
    255,
    220,
    255,
    255,
    51,
    123,
    254,
    0,
    237,
    0,
    237,
    85,
    85,
    255,
    170,
    255,
    255,
    220,
    220,
    255,
    85,
    85,
    0,
    0,
    0,
    102,
    220,
    220,
    0,
    220,
    0,
    220,
    131,
    255,
    1,
    119,
    119,
    255,
    254,
    254,
    255,
    255,
    255,
    203,
    255,
    85,
    85,
    119,
    119,
    0,
    254,
    254,
    0,
    203,
    0,
    203,
    0,
    0,
    254,
    1,
    2,
    0,
    1,
    0,
    0,
    18,
    34,
    0,
    255,
    255,
    34,
    0,
    68,
    68,
    155,
    255,
    51,
    255,
    212,
    170,
    0,
    0,
    85,
    153,
    153,
    255,
    153,
    153,
    0,
    186,
    0,
    186,
    42,
    85,
    0,
    255,
    203,
    203,
    180,
    255,
    102,
    255,
    155,
    51,
    255,
    255,
    186,
    170,
    0,
    170,
    66,
    136,
    0,
    83,
    170,
    0,
    255,
    170,
    170,
    0,
    0,
    237,
    0,
    186,
    186,
    255,
    255,
    17,
    0,
    254,
    0,
    0,
    0,
    68,
    0,
    153,
    153,
    153,
    0,
    153,
    255,
    204,
    153,
    186,
    0,
    0,
    136,
    0,
    136,
    0,
    220,
    0,
    255,
    147,
    34,
    0,
    0,
    220,
    254,
    255,
    254,
    170,
    83,
    0,
    119,
    0,
    119,
    2,
    1,
    0,
    203,
    0,
    0,
    0,
    0,
    51,
    255,
    237,
    255,
    0,
    186,
    0,
    255,
    51,
    51,
    237,
    255,
    237,
    255,
    196,
    136,
    188,
    255,
    119,
    0,
    170,
    0,
    102,
    0,
    102,
    0,
    34,
    34,
    220,
    0,
    0,
    255,
    203,
    255,
    220,
    255,
    220,
    255,
    139,
    17,
    0,
    0,
    203,
    0,
    1,
    1,
    85,
    0,
    85,
    0,
    136,
    0,
    0,
    0,
    34,
    1,
    255,
    255,
    203,
    255,
    203,
    237,
    0,
    0,
    255,
    136,
    136,
    68,
    0,
    68,
    91,
    186,
    0,
    255,
    188,
    119,
    255,
    153,
    255,
    0,
    102,
    0,
    186,
    255,
    186,
    0,
    119,
    119,
    115,
    237,
    0,
    254,
    0,
    0,
    51,
    0,
    51,
    0,
    0,
    186,
    255,
    119,
    255,
    0,
    68,
    0,
    170,
    255,
    170,
    255,
    254,
    254,
    0,
    0,
    17,
    34,
    0,
    34,
    196,
    255,
    136,
    0,
    237,
    237,
    153,
    255,
    153,
    255,
    85,
    255,
    0,
    34,
    0,
    255,
    180,
    102,
    17,
    0,
    17,
    10,
    17,
    0,
    255,
    17,
    17,
    220,
    255,
    186,
    186,
    255,
    255,
    136,
    255,
    136,
    1,
    0,
    1,
    255,
    51,
    255,
  ],
  "pixelBitlength": 8,
  "pos": 1523,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node interlaced-rgb-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 24,
  "pos": 311,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node interlaced-rgb-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 48,
  "pos": 591,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node interlaced-rgb-alpha-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 1,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 357,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node rgb-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 24,
  "pos": 141,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node rgb-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 48,
  "pos": 298,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node rgb-alpha-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 32,
  "pos": 180,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node rgb-alpha-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 6,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": true,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 64,
  "pos": 3431,
  "text": Object {},
  "transparency": Object {},
  "width": 32,
}
`;

exports[`metadata node transparent-black-grayscale-4bit.png 1`] = `
Object {
  "bits": 4,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 4,
  "pos": 425,
  "text": Object {},
  "transparency": Object {
    "grayscale": 0,
  },
  "width": 32,
}
`;

exports[`metadata node transparent-white-grayscale-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceGray",
  "colorType": 0,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 16,
  "pos": 1309,
  "text": Object {},
  "transparency": Object {
    "grayscale": 255,
  },
  "width": 32,
}
`;

exports[`metadata node transparent-white-palette-8bit.png 1`] = `
Object {
  "bits": 8,
  "colorSpace": "DeviceGray",
  "colorType": 3,
  "colors": 1,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [
    255,
    255,
    255,
    128,
    86,
    86,
    181,
    181,
    184,
    168,
    66,
    66,
    159,
    159,
    159,
    177,
    32,
    32,
    139,
    21,
    21,
    157,
    157,
    157,
    27,
    27,
    89,
    155,
    155,
    155,
    0,
    0,
    132,
    153,
    153,
    153,
    143,
    167,
    143,
    151,
    151,
    151,
    149,
    149,
    149,
    147,
    147,
    147,
    41,
    41,
    86,
    145,
    145,
    145,
    0,
    0,
    155,
    143,
    143,
    143,
    139,
    149,
    139,
    46,
    46,
    167,
    141,
    141,
    141,
    128,
    0,
    0,
    139,
    139,
    139,
    185,
    0,
    0,
    137,
    137,
    137,
    12,
    12,
    213,
    120,
    117,
    117,
    135,
    135,
    135,
    0,
    0,
    178,
    133,
    133,
    133,
    165,
    0,
    0,
    222,
    0,
    0,
    129,
    129,
    129,
    127,
    127,
    127,
    0,
    0,
    158,
    125,
    125,
    125,
    0,
    0,
    201,
    123,
    123,
    123,
    121,
    121,
    121,
    55,
    55,
    86,
    119,
    119,
    119,
    117,
    117,
    117,
    115,
    115,
    115,
    72,
    169,
    72,
    142,
    0,
    0,
    2,
    2,
    100,
    0,
    0,
    98,
    86,
    137,
    86,
    40,
    40,
    124,
    83,
    139,
    83,
    137,
    137,
    143,
    103,
    103,
    103,
    101,
    101,
    101,
    93,
    109,
    93,
    19,
    229,
    19,
    134,
    38,
    38,
    111,
    45,
    45,
    68,
    145,
    68,
    97,
    97,
    97,
    59,
    157,
    59,
    68,
    137,
    68,
    61,
    147,
    61,
    0,
    0,
    164,
    0,
    243,
    0,
    0,
    241,
    0,
    89,
    89,
    89,
    87,
    87,
    87,
    85,
    85,
    85,
    83,
    83,
    83,
    52,
    133,
    52,
    81,
    81,
    81,
    36,
    151,
    36,
    79,
    79,
    79,
    58,
    58,
    65,
    16,
    16,
    186,
    178,
    15,
    15,
    0,
    199,
    0,
    0,
    197,
    0,
    252,
    252,
    252,
    0,
    195,
    0,
    4,
    4,
    151,
    0,
    193,
    0,
    45,
    119,
    45,
    250,
    250,
    250,
    0,
    191,
    0,
    0,
    0,
    104,
    0,
    189,
    0,
    218,
    212,
    212,
    16,
    16,
    123,
    9,
    173,
    9,
    248,
    248,
    248,
    0,
    185,
    0,
    0,
    183,
    0,
    156,
    156,
    161,
    246,
    246,
    246,
    12,
    161,
    12,
    0,
    179,
    0,
    0,
    177,
    0,
    16,
    145,
    16,
    0,
    171,
    0,
    242,
    242,
    242,
    0,
    169,
    0,
    0,
    167,
    0,
    238,
    238,
    238,
    236,
    236,
    236,
    0,
    151,
    0,
    234,
    234,
    234,
    0,
    0,
    107,
    0,
    141,
    0,
    0,
    139,
    0,
    0,
    137,
    0,
    0,
    135,
    0,
    49,
    49,
    49,
    25,
    25,
    42,
    7,
    7,
    64,
    18,
    18,
    174,
    9,
    9,
    238,
    211,
    214,
    211,
    204,
    204,
    204,
    147,
    0,
    0,
    163,
    42,
    42,
    198,
    198,
    198,
    196,
    196,
    196,
    204,
    0,
    0,
    211,
    10,
    10,
    129,
    107,
    107,
    120,
    62,
    62,
    3,
    3,
    109,
    0,
    0,
    159,
    10,
    10,
    86,
    70,
    70,
    72,
    65,
    65,
    77,
    115,
    93,
    93,
    81,
    7,
    7,
    168,
    168,
    168,
    237,
    237,
    239,
    160,
    160,
    160,
    158,
    158,
    158,
    156,
    156,
    156,
    0,
    0,
    185,
    154,
    154,
    154,
    178,
    0,
    0,
    152,
    152,
    152,
    235,
    0,
    0,
    150,
    150,
    150,
    158,
    0,
    0,
    148,
    148,
    148,
    19,
    19,
    28,
    146,
    146,
    146,
    144,
    144,
    144,
    142,
    142,
    142,
    0,
    0,
    145,
    138,
    138,
    138,
    136,
    136,
    136,
    118,
    162,
    118,
    133,
    136,
    133,
    134,
    134,
    134,
    132,
    132,
    132,
    120,
    15,
    15,
    130,
    130,
    130,
    126,
    130,
    126,
    126,
    126,
    126,
    124,
    124,
    124,
    122,
    122,
    122,
    74,
    192,
    74,
    118,
    118,
    118,
    116,
    116,
    116,
    114,
    114,
    114,
    112,
    112,
    112,
    152,
    0,
    0,
    110,
    110,
    110,
    106,
    112,
    106,
    122,
    102,
    102,
    106,
    106,
    106,
    132,
    0,
    0,
    68,
    162,
    68,
    75,
    150,
    75,
    97,
    100,
    97,
    98,
    98,
    98,
    0,
    244,
    0,
    56,
    152,
    56,
    92,
    92,
    92,
    90,
    90,
    90,
    0,
    230,
    0,
    2,
    2,
    93,
    66,
    120,
    66,
    86,
    86,
    86,
    0,
    0,
    240,
    46,
    148,
    46,
    71,
    104,
    71,
    49,
    49,
    96,
    0,
    216,
    0,
    82,
    82,
    82,
    80,
    80,
    80,
    0,
    206,
    0,
    33,
    152,
    33,
    20,
    20,
    109,
    0,
    200,
    0,
    76,
    76,
    76,
    253,
    253,
    253,
    0,
    198,
    0,
    0,
    0,
    157,
    111,
    107,
    107,
    234,
    14,
    14,
    72,
    72,
    72,
    0,
    188,
    0,
    52,
    102,
    52,
    2,
    2,
    245,
    83,
    83,
    96,
    0,
    176,
    0,
    0,
    174,
    0,
    183,
    0,
    0,
    0,
    164,
    0,
    239,
    239,
    239,
    0,
    162,
    0,
    143,
    79,
    79,
    149,
    52,
    52,
    0,
    152,
    0,
    0,
    150,
    0,
    0,
    146,
    0,
    231,
    231,
    231,
    0,
    140,
    0,
    227,
    227,
    227,
    0,
    128,
    0,
    146,
    6,
    6,
    1,
    1,
    111,
    100,
    86,
    89,
    0,
    0,
    100,
    78,
    78,
    107,
    207,
    207,
    207,
    221,
    221,
    224,
    0,
    0,
    123,
    201,
    201,
    201,
    22,
    22,
    65,
    33,
    33,
    89,
    87,
    87,
    89,
    68,
    68,
    120,
    191,
    191,
    191,
    235,
    221,
    221,
    45,
    45,
    84,
    10,
    10,
    96,
    0,
    0,
    255,
    191,
    125,
    125,
  ],
  "pixelBitlength": 8,
  "pos": 1492,
  "text": Object {},
  "transparency": Object {
    "indexed": Array [
      0,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
      255,
    ],
  },
  "width": 32,
}
`;

exports[`metadata node transparent-white-rgb-16bit.png 1`] = `
Object {
  "bits": 16,
  "colorSpace": "DeviceRGB",
  "colorType": 2,
  "colors": 3,
  "compressionMethod": 0,
  "filterMethod": 0,
  "hasAlphaChannel": false,
  "height": 32,
  "interlaceMethod": 0,
  "palette": Array [],
  "pixelBitlength": 48,
  "pos": 2037,
  "text": Object {},
  "transparency": Object {
    "rgb": Array [
      255,
      255,
      255,
      255,
      255,
      255,
    ],
  },
  "width": 32,
}
`;
