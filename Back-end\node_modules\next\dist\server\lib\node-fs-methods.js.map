{"version": 3, "sources": ["../../../src/server/lib/node-fs-methods.ts"], "sourcesContent": ["import type { CacheFs } from '../../shared/lib/utils'\n\nimport fs from 'fs'\n\nexport const nodeFs: CacheFs = {\n  existsSync: fs.existsSync,\n  readFile: fs.promises.readFile,\n  readFileSync: fs.readFileSync,\n  writeFile: (f, d) => fs.promises.writeFile(f, d),\n  mkdir: (dir) => fs.promises.mkdir(dir, { recursive: true }),\n  stat: (f) => fs.promises.stat(f),\n}\n"], "names": ["nodeFs", "existsSync", "fs", "readFile", "promises", "readFileSync", "writeFile", "f", "d", "mkdir", "dir", "recursive", "stat"], "mappings": ";;;;+BAIaA;;;eAAAA;;;2DAFE;;;;;;AAER,MAAMA,SAAkB;IAC7BC,YAAYC,WAAE,CAACD,UAAU;IACzBE,UAAUD,WAAE,CAACE,QAAQ,CAACD,QAAQ;IAC9BE,cAAcH,WAAE,CAACG,YAAY;IAC7BC,WAAW,CAACC,GAAGC,IAAMN,WAAE,CAACE,QAAQ,CAACE,SAAS,CAACC,GAAGC;IAC9CC,OAAO,CAACC,MAAQR,WAAE,CAACE,QAAQ,CAACK,KAAK,CAACC,KAAK;YAAEC,WAAW;QAAK;IACzDC,MAAM,CAACL,IAAML,WAAE,CAACE,QAAQ,CAACQ,IAAI,CAACL;AAChC"}