\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{colortbl}
\usepackage{array}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{fancyhdr}
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{hyperref}
\usepackage{tikz}
\usepackage{tcolorbox}
\usepackage{enumitem}

% Page setup
\geometry{margin=2cm}
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\textbf{Product Backlog - Système de Gestion}}
\fancyhead[R]{\thepage}
\fancyfoot[C]{\textit{Confidentiel - Usage Interne}}

% Color definitions
\definecolor{headerblue}{RGB}{52, 73, 94}
\definecolor{lightblue}{RGB}{174, 214, 241}
\definecolor{darkblue}{RGB}{21, 67, 96}
\definecolor{elevee}{RGB}{231, 76, 60}
\definecolor{moyenne}{RGB}{241, 196, 15}
\definecolor{faible}{RGB}{46, 204, 113}
\definecolor{sprintcolor1}{RGB}{155, 89, 182}
\definecolor{sprintcolor2}{RGB}{52, 152, 219}
\definecolor{sprintcolor3}{RGB}{26, 188, 156}

% Custom commands
\newcommand{\sprinttitle}[3]{
    \begin{tcolorbox}[colback=#1!20, colframe=#1, title=\textbf{\Large #2}, fonttitle=\bfseries\color{white}]
        \textit{#3}
    \end{tcolorbox}
}

\newcommand{\sectiontitle}[1]{
    \subsection*{\textcolor{headerblue}{\textbf{#1}}}
}

% Table column types
\newcolumntype{C}[1]{>{\centering\arraybackslash}p{#1}}
\newcolumntype{L}[1]{>{\raggedright\arraybackslash}p{#1}}

\begin{document}

% Title page
\begin{titlepage}
    \centering
    \vspace*{2cm}

    {\Huge\textbf{\textcolor{headerblue}{PRODUCT BACKLOG}}}\\[0.5cm]
    {\Large\textbf{Système de Gestion de Factures et Devis}}\\[2cm]

    \begin{tcolorbox}[colback=lightblue, colframe=darkblue, width=0.8\textwidth]
        \centering
        \textbf{Projet Professionnel}\\[0.3cm]
        \textbf{Durée:} 90 jours (3 sprints × 30 jours)\\
        \textbf{Architecture:} React.js/Laravel + Node.js/Express.js + MongoDB\\
        \textbf{Rôles:} Admin, Responsable d'Entreprise, Vendeur, Client
    \end{tcolorbox}

    \vfill

    {\large\textbf{Version:} 1.0}\\
    {\large\textbf{Date:} \today}\\[1cm]

    \textit{Document confidentiel - Usage interne uniquement}
\end{titlepage}

\newpage
\tableofcontents
\newpage

% Sprint 1
\sprinttitle{sprintcolor1}{SPRINT 1 - FONDATION \& FONCTIONNALITÉS CORE (Jours 1-30)}{Priorité: Fonctionnalités métier critiques et stabilité du système}

\sectiontitle{1.1 Amélioration de l'Authentification \& Sécurité}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

1.1 & Authentication & En tant qu'Admin, je souhaite implémenter l'authentification à deux facteurs (2FA) pour tous les comptes utilisateurs afin d'améliorer la sécurité du système & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
1.2 & Authentication & En tant qu'Utilisateur, je souhaite réinitialiser mon mot de passe via SMS OTP en plus de l'email pour avoir plusieurs options de récupération & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 5 \\
\hline
1.3 & Authentication & En tant qu'Admin, je souhaite configurer les politiques de mots de passe (complexité, expiration) pour appliquer les standards de sécurité & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 5 \\
\hline
\end{longtable}

\sectiontitle{1.2 Amélioration de la Gestion des Abonnements}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

1.4 & Subscription & En tant qu'Admin, je souhaite créer des plans d'abonnement personnalisés avec des limitations de fonctionnalités spécifiques pour offrir une tarification flexible & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
1.5 & Subscription & En tant que Responsable, je souhaite recevoir des rappels de renouvellement automatisés 30, 15 et 7 jours avant l'expiration & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 6 \\
\hline
1.6 & Subscription & En tant qu'Admin, je souhaite implémenter une fonctionnalité de période de grâce (3-7 jours) après l'expiration de l'abonnement & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 5 \\
\hline
1.7 & Subscription & En tant que Responsable, je souhaite mettre à niveau/rétrograder mon plan d'abonnement en temps réel sans perdre de données & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 7 \\
\hline
\end{longtable}

\sectiontitle{1.3 Système de Templates de Documents}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

1.8 & Templates & En tant qu'Admin, je souhaite créer plusieurs templates de base (Standard, Moderne, Professionnel) pour les factures et devis & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
1.9 & Templates & En tant que Responsable, je souhaite personnaliser les templates avec les couleurs, polices et préférences de mise en page de mon entreprise & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
1.10 & Templates & En tant que Responsable, je souhaite prévisualiser les modifications de template en temps réel avant de les appliquer & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 5 \\
\hline
\end{longtable}

\sectiontitle{1.4 Fondation de la Gestion de Stock}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

1.11 & Stock & En tant que Vendeur, je souhaite suivre les niveaux de stock des produits et recevoir des alertes de stock faible & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
1.12 & Stock & En tant que Responsable, je souhaite définir des seuils de stock minimum pour chaque produit afin d'éviter les ruptures de stock & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 6 \\
\hline
1.13 & Stock & En tant que Vendeur, je souhaite un ajustement automatique du stock lors de la création de factures pour maintenir la précision & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
\end{longtable}

% Sprint 2
\sprinttitle{sprintcolor2}{SPRINT 2 - FONCTIONNALITÉS AVANCÉES \& OPTIMISATION (Jours 31-60)}{Priorité: Fonctionnalités améliorées et expérience utilisateur}

\sectiontitle{2.1 Gestion Avancée des Documents}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

2.1 & Documents & En tant que Vendeur, je souhaite créer des factures récurrentes avec des intervalles personnalisables (hebdomadaire, mensuel, annuel) & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
2.2 & Documents & En tant que Client, je souhaite signer numériquement les devis et contrats directement dans le système & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
2.3 & Documents & En tant que Vendeur, je souhaite ajouter plusieurs pièces jointes (images, PDFs) aux factures et devis & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 6 \\
\hline
2.4 & Documents & En tant que Responsable, je souhaite définir des workflows d'approbation pour les devis au-dessus de certains montants & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
\end{longtable}

\sectiontitle{2.2 Amélioration du Portail Client}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

2.5 & Client Portal & En tant que Client, je souhaite consulter mon historique de paiements et télécharger les reçus depuis un portail dédié & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
2.6 & Client Portal & En tant que Client, je souhaite demander des devis directement via le portail avec les spécifications des produits & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 6 \\
\hline
2.7 & Client Portal & En tant que Client, je souhaite recevoir des notifications en temps réel pour les nouvelles factures et confirmations de paiement & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 5 \\
\hline
2.8 & Client Portal & En tant que Client, je souhaite configurer des méthodes de paiement automatiques pour les factures récurrentes & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 8 \\
\hline
\end{longtable}

\sectiontitle{2.3 Rapports \& Analyses Avancés}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

2.9 & Reporting & En tant que Responsable, je souhaite générer des rapports de ventes détaillés par période, produit et vendeur & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
2.10 & Reporting & En tant qu'Admin, je souhaite consulter les analyses système incluant l'activité utilisateur et les métriques d'abonnement & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 6 \\
\hline
2.11 & Reporting & En tant que Vendeur, je souhaite suivre mes métriques de performance (ventes, taux de conversion, satisfaction client) & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 6 \\
\hline
\end{longtable}

\sectiontitle{2.4 Responsivité Mobile}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

2.12 & Mobile & En tant que Vendeur, je souhaite créer et envoyer des factures depuis mon appareil mobile lors de visites clients & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
2.13 & Mobile & En tant que Client, je souhaite consulter et payer les factures depuis mon appareil mobile avec une interface optimisée & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
2.14 & Mobile & En tant que Responsable, je souhaite approuver les devis et surveiller les métriques métier depuis mobile & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 6 \\
\hline
\end{longtable}

% Sprint 3
\sprinttitle{sprintcolor3}{SPRINT 3 - INTÉGRATION \& CAPACITÉS AVANCÉES (Jours 61-90)}{Priorité: Intégration système et fonctionnalités métier avancées}

\sectiontitle{3.1 Intégration Paiement \& Gestion Financière}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

3.1 & Payment & En tant que Client, je souhaite payer les factures en ligne via plusieurs passerelles de paiement (Stripe, PayPal, banques locales) & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 10 \\
\hline
3.2 & Payment & En tant que Responsable, je souhaite suivre les statuts de paiement et mettre à jour automatiquement les statuts de factures & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
3.3 & Payment & En tant que Vendeur, je souhaite envoyer des rappels de paiement automatiques pour les factures en retard & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 6 \\
\hline
3.4 & Financial & En tant que Responsable, je souhaite générer des rapports fiscaux et exporter les données vers les logiciels comptables & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
\end{longtable}

\sectiontitle{3.2 Gestion Avancée des Livraisons}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

3.5 & Delivery & En tant que Responsable, je souhaite assigner des itinéraires de livraison aux livreurs avec suivi GPS & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 8 \\
\hline
3.6 & Delivery & En tant que Client, je souhaite suivre ma livraison en temps réel et recevoir les heures d'arrivée estimées & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 7 \\
\hline
3.7 & Delivery & En tant que Livreur, je souhaite mettre à jour le statut de livraison et capturer les confirmations avec photos & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 6 \\
\hline
\end{longtable}

\sectiontitle{3.3 Intégration Système \& API}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

3.8 & Integration & En tant que Responsable, je souhaite intégrer avec les logiciels comptables externes (Sage, QuickBooks) & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 10 \\
\hline
3.9 & Integration & En tant que Développeur, je souhaite fournir un accès API REST pour les intégrations tierces & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 8 \\
\hline
3.10 & Integration & En tant que Responsable, je souhaite synchroniser les données clients avec les systèmes CRM & \cellcolor{faible}\textcolor{black}{\textbf{Faible}} & 7 \\
\hline
\end{longtable}

\sectiontitle{3.4 Sécurité Avancée \& Audit}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

3.11 & Security & En tant qu'Admin, je souhaite implémenter des permissions basées sur les rôles avec contrôle d'accès granulaire & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 8 \\
\hline
3.12 & Audit & En tant qu'Admin, je souhaite maintenir des journaux d'audit pour toutes les activités système et modifications de documents & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 6 \\
\hline
3.13 & Security & En tant que Responsable, je souhaite sauvegarder et restaurer les données de mon entreprise avec chiffrement & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 7 \\
\hline
\end{longtable}

\sectiontitle{3.5 Performance \& Évolutivité}

\begin{longtable}{|C{0.8cm}|L{2.5cm}|L{7cm}|C{1.8cm}|C{1.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} & \textcolor{white}{\textbf{Thème}} & \textcolor{white}{\textbf{User Story}} & \textcolor{white}{\textbf{Importance}} & \textcolor{white}{\textbf{Points}} \\
\hline
\endhead

3.14 & Performance & En tant qu'Utilisateur, je souhaite que le système charge les pages en moins de 2 secondes pour une expérience utilisateur optimale & \cellcolor{elevee}\textcolor{white}{\textbf{Élevée}} & 6 \\
\hline
3.15 & Performance & En tant qu'Admin, je souhaite surveiller les performances système et recevoir des alertes en cas de problèmes & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 5 \\
\hline
3.16 & Scalability & En tant qu'Admin, je souhaite que le système gère 1000+ utilisateurs simultanés sans dégradation des performances & \cellcolor{moyenne}\textcolor{black}{\textbf{Moyenne}} & 8 \\
\hline
\end{longtable}

% Summary Section
\section*{\textcolor{headerblue}{\textbf{RÉSUMÉ PAR SPRINT}}}

\begin{tcolorbox}[colback=sprintcolor1!10, colframe=sprintcolor1, title=\textbf{Sprint 1 (30 jours): Fondation - 85 Story Points}]
\begin{itemize}[leftmargin=*]
    \item \textbf{Authentification \& Sécurité:} 18 points
    \item \textbf{Gestion des Abonnements:} 26 points
    \item \textbf{Templates de Documents:} 20 points
    \item \textbf{Gestion de Stock:} 21 points
\end{itemize}
\end{tcolorbox}

\vspace{0.5cm}

\begin{tcolorbox}[colback=sprintcolor2!10, colframe=sprintcolor2, title=\textbf{Sprint 2 (30 jours): Amélioration - 89 Story Points}]
\begin{itemize}[leftmargin=*]
    \item \textbf{Documents Avancés:} 29 points
    \item \textbf{Portail Client:} 26 points
    \item \textbf{Rapports \& Analyses:} 19 points
    \item \textbf{Responsivité Mobile:} 21 points
\end{itemize}
\end{tcolorbox}

\vspace{0.5cm}

\begin{tcolorbox}[colback=sprintcolor3!10, colframe=sprintcolor3, title=\textbf{Sprint 3 (30 jours): Intégration - 92 Story Points}]
\begin{itemize}[leftmargin=*]
    \item \textbf{Intégration Paiement:} 31 points
    \item \textbf{Gestion des Livraisons:} 21 points
    \item \textbf{Intégration Système:} 25 points
    \item \textbf{Sécurité \& Performance:} 26 points
\end{itemize}
\end{tcolorbox}

\vspace{1cm}

\begin{tcolorbox}[colback=lightblue, colframe=darkblue, title=\textbf{\Large TOTAL PROJET}]
\centering
\textbf{\Huge 266 Story Points}\\[0.3cm]
\textbf{\Large sur 90 jours (3 sprints)}\\[0.5cm]
\textit{Moyenne de 88.7 points par sprint}
\end{tcolorbox}

\newpage

% Definition of Done
\section*{\textcolor{headerblue}{\textbf{DÉFINITION DE "TERMINÉ"}}}

\begin{tcolorbox}[colback=faible!20, colframe=faible, title=\textbf{Critères d'Acceptation}]
\begin{enumerate}[leftmargin=*]
    \item \textbf{Fonctionnalité complètement implémentée et testée}
    \begin{itemize}
        \item Code développé selon les spécifications
        \item Tests unitaires et d'intégration réalisés
        \item Couverture de tests > 80\%
    \end{itemize}

    \item \textbf{Code revu et documenté}
    \begin{itemize}
        \item Revue de code par les pairs effectuée
        \item Documentation technique mise à jour
        \item Commentaires de code appropriés
    \end{itemize}

    \item \textbf{Tests d'acceptation utilisateur terminés}
    \begin{itemize}
        \item Validation par le Product Owner
        \item Tests utilisateur réalisés
        \item Feedback intégré
    \end{itemize}

    \item \textbf{Exigences de performance respectées}
    \begin{itemize}
        \item Temps de réponse < 2 secondes
        \item Optimisation des requêtes base de données
        \item Tests de charge validés
    \end{itemize}

    \item \textbf{Exigences de sécurité validées}
    \begin{itemize}
        \item Audit de sécurité effectué
        \item Vulnérabilités corrigées
        \item Conformité aux standards
    \end{itemize}

    \item \textbf{Responsivité mobile vérifiée (si applicable)}
    \begin{itemize}
        \item Tests sur différents appareils
        \item Interface adaptative validée
        \item Performance mobile optimisée
    \end{itemize}
\end{enumerate}
\end{tcolorbox}

\vspace{1cm}

\begin{tcolorbox}[colback=headerblue!10, colframe=headerblue, title=\textbf{Notes Importantes}]
\begin{itemize}[leftmargin=*]
    \item \textbf{Priorités:} Les fonctionnalités marquées "Élevée" doivent être traitées en priorité
    \item \textbf{Dépendances:} Certaines user stories peuvent dépendre d'autres - à valider lors de la planification
    \item \textbf{Estimation:} Les story points sont basés sur la complexité relative et l'effort estimé
    \item \textbf{Révision:} Ce backlog doit être revu et ajusté à chaque fin de sprint
\end{itemize}
\end{tcolorbox}

\end{document}