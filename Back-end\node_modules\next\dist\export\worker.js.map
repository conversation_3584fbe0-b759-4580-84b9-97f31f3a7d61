{"version": 3, "sources": ["../../src/export/worker.ts"], "sourcesContent": ["import type {\n  ExportPagesInput,\n  ExportPageInput,\n  ExportPageResult,\n  ExportRouteResult,\n  WorkerRenderOpts,\n  ExportPagesResult,\n} from './types'\n\nimport '../server/node-environment'\n\nprocess.env.NEXT_IS_EXPORT_WORKER = 'true'\n\nimport { extname, join, dirname, sep } from 'path'\nimport fs from 'fs/promises'\nimport { loadComponents } from '../server/load-components'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport isError from '../lib/is-error'\nimport { addRequestMeta } from '../server/request-meta'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { createRequestResponseMocks } from '../server/lib/mock-request'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { hasNextSupport } from '../server/ci-info'\nimport { exportAppRoute } from './routes/app-route'\nimport { exportAppPage, prospectiveRenderAppPage } from './routes/app-page'\nimport { exportPagesPage } from './routes/pages'\nimport { getParams } from './helpers/get-params'\nimport { createIncrementalCache } from './helpers/create-incremental-cache'\nimport { isPostpone } from '../server/lib/router-utils/is-postpone'\nimport { isDynamicUsageError } from './helpers/is-dynamic-usage-error'\nimport { isBailoutToCSRError } from '../shared/lib/lazy-dynamic/bailout-to-csr'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n} from '../build/turborepo-access-trace'\nimport type { Params } from '../server/request/params'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../server/request/fallback-params'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport type { AppRouteRouteModule } from '../server/route-modules/app-route/module.compiled'\nimport { isStaticGenBailoutError } from '../client/components/static-generation-bailout'\nimport type { PagesRenderContext, PagesSharedContext } from '../server/render'\nimport type { AppSharedContext } from '../server/app-render/app-render'\nimport { MultiFileWriter } from '../lib/multi-file-writer'\n\nconst envConfig = require('../shared/lib/runtime-config.external')\n\n;(globalThis as any).__NEXT_DATA__ = {\n  nextExport: true,\n}\n\nclass TimeoutError extends Error {\n  code = 'NEXT_EXPORT_TIMEOUT_ERROR'\n}\n\nclass ExportPageError extends Error {\n  code = 'NEXT_EXPORT_PAGE_ERROR'\n}\n\nasync function exportPageImpl(\n  input: ExportPageInput,\n  fileWriter: MultiFileWriter\n): Promise<ExportRouteResult | undefined> {\n  const {\n    path,\n    pathMap,\n    distDir,\n    pagesDataDir,\n    buildExport = false,\n    serverRuntimeConfig,\n    subFolders = false,\n    optimizeCss,\n    disableOptimizedLoading,\n    debugOutput = false,\n    enableExperimentalReact,\n    ampValidatorPath,\n    trailingSlash,\n    sriEnabled,\n  } = input\n\n  if (enableExperimentalReact) {\n    process.env.__NEXT_EXPERIMENTAL_REACT = 'true'\n  }\n\n  const {\n    page,\n\n    // The parameters that are currently unknown.\n    _fallbackRouteParams = [],\n\n    // Check if this is an `app/` page.\n    _isAppDir: isAppDir = false,\n\n    // Check if this should error when dynamic usage is detected.\n    _isDynamicError: isDynamicError = false,\n\n    // If this page supports partial prerendering, then we need to pass that to\n    // the renderOpts.\n    _isRoutePPREnabled: isRoutePPREnabled,\n\n    // If this is a prospective render, we don't actually want to persist the\n    // result, we just want to use it to error the build if there's a problem.\n    _isProspectiveRender: isProspectiveRender = false,\n\n    // Pull the original query out.\n    query: originalQuery = {},\n  } = pathMap\n\n  const fallbackRouteParams: FallbackRouteParams | null =\n    getFallbackRouteParams(_fallbackRouteParams)\n\n  let query = { ...originalQuery }\n  const pathname = normalizeAppPath(page)\n  const isDynamic = isDynamicRoute(page)\n  const outDir = isAppDir ? join(distDir, 'server/app') : input.outDir\n\n  const filePath = normalizePagePath(path)\n  const ampPath = `${filePath}.amp`\n  let renderAmpPath = ampPath\n\n  let updatedPath = pathMap._ssgPath || path\n  let locale = pathMap._locale || input.renderOpts.locale\n\n  if (input.renderOpts.locale) {\n    const localePathResult = normalizeLocalePath(path, input.renderOpts.locales)\n\n    if (localePathResult.detectedLocale) {\n      updatedPath = localePathResult.pathname\n      locale = localePathResult.detectedLocale\n\n      if (locale === input.renderOpts.defaultLocale) {\n        renderAmpPath = `${normalizePagePath(updatedPath)}.amp`\n      }\n    }\n  }\n\n  // We need to show a warning if they try to provide query values\n  // for an auto-exported page since they won't be available\n  const hasOrigQueryValues = Object.keys(originalQuery).length > 0\n\n  // Check if the page is a specified dynamic route\n  const { pathname: nonLocalizedPath } = normalizeLocalePath(\n    path,\n    input.renderOpts.locales\n  )\n\n  let params: Params | undefined\n\n  if (isDynamic && page !== nonLocalizedPath) {\n    const normalizedPage = isAppDir ? normalizeAppPath(page) : page\n\n    params = getParams(normalizedPage, updatedPath)\n  }\n\n  const { req, res } = createRequestResponseMocks({ url: updatedPath })\n\n  // If this is a status code page, then set the response code.\n  for (const statusCode of [404, 500]) {\n    if (\n      [\n        `/${statusCode}`,\n        `/${statusCode}.html`,\n        `/${statusCode}/index.html`,\n      ].some((p) => p === updatedPath || `/${locale}${p}` === updatedPath)\n    ) {\n      res.statusCode = statusCode\n    }\n  }\n\n  // Ensure that the URL has a trailing slash if it's configured.\n  if (trailingSlash && !req.url?.endsWith('/')) {\n    req.url += '/'\n  }\n\n  if (\n    locale &&\n    buildExport &&\n    input.renderOpts.domainLocales &&\n    input.renderOpts.domainLocales.some(\n      (dl) => dl.defaultLocale === locale || dl.locales?.includes(locale || '')\n    )\n  ) {\n    addRequestMeta(req, 'isLocaleDomain', true)\n  }\n\n  envConfig.setConfig({\n    serverRuntimeConfig,\n    publicRuntimeConfig: input.renderOpts.runtimeConfig,\n  })\n\n  const getHtmlFilename = (p: string) =>\n    subFolders ? `${p}${sep}index.html` : `${p}.html`\n\n  let htmlFilename = getHtmlFilename(filePath)\n\n  // dynamic routes can provide invalid extensions e.g. /blog/[...slug] returns an\n  // extension of `.slug]`\n  const pageExt = isDynamic || isAppDir ? '' : extname(page)\n  const pathExt = isDynamic || isAppDir ? '' : extname(path)\n\n  // force output 404.html for backwards compat\n  if (path === '/404.html') {\n    htmlFilename = path\n  }\n  // Make sure page isn't a folder with a dot in the name e.g. `v1.2`\n  else if (pageExt !== pathExt && pathExt !== '') {\n    const isBuiltinPaths = ['/500', '/404'].some(\n      (p) => p === path || p === path + '.html'\n    )\n    // If the ssg path has .html extension, and it's not builtin paths, use it directly\n    // Otherwise, use that as the filename instead\n    const isHtmlExtPath = !isBuiltinPaths && path.endsWith('.html')\n    htmlFilename = isHtmlExtPath ? getHtmlFilename(path) : path\n  } else if (path === '/') {\n    // If the path is the root, just use index.html\n    htmlFilename = 'index.html'\n  }\n\n  const baseDir = join(outDir, dirname(htmlFilename))\n  let htmlFilepath = join(outDir, htmlFilename)\n\n  await fs.mkdir(baseDir, { recursive: true })\n\n  const components = await loadComponents({\n    distDir,\n    page,\n    isAppPath: isAppDir,\n    isDev: false,\n    sriEnabled,\n  })\n\n  // Handle App Routes.\n  if (isAppDir && isAppRouteRoute(page)) {\n    return exportAppRoute(\n      req,\n      res,\n      params,\n      page,\n      components.routeModule as AppRouteRouteModule,\n      input.renderOpts.incrementalCache,\n      input.renderOpts.cacheLifeProfiles,\n      htmlFilepath,\n      fileWriter,\n      input.renderOpts.experimental,\n      input.buildId\n    )\n  }\n\n  const renderOpts: WorkerRenderOpts = {\n    ...components,\n    ...input.renderOpts,\n    ampPath: renderAmpPath,\n    params,\n    optimizeCss,\n    disableOptimizedLoading,\n    locale,\n    supportsDynamicResponse: false,\n    // During the export phase in next build, we always enable the streaming metadata since if there's\n    // any dynamic access in metadata we can determine it in the build phase.\n    // If it's static, then it won't affect anything.\n    // If it's dynamic, then it can be handled when request hits the route.\n    serveStreamingMetadata: true,\n    experimental: {\n      ...input.renderOpts.experimental,\n      isRoutePPREnabled,\n    },\n  }\n\n  if (hasNextSupport) {\n    renderOpts.isRevalidate = true\n  }\n\n  // Handle App Pages\n  if (isAppDir) {\n    const sharedContext: AppSharedContext = {\n      buildId: input.buildId,\n    }\n\n    // If this is a prospective render, don't return any metrics or revalidate\n    // timings as we aren't persisting this render (it was only to error).\n    if (isProspectiveRender) {\n      return prospectiveRenderAppPage(\n        req,\n        res,\n        page,\n        pathname,\n        query,\n        fallbackRouteParams,\n        renderOpts,\n        sharedContext\n      )\n    }\n\n    return exportAppPage(\n      req,\n      res,\n      page,\n      path,\n      pathname,\n      query,\n      fallbackRouteParams,\n      renderOpts,\n      htmlFilepath,\n      debugOutput,\n      isDynamicError,\n      fileWriter,\n      sharedContext\n    )\n  }\n\n  const sharedContext: PagesSharedContext = {\n    buildId: input.buildId,\n    deploymentId: input.renderOpts.deploymentId,\n    customServer: undefined,\n  }\n\n  const renderContext: PagesRenderContext = {\n    isFallback: pathMap._pagesFallback ?? false,\n    isDraftMode: false,\n    developmentNotFoundSourcePage: undefined,\n  }\n\n  return exportPagesPage(\n    req,\n    res,\n    path,\n    page,\n    query,\n    params,\n    htmlFilepath,\n    htmlFilename,\n    ampPath,\n    subFolders,\n    outDir,\n    ampValidatorPath,\n    pagesDataDir,\n    buildExport,\n    isDynamic,\n    sharedContext,\n    renderContext,\n    hasOrigQueryValues,\n    renderOpts,\n    components,\n    fileWriter\n  )\n}\n\nexport async function exportPages(\n  input: ExportPagesInput\n): Promise<ExportPagesResult> {\n  const {\n    exportPathMap,\n    paths,\n    dir,\n    distDir,\n    outDir,\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    pagesDataDir,\n    renderOpts,\n    nextConfig,\n    options,\n  } = input\n\n  // If the fetch cache was enabled, we need to create an incremental\n  // cache instance for this page.\n  const incrementalCache = await createIncrementalCache({\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    distDir,\n    dir,\n    // skip writing to disk in minimal mode for now, pending some\n    // changes to better support it\n    flushToDisk: !hasNextSupport,\n    cacheHandlers: nextConfig.experimental.cacheHandlers,\n  })\n\n  renderOpts.incrementalCache = incrementalCache\n\n  const maxConcurrency =\n    nextConfig.experimental.staticGenerationMaxConcurrency ?? 8\n  const results: ExportPagesResult = []\n\n  const exportPageWithRetry = async (path: string, maxAttempts: number) => {\n    const pathMap = exportPathMap[path]\n    const { page } = exportPathMap[path]\n    const pageKey = page !== path ? `${page}: ${path}` : path\n    let attempt = 0\n    let result\n\n    while (attempt < maxAttempts) {\n      try {\n        result = await Promise.race<ExportPageResult | undefined>([\n          exportPage({\n            path,\n            pathMap,\n            distDir,\n            outDir,\n            pagesDataDir,\n            renderOpts,\n            ampValidatorPath:\n              nextConfig.experimental.amp?.validator || undefined,\n            trailingSlash: nextConfig.trailingSlash,\n            serverRuntimeConfig: nextConfig.serverRuntimeConfig,\n            subFolders: nextConfig.trailingSlash && !options.buildExport,\n            buildExport: options.buildExport,\n            optimizeCss: nextConfig.experimental.optimizeCss,\n            disableOptimizedLoading:\n              nextConfig.experimental.disableOptimizedLoading,\n            parentSpanId: input.parentSpanId,\n            httpAgentOptions: nextConfig.httpAgentOptions,\n            debugOutput: options.debugOutput,\n            enableExperimentalReact: needsExperimentalReact(nextConfig),\n            sriEnabled: Boolean(nextConfig.experimental.sri?.algorithm),\n            buildId: input.buildId,\n          }),\n          // If exporting the page takes longer than the timeout, reject the promise.\n          new Promise((_, reject) => {\n            setTimeout(() => {\n              reject(new TimeoutError())\n            }, nextConfig.staticPageGenerationTimeout * 1000)\n          }),\n        ])\n\n        // If there was an error in the export, throw it immediately. In the catch block, we might retry the export,\n        // or immediately fail the build, depending on user configuration. We might also continue on and attempt other pages.\n        if (result && 'error' in result) {\n          throw new ExportPageError()\n        }\n\n        // If the export succeeds, break out of the retry loop\n        break\n      } catch (err) {\n        // The only error that should be caught here is an ExportError, as `exportPage` doesn't throw and instead returns an object with an `error` property.\n        // This is an overly cautious check to ensure that we don't accidentally catch an unexpected error.\n        if (!(err instanceof ExportPageError || err instanceof TimeoutError)) {\n          throw err\n        }\n\n        if (err instanceof TimeoutError) {\n          // If the export times out, we will restart the worker up to 3 times.\n          maxAttempts = 3\n        }\n\n        // We've reached the maximum number of attempts\n        if (attempt >= maxAttempts - 1) {\n          // Log a message if we've reached the maximum number of attempts.\n          // We only care to do this if maxAttempts was configured.\n          if (maxAttempts > 1) {\n            console.info(\n              `Failed to build ${pageKey} after ${maxAttempts} attempts.`\n            )\n          }\n          // If prerenderEarlyExit is enabled, we'll exit the build immediately.\n          if (nextConfig.experimental.prerenderEarlyExit) {\n            console.error(\n              `Export encountered an error on ${pageKey}, exiting the build.`\n            )\n            process.exit(1)\n          } else {\n            // Otherwise, this is a no-op. The build will continue, and a summary of failed pages will be displayed at the end.\n          }\n        } else {\n          // Otherwise, we have more attempts to make. Wait before retrying\n          if (err instanceof TimeoutError) {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}) because it took more than ${nextConfig.staticPageGenerationTimeout} seconds. Retrying again shortly.`\n            )\n          } else {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}). Retrying again shortly.`\n            )\n          }\n\n          // Exponential backoff with random jitter to avoid thundering herd on retries\n          const baseDelay = 500 // 500ms\n          const maxDelay = 2000 // 2 seconds\n          const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay)\n          const jitter = Math.random() * 0.3 * delay // Add up to 30% random jitter\n          await new Promise((r) => setTimeout(r, delay + jitter))\n        }\n      }\n\n      attempt++\n    }\n\n    return { result, path, pageKey }\n  }\n\n  for (let i = 0; i < paths.length; i += maxConcurrency) {\n    const subset = paths.slice(i, i + maxConcurrency)\n\n    const subsetResults = await Promise.all(\n      subset.map((path) =>\n        exportPageWithRetry(\n          path,\n          nextConfig.experimental.staticGenerationRetryCount ?? 1\n        )\n      )\n    )\n\n    results.push(...subsetResults)\n  }\n\n  return results\n}\n\nasync function exportPage(\n  input: ExportPageInput\n): Promise<ExportPageResult | undefined> {\n  trace('export-page', input.parentSpanId).setAttribute('path', input.path)\n\n  // Configure the http agent.\n  setHttpClientAndAgentOptions({\n    httpAgentOptions: input.httpAgentOptions,\n  })\n\n  const fileWriter = new MultiFileWriter({\n    writeFile: (filePath, data) => fs.writeFile(filePath, data),\n    mkdir: (dir) => fs.mkdir(dir, { recursive: true }),\n  })\n\n  const exportPageSpan = trace('export-page-worker', input.parentSpanId)\n\n  const start = Date.now()\n\n  const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n\n  // Export the page.\n  let result: ExportRouteResult | undefined\n  try {\n    result = await exportPageSpan.traceAsyncFn(() =>\n      turborepoTraceAccess(\n        () => exportPageImpl(input, fileWriter),\n        turborepoAccessTraceResult\n      )\n    )\n\n    // Wait for all the files to flush to disk.\n    await fileWriter.wait()\n\n    // If there was no result, then we can exit early.\n    if (!result) return\n\n    // If there was an error, then we can exit early.\n    if ('error' in result) {\n      return { error: result.error, duration: Date.now() - start }\n    }\n  } catch (err) {\n    console.error(\n      `Error occurred prerendering page \"${input.path}\". Read more: https://nextjs.org/docs/messages/prerender-error`\n    )\n\n    // bailoutToCSRError errors should not leak to the user as they are not actionable; they're\n    // a framework signal\n    if (!isBailoutToCSRError(err)) {\n      // A static generation bailout error is a framework signal to fail static generation but\n      // and will encode a reason in the error message. If there is a message, we'll print it.\n      // Otherwise there's nothing to show as we don't want to leak an error internal error stack to the user.\n      if (isStaticGenBailoutError(err)) {\n        if (err.message) {\n          console.error(`Error: ${err.message}`)\n        }\n      } else if (isError(err) && err.stack) {\n        console.error(err.stack)\n      } else {\n        console.error(err)\n      }\n    }\n\n    return { error: true, duration: Date.now() - start }\n  }\n\n  // Notify the parent process that we processed a page (used by the progress activity indicator)\n  process.send?.([3, { type: 'activity' }])\n\n  // Otherwise we can return the result.\n  return {\n    duration: Date.now() - start,\n    ampValidations: result.ampValidations,\n    cacheControl: result.cacheControl,\n    metadata: result.metadata,\n    ssgNotFound: result.ssgNotFound,\n    hasEmptyPrelude: result.hasEmptyPrelude,\n    hasPostponed: result.hasPostponed,\n    turborepoAccessTraceResult: turborepoAccessTraceResult.serialize(),\n    fetchMetrics: result.fetchMetrics,\n  }\n}\n\nprocess.on('unhandledRejection', (err: unknown) => {\n  // if it's a postpone error, it'll be handled later\n  // when the postponed promise is actually awaited.\n  if (isPostpone(err)) {\n    return\n  }\n\n  // we don't want to log these errors\n  if (isDynamicUsageError(err)) {\n    return\n  }\n\n  console.error(err)\n})\n\nprocess.on('rejectionHandled', () => {\n  // It is ok to await a Promise late in Next.js as it allows for better\n  // prefetching patterns to avoid waterfalls. We ignore logging these.\n  // We should've already errored in anyway unhandledRejection.\n})\n\nconst FATAL_UNHANDLED_NEXT_API_EXIT_CODE = 78\n\nprocess.on('uncaughtException', (err) => {\n  if (isDynamicUsageError(err)) {\n    console.error(\n      'A Next.js API that uses exceptions to signal framework behavior was uncaught. This suggests improper usage of a Next.js API. The original error is printed below and the build will now exit.'\n    )\n    console.error(err)\n    process.exit(FATAL_UNHANDLED_NEXT_API_EXIT_CODE)\n  } else {\n    console.error(err)\n  }\n})\n"], "names": ["exportPages", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "TimeoutError", "Error", "code", "ExportPageError", "exportPageImpl", "input", "fileWriter", "req", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeCss", "disableOptimizedLoading", "debugOutput", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "sriEnabled", "__NEXT_EXPERIMENTAL_REACT", "page", "_fallbackRouteParams", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "_isRoutePPREnabled", "isRoutePPREnabled", "_isProspectiveRender", "isProspectiveRender", "query", "originalQuery", "fallbackRouteParams", "getFallbackRouteParams", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "_ssgPath", "locale", "_locale", "renderOpts", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "params", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "components", "loadComponents", "isAppPath", "isDev", "isAppRouteRoute", "exportAppRoute", "routeModule", "incrementalCache", "cacheLifeProfiles", "experimental", "buildId", "supportsDynamicResponse", "serveStreamingMetadata", "hasNextSupport", "isRevalidate", "sharedContext", "prospectiveRenderAppPage", "exportAppPage", "deploymentId", "customServer", "undefined", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "_pagesFallback", "isDraftMode", "developmentNotFoundSourcePage", "exportPagesPage", "exportPathMap", "paths", "dir", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "nextConfig", "options", "createIncrementalCache", "flushToDisk", "cacheHandlers", "maxConcurrency", "staticGenerationMaxConcurrency", "results", "exportPageWithRetry", "maxAttempts", "page<PERSON><PERSON>", "attempt", "result", "Promise", "race", "exportPage", "amp", "validator", "parentSpanId", "httpAgentOptions", "needsExperimentalReact", "Boolean", "sri", "algorithm", "_", "reject", "setTimeout", "staticPageGenerationTimeout", "err", "console", "info", "prerenderEarlyExit", "error", "exit", "baseDelay", "max<PERSON><PERSON><PERSON>", "delay", "Math", "min", "pow", "jitter", "random", "r", "i", "subset", "slice", "subsetResults", "all", "map", "staticGenerationRetryCount", "push", "trace", "setAttribute", "setHttpClientAndAgentOptions", "MultiFileWriter", "writeFile", "data", "exportPageSpan", "start", "Date", "now", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "traceAsyncFn", "turborepoTraceAccess", "wait", "duration", "isBailoutToCSRError", "isStaticGenBailoutError", "message", "isError", "stack", "send", "type", "ampValidations", "cacheControl", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "serialize", "fetchMetrics", "on", "isPostpone", "isDynamicUsageError", "FATAL_UNHANDLED_NEXT_API_EXIT_CODE"], "mappings": ";;;;+BAkWsBA;;;eAAAA;;;QAzVf;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;qCACE;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACyB;uBACxB;2BACN;wCACa;4BACZ;qCACS;8BACA;sCAI7B;gCAKA;wCACgC;yCAEC;iCAGR;;;;;;AAvChCC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAyCpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,MAAMC,qBAAqBC;;QAA3B,qBACEC,OAAO;;AACT;AAEA,MAAMC,wBAAwBF;;QAA9B,qBACEC,OAAO;;AACT;AAEA,eAAeE,eACbC,KAAsB,EACtBC,UAA2B;QA6GLC;IA3GtB,MAAM,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,UAAU,EACX,GAAGhB;IAEJ,IAAIa,yBAAyB;QAC3B1B,QAAQC,GAAG,CAAC6B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,6CAA6C;IAC7CC,uBAAuB,EAAE,EAEzB,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,2EAA2E;IAC3E,kBAAkB;IAClBC,oBAAoBC,iBAAiB,EAErC,yEAAyE;IACzE,0EAA0E;IAC1EC,sBAAsBC,sBAAsB,KAAK,EAEjD,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGzB;IAEJ,MAAM0B,sBACJC,IAAAA,sCAAsB,EAACZ;IAEzB,IAAIS,QAAQ;QAAE,GAAGC,aAAa;IAAC;IAC/B,MAAMG,WAAWC,IAAAA,0BAAgB,EAACf;IAClC,MAAMgB,YAAYC,IAAAA,yBAAc,EAACjB;IACjC,MAAMkB,SAASf,WAAWgB,IAAAA,UAAI,EAAChC,SAAS,gBAAgBL,MAAMoC,MAAM;IAEpE,MAAME,WAAWC,IAAAA,oCAAiB,EAACpC;IACnC,MAAMqC,UAAU,GAAGF,SAAS,IAAI,CAAC;IACjC,IAAIG,gBAAgBD;IAEpB,IAAIE,cAActC,QAAQuC,QAAQ,IAAIxC;IACtC,IAAIyC,SAASxC,QAAQyC,OAAO,IAAI7C,MAAM8C,UAAU,CAACF,MAAM;IAEvD,IAAI5C,MAAM8C,UAAU,CAACF,MAAM,EAAE;QAC3B,MAAMG,mBAAmBC,IAAAA,wCAAmB,EAAC7C,MAAMH,MAAM8C,UAAU,CAACG,OAAO;QAE3E,IAAIF,iBAAiBG,cAAc,EAAE;YACnCR,cAAcK,iBAAiBf,QAAQ;YACvCY,SAASG,iBAAiBG,cAAc;YAExC,IAAIN,WAAW5C,MAAM8C,UAAU,CAACK,aAAa,EAAE;gBAC7CV,gBAAgB,GAAGF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;YACzD;QACF;IACF;IAEA,gEAAgE;IAChE,0DAA0D;IAC1D,MAAMU,qBAAqBC,OAAOC,IAAI,CAACzB,eAAe0B,MAAM,GAAG;IAE/D,iDAAiD;IACjD,MAAM,EAAEvB,UAAUwB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxD7C,MACAH,MAAM8C,UAAU,CAACG,OAAO;IAG1B,IAAIQ;IAEJ,IAAIvB,aAAahB,SAASsC,kBAAkB;QAC1C,MAAME,iBAAiBrC,WAAWY,IAAAA,0BAAgB,EAACf,QAAQA;QAE3DuC,SAASE,IAAAA,oBAAS,EAACD,gBAAgBhB;IACrC;IAEA,MAAM,EAAExC,GAAG,EAAE0D,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;QAAEC,KAAKpB;IAAY;IAEnE,6DAA6D;IAC7D,KAAK,MAAMqB,cAAc;QAAC;QAAK;KAAI,CAAE;QACnC,IACE;YACE,CAAC,CAAC,EAAEA,YAAY;YAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;YACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;SAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMvB,eAAe,CAAC,CAAC,EAAEE,SAASqB,GAAG,KAAKvB,cACxD;YACAkB,IAAIG,UAAU,GAAGA;QACnB;IACF;IAEA,+DAA+D;IAC/D,IAAIhD,iBAAiB,GAACb,WAAAA,IAAI4D,GAAG,qBAAP5D,SAASgE,QAAQ,CAAC,OAAM;QAC5ChE,IAAI4D,GAAG,IAAI;IACb;IAEA,IACElB,UACArC,eACAP,MAAM8C,UAAU,CAACqB,aAAa,IAC9BnE,MAAM8C,UAAU,CAACqB,aAAa,CAACH,IAAI,CACjC,CAACI;YAAsCA;eAA/BA,GAAGjB,aAAa,KAAKP,YAAUwB,cAAAA,GAAGnB,OAAO,qBAAVmB,YAAYC,QAAQ,CAACzB,UAAU;QAExE;QACA0B,IAAAA,2BAAc,EAACpE,KAAK,kBAAkB;IACxC;IAEAZ,UAAUiF,SAAS,CAAC;QAClB/D;QACAgE,qBAAqBxE,MAAM8C,UAAU,CAAC2B,aAAa;IACrD;IAEA,MAAMC,kBAAkB,CAACT,IACvBxD,aAAa,GAAGwD,IAAIU,SAAG,CAAC,UAAU,CAAC,GAAG,GAAGV,EAAE,KAAK,CAAC;IAEnD,IAAIW,eAAeF,gBAAgBpC;IAEnC,gFAAgF;IAChF,wBAAwB;IACxB,MAAMuC,UAAU3C,aAAab,WAAW,KAAKyD,IAAAA,aAAO,EAAC5D;IACrD,MAAM6D,UAAU7C,aAAab,WAAW,KAAKyD,IAAAA,aAAO,EAAC3E;IAErD,6CAA6C;IAC7C,IAAIA,SAAS,aAAa;QACxByE,eAAezE;IACjB,OAEK,IAAI0E,YAAYE,WAAWA,YAAY,IAAI;QAC9C,MAAMC,iBAAiB;YAAC;YAAQ;SAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAM9D,QAAQ8D,MAAM9D,OAAO;QAEpC,mFAAmF;QACnF,8CAA8C;QAC9C,MAAM8E,gBAAgB,CAACD,kBAAkB7E,KAAK+D,QAAQ,CAAC;QACvDU,eAAeK,gBAAgBP,gBAAgBvE,QAAQA;IACzD,OAAO,IAAIA,SAAS,KAAK;QACvB,+CAA+C;QAC/CyE,eAAe;IACjB;IAEA,MAAMM,UAAU7C,IAAAA,UAAI,EAACD,QAAQ+C,IAAAA,aAAO,EAACP;IACrC,IAAIQ,eAAe/C,IAAAA,UAAI,EAACD,QAAQwC;IAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;QAAEK,WAAW;IAAK;IAE1C,MAAMC,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtCpF;QACAa;QACAwE,WAAWrE;QACXsE,OAAO;QACP3E;IACF;IAEA,qBAAqB;IACrB,IAAIK,YAAYuE,IAAAA,gCAAe,EAAC1E,OAAO;QACrC,OAAO2E,IAAAA,wBAAc,EACnB3F,KACA0D,KACAH,QACAvC,MACAsE,WAAWM,WAAW,EACtB9F,MAAM8C,UAAU,CAACiD,gBAAgB,EACjC/F,MAAM8C,UAAU,CAACkD,iBAAiB,EAClCZ,cACAnF,YACAD,MAAM8C,UAAU,CAACmD,YAAY,EAC7BjG,MAAMkG,OAAO;IAEjB;IAEA,MAAMpD,aAA+B;QACnC,GAAG0C,UAAU;QACb,GAAGxF,MAAM8C,UAAU;QACnBN,SAASC;QACTgB;QACA/C;QACAC;QACAiC;QACAuD,yBAAyB;QACzB,kGAAkG;QAClG,yEAAyE;QACzE,iDAAiD;QACjD,uEAAuE;QACvEC,wBAAwB;QACxBH,cAAc;YACZ,GAAGjG,MAAM8C,UAAU,CAACmD,YAAY;YAChCxE;QACF;IACF;IAEA,IAAI4E,sBAAc,EAAE;QAClBvD,WAAWwD,YAAY,GAAG;IAC5B;IAEA,mBAAmB;IACnB,IAAIjF,UAAU;QACZ,MAAMkF,gBAAkC;YACtCL,SAASlG,MAAMkG,OAAO;QACxB;QAEA,0EAA0E;QAC1E,sEAAsE;QACtE,IAAIvE,qBAAqB;YACvB,OAAO6E,IAAAA,iCAAwB,EAC7BtG,KACA0D,KACA1C,MACAc,UACAJ,OACAE,qBACAgB,YACAyD;QAEJ;QAEA,OAAOE,IAAAA,sBAAa,EAClBvG,KACA0D,KACA1C,MACAf,MACA6B,UACAJ,OACAE,qBACAgB,YACAsC,cACAxE,aACAW,gBACAtB,YACAsG;IAEJ;IAEA,MAAMA,gBAAoC;QACxCL,SAASlG,MAAMkG,OAAO;QACtBQ,cAAc1G,MAAM8C,UAAU,CAAC4D,YAAY;QAC3CC,cAAcC;IAChB;IAEA,MAAMC,gBAAoC;QACxCC,YAAY1G,QAAQ2G,cAAc,IAAI;QACtCC,aAAa;QACbC,+BAA+BL;IACjC;IAEA,OAAOM,IAAAA,sBAAe,EACpBhH,KACA0D,KACAzD,MACAe,MACAU,OACA6B,QACA2B,cACAR,cACApC,SACA/B,YACA2B,QACAtB,kBACAR,cACAC,aACA2B,WACAqE,eACAM,eACAzD,oBACAN,YACA0C,YACAvF;AAEJ;AAEO,eAAef,YACpBc,KAAuB;IAEvB,MAAM,EACJmH,aAAa,EACbC,KAAK,EACLC,GAAG,EACHhH,OAAO,EACP+B,MAAM,EACNkF,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBlH,YAAY,EACZwC,UAAU,EACV2E,UAAU,EACVC,OAAO,EACR,GAAG1H;IAEJ,mEAAmE;IACnE,gCAAgC;IAChC,MAAM+F,mBAAmB,MAAM4B,IAAAA,8CAAsB,EAAC;QACpDL;QACAC;QACAC;QACAnH;QACAgH;QACA,6DAA6D;QAC7D,+BAA+B;QAC/BO,aAAa,CAACvB,sBAAc;QAC5BwB,eAAeJ,WAAWxB,YAAY,CAAC4B,aAAa;IACtD;IAEA/E,WAAWiD,gBAAgB,GAAGA;IAE9B,MAAM+B,iBACJL,WAAWxB,YAAY,CAAC8B,8BAA8B,IAAI;IAC5D,MAAMC,UAA6B,EAAE;IAErC,MAAMC,sBAAsB,OAAO9H,MAAc+H;QAC/C,MAAM9H,UAAU+G,aAAa,CAAChH,KAAK;QACnC,MAAM,EAAEe,IAAI,EAAE,GAAGiG,aAAa,CAAChH,KAAK;QACpC,MAAMgI,UAAUjH,SAASf,OAAO,GAAGe,KAAK,EAAE,EAAEf,MAAM,GAAGA;QACrD,IAAIiI,UAAU;QACd,IAAIC;QAEJ,MAAOD,UAAUF,YAAa;YAC5B,IAAI;oBAUIT,8BAYkBA;gBArBxBY,SAAS,MAAMC,QAAQC,IAAI,CAA+B;oBACxDC,WAAW;wBACTrI;wBACAC;wBACAC;wBACA+B;wBACA9B;wBACAwC;wBACAhC,kBACE2G,EAAAA,+BAAAA,WAAWxB,YAAY,CAACwC,GAAG,qBAA3BhB,6BAA6BiB,SAAS,KAAI9B;wBAC5C7F,eAAe0G,WAAW1G,aAAa;wBACvCP,qBAAqBiH,WAAWjH,mBAAmB;wBACnDC,YAAYgH,WAAW1G,aAAa,IAAI,CAAC2G,QAAQnH,WAAW;wBAC5DA,aAAamH,QAAQnH,WAAW;wBAChCG,aAAa+G,WAAWxB,YAAY,CAACvF,WAAW;wBAChDC,yBACE8G,WAAWxB,YAAY,CAACtF,uBAAuB;wBACjDgI,cAAc3I,MAAM2I,YAAY;wBAChCC,kBAAkBnB,WAAWmB,gBAAgB;wBAC7ChI,aAAa8G,QAAQ9G,WAAW;wBAChCC,yBAAyBgI,IAAAA,8CAAsB,EAACpB;wBAChDzG,YAAY8H,SAAQrB,+BAAAA,WAAWxB,YAAY,CAAC8C,GAAG,qBAA3BtB,6BAA6BuB,SAAS;wBAC1D9C,SAASlG,MAAMkG,OAAO;oBACxB;oBACA,2EAA2E;oBAC3E,IAAIoC,QAAQ,CAACW,GAAGC;wBACdC,WAAW;4BACTD,OAAO,IAAIvJ;wBACb,GAAG8H,WAAW2B,2BAA2B,GAAG;oBAC9C;iBACD;gBAED,4GAA4G;gBAC5G,qHAAqH;gBACrH,IAAIf,UAAU,WAAWA,QAAQ;oBAC/B,MAAM,IAAIvI;gBACZ;gBAGA;YACF,EAAE,OAAOuJ,KAAK;gBACZ,qJAAqJ;gBACrJ,mGAAmG;gBACnG,IAAI,CAAEA,CAAAA,eAAevJ,mBAAmBuJ,eAAe1J,YAAW,GAAI;oBACpE,MAAM0J;gBACR;gBAEA,IAAIA,eAAe1J,cAAc;oBAC/B,qEAAqE;oBACrEuI,cAAc;gBAChB;gBAEA,+CAA+C;gBAC/C,IAAIE,WAAWF,cAAc,GAAG;oBAC9B,iEAAiE;oBACjE,yDAAyD;oBACzD,IAAIA,cAAc,GAAG;wBACnBoB,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAEpB,QAAQ,OAAO,EAAED,YAAY,UAAU,CAAC;oBAE/D;oBACA,sEAAsE;oBACtE,IAAIT,WAAWxB,YAAY,CAACuD,kBAAkB,EAAE;wBAC9CF,QAAQG,KAAK,CACX,CAAC,+BAA+B,EAAEtB,QAAQ,oBAAoB,CAAC;wBAEjEhJ,QAAQuK,IAAI,CAAC;oBACf,OAAO;oBACL,mHAAmH;oBACrH;gBACF,OAAO;oBACL,iEAAiE;oBACjE,IAAIL,eAAe1J,cAAc;wBAC/B2J,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAEpB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,4BAA4B,EAAET,WAAW2B,2BAA2B,CAAC,iCAAiC,CAAC;oBAEhL,OAAO;wBACLE,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAEpB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,0BAA0B,CAAC;oBAEpG;oBAEA,6EAA6E;oBAC7E,MAAMyB,YAAY,IAAI,QAAQ;;oBAC9B,MAAMC,WAAW,KAAK,YAAY;;oBAClC,MAAMC,QAAQC,KAAKC,GAAG,CAACJ,YAAYG,KAAKE,GAAG,CAAC,GAAG5B,UAAUwB;oBACzD,MAAMK,SAASH,KAAKI,MAAM,KAAK,MAAML,MAAM,8BAA8B;;oBACzE,MAAM,IAAIvB,QAAQ,CAAC6B,IAAMhB,WAAWgB,GAAGN,QAAQI;gBACjD;YACF;YAEA7B;QACF;QAEA,OAAO;YAAEC;YAAQlI;YAAMgI;QAAQ;IACjC;IAEA,IAAK,IAAIiC,IAAI,GAAGA,IAAIhD,MAAM7D,MAAM,EAAE6G,KAAKtC,eAAgB;QACrD,MAAMuC,SAASjD,MAAMkD,KAAK,CAACF,GAAGA,IAAItC;QAElC,MAAMyC,gBAAgB,MAAMjC,QAAQkC,GAAG,CACrCH,OAAOI,GAAG,CAAC,CAACtK,OACV8H,oBACE9H,MACAsH,WAAWxB,YAAY,CAACyE,0BAA0B,IAAI;QAK5D1C,QAAQ2C,IAAI,IAAIJ;IAClB;IAEA,OAAOvC;AACT;AAEA,eAAeQ,WACbxI,KAAsB;IAEtB4K,IAAAA,YAAK,EAAC,eAAe5K,MAAM2I,YAAY,EAAEkC,YAAY,CAAC,QAAQ7K,MAAMG,IAAI;IAExE,4BAA4B;IAC5B2K,IAAAA,+CAA4B,EAAC;QAC3BlC,kBAAkB5I,MAAM4I,gBAAgB;IAC1C;IAEA,MAAM3I,aAAa,IAAI8K,gCAAe,CAAC;QACrCC,WAAW,CAAC1I,UAAU2I,OAAS5F,iBAAE,CAAC2F,SAAS,CAAC1I,UAAU2I;QACtD3F,OAAO,CAAC+B,MAAQhC,iBAAE,CAACC,KAAK,CAAC+B,KAAK;gBAAE9B,WAAW;YAAK;IAClD;IAEA,MAAM2F,iBAAiBN,IAAAA,YAAK,EAAC,sBAAsB5K,MAAM2I,YAAY;IAErE,MAAMwC,QAAQC,KAAKC,GAAG;IAEtB,MAAMC,6BAA6B,IAAIC,gDAA0B;IAEjE,mBAAmB;IACnB,IAAIlD;IACJ,IAAI;QACFA,SAAS,MAAM6C,eAAeM,YAAY,CAAC,IACzCC,IAAAA,0CAAoB,EAClB,IAAM1L,eAAeC,OAAOC,aAC5BqL;QAIJ,2CAA2C;QAC3C,MAAMrL,WAAWyL,IAAI;QAErB,kDAAkD;QAClD,IAAI,CAACrD,QAAQ;QAEb,iDAAiD;QACjD,IAAI,WAAWA,QAAQ;YACrB,OAAO;gBAAEoB,OAAOpB,OAAOoB,KAAK;gBAAEkC,UAAUP,KAAKC,GAAG,KAAKF;YAAM;QAC7D;IACF,EAAE,OAAO9B,KAAK;QACZC,QAAQG,KAAK,CACX,CAAC,kCAAkC,EAAEzJ,MAAMG,IAAI,CAAC,8DAA8D,CAAC;QAGjH,2FAA2F;QAC3F,qBAAqB;QACrB,IAAI,CAACyL,IAAAA,iCAAmB,EAACvC,MAAM;YAC7B,wFAAwF;YACxF,wFAAwF;YACxF,wGAAwG;YACxG,IAAIwC,IAAAA,gDAAuB,EAACxC,MAAM;gBAChC,IAAIA,IAAIyC,OAAO,EAAE;oBACfxC,QAAQG,KAAK,CAAC,CAAC,OAAO,EAAEJ,IAAIyC,OAAO,EAAE;gBACvC;YACF,OAAO,IAAIC,IAAAA,gBAAO,EAAC1C,QAAQA,IAAI2C,KAAK,EAAE;gBACpC1C,QAAQG,KAAK,CAACJ,IAAI2C,KAAK;YACzB,OAAO;gBACL1C,QAAQG,KAAK,CAACJ;YAChB;QACF;QAEA,OAAO;YAAEI,OAAO;YAAMkC,UAAUP,KAAKC,GAAG,KAAKF;QAAM;IACrD;IAEA,+FAA+F;IAC/FhM,QAAQ8M,IAAI,oBAAZ9M,QAAQ8M,IAAI,MAAZ9M,SAAe;QAAC;QAAG;YAAE+M,MAAM;QAAW;KAAE;IAExC,sCAAsC;IACtC,OAAO;QACLP,UAAUP,KAAKC,GAAG,KAAKF;QACvBgB,gBAAgB9D,OAAO8D,cAAc;QACrCC,cAAc/D,OAAO+D,YAAY;QACjCC,UAAUhE,OAAOgE,QAAQ;QACzBC,aAAajE,OAAOiE,WAAW;QAC/BC,iBAAiBlE,OAAOkE,eAAe;QACvCC,cAAcnE,OAAOmE,YAAY;QACjClB,4BAA4BA,2BAA2BmB,SAAS;QAChEC,cAAcrE,OAAOqE,YAAY;IACnC;AACF;AAEAvN,QAAQwN,EAAE,CAAC,sBAAsB,CAACtD;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAIuD,IAAAA,sBAAU,EAACvD,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAIwD,IAAAA,wCAAmB,EAACxD,MAAM;QAC5B;IACF;IAEAC,QAAQG,KAAK,CAACJ;AAChB;AAEAlK,QAAQwN,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D;AAEA,MAAMG,qCAAqC;AAE3C3N,QAAQwN,EAAE,CAAC,qBAAqB,CAACtD;IAC/B,IAAIwD,IAAAA,wCAAmB,EAACxD,MAAM;QAC5BC,QAAQG,KAAK,CACX;QAEFH,QAAQG,KAAK,CAACJ;QACdlK,QAAQuK,IAAI,CAACoD;IACf,OAAO;QACLxD,QAAQG,KAAK,CAACJ;IAChB;AACF"}