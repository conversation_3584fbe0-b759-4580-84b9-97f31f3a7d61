import React, { useState, useEffect } from 'react';
import { Badge } from '@mui/material';
import abonnementService from '../../../services/abonnementService';

const RenewalRequestsBadge = ({ children }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    // Fonction pour récupérer le nombre de demandes non vues
    const fetchCount = async () => {
      try {
        const response = await abonnementService.getDemandesRenouvellementCount();
        setCount(response.count);
      } catch (error) {
        console.error('Erreur lors de la récupération du nombre de demandes:', error);
        setCount(0);
      }
    };

    // Récupérer le nombre initial
    fetchCount();

    // Mettre à jour le nombre toutes les 30 secondes
    const intervalId = setInterval(fetchCount, 30000);

    // Nettoyer l'intervalle lors du démontage du composant
    return () => clearInterval(intervalId);
  }, []);

  return (
    <Badge 
      badgeContent={count} 
      color="error"
      sx={{
        '& .MuiBadge-badge': {
          right: -3,
          top: 3,
          border: '2px solid #fff',
          padding: '0 4px',
        }
      }}
    >
      {children}
    </Badge>
  );
};

export default RenewalRequestsBadge;
