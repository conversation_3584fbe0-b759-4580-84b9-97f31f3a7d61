{"version": 3, "sources": ["../../../src/client/react-client-callbacks/report-global-error.ts"], "sourcesContent": ["export const reportGlobalError =\n  typeof reportError === 'function'\n    ? // In modern browsers, reportError will dispatch an error event,\n      // emulating an uncaught JavaScript error.\n      reportError\n    : (error: unknown) => {\n        // TODO: Dispatch error event\n        globalThis.console.error(error)\n      }\n"], "names": ["reportGlobalError", "reportError", "error", "globalThis", "console"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,oBACX,OAAOC,gBAAgB,aAEnB,0CAA0C;AAC1CA,cACA,CAACC;IACC,6BAA6B;IAC7BC,WAAWC,OAAO,CAACF,KAAK,CAACA;AAC3B"}