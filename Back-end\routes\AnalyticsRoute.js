const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const moment = require('moment');

// Import models
const Facture = require('../models/FactureModel');
const Devis = require('../models/DevisModel');
const Client = require('../models/ClientModel');
const Produit = require('../models/ProduitModel');

// Import middleware
const { verifyToken, isAdmin, isVendeur } = require('../middleware/authMiddleware');

// Helper function to get date range based on period
const getDateRange = (period = 'monthly', date = new Date()) => {
  const startDate = new Date(date);
  const endDate = new Date(date);

  switch (period) {
    case 'daily':
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'weekly':
      const day = startDate.getDay();
      startDate.setDate(startDate.getDate() - day + (day === 0 ? -6 : 1)); // Start from Monday
      startDate.setHours(0, 0, 0, 0);
      endDate.setDate(startDate.getDate() + 6); // End on Sunday
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'monthly':
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'quarterly':
      const quarter = Math.floor(startDate.getMonth() / 3);
      startDate.setMonth(quarter * 3);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(quarter * 3 + 3);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'yearly':
      startDate.setMonth(0);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(12);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'all-time':
      // For all-time, set a very wide range that includes all possible test data
      startDate.setFullYear(1900, 0, 1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setFullYear(2100, 11, 31);
      endDate.setHours(23, 59, 59, 999);
      break;
    default:
      // Default to monthly if invalid period is provided
      console.warn(`Invalid period: ${period}, defaulting to monthly`);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
  }

  return { startDate, endDate };
};

// GET /analytics/dashboard - Get dashboard analytics data
router.get('/analytics/dashboard', verifyToken, async (req, res) => {
  try {
    const { period = 'all-time', date } = req.query;
    const targetDate = date ? new Date(date) : new Date();
    const userRole = req.userRole;
    const userId = req.userId;

    console.log(`Fetching dashboard data for user: ${userId}, role: ${userRole}, period: ${period}`);

    // Get date range for the specified period
    const { startDate, endDate } = getDateRange(period, targetDate);

    // Build match filter based on user role
    let matchFilter = {
      dateEmission: { $gte: startDate, $lte: endDate }
    };

    if (userRole === 'VENDEUR') {
      matchFilter.vendeurId = new mongoose.Types.ObjectId(userId);
      console.log('Filtering by vendeurId:', userId);
    } else if (userRole === 'ENTREPRISE') {
      // For ENTREPRISE users, we need to find all vendeurs associated with this enterprise
      // and include their invoices
      const User = require('../models/UserModel');
      const vendeurs = await User.find({
        role: 'VENDEUR',
        entreprises: { $in: [new mongoose.Types.ObjectId(userId)] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log(`Found ${vendeurIds.length} vendeurs for enterprise user ${userId}`);

      if (vendeurIds.length > 0) {
        matchFilter.vendeurId = { $in: vendeurIds.map(id => new mongoose.Types.ObjectId(id)) };
      } else {
        // If no vendeurs found, try to use the entrepriseId directly
        matchFilter.entrepriseId = new mongoose.Types.ObjectId(userId);
      }
    }
    // For ADMIN users, no additional filters needed - they see all data

    console.log('Using match filter:', JSON.stringify(matchFilter));

    // Use MongoDB aggregation to calculate counts and sums directly in the database
    // This is much more efficient than fetching all documents and processing in memory

    // Get invoice counts and revenue by status
    const invoiceStats = await Facture.aggregate([
      {
        $match: matchFilter
      },
      {
        $group: {
          _id: "$statut",
          count: { $sum: 1 },
          total: { $sum: "$total" }
        }
      }
    ]);

    // Get quote counts by status
    // Adjust the match filter for Devis (quotes) which use dateCréation instead of dateEmission
    const quoteMatchFilter = { ...matchFilter };
    delete quoteMatchFilter.dateEmission;
    quoteMatchFilter.dateCréation = { $gte: startDate, $lte: endDate };

    console.log('Using quote match filter:', JSON.stringify(quoteMatchFilter));

    const quoteStats = await Devis.aggregate([
      {
        $match: quoteMatchFilter
      },
      {
        $group: {
          _id: "$statut",
          count: { $sum: 1 }
        }
      }
    ]);

    // Build client filter based on user role
    let clientFilter = {};
    let totalClientsCount = 0;
    let newClientsCount = 0;

    if (userRole === 'VENDEUR') {
      clientFilter.vendeurId = new mongoose.Types.ObjectId(userId);

      // Count new clients
      const newClientsFilter = {
        ...clientFilter,
        createdAt: { $gte: startDate, $lte: endDate }
      };
      console.log('New clients filter:', JSON.stringify(newClientsFilter));
      newClientsCount = await Client.countDocuments(newClientsFilter);

      // Count total clients
      console.log('Total clients filter:', JSON.stringify(clientFilter));
      totalClientsCount = await Client.countDocuments(clientFilter);
    } else if (userRole === 'ENTREPRISE') {
      // For ENTREPRISE users, we need to find all vendeurs associated with this enterprise
      // and count their clients
      const User = require('../models/UserModel');
      const vendeurs = await User.find({
        role: 'VENDEUR',
        entreprises: { $in: [new mongoose.Types.ObjectId(userId)] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log(`Found ${vendeurIds.length} vendeurs for enterprise user ${userId}`);

      if (vendeurIds.length > 0) {
        // Count clients associated with any of these vendeurs
        totalClientsCount = await Client.countDocuments({
          vendeurId: { $in: vendeurIds.map(id => new mongoose.Types.ObjectId(id)) }
        });

        // Count new clients
        newClientsCount = await Client.countDocuments({
          vendeurId: { $in: vendeurIds.map(id => new mongoose.Types.ObjectId(id)) },
          createdAt: { $gte: startDate, $lte: endDate }
        });
      } else {
        // If no vendeurs found, try to use the ClientRoute.js logic to get clients
        // Get all clients
        const allClients = await Client.find();

        // If the user is an ENTREPRISE, filter out any clients that are actually vendeurs
        // associated with this entreprise (similar to ClientRoute.js logic)
        const vendeurs = await User.find({
          role: 'VENDEUR',
          entreprises: { $in: [new mongoose.Types.ObjectId(userId)] }
        });

        // Get the emails of all vendeurs
        const vendeurEmails = vendeurs.map(v => v.email);

        // Filter out clients with the same email as any vendeur
        const filteredClients = allClients.filter(client => !vendeurEmails.includes(client.email));

        totalClientsCount = filteredClients.length;

        // Count new clients
        newClientsCount = filteredClients.filter(
          client => client.createdAt >= startDate && client.createdAt <= endDate
        ).length;
      }
    } else {
      // For ADMIN users, count all clients
      totalClientsCount = await Client.countDocuments({});
      newClientsCount = await Client.countDocuments({
        createdAt: { $gte: startDate, $lte: endDate }
      });
    }

    // Process invoice stats
    const invoicesByStatus = {
      draft: 0,
      sent: 0,
      paid: 0,
      canceled: 0
    };

    const revenueByStatus = {
      paid: 0,
      pending: 0,
      overdue: 0,
      canceled: 0
    };

    let totalInvoices = 0;
    let totalRevenue = 0;

    invoiceStats.forEach(stat => {
      totalInvoices += stat.count;

      if (stat._id === 'DRAFT') {
        invoicesByStatus.draft = stat.count;
      } else if (stat._id === 'SENT') {
        invoicesByStatus.sent = stat.count;
        revenueByStatus.pending = stat.total;
      } else if (stat._id === 'PAID') {
        invoicesByStatus.paid = stat.count;
        revenueByStatus.paid = stat.total;
        totalRevenue = stat.total;
      } else if (stat._id === 'CANCELED') {
        invoicesByStatus.canceled = stat.count;
        revenueByStatus.canceled = stat.total;
      }
    });

    // Process quote stats
    const quotesByStatus = {
      draft: 0,
      sent: 0,
      accepted: 0,
      rejected: 0
    };

    let totalQuotes = 0;

    quoteStats.forEach(stat => {
      totalQuotes += stat.count;

      if (stat._id === 'DRAFT') {
        quotesByStatus.draft = stat.count;
      } else if (stat._id === 'SENT') {
        quotesByStatus.sent = stat.count;
      } else if (stat._id === 'ACCEPTED') {
        quotesByStatus.accepted = stat.count;
      } else if (stat._id === 'REJECTED') {
        quotesByStatus.rejected = stat.count;
      }
    });

    // Calculate overdue invoices (requires a separate query)
    const overdueDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const overdueInvoices = await Facture.aggregate([
      {
        $match: {
          statut: 'SENT',
          dateEmission: { $lt: overdueDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$total" }
        }
      }
    ]);

    if (overdueInvoices.length > 0) {
      revenueByStatus.overdue = overdueInvoices[0].total;
    }

    // Prepare response data
    const dashboardData = {
      period,
      dateRange: {
        start: startDate,
        end: endDate,
      },
      revenue: {
        total: totalRevenue || 0,
        byStatus: {
          paid: revenueByStatus.paid || 0,
          pending: revenueByStatus.pending || 0,
          overdue: revenueByStatus.overdue || 0,
          canceled: revenueByStatus.canceled || 0
        },
      },
      invoices: {
        total: totalInvoices || 0,
        byStatus: {
          draft: invoicesByStatus.draft || 0,
          sent: invoicesByStatus.sent || 0,
          paid: invoicesByStatus.paid || 0,
          canceled: invoicesByStatus.canceled || 0
        },
      },
      quotes: {
        total: totalQuotes || 0,
        byStatus: {
          draft: quotesByStatus.draft || 0,
          sent: quotesByStatus.sent || 0,
          accepted: quotesByStatus.accepted || 0,
          rejected: quotesByStatus.rejected || 0
        },
      },
      clients: {
        total: totalClientsCount || 0,
        new: newClientsCount || 0,
      },
    };

    res.status(200).json(dashboardData);
  } catch (error) {
    console.error('Error fetching dashboard analytics:', error);
    res.status(500).json({
      error: 'Error fetching dashboard analytics',
      message: error.message,
      details: error.stack
    });
  }
});

// GET /analytics/vendeur-dashboard - Get vendeur dashboard stats
router.get('/analytics/vendeur-dashboard', verifyToken, async (req, res) => {
  try {
    const { dateRange = 'month', startDate, endDate } = req.query;
    const vendeurId = req.userId; // Get the current vendeur's ID
    const userRole = req.userRole;

    console.log(`Fetching vendeur dashboard for user: ${vendeurId}, role: ${userRole}, dateRange: ${dateRange}`);

    // Determine if we should use the vendeurId directly or handle based on role
    let actualVendeurId = vendeurId;

    // If the user is not a vendeur but an admin or entreprise, they might be viewing a vendeur's dashboard
    if (userRole !== 'VENDEUR' && req.query.vendeurId) {
      actualVendeurId = req.query.vendeurId;
      console.log(`Admin/Entreprise user viewing vendeur dashboard for: ${actualVendeurId}`);
    }

    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        dateEmission: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    } else {
      const { startDate: start, endDate: end } = getDateRange(dateRange === 'month' ? 'monthly' :
                                                             dateRange === 'quarter' ? 'quarterly' :
                                                             dateRange === 'year' ? 'yearly' :
                                                             dateRange === 'allTime' ? 'all-time' : 'monthly');
      dateFilter = {
        dateEmission: {
          $gte: start,
          $lte: end
        }
      };
    }

    console.log('Using date filter:', JSON.stringify(dateFilter));

    // Get invoice counts and revenue
    const invoiceStats = await Facture.aggregate([
      {
        $match: {
          ...dateFilter,
          vendeurId: new mongoose.Types.ObjectId(actualVendeurId)
        }
      },
      {
        $group: {
          _id: "$statut",
          count: { $sum: 1 },
          total: { $sum: "$total" }
        }
      }
    ]);

    // Adjust date filter for quotes (which use dateCréation instead of dateEmission)
    const quoteFilter = { ...dateFilter };
    delete quoteFilter.dateEmission;

    if (startDate && endDate) {
      quoteFilter.dateCréation = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else {
      const { startDate: start, endDate: end } = getDateRange(dateRange === 'month' ? 'monthly' :
                                                             dateRange === 'quarter' ? 'quarterly' :
                                                             dateRange === 'year' ? 'yearly' :
                                                             dateRange === 'allTime' ? 'all-time' : 'monthly');
      quoteFilter.dateCréation = {
        $gte: start,
        $lte: end
      };
    }

    console.log('Using quote filter:', JSON.stringify(quoteFilter));

    // Get quote counts
    const quoteStats = await Devis.aggregate([
      {
        $match: {
          ...quoteFilter,
          vendeurId: new mongoose.Types.ObjectId(actualVendeurId)
        }
      },
      {
        $group: {
          _id: "$statut",
          count: { $sum: 1 }
        }
      }
    ]);

    // Count clients
    const clientCount = await Client.countDocuments({
      vendeurId: new mongoose.Types.ObjectId(actualVendeurId)
    });

    // Process invoice stats
    let totalInvoices = 0;
    let paidInvoices = 0;
    let pendingInvoices = 0;
    let overdueInvoices = 0;
    let totalRevenue = 0;

    invoiceStats.forEach(stat => {
      totalInvoices += stat.count;

      if (stat._id === 'PAID') {
        paidInvoices = stat.count;
        totalRevenue = stat.total;
      } else if (stat._id === 'SENT') {
        pendingInvoices = stat.count;
      }
    });

    // Calculate overdue invoices
    const overdueDate = new Date();
    overdueDate.setDate(overdueDate.getDate() - 30);

    const overdueCount = await Facture.countDocuments({
      vendeurId: new mongoose.Types.ObjectId(vendeurId),
      statut: 'SENT',
      dateEmission: { $lt: overdueDate }
    });

    overdueInvoices = overdueCount;

    // Process quote stats
    let totalQuotes = 0;
    let acceptedQuotes = 0;

    quoteStats.forEach(stat => {
      totalQuotes += stat.count;

      if (stat._id === 'ACCEPTED') {
        acceptedQuotes = stat.count;
      }
    });

    // Get sales by month data
    // Use a wider range of years to include test data
    const currentYear = new Date().getFullYear();
    const salesByMonth = {
      invoices: Array(12).fill(0),
      quotes: Array(12).fill(0)
    };

    console.log(`Getting sales by month data for year: ${currentYear}`);

    // Get invoice data by month
    const invoicesByMonth = await Facture.aggregate([
      {
        $match: {
          vendeurId: new mongoose.Types.ObjectId(actualVendeurId),
          dateEmission: {
            $gte: new Date(1900, 0, 1),
            $lte: new Date(2100, 11, 31, 23, 59, 59)
          }
        }
      },
      {
        $group: {
          _id: { month: { $month: "$dateEmission" } },
          total: { $sum: "$total" }
        }
      }
    ]);

    console.log(`Found ${invoicesByMonth.length} invoice data points by month`);

    invoicesByMonth.forEach(item => {
      const monthIndex = item._id.month - 1;
      salesByMonth.invoices[monthIndex] = item.total;
    });

    // Get quote data by month - note we're using dateCréation for quotes
    const quotesByMonth = await Devis.aggregate([
      {
        $match: {
          vendeurId: new mongoose.Types.ObjectId(actualVendeurId),
          dateCréation: {
            $gte: new Date(1900, 0, 1),
            $lte: new Date(2100, 11, 31, 23, 59, 59)
          }
        }
      },
      {
        $group: {
          _id: { month: { $month: "$dateCréation" } },
          total: { $sum: "$total" }
        }
      }
    ]);

    console.log(`Found ${quotesByMonth.length} quote data points by month`);

    quotesByMonth.forEach(item => {
      const monthIndex = item._id.month - 1;
      salesByMonth.quotes[monthIndex] = item.total;
    });

    // Get category analysis from invoice products
    console.log('Getting category analysis...');
    const categoryAnalysis = await Facture.aggregate([
      {
        $match: {
          vendeurId: new mongoose.Types.ObjectId(actualVendeurId),
          ...dateFilter
        }
      },
      {
        $unwind: {
          path: '$lignes',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'produits',
          localField: 'lignes.produit',
          foreignField: '_id',
          as: 'produitInfo'
        }
      },
      {
        $unwind: {
          path: '$produitInfo',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: { $ifNull: ['$produitInfo.category', 'Autres'] },
          total: { $sum: { $ifNull: ['$lignes.montantHT', 0] } }
        }
      },
      {
        $sort: { total: -1 }
      },
      {
        $limit: 3
      }
    ]).catch(err => {
      console.error('Error fetching category analysis:', err);
      return [];
    });

    console.log('Category analysis results:', JSON.stringify(categoryAnalysis));

    // Format category analysis data
    // If we have less than 3 categories, fill with zeros
    const formattedCategoryAnalysis = [0, 0, 0];
    categoryAnalysis.forEach((cat, index) => {
      if (index < 3) {
        formattedCategoryAnalysis[index] = cat.total || 0;
      }
    });

    // Get client distribution - active vs inactive clients
    console.log('Getting client distribution...');

    // First, get all clients for this vendeur
    const allClients = await Client.find({
      vendeurId: new mongoose.Types.ObjectId(actualVendeurId)
    }).select('_id');

    const clientIds = allClients.map(c => c._id);
    console.log(`Found ${clientIds.length} total clients for vendeur`);

    // Then, find which clients have invoices (active)
    const activeClientIds = await Facture.distinct('clientId', {
      vendeurId: new mongoose.Types.ObjectId(actualVendeurId),
      clientId: { $in: clientIds }
    });

    console.log(`Found ${activeClientIds.length} active clients with invoices`);

    // Calculate active and inactive counts
    const activeCount = activeClientIds.length;
    const inactiveCount = clientIds.length - activeCount;

    const formattedClientDistribution = [activeCount, inactiveCount];

    // Get recent invoices
    console.log('Getting recent invoices...');
    const recentInvoices = await Facture.find({
      vendeurId: new mongoose.Types.ObjectId(actualVendeurId)
    })
      .sort({ dateEmission: -1 })
      .limit(5)
      .populate('clientId');

    console.log(`Found ${recentInvoices.length} recent invoices`);

    const formattedInvoices = recentInvoices.map(invoice => ({
      id: invoice.numero,
      date: invoice.dateEmission,
      client: invoice.clientId ? invoice.clientId.nom : 'Client inconnu',
      montant: invoice.total,
      statut: invoice.statut
    }));

    // Get recent quotes
    console.log('Getting recent quotes...');
    const recentQuotes = await Devis.find({
      vendeurId: new mongoose.Types.ObjectId(actualVendeurId)
    })
      .sort({ dateCréation: -1 }) // Use dateCréation instead of dateEmission
      .limit(5)
      .populate('clientId');

    console.log(`Found ${recentQuotes.length} recent quotes`);

    const formattedQuotes = recentQuotes.map(quote => ({
      id: quote.numéro, // Use numéro instead of numero
      date: quote.dateCréation, // Use dateCréation instead of dateEmission
      client: quote.clientId ? quote.clientId.nom : 'Client inconnu',
      montant: quote.total,
      statut: quote.statut
    }));

    // Get top clients
    console.log('Getting top clients...');
    const topClients = await Facture.aggregate([
      {
        $match: {
          vendeurId: new mongoose.Types.ObjectId(actualVendeurId),
          clientId: { $exists: true, $ne: null }
        }
      },
      {
        $lookup: {
          from: 'clients',
          localField: 'clientId',
          foreignField: '_id',
          as: 'clientInfo'
        }
      },
      {
        $unwind: {
          path: '$clientInfo',
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $group: {
          _id: '$clientId',
          nom: { $first: '$clientInfo.nom' },
          email: { $first: '$clientInfo.email' },
          montantTotal: { $sum: '$total' },
          facturesCount: { $sum: 1 }
        }
      },
      {
        $sort: { montantTotal: -1 }
      },
      {
        $limit: 5
      },
      {
        $project: {
          _id: 0,
          id: '$_id',
          nom: 1,
          email: 1,
          montantTotal: 1,
          facturesCount: 1
        }
      }
    ]);

    console.log(`Found ${topClients.length} top clients`);

    // Prepare response data
    const dashboardData = {
      factures: totalInvoices || 0,
      devis: totalQuotes || 0,
      clients: clientCount || 0,
      totalRevenue: totalRevenue || 0,
      paidInvoices: paidInvoices || 0,
      pendingInvoices: pendingInvoices || 0,
      overdueInvoices: overdueInvoices || 0,
      acceptedDevis: acceptedQuotes || 0,
      salesByMonth: {
        invoices: salesByMonth.invoices || Array(12).fill(0),
        quotes: salesByMonth.quotes || Array(12).fill(0)
      },
      categoryAnalysis: formattedCategoryAnalysis,
      clientDistribution: formattedClientDistribution,
      recentInvoices: formattedInvoices.length > 0 ? formattedInvoices : [],
      recentQuotes: formattedQuotes.length > 0 ? formattedQuotes : [],
      topClients: topClients.length > 0 ? topClients : []
    };

    res.status(200).json(dashboardData);
  } catch (error) {
    console.error('Error fetching vendeur dashboard stats:', error);
    res.status(500).json({
      error: 'Error fetching vendeur dashboard stats',
      message: error.message,
      details: error.stack
    });
  }
});

// GET /analytics/revenue - Get revenue data over time
router.get('/analytics/revenue', verifyToken, async (req, res) => {
  try {
    const { period = 'all-time' } = req.query;
    const userRole = req.userRole;
    const userId = req.userId;

    console.log(`Fetching revenue data for user: ${userId}, role: ${userRole}, period: ${period}`);

    // Determine the date range based on the period
    const now = new Date();
    const { startDate, endDate } = getDateRange(period, now);

    // Build match filter based on user role
    let matchFilter = {
      dateEmission: { $gte: startDate, $lte: endDate }
    };

    if (userRole === 'VENDEUR') {
      matchFilter.vendeurId = new mongoose.Types.ObjectId(userId);
      console.log('Filtering by vendeurId:', userId);
    } else if (userRole === 'ENTREPRISE') {
      // For ENTREPRISE users, we need to find all vendeurs associated with this enterprise
      // and include their invoices
      const User = require('../models/UserModel');
      const vendeurs = await User.find({
        role: 'VENDEUR',
        entreprises: { $in: [new mongoose.Types.ObjectId(userId)] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log(`Found ${vendeurIds.length} vendeurs for enterprise user ${userId}`);

      if (vendeurIds.length > 0) {
        matchFilter.vendeurId = { $in: vendeurIds.map(id => new mongoose.Types.ObjectId(id)) };
      } else {
        // If no vendeurs found, try to use the entrepriseId directly
        matchFilter.entrepriseId = new mongoose.Types.ObjectId(userId);
      }
    }
    // For ADMIN users, no additional filters needed - they see all data

    console.log('Using match filter:', JSON.stringify(matchFilter));

    // Determine the time interval for grouping data
    let groupByFormat;
    let sortField;

    if (period === 'daily' || period === 'weekly') {
      groupByFormat = { year: { $year: "$dateEmission" }, month: { $month: "$dateEmission" }, day: { $dayOfMonth: "$dateEmission" } };
      sortField = { year: 1, month: 1, day: 1 };
    } else if (period === 'monthly' || period === 'quarterly') {
      groupByFormat = { year: { $year: "$dateEmission" }, month: { $month: "$dateEmission" } };
      sortField = { year: 1, month: 1 };
    } else {
      groupByFormat = { year: { $year: "$dateEmission" } };
      sortField = { year: 1 };
    }

    // Get revenue data grouped by the appropriate time interval
    const revenueData = await Facture.aggregate([
      {
        $match: matchFilter
      },
      {
        $group: {
          _id: groupByFormat,
          revenue: { $sum: "$total" },
          count: { $sum: 1 }
        }
      },
      {
        $sort: sortField
      }
    ]);

    console.log(`Found ${revenueData.length} revenue data points`);

    // Format the data for the response
    const formattedData = revenueData.map(item => {
      let date;

      if (period === 'daily' || period === 'weekly') {
        date = `${item._id.year}-${String(item._id.month).padStart(2, '0')}-${String(item._id.day).padStart(2, '0')}`;
      } else if (period === 'monthly' || period === 'quarterly') {
        date = `${item._id.year}-${String(item._id.month).padStart(2, '0')}`;
      } else {
        date = `${item._id.year}`;
      }

      return {
        date,
        revenue: item.revenue,
        count: item.count
      };
    });

    res.status(200).json(formattedData);
  } catch (error) {
    console.error('Error fetching revenue data:', error);
    res.status(500).json({
      error: 'Error fetching revenue data',
      message: error.message
    });
  }
});

// GET /analytics/top-clients - Get top clients by revenue
router.get('/analytics/top-clients', verifyToken, async (req, res) => {
  try {
    const { limit = 10, period = 'all-time' } = req.query;
    const userRole = req.userRole;
    const userId = req.userId;

    console.log(`Fetching top clients for user: ${userId}, role: ${userRole}, period: ${period}, limit: ${limit}`);

    // Get date range for the specified period
    const { startDate, endDate } = getDateRange(period);

    // Build match filter based on user role
    let matchFilter = {
      dateEmission: { $gte: startDate, $lte: endDate },
      clientId: { $exists: true, $ne: null }
    };

    if (userRole === 'VENDEUR') {
      matchFilter.vendeurId = new mongoose.Types.ObjectId(userId);
      console.log('Filtering by vendeurId:', userId);
    } else if (userRole === 'ENTREPRISE') {
      // For ENTREPRISE users, we need to find all vendeurs associated with this enterprise
      // and include their invoices
      const User = require('../models/UserModel');
      const vendeurs = await User.find({
        role: 'VENDEUR',
        entreprises: { $in: [new mongoose.Types.ObjectId(userId)] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log(`Found ${vendeurIds.length} vendeurs for enterprise user ${userId}`);

      if (vendeurIds.length > 0) {
        matchFilter.vendeurId = { $in: vendeurIds.map(id => new mongoose.Types.ObjectId(id)) };
      } else {
        // If no vendeurs found, try to use the entrepriseId directly
        matchFilter.entrepriseId = new mongoose.Types.ObjectId(userId);
      }
    }
    // For ADMIN users, no additional filters needed - they see all data

    console.log('Using match filter:', JSON.stringify(matchFilter));

    // Get top clients by revenue
    const topClients = await Facture.aggregate([
      {
        $match: matchFilter
      },
      {
        $lookup: {
          from: 'clients',
          localField: 'clientId',
          foreignField: '_id',
          as: 'clientInfo'
        }
      },
      {
        $unwind: {
          path: '$clientInfo',
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $group: {
          _id: '$clientId',
          name: { $first: '$clientInfo.nom' },
          email: { $first: '$clientInfo.email' },
          totalRevenue: { $sum: '$total' },
          invoiceCount: { $sum: 1 }
        }
      },
      {
        $sort: { totalRevenue: -1 }
      },
      {
        $limit: parseInt(limit)
      }
    ]);

    console.log(`Found ${topClients.length} top clients`);

    res.status(200).json(topClients);
  } catch (error) {
    console.error('Error fetching top clients:', error);
    res.status(500).json({
      error: 'Error fetching top clients',
      message: error.message
    });
  }
});

// GET /analytics/recent-invoices - Get recent invoices (DÉSACTIVÉ)
router.get('/analytics/recent-invoices', verifyToken, async (req, res) => {
  // Cette route a été désactivée
  res.status(200).json([]);
});

// GET /analytics/top-products - Get top products by sales (DÉSACTIVÉ)
router.get('/analytics/top-products', verifyToken, async (req, res) => {
  // Cette route a été désactivée
  res.status(200).json([]);
});

// GET /analytics/payment-status - Get payment status distribution
router.get('/analytics/payment-status', verifyToken, async (req, res) => {
  try {
    const { period = 'all-time' } = req.query;
    const userRole = req.userRole;
    const userId = req.userId;

    console.log(`Fetching payment status for user: ${userId}, role: ${userRole}, period: ${period}`);

    // Get date range for the specified period
    const { startDate, endDate } = getDateRange(period);

    // Build match filter based on user role
    let matchFilter = {
      dateEmission: { $gte: startDate, $lte: endDate }
    };

    if (userRole === 'VENDEUR') {
      matchFilter.vendeurId = new mongoose.Types.ObjectId(userId);
      console.log('Filtering by vendeurId:', userId);
    } else if (userRole === 'ENTREPRISE') {
      // For ENTREPRISE users, we need to find all vendeurs associated with this enterprise
      // and include their invoices
      const User = require('../models/UserModel');
      const vendeurs = await User.find({
        role: 'VENDEUR',
        entreprises: { $in: [new mongoose.Types.ObjectId(userId)] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log(`Found ${vendeurIds.length} vendeurs for enterprise user ${userId}`);

      if (vendeurIds.length > 0) {
        matchFilter.vendeurId = { $in: vendeurIds.map(id => new mongoose.Types.ObjectId(id)) };
      } else {
        // If no vendeurs found, try to use the entrepriseId directly
        matchFilter.entrepriseId = new mongoose.Types.ObjectId(userId);
      }
    }
    // For ADMIN users, no additional filters needed - they see all data

    console.log('Using match filter:', JSON.stringify(matchFilter));

    // Get payment status distribution
    const paymentStatus = await Facture.aggregate([
      {
        $match: matchFilter
      },
      {
        $group: {
          _id: '$statut',
          count: { $sum: 1 },
          total: { $sum: '$total' }
        }
      }
    ]);

    console.log(`Found ${paymentStatus.length} payment status groups`);

    // Calculate overdue invoices
    const overdueDate = new Date();
    overdueDate.setDate(overdueDate.getDate() - 30); // Invoices older than 30 days are considered overdue

    // Create a copy of the match filter for overdue invoices
    const overdueMatchFilter = { ...matchFilter };
    overdueMatchFilter.statut = 'SENT';
    overdueMatchFilter.dateEmission.$lt = overdueDate;

    console.log('Using overdue match filter:', JSON.stringify(overdueMatchFilter));

    const overdueInvoices = await Facture.aggregate([
      {
        $match: overdueMatchFilter
      },
      {
        $group: {
          _id: 'OVERDUE',
          count: { $sum: 1 },
          total: { $sum: '$total' }
        }
      }
    ]);

    console.log(`Found ${overdueInvoices.length > 0 ? overdueInvoices[0].count : 0} overdue invoices`);

    // Format the data for the response
    const formattedData = paymentStatus.map(status => ({
      status: status._id,
      count: status.count,
      total: status.total
    }));

    // Add overdue invoices to the response
    if (overdueInvoices.length > 0) {
      formattedData.push({
        status: 'OVERDUE',
        count: overdueInvoices[0].count,
        total: overdueInvoices[0].total
      });
    }

    res.status(200).json(formattedData);
  } catch (error) {
    console.error('Error fetching payment status:', error);
    res.status(500).json({
      error: 'Error fetching payment status',
      message: error.message
    });
  }
});

// GET /analytics/kpi - Get key performance indicators
router.get('/analytics/kpi', verifyToken, async (req, res) => {
  try {
    // Calculate KPIs for different time periods
    const currentMonth = new Date();
    const previousMonth = new Date();
    previousMonth.setMonth(previousMonth.getMonth() - 1);

    const currentYear = new Date();
    const previousYear = new Date();
    previousYear.setFullYear(previousYear.getFullYear() - 1);

    // Get date ranges
    const currentMonthRange = getDateRange('monthly', currentMonth);
    const previousMonthRange = getDateRange('monthly', previousMonth);
    const currentYearRange = getDateRange('yearly', currentYear);
    const previousYearRange = getDateRange('yearly', previousYear);

    // Get current month revenue
    const currentMonthRevenue = await Facture.aggregate([
      {
        $match: {
          dateEmission: { $gte: currentMonthRange.startDate, $lte: currentMonthRange.endDate },
          statut: 'PAID'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$total' }
        }
      }
    ]);

    // Get previous month revenue
    const previousMonthRevenue = await Facture.aggregate([
      {
        $match: {
          dateEmission: { $gte: previousMonthRange.startDate, $lte: previousMonthRange.endDate },
          statut: 'PAID'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$total' }
        }
      }
    ]);

    // Get current year revenue
    const currentYearRevenue = await Facture.aggregate([
      {
        $match: {
          dateEmission: { $gte: currentYearRange.startDate, $lte: currentYearRange.endDate },
          statut: 'PAID'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$total' }
        }
      }
    ]);

    // Get previous year revenue
    const previousYearRevenue = await Facture.aggregate([
      {
        $match: {
          dateEmission: { $gte: previousYearRange.startDate, $lte: previousYearRange.endDate },
          statut: 'PAID'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$total' }
        }
      }
    ]);

    // Get invoice counts
    const currentMonthInvoices = await Facture.countDocuments({
      dateEmission: { $gte: currentMonthRange.startDate, $lte: currentMonthRange.endDate }
    });

    const previousMonthInvoices = await Facture.countDocuments({
      dateEmission: { $gte: previousMonthRange.startDate, $lte: previousMonthRange.endDate }
    });

    // Get client counts
    const currentMonthClients = await Client.countDocuments({
      createdAt: { $gte: currentMonthRange.startDate, $lte: currentMonthRange.endDate }
    });

    const previousMonthClients = await Client.countDocuments({
      createdAt: { $gte: previousMonthRange.startDate, $lte: previousMonthRange.endDate }
    });

    // Calculate growth rates
    const revenueGrowthMonth = previousMonthRevenue.length > 0 && previousMonthRevenue[0].total > 0
      ? ((currentMonthRevenue.length > 0 ? currentMonthRevenue[0].total : 0) - previousMonthRevenue[0].total) / previousMonthRevenue[0].total * 100
      : 0;

    const revenueGrowthYear = previousYearRevenue.length > 0 && previousYearRevenue[0].total > 0
      ? ((currentYearRevenue.length > 0 ? currentYearRevenue[0].total : 0) - previousYearRevenue[0].total) / previousYearRevenue[0].total * 100
      : 0;

    const invoiceGrowth = previousMonthInvoices > 0
      ? (currentMonthInvoices - previousMonthInvoices) / previousMonthInvoices * 100
      : 0;

    const clientGrowth = previousMonthClients > 0
      ? (currentMonthClients - previousMonthClients) / previousMonthClients * 100
      : 0;

    // Prepare response data
    const kpiData = {
      revenue: {
        currentMonth: currentMonthRevenue.length > 0 ? currentMonthRevenue[0].total : 0,
        previousMonth: previousMonthRevenue.length > 0 ? previousMonthRevenue[0].total : 0,
        currentYear: currentYearRevenue.length > 0 ? currentYearRevenue[0].total : 0,
        previousYear: previousYearRevenue.length > 0 ? previousYearRevenue[0].total : 0,
        growthMonth: revenueGrowthMonth,
        growthYear: revenueGrowthYear
      },
      invoices: {
        currentMonth: currentMonthInvoices,
        previousMonth: previousMonthInvoices,
        growth: invoiceGrowth
      },
      clients: {
        currentMonth: currentMonthClients,
        previousMonth: previousMonthClients,
        growth: clientGrowth
      }
    };

    res.status(200).json(kpiData);
  } catch (error) {
    console.error('Error fetching KPI data:', error);
    res.status(500).json({
      error: 'Error fetching KPI data',
      message: error.message
    });
  }
});

module.exports = router;
