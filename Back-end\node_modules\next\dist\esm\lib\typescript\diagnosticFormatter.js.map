{"version": 3, "sources": ["../../../src/lib/typescript/diagnosticFormatter.ts"], "sourcesContent": ["import { bold, cyan, red, yellow } from '../picocolors'\nimport path from 'path'\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\nexport enum DiagnosticCategory {\n  Warning = 0,\n  Error = 1,\n  Suggestion = 2,\n  Message = 3,\n}\n\nfunction getFormattedLinkDiagnosticMessageText(\n  diagnostic: import('typescript').Diagnostic\n) {\n  const message = diagnostic.messageText\n  if (typeof message === 'string' && diagnostic.code === 2322) {\n    const match =\n      message.match(\n        /Type '\"(.+)\"' is not assignable to type 'RouteImpl<.+> \\| UrlObject'\\./\n      ) ||\n      message.match(\n        /Type '\"(.+)\"' is not assignable to type 'UrlObject \\| RouteImpl<.+>'\\./\n      )\n\n    if (match) {\n      const [, href] = match\n      return `\"${bold(\n        href\n      )}\" is not an existing route. If it is intentional, please type it explicitly with \\`as Route\\`.`\n    } else if (\n      message === \"Type 'string' is not assignable to type 'UrlObject'.\"\n    ) {\n      const relatedMessage = diagnostic.relatedInformation?.[0]?.messageText\n      if (\n        typeof relatedMessage === 'string' &&\n        relatedMessage.match(\n          /The expected type comes from property 'href' which is declared here on type 'IntrinsicAttributes & /\n        )\n      ) {\n        return `Invalid \\`href\\` property of \\`Link\\`: the route does not exist. If it is intentional, please type it explicitly with \\`as Route\\`.`\n      }\n    }\n  } else if (typeof message === 'string' && diagnostic.code === 2820) {\n    const match =\n      message.match(\n        /Type '\"(.+)\"' is not assignable to type 'RouteImpl<.+> \\| UrlObject'\\. Did you mean '\"(.+)\"'?/\n      ) ||\n      message.match(\n        /Type '\"(.+)\"' is not assignable to type 'UrlObject \\| RouteImpl<.+>'\\. Did you mean '\"(.+)\"'?/\n      )\n\n    if (match) {\n      const [, href, suggestion] = match\n      return `\"${bold(href)}\" is not an existing route. Did you mean \"${bold(\n        suggestion\n      )}\" instead? If it is intentional, please type it explicitly with \\`as Route\\`.`\n    }\n  }\n}\n\nfunction getFormattedLayoutAndPageDiagnosticMessageText(\n  relativeSourceFilepath: string,\n  diagnostic: import('typescript').Diagnostic\n) {\n  const message =\n    typeof diagnostic.messageText === 'string'\n      ? diagnostic\n      : diagnostic.messageText\n  const messageText = message.messageText\n\n  if (typeof messageText === 'string') {\n    const type = /page\\.[^.]+$/.test(relativeSourceFilepath)\n      ? 'Page'\n      : /route\\.[^.]+$/.test(relativeSourceFilepath)\n        ? 'Route'\n        : 'Layout'\n\n    // Reference of error codes:\n    // https://github.com/Microsoft/TypeScript/blob/main/src/compiler/diagnosticMessages.json\n    switch (message.code) {\n      case 2344:\n        const filepathAndType = messageText.match(/typeof import\\(\"(.+)\"\\)/)\n        if (filepathAndType) {\n          let main = `${type} \"${bold(\n            relativeSourceFilepath\n          )}\" does not match the required types of a Next.js ${type}.`\n\n          function processNext(\n            indent: number,\n            next?: import('typescript').DiagnosticMessageChain[]\n          ) {\n            if (!next) return\n\n            for (const item of next) {\n              switch (item.code) {\n                case 2200:\n                  const mismatchedField =\n                    item.messageText.match(/The types of '(.+)'/)\n                  if (mismatchedField) {\n                    main += '\\n' + ' '.repeat(indent * 2)\n                    main += `\"${bold(mismatchedField[1])}\" has the wrong type:`\n                  }\n                  break\n                case 2322:\n                  const types = item.messageText.match(\n                    /Type '(.+)' is not assignable to type '(.+)'./\n                  )\n                  if (types) {\n                    main += '\\n' + ' '.repeat(indent * 2)\n\n                    if (\n                      types[2] === 'PageComponent' ||\n                      types[2] === 'LayoutComponent'\n                    ) {\n                      main += `The exported ${type} component isn't correctly typed.`\n                    } else {\n                      main += `Expected \"${bold(\n                        types[2].replace(\n                          '\"__invalid_negative_number__\"',\n                          'number (>= 0)'\n                        )\n                      )}\", got \"${bold(types[1])}\".`\n                    }\n                  }\n                  break\n                case 2326:\n                  const invalidConfig = item.messageText.match(\n                    /Types of property '(.+)' are incompatible\\./\n                  )\n                  main += '\\n' + ' '.repeat(indent * 2)\n                  main += `Invalid configuration${\n                    invalidConfig ? ` \"${bold(invalidConfig[1])}\"` : ''\n                  }:`\n                  break\n                case 2530:\n                  const invalidField = item.messageText.match(\n                    /Property '(.+)' is incompatible with index signature/\n                  )\n                  if (invalidField) {\n                    main += '\\n' + ' '.repeat(indent * 2)\n                    main += `\"${bold(\n                      invalidField[1]\n                    )}\" is not a valid ${type} export field.`\n                  }\n                  return\n                case 2739:\n                  const invalidProp = item.messageText.match(\n                    /Type '(.+)' is missing the following properties from type '(.+)'/\n                  )\n                  if (invalidProp) {\n                    if (\n                      invalidProp[1] === 'LayoutProps' ||\n                      invalidProp[1] === 'PageProps'\n                    ) {\n                      main += '\\n' + ' '.repeat(indent * 2)\n                      main += `Prop \"${invalidProp[2]}\" is incompatible with the ${type}.`\n                    }\n                  }\n                  break\n                case 2559:\n                  const invalid = item.messageText.match(/Type '(.+)' has/)\n                  if (invalid) {\n                    main += '\\n' + ' '.repeat(indent * 2)\n                    main += `Type \"${bold(invalid[1])}\" isn't allowed.`\n                  }\n                  break\n                case 2741:\n                  const incompatPageProp = item.messageText.match(\n                    /Property '(.+)' is missing in type 'PageProps'/\n                  )\n                  if (incompatPageProp) {\n                    main += '\\n' + ' '.repeat(indent * 2)\n                    main += `Prop \"${bold(\n                      incompatPageProp[1]\n                    )}\" will never be passed. Remove it from the component's props.`\n                  } else {\n                    const extraLayoutProp = item.messageText.match(\n                      /Property '(.+)' is missing in type 'LayoutProps' but required in type '(.+)'/\n                    )\n                    if (extraLayoutProp) {\n                      main += '\\n' + ' '.repeat(indent * 2)\n                      main += `Prop \"${bold(\n                        extraLayoutProp[1]\n                      )}\" is not valid for this Layout, remove it to fix.`\n                    }\n                  }\n                  break\n                default:\n              }\n\n              processNext(indent + 1, item.next)\n            }\n          }\n\n          if ('next' in message) processNext(1, message.next)\n          return main\n        }\n\n        const invalidExportFnArg = messageText.match(\n          /Type 'OmitWithTag<(.+), .+, \"(.+)\">' does not satisfy the constraint/\n        )\n        if (invalidExportFnArg) {\n          const main = `${type} \"${bold(\n            relativeSourceFilepath\n          )}\" has an invalid \"${bold(\n            invalidExportFnArg[2]\n          )}\" export:\\n  Type \"${bold(invalidExportFnArg[1])}\" is not valid.`\n          return main\n        }\n\n        function processNextItems(\n          indent: number,\n          next?: import('typescript').DiagnosticMessageChain[]\n        ) {\n          if (!next) return ''\n\n          let result = ''\n\n          for (const item of next) {\n            switch (item.code) {\n              case 2322:\n                const types = item.messageText.match(\n                  /Type '(.+)' is not assignable to type '(.+)'./\n                )\n                if (types) {\n                  result += '\\n' + ' '.repeat(indent * 2)\n                  result += `Expected \"${bold(types[2])}\", got \"${bold(\n                    types[1]\n                  )}\".`\n                }\n                break\n              default:\n            }\n\n            result += processNextItems(indent + 1, item.next)\n          }\n\n          return result\n        }\n\n        const invalidParamFn = messageText.match(\n          /Type '{ __tag__: (.+); __param_position__: \"(.*)\"; __param_type__: (.+); }' does not satisfy/\n        )\n        if (invalidParamFn) {\n          let main = `${type} \"${bold(\n            relativeSourceFilepath\n          )}\" has an invalid ${invalidParamFn[1]} export:\\n  Type \"${bold(\n            invalidParamFn[3]\n          )}\" is not a valid type for the function's ${\n            invalidParamFn[2]\n          } argument.`\n\n          if ('next' in message) main += processNextItems(1, message.next)\n          return main\n        }\n\n        const invalidExportFnReturn = messageText.match(\n          /Type '{ __tag__: \"(.+)\"; __return_type__: (.+); }' does not satisfy/\n        )\n        if (invalidExportFnReturn) {\n          let main = `${type} \"${bold(\n            relativeSourceFilepath\n          )}\" has an invalid export:\\n  \"${bold(\n            invalidExportFnReturn[2]\n          )}\" is not a valid ${invalidExportFnReturn[1]} return type:`\n\n          if ('next' in message) main += processNextItems(1, message.next)\n          return main\n        }\n\n        break\n      case 2345:\n        const filepathAndInvalidExport = messageText.match(\n          /'typeof import\\(\"(.+)\"\\)'.+Impossible<\"(.+)\">/\n        )\n        if (filepathAndInvalidExport) {\n          const main = `${type} \"${bold(\n            relativeSourceFilepath\n          )}\" exports an invalid \"${bold(\n            filepathAndInvalidExport[2]\n          )}\" field. ${type} should only export a default React component and configuration options. Learn more: https://nextjs.org/docs/messages/invalid-segment-export`\n          return main\n        }\n        break\n      case 2559:\n        const invalid = messageText.match(\n          /Type '(.+)' has no properties in common with type '(.+)'/\n        )\n        if (invalid) {\n          const main = `${type} \"${bold(\n            relativeSourceFilepath\n          )}\" contains an invalid type \"${bold(invalid[1])}\" as ${invalid[2]}.`\n          return main\n        }\n        break\n      default:\n    }\n  }\n}\n\nfunction getAppEntrySourceFilePath(\n  baseDir: string,\n  diagnostic: import('typescript').Diagnostic\n) {\n  const sourceFilepath =\n    diagnostic.file?.text.trim().match(/^\\/\\/ File: (.+)\\n/)?.[1] || ''\n\n  return path.relative(baseDir, sourceFilepath)\n}\n\nexport function getFormattedDiagnostic(\n  ts: typeof import('typescript'),\n  baseDir: string,\n  distDir: string,\n  diagnostic: import('typescript').Diagnostic,\n  isAppDirEnabled?: boolean\n): string {\n  // If the error comes from .next/types/, we handle it specially.\n  const isLayoutOrPageError =\n    isAppDirEnabled &&\n    diagnostic.file?.fileName.startsWith(path.join(baseDir, distDir, 'types'))\n\n  let message = ''\n\n  const appPath = isLayoutOrPageError\n    ? getAppEntrySourceFilePath(baseDir, diagnostic)\n    : null\n  const linkReason = getFormattedLinkDiagnosticMessageText(diagnostic)\n  const appReason =\n    !linkReason && isLayoutOrPageError && appPath\n      ? getFormattedLayoutAndPageDiagnosticMessageText(appPath, diagnostic)\n      : null\n\n  const reason =\n    linkReason ||\n    appReason ||\n    ts.flattenDiagnosticMessageText(diagnostic.messageText, '\\n')\n  const category = diagnostic.category\n  switch (category) {\n    // Warning\n    case DiagnosticCategory.Warning: {\n      message += yellow(bold('Type warning')) + ': '\n      break\n    }\n    // Error\n    case DiagnosticCategory.Error: {\n      message += red(bold('Type error')) + ': '\n      break\n    }\n    // 2 = Suggestion, 3 = Message\n    case DiagnosticCategory.Suggestion:\n    case DiagnosticCategory.Message:\n    default: {\n      message += cyan(bold(category === 2 ? 'Suggestion' : 'Info')) + ': '\n      break\n    }\n  }\n\n  message += reason + '\\n'\n\n  if (!isLayoutOrPageError && diagnostic.file) {\n    const { codeFrameColumns } = require('next/dist/compiled/babel/code-frame')\n    const pos = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start!)\n    const line = pos.line + 1\n    const character = pos.character + 1\n\n    let fileName = path.posix.normalize(\n      path.relative(baseDir, diagnostic.file.fileName).replace(/\\\\/g, '/')\n    )\n    if (!fileName.startsWith('.')) {\n      fileName = './' + fileName\n    }\n\n    message =\n      cyan(fileName) +\n      ':' +\n      yellow(line.toString()) +\n      ':' +\n      yellow(character.toString()) +\n      '\\n' +\n      message\n\n    message +=\n      '\\n' +\n      codeFrameColumns(\n        diagnostic.file.getFullText(diagnostic.file.getSourceFile()),\n        {\n          start: { line: line, column: character },\n        },\n        { forceColor: true }\n      )\n  } else if (isLayoutOrPageError && appPath) {\n    message = cyan(appPath) + '\\n' + message\n  }\n\n  return message\n}\n"], "names": ["bold", "cyan", "red", "yellow", "path", "DiagnosticCategory", "getFormattedLinkDiagnosticMessageText", "diagnostic", "message", "messageText", "code", "match", "href", "relatedMessage", "relatedInformation", "suggestion", "getFormattedLayoutAndPageDiagnosticMessageText", "relativeSourceFilepath", "type", "test", "filepathAndType", "main", "processNext", "indent", "next", "item", "mismatchedField", "repeat", "types", "replace", "invalidConfig", "<PERSON><PERSON><PERSON>", "invalidProp", "invalid", "incompatPageProp", "extraLayoutProp", "invalidExportFnArg", "processNextItems", "result", "invalidParamFn", "invalidExportFnReturn", "filepathAndInvalidExport", "getAppEntrySourceFilePath", "baseDir", "sourceFilepath", "file", "text", "trim", "relative", "getFormattedDiagnostic", "ts", "distDir", "isAppDirEnabled", "isLayoutOrPageError", "fileName", "startsWith", "join", "appPath", "linkReason", "appReason", "reason", "flattenDiagnosticMessageText", "category", "codeFrameColumns", "require", "pos", "getLineAndCharacterOfPosition", "start", "line", "character", "posix", "normalize", "toString", "getFullText", "getSourceFile", "column", "forceColor"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,gBAAe;AACvD,OAAOC,UAAU,OAAM;AAEvB,4CAA4C;AAC5C,4BAA4B,GAC5B,OAAO,IAAA,AAAKC,4CAAAA;;;;;WAAAA;MAKX;AAED,SAASC,sCACPC,UAA2C;IAE3C,MAAMC,UAAUD,WAAWE,WAAW;IACtC,IAAI,OAAOD,YAAY,YAAYD,WAAWG,IAAI,KAAK,MAAM;QAC3D,MAAMC,QACJH,QAAQG,KAAK,CACX,6EAEFH,QAAQG,KAAK,CACX;QAGJ,IAAIA,OAAO;YACT,MAAM,GAAGC,KAAK,GAAGD;YACjB,OAAO,CAAC,CAAC,EAAEX,KACTY,MACA,8FAA8F,CAAC;QACnG,OAAO,IACLJ,YAAY,wDACZ;gBACuBD,iCAAAA;YAAvB,MAAMM,kBAAiBN,iCAAAA,WAAWO,kBAAkB,sBAA7BP,kCAAAA,8BAA+B,CAAC,EAAE,qBAAlCA,gCAAoCE,WAAW;YACtE,IACE,OAAOI,mBAAmB,YAC1BA,eAAeF,KAAK,CAClB,wGAEF;gBACA,OAAO,CAAC,mIAAmI,CAAC;YAC9I;QACF;IACF,OAAO,IAAI,OAAOH,YAAY,YAAYD,WAAWG,IAAI,KAAK,MAAM;QAClE,MAAMC,QACJH,QAAQG,KAAK,CACX,oGAEFH,QAAQG,KAAK,CACX;QAGJ,IAAIA,OAAO;YACT,MAAM,GAAGC,MAAMG,WAAW,GAAGJ;YAC7B,OAAO,CAAC,CAAC,EAAEX,KAAKY,MAAM,0CAA0C,EAAEZ,KAChEe,YACA,6EAA6E,CAAC;QAClF;IACF;AACF;AAEA,SAASC,+CACPC,sBAA8B,EAC9BV,UAA2C;IAE3C,MAAMC,UACJ,OAAOD,WAAWE,WAAW,KAAK,WAC9BF,aACAA,WAAWE,WAAW;IAC5B,MAAMA,cAAcD,QAAQC,WAAW;IAEvC,IAAI,OAAOA,gBAAgB,UAAU;QACnC,MAAMS,OAAO,eAAeC,IAAI,CAACF,0BAC7B,SACA,gBAAgBE,IAAI,CAACF,0BACnB,UACA;QAEN,4BAA4B;QAC5B,yFAAyF;QACzF,OAAQT,QAAQE,IAAI;YAClB,KAAK;gBACH,MAAMU,kBAAkBX,YAAYE,KAAK,CAAC;gBAC1C,IAAIS,iBAAiB;oBACnB,IAAIC,OAAO,GAAGH,KAAK,EAAE,EAAElB,KACrBiB,wBACA,iDAAiD,EAAEC,KAAK,CAAC,CAAC;oBAE5D,SAASI,YACPC,MAAc,EACdC,IAAoD;wBAEpD,IAAI,CAACA,MAAM;wBAEX,KAAK,MAAMC,QAAQD,KAAM;4BACvB,OAAQC,KAAKf,IAAI;gCACf,KAAK;oCACH,MAAMgB,kBACJD,KAAKhB,WAAW,CAACE,KAAK,CAAC;oCACzB,IAAIe,iBAAiB;wCACnBL,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,CAAC,EAAErB,KAAK0B,eAAe,CAAC,EAAE,EAAE,qBAAqB,CAAC;oCAC7D;oCACA;gCACF,KAAK;oCACH,MAAME,QAAQH,KAAKhB,WAAW,CAACE,KAAK,CAClC;oCAEF,IAAIiB,OAAO;wCACTP,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCAEnC,IACEK,KAAK,CAAC,EAAE,KAAK,mBACbA,KAAK,CAAC,EAAE,KAAK,mBACb;4CACAP,QAAQ,CAAC,aAAa,EAAEH,KAAK,iCAAiC,CAAC;wCACjE,OAAO;4CACLG,QAAQ,CAAC,UAAU,EAAErB,KACnB4B,KAAK,CAAC,EAAE,CAACC,OAAO,CACd,iCACA,kBAEF,QAAQ,EAAE7B,KAAK4B,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;wCAChC;oCACF;oCACA;gCACF,KAAK;oCACH,MAAME,gBAAgBL,KAAKhB,WAAW,CAACE,KAAK,CAC1C;oCAEFU,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;oCACnCF,QAAQ,CAAC,qBAAqB,EAC5BS,gBAAgB,CAAC,EAAE,EAAE9B,KAAK8B,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAClD,CAAC,CAAC;oCACH;gCACF,KAAK;oCACH,MAAMC,eAAeN,KAAKhB,WAAW,CAACE,KAAK,CACzC;oCAEF,IAAIoB,cAAc;wCAChBV,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,CAAC,EAAErB,KACV+B,YAAY,CAAC,EAAE,EACf,iBAAiB,EAAEb,KAAK,cAAc,CAAC;oCAC3C;oCACA;gCACF,KAAK;oCACH,MAAMc,cAAcP,KAAKhB,WAAW,CAACE,KAAK,CACxC;oCAEF,IAAIqB,aAAa;wCACf,IACEA,WAAW,CAAC,EAAE,KAAK,iBACnBA,WAAW,CAAC,EAAE,KAAK,aACnB;4CACAX,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;4CACnCF,QAAQ,CAAC,MAAM,EAAEW,WAAW,CAAC,EAAE,CAAC,2BAA2B,EAAEd,KAAK,CAAC,CAAC;wCACtE;oCACF;oCACA;gCACF,KAAK;oCACH,MAAMe,UAAUR,KAAKhB,WAAW,CAACE,KAAK,CAAC;oCACvC,IAAIsB,SAAS;wCACXZ,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,MAAM,EAAErB,KAAKiC,OAAO,CAAC,EAAE,EAAE,gBAAgB,CAAC;oCACrD;oCACA;gCACF,KAAK;oCACH,MAAMC,mBAAmBT,KAAKhB,WAAW,CAACE,KAAK,CAC7C;oCAEF,IAAIuB,kBAAkB;wCACpBb,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,MAAM,EAAErB,KACfkC,gBAAgB,CAAC,EAAE,EACnB,6DAA6D,CAAC;oCAClE,OAAO;wCACL,MAAMC,kBAAkBV,KAAKhB,WAAW,CAACE,KAAK,CAC5C;wCAEF,IAAIwB,iBAAiB;4CACnBd,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;4CACnCF,QAAQ,CAAC,MAAM,EAAErB,KACfmC,eAAe,CAAC,EAAE,EAClB,iDAAiD,CAAC;wCACtD;oCACF;oCACA;gCACF;4BACF;4BAEAb,YAAYC,SAAS,GAAGE,KAAKD,IAAI;wBACnC;oBACF;oBAEA,IAAI,UAAUhB,SAASc,YAAY,GAAGd,QAAQgB,IAAI;oBAClD,OAAOH;gBACT;gBAEA,MAAMe,qBAAqB3B,YAAYE,KAAK,CAC1C;gBAEF,IAAIyB,oBAAoB;oBACtB,MAAMf,OAAO,GAAGH,KAAK,EAAE,EAAElB,KACvBiB,wBACA,kBAAkB,EAAEjB,KACpBoC,kBAAkB,CAAC,EAAE,EACrB,mBAAmB,EAAEpC,KAAKoC,kBAAkB,CAAC,EAAE,EAAE,eAAe,CAAC;oBACnE,OAAOf;gBACT;gBAEA,SAASgB,iBACPd,MAAc,EACdC,IAAoD;oBAEpD,IAAI,CAACA,MAAM,OAAO;oBAElB,IAAIc,SAAS;oBAEb,KAAK,MAAMb,QAAQD,KAAM;wBACvB,OAAQC,KAAKf,IAAI;4BACf,KAAK;gCACH,MAAMkB,QAAQH,KAAKhB,WAAW,CAACE,KAAK,CAClC;gCAEF,IAAIiB,OAAO;oCACTU,UAAU,OAAO,IAAIX,MAAM,CAACJ,SAAS;oCACrCe,UAAU,CAAC,UAAU,EAAEtC,KAAK4B,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE5B,KAC9C4B,KAAK,CAAC,EAAE,EACR,EAAE,CAAC;gCACP;gCACA;4BACF;wBACF;wBAEAU,UAAUD,iBAAiBd,SAAS,GAAGE,KAAKD,IAAI;oBAClD;oBAEA,OAAOc;gBACT;gBAEA,MAAMC,iBAAiB9B,YAAYE,KAAK,CACtC;gBAEF,IAAI4B,gBAAgB;oBAClB,IAAIlB,OAAO,GAAGH,KAAK,EAAE,EAAElB,KACrBiB,wBACA,iBAAiB,EAAEsB,cAAc,CAAC,EAAE,CAAC,kBAAkB,EAAEvC,KACzDuC,cAAc,CAAC,EAAE,EACjB,yCAAyC,EACzCA,cAAc,CAAC,EAAE,CAClB,UAAU,CAAC;oBAEZ,IAAI,UAAU/B,SAASa,QAAQgB,iBAAiB,GAAG7B,QAAQgB,IAAI;oBAC/D,OAAOH;gBACT;gBAEA,MAAMmB,wBAAwB/B,YAAYE,KAAK,CAC7C;gBAEF,IAAI6B,uBAAuB;oBACzB,IAAInB,OAAO,GAAGH,KAAK,EAAE,EAAElB,KACrBiB,wBACA,6BAA6B,EAAEjB,KAC/BwC,qBAAqB,CAAC,EAAE,EACxB,iBAAiB,EAAEA,qBAAqB,CAAC,EAAE,CAAC,aAAa,CAAC;oBAE5D,IAAI,UAAUhC,SAASa,QAAQgB,iBAAiB,GAAG7B,QAAQgB,IAAI;oBAC/D,OAAOH;gBACT;gBAEA;YACF,KAAK;gBACH,MAAMoB,2BAA2BhC,YAAYE,KAAK,CAChD;gBAEF,IAAI8B,0BAA0B;oBAC5B,MAAMpB,OAAO,GAAGH,KAAK,EAAE,EAAElB,KACvBiB,wBACA,sBAAsB,EAAEjB,KACxByC,wBAAwB,CAAC,EAAE,EAC3B,SAAS,EAAEvB,KAAK,4IAA4I,CAAC;oBAC/J,OAAOG;gBACT;gBACA;YACF,KAAK;gBACH,MAAMY,UAAUxB,YAAYE,KAAK,CAC/B;gBAEF,IAAIsB,SAAS;oBACX,MAAMZ,OAAO,GAAGH,KAAK,EAAE,EAAElB,KACvBiB,wBACA,4BAA4B,EAAEjB,KAAKiC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAEA,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrE,OAAOZ;gBACT;gBACA;YACF;QACF;IACF;AACF;AAEA,SAASqB,0BACPC,OAAe,EACfpC,UAA2C;QAGzCA,kCAAAA;IADF,MAAMqC,iBACJrC,EAAAA,mBAAAA,WAAWsC,IAAI,sBAAftC,mCAAAA,iBAAiBuC,IAAI,CAACC,IAAI,GAAGpC,KAAK,CAAC,0CAAnCJ,gCAA0D,CAAC,EAAE,KAAI;IAEnE,OAAOH,KAAK4C,QAAQ,CAACL,SAASC;AAChC;AAEA,OAAO,SAASK,uBACdC,EAA+B,EAC/BP,OAAe,EACfQ,OAAe,EACf5C,UAA2C,EAC3C6C,eAAyB;QAKvB7C;IAHF,gEAAgE;IAChE,MAAM8C,sBACJD,qBACA7C,mBAAAA,WAAWsC,IAAI,qBAAftC,iBAAiB+C,QAAQ,CAACC,UAAU,CAACnD,KAAKoD,IAAI,CAACb,SAASQ,SAAS;IAEnE,IAAI3C,UAAU;IAEd,MAAMiD,UAAUJ,sBACZX,0BAA0BC,SAASpC,cACnC;IACJ,MAAMmD,aAAapD,sCAAsCC;IACzD,MAAMoD,YACJ,CAACD,cAAcL,uBAAuBI,UAClCzC,+CAA+CyC,SAASlD,cACxD;IAEN,MAAMqD,SACJF,cACAC,aACAT,GAAGW,4BAA4B,CAACtD,WAAWE,WAAW,EAAE;IAC1D,MAAMqD,WAAWvD,WAAWuD,QAAQ;IACpC,OAAQA;QACN,UAAU;QACV;YAAiC;gBAC/BtD,WAAWL,OAAOH,KAAK,mBAAmB;gBAC1C;YACF;QACA,QAAQ;QACR;YAA+B;gBAC7BQ,WAAWN,IAAIF,KAAK,iBAAiB;gBACrC;YACF;QACA,8BAA8B;QAC9B;QACA;QACA;YAAS;gBACPQ,WAAWP,KAAKD,KAAK8D,aAAa,IAAI,eAAe,WAAW;gBAChE;YACF;IACF;IAEAtD,WAAWoD,SAAS;IAEpB,IAAI,CAACP,uBAAuB9C,WAAWsC,IAAI,EAAE;QAC3C,MAAM,EAAEkB,gBAAgB,EAAE,GAAGC,QAAQ;QACrC,MAAMC,MAAM1D,WAAWsC,IAAI,CAACqB,6BAA6B,CAAC3D,WAAW4D,KAAK;QAC1E,MAAMC,OAAOH,IAAIG,IAAI,GAAG;QACxB,MAAMC,YAAYJ,IAAII,SAAS,GAAG;QAElC,IAAIf,WAAWlD,KAAKkE,KAAK,CAACC,SAAS,CACjCnE,KAAK4C,QAAQ,CAACL,SAASpC,WAAWsC,IAAI,CAACS,QAAQ,EAAEzB,OAAO,CAAC,OAAO;QAElE,IAAI,CAACyB,SAASC,UAAU,CAAC,MAAM;YAC7BD,WAAW,OAAOA;QACpB;QAEA9C,UACEP,KAAKqD,YACL,MACAnD,OAAOiE,KAAKI,QAAQ,MACpB,MACArE,OAAOkE,UAAUG,QAAQ,MACzB,OACAhE;QAEFA,WACE,OACAuD,iBACExD,WAAWsC,IAAI,CAAC4B,WAAW,CAAClE,WAAWsC,IAAI,CAAC6B,aAAa,KACzD;YACEP,OAAO;gBAAEC,MAAMA;gBAAMO,QAAQN;YAAU;QACzC,GACA;YAAEO,YAAY;QAAK;IAEzB,OAAO,IAAIvB,uBAAuBI,SAAS;QACzCjD,UAAUP,KAAKwD,WAAW,OAAOjD;IACnC;IAEA,OAAOA;AACT"}