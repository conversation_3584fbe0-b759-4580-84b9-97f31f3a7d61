import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typo<PERSON>,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import abonnementService from '../../../services/abonnementService';

const RenewalRequestsList = () => {
  const theme = useTheme();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [action, setAction] = useState(null);
  const [comment, setComment] = useState('');
  const [processing, setProcessing] = useState(false);
  const [success, setSuccess] = useState(null);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      const data = await abonnementService.getDemandesRenouvellement();
      setRequests(data);

      // Marquer toutes les demandes non vues comme vues
      const nonVues = data.filter(req => !req.vuParAdmin && req.statut === 'EN_ATTENTE');
      for (const req of nonVues) {
        await abonnementService.marquerDemandeCommeVue(req._id);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des demandes de renouvellement:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const handleRefresh = () => {
    fetchRequests();
  };

  const handleOpenDialog = (request, actionType) => {
    setSelectedRequest(request);
    setAction(actionType);
    setComment('');
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedRequest(null);
    setAction(null);
    setComment('');
  };

  const handleProcess = async () => {
    if (!selectedRequest || !action) return;

    setProcessing(true);
    try {
      const response = await abonnementService.traiterDemandeRenouvellement(
        selectedRequest._id,
        {
          statut: action === 'approve' ? 'APPROUVEE' : 'REJETEE',
          commentaires: comment
        }
      );

      setSuccess({
        message: response.message,
        severity: 'success'
      });

      // Fermer la boîte de dialogue après 2 secondes
      setTimeout(() => {
        handleCloseDialog();
        fetchRequests();
        setSuccess(null);
      }, 2000);
    } catch (error) {
      console.error('Erreur lors du traitement de la demande:', error);
      setSuccess({
        message: error.message,
        severity: 'error'
      });
    } finally {
      setProcessing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'EN_ATTENTE':
        return theme.palette.warning.main;
      case 'APPROUVEE':
        return theme.palette.success.main;
      case 'REJETEE':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'EN_ATTENTE':
        return 'En attente';
      case 'APPROUVEE':
        return 'Approuvée';
      case 'REJETEE':
        return 'Rejetée';
      default:
        return status;
    }
  };

  const getDurationLabel = (duration) => {
    switch (duration) {
      case '30_MIN':
        return '30 minutes';
      case '3_MOIS':
        return '3 mois';
      case '6_MOIS':
        return '6 mois';
      case '1_AN':
        return '1 an';
      default:
        return duration;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const pendingRequests = requests.filter(req => req.statut === 'EN_ATTENTE');
  const processedRequests = requests.filter(req => req.statut !== 'EN_ATTENTE');

  return (
    <Card
      elevation={0}
      sx={{
        mb: 4,
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
        overflow: 'hidden'
      }}
    >
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6" fontWeight="600">
              Demandes de Renouvellement
            </Typography>
            <Tooltip title="Actualiser">
              <IconButton size="small" onClick={handleRefresh}>
                <RefreshIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        }
      />
      <Divider />
      <CardContent>
        {requests.length === 0 ? (
          <Typography variant="body1" color="text.secondary" align="center" sx={{ py: 4 }}>
            Aucune demande de renouvellement à afficher
          </Typography>
        ) : (
          <>
            {pendingRequests.length > 0 && (
              <>
                <Typography variant="subtitle1" fontWeight="600" sx={{ mb: 2 }}>
                  Demandes en attente ({pendingRequests.length})
                </Typography>
                <List>
                  {pendingRequests.map((request) => (
                    <React.Fragment key={request._id}>
                      <ListItem
                        sx={{
                          borderRadius: 1,
                          mb: 1,
                          backgroundColor: alpha(theme.palette.warning.main, 0.05),
                          border: `1px solid ${alpha(theme.palette.warning.main, 0.1)}`,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.warning.main, 0.1),
                          }
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <AccessTimeIcon fontSize="small" color="warning" sx={{ mr: 1 }} />
                              <Typography variant="subtitle2" fontWeight="600">
                                {request.responsableId?.prenom} {request.responsableId?.nom}
                              </Typography>
                              <Chip
                                label={getDurationLabel(request.dureeChoisie)}
                                size="small"
                                color="primary"
                                variant="outlined"
                                sx={{ ml: 2 }}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              Demande créée le {new Date(request.dateCreation).toLocaleDateString('fr-FR')} à {new Date(request.dateCreation).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                            </Typography>
                          }
                        />
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            color="error"
                            startIcon={<CancelIcon />}
                            onClick={() => handleOpenDialog(request, 'reject')}
                          >
                            Rejeter
                          </Button>
                          <Button
                            size="small"
                            variant="contained"
                            color="success"
                            startIcon={<CheckCircleIcon />}
                            onClick={() => handleOpenDialog(request, 'approve')}
                          >
                            Approuver
                          </Button>
                        </Box>
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              </>
            )}

            {processedRequests.length > 0 && (
              <>
                <Typography variant="subtitle1" fontWeight="600" sx={{ mt: 3, mb: 2 }}>
                  Demandes traitées ({processedRequests.length})
                </Typography>
                <List>
                  {processedRequests.slice(0, 5).map((request) => (
                    <React.Fragment key={request._id}>
                      <ListItem
                        sx={{
                          borderRadius: 1,
                          mb: 1,
                          backgroundColor: alpha(getStatusColor(request.statut), 0.05),
                          border: `1px solid ${alpha(getStatusColor(request.statut), 0.1)}`,
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <Typography variant="subtitle2" fontWeight="600">
                                {request.responsableId?.prenom} {request.responsableId?.nom}
                              </Typography>
                              <Chip
                                label={getStatusLabel(request.statut)}
                                size="small"
                                color={request.statut === 'APPROUVEE' ? 'success' : 'error'}
                                sx={{ ml: 2 }}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              {request.dateTraitement ?
                                `Traitée le ${new Date(request.dateTraitement).toLocaleDateString('fr-FR')} à ${new Date(request.dateTraitement).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}` :
                                `Demande du ${new Date(request.dateCreation).toLocaleDateString('fr-FR')}`}
                              {request.commentaires && ` - ${request.commentaires}`}
                            </Typography>
                          }
                        />
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              </>
            )}
          </>
        )}
      </CardContent>

      {/* Boîte de dialogue de confirmation */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog}>
        <DialogTitle>
          {action === 'approve' ? 'Approuver la demande' : 'Rejeter la demande'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {action === 'approve'
              ? `Vous êtes sur le point d'approuver la demande de renouvellement de ${selectedRequest?.responsableId?.prenom || ''} ${selectedRequest?.responsableId?.nom || ''} pour une durée de ${getDurationLabel(selectedRequest?.dureeChoisie)}.`
              : `Vous êtes sur le point de rejeter la demande de renouvellement de ${selectedRequest?.responsableId?.prenom || ''} ${selectedRequest?.responsableId?.nom || ''}.`}
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="comment"
            label="Commentaire (optionnel)"
            type="text"
            fullWidth
            variant="outlined"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            sx={{ mt: 2 }}
          />
          {success && (
            <Alert severity={success.severity} sx={{ mt: 2 }}>
              {success.message}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={processing}>
            Annuler
          </Button>
          <Button
            onClick={handleProcess}
            color={action === 'approve' ? 'success' : 'error'}
            variant="contained"
            disabled={processing}
            startIcon={processing ? <CircularProgress size={20} /> : null}
          >
            {processing
              ? 'Traitement...'
              : action === 'approve'
                ? 'Approuver'
                : 'Rejeter'}
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};

export default RenewalRequestsList;
