import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Box,
  Typography,
  Button,
  Grid,
  Paper,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  alpha,
  Avatar,
  Stack,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  TextField
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  PictureAsPdf as PdfIcon,
  Print as PrintIcon,
  Description as DescriptionIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  Notes as NotesIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import devisService from '../services/devisService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';

const ClientDevisView = () => {
  const theme = useTheme();
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [devis, setDevis] = useState(null);
  const [error, setError] = useState(null);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    action: null
  });

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  useEffect(() => {
    const fetchDevis = async () => {
      try {
        setLoading(true);
        const response = await devisService.getDevisById(id);
        console.log('Devis récupéré du serveur:', response);
        console.log('ID client connecté:', currentUser.clientId);

        // Vérifier que le devis appartient bien au client connecté
        if (!response.clientId) {
          console.error('Le devis n\'a pas de clientId');
          setError('Erreur: Ce devis n\'est pas associé à un client');
          setLoading(false);
          return;
        }

        console.log('Email de l\'utilisateur connecté:', currentUser.email);

        // Extraire les IDs en tant que chaînes de caractères
        let devisClientId;
        let devisClientEmail = '';

        if (typeof response.clientId === 'object' && response.clientId !== null) {
          devisClientId = response.clientId._id ? response.clientId._id.toString() : '';
          devisClientEmail = response.clientId.email || '';
          console.log('ID client du devis (objet):', devisClientId);
          console.log('Email du client du devis:', devisClientEmail);
        } else {
          devisClientId = response.clientId.toString();
          console.log('ID client du devis (chaîne):', devisClientId);
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId._id) {
          userClientId = currentUser.clientId._id.toString();
          console.log('ID client de l\'utilisateur (objet):', userClientId);
        } else {
          userClientId = currentUser.clientId.toString();
          console.log('ID client de l\'utilisateur (chaîne):', userClientId);
        }

        console.log('Comparaison des IDs:', devisClientId, '===', userClientId);
        console.log('Comparaison des emails:', devisClientEmail, '===', currentUser.email);

        // Vérifier si les IDs correspondent OU si les emails correspondent
        const matchById = devisClientId === userClientId;
        const matchByEmail = devisClientEmail && currentUser.email &&
                            devisClientEmail.toLowerCase() === currentUser.email.toLowerCase();

        if (matchById || matchByEmail) {
          console.log('Accès autorisé (correspondance par ID ou email)');
          setDevis(response);
        } else {
          console.error('Ni les IDs ni les emails ne correspondent');
          console.log('ID client du devis:', devisClientId);
          console.log('ID client de l\'utilisateur:', userClientId);
          console.log('Email client du devis:', devisClientEmail);
          console.log('Email de l\'utilisateur:', currentUser.email);
          setError('Vous n\'avez pas accès à ce devis');
        }

        setLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement du devis:', error);
        setError('Erreur lors du chargement du devis');
        setLoading(false);
      }
    };

    if (id && currentUser && currentUser.clientId) {
      fetchDevis();
    } else {
      console.error('Utilisateur non connecté ou sans clientId');
      setError('Erreur: Vous devez être connecté pour accéder à ce devis');
      setLoading(false);
    }
  }, [id, currentUser]);

  const handleGoBack = () => {
    navigate('/client/devis');
  };

  const handleGeneratePdf = async () => {
    try {
      await devisService.generatePdf(id);
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
    }
  };

  const handlePrint = async () => {
    try {
      await devisService.printDevis(id);
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
    }
  };

  const handleAcceptDevis = () => {
    setConfirmDialog({
      open: true,
      title: 'Accepter le devis',
      message: 'Êtes-vous sûr de vouloir accepter ce devis ? Cette action ne peut pas être annulée.',
      action: 'accept'
    });
  };

  const [rejectReason, setRejectReason] = useState('');

  const handleRejectDevis = () => {
    setRejectReason('');
    setConfirmDialog({
      open: true,
      title: 'Refuser le devis',
      message: 'Veuillez indiquer la raison de votre refus. Cette action ne peut pas être annulée.',
      action: 'reject'
    });
  };

  const handleConfirmAction = async () => {
    try {
      const { action } = confirmDialog;

      if (action === 'accept') {
        console.log(`Acceptation du devis ${id} depuis la vue détaillée`);
        await devisService.acceptDevis(id);
      } else if (action === 'reject') {
        // Vérifier que la raison du refus est fournie
        if (!rejectReason.trim()) {
          setSnackbar({
            open: true,
            message: 'Veuillez indiquer la raison du refus',
            severity: 'error'
          });
          return;
        }

        console.log(`Refus du devis ${id} depuis la vue détaillée avec raison: ${rejectReason}`);
        await devisService.rejectDevis(id, rejectReason);
      }

      // Rafraîchir les données
      const response = await devisService.getDevisById(id);
      setDevis(response);

      // Fermer la boîte de dialogue
      setConfirmDialog({ ...confirmDialog, open: false });

      // Afficher un message de succès
      setSnackbar({
        open: true,
        message: action === 'accept' ? 'Devis accepté avec succès' : 'Devis refusé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de l\'action sur le devis:', error);
      setConfirmDialog({ ...confirmDialog, open: false });

      // Afficher un message d'erreur
      setSnackbar({
        open: true,
        message: `Erreur: ${error.message}`,
        severity: 'error'
      });
    }
  };

  const handleCloseDialog = () => {
    setConfirmDialog({ ...confirmDialog, open: false });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          sx={{ mt: 2 }}
        >
          Retour aux devis
        </Button>
      </Box>
    );
  }

  if (!devis) {
    return (
      <Box sx={{ mt: 4 }}>
        <Alert severity="warning">Devis non trouvé</Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          sx={{ mt: 2 }}
        >
          Retour aux devis
        </Button>
      </Box>
    );
  }

  const canAcceptReject = devis.statut === 'PENDING' || devis.statut === 'SENT' || devis.statut === 'ENVOYÉ';

  return (
    <Box component={motion.div}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
        >
          Retour aux devis
        </Button>

        <Box>
          {canAcceptReject && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<CheckCircleIcon />}
                onClick={handleAcceptDevis}
                sx={{ mr: 1 }}
              >
                Accepter
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<CancelIcon />}
                onClick={handleRejectDevis}
                sx={{ mr: 1 }}
              >
                Refuser
              </Button>
            </>
          )}
          <Button
            variant="outlined"
            startIcon={<PdfIcon />}
            onClick={handleGeneratePdf}
            sx={{ mr: 1 }}
          >
            Télécharger PDF
          </Button>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
          >
            Imprimer
          </Button>
        </Box>
      </Box>

      {/* En-tête du devis */}
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <Box sx={{
          p: 3,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          bgcolor: alpha(theme.palette.primary.main, 0.03)
        }}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item>
              <Avatar
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  width: 48,
                  height: 48
                }}
              >
                <DescriptionIcon />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h5" fontWeight="bold">
                Devis {devis.numéro}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                <CalendarIcon fontSize="small" sx={{ color: 'text.secondary', mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Créé le {formatDate(devis.dateCréation)}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mx: 1 }}>
                  • Valide jusqu'au {formatDate(devis.dateValidite)}
                </Typography>
                <Chip
                  label={formatStatut(devis.statut)}
                  color={
                    devis.statut === 'ACCEPTED' || devis.statut === 'ACCEPTÉ' ? 'success' :
                    devis.statut === 'REJECTED' || devis.statut === 'REFUSÉ' ? 'error' :
                    devis.statut === 'EXPIRED' || devis.statut === 'EXPIRÉ' ? 'error' :
                    devis.statut === 'DRAFT' || devis.statut === 'BROUILLON' ? 'info' : 'warning'
                  }
                  size="small"
                />
              </Box>
            </Grid>
          </Grid>
        </Box>

        <CardContent>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Émetteur
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {devis.vendeurId ? devis.vendeurId.nom : 'N/A'}
              </Typography>
              {devis.vendeurId && (
                <>
                  <Typography variant="body2">{devis.vendeurId.email || 'N/A'}</Typography>
                  <Typography variant="body2">{devis.vendeurId.telephone || 'N/A'}</Typography>
                </>
              )}
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Client
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {devis.clientId ? devis.clientId.nom : 'N/A'}
              </Typography>
              {devis.clientId && (
                <>
                  <Typography variant="body2">{devis.clientId.email || 'N/A'}</Typography>
                  <Typography variant="body2">{devis.clientId.adresse || 'N/A'}</Typography>
                  <Typography variant="body2">{devis.clientId.telephone || 'N/A'}</Typography>
                </>
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Détails du devis */}
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Détails du devis
          </Typography>

          <TableContainer component={Paper} elevation={0} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.03) }}>
                  <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Quantité</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Prix unitaire</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Total</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {devis.lignes && devis.lignes.map((ligne, index) => (
                  <TableRow key={index}>
                    <TableCell>{ligne.description}</TableCell>
                    <TableCell align="right">{ligne.quantite}</TableCell>
                    <TableCell align="right">{formatCurrency(ligne.prixUnitaire)}</TableCell>
                    <TableCell align="right">{formatCurrency(ligne.quantite * ligne.prixUnitaire)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Grid container spacing={1} sx={{ maxWidth: 300 }}>
              <Grid item xs={6}>
                <Typography variant="body2" align="right">
                  Sous-total:
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" align="right" fontWeight="medium">
                  {formatCurrency(devis.montantHT || 0)}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body2" align="right">
                  TVA ({devis.tauxTVA}%):
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" align="right" fontWeight="medium">
                  {formatCurrency((devis.montantTTC || 0) - (devis.montantHT || 0))}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="subtitle1" align="right" fontWeight="bold">
                  Total:
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1" align="right" fontWeight="bold">
                  {formatCurrency(devis.total)}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>

      {/* Notes */}
      {devis.notes && (
        <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <NotesIcon sx={{ color: theme.palette.text.secondary, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold">
                Notes
              </Typography>
            </Box>
            <Typography variant="body2">
              {devis.notes}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Dialogue de confirmation */}
      <Dialog
        open={confirmDialog.open}
        onClose={handleCloseDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>{confirmDialog.title}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {confirmDialog.message}
          </DialogContentText>

          {confirmDialog.action === 'reject' && (
            <TextField
              autoFocus
              margin="dense"
              id="rejectReason"
              label="Raison du refus"
              type="text"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              sx={{ mt: 2 }}
              placeholder="Veuillez expliquer pourquoi vous refusez ce devis..."
              required
              error={confirmDialog.action === 'reject' && !rejectReason.trim()}
              helperText={confirmDialog.action === 'reject' && !rejectReason.trim() ? "Ce champ est obligatoire" : ""}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Annuler
          </Button>
          <Button
            onClick={handleConfirmAction}
            color={confirmDialog.action === 'accept' ? 'success' : 'error'}
            variant="contained"
            autoFocus
            disabled={confirmDialog.action === 'reject' && !rejectReason.trim()}
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar pour les notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ClientDevisView;
