const express = require('express');
const router = express.Router();
const Abonnement = require('../models/AbonnementModel');
const User = require('../models/UserModel');
const Client = require('../models/ClientModel');
const ResponsableEntreprise = require('../models/ResponsableEntrepriseModel');
const DemandeRenouvellement = require('../models/DemandeRenouvellementModel');
const { verifyToken, isAdmin } = require('../middleware/authMiddleware');
const { checkExpiredSubscriptions } = require('../tasks/subscriptionStatusChecker');

// Middleware pour vérifier que l'utilisateur est un administrateur
const adminOnly = [verifyToken, isAdmin];

// Créer un nouvel abonnement (Admin uniquement)
router.post('/', adminOnly, async (req, res) => {
  try {
    console.log('Création d\'un nouvel abonnement:', req.body);

    // Vérifier que le responsable existe
    const responsable = await User.findById(req.body.responsableId);
    if (!responsable) {
      return res.status(404).json({ message: 'Responsable non trouvé' });
    }

    // Vérifier que l'entreprise existe (soit dans Client, soit dans ResponsableEntreprise)
    let entreprise = null;
    let entrepriseModel = 'Client';

    // D'abord, essayer de trouver dans Client
    entreprise = await Client.findById(req.body.entrepriseId);

    // Si non trouvé dans Client, essayer dans ResponsableEntreprise
    if (!entreprise) {
      entreprise = await ResponsableEntreprise.findById(req.body.entrepriseId);
      if (entreprise) {
        entrepriseModel = 'ResponsableEntreprise';
      }
    }

    // Si toujours pas trouvé, retourner une erreur
    if (!entreprise) {
      console.error(`Entreprise non trouvée avec l'ID: ${req.body.entrepriseId}`);
      return res.status(404).json({ message: 'Entreprise non trouvée' });
    }

    // Ajouter le modèle d'entreprise aux données
    req.body.entrepriseModel = entrepriseModel;

    // Calculer la date de fin en fonction de la durée
    const dateDebut = new Date(req.body.dateDebut || Date.now());
    let dateFin = new Date(dateDebut);

    switch(req.body.duree) {
      case '10_MIN':
        // Ajouter 10 minutes à la date de début
        // Assurons-nous que la date de début est bien la date actuelle pour les abonnements courts
        if (req.body.duree === '10_MIN') {
          // Forcer la date de début à être maintenant pour les abonnements courts
          const maintenant = new Date();
          dateDebut.setTime(maintenant.getTime());
          dateFin = new Date(maintenant);
        }
        dateFin.setMinutes(dateFin.getMinutes() + 10);
        break;
      case '1_JOUR':
        // Ajouter 1 jour à la date de début
        dateFin.setDate(dateFin.getDate() + 1);
        break;
      case '7_JOURS':
        // Ajouter 7 jours à la date de début
        dateFin.setDate(dateFin.getDate() + 7);
        break;
      case '3_MOIS':
        dateFin.setMonth(dateFin.getMonth() + 3);
        break;
      case '6_MOIS':
        dateFin.setMonth(dateFin.getMonth() + 6);
        break;
      case '1_AN':
      case 'ANNEE':
        dateFin.setFullYear(dateFin.getFullYear() + 1);
        break;
      default:
        return res.status(400).json({ message: 'Durée d\'abonnement invalide' });
    }

    // Log pour le débogage
    if (req.body.duree === '10_MIN') {
      console.log('Création d\'un abonnement de 10 minutes:');
      console.log('Date de début:', dateDebut);
      console.log('Date de fin:', dateFin);
      console.log('Durée en minutes:', (dateFin - dateDebut) / (1000 * 60));
    }

    // Créer le nouvel abonnement
    const abonnement = new Abonnement({
      ...req.body,
      dateFin
    });

    await abonnement.save();

    // Mettre à jour le statut d'abonnement du responsable
    await User.findByIdAndUpdate(req.body.responsableId, {
      abonnementActif: true,
      abonnementId: abonnement._id
    });

    res.status(201).json(abonnement);
  } catch (error) {
    console.error('Erreur lors de la création de l\'abonnement:', error);
    res.status(400).json({
      message: 'Erreur lors de la création de l\'abonnement',
      error: error.message
    });
  }
});

// Récupérer toutes les demandes de renouvellement (Admin uniquement)
router.get('/renouvellements', adminOnly, async (req, res) => {
  try {
    const demandes = await DemandeRenouvellement.find()
      .populate('abonnementId')
      .populate('responsableId', 'nom prenom email')
      .sort({ dateCreation: -1 }); // Les plus récentes d'abord

    res.json(demandes);
  } catch (error) {
    console.error('Erreur lors de la récupération des demandes de renouvellement:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des demandes de renouvellement',
      error: error.message
    });
  }
});

// Récupérer le nombre de demandes de renouvellement non vues (Admin uniquement)
router.get('/renouvellements/count', adminOnly, async (req, res) => {
  try {
    const count = await DemandeRenouvellement.countDocuments({
      vuParAdmin: false,
      statut: 'EN_ATTENTE'
    });

    res.json({ count });
  } catch (error) {
    console.error('Erreur lors du comptage des demandes de renouvellement:', error);
    res.status(500).json({
      message: 'Erreur lors du comptage des demandes de renouvellement',
      error: error.message
    });
  }
});

// Récupérer l'abonnement du responsable connecté
router.get('/user/responsable', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({ message: 'Accès non autorisé. Seuls les responsables peuvent accéder à cette ressource.' });
    }

    // Récupérer l'abonnement associé au responsable
    const abonnement = await Abonnement.findOne({ responsableId: req.userId })
      .populate({
        path: 'entrepriseId',
        select: 'nom adresse email raisonSociale',
      })
      .populate('responsableId', 'nom prenom email');

    if (!abonnement) {
      return res.status(404).json({
        message: 'Aucun abonnement trouvé pour ce responsable',
        hasAbonnement: false
      });
    }

    // Vérifier si l'abonnement est actif
    const isActif = abonnement.statut === 'ACTIF' && new Date(abonnement.dateFin) > new Date();

    // Calculer les jours restants
    const today = new Date();
    const dateFin = new Date(abonnement.dateFin);
    const diffTime = Math.abs(dateFin - today);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    res.json({
      abonnement,
      isActif,
      joursRestants: diffDays,
      hasAbonnement: true
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'abonnement du responsable:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'abonnement',
      error: error.message
    });
  }
});

// Vérifier si l'abonnement est sur le point d'expirer (moins de 2 minutes restantes)
router.get('/user/check-expiration', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({ message: 'Accès non autorisé. Seuls les responsables peuvent accéder à cette ressource.' });
    }

    // Récupérer l'abonnement associé au responsable
    const abonnement = await Abonnement.findOne({ responsableId: req.userId });

    if (!abonnement) {
      return res.status(404).json({
        message: 'Aucun abonnement trouvé pour ce responsable',
        hasAbonnement: false
      });
    }

    // Vérifier si l'abonnement est actif
    const isActif = abonnement.statut === 'ACTIF';

    if (!isActif) {
      return res.json({
        isExpiring: false,
        isExpired: true,
        message: 'Votre abonnement a déjà expiré ou est suspendu.'
      });
    }

    // Calculer les minutes restantes
    const now = new Date();
    const dateFin = new Date(abonnement.dateFin);
    const diffTime = dateFin - now; // en millisecondes
    const diffMinutes = Math.floor(diffTime / (1000 * 60));

    console.log('Check-expiration pour utilisateur:', req.userId);
    console.log('Durée de l\'abonnement:', abonnement.duree);
    console.log('Date de fin:', dateFin);
    console.log('Date actuelle:', now);
    console.log('Minutes restantes:', diffMinutes);

    // Vérifier si moins de 2 minutes restantes
    // Pour les abonnements de 30 minutes, on considère qu'ils expirent seulement après -1 minute
    // pour tenir compte des décalages d'horloge
    let isExpiring, isExpired;

    if (abonnement.duree === '30_MIN') {
      // Pour les abonnements de 30 minutes
      isExpiring = diffMinutes <= 2 && diffMinutes > -1;
      isExpired = diffMinutes <= -1;
    } else {
      // Pour les autres abonnements
      isExpiring = diffMinutes <= 2 && diffMinutes > 0;
      isExpired = diffMinutes <= 0;
    }

    res.json({
      isExpiring,
      isExpired,
      minutesRestantes: Math.max(0, diffMinutes),
      message: isExpiring
        ? 'Attention : Votre abonnement expire dans moins de 2 minutes.'
        : (isExpired ? 'Votre abonnement a expiré.' : 'Votre abonnement est actif.'),
      abonnementId: abonnement._id,
      duree: abonnement.duree
    });
  } catch (error) {
    console.error('Erreur lors de la vérification de l\'expiration de l\'abonnement:', error);
    res.status(500).json({
      message: 'Erreur lors de la vérification de l\'expiration de l\'abonnement',
      error: error.message
    });
  }
});

// Endpoint pour vérifier manuellement les abonnements expirés (Admin uniquement)
router.post('/check-expired', adminOnly, async (req, res) => {
  try {
    console.log('Vérification manuelle des abonnements expirés...');
    await checkExpiredSubscriptions();
    res.json({
      success: true,
      message: 'Vérification des abonnements expirés effectuée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la vérification manuelle des abonnements expirés:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des abonnements expirés',
      error: error.message
    });
  }
});

// Récupérer tous les abonnements (Admin uniquement)
router.get('/', adminOnly, async (req, res) => {
  try {
    const abonnements = await Abonnement.find()
      .populate({
        path: 'entrepriseId',
        select: 'nom adresse email raisonSociale',
        // Le modèle est déterminé automatiquement grâce à refPath
      })
      .populate('responsableId', 'nom prenom email');

    res.json(abonnements);
  } catch (error) {
    console.error('Erreur lors de la récupération des abonnements:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des abonnements',
      error: error.message
    });
  }
});

// Récupérer un abonnement par ID
router.get('/:id', verifyToken, async (req, res) => {
  try {
    const abonnement = await Abonnement.findById(req.params.id)
      .populate({
        path: 'entrepriseId',
        select: 'nom adresse email raisonSociale',
        // Le modèle est déterminé automatiquement grâce à refPath
      })
      .populate('responsableId', 'nom prenom email');

    if (!abonnement) {
      return res.status(404).json({ message: 'Abonnement non trouvé' });
    }

    // Vérifier que l'utilisateur est admin ou le responsable associé
    if (req.userRole !== 'ADMIN' && req.userId !== abonnement.responsableId._id.toString()) {
      return res.status(403).json({ message: 'Accès non autorisé' });
    }

    res.json(abonnement);
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'abonnement:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'abonnement',
      error: error.message
    });
  }
});

// Mettre à jour un abonnement (Admin uniquement)
router.put('/:id', adminOnly, async (req, res) => {
  try {
    const { duree, dateDebut, statut } = req.body;
    const abonnement = await Abonnement.findById(req.params.id);

    if (!abonnement) {
      return res.status(404).json({ message: 'Abonnement non trouvé' });
    }

    // Si la durée ou la date de début change, recalculer la date de fin
    if (duree || dateDebut) {
      let newDateDebut = dateDebut ? new Date(dateDebut) : abonnement.dateDebut;
      let newDateFin = new Date(newDateDebut);

      switch(duree || abonnement.duree) {
        case '10_MIN':
          // Pour les abonnements de 10 minutes, toujours utiliser la date actuelle comme point de départ
          if ((duree && duree === '10_MIN') || (!duree && abonnement.duree === '10_MIN')) {
            // Forcer la date de début à être maintenant
            const maintenant = new Date();
            newDateDebut = maintenant;
            newDateFin = new Date(maintenant);
          }
          // Ajouter 10 minutes à la date de début
          newDateFin.setMinutes(newDateFin.getMinutes() + 10);

          // Log pour le débogage
          console.log('Mise à jour d\'un abonnement de 10 minutes:');
          console.log('Nouvelle date de début:', newDateDebut);
          console.log('Nouvelle date de fin:', newDateFin);
          console.log('Durée en minutes:', (newDateFin - newDateDebut) / (1000 * 60));
          break;
        case '1_JOUR':
          newDateFin.setDate(newDateFin.getDate() + 1);
          break;
        case '7_JOURS':
          newDateFin.setDate(newDateFin.getDate() + 7);
          break;
        case '3_MOIS':
          newDateFin.setMonth(newDateFin.getMonth() + 3);
          break;
        case '6_MOIS':
          newDateFin.setMonth(newDateFin.getMonth() + 6);
          break;
        case '1_AN':
        case 'ANNEE':
          newDateFin.setFullYear(newDateFin.getFullYear() + 1);
          break;
      }

      abonnement.dateFin = newDateFin;
      if (dateDebut) abonnement.dateDebut = newDateDebut;
      if (duree) abonnement.duree = duree;
    }

    // Mettre à jour le statut si fourni
    if (statut) {
      abonnement.statut = statut;

      // Mettre à jour le statut d'abonnement du responsable
      await User.findByIdAndUpdate(abonnement.responsableId, {
        abonnementActif: statut === 'ACTIF'
      });
    }

    // Mettre à jour les autres champs fournis
    Object.keys(req.body).forEach(key => {
      if (!['duree', 'dateDebut', 'dateFin', 'statut'].includes(key)) {
        abonnement[key] = req.body[key];
      }
    });

    await abonnement.save();

    res.json(abonnement);
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'abonnement:', error);
    res.status(400).json({
      message: 'Erreur lors de la mise à jour de l\'abonnement',
      error: error.message
    });
  }
});

// Supprimer un abonnement (Admin uniquement)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const abonnement = await Abonnement.findById(req.params.id);

    if (!abonnement) {
      return res.status(404).json({ message: 'Abonnement non trouvé' });
    }

    // Mettre à jour le statut d'abonnement du responsable
    await User.findByIdAndUpdate(abonnement.responsableId, {
      abonnementActif: false,
      abonnementId: null
    });

    await Abonnement.findByIdAndDelete(req.params.id);

    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'abonnement:', error);
    res.status(500).json({
      message: 'Erreur lors de la suppression de l\'abonnement',
      error: error.message
    });
  }
});

// Récupérer l'abonnement du responsable connecté
router.get('/user/responsable', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({ message: 'Accès non autorisé. Seuls les responsables peuvent accéder à cette ressource.' });
    }

    // Récupérer l'abonnement associé au responsable
    const abonnement = await Abonnement.findOne({ responsableId: req.userId })
      .populate({
        path: 'entrepriseId',
        select: 'nom adresse email raisonSociale',
      })
      .populate('responsableId', 'nom prenom email');

    if (!abonnement) {
      return res.status(404).json({
        message: 'Aucun abonnement trouvé pour ce responsable',
        hasAbonnement: false
      });
    }

    // Vérifier si l'abonnement est actif
    const isActif = abonnement.statut === 'ACTIF' && new Date(abonnement.dateFin) > new Date();

    // Calculer les jours restants
    const today = new Date();
    const dateFin = new Date(abonnement.dateFin);
    const diffTime = Math.abs(dateFin - today);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    res.json({
      abonnement,
      isActif,
      joursRestants: diffDays,
      hasAbonnement: true
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'abonnement du responsable:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'abonnement',
      error: error.message
    });
  }
});

// Vérifier si l'abonnement est sur le point d'expirer (moins de 2 minutes restantes)
router.get('/user/check-expiration', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({ message: 'Accès non autorisé. Seuls les responsables peuvent accéder à cette ressource.' });
    }

    // Récupérer l'abonnement associé au responsable
    const abonnement = await Abonnement.findOne({ responsableId: req.userId });

    if (!abonnement) {
      return res.status(404).json({
        message: 'Aucun abonnement trouvé pour ce responsable',
        hasAbonnement: false
      });
    }

    // Vérifier si l'abonnement est actif
    const isActif = abonnement.statut === 'ACTIF';

    if (!isActif) {
      return res.json({
        isExpiring: false,
        isExpired: true,
        message: 'Votre abonnement a déjà expiré ou est suspendu.'
      });
    }

    // Calculer les minutes restantes
    const now = new Date();
    const dateFin = new Date(abonnement.dateFin);
    const diffTime = dateFin - now; // en millisecondes
    const diffMinutes = Math.floor(diffTime / (1000 * 60));

    console.log('Check-expiration pour utilisateur:', req.userId);
    console.log('Durée de l\'abonnement:', abonnement.duree);
    console.log('Date de fin:', dateFin);
    console.log('Date actuelle:', now);
    console.log('Minutes restantes:', diffMinutes);

    // Vérifier si moins de 2 minutes restantes
    // Pour les abonnements de 30 minutes, on considère qu'ils expirent seulement après -1 minute
    // pour tenir compte des décalages d'horloge
    let isExpiring, isExpired;

    if (abonnement.duree === '10_MIN') {
      // Pour les abonnements de 10 minutes
      isExpiring = diffMinutes <= 2 && diffMinutes > -1;
      isExpired = diffMinutes <= -1;
    } else {
      // Pour les autres abonnements
      isExpiring = diffMinutes <= 2 && diffMinutes > 0;
      isExpired = diffMinutes <= 0;
    }

    res.json({
      isExpiring,
      isExpired,
      minutesRestantes: Math.max(0, diffMinutes),
      message: isExpiring
        ? 'Attention : Votre abonnement expire dans moins de 2 minutes.'
        : (isExpired ? 'Votre abonnement a expiré.' : 'Votre abonnement est actif.'),
      abonnementId: abonnement._id,
      duree: abonnement.duree
    });
  } catch (error) {
    console.error('Erreur lors de la vérification de l\'expiration de l\'abonnement:', error);
    res.status(500).json({
      message: 'Erreur lors de la vérification de l\'expiration de l\'abonnement',
      error: error.message
    });
  }
});

// Demander un renouvellement d'abonnement (Responsable uniquement)
router.post('/user/renouvellement', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({ message: 'Accès non autorisé. Seuls les responsables peuvent demander un renouvellement.' });
    }

    // Récupérer l'abonnement actuel du responsable
    const abonnementActuel = await Abonnement.findOne({ responsableId: req.userId });

    if (!abonnementActuel) {
      return res.status(404).json({ message: 'Aucun abonnement trouvé pour ce responsable' });
    }

    // Récupérer les informations du responsable
    const responsable = await User.findById(req.userId);

    // Créer une demande de renouvellement et l'enregistrer dans la base de données
    const demandeRenouvellement = new DemandeRenouvellement({
      abonnementId: abonnementActuel._id,
      responsableId: req.userId,
      dureeChoisie: req.body.duree || abonnementActuel.duree,
      statut: 'EN_ATTENTE'
    });

    // Enregistrer la demande dans la base de données
    await demandeRenouvellement.save();

    console.log(`Nouvelle demande de renouvellement créée par ${responsable.prenom} ${responsable.nom} (${responsable.email})`);

    res.status(201).json({
      message: 'Votre demande de renouvellement a été envoyée avec succès',
      demande: demandeRenouvellement
    });
  } catch (error) {
    console.error('Erreur lors de la demande de renouvellement:', error);
    res.status(500).json({
      message: 'Erreur lors de la demande de renouvellement',
      error: error.message
    });
  }
});



// Marquer une demande de renouvellement comme vue (Admin uniquement)
router.put('/renouvellements/:id/vu', adminOnly, async (req, res) => {
  try {
    const demande = await DemandeRenouvellement.findByIdAndUpdate(
      req.params.id,
      { vuParAdmin: true },
      { new: true }
    );

    if (!demande) {
      return res.status(404).json({ message: 'Demande de renouvellement non trouvée' });
    }

    res.json(demande);
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la demande de renouvellement:', error);
    res.status(500).json({
      message: 'Erreur lors de la mise à jour de la demande de renouvellement',
      error: error.message
    });
  }
});

// Traiter une demande de renouvellement (Admin uniquement)
router.put('/renouvellements/:id/traiter', adminOnly, async (req, res) => {
  try {
    const { statut, commentaires } = req.body;

    if (!statut || !['APPROUVEE', 'REJETEE'].includes(statut)) {
      return res.status(400).json({ message: 'Statut invalide. Utilisez APPROUVEE ou REJETEE.' });
    }

    const demande = await DemandeRenouvellement.findById(req.params.id);

    if (!demande) {
      return res.status(404).json({ message: 'Demande de renouvellement non trouvée' });
    }

    // Mettre à jour la demande
    demande.statut = statut;
    demande.commentaires = commentaires;
    demande.dateTraitement = new Date();
    demande.traitePar = req.userId;
    demande.vuParAdmin = true;

    await demande.save();

    // Si la demande est approuvée, renouveler l'abonnement
    if (statut === 'APPROUVEE') {
      const abonnement = await Abonnement.findById(demande.abonnementId);

      if (!abonnement) {
        return res.status(404).json({ message: 'Abonnement non trouvé' });
      }

      // Calculer la nouvelle date de fin
      let dateFin = new Date();

      switch(demande.dureeChoisie) {
        case '10_MIN':
          dateFin.setMinutes(dateFin.getMinutes() + 10);
          break;
        case '1_JOUR':
          dateFin.setDate(dateFin.getDate() + 1);
          break;
        case '7_JOURS':
          dateFin.setDate(dateFin.getDate() + 7);
          break;
        case '3_MOIS':
          dateFin.setMonth(dateFin.getMonth() + 3);
          break;
        case '6_MOIS':
          dateFin.setMonth(dateFin.getMonth() + 6);
          break;
        case '1_AN':
          dateFin.setFullYear(dateFin.getFullYear() + 1);
          break;
        default:
          return res.status(400).json({ message: 'Durée d\'abonnement invalide' });
      }

      // Mettre à jour l'abonnement
      abonnement.duree = demande.dureeChoisie;
      abonnement.dateDebut = new Date();
      abonnement.dateFin = dateFin;
      abonnement.statut = 'ACTIF';

      await abonnement.save();

      // Mettre à jour le statut d'abonnement du responsable
      await User.findByIdAndUpdate(demande.responsableId, {
        abonnementActif: true
      });
    }

    res.json({
      message: `Demande de renouvellement ${statut === 'APPROUVEE' ? 'approuvée' : 'rejetée'} avec succès`,
      demande
    });
  } catch (error) {
    console.error('Erreur lors du traitement de la demande de renouvellement:', error);
    res.status(500).json({
      message: 'Erreur lors du traitement de la demande de renouvellement',
      error: error.message
    });
  }
});

module.exports = router;
