"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "createSelfSignedCertificate", {
    enumerable: true,
    get: function() {
        return createSelfSignedCertificate;
    }
});
const _nodefs = /*#__PURE__*/ _interop_require_default(require("node:fs"));
const _nodepath = /*#__PURE__*/ _interop_require_default(require("node:path"));
const _nodecrypto = require("node:crypto");
const _getcachedirectory = require("./helpers/get-cache-directory");
const _log = /*#__PURE__*/ _interop_require_wildcard(require("../build/output/log"));
const _nodechild_process = require("node:child_process");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const { WritableStream } = require('node:stream/web');
const MKCERT_VERSION = 'v1.4.4';
function getBinaryName() {
    const platform = process.platform;
    const arch = process.arch === 'x64' ? 'amd64' : process.arch;
    if (platform === 'win32') {
        return `mkcert-${MKCERT_VERSION}-windows-${arch}.exe`;
    }
    if (platform === 'darwin') {
        return `mkcert-${MKCERT_VERSION}-darwin-${arch}`;
    }
    if (platform === 'linux') {
        return `mkcert-${MKCERT_VERSION}-linux-${arch}`;
    }
    throw Object.defineProperty(new Error(`Unsupported platform: ${platform}`), "__NEXT_ERROR_CODE", {
        value: "E141",
        enumerable: false,
        configurable: true
    });
}
async function downloadBinary() {
    try {
        const binaryName = getBinaryName();
        const cacheDirectory = (0, _getcachedirectory.getCacheDirectory)('mkcert');
        const binaryPath = _nodepath.default.join(cacheDirectory, binaryName);
        if (_nodefs.default.existsSync(binaryPath)) {
            return binaryPath;
        }
        const downloadUrl = `https://github.com/FiloSottile/mkcert/releases/download/${MKCERT_VERSION}/${binaryName}`;
        await _nodefs.default.promises.mkdir(cacheDirectory, {
            recursive: true
        });
        _log.info(`Downloading mkcert package...`);
        const response = await fetch(downloadUrl);
        if (!response.ok || !response.body) {
            throw Object.defineProperty(new Error(`request failed with status ${response.status}`), "__NEXT_ERROR_CODE", {
                value: "E109",
                enumerable: false,
                configurable: true
            });
        }
        _log.info(`Download response was successful, writing to disk`);
        const binaryWriteStream = _nodefs.default.createWriteStream(binaryPath);
        await response.body.pipeTo(new WritableStream({
            write (chunk) {
                return new Promise((resolve, reject)=>{
                    binaryWriteStream.write(chunk, (error)=>{
                        if (error) {
                            reject(error);
                            return;
                        }
                        resolve();
                    });
                });
            },
            close () {
                return new Promise((resolve, reject)=>{
                    binaryWriteStream.close((error)=>{
                        if (error) {
                            reject(error);
                            return;
                        }
                        resolve();
                    });
                });
            }
        }));
        await _nodefs.default.promises.chmod(binaryPath, 493);
        return binaryPath;
    } catch (err) {
        _log.error('Error downloading mkcert:', err);
    }
}
async function createSelfSignedCertificate(host, certDir = 'certificates') {
    try {
        const binaryPath = await downloadBinary();
        if (!binaryPath) throw Object.defineProperty(new Error('missing mkcert binary'), "__NEXT_ERROR_CODE", {
            value: "E198",
            enumerable: false,
            configurable: true
        });
        const resolvedCertDir = _nodepath.default.resolve(process.cwd(), `./${certDir}`);
        await _nodefs.default.promises.mkdir(resolvedCertDir, {
            recursive: true
        });
        const keyPath = _nodepath.default.resolve(resolvedCertDir, 'localhost-key.pem');
        const certPath = _nodepath.default.resolve(resolvedCertDir, 'localhost.pem');
        if (_nodefs.default.existsSync(keyPath) && _nodefs.default.existsSync(certPath)) {
            const cert = new _nodecrypto.X509Certificate(_nodefs.default.readFileSync(certPath));
            const key = _nodefs.default.readFileSync(keyPath);
            if (cert.checkHost(host ?? 'localhost') && cert.checkPrivateKey((0, _nodecrypto.createPrivateKey)(key))) {
                _log.info('Using already generated self signed certificate');
                const caLocation = (0, _nodechild_process.execSync)(`"${binaryPath}" -CAROOT`).toString().trim();
                return {
                    key: keyPath,
                    cert: certPath,
                    rootCA: `${caLocation}/rootCA.pem`
                };
            }
        }
        _log.info('Attempting to generate self signed certificate. This may prompt for your password');
        const defaultHosts = [
            'localhost',
            '127.0.0.1',
            '::1'
        ];
        const hosts = host && !defaultHosts.includes(host) ? [
            ...defaultHosts,
            host
        ] : defaultHosts;
        (0, _nodechild_process.execSync)(`"${binaryPath}" -install -key-file "${keyPath}" -cert-file "${certPath}" ${hosts.join(' ')}`, {
            stdio: 'ignore'
        });
        const caLocation = (0, _nodechild_process.execSync)(`"${binaryPath}" -CAROOT`).toString().trim();
        if (!_nodefs.default.existsSync(keyPath) || !_nodefs.default.existsSync(certPath)) {
            throw Object.defineProperty(new Error('Certificate files not found'), "__NEXT_ERROR_CODE", {
                value: "E131",
                enumerable: false,
                configurable: true
            });
        }
        _log.info(`CA Root certificate created in ${caLocation}`);
        _log.info(`Certificates created in ${resolvedCertDir}`);
        const gitignorePath = _nodepath.default.resolve(process.cwd(), './.gitignore');
        if (_nodefs.default.existsSync(gitignorePath)) {
            const gitignore = await _nodefs.default.promises.readFile(gitignorePath, 'utf8');
            if (!gitignore.includes(certDir)) {
                _log.info('Adding certificates to .gitignore');
                await _nodefs.default.promises.appendFile(gitignorePath, `\n${certDir}`);
            }
        }
        return {
            key: keyPath,
            cert: certPath,
            rootCA: `${caLocation}/rootCA.pem`
        };
    } catch (err) {
        _log.error('Failed to generate self-signed certificate. Falling back to http.', err);
    }
}

//# sourceMappingURL=mkcert.js.map