import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Tooltip,
  CircularProgress,
  useTheme,
  alpha,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Stack,
  Badge,
  LinearProgress,
  useMediaQuery,
  Alert,
  Snackbar
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Visibility as VisibilityIcon,
  Receipt as ReceiptIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Payment as PaymentIcon,
  AccountBalance as BankIcon,
  CreditCard as CardIcon,
  Money as CashIcon,
  CheckCircle as CheckCircleIcon,
  PendingActions as PendingIcon,
  Cancel as CancelIcon,
  Email as EmailIcon,
  AttachMoney as MoneyIcon,
  ReceiptLong as ReceiptLongIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import paiementService from '../services/paiementService';
import factureService from '../services/factureService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';
import PaiementReceiptDialog from '../components/PaiementReceiptDialog';
import SimpleDateFilter from '../components/SimpleDateFilter';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

// Fonction pour obtenir la plage de dates à partir de l'ID de période
const getDateRangeFromId = (rangeId) => {
  const now = new Date();
  let startDate, endDate;

  switch (rangeId) {
    case 'thisMonth':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      break;
    case 'thisQuarter':
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0);
      break;
    case 'thisYear':
      startDate = new Date(now.getFullYear(), 0, 1);
      endDate = new Date(now.getFullYear(), 11, 31);
      break;
    case 'allTime':
      startDate = new Date(2000, 0, 1);
      endDate = new Date(now.getFullYear() + 10, 11, 31);
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  }

  // S'assurer que les dates sont au format ISO pour MongoDB
  return {
    startDate: new Date(startDate.setHours(0, 0, 0, 0)),
    endDate: new Date(endDate.setHours(23, 59, 59, 999))
  };
};

// Fonctions utilitaires pour les statuts et modes de paiement
const getStatusColor = (status) => {
  switch (status) {
    case 'PAID':
      return 'success';
    case 'SENT':
      return 'warning';
    case 'DRAFT':
      return 'info';
    case 'CANCELED':
      return 'error';
    default:
      return 'default';
  }
};

const getStatusIcon = (status) => {
  switch (status) {
    case 'PAID':
      return <CheckCircleIcon fontSize="small" />;
    case 'SENT':
      return <PendingIcon fontSize="small" />;
    case 'DRAFT':
      return <InfoIcon fontSize="small" />;
    case 'CANCELED':
      return <CancelIcon fontSize="small" />;
    default:
      return null;
  }
};

const getPaymentModeIcon = (mode) => {
  switch (mode) {
    case 'BANK_TRANSFER':
      return <BankIcon fontSize="small" />;
    case 'CHECK':
      return <ReceiptIcon fontSize="small" />;
    case 'CASH':
      return <CashIcon fontSize="small" />;
    default:
      return <PaymentIcon fontSize="small" />;
  }
};

const getPaymentModeLabel = (mode) => {
  switch (mode) {
    case 'BANK_TRANSFER':
      return 'Virement bancaire';
    case 'CHECK':
      return 'Chèque';
    case 'CASH':
      return 'Espèces';
    default:
      return mode;
  }
};

const ClientPaiements = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(true);
  const [paiements, setPaiements] = useState([]);
  const [filteredPaiements, setFilteredPaiements] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState('allTime');
  const [customDateRange, setCustomDateRange] = useState(null);
  const [selectedPaiement, setSelectedPaiement] = useState(null);
  const [openReceiptDialog, setOpenReceiptDialog] = useState(false);
  const [entrepriseInfo, setEntrepriseInfo] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Statistiques
  const [stats, setStats] = useState({
    totalPaiements: 0,
    totalMontant: 0,
    paiementsRecents: []
  });

  // Fonction pour calculer les statistiques
  const calculateStats = (paiementsData) => {
    const totalPaiements = paiementsData.length;
    const totalMontant = paiementsData.reduce((sum, p) => sum + p.montant, 0);

    // Récupérer les 5 derniers paiements
    const paiementsRecents = [...paiementsData]
      .sort((a, b) => new Date(b.datePaiement) - new Date(a.datePaiement))
      .slice(0, 5);

    setStats({
      totalPaiements,
      totalMontant,
      paiementsRecents
    });
  };

  const fetchData = async (period = 'allTime', customRange = null) => {
    try {
      setLoading(true);

      // Récupérer les paiements du client
      const paiementsResponse = await paiementService.getPaiements();

      // Filtrer les paiements pour n'afficher que ceux du client connecté
      const paiementsData = paiementsResponse.filter(paiement => {
        if (!paiement.factureId || !paiement.factureId.clientId) {
          return false;
        }

        // Vérifier si clientId est un objet avec _id ou directement un ID
        let paiementClientId;
        let paiementClientEmail = '';

        if (typeof paiement.factureId.clientId === 'object' && paiement.factureId.clientId !== null) {
          paiementClientId = paiement.factureId.clientId._id ? paiement.factureId.clientId._id.toString() : '';
          paiementClientEmail = paiement.factureId.clientId.email || '';
        } else {
          paiementClientId = paiement.factureId.clientId.toString();
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
          userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
        } else {
          userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
        }

        // Vérifier si l'ID correspond OU si l'email correspond
        const matchById = paiementClientId === userClientId;
        const matchByEmail = paiementClientEmail && currentUser.email &&
                            paiementClientEmail.toLowerCase() === currentUser.email.toLowerCase();

        return matchById || matchByEmail;
      });

      // Filtrer par période si nécessaire
      let filteredPaiements = paiementsData;

      if (period !== 'allTime') {
        const dateRangeObj = period === 'custom' && customRange
          ? customRange
          : getDateRangeFromId(period);

        filteredPaiements = paiementsData.filter(paiement => {
          const paiementDate = new Date(paiement.datePaiement);
          return paiementDate >= dateRangeObj.startDate && paiementDate <= dateRangeObj.endDate;
        });
      }

      setPaiements(paiementsData);
      setFilteredPaiements(filteredPaiements);

      // Calculer les statistiques
      calculateStats(filteredPaiements);

      // Récupérer les informations de l'entreprise si disponibles
      if (paiementsData.length > 0 &&
          paiementsData[0].factureId &&
          paiementsData[0].factureId.vendeurId) {

        // Utiliser les informations du vendeur comme informations d'entreprise
        setEntrepriseInfo({
          nom: paiementsData[0].factureId.vendeurId.nom || 'Entreprise',
          email: paiementsData[0].factureId.vendeurId.email || '',
        });
      }

      setLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement des paiements:', error);
      setError('Erreur lors du chargement des données. Veuillez réessayer.');
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchData(dateRange, customDateRange);
    }
  }, [currentUser, dateRange, customDateRange]);

  // Appliquer les filtres de recherche
  useEffect(() => {
    if (!searchTerm) {
      // Si aucun terme de recherche, utiliser les paiements filtrés par date
      return;
    }

    const search = searchTerm.toLowerCase();
    const filtered = filteredPaiements.filter(paiement =>
      (paiement.factureId && paiement.factureId.numero && paiement.factureId.numero.toLowerCase().includes(search)) ||
      (paiement.reference && paiement.reference.toLowerCase().includes(search)) ||
      (paiement.modePaiement && paiement.modePaiement.toLowerCase().includes(search))
    );

    setFilteredPaiements(filtered);
  }, [searchTerm]);

  // Gérer le changement de période
  const handleDateRangeChange = (range, customRange = null) => {
    setDateRange(range);
    setCustomDateRange(customRange);
  };

  // Gérer le rafraîchissement des données
  const handleRefresh = () => {
    fetchData(dateRange, customDateRange);
    setSuccess('Données actualisées avec succès');

    // Effacer le message de succès après 5 secondes
    setTimeout(() => {
      setSuccess('');
    }, 5000);
  };

  const handleViewReceipt = (paiement) => {
    setSelectedPaiement(paiement);
    setOpenReceiptDialog(true);
  };

  const handleCloseReceiptDialog = () => {
    setOpenReceiptDialog(false);
    setSelectedPaiement(null);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* En-tête avec design amélioré */}
      <Card
        elevation={3}
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          borderRadius: 2,
          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
          position: 'relative',
          overflow: 'hidden',
          borderLeft: '4px solid',
          borderColor: 'primary.main'
        }}
      >
        <CardContent sx={{ p: 3, position: 'relative', zIndex: 2 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}>
            <Box>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 0.5,
                  letterSpacing: 0.7
                }}
              >
                MES PAIEMENTS
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600
                }}
              >
                Consultez l'historique de vos paiements et téléchargez vos reçus.
              </Typography>
            </Box>
            <Stack direction="row" spacing={2}>
              <SimpleDateFilter
                onDateRangeChange={handleDateRangeChange}
                onRefresh={handleRefresh}
                initialRange={dateRange}
                showRefreshButton={false}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={handleRefresh}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
                disabled={loading}
                sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                  minWidth: 130
                }}
              >
                {loading ? 'Actualisation...' : 'Actualiser'}
              </Button>
            </Stack>
          </Box>
        </CardContent>
      </Card>

      {/* Messages de succès/erreur */}
      {success && (
        <Alert
          severity="success"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 2px 10px rgba(0,0,0,0.08)'
          }}
          onClose={() => setSuccess('')}
        >
          {success}
        </Alert>
      )}

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 2px 10px rgba(0,0,0,0.08)'
          }}
          onClose={() => setError('')}
        >
          {error}
        </Alert>
      )}

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card
            component={motion.div}
            variants={itemVariants}
            elevation={2}
            sx={{
              borderRadius: 2,
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Total des paiements
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalPaiements}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: 'primary.main' }}>
                  <PaymentIcon />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Montant total:
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {formatCurrency(stats.totalMontant)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={8}>
          <Card
            component={motion.div}
            variants={itemVariants}
            elevation={2}
            sx={{
              borderRadius: 2,
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Derniers paiements
                </Typography>
                <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: 'success.main' }}>
                  <MoneyIcon />
                </Avatar>
              </Box>

              {stats.paiementsRecents.length > 0 ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {stats.paiementsRecents.map((paiement, index) => (
                    <Box
                      key={paiement._id || index}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        p: 1,
                        borderRadius: 1,
                        bgcolor: alpha(theme.palette.background.default, 0.5),
                        '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.05) }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            mr: 1.5,
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main
                          }}
                        >
                          {getPaymentModeIcon(paiement.modePaiement)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {paiement.factureId?.numero || 'N/A'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatDate(paiement.datePaiement)}
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        {formatCurrency(paiement.montant)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                  Aucun paiement récent
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filtres améliorés */}
      <Paper
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: 'white',
          boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
        }} />

        <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
          <FilterListIcon sx={{ mr: 1 }} fontSize="small" />
          Filtrer les paiements
        </Typography>

        <TextField
          fullWidth
          variant="outlined"
          size="small"
          placeholder="Rechercher par numéro de facture ou référence..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              transition: 'box-shadow 0.3s',
              '&:hover': {
                boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.1)'
              },
              '&.Mui-focused': {
                boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)'
              }
            }
          }}
        />
      </Paper>

      {/* Tableau des paiements amélioré */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          mb: 4
        }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid rgba(0,0,0,0.08)' }}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
            <ReceiptLongIcon sx={{ mr: 1 }} fontSize="small" />
            Liste des paiements
          </Typography>
          <Box>
            <Chip
              label={`${filteredPaiements.length} paiement${filteredPaiements.length > 1 ? 's' : ''}`}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ borderRadius: 1 }}
            />
          </Box>
        </Box>

        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow sx={{
                '& .MuiTableCell-head': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                  fontWeight: 'bold'
                }
              }}>
                <TableCell>Facture</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Montant</TableCell>
                <TableCell>Mode de paiement</TableCell>
                <TableCell>Référence</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredPaiements.length > 0 ? (
                filteredPaiements.map((paiement) => (
                  <TableRow
                    key={paiement._id}
                    hover
                    sx={{
                      transition: 'background-color 0.2s',
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.03),
                        cursor: 'pointer'
                      }
                    }}
                  >
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {paiement.factureId ? paiement.factureId.numero : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>{formatDate(paiement.datePaiement)}</TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(paiement.montant)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getPaymentModeIcon(paiement.modePaiement)}
                        label={getPaymentModeLabel(paiement.modePaiement)}
                        size="small"
                        variant="outlined"
                        sx={{
                          fontWeight: 500,
                          borderRadius: '4px',
                          '& .MuiChip-icon': {
                            fontSize: '0.875rem',
                            marginLeft: '4px'
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {paiement.reference || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Tooltip title="Voir le reçu">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleViewReceipt(paiement)}
                            sx={{
                              backgroundColor: alpha(theme.palette.primary.main, 0.1),
                              mr: 1,
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                              }
                            }}
                          >
                            <ReceiptIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Télécharger le reçu">
                          <IconButton
                            size="small"
                            onClick={() => handleViewReceipt(paiement)}
                            sx={{
                              color: theme.palette.text.secondary,
                              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.text.secondary, 0.2),
                              }
                            }}
                          >
                            <DownloadIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
                      <PaymentIcon sx={{ fontSize: 60, color: alpha(theme.palette.text.secondary, 0.2), mb: 2 }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        Aucun paiement trouvé
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Modifiez vos critères de recherche ou consultez tous les paiements.
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Receipt Dialog */}
      {selectedPaiement && (
        <PaiementReceiptDialog
          open={openReceiptDialog}
          onClose={handleCloseReceiptDialog}
          paiement={selectedPaiement}
          facture={selectedPaiement.factureId}
          entreprise={entrepriseInfo}
        />
      )}
    </Box>
  );
};

export default ClientPaiements;
