{"version": 3, "sources": ["../../../../src/server/normalizers/request/suffix.ts"], "sourcesContent": ["import type { Normalizer } from '../normalizer'\n\nexport class SuffixPathnameNormalizer implements Normalizer {\n  constructor(private readonly suffix: string) {}\n\n  public match(pathname: string) {\n    // If the pathname doesn't end in the suffix, we don't match.\n    if (!pathname.endsWith(this.suffix)) return false\n\n    return true\n  }\n\n  public normalize(pathname: string, matched?: boolean): string {\n    // If we're not matched and we don't match, we don't need to normalize.\n    if (!matched && !this.match(pathname)) return pathname\n\n    return pathname.substring(0, pathname.length - this.suffix.length)\n  }\n}\n"], "names": ["SuffixPathnameNormalizer", "constructor", "suffix", "match", "pathname", "endsWith", "normalize", "matched", "substring", "length"], "mappings": "AAEA,OAAO,MAAMA;IACXC,YAAY,AAAiBC,MAAc,CAAE;aAAhBA,SAAAA;IAAiB;IAEvCC,MAAMC,QAAgB,EAAE;QAC7B,6DAA6D;QAC7D,IAAI,CAACA,SAASC,QAAQ,CAAC,IAAI,CAACH,MAAM,GAAG,OAAO;QAE5C,OAAO;IACT;IAEOI,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,OAAOA,SAASI,SAAS,CAAC,GAAGJ,SAASK,MAAM,GAAG,IAAI,CAACP,MAAM,CAACO,MAAM;IACnE;AACF"}