const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { verifyToken, isAdmin } = require('../middleware/authMiddleware');
const Facture = require('../models/FactureModel');
const Devis = require('../models/DevisModel');
const Client = require('../models/ClientModel');
const User = require('../models/UserModel');
const Produit = require('../models/ProduitModel');

// Helper function to get date range
const getDateRange = (period, targetDate = new Date()) => {
  const startDate = new Date(targetDate);
  const endDate = new Date(targetDate);

  switch (period) {
    case 'daily':
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'weekly':
      const day = startDate.getDay();
      const diff = startDate.getDate() - day + (day === 0 ? -6 : 1);
      startDate.setDate(diff);
      startDate.setHours(0, 0, 0, 0);
      endDate.setDate(startDate.getDate() + 6);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'monthly':
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'quarterly':
      const quarter = Math.floor(startDate.getMonth() / 3);
      startDate.setMonth(quarter * 3);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(quarter * 3 + 3);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'yearly':
      startDate.setMonth(0);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(11);
      endDate.setDate(31);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'all-time':
      startDate.setFullYear(2000);
      startDate.setMonth(0);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      break;
    default:
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
  }

  return { startDate, endDate };
};

// GET /admin/analytics/system-metrics - Get system-wide metrics for admin dashboard
router.get('/admin/analytics/system-metrics', verifyToken, isAdmin, async (req, res) => {
  try {
    const { period = 'monthly' } = req.query;
    const { startDate, endDate } = getDateRange(period);

    // Previous period for comparison
    const previousPeriodEndDate = new Date(startDate);
    previousPeriodEndDate.setDate(previousPeriodEndDate.getDate() - 1);

    let previousPeriodStartDate;
    if (period === 'monthly') {
      previousPeriodStartDate = new Date(startDate);
      previousPeriodStartDate.setMonth(previousPeriodStartDate.getMonth() - 1);
    } else if (period === 'quarterly') {
      previousPeriodStartDate = new Date(startDate);
      previousPeriodStartDate.setMonth(previousPeriodStartDate.getMonth() - 3);
    } else if (period === 'yearly') {
      previousPeriodStartDate = new Date(startDate);
      previousPeriodStartDate.setFullYear(previousPeriodStartDate.getFullYear() - 1);
    } else {
      // Default to previous 30 days
      previousPeriodStartDate = new Date(startDate);
      previousPeriodStartDate.setDate(previousPeriodStartDate.getDate() - 30);
    }

    // Get current period metrics
    const [invoiceStats, quoteStats, clientCount, userCount] = await Promise.all([
      Facture.aggregate([
        {
          $match: {
            dateEmission: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: "$statut",
            count: { $sum: 1 },
            total: { $sum: "$total" }
          }
        }
      ]),
      Devis.aggregate([
        {
          $match: {
            dateEmission: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: "$statut",
            count: { $sum: 1 }
          }
        }
      ]),
      Client.countDocuments({
        createdAt: { $gte: startDate, $lte: endDate }
      }),
      User.countDocuments({
        createdAt: { $gte: startDate, $lte: endDate }
      })
    ]);

    // Get previous period metrics for comparison
    const [previousInvoiceStats] = await Promise.all([
      Facture.aggregate([
        {
          $match: {
            dateEmission: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: "$total" }
          }
        }
      ])
    ]);

    // Calculate total revenue for current period
    let totalRevenue = 0;
    let totalInvoices = 0;

    invoiceStats.forEach(stat => {
      totalInvoices += stat.count;
      if (stat._id === 'PAID') {
        totalRevenue += stat.total;
      }
    });

    // Calculate total quotes
    let totalQuotes = 0;
    quoteStats.forEach(stat => {
      totalQuotes += stat.count;
    });

    // Calculate revenue growth
    const previousRevenue = previousInvoiceStats.length > 0 ? previousInvoiceStats[0].total : 0;
    const revenueGrowth = previousRevenue > 0
      ? ((totalRevenue - previousRevenue) / previousRevenue) * 100
      : 0;

    // Calculate document growth (mock value replaced with real calculation)
    const [previousDocumentStats] = await Promise.all([
      Promise.all([
        Facture.countDocuments({
          dateEmission: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate }
        }),
        Devis.countDocuments({
          dateEmission: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate }
        })
      ])
    ]);

    const previousTotalDocuments = previousDocumentStats[0] + previousDocumentStats[1];
    const currentTotalDocuments = totalInvoices + totalQuotes;

    const documentGrowth = previousTotalDocuments > 0
      ? ((currentTotalDocuments - previousTotalDocuments) / previousTotalDocuments) * 100
      : 0;

    // Prepare response with default values for empty data
    const systemMetrics = {
      totalRevenue: totalRevenue || 0,
      revenueGrowth: parseFloat(revenueGrowth.toFixed(1)) || 0,
      totalDocuments: currentTotalDocuments || 0,
      documentGrowth: parseFloat(documentGrowth.toFixed(1)) || 0,
      invoices: totalInvoices || 0,
      quotes: totalQuotes || 0,
      newClients: clientCount || 0,
      newUsers: userCount || 0,
      period,
      dateRange: {
        start: startDate,
        end: endDate
      }
    };

    res.status(200).json(systemMetrics);
  } catch (error) {
    console.error('Error fetching system metrics:', error);
    res.status(500).json({
      error: 'Error fetching system metrics',
      message: error.message
    });
  }
});

// GET /admin/analytics/system-health - Get system health information
router.get('/admin/analytics/system-health', verifyToken, isAdmin, async (req, res) => {
  try {
    // Calculer l'utilisation réelle du stockage en comptant les documents
    const [invoiceCount, quoteCount, clientCount, userCount, productCount] = await Promise.all([
      Facture.countDocuments(),
      Devis.countDocuments(),
      Client.countDocuments(),
      User.countDocuments(),
      Produit.countDocuments()
    ]);

    // Estimer l'utilisation du stockage (en MB) basée sur le nombre de documents
    // Ces valeurs sont des estimations et devraient être ajustées selon les besoins réels
    const estimatedStorageUsed = (
      (invoiceCount * 0.05) + // ~50KB par facture
      (quoteCount * 0.04) +   // ~40KB par devis
      (clientCount * 0.02) +  // ~20KB par client
      (userCount * 0.01) +    // ~10KB par utilisateur
      (productCount * 0.03)   // ~30KB par produit
    );

    // Valeur totale fixe pour la démonstration (500 MB)
    const totalStorage = 500;

    // Calculer l'espace libre
    const freeStorage = Math.max(0, totalStorage - estimatedStorageUsed);

    // This would typically come from monitoring systems
    // For now, we'll return calculated data with real document counts
    const systemHealth = {
      status: 'Normal',
      uptime: '99.9%',
      responseTime: '120ms',
      errorRate: '0.05%',
      lastIncident: null,
      storage: {
        total: totalStorage,
        used: Math.min(totalStorage, Math.round(estimatedStorageUsed * 100) / 100),
        free: Math.round(freeStorage * 100) / 100
      },
      documents: {
        invoices: invoiceCount || 0,
        quotes: quoteCount || 0,
        clients: clientCount || 0,
        users: userCount || 0,
        products: productCount || 0,
        total: (invoiceCount + quoteCount + clientCount + userCount + productCount) || 0
      }
    };

    res.status(200).json(systemHealth);
  } catch (error) {
    console.error('Error fetching system health:', error);
    res.status(500).json({
      error: 'Error fetching system health',
      message: error.message
    });
  }
});

module.exports = router;
