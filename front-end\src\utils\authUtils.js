"use client"
import { Navigate, useLocation } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"
import { useEffect, useState } from "react"
import authService from "../services/authService"

/**
 * Get the authentication token from localStorage
 * @returns {string|null} The authentication token or null if not found
 */
export const getAuthToken = () => {
  return localStorage.getItem("token");
}

// Composant pour protéger les routes
export const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()
  const location = useLocation()
  const [verifying, setVerifying] = useState(true)
  const [authenticated, setAuthenticated] = useState(false)

  useEffect(() => {
    const verifyAuthentication = async () => {
      setVerifying(true)
      try {
        // Si le token est présent dans le localStorage
        const token = localStorage.getItem("token")
        const userStr = localStorage.getItem("user")

        if (token && userStr) {
          // Effectuer une vérification du token auprès du serveur
          const isValid = await authService.verifyToken()
          setAuthenticated(isValid)
        } else {
          setAuthenticated(false)
        }
      } catch (error) {
        console.error("Erreur lors de la vérification du token:", error)
        setAuthenticated(false)
      } finally {
        setVerifying(false)
      }
    }

    verifyAuthentication()
  }, [])

  // Afficher un indicateur de chargement pendant la vérification
  if (loading || verifying) {
    return <div>Chargement...</div>
  }

  // Utiliser l'état local pour décider de la redirection
  // Cela garantit que la vérification du token est effectuée avant toute redirection
  if (!isAuthenticated && !authenticated) {
    // Rediriger vers la page de connexion avec l'URL de retour
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return children
}

// Vérifier les rôles utilisateur
export const hasRole = (user, roles = []) => {
  if (!user || !user.role) return false

  if (typeof roles === "string") {
    roles = [roles]
  }

  return roles.includes(user.role)
}

// Composant pour les routes basées sur les rôles
export const RoleBasedRoute = ({ children, roles }) => {
  const { currentUser, isAuthenticated, loading } = useAuth()
  const location = useLocation()
  const [verifying, setVerifying] = useState(true)
  const [authenticated, setAuthenticated] = useState(false)

  useEffect(() => {
    const verifyAuthentication = async () => {
      setVerifying(true)
      try {
        // Si le token est présent dans le localStorage
        const token = localStorage.getItem("token")
        const userStr = localStorage.getItem("user")

        if (token && userStr) {
          // Effectuer une vérification du token auprès du serveur
          const isValid = await authService.verifyToken()
          setAuthenticated(isValid)
        } else {
          setAuthenticated(false)
        }
      } catch (error) {
        console.error("Erreur lors de la vérification du token:", error)
        setAuthenticated(false)
      } finally {
        setVerifying(false)
      }
    }

    verifyAuthentication()
  }, [])

  // Afficher un indicateur de chargement pendant la vérification
  if (loading || verifying) {
    return <div>Chargement...</div>
  }

  // Utiliser l'état local pour décider de la redirection
  if (!isAuthenticated && !authenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Vérifier si l'utilisateur a le rôle requis
  if (!hasRole(currentUser, roles)) {
    // Rediriger vers la page d'accueil appropriée en fonction du rôle de l'utilisateur
    if (currentUser && currentUser.role) {
      if (currentUser.role === 'ADMIN') {
        return <Navigate to="/admin/analytics" replace />
      } else if (currentUser.role === 'RESPONSABLE') {
        return <Navigate to="/responsable/analytics" replace />
      } else if (currentUser.role === 'VENDEUR') {
        return <Navigate to="/vendeur/analytics" replace />
      } else if (currentUser.role === 'CLIENT') {
        return <Navigate to="/client/dashboard" replace />
      }
    }
    // Si aucun rôle correspondant, rediriger vers la page d'accueil
    return <Navigate to="/" replace />
  }

  // Accès autorisé pour les utilisateurs authentifiés avec le rôle approprié
  return children
}