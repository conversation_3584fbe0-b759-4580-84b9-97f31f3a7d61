{"version": 3, "sources": ["../../../src/build/next-config-ts/transpile-config.ts"], "sourcesContent": ["import type { Options as SWCOptions } from '@swc/core'\nimport type { CompilerOptions } from 'typescript'\nimport { join } from 'node:path'\nimport { readFile } from 'node:fs/promises'\nimport { deregisterHook, registerHook, requireFromString } from './require-hook'\nimport { parseJsonFile } from '../load-jsconfig'\n\nfunction resolveSWCOptions(\n  cwd: string,\n  compilerOptions: CompilerOptions\n): SWCOptions {\n  const resolvedBaseUrl = join(cwd, compilerOptions.baseUrl ?? '.')\n  return {\n    jsc: {\n      target: 'es5',\n      parser: {\n        syntax: 'typescript',\n      },\n      paths: compilerOptions.paths,\n      baseUrl: resolvedBaseUrl,\n    },\n    module: {\n      type: 'commonjs',\n    },\n    isModule: 'unknown',\n  } satisfies SWCOptions\n}\n\nasync function lazilyGetTSConfig(cwd: string) {\n  let tsConfig: { compilerOptions: CompilerOptions }\n  try {\n    tsConfig = parseJsonFile(join(cwd, 'tsconfig.json'))\n  } catch (error) {\n    // ignore if tsconfig.json does not exist\n    if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {\n      throw error\n    }\n    tsConfig = { compilerOptions: {} }\n  }\n\n  return tsConfig\n}\n\nexport async function transpileConfig({\n  nextConfigPath,\n  cwd,\n}: {\n  nextConfigPath: string\n  cwd: string\n}) {\n  let hasRequire = false\n  try {\n    const { compilerOptions } = await lazilyGetTSConfig(cwd)\n    const swcOptions = resolveSWCOptions(cwd, compilerOptions)\n\n    const nextConfigString = await readFile(nextConfigPath, 'utf8')\n    // lazy require swc since it loads React before even setting NODE_ENV\n    // resulting loading Development React on Production\n    const { transform } = require('../swc')\n    const { code } = await transform(nextConfigString, swcOptions)\n\n    // register require hook only if require exists\n    if (code.includes('require(')) {\n      registerHook(swcOptions)\n      hasRequire = true\n    }\n\n    // filename & extension don't matter here\n    return requireFromString(code, join(cwd, 'next.config.compiled.js'))\n  } catch (error) {\n    throw error\n  } finally {\n    if (hasRequire) {\n      deregisterHook()\n    }\n  }\n}\n"], "names": ["join", "readFile", "deregisterHook", "registerHook", "requireFromString", "parseJsonFile", "resolveSWCOptions", "cwd", "compilerOptions", "resolvedBaseUrl", "baseUrl", "jsc", "target", "parser", "syntax", "paths", "module", "type", "isModule", "lazilyGetTSConfig", "tsConfig", "error", "code", "transpileConfig", "nextConfigPath", "hasRequire", "swcOptions", "nextConfigString", "transform", "require", "includes"], "mappings": "AAEA,SAASA,IAAI,QAAQ,YAAW;AAChC,SAASC,QAAQ,QAAQ,mBAAkB;AAC3C,SAASC,cAAc,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,iBAAgB;AAChF,SAASC,aAAa,QAAQ,mBAAkB;AAEhD,SAASC,kBACPC,GAAW,EACXC,eAAgC;IAEhC,MAAMC,kBAAkBT,KAAKO,KAAKC,gBAAgBE,OAAO,IAAI;IAC7D,OAAO;QACLC,KAAK;YACHC,QAAQ;YACRC,QAAQ;gBACNC,QAAQ;YACV;YACAC,OAAOP,gBAAgBO,KAAK;YAC5BL,SAASD;QACX;QACAO,QAAQ;YACNC,MAAM;QACR;QACAC,UAAU;IACZ;AACF;AAEA,eAAeC,kBAAkBZ,GAAW;IAC1C,IAAIa;IACJ,IAAI;QACFA,WAAWf,cAAcL,KAAKO,KAAK;IACrC,EAAE,OAAOc,OAAO;QACd,yCAAyC;QACzC,IAAI,AAACA,MAAgCC,IAAI,KAAK,UAAU;YACtD,MAAMD;QACR;QACAD,WAAW;YAAEZ,iBAAiB,CAAC;QAAE;IACnC;IAEA,OAAOY;AACT;AAEA,OAAO,eAAeG,gBAAgB,EACpCC,cAAc,EACdjB,GAAG,EAIJ;IACC,IAAIkB,aAAa;IACjB,IAAI;QACF,MAAM,EAAEjB,eAAe,EAAE,GAAG,MAAMW,kBAAkBZ;QACpD,MAAMmB,aAAapB,kBAAkBC,KAAKC;QAE1C,MAAMmB,mBAAmB,MAAM1B,SAASuB,gBAAgB;QACxD,qEAAqE;QACrE,oDAAoD;QACpD,MAAM,EAAEI,SAAS,EAAE,GAAGC,QAAQ;QAC9B,MAAM,EAAEP,IAAI,EAAE,GAAG,MAAMM,UAAUD,kBAAkBD;QAEnD,+CAA+C;QAC/C,IAAIJ,KAAKQ,QAAQ,CAAC,aAAa;YAC7B3B,aAAauB;YACbD,aAAa;QACf;QAEA,yCAAyC;QACzC,OAAOrB,kBAAkBkB,MAAMtB,KAAKO,KAAK;IAC3C,EAAE,OAAOc,OAAO;QACd,MAAMA;IACR,SAAU;QACR,IAAII,YAAY;YACdvB;QACF;IACF;AACF"}