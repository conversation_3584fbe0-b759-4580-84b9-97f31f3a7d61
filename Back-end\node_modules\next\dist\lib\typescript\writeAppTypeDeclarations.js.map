{"version": 3, "sources": ["../../../src/lib/typescript/writeAppTypeDeclarations.ts"], "sourcesContent": ["import os from 'os'\nimport path from 'path'\nimport { promises as fs } from 'fs'\n\nexport async function writeAppTypeDeclarations({\n  baseDir,\n  imageImportsEnabled,\n  hasPagesDir,\n  hasAppDir,\n}: {\n  baseDir: string\n  imageImportsEnabled: boolean\n  hasPagesDir: boolean\n  hasAppDir: boolean\n}): Promise<void> {\n  // Reference `next` types\n  const appTypeDeclarations = path.join(baseDir, 'next-env.d.ts')\n\n  // Defaults EOL to system default\n  let eol = os.EOL\n  let currentContent: string | undefined\n\n  try {\n    currentContent = await fs.readFile(appTypeDeclarations, 'utf8')\n    // If file already exists then preserve its line ending\n    const lf = currentContent.indexOf('\\n', /* skip first so we can lf - 1 */ 1)\n\n    if (lf !== -1) {\n      if (currentContent[lf - 1] === '\\r') {\n        eol = '\\r\\n'\n      } else {\n        eol = '\\n'\n      }\n    }\n  } catch {}\n\n  /**\n   * \"Triple-slash directives\" used to create typings files for Next.js projects\n   * using Typescript .\n   *\n   * @see https://www.typescriptlang.org/docs/handbook/triple-slash-directives.html\n   */\n  const directives: string[] = [\n    // Include the core Next.js typings.\n    '/// <reference types=\"next\" />',\n  ]\n\n  if (imageImportsEnabled) {\n    directives.push('/// <reference types=\"next/image-types/global\" />')\n  }\n\n  if (hasAppDir && hasPagesDir) {\n    directives.push(\n      '/// <reference types=\"next/navigation-types/compat/navigation\" />'\n    )\n  }\n\n  // Push the notice in.\n  directives.push(\n    '',\n    '// NOTE: This file should not be edited',\n    `// see https://nextjs.org/docs/${hasAppDir ? 'app' : 'pages'}/api-reference/config/typescript for more information.`\n  )\n\n  const content = directives.join(eol) + eol\n\n  // Avoids an un-necessary write on read-only fs\n  if (currentContent === content) {\n    return\n  }\n  await fs.writeFile(appTypeDeclarations, content)\n}\n"], "names": ["writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "hasPagesDir", "hasAppDir", "appTypeDeclarations", "path", "join", "eol", "os", "EOL", "currentC<PERSON>nt", "fs", "readFile", "lf", "indexOf", "directives", "push", "content", "writeFile"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;2DAJP;6DACE;oBACc;;;;;;AAExB,eAAeA,yBAAyB,EAC7CC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,SAAS,EAMV;IACC,yBAAyB;IACzB,MAAMC,sBAAsBC,aAAI,CAACC,IAAI,CAACN,SAAS;IAE/C,iCAAiC;IACjC,IAAIO,MAAMC,WAAE,CAACC,GAAG;IAChB,IAAIC;IAEJ,IAAI;QACFA,iBAAiB,MAAMC,YAAE,CAACC,QAAQ,CAACR,qBAAqB;QACxD,uDAAuD;QACvD,MAAMS,KAAKH,eAAeI,OAAO,CAAC,MAAM,+BAA+B,GAAG;QAE1E,IAAID,OAAO,CAAC,GAAG;YACb,IAAIH,cAAc,CAACG,KAAK,EAAE,KAAK,MAAM;gBACnCN,MAAM;YACR,OAAO;gBACLA,MAAM;YACR;QACF;IACF,EAAE,OAAM,CAAC;IAET;;;;;GAKC,GACD,MAAMQ,aAAuB;QAC3B,oCAAoC;QACpC;KACD;IAED,IAAId,qBAAqB;QACvBc,WAAWC,IAAI,CAAC;IAClB;IAEA,IAAIb,aAAaD,aAAa;QAC5Ba,WAAWC,IAAI,CACb;IAEJ;IAEA,sBAAsB;IACtBD,WAAWC,IAAI,CACb,IACA,2CACA,CAAC,+BAA+B,EAAEb,YAAY,QAAQ,QAAQ,sDAAsD,CAAC;IAGvH,MAAMc,UAAUF,WAAWT,IAAI,CAACC,OAAOA;IAEvC,+CAA+C;IAC/C,IAAIG,mBAAmBO,SAAS;QAC9B;IACF;IACA,MAAMN,YAAE,CAACO,SAAS,CAACd,qBAAqBa;AAC1C"}