import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Grid,
  Typography,
  Divider,
  Box,
  Alert,
  InputAdornment
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { fr } from 'date-fns/locale';
import { formatCurrency } from '../utils/formatters';

const modesPaiement = [
  { value: 'BANK_TRANSFER', label: 'Virement bancaire' },
  { value: 'CHECK', label: 'Chèque' },
  { value: 'CASH', label: 'Espèces' },
];

const PaiementModal = ({ open, onClose, facture, onSave }) => {
  const [modePaiement, setModePaiement] = useState('BANK_TRANSFER');
  const [datePaiement, setDatePaiement] = useState(new Date());
  const [montant, setMontant] = useState(facture?.total || 0);
  const [reference, setReference] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = () => {
    if (montant <= 0) {
      setError('Le montant doit être supérieur à 0');
      return;
    }

    const paiementData = {
      montant: parseFloat(montant),
      modePaiement,
      datePaiement,
      reference,
      factureId: facture._id,
    };

    onSave(paiementData);
    onClose();
  };

  const handleMontantChange = (e) => {
    const value = parseFloat(e.target.value) || 0;
    setMontant(value);
    if (value > 0) setError('');
  };

  if (!facture) return null;

  const montantRestant = facture.total - montant;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>Enregistrer un paiement</DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <Typography variant="h6">
              Facture #{facture.numero || facture.numéro}
            </Typography>
            <Typography variant="body2">
              Client: {facture.clientId?.nom || 'N/A'}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>

          <Grid item xs={6}>
            <Typography>Montant total:</Typography>
            <Typography variant="h6">
              {facture.total.toFixed(2)} DT
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography>Montant restant:</Typography>
            <Typography variant="h6" color={montantRestant > 0 ? 'error' : 'success'}>
              {montantRestant.toFixed(2)} DT
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              select
              label="Mode de paiement"
              value={modePaiement}
              onChange={(e) => setModePaiement(e.target.value)}
              fullWidth
            >
              {modesPaiement.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12} md={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
              <DatePicker
                label="Date de paiement"
                value={datePaiement}
                onChange={(newValue) => setDatePaiement(newValue)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Montant"
              type="number"
              value={montant}
              onChange={handleMontantChange}
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start"></InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">DT</InputAdornment>
                ),
                inputProps: {
                  min: 0,
                  max: facture.total,
                  step: 1
                }
              }}
              error={!!error}
              helperText={error}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Référence"
              value={reference}
              onChange={(e) => setReference(e.target.value)}
              fullWidth
              placeholder="Numéro de chèque, référence virement..."
            />
          </Grid>

          {montantRestant < 0 && (
            <Grid item xs={12}>
              <Alert severity="warning">
                Le montant saisi est supérieur au montant dû. Un avoir sera créé.
              </Alert>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Annuler</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={!!error}
        >
          Enregistrer le paiement
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaiementModal;