{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { fillCacheWithNewSubTreeData } from './fill-cache-with-new-subtree-data'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\nexport function applyFlightData(\n  navigatedAt: number,\n  existingCache: CacheNode,\n  cache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): boolean {\n  // The one before last item is the router state tree patch\n  const { tree: treePatch, seedData, head, isRootRender } = flightData\n\n  // Handles case where prefetch only returns the router tree patch without rendered components.\n  if (seedData === null) {\n    return false\n  }\n\n  if (isRootRender) {\n    const rsc = seedData[1]\n    const loading = seedData[3]\n    cache.loading = loading\n    cache.rsc = rsc\n    // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n    // this path during a navigation, but until PPR is fully implemented\n    // yet it's possible the existing node does have a non-null\n    // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n    // old behavior — no PPR value.\n    cache.prefetchRsc = null\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      cache,\n      existingCache,\n      treePatch,\n      seedData,\n      head,\n      prefetchEntry\n    )\n  } else {\n    // Copy rsc for the root node of the cache.\n    cache.rsc = existingCache.rsc\n    // This is a PPR-only field. Unlike the previous branch, since we're\n    // just cloning the existing cache node, we might as well keep the\n    // PPR value, if it exists.\n    cache.prefetchRsc = existingCache.prefetchRsc\n    cache.parallelRoutes = new Map(existingCache.parallelRoutes)\n    cache.loading = existingCache.loading\n    // Create a copy of the existing cache with the rsc applied.\n    fillCacheWithNewSubTreeData(\n      navigatedAt,\n      cache,\n      existingCache,\n      flightData,\n      prefetchEntry\n    )\n  }\n\n  return true\n}\n"], "names": ["fillLazyItemsTillLeafWithHead", "fillCacheWithNewSubTreeData", "applyFlightData", "navigatedAt", "existingCache", "cache", "flightData", "prefetchEntry", "tree", "treePatch", "seedData", "head", "isRootRender", "rsc", "loading", "prefetchRsc", "parallelRoutes", "Map"], "mappings": "AACA,SAASA,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,2BAA2B,QAAQ,qCAAoC;AAIhF,OAAO,SAASC,gBACdC,WAAmB,EACnBC,aAAwB,EACxBC,KAAgB,EAChBC,UAAgC,EAChCC,aAAkC;IAElC,0DAA0D;IAC1D,MAAM,EAAEC,MAAMC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAE,GAAGN;IAE1D,8FAA8F;IAC9F,IAAII,aAAa,MAAM;QACrB,OAAO;IACT;IAEA,IAAIE,cAAc;QAChB,MAAMC,MAAMH,QAAQ,CAAC,EAAE;QACvB,MAAMI,UAAUJ,QAAQ,CAAC,EAAE;QAC3BL,MAAMS,OAAO,GAAGA;QAChBT,MAAMQ,GAAG,GAAGA;QACZ,kEAAkE;QAClE,oEAAoE;QACpE,2DAA2D;QAC3D,kEAAkE;QAClE,+BAA+B;QAC/BR,MAAMU,WAAW,GAAG;QACpBf,8BACEG,aACAE,OACAD,eACAK,WACAC,UACAC,MACAJ;IAEJ,OAAO;QACL,2CAA2C;QAC3CF,MAAMQ,GAAG,GAAGT,cAAcS,GAAG;QAC7B,oEAAoE;QACpE,kEAAkE;QAClE,2BAA2B;QAC3BR,MAAMU,WAAW,GAAGX,cAAcW,WAAW;QAC7CV,MAAMW,cAAc,GAAG,IAAIC,IAAIb,cAAcY,cAAc;QAC3DX,MAAMS,OAAO,GAAGV,cAAcU,OAAO;QACrC,4DAA4D;QAC5Db,4BACEE,aACAE,OACAD,eACAE,YACAC;IAEJ;IAEA,OAAO;AACT"}