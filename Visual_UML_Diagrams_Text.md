# Visual UML Use Case Diagrams (Text Format)

## 1. CLIENT Use Case Diagram

```
                    ┌─────────────────┐
                    │   S'authentifier │
                    │  (email ou CIN)  │
                    └─────────────────┘
                             │
                             │ <<include>>
                             ▼
    ┌─────────────────────────────────────────────────────────┐
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Consulter       │    │ G<PERSON><PERSON> les       │            │
    │  │ tableau de bord │    │ documents       │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                  │                      │
    │                                  ├─ Consulter factures │
    │                                  ├─ Consulter devis    │
    │                                  └─ Consulter bons     │
    │                                     de livraison       │
    │                                                         │
○   │  ┌─────────────────┐    ┌─────────────────┐            │
│   │  │ Effectuer       │    │ Demander        │            │
│   │  │ paiements       │    │ devis           │            │
CLIENT │ └─────────────────┘    └─────────────────┘            │
│   │           │                                             │
    │           ├─ Payer par virement                         │
    │           ├─ Payer par chèque                           │
    │           └─ Payer par espèces                          │
    │                                                         │
    │  ┌─────────────────┐                                   │
    │  │ G<PERSON><PERSON> profil    │                                   │
    │  └─────────────────┘                                   │
    │                                                         │
    └─────────────────────────────────────────────────────────┘
```

## 2. VENDEUR Use Case Diagram

```
                    ┌─────────────────┐
                    │   S'authentifier │
                    └─────────────────┘
                             │
                             │ <<include>>
                             ▼
    ┌─────────────────────────────────────────────────────────┐
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Gérer tableau   │    │ Gérer les       │            │
    │  │ de bord         │    │ documents       │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                  │                      │
    │                                  ├─ Gérer factures     │
    │                                  ├─ Gérer devis        │
    │                                  └─ Gérer bons de      │
    │                                     livraison          │
    │                                                         │
○   │  ┌─────────────────┐    ┌─────────────────┐            │
│   │  │ Gérer les       │    │ Gérer les       │            │
│   │  │ clients         │    │ produits        │            │
VENDEUR │ └─────────────────┘    └─────────────────┘            │
│   │           │                         │                   │
    │           ├─ Ajouter client         ├─ Ajouter produit │
    │           ├─ Modifier client        ├─ Modifier produit│
    │           └─ Supprimer client       └─ Gérer stock     │
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Gérer les       │    │ Gérer son       │            │
    │  │ livreurs        │    │ profil          │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                                         │
    └─────────────────────────────────────────────────────────┘
```

## 3. RESPONSABLE D'ENTREPRISE Use Case Diagram

```
                    ┌─────────────────┐
                    │   S'authentifier │
                    └─────────────────┘
                             │
                             │ <<include>>
                             ▼
    ┌─────────────────────────────────────────────────────────┐
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Gérer tableau   │    │ Gérer l'équipe  │            │
    │  │ de bord         │    │                 │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                  │                      │
    │                                  ├─ Gérer vendeurs     │
    │                                  └─ Gérer livreurs     │
    │                                                         │
○   │  ┌─────────────────┐    ┌─────────────────┐            │
│   │  │ Gérer les       │    │ Gérer           │            │
│   │  │ documents       │    │ l'entreprise    │            │
RESPONSABLE │ └─────────────────┘    └─────────────────┘            │
│   │                                  │                      │
    │                                  ├─ Configurer infos   │
    │                                  ├─ Personnaliser      │
    │                                  │   templates          │
    │                                  └─ Gérer paramètres   │
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Gérer           │    │ Gérer son       │            │
    │  │ abonnement      │    │ profil          │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                                         │
    └─────────────────────────────────────────────────────────┘
```

## 4. ADMIN Use Case Diagram

```
                    ┌─────────────────┐
                    │   S'authentifier │
                    └─────────────────┘
                             │
                             │ <<include>>
                             ▼
    ┌─────────────────────────────────────────────────────────┐
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Gérer tableau   │    │ Gérer les       │            │
    │  │ de bord         │    │ utilisateurs    │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                  │                      │
    │                                  ├─ Gérer entreprises  │
    │                                  ├─ Gérer vendeurs     │
    │                                  └─ Gérer clients      │
    │                                                         │
○   │  ┌─────────────────┐    ┌─────────────────┐            │
│   │  │ Gérer les       │    │ Gérer les       │            │
│   │  │ templates       │    │ abonnements     │            │
ADMIN  │ └─────────────────┘    └─────────────────┘            │
│   │           │                         │                   │
    │           ├─ Créer templates        ├─ Créer plans     │
    │           ├─ Modifier templates     ├─ Modifier tarifs │
    │           └─ Configurer options     └─ Gérer           │
    │                                       renouvellements  │
    │                                                         │
    │  ┌─────────────────┐    ┌─────────────────┐            │
    │  │ Gérer paramètres│    │ Gérer son       │            │
    │  │ système         │    │ profil          │            │
    │  └─────────────────┘    └─────────────────┘            │
    │                                                         │
    └─────────────────────────────────────────────────────────┘
```

## Key Relationships and Extensions

### Include Relationships:
- All use cases include "S'authentifier" (Authentication required)

### Extend Relationships:
- Email notifications extend document management
- PDF generation extends all document operations
- Stock updates extend product sales
- Subscription monitoring extends enterprise management
- Automatic suspension extends subscription expiry

### Actor Relationships:
- RESPONSABLE manages VENDEUR accounts
- VENDEUR serves CLIENT needs
- ADMIN oversees all system users
- CLIENT interacts with documents created by VENDEUR

## New Features Highlighted:

### 🆕 Bon de Livraison (Delivery Notes):
- Create, modify, track delivery notes
- Assign delivery personnel
- PDF generation and email sending
- Delivery status tracking

### 🆕 Livreur Management:
- Add/modify delivery personnel
- Track availability and performance
- Assign delivery zones
- Monitor delivery statistics

### 🆕 Enhanced Authentication:
- CIN-based login for clients
- Automatic role detection
- Simplified login process

### 🆕 Subscription Management:
- Real-time subscription monitoring
- Automatic account suspension
- Renewal request handling
- Usage limit tracking

### 🆕 Template System:
- Standard and Moderne base templates
- Color and logo customization
- Professional PDF generation
- Enterprise branding options
