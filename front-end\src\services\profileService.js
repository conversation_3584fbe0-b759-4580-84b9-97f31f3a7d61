import api from "./api";

const profileService = {
  // Récupérer les données du profil de l'utilisateur connecté
  getProfile: async () => {
    try {
      const response = await api.get("/profile");
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la récupération du profil");
    }
  },

  // Mettre à jour les données du profil
  updateProfile: async (profileData) => {
    try {
      const response = await api.put("/profile", profileData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la mise à jour du profil");
    }
  },

  // Télécharger une image de profil
  uploadProfileImage: async (formData) => {
    try {
      const response = await api.post("/profile/upload-image", formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors du téléchargement de l'image de profil");
    }
  },

  // Changer le mot de passe
  changePassword: async (passwordData) => {
    try {
      const response = await api.post("/profile/change-password", passwordData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors du changement de mot de passe");
    }
  },

  // Récupérer l'historique des activités
  getActivities: async () => {
    try {
      const response = await api.get("/profile/activities");
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la récupération des activités");
    }
  },

  // Récupérer les préférences de l'utilisateur
  getPreferences: async () => {
    try {
      const response = await api.get("/profile/preferences");
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la récupération des préférences");
    }
  },

  // Mettre à jour les préférences de l'utilisateur
  updatePreferences: async (preferencesData) => {
    try {
      const response = await api.put("/profile/preferences", preferencesData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la mise à jour des préférences");
    }
  }
};

export default profileService;
