import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  Dialog,
  Chip,
  Tabs,
  Tab,
  Divider,
  Avatar,
  Card,
  CardContent,
  Tooltip,
  Badge,
  LinearProgress,
  GlobalStyles
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Category as CategoryIcon,
  Inventory as InventoryIcon,
  List as ListIcon,
  Image as ImageIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import ProduitForm from '../components/ProduitForm';
import StockEvolution from '../components/StockEvolution';
import produitService from '../services/produitService';
import { formatCurrency, formatStockQuantity } from '../utils/formatters';

// Global styles for animations
const globalStyles = (
  <GlobalStyles
    styles={{
      '@keyframes gradientAnimation': {
        '0%': {
          backgroundPosition: '0% 50%'
        },
        '50%': {
          backgroundPosition: '100% 50%'
        },
        '100%': {
          backgroundPosition: '0% 50%'
        }
      }
    }}
  />
);

const categories = [
  'Service',
  'Produit physique',
  'Logiciel',
  'Abonnement',
  'Formation'
];

// Function to determine the color for category chips
const getCategoryColor = (category) => {
  switch(category) {
    case 'Service':
      return 'info';
    case 'Produit physique':
      return 'primary';
    case 'Logiciel':
      return 'secondary';
    case 'Abonnement':
      return 'success';
    case 'Formation':
      return 'warning';
    default:
      return 'default';
  }
};

const Produits = () => {
  const [produits, setProduits] = useState([]);
  const [filteredProduits, setFilteredProduits] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [currentProduit, setCurrentProduit] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Tous');
  const [activeTab, setActiveTab] = useState(0);

  // Function to get the full image URL
  const getImageUrl = (imageUrl) => {
    if (!imageUrl) return null;

    // If it's already an absolute URL, return it
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Otherwise, prepend the API base URL
    return `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${imageUrl}`;
  };

  useEffect(() => {
    fetchProduits();
  }, []);

  useEffect(() => {
    filterProduits();
  }, [produits, searchTerm, selectedCategory]);

  const fetchProduits = async () => {
    try {
      const data = await produitService.getProduits();
      setProduits(data);
    } catch (error) {
      console.error('Error fetching produits:', error);
    }
  };

  const filterProduits = () => {
    let filtered = [...produits];

    if (searchTerm) {
      filtered = filtered.filter(produit =>
        produit.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        produit.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== 'Tous') {
      filtered = filtered.filter(produit =>
        produit.category === selectedCategory
      );
    }

    setFilteredProduits(filtered);
  };

  const handleOpenDialog = (produit = null) => {
    // If we have a product with an image URL, make sure it's the full URL
    if (produit && produit.imageUrl) {
      const productWithFullImageUrl = {
        ...produit,
        imageUrl: getImageUrl(produit.imageUrl)
      };
      setCurrentProduit(productWithFullImageUrl);
    } else {
      setCurrentProduit(produit);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    // Don't immediately set currentProduit to null to avoid flickering
    // It will be reset when the dialog is opened again
    setTimeout(() => {
      setCurrentProduit(null);
    }, 300); // Wait for dialog close animation
  };

  const handleSave = async (produitData) => {
    try {
      if (currentProduit) {
        await produitService.updateProduit(currentProduit._id, produitData);
      } else {
        await produitService.createProduit(produitData);
      }
      fetchProduits();
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving produit:', error);
    }
  };

  const handleDelete = async (id) => {
    try {
      await produitService.deleteProduit(id);
      fetchProduits();
    } catch (error) {
      console.error('Error deleting produit:', error);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ p: 3 }}>
      {globalStyles}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2,
          background: 'white',
          position: 'relative',
          overflow: 'hidden',
          borderLeft: '4px solid',
          borderColor: 'primary.main'
        }}
      >
        <CardContent sx={{ p: 1, position: 'relative', zIndex: 2 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}>
            <Box>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 0.5,
                  letterSpacing: 0.7
                }}
              >
                CATALOGUE PRODUITS & SERVICES
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600
                }}
              >
                Créez, modifiez et gérez vos produits et services pour les inclure dans vos factures et devis
              </Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: '0 4px 10px rgba(0,0,0,0.15)',
                fontWeight: 'bold',
                letterSpacing: 0.5,
                textTransform: 'none',
                fontSize: '0.95rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 6px 15px rgba(0,0,0,0.2)',
                  transform: 'translateY(-2px)',
                }
              }}
            >
              Ajouter un produit
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Tabs pour diviser l'interface */}
      <Card elevation={2} sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              '& .MuiTabs-indicator': {
                height: 3,
                borderRadius: '3px 3px 0 0'
              },
              '& .MuiTab-root': {
                fontWeight: 'bold',
                py: 2
              }
            }}
          >
            <Tab
              icon={<ListIcon />}
              label="CATALOGUE PRODUITS"
              iconPosition="start"
              sx={{
                color: activeTab === 0 ? 'primary.main' : 'text.secondary',
                '&.Mui-selected': {
                  color: 'primary.main',
                },
                letterSpacing: 0.5,
                fontSize: '0.85rem'
              }}
            />
            <Tab
              icon={<InventoryIcon />}
              label="GESTION DES STOCKS"
              iconPosition="start"
              sx={{
                color: activeTab === 1 ? 'primary.main' : 'text.secondary',
                '&.Mui-selected': {
                  color: 'primary.main',
                },
                letterSpacing: 0.5,
                fontSize: '0.85rem'
              }}
            />
          </Tabs>
        </Box>
      </Card>

      {/* Tab 1: Liste des produits */}
      {activeTab === 0 && (
        <>
          <Card
            elevation={3}
            sx={{
              mb: 3,
              p: 2,
              borderRadius: 2,
              background: 'linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95)), linear-gradient(to right, #f5f7fa, #e4e7eb)',
              boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
              border: '1px solid rgba(0,0,0,0.05)'
            }}
          >
            <CardContent sx={{ p: 1 }}>
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'stretch', md: 'center' },
                gap: 2
              }}>
                <Box sx={{ position: 'relative', width: { xs: '100%', md: 400 } }}>
                  <TextField
                    variant="outlined"
                    placeholder="Rechercher un produit ou service..."
                    fullWidth
                    size="small"
                    InputProps={{
                      startAdornment: <SearchIcon color="primary" sx={{ mr: 1 }} />,
                    }}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: 'white',
                        boxShadow: '0 2px 5px rgba(0,0,0,0.08)',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                        },
                        '&.Mui-focused': {
                          boxShadow: '0 4px 15px rgba(0,0,0,0.15)',
                        }
                      },
                      '& .MuiInputBase-input': {
                        fontWeight: 500
                      }
                    }}
                  />
                  {searchTerm && (
                    <IconButton
                      size="small"
                      sx={{
                        position: 'absolute',
                        right: 8,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        bgcolor: 'rgba(0,0,0,0.05)',
                        '&:hover': {
                          bgcolor: 'rgba(0,0,0,0.1)',
                        }
                      }}
                      onClick={() => setSearchTerm('')}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  )}
                </Box>

                <Box sx={{
                  display: 'flex',
                  gap: 1,
                  flexWrap: 'wrap',
                  justifyContent: { xs: 'center', md: 'flex-end' }
                }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      alignSelf: 'center',
                      mr: 1,
                      color: 'text.secondary',
                      display: { xs: 'none', md: 'block' },
                      fontWeight: 600,
                      letterSpacing: 0.3
                    }}
                  >
                    CATÉGORIE:
                  </Typography>
                  <Chip
                    label="Tous"
                    onClick={() => setSelectedCategory('Tous')}
                    color={selectedCategory === 'Tous' ? 'primary' : 'default'}
                    variant={selectedCategory === 'Tous' ? 'filled' : 'outlined'}
                    sx={{
                      borderRadius: 1,
                      fontWeight: selectedCategory === 'Tous' ? 'bold' : 'normal',
                      boxShadow: selectedCategory === 'Tous' ? '0 2px 5px rgba(0,0,0,0.15)' : 0,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                        transform: 'translateY(-1px)'
                      }
                    }}
                  />
                  {categories.map(category => (
                    <Chip
                      key={category}
                      label={category}
                      onClick={() => setSelectedCategory(category)}
                      color={selectedCategory === category ? getCategoryColor(category) : 'default'}
                      variant={selectedCategory === category ? 'filled' : 'outlined'}
                      sx={{
                        borderRadius: 1,
                        fontWeight: selectedCategory === category ? 'bold' : 'normal',
                        boxShadow: selectedCategory === category ? '0 2px 5px rgba(0,0,0,0.15)' : 0,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          transform: 'translateY(-1px)'
                        }
                      }}
                    />
                  ))}
                </Box>
              </Box>

              {/* Results count */}
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {filteredProduits.length} produit{filteredProduits.length !== 1 ? 's' : ''} trouvé{filteredProduits.length !== 1 ? 's' : ''}
                  {searchTerm && ` pour "${searchTerm}"`}
                  {selectedCategory !== 'Tous' && ` dans la catégorie "${selectedCategory}"`}
                </Typography>

                {(searchTerm || selectedCategory !== 'Tous') && (
                  <Button
                    size="small"
                    variant="text"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('Tous');
                    }}
                    startIcon={<DeleteIcon fontSize="small" />}
                    sx={{
                      fontWeight: 500,
                      letterSpacing: 0.3,
                      textTransform: 'none'
                    }}
                  >
                    Réinitialiser la recherche
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>

          <TableContainer
            component={Paper}
            elevation={4}
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              transition: 'box-shadow 0.3s ease',
              '&:hover': {
                boxShadow: '0 6px 25px rgba(0,0,0,0.12)',
              }
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{
                  background: 'white',
                  borderBottom: '2px solid',
                  borderColor: 'primary.main',
                }}>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ImageIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>PHOTO</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CategoryIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>PRODUIT</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <InfoIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>DÉTAILS</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ListIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>TYPE</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>TARIF</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>GESTION</Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredProduits.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 5 }}>
                      <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 600 }}>
                        Aucun élément à afficher
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1, maxWidth: 450, mx: 'auto' }}>
                        Modifiez vos critères de recherche ou cliquez sur "Nouveau Produit" pour ajouter un élément à votre catalogue
                      </Typography>
                      <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={() => handleOpenDialog()}
                        sx={{ mt: 2 }}
                      >
                        Créer un nouveau produit
                      </Button>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProduits.map((produit, index) => (
                    <TableRow
                      key={produit._id}
                      hover
                      sx={{
                        backgroundColor: index % 2 === 0 ? 'white' : 'rgba(0, 0, 0, 0.02)',
                        transition: 'background-color 0.2s ease',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        }
                      }}
                    >
                      <TableCell sx={{ py: 2 }}>
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}>
                          {produit.imageUrl ? (
                            <Avatar
                              src={getImageUrl(produit.imageUrl)}
                              alt={produit.nom}
                              variant="rounded"
                              sx={{
                                width: 70,
                                height: 70,
                                border: '1px solid #eee',
                                boxShadow: 2,
                                transition: 'transform 0.2s ease',
                                '&:hover': {
                                  transform: 'scale(1.05)',
                                  cursor: 'pointer',
                                  boxShadow: 3
                                }
                              }}
                            />
                          ) : (
                            <Avatar
                              variant="rounded"
                              sx={{
                                width: 70,
                                height: 70,
                                bgcolor: 'grey.100',
                                color: 'text.secondary',
                                border: '1px dashed #ccc'
                              }}
                            >
                              <ImageIcon fontSize="large" />
                            </Avatar>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                          <Typography
                            sx={{
                              fontWeight: 'medium',
                              fontSize: '1rem',
                              color: 'primary.dark'
                            }}
                          >
                            {produit.nom}
                          </Typography>
                          {produit.gestionStock && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <Typography variant="caption" color="text.secondary">
                                Stock:
                              </Typography>
                              <Chip
                                size="small"
                                label={formatStockQuantity(produit.quantiteStock)}
                                color={
                                  produit.quantiteStock > (produit.seuilAlerte || 5) ? 'success' :
                                  produit.quantiteStock > 0 ? 'warning' : 'error'
                                }
                                variant="outlined"
                                sx={{ height: 20, fontSize: '0.7rem' }}
                              />
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            maxWidth: 250
                          }}
                        >
                          {produit.description || 'Aucune description'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Chip
                          label={produit.category || 'Non classé'}
                          size="small"
                          color={getCategoryColor(produit.category)}
                          variant="filled"
                          sx={{
                            borderRadius: 1,
                            fontWeight: 500,
                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Typography
                          fontWeight="bold"
                          color="primary.dark"
                          sx={{
                            fontSize: '1.05rem',
                            display: 'inline-block',
                            bgcolor: 'rgba(0, 0, 0, 0.03)',
                            px: 1.5,
                            py: 0.5,
                            borderRadius: 1,
                            border: '1px solid rgba(0, 0, 0, 0.08)'
                          }}
                        >
                          {formatCurrency(produit.prix)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Éditer les informations" arrow placement="top">
                            <IconButton
                              onClick={() => handleOpenDialog(produit)}
                              color="primary"
                              size="small"
                              sx={{
                                bgcolor: 'primary.light',
                                color: 'white',
                                '&:hover': {
                                  bgcolor: 'primary.main',
                                  transform: 'translateY(-2px)',
                                  boxShadow: 2
                                },
                                transition: 'all 0.2s ease'
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Supprimer définitivement" arrow placement="top">
                            <IconButton
                              onClick={() => handleDelete(produit._id)}
                              color="error"
                              size="small"
                              sx={{
                                bgcolor: 'error.light',
                                color: 'white',
                                '&:hover': {
                                  bgcolor: 'error.main',
                                  transform: 'translateY(-2px)',
                                  boxShadow: 2
                                },
                                transition: 'all 0.2s ease'
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      {/* Tab 2: Évolution des stocks */}
      {activeTab === 1 && (
        <StockEvolution />
      )}

      <ProduitForm
        open={openDialog}
        onClose={handleCloseDialog}
        produit={currentProduit}
        onSave={handleSave}
        categories={categories}
      />
    </Box>
  );
};

export default Produits;