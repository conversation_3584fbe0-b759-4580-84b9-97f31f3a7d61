import api from './api';

const vendeurService = {
  // Get all invoices
  getAllInvoices: async () => {
    try {
      const response = await api.get('/factures');
      return response.data;
    } catch (error) {
      console.error('Error fetching all invoices:', error);
      throw error.response?.data || { error: 'Erreur lors de la récupération des factures' };
    }
  },

  // Get all quotes
  getAllQuotes: async () => {
    try {
      const response = await api.get('/devis');
      return response.data;
    } catch (error) {
      console.error('Error fetching all quotes:', error);
      throw error.response?.data || { error: 'Erreur lors de la récupération des devis' };
    }
  },

  // Get all clients
  getAllClients: async () => {
    try {
      const response = await api.get('/clients');
      return response.data;
    } catch (error) {
      console.error('Error fetching all clients:', error);
      throw error.response?.data || { error: 'Erreur lors de la récupération des clients' };
    }
  },

  // Get all products
  getAllProducts: async () => {
    try {
      const response = await api.get('/produits');
      return response.data;
    } catch (error) {
      console.error('Error fetching all products:', error);
      throw error.response?.data || { error: 'Erreur lors de la récupération des produits' };
    }
  },
};

export default vendeurService;
