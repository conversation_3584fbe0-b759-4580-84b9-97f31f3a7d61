{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/code-frame/parse-code-frame.ts"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport Anser, { type AnserJsonEntry } from 'next/dist/compiled/anser'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n\n// Strip leading spaces out of the code frame\nexport function formatCodeFrame(codeFrame: string) {\n  const lines = codeFrame.split(/\\r?\\n/g)\n\n  // Find the minimum length of leading spaces after `|` in the code frame\n  const miniLeadingSpacesLength = lines\n    .map((line) =>\n      /^>? +\\d+ +\\| [ ]+/.exec(stripAnsi(line)) === null\n        ? null\n        : /^>? +\\d+ +\\| ( *)/.exec(stripAnsi(line))\n    )\n    .filter(Boolean)\n    .map((v) => v!.pop()!)\n    .reduce((c, n) => (isNaN(c) ? n.length : Math.min(c, n.length)), NaN)\n\n  // When the minimum length of leading spaces is greater than 1, remove them\n  // from the code frame to help the indentation looks better when there's a lot leading spaces.\n  if (miniLeadingSpacesLength > 1) {\n    return lines\n      .map((line, a) =>\n        ~(a = line.indexOf('|'))\n          ? line.substring(0, a) +\n            line.substring(a).replace(`^\\\\ {${miniLeadingSpacesLength}}`, '')\n          : line\n      )\n      .join('\\n')\n  }\n  return lines.join('\\n')\n}\n\nexport function groupCodeFrameLines(formattedFrame: string) {\n  // Map the decoded lines to a format that can be rendered\n  const decoded = Anser.ansiToJson(formattedFrame, {\n    json: true,\n    use_classes: true,\n    remove_empty: true,\n  })\n  const lines: (typeof decoded)[] = []\n\n  let line: typeof decoded = []\n  for (const token of decoded) {\n    if (token.content === '\\n') {\n      lines.push(line)\n      line = []\n    } else {\n      line.push(token)\n    }\n  }\n  if (line.length > 0) {\n    lines.push(line)\n  }\n\n  return lines\n}\n\nexport function parseLineNumberFromCodeFrameLine(\n  line: AnserJsonEntry[],\n  stackFrame: StackFrame\n) {\n  let lineNumberToken: AnserJsonEntry | undefined\n  let lineNumber: string | undefined\n  // parse line number from line first 2 tokens\n  // e.g. ` > 1 | const foo = 'bar'` => `1`, first token is `1 |`\n  // e.g. `  2 | const foo = 'bar'` => `2`. first 2 tokens are ' ' and ' 2 |'\n  // console.log('line', line)\n  if (line[0]?.content === '>' || line[0]?.content === ' ') {\n    lineNumberToken = line[1]\n    lineNumber = lineNumberToken?.content?.replace('|', '')?.trim()\n  }\n\n  // When the line number is possibly undefined, it can be just the non-source code line\n  // e.g. the ^ sign can also take a line, we skip rendering line number for it\n  return {\n    lineNumber,\n    isErroredLine: lineNumber === stackFrame.lineNumber?.toString(),\n  }\n}\n"], "names": ["<PERSON><PERSON>", "stripAnsi", "formatCodeFrame", "codeFrame", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "groupCodeFrameLines", "formattedFrame", "decoded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "token", "content", "push", "parseLineNumberFromCodeFrameLine", "stackFrame", "lineNumberToken", "lineNumber", "trim", "isErroredLine", "toString"], "mappings": "AACA,OAAOA,WAAoC,2BAA0B;AACrE,OAAOC,eAAe,gCAA+B;AAErD,6CAA6C;AAC7C,OAAO,SAASC,gBAAgBC,SAAiB;IAC/C,MAAMC,QAAQD,UAAUE,KAAK,CAAC;IAE9B,wEAAwE;IACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACR,UAAUO,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACR,UAAUO,QAExCE,MAAM,CAACC,SACPJ,GAAG,CAAC,CAACK,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;IAEnE,2EAA2E;IAC3E,8FAA8F;IAC9F,IAAIf,0BAA0B,GAAG;QAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMc,IACV,CAAEA,CAAAA,IAAId,KAAKe,OAAO,CAAC,IAAG,IAClBf,KAAKgB,SAAS,CAAC,GAAGF,KAClBd,KAAKgB,SAAS,CAACF,GAAGG,OAAO,CAAC,AAAC,UAAOnB,0BAAwB,KAAI,MAC9DE,MAELkB,IAAI,CAAC;IACV;IACA,OAAOtB,MAAMsB,IAAI,CAAC;AACpB;AAEA,OAAO,SAASC,oBAAoBC,cAAsB;IACxD,yDAAyD;IACzD,MAAMC,UAAU7B,MAAM8B,UAAU,CAACF,gBAAgB;QAC/CG,MAAM;QACNC,aAAa;QACbC,cAAc;IAChB;IACA,MAAM7B,QAA4B,EAAE;IAEpC,IAAII,OAAuB,EAAE;IAC7B,KAAK,MAAM0B,SAASL,QAAS;QAC3B,IAAIK,MAAMC,OAAO,KAAK,MAAM;YAC1B/B,MAAMgC,IAAI,CAAC5B;YACXA,OAAO,EAAE;QACX,OAAO;YACLA,KAAK4B,IAAI,CAACF;QACZ;IACF;IACA,IAAI1B,KAAKU,MAAM,GAAG,GAAG;QACnBd,MAAMgC,IAAI,CAAC5B;IACb;IAEA,OAAOJ;AACT;AAEA,OAAO,SAASiC,iCACd7B,IAAsB,EACtB8B,UAAsB;QAQlB9B,QAA4BA,SASA8B;IAfhC,IAAIC;IACJ,IAAIC;IACJ,6CAA6C;IAC7C,+DAA+D;IAC/D,2EAA2E;IAC3E,4BAA4B;IAC5B,IAAIhC,EAAAA,SAAAA,IAAI,CAAC,EAAE,qBAAPA,OAAS2B,OAAO,MAAK,OAAO3B,EAAAA,UAAAA,IAAI,CAAC,EAAE,qBAAPA,QAAS2B,OAAO,MAAK,KAAK;YAE3CI,kCAAAA;QADbA,kBAAkB/B,IAAI,CAAC,EAAE;QACzBgC,aAAaD,oCAAAA,2BAAAA,gBAAiBJ,OAAO,sBAAxBI,mCAAAA,yBAA0Bd,OAAO,CAAC,KAAK,wBAAvCc,iCAA4CE,IAAI;IAC/D;IAEA,sFAAsF;IACtF,6EAA6E;IAC7E,OAAO;QACLD;QACAE,eAAeF,iBAAeF,yBAAAA,WAAWE,UAAU,qBAArBF,uBAAuBK,QAAQ;IAC/D;AACF"}