import api from './api';

const produitService = {
  // Get all products with optional filters
  getProduits: async (filters = {}) => {
    try {
      const response = await api.get('/produit/prods', { params: filters });
      console.log('Products fetched from API:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  // Alias pour getAllProduits (pour compatibilité avec BonLivraisonForm)
  getAllProduits: async () => {
    return produitService.getProduits();
  },

  // Get a single product by ID
  getProduitById: async (id) => {
    try {
      const response = await api.get(`/produit/prods/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Create a new product with image upload support
  createProduit: async (produitData) => {
    try {
      // Handle file upload if there's an image
      let formData;
      if (produitData.image instanceof File) {
        formData = new FormData();

        // Add all product data to form
        Object.keys(produitData).forEach(key => {
          if (key === 'image') {
            formData.append('image', produitData.image);
          } else if (key === 'variants' || key === 'prixVolume') {
            // Convert arrays/objects to JSON strings
            if (produitData[key] && produitData[key].length > 0) {
              formData.append(key, JSON.stringify(produitData[key]));
            }
          } else {
            formData.append(key, produitData[key]);
          }
        });

        const response = await api.post('/produit/prods', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        return response.data;
      } else {
        // Regular JSON request if no image
        const response = await api.post('/produit/prods', produitData);
        return response.data;
      }
    } catch (error) {
      throw error;
    }
  },

  // Update a product with image upload support
  updateProduit: async (id, produitData) => {
    try {
      // Handle file upload if there's an image
      let formData;
      if (produitData.image instanceof File) {
        formData = new FormData();

        // Add all product data to form
        Object.keys(produitData).forEach(key => {
          if (key === 'image') {
            formData.append('image', produitData.image);
          } else if (key === 'variants' || key === 'prixVolume') {
            // Convert arrays/objects to JSON strings
            if (produitData[key] && produitData[key].length > 0) {
              formData.append(key, JSON.stringify(produitData[key]));
            }
          } else {
            formData.append(key, produitData[key]);
          }
        });

        const response = await api.put(`/produit/prods/${id}`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        return response.data;
      } else {
        // Regular JSON request if no image
        const response = await api.put(`/produit/prods/${id}`, produitData);
        return response.data;
      }
    } catch (error) {
      throw error;
    }
  },

  // Delete a product
  deleteProduit: async (id) => {
    try {
      const response = await api.delete(`/produit/prods/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get product categories
  getCategories: async () => {
    try {
      const response = await api.get('/produit/categories');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get product statistics
  getProductStats: async () => {
    try {
      const response = await api.get('/produit/stats');
      return response.data;
    } catch (error) {
      // Si l'endpoint n'existe pas encore, retourner des données fictives pour le développement
      console.warn('Endpoint /produit/stats non disponible, utilisation de données fictives', error);
      return {
        topProducts: [],
        lowStockProducts: [],
        categoryCounts: []
      };
    }
  },

  // Get stock history for a product
  getStockHistory: async (productId) => {
    try {
      const response = await api.get(`/produit/prods/${productId}/stock-history`);
      return response.data;
    } catch (error) {
      // Si l'endpoint n'existe pas encore, retourner des données fictives pour le développement
      console.warn(`Endpoint /produit/prods/${productId}/stock-history non disponible, utilisation de données fictives`, error);
      return [];
    }
  },

  // Get bundle suggestions for a product
  getBundleSuggestions: async (productId) => {
    try {
      const response = await api.get(`/produit/prods/${productId}/bundles`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update product stock with reason
  updateStock: async (productId, quantiteStock, motif = '') => {
    try {
      const response = await api.patch(`/produit/prods/${productId}/stock`, {
        quantiteStock,
        motif
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get stock history for a product
  getStockHistory: async (productId, options = {}) => {
    try {
      const response = await api.get(`/produit/prods/${productId}/stock-history`, {
        params: options
      });
      return response.data;
    } catch (error) {
      // Si l'endpoint n'existe pas encore, retourner des données fictives pour le développement
      console.warn(`Endpoint /produit/prods/${productId}/stock-history non disponible, utilisation de données fictives`, error);

      // Générer des données fictives pour le développement
      const fakeHistory = [];
      const now = new Date();

      // Générer 10 entrées fictives
      for (let i = 0; i < 10; i++) {
        const isDecrease = Math.random() > 0.3;
        const quantity = Math.floor(Math.random() * 5) + 1;
        const date = new Date(now);
        date.setDate(date.getDate() - i);

        fakeHistory.push({
          _id: `fake-${i}`,
          produitId: productId,
          quantiteAvant: isDecrease ? 20 + quantity : 20 - quantity,
          quantiteApres: isDecrease ? 20 : 20 - quantity,
          difference: isDecrease ? -quantity : quantity,
          typeOperation: isDecrease ? 'VENTE' : Math.random() > 0.5 ? 'AJUSTEMENT' : 'RETOUR',
          motif: isDecrease ? 'Vente via facture' : 'Ajustement manuel',
          date: date.toISOString()
        });
      }

      return fakeHistory;
    }
  },

  // Export products to CSV
  exportProducts: async () => {
    try {
      const response = await api.get('/produit/export', { responseType: 'blob' });
      // Create a download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'produits.csv');
      document.body.appendChild(link);
      link.click();
      link.remove();
      return true;
    } catch (error) {
      throw error;
    }
  }
};

export default produitService;