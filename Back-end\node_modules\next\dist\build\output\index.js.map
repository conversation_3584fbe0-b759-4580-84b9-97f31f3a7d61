{"version": 3, "sources": ["../../../src/build/output/index.ts"], "sourcesContent": ["import { bold, red, yellow } from '../../lib/picocolors'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport textTable from 'next/dist/compiled/text-table'\nimport createStore from 'next/dist/compiled/unistore'\nimport formatWebpackMessages from '../../client/components/react-dev-overlay/utils/format-webpack-messages'\nimport { store as consoleStore } from './store'\nimport type { OutputState } from './store'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { COMPILER_NAMES } from '../../shared/lib/constants'\nimport type { CompilerNameValues } from '../../shared/lib/constants'\n\ntype CompilerDiagnostics = {\n  totalModulesCount: number\n  errors: string[] | null\n  warnings: string[] | null\n}\n\ntype WebpackStatus =\n  | { loading: true }\n  | ({ loading: false } & CompilerDiagnostics)\n\ntype AmpStatus = {\n  message: string\n  line: number\n  col: number\n  specUrl: string | null\n  code: string\n}\n\nexport type AmpPageStatus = {\n  [page: string]: { errors: AmpStatus[]; warnings: AmpStatus[] }\n}\n\ntype BuildStatusStore = {\n  client: WebpackStatus\n  server: WebpackStatus\n  edgeServer: WebpackStatus\n  trigger: string | undefined\n  url: string | undefined\n  amp: AmpPageStatus\n}\n\nexport function formatAmpMessages(amp: AmpPageStatus) {\n  let output = bold('Amp Validation') + '\\n\\n'\n  let messages: string[][] = []\n\n  const chalkError = red('error')\n  function ampError(page: string, error: AmpStatus) {\n    messages.push([page, chalkError, error.message, error.specUrl || ''])\n  }\n\n  const chalkWarn = yellow('warn')\n  function ampWarn(page: string, warn: AmpStatus) {\n    messages.push([page, chalkWarn, warn.message, warn.specUrl || ''])\n  }\n\n  for (const page in amp) {\n    let { errors, warnings } = amp[page]\n\n    const devOnlyFilter = (err: AmpStatus) => err.code !== 'DEV_MODE_ONLY'\n    errors = errors.filter(devOnlyFilter)\n    warnings = warnings.filter(devOnlyFilter)\n    if (!(errors.length || warnings.length)) {\n      // Skip page with no non-dev warnings\n      continue\n    }\n\n    if (errors.length) {\n      ampError(page, errors[0])\n      for (let index = 1; index < errors.length; ++index) {\n        ampError('', errors[index])\n      }\n    }\n    if (warnings.length) {\n      ampWarn(errors.length ? '' : page, warnings[0])\n      for (let index = 1; index < warnings.length; ++index) {\n        ampWarn('', warnings[index])\n      }\n    }\n    messages.push(['', '', '', ''])\n  }\n\n  if (!messages.length) {\n    return ''\n  }\n\n  output += textTable(messages, {\n    align: ['l', 'l', 'l', 'l'],\n    stringLength(str: string) {\n      return stripAnsi(str).length\n    },\n  })\n\n  return output\n}\n\nconst buildStore = createStore<BuildStatusStore>({\n  // @ts-expect-error initial value\n  client: {},\n  // @ts-expect-error initial value\n  server: {},\n  // @ts-expect-error initial value\n  edgeServer: {},\n})\nlet buildWasDone = false\nlet clientWasLoading = true\nlet serverWasLoading = true\nlet edgeServerWasLoading = false\n\nbuildStore.subscribe((state) => {\n  const { amp, client, server, edgeServer, trigger, url } = state\n\n  const { appUrl } = consoleStore.getState()\n\n  if (client.loading || server.loading || edgeServer?.loading) {\n    consoleStore.setState(\n      {\n        bootstrap: false,\n        appUrl: appUrl!,\n        // If it takes more than 3 seconds to compile, mark it as loading status\n        loading: true,\n        trigger,\n        url,\n      } as OutputState,\n      true\n    )\n    clientWasLoading = (!buildWasDone && clientWasLoading) || client.loading\n    serverWasLoading = (!buildWasDone && serverWasLoading) || server.loading\n    edgeServerWasLoading =\n      (!buildWasDone && edgeServerWasLoading) || edgeServer.loading\n    buildWasDone = false\n    return\n  }\n\n  buildWasDone = true\n\n  let partialState: Partial<OutputState> = {\n    bootstrap: false,\n    appUrl: appUrl!,\n    loading: false,\n    typeChecking: false,\n    totalModulesCount:\n      (clientWasLoading ? client.totalModulesCount : 0) +\n      (serverWasLoading ? server.totalModulesCount : 0) +\n      (edgeServerWasLoading ? edgeServer?.totalModulesCount || 0 : 0),\n    hasEdgeServer: !!edgeServer,\n  }\n  if (client.errors && clientWasLoading) {\n    // Show only client errors\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: client.errors,\n        warnings: null,\n      } as OutputState,\n      true\n    )\n  } else if (server.errors && serverWasLoading) {\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: server.errors,\n        warnings: null,\n      } as OutputState,\n      true\n    )\n  } else if (edgeServer.errors && edgeServerWasLoading) {\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: edgeServer.errors,\n        warnings: null,\n      } as OutputState,\n      true\n    )\n  } else {\n    // Show warnings from all of them\n    const warnings = [\n      ...(client.warnings || []),\n      ...(server.warnings || []),\n      ...(edgeServer.warnings || []),\n    ].concat(formatAmpMessages(amp) || [])\n\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: null,\n        warnings: warnings.length === 0 ? null : warnings,\n      } as OutputState,\n      true\n    )\n  }\n})\n\nexport function ampValidation(\n  page: string,\n  errors: AmpStatus[],\n  warnings: AmpStatus[]\n) {\n  const { amp } = buildStore.getState()\n  if (!(errors.length || warnings.length)) {\n    buildStore.setState({\n      amp: Object.keys(amp)\n        .filter((k) => k !== page)\n        .sort()\n        // eslint-disable-next-line no-sequences\n        .reduce((a, c) => ((a[c] = amp[c]), a), {} as AmpPageStatus),\n    })\n    return\n  }\n\n  const newAmp: AmpPageStatus = { ...amp, [page]: { errors, warnings } }\n  buildStore.setState({\n    amp: Object.keys(newAmp)\n      .sort()\n      // eslint-disable-next-line no-sequences\n      .reduce((a, c) => ((a[c] = newAmp[c]), a), {} as AmpPageStatus),\n  })\n}\n\nexport function watchCompilers(\n  client: webpack.Compiler,\n  server: webpack.Compiler,\n  edgeServer: webpack.Compiler\n) {\n  buildStore.setState({\n    client: { loading: true },\n    server: { loading: true },\n    edgeServer: { loading: true },\n    trigger: 'initial',\n    url: undefined,\n  })\n\n  function tapCompiler(\n    key: CompilerNameValues,\n    compiler: webpack.Compiler,\n    onEvent: (status: WebpackStatus) => void\n  ) {\n    compiler.hooks.invalid.tap(`NextJsInvalid-${key}`, () => {\n      onEvent({ loading: true })\n    })\n\n    compiler.hooks.done.tap(`NextJsDone-${key}`, (stats: webpack.Stats) => {\n      buildStore.setState({ amp: {} })\n\n      const { errors, warnings } = formatWebpackMessages(\n        stats.toJson({\n          preset: 'errors-warnings',\n          moduleTrace: true,\n        })\n      )\n\n      const hasErrors = !!errors?.length\n      const hasWarnings = !!warnings?.length\n\n      onEvent({\n        loading: false,\n        totalModulesCount: stats.compilation.modules.size,\n        errors: hasErrors ? errors : null,\n        warnings: hasWarnings ? warnings : null,\n      })\n    })\n  }\n\n  tapCompiler(COMPILER_NAMES.client, client, (status) => {\n    if (\n      !status.loading &&\n      !buildStore.getState().server.loading &&\n      !buildStore.getState().edgeServer.loading &&\n      status.totalModulesCount > 0\n    ) {\n      buildStore.setState({\n        client: status,\n        trigger: undefined,\n        url: undefined,\n      })\n    } else {\n      buildStore.setState({\n        client: status,\n      })\n    }\n  })\n  tapCompiler(COMPILER_NAMES.server, server, (status) => {\n    if (\n      !status.loading &&\n      !buildStore.getState().client.loading &&\n      !buildStore.getState().edgeServer.loading &&\n      status.totalModulesCount > 0\n    ) {\n      buildStore.setState({\n        server: status,\n        trigger: undefined,\n        url: undefined,\n      })\n    } else {\n      buildStore.setState({\n        server: status,\n      })\n    }\n  })\n  tapCompiler(COMPILER_NAMES.edgeServer, edgeServer, (status) => {\n    if (\n      !status.loading &&\n      !buildStore.getState().client.loading &&\n      !buildStore.getState().server.loading &&\n      status.totalModulesCount > 0\n    ) {\n      buildStore.setState({\n        edgeServer: status,\n        trigger: undefined,\n        url: undefined,\n      })\n    } else {\n      buildStore.setState({\n        edgeServer: status,\n      })\n    }\n  })\n}\n\nexport function reportTrigger(trigger: string, url?: string) {\n  buildStore.setState({\n    trigger,\n    url,\n  })\n}\n"], "names": ["ampValidation", "formatAmpMessages", "reportTrigger", "watchCompilers", "amp", "output", "bold", "messages", "chalkError", "red", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "yellow", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "textTable", "align", "stringLength", "str", "stripAnsi", "buildStore", "createStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "url", "appUrl", "consoleStore", "getState", "loading", "setState", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "undefined", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "formatWebpackMessages", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "COMPILER_NAMES", "status"], "mappings": ";;;;;;;;;;;;;;;;;IAkMgBA,aAAa;eAAbA;;IAxJAC,iBAAiB;eAAjBA;;IAsRAC,aAAa;eAAbA;;IApGAC,cAAc;eAAdA;;;4BA5NkB;kEACZ;kEACA;iEACE;8EACU;uBACI;2BAGP;;;;;;AAkCxB,SAASF,kBAAkBG,GAAkB;IAClD,IAAIC,SAASC,IAAAA,gBAAI,EAAC,oBAAoB;IACtC,IAAIC,WAAuB,EAAE;IAE7B,MAAMC,aAAaC,IAAAA,eAAG,EAAC;IACvB,SAASC,SAASC,IAAY,EAAEC,KAAgB;QAC9CL,SAASM,IAAI,CAAC;YAACF;YAAMH;YAAYI,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYC,IAAAA,kBAAM,EAAC;IACzB,SAASC,QAAQP,IAAY,EAAEQ,IAAe;QAC5CZ,SAASM,IAAI,CAAC;YAACF;YAAMK;YAAWG,KAAKL,OAAO;YAAEK,KAAKJ,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQP,IAAK;QACtB,IAAI,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGjB,GAAG,CAACO,KAAK;QAEpC,MAAMW,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBhB,SAASC,MAAMS,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDjB,SAAS,IAAIU,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKf,MAAMU,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACApB,SAASM,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACN,SAASmB,MAAM,EAAE;QACpB,OAAO;IACT;IAEArB,UAAUuB,IAAAA,kBAAS,EAACrB,UAAU;QAC5BsB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAOC,IAAAA,kBAAS,EAACD,KAAKL,MAAM;QAC9B;IACF;IAEA,OAAOrB;AACT;AAEA,MAAM4B,aAAaC,IAAAA,iBAAW,EAAmB;IAC/C,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BR,WAAWS,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEvC,GAAG,EAAE+B,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAEC,GAAG,EAAE,GAAGF;IAE1D,MAAM,EAAEG,MAAM,EAAE,GAAGC,YAAY,CAACC,QAAQ;IAExC,IAAIb,OAAOc,OAAO,IAAIb,OAAOa,OAAO,KAAIZ,8BAAAA,WAAYY,OAAO,GAAE;QAC3DF,YAAY,CAACG,QAAQ,CACnB;YACEC,WAAW;YACXL,QAAQA;YACR,wEAAwE;YACxEG,SAAS;YACTL;YACAC;QACF,GACA;QAEFN,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOc,OAAO;QACxET,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOa,OAAO;QACxER,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWY,OAAO;QAC/DX,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIc,eAAqC;QACvCD,WAAW;QACXL,QAAQA;QACRG,SAAS;QACTI,cAAc;QACdC,mBACE,AAACf,CAAAA,mBAAmBJ,OAAOmB,iBAAiB,GAAG,CAAA,IAC9Cd,CAAAA,mBAAmBJ,OAAOkB,iBAAiB,GAAG,CAAA,IAC9Cb,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYiB,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAAClB;IACnB;IACA,IAAIF,OAAOf,MAAM,IAAImB,kBAAkB;QACrC,0BAA0B;QAC1BQ,YAAY,CAACG,QAAQ,CACnB;YACE,GAAGE,YAAY;YACfhC,QAAQe,OAAOf,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIe,OAAOhB,MAAM,IAAIoB,kBAAkB;QAC5CO,YAAY,CAACG,QAAQ,CACnB;YACE,GAAGE,YAAY;YACfhC,QAAQgB,OAAOhB,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIgB,WAAWjB,MAAM,IAAIqB,sBAAsB;QACpDM,YAAY,CAACG,QAAQ,CACnB;YACE,GAAGE,YAAY;YACfhC,QAAQiB,WAAWjB,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXc,OAAOd,QAAQ,IAAI,EAAE;eACrBe,OAAOf,QAAQ,IAAI,EAAE;eACrBgB,WAAWhB,QAAQ,IAAI,EAAE;SAC9B,CAACmC,MAAM,CAACvD,kBAAkBG,QAAQ,EAAE;QAErC2C,YAAY,CAACG,QAAQ,CACnB;YACE,GAAGE,YAAY;YACfhC,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEO,SAASrB,cACdW,IAAY,EACZS,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEjB,GAAG,EAAE,GAAG6B,WAAWe,QAAQ;IACnC,IAAI,CAAE5B,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCO,WAAWiB,QAAQ,CAAC;YAClB9C,KAAKqD,OAAOC,IAAI,CAACtD,KACdqB,MAAM,CAAC,CAACkC,IAAMA,MAAMhD,MACpBiD,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAG3D,GAAG,CAAC2D,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAG5D,GAAG;QAAE,CAACO,KAAK,EAAE;YAAES;YAAQC;QAAS;IAAE;IACrEY,WAAWiB,QAAQ,CAAC;QAClB9C,KAAKqD,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEO,SAAS3D,eACdgC,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BJ,WAAWiB,QAAQ,CAAC;QAClBf,QAAQ;YAAEc,SAAS;QAAK;QACxBb,QAAQ;YAAEa,SAAS;QAAK;QACxBZ,YAAY;YAAEY,SAAS;QAAK;QAC5BL,SAAS;QACTC,KAAKoB;IACP;IAEA,SAASC,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,KAAK,EAAE;YACjDE,QAAQ;gBAAEpB,SAAS;YAAK;QAC1B;QAEAmB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,KAAK,EAAE,CAACO;YAC5CzC,WAAWiB,QAAQ,CAAC;gBAAE9C,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGsD,IAAAA,8BAAqB,EAChDD,MAAME,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAAC3D,0BAAAA,OAAQM,MAAM;YAClC,MAAMsD,cAAc,CAAC,EAAC3D,4BAAAA,SAAUK,MAAM;YAEtC2C,QAAQ;gBACNpB,SAAS;gBACTK,mBAAmBoB,MAAMO,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjD/D,QAAQ2D,YAAY3D,SAAS;gBAC7BC,UAAU2D,cAAc3D,WAAW;YACrC;QACF;IACF;IAEA6C,YAAYkB,yBAAc,CAACjD,MAAM,EAAEA,QAAQ,CAACkD;QAC1C,IACE,CAACA,OAAOpC,OAAO,IACf,CAAChB,WAAWe,QAAQ,GAAGZ,MAAM,CAACa,OAAO,IACrC,CAAChB,WAAWe,QAAQ,GAAGX,UAAU,CAACY,OAAO,IACzCoC,OAAO/B,iBAAiB,GAAG,GAC3B;YACArB,WAAWiB,QAAQ,CAAC;gBAClBf,QAAQkD;gBACRzC,SAASqB;gBACTpB,KAAKoB;YACP;QACF,OAAO;YACLhC,WAAWiB,QAAQ,CAAC;gBAClBf,QAAQkD;YACV;QACF;IACF;IACAnB,YAAYkB,yBAAc,CAAChD,MAAM,EAAEA,QAAQ,CAACiD;QAC1C,IACE,CAACA,OAAOpC,OAAO,IACf,CAAChB,WAAWe,QAAQ,GAAGb,MAAM,CAACc,OAAO,IACrC,CAAChB,WAAWe,QAAQ,GAAGX,UAAU,CAACY,OAAO,IACzCoC,OAAO/B,iBAAiB,GAAG,GAC3B;YACArB,WAAWiB,QAAQ,CAAC;gBAClBd,QAAQiD;gBACRzC,SAASqB;gBACTpB,KAAKoB;YACP;QACF,OAAO;YACLhC,WAAWiB,QAAQ,CAAC;gBAClBd,QAAQiD;YACV;QACF;IACF;IACAnB,YAAYkB,yBAAc,CAAC/C,UAAU,EAAEA,YAAY,CAACgD;QAClD,IACE,CAACA,OAAOpC,OAAO,IACf,CAAChB,WAAWe,QAAQ,GAAGb,MAAM,CAACc,OAAO,IACrC,CAAChB,WAAWe,QAAQ,GAAGZ,MAAM,CAACa,OAAO,IACrCoC,OAAO/B,iBAAiB,GAAG,GAC3B;YACArB,WAAWiB,QAAQ,CAAC;gBAClBb,YAAYgD;gBACZzC,SAASqB;gBACTpB,KAAKoB;YACP;QACF,OAAO;YACLhC,WAAWiB,QAAQ,CAAC;gBAClBb,YAAYgD;YACd;QACF;IACF;AACF;AAEO,SAASnF,cAAc0C,OAAe,EAAEC,GAAY;IACzDZ,WAAWiB,QAAQ,CAAC;QAClBN;QACAC;IACF;AACF"}