const Entreprise = require('../models/EntrepriseModel');
const ResponsableEntreprise = require('../models/ResponsableEntrepriseModel');
const User = require('../models/UserModel');

/**
 * <PERSON><PERSON><PERSON> ou met à jour une entreprise associée à un responsable
 * @param {Object} userData - Données de l'utilisateur responsable
 * @param {String} userId - ID de l'utilisateur responsable
 * @returns {Promise<Object>} - L'entreprise créée ou mise à jour
 */
const createOrUpdateEntreprise = async (userData, userId) => {
  try {
    console.log('Création/mise à jour d\'entreprise pour le responsable:', userId);

    // Préparer les données de l'entreprise
    const entrepriseData = {
      nom: userData.nomEntreprise || userData.nom,
      raisonSociale: userData.nomEntreprise,
      adresse: userData.adresseEntreprise,
      email: userData.email,
      telephone: userData.telephoneEntreprise,
      numeroFiscal: userData.numeroFiscal,
      pays: 'France',
      responsableId: userId,
      createdBy: userId
    };

    // Vérifier si une entreprise avec ce responsable existe déjà
    let existingEntreprise = await ResponsableEntreprise.findOne({ responsableId: userId });

    // Préparer les données simples pour la collection Entreprise
    const simpleEntrepriseData = {
      nom: userData.nomEntreprise || userData.nom,
      adresse: userData.adresseEntreprise,
      telephone: userData.telephoneEntreprise,
      numéroFiscal: userData.numeroFiscal,
    };

    if (existingEntreprise) {
      // Mettre à jour l'entreprise existante
      console.log('Mise à jour de l\'entreprise existante:', existingEntreprise._id);
      existingEntreprise = await ResponsableEntreprise.findByIdAndUpdate(
        existingEntreprise._id,
        entrepriseData,
        { new: true }
      );
    } else {
      // Créer une nouvelle entreprise
      console.log('Création d\'une nouvelle entreprise pour le responsable:', userId);
      const newEntreprise = new ResponsableEntreprise({
        ...entrepriseData,
        responsableId: userId
      });
      await newEntreprise.save();
      existingEntreprise = newEntreprise;

      // Mettre à jour l'utilisateur avec l'ID de l'entreprise
      await User.findByIdAndUpdate(userId, {
        entrepriseId: newEntreprise._id,
        responsableId: newEntreprise._id
      });
    }

    // Aussi sauvegarder/mettre à jour dans la collection Entreprise pour la page "Mon Entreprise"
    try {
      const existingSimpleEntreprise = await Entreprise.findOne();
      if (existingSimpleEntreprise) {
        await Entreprise.findOneAndUpdate({}, simpleEntrepriseData, { new: true });
        console.log('Collection Entreprise mise à jour avec les données simples');
      } else {
        const newSimpleEntreprise = new Entreprise(simpleEntrepriseData);
        await newSimpleEntreprise.save();
        console.log('Nouvelle entrée créée dans la collection Entreprise');
      }
    } catch (simpleError) {
      console.error('Erreur lors de la sauvegarde dans la collection Entreprise:', simpleError);
      // On continue même si cette sauvegarde échoue
    }

    return existingEntreprise;
  } catch (error) {
    console.error('Erreur lors de la création/mise à jour de l\'entreprise:', error);
    throw error;
  }
};

/**
 * Récupère toutes les entreprises
 * @returns {Promise<Array>} - Liste des entreprises
 */
const getAllEntreprises = async () => {
  try {
    return await ResponsableEntreprise.find();
  } catch (error) {
    console.error('Erreur lors de la récupération des entreprises:', error);
    throw error;
  }
};

/**
 * Récupère une entreprise par son ID
 * @param {String} id - ID de l'entreprise
 * @returns {Promise<Object>} - L'entreprise trouvée
 */
const getEntrepriseById = async (id) => {
  try {
    return await ResponsableEntreprise.findById(id);
  } catch (error) {
    console.error(`Erreur lors de la récupération de l'entreprise ${id}:`, error);
    throw error;
  }
};

/**
 * Récupère l'entreprise associée à un responsable
 * @param {String} responsableId - ID du responsable
 * @returns {Promise<Object>} - L'entreprise associée au responsable
 */
const getEntrepriseByResponsable = async (responsableId) => {
  try {
    return await ResponsableEntreprise.findOne({ responsableId });
  } catch (error) {
    console.error(`Erreur lors de la récupération de l'entreprise pour le responsable ${responsableId}:`, error);
    throw error;
  }
};

module.exports = {
  createOrUpdateEntreprise,
  getAllEntreprises,
  getEntrepriseById,
  getEntrepriseByResponsable
};
