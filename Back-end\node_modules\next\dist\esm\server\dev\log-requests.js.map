{"version": 3, "sources": ["../../../src/server/dev/log-requests.ts"], "sourcesContent": ["import {\n  blue,\n  bold,\n  gray,\n  green,\n  red,\n  white,\n  yellow,\n} from '../../lib/picocolors'\nimport { stripNextRscUnionQuery } from '../../lib/url'\nimport type { FetchMetric } from '../base-http'\nimport type { NodeNextRequest, NodeNextResponse } from '../base-http/node'\nimport type { LoggingConfig } from '../config-shared'\nimport { getRequestMeta } from '../request-meta'\n\nexport interface RequestLoggingOptions {\n  readonly request: NodeNextRequest\n  readonly response: NodeNextResponse\n  readonly loggingConfig: LoggingConfig | undefined\n  readonly requestDurationInMs: number\n}\n\n/**\n * Returns true if the incoming request should be ignored for logging.\n */\nexport function ignoreLoggingIncomingRequests(\n  request: NodeNextRequest,\n  loggingConfig: LoggingConfig | undefined\n): boolean {\n  // If it's boolean use the boolean value\n  if (typeof loggingConfig?.incomingRequests === 'boolean') {\n    return !loggingConfig.incomingRequests\n  }\n\n  // Any of the value on the chain is falsy, will not ignore the request.\n  const ignore = loggingConfig?.incomingRequests?.ignore\n\n  // If ignore is not set, don't ignore anything\n  if (!ignore) {\n    return false\n  }\n\n  // If array of RegExp, ignore if any pattern matches\n  return ignore.some((pattern) => pattern.test(request.url))\n}\n\nexport function logRequests(options: RequestLoggingOptions): void {\n  const { request, response, loggingConfig, requestDurationInMs } = options\n\n  if (!ignoreLoggingIncomingRequests(request, loggingConfig)) {\n    logIncomingRequests({\n      request,\n      requestDurationInMs,\n      statusCode: response.statusCode,\n    })\n  }\n\n  if (request.fetchMetrics) {\n    for (const fetchMetric of request.fetchMetrics) {\n      logFetchMetric(fetchMetric, loggingConfig)\n    }\n  }\n}\n\ninterface IncomingRequestOptions {\n  readonly request: NodeNextRequest\n  readonly requestDurationInMs: number\n  readonly statusCode: number\n}\n\nfunction logIncomingRequests(options: IncomingRequestOptions): void {\n  const { request, requestDurationInMs, statusCode } = options\n  const isRSC = getRequestMeta(request, 'isRSCRequest')\n  const url = isRSC ? stripNextRscUnionQuery(request.url) : request.url\n\n  const statusCodeColor =\n    statusCode < 200\n      ? white\n      : statusCode < 300\n        ? green\n        : statusCode < 400\n          ? blue\n          : statusCode < 500\n            ? yellow\n            : red\n\n  const coloredStatus = statusCodeColor(statusCode.toString())\n\n  return writeLine(\n    `${request.method} ${url} ${coloredStatus} in ${requestDurationInMs}ms`\n  )\n}\n\nfunction logFetchMetric(\n  fetchMetric: FetchMetric,\n  loggingConfig: LoggingConfig | undefined\n): void {\n  let {\n    cacheReason,\n    cacheStatus,\n    cacheWarning,\n    end,\n    method,\n    start,\n    status,\n    url,\n  } = fetchMetric\n\n  if (cacheStatus === 'hmr' && !loggingConfig?.fetches?.hmrRefreshes) {\n    // Cache hits during HMR refreshes are intentionally not logged, unless\n    // explicitly enabled in the logging config.\n    return\n  }\n\n  if (loggingConfig?.fetches) {\n    if (url.length > 48 && !loggingConfig.fetches.fullUrl) {\n      url = truncateUrl(url)\n    }\n\n    writeLine(\n      white(\n        `${method} ${url} ${status} in ${Math.round(end - start)}ms ${formatCacheStatus(cacheStatus)}`\n      ),\n      1\n    )\n\n    if (cacheStatus === 'skip' || cacheStatus === 'miss') {\n      writeLine(\n        gray(\n          `Cache ${cacheStatus === 'skip' ? 'skipped' : 'missed'} reason: (${white(cacheReason)})`\n        ),\n        2\n      )\n    }\n  } else if (cacheWarning) {\n    // When logging for fetches is not enabled, we still want to print any\n    // associated warnings, so we print the request first to provide context.\n    writeLine(white(`${method} ${url}`), 1)\n  }\n\n  if (cacheWarning) {\n    writeLine(`${yellow(bold('⚠'))} ${white(cacheWarning)}`, 2)\n  }\n}\n\nfunction writeLine(text: string, indentationLevel = 0): void {\n  process.stdout.write(` ${'│ '.repeat(indentationLevel)}${text}\\n`)\n}\n\nfunction truncate(text: string, maxLength: number): string {\n  return maxLength !== undefined && text.length > maxLength\n    ? text.substring(0, maxLength) + '..'\n    : text\n}\n\nfunction truncateUrl(url: string): string {\n  const { protocol, host, pathname, search } = new URL(url)\n\n  return (\n    protocol +\n    '//' +\n    truncate(host, 16) +\n    truncate(pathname, 24) +\n    truncate(search, 16)\n  )\n}\n\nfunction formatCacheStatus(cacheStatus: FetchMetric['cacheStatus']): string {\n  switch (cacheStatus) {\n    case 'hmr':\n      return green('(HMR cache)')\n    case 'hit':\n      return green('(cache hit)')\n    default:\n      return yellow(`(cache ${cacheStatus})`)\n  }\n}\n"], "names": ["blue", "bold", "gray", "green", "red", "white", "yellow", "stripNextRscUnionQuery", "getRequestMeta", "ignoreLoggingIncomingRequests", "request", "loggingConfig", "incomingRequests", "ignore", "some", "pattern", "test", "url", "logRequests", "options", "response", "requestDurationInMs", "logIncomingRequests", "statusCode", "fetchMetrics", "fetchMetric", "logFetchMetric", "isRSC", "statusCodeColor", "coloredStatus", "toString", "writeLine", "method", "cacheReason", "cacheStatus", "cacheWarning", "end", "start", "status", "fetches", "hmrRefreshes", "length", "fullUrl", "truncateUrl", "Math", "round", "formatCacheStatus", "text", "indentationLevel", "process", "stdout", "write", "repeat", "truncate", "max<PERSON><PERSON><PERSON>", "undefined", "substring", "protocol", "host", "pathname", "search", "URL"], "mappings": "AAAA,SACEA,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,QACD,uBAAsB;AAC7B,SAASC,sBAAsB,QAAQ,gBAAe;AAItD,SAASC,cAAc,QAAQ,kBAAiB;AAShD;;CAEC,GACD,OAAO,SAASC,8BACdC,OAAwB,EACxBC,aAAwC;QAQzBA;IANf,wCAAwC;IACxC,IAAI,QAAOA,iCAAAA,cAAeC,gBAAgB,MAAK,WAAW;QACxD,OAAO,CAACD,cAAcC,gBAAgB;IACxC;IAEA,uEAAuE;IACvE,MAAMC,SAASF,kCAAAA,kCAAAA,cAAeC,gBAAgB,qBAA/BD,gCAAiCE,MAAM;IAEtD,8CAA8C;IAC9C,IAAI,CAACA,QAAQ;QACX,OAAO;IACT;IAEA,oDAAoD;IACpD,OAAOA,OAAOC,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAACN,QAAQO,GAAG;AAC1D;AAEA,OAAO,SAASC,YAAYC,OAA8B;IACxD,MAAM,EAAET,OAAO,EAAEU,QAAQ,EAAET,aAAa,EAAEU,mBAAmB,EAAE,GAAGF;IAElE,IAAI,CAACV,8BAA8BC,SAASC,gBAAgB;QAC1DW,oBAAoB;YAClBZ;YACAW;YACAE,YAAYH,SAASG,UAAU;QACjC;IACF;IAEA,IAAIb,QAAQc,YAAY,EAAE;QACxB,KAAK,MAAMC,eAAef,QAAQc,YAAY,CAAE;YAC9CE,eAAeD,aAAad;QAC9B;IACF;AACF;AAQA,SAASW,oBAAoBH,OAA+B;IAC1D,MAAM,EAAET,OAAO,EAAEW,mBAAmB,EAAEE,UAAU,EAAE,GAAGJ;IACrD,MAAMQ,QAAQnB,eAAeE,SAAS;IACtC,MAAMO,MAAMU,QAAQpB,uBAAuBG,QAAQO,GAAG,IAAIP,QAAQO,GAAG;IAErE,MAAMW,kBACJL,aAAa,MACTlB,QACAkB,aAAa,MACXpB,QACAoB,aAAa,MACXvB,OACAuB,aAAa,MACXjB,SACAF;IAEZ,MAAMyB,gBAAgBD,gBAAgBL,WAAWO,QAAQ;IAEzD,OAAOC,UACL,GAAGrB,QAAQsB,MAAM,CAAC,CAAC,EAAEf,IAAI,CAAC,EAAEY,cAAc,IAAI,EAAER,oBAAoB,EAAE,CAAC;AAE3E;AAEA,SAASK,eACPD,WAAwB,EACxBd,aAAwC;QAaVA;IAX9B,IAAI,EACFsB,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,GAAG,EACHJ,MAAM,EACNK,KAAK,EACLC,MAAM,EACNrB,GAAG,EACJ,GAAGQ;IAEJ,IAAIS,gBAAgB,SAAS,EAACvB,kCAAAA,yBAAAA,cAAe4B,OAAO,qBAAtB5B,uBAAwB6B,YAAY,GAAE;QAClE,uEAAuE;QACvE,4CAA4C;QAC5C;IACF;IAEA,IAAI7B,iCAAAA,cAAe4B,OAAO,EAAE;QAC1B,IAAItB,IAAIwB,MAAM,GAAG,MAAM,CAAC9B,cAAc4B,OAAO,CAACG,OAAO,EAAE;YACrDzB,MAAM0B,YAAY1B;QACpB;QAEAc,UACE1B,MACE,GAAG2B,OAAO,CAAC,EAAEf,IAAI,CAAC,EAAEqB,OAAO,IAAI,EAAEM,KAAKC,KAAK,CAACT,MAAMC,OAAO,GAAG,EAAES,kBAAkBZ,cAAc,GAEhG;QAGF,IAAIA,gBAAgB,UAAUA,gBAAgB,QAAQ;YACpDH,UACE7B,KACE,CAAC,MAAM,EAAEgC,gBAAgB,SAAS,YAAY,SAAS,UAAU,EAAE7B,MAAM4B,aAAa,CAAC,CAAC,GAE1F;QAEJ;IACF,OAAO,IAAIE,cAAc;QACvB,sEAAsE;QACtE,yEAAyE;QACzEJ,UAAU1B,MAAM,GAAG2B,OAAO,CAAC,EAAEf,KAAK,GAAG;IACvC;IAEA,IAAIkB,cAAc;QAChBJ,UAAU,GAAGzB,OAAOL,KAAK,MAAM,CAAC,EAAEI,MAAM8B,eAAe,EAAE;IAC3D;AACF;AAEA,SAASJ,UAAUgB,IAAY,EAAEC,mBAAmB,CAAC;IACnDC,QAAQC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAKC,MAAM,CAACJ,oBAAoBD,KAAK,EAAE,CAAC;AACnE;AAEA,SAASM,SAASN,IAAY,EAAEO,SAAiB;IAC/C,OAAOA,cAAcC,aAAaR,KAAKN,MAAM,GAAGa,YAC5CP,KAAKS,SAAS,CAAC,GAAGF,aAAa,OAC/BP;AACN;AAEA,SAASJ,YAAY1B,GAAW;IAC9B,MAAM,EAAEwC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAG,IAAIC,IAAI5C;IAErD,OACEwC,WACA,OACAJ,SAASK,MAAM,MACfL,SAASM,UAAU,MACnBN,SAASO,QAAQ;AAErB;AAEA,SAASd,kBAAkBZ,WAAuC;IAChE,OAAQA;QACN,KAAK;YACH,OAAO/B,MAAM;QACf,KAAK;YACH,OAAOA,MAAM;QACf;YACE,OAAOG,OAAO,CAAC,OAAO,EAAE4B,YAAY,CAAC,CAAC;IAC1C;AACF"}