import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  Button,
  CircularProgress,
  Alert,
  useTheme,
  IconButton,
  Tooltip,
  Chip,
  Avatar,
  Stack,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  FormControl,
  Select,
  Snackbar
} from '@mui/material';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import {
  Refresh as RefreshIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  People as PeopleIcon,
  PeopleAlt as PeopleAltIcon,
  AttachMoney as MoneyIcon,
  FileDownload as FileDownloadIcon,
  DateRange as DateRangeIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Search as SearchIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import { useAuth } from '../contexts/AuthContext';
import SimpleDateFilter from '../components/SimpleDateFilter';
import { formatCurrency } from '../utils/formatters';
import analyticsService from '../services/analyticsService';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { fr } from 'date-fns/locale';
import PowerBIExport from '../components/PowerBIExport';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
};

const cardVariants = {
  hidden: { scale: 0.95, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  },
  hover: {
    scale: 1.02,
    boxShadow: "0px 8px 25px rgba(0, 0, 0, 0.1)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

const VendeurBIDashboard = () => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('allTime');
  const [error, setError] = useState(null);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [searchQuery, setSearchQuery] = useState('');
  // Les états pour l'exportation ont été supprimés car nous utilisons maintenant le composant PowerBIExport

  // Initialize sales data with mock values for recharts
  const [salesData, setSalesData] = useState([
    { month: 'Jan', factures: 0, devis: 0 },
    { month: 'Fév', factures: 0, devis: 0 },
    { month: 'Mar', factures: 0, devis: 0 },
    { month: 'Avr', factures: 0, devis: 0 },
    { month: 'Mai', factures: 0, devis: 0 },
    { month: 'Juin', factures: 0, devis: 0 },
    { month: 'Juil', factures: 0, devis: 0 },
    { month: 'Août', factures: 0, devis: 0 },
    { month: 'Sep', factures: 0, devis: 0 },
    { month: 'Oct', factures: 0, devis: 0 },
    { month: 'Nov', factures: 0, devis: 0 },
    { month: 'Déc', factures: 0, devis: 0 }
  ]);

  // Initialize category data with mock values for recharts
  const [categoryData, setCategoryData] = useState([
    { name: 'Produits', value: 0 },
    { name: 'Services', value: 0 },
    { name: 'Autres', value: 0 }
  ]);

  // Removed client distribution data as it's no longer needed

  // Initialize table data with empty arrays
  const [tableData, setTableData] = useState({
    recentInvoices: [],
    recentQuotes: [],
    topClients: []
  });

  // Initialize stats with zeros
  const [stats, setStats] = useState({
    factures: 0,
    devis: 0,
    clients: 0,
    totalRevenue: 0,
    paidInvoices: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
    acceptedDevis: 0
  });

  // Initialize product stats with empty values
  const [productStats, setProductStats] = useState({
    topSellingProduct: {
      name: '',
      sales: 0,
      category: ''
    },
    mostProfitableProduct: {
      name: '',
      margin: 0,
      category: ''
    },
    topCategory: {
      name: '',
      percentage: 0
    },
    lowStock: {
      count: 0,
      products: []
    }
  });

  // Handle date range change
  const handleDateRangeChange = (rangeId, dateRange) => {
    // Map the range ID to period format
    let newRange;
    switch(rangeId) {
      case 'today':
      case 'yesterday':
        newRange = 'daily';
        break;
      case 'thisWeek':
      case 'lastWeek':
        newRange = 'weekly';
        break;
      case 'thisMonth':
      case 'lastMonth':
        newRange = 'monthly';
        break;
      case 'thisQuarter':
        newRange = 'quarterly';
        break;
      case 'thisYear':
      case 'lastYear':
        newRange = 'yearly';
        break;
      case 'allTime':
        newRange = 'all-time';
        break;
      case 'custom':
        newRange = 'custom';
        break;
      default:
        newRange = 'monthly';
    }

    setTimeRange(newRange);
    setLoading(true);
    fetchDashboardData(newRange, dateRange);
  };

  // Refresh data
  const handleRefresh = () => {
    setRefreshing(true);
    // Utiliser la période actuellement sélectionnée pour filtrer les données correctement
    fetchDashboardData(timeRange).finally(() => {
      setRefreshing(false);
      setNotification({
        open: true,
        message: 'Données actualisées avec succès',
        severity: 'success'
      });
    });
  };

  // La fonction d'exportation a été supprimée car nous utilisons maintenant le composant PowerBIExport

  // Fetch dashboard data from the API
  const fetchDashboardData = async (dateRange, customRange = null) => {
    try {
      console.log('VendeurBIDashboard - fetchDashboardData called with dateRange:', dateRange, 'customRange:', customRange);

      const data = await analyticsService.getVendeurDashboardStats(dateRange, customRange);
      console.log('VendeurBIDashboard - Data received from API:', data);

      setStats(data);

      // Update sales data for recharts based on the selected date range
      let chartLabels = [];
      let chartData = [];

      // Format chart data based on date range
      if (dateRange === 'today' || dateRange === 'yesterday') {
        // For daily view, show hourly data
        chartLabels = Array.from({ length: 24 }, (_, i) => `${i}h`);
        chartData = data.salesByHour || { invoices: Array(24).fill(0), quotes: Array(24).fill(0) };
      } else if (dateRange === 'thisWeek' || dateRange === 'lastWeek') {
        // For weekly view, show daily data
        chartLabels = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
        chartData = data.salesByDay || { invoices: Array(7).fill(0), quotes: Array(7).fill(0) };
      } else if (dateRange === 'thisMonth' || dateRange === 'lastMonth') {
        // For monthly view, show data by day of month (simplified to weeks)
        chartLabels = ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4', 'Sem 5'];
        chartData = data.salesByWeek || { invoices: Array(5).fill(0), quotes: Array(5).fill(0) };
      } else {
        // Default to monthly data for longer periods
        chartLabels = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
        chartData = data.salesByMonth || { invoices: Array(12).fill(0), quotes: Array(12).fill(0) };
      }

      // Create formatted sales data for the chart
      const newSalesData = chartLabels.map((label, index) => ({
        month: label, // Keep the property name as 'month' for compatibility
        factures: chartData.invoices?.[index] || 0,
        devis: chartData.quotes?.[index] || 0
      }));

      setSalesData(newSalesData);

      // Update category data for recharts with improved formatting
      const categories = ['Produits', 'Services', 'Autres'];
      const newCategoryData = categories.map((name, index) => ({
        name,
        value: data.categoryAnalysis?.[index] || 0
      }));

      // Filter out zero values for better visualization
      const filteredCategoryData = newCategoryData.filter(item => item.value > 0);

      // If all values are zero, keep at least one category
      setCategoryData(filteredCategoryData.length > 0 ? filteredCategoryData : [{ name: 'Aucune donnée', value: 1 }]);

      // Update table data - ensure arrays are defined even if empty
      setTableData({
        recentInvoices: data.recentInvoices || [],
        recentQuotes: data.recentQuotes || [],
        topClients: data.topClients || []
      });

      // Update product stats from API data with default values
      const defaultProductStats = {
        topSellingProduct: {
          name: 'Aucun produit',
          sales: 0,
          category: 'Produits'
        },
        mostProfitableProduct: {
          name: 'Aucun produit',
          margin: 0,
          category: 'Produits'
        },
        topCategory: {
          name: 'Produits',
          percentage: 0
        },
        lowStock: {
          count: 0,
          products: []
        }
      };

      if (data.productStats) {
        // Merge with default values to ensure all fields are present
        setProductStats({...defaultProductStats, ...data.productStats});
      } else if (data.products && data.productCategories) {
        // Format product stats from direct API response
        const formattedProductStats = {
          topSellingProduct: {
            name: data.products.topSelling?.name || 'Aucun produit',
            sales: data.products.topSelling?.sales || 0,
            category: data.products.topSelling?.category || 'Produits'
          },
          mostProfitableProduct: {
            name: data.products.mostProfitable?.name || 'Aucun produit',
            margin: data.products.mostProfitable?.margin || 0,
            category: data.products.mostProfitable?.category || 'Produits'
          },
          topCategory: {
            name: 'Produits',
            percentage: data.productCategories.products || 0
          },
          lowStock: {
            count: data.products.lowStock?.length || 0,
            products: data.products.lowStock?.map(p => p.name) || []
          }
        };
        setProductStats(formattedProductStats);
      } else {
        // If no data is available, use default values
        setProductStats(defaultProductStats);
      }

      // Only show notification if not refreshing (initial load)
      if (!refreshing) {
        setNotification({
          open: true,
          message: 'Données chargées avec succès',
          severity: 'success'
        });
      }
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Erreur lors du chargement des données. Veuillez réessayer.');
      setNotification({
        open: true,
        message: 'Erreur lors du chargement des données',
        severity: 'error'
      });
    } finally {
      // Only update loading state if not refreshing
      if (!refreshing) {
        setLoading(false);
      }
      // Note: refreshing state is handled in the handleRefresh function
    }
  };

  // Les fonctions de gestion du dialogue d'exportation ont été supprimées car nous utilisons maintenant le composant PowerBIExport

  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  useEffect(() => {
    // Fetch data when component mounts
    // Utiliser la période par défaut (timeRange) pour le chargement initial
    fetchDashboardData(timeRange);
  }, []);

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.info.main,
    theme.palette.warning.main,
  ];

  // KPI Card Component
  const KpiCard = ({ title, value, subtitle, icon, color }) => {
    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <Card
          elevation={0}
          sx={{
            height: '100%',
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette[color].light, 0.2)} 0%, ${alpha(theme.palette[color].main, 0.05)} 100%)`,
            border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette[color].main, 0.03)} 100%)`,
              zIndex: 0
            }
          }}
        >
          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  backgroundColor: alpha(theme.palette[color].main, 0.15),
                  color: theme.palette[color].main,
                  mr: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette[color].main, 0.2)}`
                }}
              >
                {icon}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="500" color="text.primary">
                  {title}
                </Typography>
                {/* Removed trend indicator and percentage */}
              </Box>
            </Box>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                color: theme.palette[color].dark,
                textShadow: `0 2px 4px ${alpha(theme.palette[color].main, 0.2)}`
              }}
            >
              {value}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                opacity: 0.8,
                maxWidth: '90%'
              }}
            >
              {subtitle}
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Nous avons remplacé le dialogue d'exportation par le composant PowerBIExport intégré directement dans le tableau de bord

  // Skip loading state and render the dashboard immediately
  // This removes the loading spinner and message

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 2, sm: 3 },
        background: `linear-gradient(180deg, ${alpha(theme.palette.background.default, 0.6)} 0%, ${theme.palette.background.default} 100%)`,
        minHeight: '100vh'
      }}
    >
      <Container maxWidth="xl">
        {/* Header */}
        <Box
          component={motion.div}
          variants={itemVariants}
          sx={{
            mb: 4,
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', md: 'center' }
          }}
        >
          <Box>
            <Typography
              variant="h3"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}
            >
              Tableau de Bord Vendeur
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
              Bienvenue, {currentUser?.nom || 'Vendeur'}. Visualisez vos performances de vente et suivez vos clients.
            </Typography>
          </Box>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mt: { xs: 2, md: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            <TextField
              placeholder="Rechercher..."
              size="small"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                sx: { borderRadius: 2 }
              }}
              sx={{ width: { xs: '100%', sm: 220 } }}
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleRefresh}
              startIcon={refreshing ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
              disabled={refreshing}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                minWidth: 130
              }}
            >
              {refreshing ? 'Actualisation...' : 'Actualiser'}
            </Button>

            <SimpleDateFilter
              onDateRangeChange={handleDateRangeChange}
              onRefresh={handleRefresh}
              initialRange="allTime"
              showRefreshButton={false}
            />

            {/* Le bouton d'exportation a été supprimé car nous utilisons maintenant le composant PowerBIExport */}
          </Stack>
        </Box>

        {/* Dashboard Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* KPI Cards */}
          <Grid
            container
            spacing={3}
            sx={{ mb: 4 }}
            component={motion.div}
            variants={itemVariants}
          >
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Factures"
                value={stats.factures}
                subtitle={`${stats.paidInvoices} factures payées`}
                icon={<ReceiptIcon />}
                color="primary"
                trend={true}
                trendValue={5.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Devis"
                value={stats.devis}
                subtitle={`${stats.acceptedDevis} devis acceptés`}
                icon={<DescriptionIcon />}
                color="secondary"
                trend={true}
                trendValue={3.8}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Clients"
                value={stats.clients}
                subtitle={`${tableData.topClients.length} clients actifs`}
                icon={<PeopleIcon />}
                color="info"
                trend={true}
                trendValue={7.5}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KpiCard
                title="Revenus"
                value={`${formatCurrency(stats.totalRevenue)} DT`}
                subtitle={`${stats.paidInvoices} factures payées`}
                icon={<MoneyIcon />}
                color="success"
                trend={true}
                trendValue={12.3}
              />
            </Grid>
          </Grid>

          {/* Main Content */}
          <Grid
            container
            spacing={3}
            component={motion.div}
            variants={itemVariants}
          >
            {/* Left Column - Charts */}
            <Grid item xs={12} lg={8}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    mb: 3,
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h5" fontWeight="600">
                        Ventes
                      </Typography>
                    }
                    sx={{
                      px: 3,
                      pt: 3,
                      pb: 2,
                      '& .MuiCardHeader-action': {
                        m: 0,
                        alignSelf: 'center'
                      }
                    }}
                  />
                  <Divider />
                  <CardContent sx={{ height: 400, p: 3 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={salesData}>
                        <defs>
                          <linearGradient id="colorFactures" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8} />
                            <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0} />
                          </linearGradient>
                          <linearGradient id="colorDevis" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={theme.palette.secondary.main} stopOpacity={0.8} />
                            <stop offset="95%" stopColor={theme.palette.secondary.main} stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                        <XAxis
                          dataKey="month"
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                        />
                        <YAxis
                          stroke={theme.palette.text.secondary}
                          fontSize={12}
                          tickLine={false}
                          axisLine={{ stroke: theme.palette.divider }}
                          tickFormatter={(value) => `${value}`}
                        />
                        <RechartsTooltip
                          formatter={(value) => [`${value}`, '']}
                          labelFormatter={(label) => `Mois: ${label}`}
                          contentStyle={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 8,
                            boxShadow: theme.shadows[3],
                            padding: '10px 14px'
                          }}
                        />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="factures"
                          stroke={theme.palette.primary.main}
                          strokeWidth={3}
                          fillOpacity={1}
                          fill="url(#colorFactures)"
                          activeDot={{ r: 8, fill: theme.palette.primary.main, strokeWidth: 0 }}
                          name="Factures"
                        />
                        <Area
                          type="monotone"
                          dataKey="devis"
                          stroke={theme.palette.secondary.main}
                          strokeWidth={3}
                          fillOpacity={1}
                          fill="url(#colorDevis)"
                          activeDot={{ r: 8, fill: theme.palette.secondary.main, strokeWidth: 0 }}
                          name="Devis"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </motion.div>

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <motion.div variants={cardVariants}>
                    <Card
                      elevation={0}
                      sx={{
                        height: '100%',
                        borderRadius: 3,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                        overflow: 'hidden'
                      }}
                    >
                      <CardHeader
                        title={
                          <Typography variant="h6" fontWeight="600">
                            Analyse par catégorie
                          </Typography>
                        }
                        sx={{ px: 3, pt: 3, pb: 2 }}
                      />
                      <Divider />
                      <CardContent sx={{ height: 300, p: 3 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={categoryData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={80}
                              paddingAngle={3}
                              dataKey="value"
                              label={(props) => {
                                const { cx, cy, midAngle, outerRadius, name, percent } = props;
                                const radius = outerRadius * 1.2;
                                const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
                                const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);
                                return (
                                  <text
                                    x={x}
                                    y={y}
                                    fill={theme.palette.text.primary}
                                    textAnchor={x > cx ? 'start' : 'end'}
                                    dominantBaseline="central"
                                    fontSize="12px"
                                    fontWeight="500"
                                  >
                                    {`${name}: ${(percent * 100).toFixed(0)}%`}
                                  </text>
                                );
                              }}
                              labelLine={{
                                stroke: theme.palette.divider,
                                strokeWidth: 1,
                                strokeDasharray: '3 3'
                              }}
                            >
                              {categoryData.map((_, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Legend
                              verticalAlign="bottom"
                              height={40}
                              iconSize={10}
                              iconType="circle"
                              layout="horizontal"
                              formatter={(value) => (
                                <span style={{
                                  fontSize: '0.875rem',
                                  color: theme.palette.text.primary,
                                  fontWeight: 500,
                                  padding: '0 8px'
                                }}>
                                  {value}
                                </span>
                              )}
                              wrapperStyle={{
                                paddingTop: 10,
                                marginTop: 10,
                                borderTop: `1px solid ${theme.palette.divider}`
                              }}
                            />
                            <Tooltip formatter={(value) => [`${value}`, 'Montant']} />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>
            </Grid>

            {/* Right Column - Tables */}
            <Grid item xs={12} lg={4}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    mb: 3,
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Factures récentes
                      </Typography>
                    }
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ p: 0 }}>
                    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {tableData.recentInvoices && tableData.recentInvoices.length > 0 ? (
                        <Box sx={{ p: 2 }}>
                          {tableData.recentInvoices.map((invoice, index) => (
                            <Box
                              key={index}
                              sx={{
                                p: 2,
                                borderBottom: index < tableData.recentInvoices.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                                '&:hover': {
                                  bgcolor: alpha(theme.palette.primary.main, 0.05)
                                }
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="subtitle1" fontWeight="medium">
                                  {invoice.id}
                                </Typography>
                                <Chip
                                  label={invoice.statut === 'PAID' ? 'Payée' : invoice.statut === 'PENDING' ? 'En attente' : 'En retard'}
                                  size="small"
                                  color={invoice.statut === 'PAID' ? 'success' : invoice.statut === 'PENDING' ? 'warning' : 'error'}
                                  sx={{ height: 24 }}
                                />
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                  {invoice.client}
                                </Typography>
                                <Typography variant="body2" fontWeight="bold">
                                  {formatCurrency(invoice.montant)} DT
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary">
                                Date: {invoice.date}
                              </Typography>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{
                          height: 200,
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          bgcolor: 'background.paper',
                          borderRadius: 1,
                          p: 2
                        }}>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Aucune facture récente à afficher
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden',
                    bgcolor: alpha(theme.palette.primary.light, 0.05)
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Meilleurs clients
                      </Typography>
                    }
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ p: 0 }}>
                    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {tableData.topClients && tableData.topClients.length > 0 ? (
                        <Box sx={{ p: 2 }}>
                          {tableData.topClients.map((client, index) => (
                            <Box
                              key={index}
                              sx={{
                                p: 2,
                                borderBottom: index < tableData.topClients.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                                '&:hover': {
                                  bgcolor: alpha(theme.palette.primary.main, 0.05)
                                },
                                transition: 'all 0.2s ease-in-out'
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="subtitle1" fontWeight="medium">
                                  {client.nom || 'Client sans nom'}
                                </Typography>
                                <Typography variant="body2" fontWeight="bold" color="primary.main">
                                  {formatCurrency(client.montantTotal)} DT
                                </Typography>
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="body2" color="text.secondary" sx={{ maxWidth: '60%', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                                  {client.email || 'Pas d\'email'}
                                </Typography>
                                <Chip
                                  label={`${client.facturesCount} facture${client.facturesCount > 1 ? 's' : ''}`}
                                  size="small"
                                  color="primary"
                                  sx={{
                                    height: 24,
                                    fontSize: '0.7rem',
                                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                                    boxShadow: `0 2px 5px ${alpha(theme.palette.primary.main, 0.2)}`
                                  }}
                                />
                              </Box>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{
                          height: 200,
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          bgcolor: alpha(theme.palette.background.paper, 0.5),
                          borderRadius: 2,
                          p: 3
                        }}>
                          <PeopleAltIcon sx={{ fontSize: 40, color: alpha(theme.palette.text.secondary, 0.4), mb: 1 }} />
                          <Typography variant="body1" color="text.secondary" align="center">
                            Aucun client à afficher
                          </Typography>
                          <Typography variant="caption" color="text.secondary" align="center" sx={{ mt: 1 }}>
                            Les meilleurs clients apparaîtront ici une fois que vous aurez des factures
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            {/* Power BI Export Component */}
            <Grid item xs={12}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden',
                    background: `linear-gradient(135deg, ${alpha(theme.palette.info.light, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Exporter les données pour Power BI
                      </Typography>
                    }
                    subheader="Exportez vos données au format CSV pour les importer dans Power BI"
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ p: 3 }}>
                    <PowerBIExport />
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Notifications */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
            {notification.message}
          </Alert>
        </Snackbar>

      </Container>
    </Box>
  );
}

export default VendeurBIDashboard;
