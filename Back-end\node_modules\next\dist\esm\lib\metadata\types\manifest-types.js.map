{"version": 3, "sources": ["../../../../src/lib/metadata/types/manifest-types.ts"], "sourcesContent": ["type ClientModeEnum =\n  | 'auto'\n  | 'focus-existing'\n  | 'navigate-existing'\n  | 'navigate-new'\n\ntype File = {\n  name: string\n  accept: string | string[]\n}\n\ntype Icon = {\n  src: string\n  type?: string | undefined\n  sizes?: string | undefined\n  purpose?: 'any' | 'maskable' | 'monochrome' | undefined\n}\n\nexport type Manifest = {\n  background_color?: string | undefined\n  categories?: string[] | undefined\n  description?: string | undefined\n  dir?: 'ltr' | 'rtl' | 'auto' | undefined\n  display?: 'fullscreen' | 'standalone' | 'minimal-ui' | 'browser' | undefined\n  display_override?:\n    | (\n        | 'fullscreen'\n        | 'standalone'\n        | 'minimal-ui'\n        | 'browser'\n        | 'window-controls-overlay'\n      )[]\n    | undefined\n  file_handlers?:\n    | {\n        action: string\n        accept: {\n          [mimeType: string]: string[]\n        }\n      }[]\n    | undefined\n  icons?: Icon[] | undefined\n  id?: string | undefined\n  lang?: string | undefined\n  launch_handler?:\n    | {\n        client_mode: ClientModeEnum | ClientModeEnum[]\n      }\n    | undefined\n  name?: string | undefined\n  orientation?:\n    | 'any'\n    | 'natural'\n    | 'landscape'\n    | 'portrait'\n    | 'portrait-primary'\n    | 'portrait-secondary'\n    | 'landscape-primary'\n    | 'landscape-secondary'\n    | undefined\n  prefer_related_applications?: boolean | undefined\n  protocol_handlers?:\n    | {\n        protocol: string\n        url: string\n      }[]\n    | undefined\n  related_applications?:\n    | {\n        platform: string\n        url: string\n        id?: string | undefined\n      }[]\n    | undefined\n  scope?: string | undefined\n  screenshots?:\n    | {\n        form_factor?: 'narrow' | 'wide' | undefined\n        label?: string | undefined\n        platform?:\n          | 'android'\n          | 'chromeos'\n          | 'ipados'\n          | 'ios'\n          | 'kaios'\n          | 'macos'\n          | 'windows'\n          | 'xbox'\n          | 'chrome_web_store'\n          | 'itunes'\n          | 'microsoft-inbox'\n          | 'microsoft-store'\n          | 'play'\n          | undefined\n        src: string\n        type?: string | undefined\n        sizes?: string | undefined\n      }[]\n    | undefined\n  share_target?:\n    | {\n        action: string\n        method?: 'get' | 'post' | 'GET' | 'POST' | undefined\n        enctype?:\n          | 'application/x-www-form-urlencoded'\n          | 'multipart/form-data'\n          | undefined\n        params: {\n          title?: string | undefined\n          text?: string | undefined\n          url?: string | undefined\n          files?: File | File[] | undefined\n        }\n      }\n    | undefined\n  short_name?: string | undefined\n  shortcuts?:\n    | {\n        name: string\n        short_name?: string | undefined\n        description?: string | undefined\n        url: string\n        icons?: Icon[] | undefined\n      }[]\n    | undefined\n  start_url?: string | undefined\n  theme_color?: string | undefined\n}\n"], "names": [], "mappings": "AAkBA,WA6GC"}