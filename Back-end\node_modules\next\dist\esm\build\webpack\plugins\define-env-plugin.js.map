{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "sourcesContent": ["import type {\n  I18NDomains,\n  NextConfigComplete,\n} from '../../../server/config-shared'\nimport type { MiddlewareMatcher } from '../../analysis/get-page-static-info'\nimport { needsExperimentalReact } from '../../../lib/needs-experimental-react'\nimport { checkIsAppPPREnabled } from '../../../server/lib/experimental/ppr'\nimport {\n  getNextConfigEnv,\n  getNextPublicEnvironmentVariables,\n} from '../../../lib/static-env'\nimport getWebpackBundler from '../../../shared/lib/get-webpack-bundler'\n\ntype BloomFilter = ReturnType<\n  import('../../../shared/lib/bloom-filter').BloomFilter['export']\n>\n\nexport interface DefineEnvPluginOptions {\n  isTurbopack: boolean\n  clientRouterFilters?: {\n    staticFilter: BloomFilter\n    dynamicFilter: BloomFilter\n  }\n  config: NextConfigComplete\n  dev: boolean\n  distDir: string\n  fetchCacheKeyPrefix: string | undefined\n  hasRewrites: boolean\n  isClient: boolean\n  isEdgeServer: boolean\n  isNodeOrEdgeCompilation: boolean\n  isNodeServer: boolean\n  middlewareMatchers: MiddlewareMatcher[] | undefined\n  omitNonDeterministic?: boolean\n}\n\ninterface DefineEnv {\n  [key: string]:\n    | string\n    | string[]\n    | boolean\n    | MiddlewareMatcher[]\n    | BloomFilter\n    | Partial<NextConfigComplete['images']>\n    | I18NDomains\n}\n\ninterface SerializedDefineEnv {\n  [key: string]: string\n}\n\n/**\n * Serializes the DefineEnv config so that it can be inserted into the code by Webpack/Turbopack, JSON stringifies each value.\n */\nfunction serializeDefineEnv(defineEnv: DefineEnv): SerializedDefineEnv {\n  const defineEnvStringified: SerializedDefineEnv = {}\n  for (const key in defineEnv) {\n    const value = defineEnv[key]\n    defineEnvStringified[key] = JSON.stringify(value)\n  }\n\n  return defineEnvStringified\n}\n\nfunction getImageConfig(\n  config: NextConfigComplete,\n  dev: boolean\n): { 'process.env.__NEXT_IMAGE_OPTS': Partial<NextConfigComplete['images']> } {\n  return {\n    'process.env.__NEXT_IMAGE_OPTS': {\n      deviceSizes: config.images.deviceSizes,\n      imageSizes: config.images.imageSizes,\n      qualities: config.images.qualities,\n      path: config.images.path,\n      loader: config.images.loader,\n      dangerouslyAllowSVG: config.images.dangerouslyAllowSVG,\n      unoptimized: config?.images?.unoptimized,\n      ...(dev\n        ? {\n            // additional config in dev to allow validating on the client\n            domains: config.images.domains,\n            remotePatterns: config.images?.remotePatterns,\n            localPatterns: config.images?.localPatterns,\n            output: config.output,\n          }\n        : {}),\n    },\n  }\n}\n\nexport function getDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  isClient,\n  isEdgeServer,\n  isNodeOrEdgeCompilation,\n  isNodeServer,\n  middlewareMatchers,\n  omitNonDeterministic,\n}: DefineEnvPluginOptions): SerializedDefineEnv {\n  const nextPublicEnv = getNextPublicEnvironmentVariables()\n  const nextConfigEnv = getNextConfigEnv(config)\n\n  const isPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n  const isDynamicIOEnabled = !!config.experimental.dynamicIO\n  const isUseCacheEnabled = !!config.experimental.useCache\n\n  const defineEnv: DefineEnv = {\n    // internal field to identify the plugin config\n    __NEXT_DEFINE_ENV: true,\n\n    ...nextPublicEnv,\n    ...nextConfigEnv,\n    ...(!isEdgeServer\n      ? {}\n      : {\n          EdgeRuntime:\n            /**\n             * Cloud providers can set this environment variable to allow users\n             * and library authors to have different implementations based on\n             * the runtime they are running with, if it's not using `edge-runtime`\n             */\n            process.env.NEXT_EDGE_RUNTIME_PROVIDER ?? 'edge-runtime',\n\n          // process should be only { env: {...} } for edge runtime.\n          // For ignore avoid warn on `process.emit` usage but directly omit it.\n          'process.emit': false,\n        }),\n    'process.turbopack': isTurbopack,\n    'process.env.TURBOPACK': isTurbopack,\n    'process.env.__NEXT_BUNDLER': isTurbopack\n      ? 'Turbopack'\n      : process.env.NEXT_RSPACK\n        ? 'Rspack'\n        : 'Webpack',\n    // TODO: enforce `NODE_ENV` on `process.env`, and add a test:\n    'process.env.NODE_ENV':\n      dev || config.experimental.allowDevelopmentBuild\n        ? 'development'\n        : 'production',\n    'process.env.NEXT_RUNTIME': isEdgeServer\n      ? 'edge'\n      : isNodeServer\n        ? 'nodejs'\n        : '',\n    'process.env.NEXT_MINIMAL': '',\n    'process.env.__NEXT_APP_NAV_FAIL_HANDLING': Boolean(\n      config.experimental.appNavFailHandling\n    ),\n    'process.env.__NEXT_PPR': isPPREnabled,\n    'process.env.__NEXT_DYNAMIC_IO': isDynamicIOEnabled,\n    'process.env.__NEXT_USE_CACHE': isUseCacheEnabled,\n    'process.env.NEXT_DEPLOYMENT_ID': config.deploymentId || false,\n    // Propagates the `__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING` environment\n    // variable to the client.\n    'process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING':\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING || false,\n    'process.env.__NEXT_FETCH_CACHE_KEY_PREFIX': fetchCacheKeyPrefix ?? '',\n    ...(isTurbopack\n      ? {}\n      : {\n          'process.env.__NEXT_MIDDLEWARE_MATCHERS': middlewareMatchers ?? [],\n        }),\n    'process.env.__NEXT_MANUAL_CLIENT_BASE_PATH':\n      config.experimental.manualClientBasePath ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.dynamic))\n        ? 0\n        : config.experimental.staleTimes?.dynamic\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.static))\n        ? 5 * 60 // 5 minutes\n        : config.experimental.staleTimes?.static\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED':\n      config.experimental.clientRouterFilter ?? true,\n    'process.env.__NEXT_CLIENT_ROUTER_S_FILTER':\n      clientRouterFilters?.staticFilter ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_D_FILTER':\n      clientRouterFilters?.dynamicFilter ?? false,\n    'process.env.__NEXT_CLIENT_SEGMENT_CACHE': Boolean(\n      config.experimental.clientSegmentCache\n    ),\n    'process.env.__NEXT_DYNAMIC_ON_HOVER': Boolean(\n      config.experimental.dynamicOnHover\n    ),\n    'process.env.__NEXT_ROUTER_BF_CACHE': Boolean(\n      config.experimental.routerBFCache\n    ),\n    'process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE':\n      config.experimental.optimisticClientCache ?? true,\n    'process.env.__NEXT_MIDDLEWARE_PREFETCH':\n      config.experimental.middlewarePrefetch ?? 'flexible',\n    'process.env.__NEXT_CROSS_ORIGIN': config.crossOrigin,\n    'process.browser': isClient,\n    'process.env.__NEXT_TEST_MODE': process.env.__NEXT_TEST_MODE ?? false,\n    // This is used in client/dev-error-overlay/hot-dev-client.js to replace the dist directory\n    ...(dev && (isClient ?? isEdgeServer)\n      ? {\n          'process.env.__NEXT_DIST_DIR': distDir,\n        }\n      : {}),\n    'process.env.__NEXT_TRAILING_SLASH': config.trailingSlash,\n    'process.env.__NEXT_DEV_INDICATOR': config.devIndicators !== false,\n    'process.env.__NEXT_DEV_INDICATOR_POSITION':\n      config.devIndicators === false\n        ? 'bottom-left' // This will not be used as the indicator is disabled.\n        : config.devIndicators.position ?? 'bottom-left',\n    'process.env.__NEXT_STRICT_MODE':\n      config.reactStrictMode === null ? false : config.reactStrictMode,\n    'process.env.__NEXT_STRICT_MODE_APP':\n      // When next.config.js does not have reactStrictMode it's enabled by default.\n      config.reactStrictMode === null ? true : config.reactStrictMode,\n    'process.env.__NEXT_OPTIMIZE_CSS':\n      (config.experimental.optimizeCss && !dev) ?? false,\n    'process.env.__NEXT_SCRIPT_WORKERS':\n      (config.experimental.nextScriptWorkers && !dev) ?? false,\n    'process.env.__NEXT_SCROLL_RESTORATION':\n      config.experimental.scrollRestoration ?? false,\n    ...getImageConfig(config, dev),\n    'process.env.__NEXT_ROUTER_BASEPATH': config.basePath,\n    'process.env.__NEXT_STRICT_NEXT_HEAD':\n      config.experimental.strictNextHead ?? true,\n    'process.env.__NEXT_HAS_REWRITES': hasRewrites,\n    'process.env.__NEXT_CONFIG_OUTPUT': config.output,\n    'process.env.__NEXT_I18N_SUPPORT': !!config.i18n,\n    'process.env.__NEXT_I18N_DOMAINS': config.i18n?.domains ?? false,\n    'process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE':\n      config.skipMiddlewareUrlNormalize,\n    'process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE':\n      config.experimental.externalMiddlewareRewritesResolve ?? false,\n    'process.env.__NEXT_MANUAL_TRAILING_SLASH':\n      config.skipTrailingSlashRedirect,\n    'process.env.__NEXT_HAS_WEB_VITALS_ATTRIBUTION':\n      (config.experimental.webVitalsAttribution &&\n        config.experimental.webVitalsAttribution.length > 0) ??\n      false,\n    'process.env.__NEXT_WEB_VITALS_ATTRIBUTION':\n      config.experimental.webVitalsAttribution ?? false,\n    'process.env.__NEXT_LINK_NO_TOUCH_START':\n      config.experimental.linkNoTouchStart ?? false,\n    'process.env.__NEXT_ASSET_PREFIX': config.assetPrefix,\n    'process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS':\n      !!config.experimental.authInterrupts,\n    'process.env.__NEXT_TELEMETRY_DISABLED': Boolean(\n      process.env.NEXT_TELEMETRY_DISABLED\n    ),\n    ...(isNodeOrEdgeCompilation\n      ? {\n          // Fix bad-actors in the npm ecosystem (e.g. `node-formidable`)\n          // This is typically found in unmaintained modules from the\n          // pre-webpack era (common in server-side code)\n          'global.GENTLY': false,\n        }\n      : undefined),\n    ...(isNodeOrEdgeCompilation\n      ? {\n          'process.env.__NEXT_EXPERIMENTAL_REACT':\n            needsExperimentalReact(config),\n        }\n      : undefined),\n  }\n\n  const userDefines = config.compiler?.define ?? {}\n  for (const key in userDefines) {\n    if (defineEnv.hasOwnProperty(key)) {\n      throw new Error(\n        `The \\`compiler.define\\` option is configured to replace the \\`${key}\\` variable. This variable is either part of a Next.js built-in or is already configured via the \\`env\\` option.`\n      )\n    }\n    defineEnv[key] = userDefines[key]\n  }\n\n  const serializedDefineEnv = serializeDefineEnv(defineEnv)\n\n  // we delay inlining these values until after the build\n  // with flying shuttle enabled so we can update them\n  // without invalidating entries\n  if (!dev && omitNonDeterministic) {\n    // client uses window. instead of leaving process.env\n    // in case process isn't polyfilled on client already\n    // since by this point it won't be added by webpack\n    const safeKey = (key: string) =>\n      isClient ? `window.${key.split('.').pop()}` : key\n\n    for (const key in nextPublicEnv) {\n      serializedDefineEnv[key] = safeKey(key)\n    }\n    for (const key in nextConfigEnv) {\n      serializedDefineEnv[key] = safeKey(key)\n    }\n    for (const key of ['process.env.NEXT_DEPLOYMENT_ID']) {\n      serializedDefineEnv[key] = safeKey(key)\n    }\n  }\n\n  return serializedDefineEnv\n}\n\nexport function getDefineEnvPlugin(options: DefineEnvPluginOptions) {\n  return new (getWebpackBundler().DefinePlugin)(getDefineEnv(options))\n}\n"], "names": ["needsExperimentalReact", "checkIsAppPPREnabled", "getNextConfigEnv", "getNextPublicEnvironmentVariables", "getWebpackBundler", "serializeDefineEnv", "defineEnv", "defineEnvStringified", "key", "value", "JSON", "stringify", "getImageConfig", "config", "dev", "deviceSizes", "images", "imageSizes", "qualities", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "localPatterns", "output", "getDefineEnv", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "omitNonDeterministic", "nextPublicEnv", "nextConfigEnv", "isPPREnabled", "experimental", "ppr", "isDynamicIOEnabled", "dynamicIO", "isUseCacheEnabled", "useCache", "__NEXT_DEFINE_ENV", "EdgeRuntime", "process", "env", "NEXT_EDGE_RUNTIME_PROVIDER", "NEXT_RSPACK", "allowDevelopmentBuild", "Boolean", "appNavFailHandling", "deploymentId", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "clientSegmentCache", "dynamicOnHover", "routerBFCache", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "position", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "authInterrupts", "NEXT_TELEMETRY_DISABLED", "undefined", "userDefines", "compiler", "define", "hasOwnProperty", "Error", "serializedDefineEnv", "<PERSON><PERSON><PERSON>", "split", "pop", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAKA,SAASA,sBAAsB,QAAQ,wCAAuC;AAC9E,SAASC,oBAAoB,QAAQ,uCAAsC;AAC3E,SACEC,gBAAgB,EAChBC,iCAAiC,QAC5B,0BAAyB;AAChC,OAAOC,uBAAuB,0CAAyC;AAwCvE;;CAEC,GACD,SAASC,mBAAmBC,SAAoB;IAC9C,MAAMC,uBAA4C,CAAC;IACnD,IAAK,MAAMC,OAAOF,UAAW;QAC3B,MAAMG,QAAQH,SAAS,CAACE,IAAI;QAC5BD,oBAAoB,CAACC,IAAI,GAAGE,KAAKC,SAAS,CAACF;IAC7C;IAEA,OAAOF;AACT;AAEA,SAASK,eACPC,MAA0B,EAC1BC,GAAY;QAUKD,gBAKSA,iBACDA;IAdzB,OAAO;QACL,iCAAiC;YAC/BE,aAAaF,OAAOG,MAAM,CAACD,WAAW;YACtCE,YAAYJ,OAAOG,MAAM,CAACC,UAAU;YACpCC,WAAWL,OAAOG,MAAM,CAACE,SAAS;YAClCC,MAAMN,OAAOG,MAAM,CAACG,IAAI;YACxBC,QAAQP,OAAOG,MAAM,CAACI,MAAM;YAC5BC,qBAAqBR,OAAOG,MAAM,CAACK,mBAAmB;YACtDC,WAAW,EAAET,2BAAAA,iBAAAA,OAAQG,MAAM,qBAAdH,eAAgBS,WAAW;YACxC,GAAIR,MACA;gBACE,6DAA6D;gBAC7DS,SAASV,OAAOG,MAAM,CAACO,OAAO;gBAC9BC,cAAc,GAAEX,kBAAAA,OAAOG,MAAM,qBAAbH,gBAAeW,cAAc;gBAC7CC,aAAa,GAAEZ,kBAAAA,OAAOG,MAAM,qBAAbH,gBAAeY,aAAa;gBAC3CC,QAAQb,OAAOa,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,mBAAmB,EACnBhB,MAAM,EACNC,GAAG,EACHgB,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACG;QAmENzB,iCAETA,kCAGSA,kCAETA,kCAsD6BA,cAqCjBA;IApKpB,MAAM0B,gBAAgBpC;IACtB,MAAMqC,gBAAgBtC,iBAAiBW;IAEvC,MAAM4B,eAAexC,qBAAqBY,OAAO6B,YAAY,CAACC,GAAG;IACjE,MAAMC,qBAAqB,CAAC,CAAC/B,OAAO6B,YAAY,CAACG,SAAS;IAC1D,MAAMC,oBAAoB,CAAC,CAACjC,OAAO6B,YAAY,CAACK,QAAQ;IAExD,MAAMzC,YAAuB;QAC3B,+CAA+C;QAC/C0C,mBAAmB;QAEnB,GAAGT,aAAa;QAChB,GAAGC,aAAa;QAChB,GAAI,CAACN,eACD,CAAC,IACD;YACEe,aACE;;;;aAIC,GACDC,QAAQC,GAAG,CAACC,0BAA0B,IAAI;YAE5C,0DAA0D;YAC1D,sEAAsE;YACtE,gBAAgB;QAClB,CAAC;QACL,qBAAqBxB;QACrB,yBAAyBA;QACzB,8BAA8BA,cAC1B,cACAsB,QAAQC,GAAG,CAACE,WAAW,GACrB,WACA;QACN,6DAA6D;QAC7D,wBACEvC,OAAOD,OAAO6B,YAAY,CAACY,qBAAqB,GAC5C,gBACA;QACN,4BAA4BpB,eACxB,SACAE,eACE,WACA;QACN,4BAA4B;QAC5B,4CAA4CmB,QAC1C1C,OAAO6B,YAAY,CAACc,kBAAkB;QAExC,0BAA0Bf;QAC1B,iCAAiCG;QACjC,gCAAgCE;QAChC,kCAAkCjC,OAAO4C,YAAY,IAAI;QACzD,0EAA0E;QAC1E,0BAA0B;QAC1B,0DACEP,QAAQC,GAAG,CAACO,0CAA0C,IAAI;QAC5D,6CAA6C3B,uBAAuB;QACpE,GAAIH,cACA,CAAC,IACD;YACE,0CAA0CS,sBAAsB,EAAE;QACpE,CAAC;QACL,8CACExB,OAAO6B,YAAY,CAACiB,oBAAoB,IAAI;QAC9C,sDAAsDjD,KAAKC,SAAS,CAClEiD,MAAMC,QAAOhD,kCAAAA,OAAO6B,YAAY,CAACoB,UAAU,qBAA9BjD,gCAAgCkD,OAAO,KAChD,KACAlD,mCAAAA,OAAO6B,YAAY,CAACoB,UAAU,qBAA9BjD,iCAAgCkD,OAAO;QAE7C,qDAAqDrD,KAAKC,SAAS,CACjEiD,MAAMC,QAAOhD,mCAAAA,OAAO6B,YAAY,CAACoB,UAAU,qBAA9BjD,iCAAgCmD,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBnD,mCAAAA,OAAO6B,YAAY,CAACoB,UAAU,qBAA9BjD,iCAAgCmD,MAAM;QAE5C,mDACEnD,OAAO6B,YAAY,CAACuB,kBAAkB,IAAI;QAC5C,6CACEpC,CAAAA,uCAAAA,oBAAqBqC,YAAY,KAAI;QACvC,6CACErC,CAAAA,uCAAAA,oBAAqBsC,aAAa,KAAI;QACxC,2CAA2CZ,QACzC1C,OAAO6B,YAAY,CAAC0B,kBAAkB;QAExC,uCAAuCb,QACrC1C,OAAO6B,YAAY,CAAC2B,cAAc;QAEpC,sCAAsCd,QACpC1C,OAAO6B,YAAY,CAAC4B,aAAa;QAEnC,8CACEzD,OAAO6B,YAAY,CAAC6B,qBAAqB,IAAI;QAC/C,0CACE1D,OAAO6B,YAAY,CAAC8B,kBAAkB,IAAI;QAC5C,mCAAmC3D,OAAO4D,WAAW;QACrD,mBAAmBxC;QACnB,gCAAgCiB,QAAQC,GAAG,CAACuB,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAI5D,OAAQmB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqCjB,OAAO8D,aAAa;QACzD,oCAAoC9D,OAAO+D,aAAa,KAAK;QAC7D,6CACE/D,OAAO+D,aAAa,KAAK,QACrB,cAAc,sDAAsD;WACpE/D,OAAO+D,aAAa,CAACC,QAAQ,IAAI;QACvC,kCACEhE,OAAOiE,eAAe,KAAK,OAAO,QAAQjE,OAAOiE,eAAe;QAClE,sCACE,6EAA6E;QAC7EjE,OAAOiE,eAAe,KAAK,OAAO,OAAOjE,OAAOiE,eAAe;QACjE,mCACE,AAACjE,CAAAA,OAAO6B,YAAY,CAACqC,WAAW,IAAI,CAACjE,GAAE,KAAM;QAC/C,qCACE,AAACD,CAAAA,OAAO6B,YAAY,CAACsC,iBAAiB,IAAI,CAAClE,GAAE,KAAM;QACrD,yCACED,OAAO6B,YAAY,CAACuC,iBAAiB,IAAI;QAC3C,GAAGrE,eAAeC,QAAQC,IAAI;QAC9B,sCAAsCD,OAAOqE,QAAQ;QACrD,uCACErE,OAAO6B,YAAY,CAACyC,cAAc,IAAI;QACxC,mCAAmCnD;QACnC,oCAAoCnB,OAAOa,MAAM;QACjD,mCAAmC,CAAC,CAACb,OAAOuE,IAAI;QAChD,mCAAmCvE,EAAAA,eAAAA,OAAOuE,IAAI,qBAAXvE,aAAaU,OAAO,KAAI;QAC3D,kDACEV,OAAOwE,0BAA0B;QACnC,0DACExE,OAAO6B,YAAY,CAAC4C,iCAAiC,IAAI;QAC3D,4CACEzE,OAAO0E,yBAAyB;QAClC,iDACE,AAAC1E,CAAAA,OAAO6B,YAAY,CAAC8C,oBAAoB,IACvC3E,OAAO6B,YAAY,CAAC8C,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACE5E,OAAO6B,YAAY,CAAC8C,oBAAoB,IAAI;QAC9C,0CACE3E,OAAO6B,YAAY,CAACgD,gBAAgB,IAAI;QAC1C,mCAAmC7E,OAAO8E,WAAW;QACrD,mDACE,CAAC,CAAC9E,OAAO6B,YAAY,CAACkD,cAAc;QACtC,yCAAyCrC,QACvCL,QAAQC,GAAG,CAAC0C,uBAAuB;QAErC,GAAI1D,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACA2D,SAAS;QACb,GAAI3D,0BACA;YACE,yCACEnC,uBAAuBa;QAC3B,IACAiF,SAAS;IACf;IAEA,MAAMC,cAAclF,EAAAA,mBAAAA,OAAOmF,QAAQ,qBAAfnF,iBAAiBoF,MAAM,KAAI,CAAC;IAChD,IAAK,MAAMzF,OAAOuF,YAAa;QAC7B,IAAIzF,UAAU4F,cAAc,CAAC1F,MAAM;YACjC,MAAM,qBAEL,CAFK,IAAI2F,MACR,CAAC,8DAA8D,EAAE3F,IAAI,gHAAgH,CAAC,GADlL,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACAF,SAAS,CAACE,IAAI,GAAGuF,WAAW,CAACvF,IAAI;IACnC;IAEA,MAAM4F,sBAAsB/F,mBAAmBC;IAE/C,uDAAuD;IACvD,oDAAoD;IACpD,+BAA+B;IAC/B,IAAI,CAACQ,OAAOwB,sBAAsB;QAChC,qDAAqD;QACrD,qDAAqD;QACrD,mDAAmD;QACnD,MAAM+D,UAAU,CAAC7F,MACfyB,WAAW,CAAC,OAAO,EAAEzB,IAAI8F,KAAK,CAAC,KAAKC,GAAG,IAAI,GAAG/F;QAEhD,IAAK,MAAMA,OAAO+B,cAAe;YAC/B6D,mBAAmB,CAAC5F,IAAI,GAAG6F,QAAQ7F;QACrC;QACA,IAAK,MAAMA,OAAOgC,cAAe;YAC/B4D,mBAAmB,CAAC5F,IAAI,GAAG6F,QAAQ7F;QACrC;QACA,KAAK,MAAMA,OAAO;YAAC;SAAiC,CAAE;YACpD4F,mBAAmB,CAAC5F,IAAI,GAAG6F,QAAQ7F;QACrC;IACF;IAEA,OAAO4F;AACT;AAEA,OAAO,SAASI,mBAAmBC,OAA+B;IAChE,OAAO,IAAKrG,CAAAA,mBAAkB,EAAEsG,YAAY,CAAE/E,aAAa8E;AAC7D"}