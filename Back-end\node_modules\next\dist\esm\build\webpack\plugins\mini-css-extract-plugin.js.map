{"version": 3, "sources": ["../../../../src/build/webpack/plugins/mini-css-extract-plugin.ts"], "sourcesContent": ["// @ts-ignore: TODO: remove when webpack 5 is stable\nimport MiniCssExtractPlugin from 'next/dist/compiled/mini-css-extract-plugin'\n\nexport default class NextMiniCssExtractPlugin extends MiniCssExtractPlugin {\n  __next_css_remove = true\n}\n"], "names": ["MiniCssExtractPlugin", "NextMiniCssExtractPlugin", "__next_css_remove"], "mappings": "AAAA,oDAAoD;AACpD,OAAOA,0BAA0B,6CAA4C;AAE7E,eAAe,MAAMC,iCAAiCD;;QAAvC,qBACbE,oBAAoB;;AACtB"}