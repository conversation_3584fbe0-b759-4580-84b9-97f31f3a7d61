import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper, Button, CircularProgress } from '@mui/material';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

const UserDashboard = () => {
  const { user, token } = useAuth();
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [summary, setSummary] = useState({
    totalDue: 0,
    totalPaid: 0,
  });

  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        const response = await axios.get('/api/factures', {
          headers: { Authorization: `Bearer ${token}` },
        });
        setInvoices(response.data);
        calculateSummary(response.data);
      } catch (error) {
        console.error('Error fetching invoices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, [token]);

  const calculateSummary = (invoices) => {
    let totalDue = 0;
    let totalPaid = 0;
    invoices.forEach((invoice) => {
      if (invoice.status === 'paid') {
        totalPaid += invoice.amount;
      } else {
        totalDue += invoice.amount;
      }
    });
    setSummary({ totalDue, totalPaid });
  };

  const handlePayInvoice = (invoiceId) => {
    // Implement payment logic here
    alert(`Paiement de la facture ${invoiceId} non implémenté.`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Tableau de bord utilisateur
      </Typography>

      <Paper sx={{ p: 2, mb: 4 }}>
        <Typography variant="h6">Résumé de l'activité</Typography>
        <Typography>Total dû: {summary.totalDue} €</Typography>
        <Typography>Total payé: {summary.totalPaid} €</Typography>
      </Paper>

      <Typography variant="h6" gutterBottom>
        Vos factures
      </Typography>
      {invoices.length === 0 ? (
        <Typography>Aucune facture trouvée.</Typography>
      ) : (
        invoices.map((invoice) => (
          <Paper key={invoice._id} sx={{ p: 2, mb: 2 }}>
            <Typography>Numéro: {invoice.numero}</Typography>
            <Typography>Date: {new Date(invoice.date).toLocaleDateString()}</Typography>
            <Typography>Montant: {invoice.amount} €</Typography>
            <Typography>Statut: {invoice.status}</Typography>
            {invoice.status !== 'paid' && (
              <Button variant="contained" onClick={() => handlePayInvoice(invoice._id)} sx={{ mt: 1 }}>
                Payer
              </Button>
            )}
          </Paper>
        ))
      )}
    </Box>
  );
};

export default UserDashboard;
