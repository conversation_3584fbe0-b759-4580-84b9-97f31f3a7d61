{"version": 3, "sources": ["../../src/server/get-page-files.ts"], "sourcesContent": ["import { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\n\nexport type BuildManifest = {\n  devFiles: readonly string[]\n  ampDevFiles: readonly string[]\n  polyfillFiles: readonly string[]\n  lowPriorityFiles: readonly string[]\n  rootMainFiles: readonly string[]\n  // this is a separate field for flying shuttle to allow\n  // different root main files per entries/build (ideally temporary)\n  // until we can stitch the runtime chunks together safely\n  rootMainFilesTree: { [appRoute: string]: readonly string[] }\n  pages: {\n    '/_app': readonly string[]\n    [page: string]: readonly string[]\n  }\n  ampFirstPages: readonly string[]\n}\n\nexport function getPageFiles(\n  buildManifest: BuildManifest,\n  page: string\n): readonly string[] {\n  const normalizedPage = denormalizePagePath(normalizePagePath(page))\n  let files = buildManifest.pages[normalizedPage]\n\n  if (!files) {\n    console.warn(\n      `Could not find files for ${normalizedPage} in .next/build-manifest.json`\n    )\n    return []\n  }\n\n  return files\n}\n"], "names": ["denormalizePagePath", "normalizePagePath", "getPageFiles", "buildManifest", "page", "normalizedPage", "files", "pages", "console", "warn"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,iBAAiB,QAAQ,8CAA6C;AAmB/E,OAAO,SAASC,aACdC,aAA4B,EAC5BC,IAAY;IAEZ,MAAMC,iBAAiBL,oBAAoBC,kBAAkBG;IAC7D,IAAIE,QAAQH,cAAcI,KAAK,CAACF,eAAe;IAE/C,IAAI,CAACC,OAAO;QACVE,QAAQC,IAAI,CACV,CAAC,yBAAyB,EAAEJ,eAAe,6BAA6B,CAAC;QAE3E,OAAO,EAAE;IACX;IAEA,OAAOC;AACT"}