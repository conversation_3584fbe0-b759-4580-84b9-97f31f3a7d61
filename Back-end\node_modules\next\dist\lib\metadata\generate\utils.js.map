{"version": 3, "sources": ["../../../../src/lib/metadata/generate/utils.ts"], "sourcesContent": ["function resolveArray<T>(value: T | T[]): T[] {\n  if (Array.isArray(value)) {\n    return value as any\n  }\n  return [value] as any\n}\n\nfunction resolveAsArrayOrUndefined<T>(\n  value: T | T[] | undefined | null\n): T extends undefined | null ? undefined : T[] {\n  if (typeof value === 'undefined' || value === null) {\n    return undefined as any\n  }\n  return resolveArray(value) as any\n}\n\nfunction getOrigin(url: string | URL): string | undefined {\n  let origin = undefined\n  if (typeof url === 'string') {\n    try {\n      url = new URL(url)\n      origin = url.origin\n    } catch {}\n  }\n  return origin\n}\n\nexport { resolveAsArrayOrUndefined, resolveArray, getOrigin }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "resolveArray", "resolveAsArrayOrUndefined", "value", "Array", "isArray", "undefined", "url", "origin", "URL"], "mappings": ";;;;;;;;;;;;;;;;IA2BkDA,SAAS;eAATA;;IAAdC,YAAY;eAAZA;;IAA3BC,yBAAyB;eAAzBA;;;AA3BT,SAASD,aAAgBE,KAAc;IACrC,IAAIC,MAAMC,OAAO,CAACF,QAAQ;QACxB,OAAOA;IACT;IACA,OAAO;QAACA;KAAM;AAChB;AAEA,SAASD,0BACPC,KAAiC;IAEjC,IAAI,OAAOA,UAAU,eAAeA,UAAU,MAAM;QAClD,OAAOG;IACT;IACA,OAAOL,aAAaE;AACtB;AAEA,SAASH,UAAUO,GAAiB;IAClC,IAAIC,SAASF;IACb,IAAI,OAAOC,QAAQ,UAAU;QAC3B,IAAI;YACFA,MAAM,IAAIE,IAAIF;YACdC,SAASD,IAAIC,MAAM;QACrB,EAAE,OAAM,CAAC;IACX;IACA,OAAOA;AACT"}