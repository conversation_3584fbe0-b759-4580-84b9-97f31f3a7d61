import api from './api';

const abonnementService = {
  // R<PERSON>cupérer tous les abonnements (admin uniquement)
  getAbonnements: async () => {
    try {
      const response = await api.get('/abonnements');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des abonnements');
    }
  },

  // Récupérer un abonnement par ID
  getAbonnementById: async (id) => {
    try {
      const response = await api.get(`/abonnements/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la récupération de l'abonnement ${id}`);
    }
  },

  // Créer un nouvel abonnement
  createAbonnement: async (abonnementData) => {
    try {
      const response = await api.post('/abonnements', abonnementData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'abonnement');
    }
  },

  // Mettre à jour un abonnement
  updateAbonnement: async (id, abonnementData) => {
    try {
      const response = await api.put(`/abonnements/${id}`, abonnementData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la mise à jour de l'abonnement ${id}`);
    }
  },

  // Supprimer un abonnement
  deleteAbonnement: async (id) => {
    try {
      const response = await api.delete(`/abonnements/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la suppression de l'abonnement ${id}`);
    }
  },

  // Vérifier si l'utilisateur a un abonnement actif
  checkAbonnementActif: async () => {
    try {
      const response = await api.get('/abonnements/check');
      return response.data;
    } catch (error) {
      if (error.response?.status === 403) {
        return {
          actif: false,
          message: error.response.data.message,
          subscriptionRequired: error.response.data.subscriptionRequired,
          subscriptionExpired: error.response.data.subscriptionExpired
        };
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la vérification de l\'abonnement');
    }
  },

  // Récupérer les détails de l'abonnement du responsable connecté
  getResponsableAbonnement: async () => {
    try {
      const response = await api.get('/abonnements/user/responsable');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des détails de l\'abonnement');
    }
  },

  // Vérifier si l'abonnement est sur le point d'expirer
  checkExpiration: async () => {
    try {
      const response = await api.get('/abonnements/user/check-expiration');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la vérification de l\'expiration de l\'abonnement');
    }
  },

  // Demander un renouvellement d'abonnement
  demanderRenouvellement: async (data) => {
    try {
      const response = await api.post('/abonnements/user/renouvellement', data);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la demande de renouvellement');
    }
  },

  // Récupérer toutes les demandes de renouvellement (admin uniquement)
  getDemandesRenouvellement: async () => {
    try {
      const response = await api.get('/abonnements/renouvellements');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des demandes de renouvellement');
    }
  },

  // Récupérer le nombre de demandes de renouvellement non vues (admin uniquement)
  getDemandesRenouvellementCount: async () => {
    try {
      const response = await api.get('/abonnements/renouvellements/count');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors du comptage des demandes de renouvellement');
    }
  },

  // Marquer une demande de renouvellement comme vue (admin uniquement)
  marquerDemandeCommeVue: async (id) => {
    try {
      const response = await api.put(`/abonnements/renouvellements/${id}/vu`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors du marquage de la demande comme vue');
    }
  },

  // Traiter une demande de renouvellement (admin uniquement)
  traiterDemandeRenouvellement: async (id, data) => {
    try {
      const response = await api.put(`/abonnements/renouvellements/${id}/traiter`, data);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors du traitement de la demande de renouvellement');
    }
  }
};

export default abonnementService;
