import React, { useEffect, useRef } from 'react';
import { Box, Paper, Typography, Divider, Grid, Button, useTheme, CircularProgress } from '@mui/material';
import { Print as PrintIcon, Download as DownloadIcon, Email as EmailIcon } from '@mui/icons-material';
import { formatCurrency as formatCurrencyUtil, getDefaultCurrency } from '../utils/formatters';
import useTemplate from '../hooks/useTemplate';
import factureService from '../services/factureService';
import devisService from '../services/devisService';

const DocumentPreview = ({ document: documentData, type, companyData, onEmail }) => {
  const theme = useTheme();
  const { template, loading: templateLoading, error: templateError, getTemplateStyles } = useTemplate(type === 'facture' ? 'Facture' : 'Devis');
  const documentRef = useRef(null);

  // Utiliser la fonction formatCurrency du fichier formatters.js
  const formatCurrency = (amount) => {
    return formatCurrencyUtil(amount);
  };

  // Formatage des dates
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR').format(date);
  };

  // Appliquer les styles du template au document
  useEffect(() => {
    if (documentRef.current && !templateLoading) {
      // Injecter les styles CSS du template
      const styleElement = window.document.createElement('style');
      styleElement.textContent = getTemplateStyles();
      documentRef.current.appendChild(styleElement);

      return () => {
        // Nettoyer les styles lors du démontage
        if (styleElement && documentRef.current) {
          documentRef.current.removeChild(styleElement);
        }
      };
    }
  }, [template, templateLoading, getTemplateStyles]);

  if (!documentData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Handlers for buttons
  const handlePrint = async () => {
    if (!documentData || !documentData._id) {
      alert("Impossible d'imprimer : document non disponible");
      return;
    }

    try {
      if (type === 'facture') {
        await factureService.printFacture(documentData._id);
      } else {
        await devisService.printDevis(documentData._id);
      }
    } catch (error) {
      console.error("Erreur lors de l'impression:", error);
      // L'alerte est déjà gérée dans les services
    }
  };

  const handleDownload = async () => {
    if (!documentData || !documentData._id) {
      alert("Impossible de télécharger : document non disponible");
      return;
    }

    try {
      if (type === 'facture') {
        await factureService.generatePdf(documentData._id);
      } else {
        await devisService.generatePdf(documentData._id);
      }
    } catch (error) {
      console.error("Erreur lors du téléchargement:", error);
      // L'alerte est déjà gérée dans les services
    }
  };

  const handleEmail = async () => {
    if (!documentData || !documentData._id) {
      alert("Impossible d'envoyer par email : document non disponible");
      return;
    }

    try {
      if (onEmail) {
        onEmail(documentData);
      } else {
        // Default email handling if no custom handler provided
        if (type === 'facture') {
          await factureService.sendEmail(documentData._id);
        } else {
          await devisService.sendEmail(documentData._id);
        }
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi par email:", error);
      // L'alerte est déjà gérée dans les services
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
        <Button
          startIcon={<PrintIcon />}
          variant="outlined"
          size="small"
          onClick={handlePrint}
        >
          Imprimer
        </Button>
        <Button
          startIcon={<DownloadIcon />}
          variant="outlined"
          size="small"
          onClick={handleDownload}
        >
          Télécharger
        </Button>
        <Button
          startIcon={<EmailIcon />}
          variant="contained"
          size="small"
          onClick={handleEmail}
          disabled={type === 'facture' ? documentData.statut === 'DRAFT' : documentData.statut === 'BROUILLON'}
        >
          Envoyer par email
        </Button>
      </Box>

      <Paper
        ref={documentRef}
        elevation={3}
        sx={{
          p: 4,
          maxWidth: '210mm',
          mx: 'auto',
          backgroundColor: '#fff',
          fontFamily: template.font || 'Inter',
        }}
        className="template-document"
      >
        {/* En-tête du document */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
          <Box>
            {(template.logo || companyData?.logo) && (
              <img
                src={template.logo || companyData?.logo || "/placeholder.svg"}
                alt="Logo"
                style={{ maxHeight: '80px', maxWidth: '200px', marginBottom: '10px' }}
              />
            )}
            <Typography variant="h6" sx={{ color: template.color || theme.palette.primary.main }}>
              {companyData?.nomEntreprise || 'Nom de l\'entreprise'}
            </Typography>
            <Typography variant="body2">
              {companyData?.adresseEntreprise || 'Adresse de l\'entreprise'}<br />
              {companyData?.codePostal || ''} {companyData?.ville || ''}<br />
              {companyData?.email || '<EMAIL>'} | {companyData?.telephone || '01 23 45 67 89'}
            </Typography>
          </Box>

          <Box sx={{
            p: 2,
            borderRadius: 1,
            textAlign: 'center',
            minWidth: '150px',
            color: '#fff',
            bgcolor: template.color || theme.palette.primary.main
          }}
          className="template-header">
            <Typography variant="h6">
              {type === 'facture' ? 'FACTURE' : 'DEVIS'}
            </Typography>
            <Typography variant="body2">
              N° {type === 'devis' ? documentData.numéro : documentData.numero || (type === 'facture' ? 'FACT-0001' : 'DEV-0001')}
            </Typography>
            <Typography variant="body2">
              Date: {formatDate(type === 'devis' ? documentData.dateCréation : documentData.dateEmission) || formatDate(new Date())}
            </Typography>
          </Box>
        </Box>

        {/* Informations client */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle1" sx={{
            color: template.color || theme.palette.primary.main,
            fontWeight: 'bold',
            mb: 1
          }}>
            CLIENT
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            {documentData.clientId?.nom || 'Nom du client'}
          </Typography>
          <Typography variant="body2">
            {documentData.clientId?.adresse || 'Adresse du client'}<br />
            {documentData.clientId?.email || ''} {documentData.clientId?.telephone ? `| ${documentData.clientId.telephone}` : ''}
          </Typography>
        </Box>

        {/* Tableau des produits/services */}
        <Box sx={{ mb: 4, backgroundColor: template.secondaryColor || '#f5f5f5', p: 2, borderRadius: 1 }}>
          <Grid container sx={{
            fontWeight: 'bold',
            p: 1,
            borderBottom: '1px solid #ddd',
            bgcolor: template.color || theme.palette.primary.main,
            color: '#fff',
            borderRadius: '4px 4px 0 0',
            mb: 1
          }}
          className="template-table-header">
            <Grid item xs={6}>Description</Grid>
            <Grid item xs={2} sx={{ textAlign: 'center' }}>Quantité</Grid>
            <Grid item xs={2} sx={{ textAlign: 'right' }}>Prix unitaire</Grid>
            <Grid item xs={2} sx={{ textAlign: 'right' }}>Total</Grid>
          </Grid>

          <Box className="template-table-body">
            {(documentData.lignes || []).map((ligne, index) => (
              <Grid
                container
                key={index}
                sx={{
                  p: 1,
                  borderBottom: index < (documentData.lignes?.length || 0) - 1 ? '1px solid #ddd' : 'none',
                  backgroundColor: template.tableStyle === 'striped' && index % 2 === 1 ? 'rgba(0,0,0,0.03)' : 'transparent'
                }}
                className="template-table-row"
              >
                <Grid item xs={6}>{ligne.description}</Grid>
                <Grid item xs={2} sx={{ textAlign: 'center' }}>{ligne.quantite}</Grid>
                <Grid item xs={2} sx={{ textAlign: 'right' }}>{formatCurrency(ligne.prixUnitaire)}</Grid>
                <Grid item xs={2} sx={{ textAlign: 'right' }}>{formatCurrency(ligne.montantHT)}</Grid>
              </Grid>
            ))}
          </Box>
        </Box>

        {/* Totaux */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 4 }}>
          <Box sx={{ width: '250px' }}>
            {/* Calculate totalHT from lignes if not provided */}
            {(() => {
              // Calculate Total HT from line items - ensure we're working with numbers
              const totalHT = documentData.totalHT || documentData.lignes?.reduce(
                (sum, ligne) => {
                  const quantite = parseFloat(ligne.quantite) || 0;
                  const prixUnitaire = parseFloat(ligne.prixUnitaire) || 0;
                  return sum + (quantite * prixUnitaire);
                },
                0
              ) || 0;

              // Calculate TVA amount - ensure we're working with numbers
              const tauxTVA = parseFloat(documentData.tauxTVA) || 0;
              const montantTVA = documentData.montantTVA || (totalHT * tauxTVA / 100);

              // Calculate Total TTC - ensure we're working with numbers
              let totalTTC;

              // If total is provided in documentData, try to use it
              if (documentData.total !== undefined && documentData.total !== null) {
                // Handle string values that might contain formatting issues
                if (typeof documentData.total === 'string') {
                  // Remove any non-numeric characters except decimal separators
                  const cleanedTotal = documentData.total.replace(/[^\d.,]/g, '');

                  // Handle slash format if present
                  if (documentData.total.includes('/')) {
                    const parts = documentData.total.split('/');
                    if (parts.length === 2) {
                      totalTTC = parseFloat(parts[1].replace(',', '.'));
                    } else {
                      totalTTC = parseFloat(cleanedTotal.replace(',', '.'));
                    }
                  } else {
                    totalTTC = parseFloat(cleanedTotal.replace(',', '.'));
                  }

                  // If parsing failed, calculate from totalHT and montantTVA
                  if (isNaN(totalTTC)) {
                    totalTTC = totalHT + montantTVA;
                  }
                } else {
                  // If it's already a number, use it directly
                  totalTTC = parseFloat(documentData.total);
                }
              } else {
                // If no total provided, calculate it
                totalTTC = totalHT + montantTVA;
              }

              // Ensure all values are properly rounded numbers
              const roundedTotalHT = Number(totalHT.toFixed(2));
              const roundedMontantTVA = Number(montantTVA.toFixed(2));
              const roundedTotalTTC = Number(totalTTC.toFixed(2));

              return (
                <>
                  <Grid container sx={{ mb: 1 }}>
                    <Grid item xs={6}>Total HT:</Grid>
                    <Grid item xs={6} sx={{ textAlign: 'right', fontSize: '0.9rem', fontWeight: 'normal' }}>{formatCurrency(roundedTotalHT)}</Grid>
                  </Grid>
                  <Grid container sx={{ mb: 1 }}>
                    <Grid item xs={6}>TVA ({documentData.tauxTVA || 0}%):</Grid>
                    <Grid item xs={6} sx={{ textAlign: 'right', fontSize: '0.9rem', fontWeight: 'normal' }}>{formatCurrency(roundedMontantTVA)}</Grid>
                  </Grid>
                  <Grid container sx={{
                    bgcolor: template.color || theme.palette.primary.main,
                    color: '#fff',
                    p: 1,
                    borderRadius: 1
                  }}
                  className="template-total">
                    <Grid item xs={6} sx={{ fontWeight: 'bold' }}>Total TTC:</Grid>
                    <Grid item xs={6} sx={{ textAlign: 'right', fontSize: '0.9rem', fontWeight: 'normal' }}>{formatCurrency(roundedTotalTTC)}</Grid>
                  </Grid>
                </>
              );
            })()}
          </Box>
        </Box>

        {/* Informations de paiement */}
        {type === 'facture' && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" sx={{
              color: template.color || theme.palette.primary.main,
              fontWeight: 'bold',
              mb: 1
            }}>
              INFORMATIONS DE PAIEMENT
            </Typography>
            <Typography variant="body2">
              Date d'échéance: {formatDate(documentData.dateEcheance) || 'À réception'}<br />
              Mode de paiement: {documentData.modePaiement || 'Virement bancaire'}
            </Typography>
          </Box>
        )}

        {/* Conditions et notes */}
        <Box>
          <Typography variant="body2" sx={{ fontStyle: 'italic', mb: 2 }}>
            {documentData.notes || 'Merci pour votre confiance.'}
          </Typography>

          <Divider sx={{ mb: 2 }} />

          <Typography variant="caption" sx={{ display: 'block', textAlign: 'center' }}>
            {companyData?.mentionsLegales || 'Mentions légales de l\'entreprise'}
          </Typography>
        </Box>
        </Paper>
    </Box>
  );
};

export default DocumentPreview;