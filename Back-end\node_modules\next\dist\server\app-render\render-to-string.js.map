{"version": 3, "sources": ["../../../src/server/app-render/render-to-string.tsx"], "sourcesContent": ["import { streamToString } from '../stream-utils/node-web-streams-helper'\n\nexport async function renderToString({\n  renderToReadableStream,\n  element,\n}: {\n  // `renderToReadableStream()` method could come from different react-dom/server implementations\n  // such as `react-dom/server.edge` or `react-dom/server.node`, etc.\n  renderToReadableStream: typeof import('react-dom/server.edge').renderToReadableStream\n  element: React.ReactElement\n}): Promise<string> {\n  const renderStream = await renderToReadableStream(element)\n  await renderStream.allReady\n  return streamToString(renderStream)\n}\n"], "names": ["renderToString", "renderToReadableStream", "element", "renderStream", "allReady", "streamToString"], "mappings": ";;;;+BAEsBA;;;eAAAA;;;sCAFS;AAExB,eAAeA,eAAe,EACnCC,sBAAsB,EACtBC,OAAO,EAMR;IACC,MAAMC,eAAe,MAAMF,uBAAuBC;IAClD,MAAMC,aAAaC,QAAQ;IAC3B,OAAOC,IAAAA,oCAAc,EAACF;AACxB"}