{"name": "back-end", "version": "1.0.0", "main": "server", "scripts": {"start": "nodemon server.js", "start:high-memory": "node start-server.js", "start:max-memory": "node --max-old-space-size=8192 server.js", "test": "echo \"Error: no test specified\" && exit 1", "init-admin": "node scripts/initAdmin.js", "setup": "npm install && npm run init-admin && npm start", "fix-invoices": "node --max-old-space-size=8192 scripts/fixInvoiceClientIds.js", "create-test-invoices": "node scripts/createInvoicesForClient.js", "cleanup-test-data": "node scripts/cleanupTestData.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "moment": "^2.30.1", "mongoose": "^8.13.3", "multer": "^1.4.5-lts.2", "next": "^15.2.3", "node-cron": "^4.0.6", "nodemailer": "^6.10.1", "pdfkit": "^0.17.1", "sharp": "^0.34.1", "socket.io": "^4.8.1"}, "devDependencies": {"@types/next": "^8.0.7", "nodemon": "^3.1.0"}}