import api from "./api";
import { getTemplateSettings } from "./templateService";
import { formatCurrency, getDefaultCurrency, normalizeStatus } from "../utils/formatters";

const factureService = {
  getFactures: async () => {
    try {
      console.log("Calling GET /factures API...");
      const response = await api.get("/factures");
      console.log("Response from getFactures:", response.data);

      // Compter les factures par statut pour le débogage
      const statusCounts = {};
      response.data.forEach(facture => {
        statusCounts[facture.statut] = (statusCounts[facture.statut] || 0) + 1;
      });
      console.log("Factures par statut:", statusCounts);

      // Vérifier spécifiquement les factures avec le statut "SENT"
      const sentInvoices = response.data.filter(f => f.statut === 'SENT');
      console.log(`Nombre de factures avec statut SENT: ${sentInvoices.length}`);
      if (sentInvoices.length > 0) {
        console.log("Exemple de facture SENT:", sentInvoices[0]);
      }

      return response.data;
    } catch (error) {
      console.error("Erreur lors de la récupération des factures:", error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      return [];
    }
  },

  getFactureById: async (id) => {
    try {
      const response = await api.get(`/factures/${id}`);
      console.log("Response from getFactureById:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  createFacture: async (factureData) => {
    try {
      const response = await api.post("/factures", factureData);
      console.log("Response from createFacture:", response.data);
      return response.data.facture; // Return the facture object directly
    } catch (error) {
      console.error("Erreur lors de la création de la facture:", error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  updateFacture: async (id, factureData) => {
    try {
      const response = await api.put(`/factures/${id}`, {
        ...factureData,
        clientId: factureData.clientId?._id || factureData.clientId,
        lignes: factureData.lignes.map((ligne) => ({
          ...ligne,
          produit: ligne.produit?._id || ligne.produit,
        })),
      });
      console.log("Response from updateFacture:", response.data);
      return response.data; // Return the updated facture object
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  deleteFacture: async (id) => {
    try {
      const response = await api.delete(`/factures/${id}`);
      console.log("Response from deleteFacture:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression de la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  finalizeFacture: async (id) => {
    try {
      console.log(`Finalizing invoice ${id} - changing status to SENT`);

      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus("SENT");
      console.log(`Normalized status: ${normalizedStatus}`);

      const response = await api.put(`/factures/${id}`, { statut: normalizedStatus });
      console.log("Response from finalizeFacture:", response.data);

      // Vérifier que le statut a bien été mis à jour
      if (response.data.statut !== normalizedStatus) {
        console.warn(`Warning: Invoice status was not updated to ${normalizedStatus}. Current status: ${response.data.statut}`);
      } else {
        console.log(`Invoice status successfully updated to ${normalizedStatus}`);
      }

      return response.data; // Return the finalized facture object
    } catch (error) {
      console.error(`Erreur lors de la finalisation de la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  // Generate PDF for an invoice
  generatePdf: async (id) => {
    try {
      console.log(`Generating PDF for invoice ${id} using backend API`);

      // Use the backend API to generate the PDF
      const response = await api.get(`/factures/${id}/pdf`, {
        responseType: 'blob' // Important: set the response type to blob
      });

      // Create a blob URL from the PDF data
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      // Create a link element and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `facture-${id}.pdf`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      return { success: true, message: "PDF généré avec succès" };
    } catch (error) {
      console.error(`Erreur lors de la génération du PDF pour la facture ${id}:`, error);

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de la génération du PDF: ${error.message}`);

      throw new Error(`Erreur lors de la génération du PDF: ${error.message}`);
    }
  },

  // Send invoice by email
  sendEmail: async (id, emailData = {}) => {
    try {
      const response = await api.post(`/factures/${id}/email`, emailData);
      console.log("Response from sendEmail:", response.data);

      // Afficher une alerte de succès
      alert("Email envoyé avec succès !");

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'envoi de l'email pour la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de l'envoi de l'email: ${error.message}`);

      throw new Error(`Erreur lors de l'envoi de l'email: ${error.message}`);
    }
  },

  // Print invoice
  printFacture: async (id) => {
    try {
      console.log(`Printing invoice ${id} using backend API`);

      // Use the backend API to generate the PDF for printing
      const response = await api.get(`/factures/${id}/print`, {
        responseType: 'blob' // Important: set the response type to blob
      });

      // Create a blob URL from the PDF data
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      // Open the PDF in a new window for printing
      const printWindow = window.open(url, '_blank');

      // If the window was successfully opened, trigger print
      if (printWindow) {
        printWindow.addEventListener('load', () => {
          setTimeout(() => {
            printWindow.print();
          }, 1000); // Small delay to ensure the PDF is loaded
        });
      } else {
        // Si la fenêtre n'a pas pu être ouverte (bloqueur de popups)
        alert("Veuillez autoriser les popups pour imprimer le document.");
      }

      return { success: true, message: "Document envoyé à l'impression avec succès" };
    } catch (error) {
      console.error(`Erreur lors de l'impression de la facture ${id}:`, error);

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de l'impression: ${error.message}`);

      throw new Error(`Erreur lors de l'impression: ${error.message}`);
    }
  },

  // Register payment for an invoice
  registerPayment: async (id, paymentData = {}) => {
    try {
      console.log(`Registering payment for invoice ${id}`);

      // Utiliser l'API de paiement au lieu de simplement mettre à jour le statut
      // Cela garantit qu'un enregistrement de paiement est créé dans la base de données
      const defaultPaymentData = {
        montant: 0, // Sera remplacé par le montant total de la facture côté serveur
        modePaiement: 'BANK_TRANSFER',
        datePaiement: new Date(),
        reference: `REF-${Date.now()}`,
        notes: 'Paiement enregistré via l\'application'
      };

      const response = await api.post(`/paiement/payer/${id}`, {
        ...defaultPaymentData,
        ...paymentData
      });

      console.log("Response from registerPayment:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'enregistrement du paiement pour la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  // Accepter une facture (client)
  accepterFacture: async (id) => {
    try {
      console.log(`Acceptation de la facture ${id}`);

      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus("ACCEPTED");
      console.log(`Normalized status: ${normalizedStatus}`);

      const response = await api.put(`/factures/${id}/accepter`, { statut: normalizedStatus });
      console.log("Response from accepterFacture:", response.data);

      // Vérifier que le statut a bien été mis à jour
      if (response.data.statut !== normalizedStatus) {
        console.warn(`Warning: Invoice status was not updated to ${normalizedStatus}. Current status: ${response.data.statut}`);
      } else {
        console.log(`Invoice status successfully updated to ${normalizedStatus}`);
      }

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'acceptation de la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },

  // Refuser une facture (client)
  refuserFacture: async (id, motif = "") => {
    try {
      console.log(`Refus de la facture ${id}`);

      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus("REJECTED");
      console.log(`Normalized status: ${normalizedStatus}`);

      const response = await api.put(`/factures/${id}/refuser`, {
        statut: normalizedStatus,
        motifRefus: motif
      });
      console.log("Response from refuserFacture:", response.data);

      // Vérifier que le statut a bien été mis à jour
      if (response.data.statut !== normalizedStatus) {
        console.warn(`Warning: Invoice status was not updated to ${normalizedStatus}. Current status: ${response.data.statut}`);
      } else {
        console.log(`Invoice status successfully updated to ${normalizedStatus}`);
      }

      return response.data;
    } catch (error) {
      console.error(`Erreur lors du refus de la facture ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }
      throw error;
    }
  },
};

export default factureService;