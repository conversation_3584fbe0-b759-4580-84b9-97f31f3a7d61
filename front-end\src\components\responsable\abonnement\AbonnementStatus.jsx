import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Divider,
  Grid,
  useTheme
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  AccessTime as AccessTimeIcon,
  CalendarToday as CalendarTodayIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import abonnementService from '../../../services/abonnementService';
import AbonnementTimer from './AbonnementTimer';

const AbonnementStatus = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [abonnementData, setAbonnementData] = useState(null);

  useEffect(() => {
    const fetchAbonnementStatus = async () => {
      try {
        setLoading(true);
        const data = await abonnementService.getResponsableAbonnement();
        setAbonnementData(data);
        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération du statut d\'abonnement:', err);
        setError(err.message || 'Une erreur est survenue lors de la récupération des informations d\'abonnement');
      } finally {
        setLoading(false);
      }
    };

    fetchAbonnementStatus();
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIF':
        return theme.palette.success.main;
      case 'SUSPENDU':
        return theme.palette.warning.main;
      case 'EXPIRE':
      case 'ANNULE':
        return theme.palette.error.main;
      default:
        return theme.palette.info.main;
    }
  };

  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (isActif) => {
    if (isActif) {
      return <CheckCircleIcon sx={{ color: theme.palette.success.main, mr: 1 }} />;
    } else {
      return <WarningIcon sx={{ color: theme.palette.warning.main, mr: 1 }} />;
    }
  };

  // Fonction pour traduire la durée
  const translateDuree = (duree) => {
    switch (duree) {
      case '10_MIN':
        return '10 minutes';
      case '1_JOUR':
        return '1 jour';
      case '7_JOURS':
        return '7 jours';
      case '3_MOIS':
        return '3 mois';
      case '6_MOIS':
        return '6 mois';
      case '1_AN':
      case 'ANNEE':
        return '1 an';
      default:
        return duree;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!abonnementData || !abonnementData.hasAbonnement) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        Vous n'avez pas d'abonnement actif. Veuillez contacter l'administrateur pour en souscrire un.
      </Alert>
    );
  }

  const { abonnement, isActif, joursRestants } = abonnementData;

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Card
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 3,
          borderRadius: 2,
          boxShadow: theme.shadows[3],
          overflow: 'hidden'
        }}
      >
        <Box
          sx={{
            p: 2,
            backgroundColor: isActif ? theme.palette.success.light : theme.palette.warning.light,
            display: 'flex',
            alignItems: 'center'
          }}
        >
          {getStatusIcon(isActif)}
          <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: isActif ? theme.palette.success.dark : theme.palette.warning.dark }}>
            {isActif ? 'Abonnement actif' : 'Abonnement expiré ou suspendu'}
          </Typography>
        </Box>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  <BusinessIcon sx={{ fontSize: '1rem', mr: 0.5, verticalAlign: 'middle' }} />
                  Entreprise
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {abonnement.entrepriseId?.nom || 'Non spécifié'}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  <CalendarTodayIcon sx={{ fontSize: '1rem', mr: 0.5, verticalAlign: 'middle' }} />
                  Période d'abonnement
                </Typography>
                <Typography variant="body1">
                  Du {formatDate(abonnement.dateDebut)} au {formatDate(abonnement.dateFin)}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Durée
                </Typography>
                <Typography variant="body1">
                  {translateDuree(abonnement.duree)}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  <AccessTimeIcon sx={{ fontSize: '1rem', mr: 0.5, verticalAlign: 'middle' }} />
                  {abonnement.duree === '10_MIN' ? 'Temps restant' : 'Jours restants'}
                </Typography>
                {abonnement.duree === '10_MIN' ? (
                  // Pour les abonnements de 10 minutes, utiliser le composant de minuteur en temps réel
                  <AbonnementTimer
                    abonnementId={abonnement._id}
                    onExpire={() => {
                      // Rafraîchir les données d'abonnement lorsque le temps expire
                      window.location.reload();
                    }}
                  />
                ) : (
                  <Typography
                    variant="h4"
                    fontWeight={600}
                    color={joursRestants < 30 ? theme.palette.warning.main : theme.palette.success.main}
                  >
                    {joursRestants} jour{joursRestants !== 1 ? 's' : ''}
                  </Typography>
                )}
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Statut
                </Typography>
                <Chip
                  label={abonnement.statut}
                  sx={{
                    backgroundColor: getStatusColor(abonnement.statut),
                    color: '#fff',
                    fontWeight: 'bold'
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AbonnementStatus;
