{"version": 3, "sources": ["../../../src/server/lib/node-fs-methods.ts"], "sourcesContent": ["import type { CacheFs } from '../../shared/lib/utils'\n\nimport fs from 'fs'\n\nexport const nodeFs: CacheFs = {\n  existsSync: fs.existsSync,\n  readFile: fs.promises.readFile,\n  readFileSync: fs.readFileSync,\n  writeFile: (f, d) => fs.promises.writeFile(f, d),\n  mkdir: (dir) => fs.promises.mkdir(dir, { recursive: true }),\n  stat: (f) => fs.promises.stat(f),\n}\n"], "names": ["fs", "nodeFs", "existsSync", "readFile", "promises", "readFileSync", "writeFile", "f", "d", "mkdir", "dir", "recursive", "stat"], "mappings": "AAEA,OAAOA,QAAQ,KAAI;AAEnB,OAAO,MAAMC,SAAkB;IAC7BC,YAAYF,GAAGE,UAAU;IACzBC,UAAUH,GAAGI,QAAQ,CAACD,QAAQ;IAC9BE,cAAcL,GAAGK,YAAY;IAC7BC,WAAW,CAACC,GAAGC,IAAMR,GAAGI,QAAQ,CAACE,SAAS,CAACC,GAAGC;IAC9CC,OAAO,CAACC,MAAQV,GAAGI,QAAQ,CAACK,KAAK,CAACC,KAAK;YAAEC,WAAW;QAAK;IACzDC,MAAM,CAACL,IAAMP,GAAGI,QAAQ,CAACQ,IAAI,CAACL;AAChC,EAAC"}