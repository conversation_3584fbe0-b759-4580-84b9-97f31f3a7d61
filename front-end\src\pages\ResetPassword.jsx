import React, { useState, useEffect } from "react"
import { usePara<PERSON>, useNavigate, Link as RouterLink } from "react-router-dom"
import {
  Box,
  Button,
  Container,
  Link,
  Card,
  TextField,
  Typography,
  Alert,
  InputAdornment,
  CircularProgress,
  useTheme
} from "@mui/material"
import { Lock, ArrowBack } from "@mui/icons-material"
import { motion } from "framer-motion"
import authService from "../services/authService"
import logo from '../assets/benyounes_logo.png'

const ResetPassword = () => {
  const { token } = useParams()
  const navigate = useNavigate()
  const theme = useTheme()
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState({ type: "", text: "" })
  const [isVerifying, setIsVerifying] = useState(true)
  const [isTokenValid, setIsTokenValid] = useState(false)

  useEffect(() => {
    const verifyToken = async () => {
      try {
        await authService.verifyResetToken(token)
        setIsTokenValid(true)
      } catch (error) {
        setMessage({
          type: "error",
          text: "Le lien de réinitialisation est invalide ou a expiré. Veuillez demander un nouveau lien.",
        })
        setIsTokenValid(false)
      } finally {
        setIsVerifying(false)
      }
    }

    if (token) {
      verifyToken()
    } else {
      setIsVerifying(false)
      setIsTokenValid(false)
      setMessage({
        type: "error",
        text: "Aucun token de réinitialisation fourni.",
      })
    }
  }, [token])

  const handleChange = (e) => {
    const { name, value } = e.target
    if (name === "password") {
      setPassword(value)
    } else if (name === "confirmPassword") {
      setConfirmPassword(value)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validation
    if (!password) {
      setMessage({ type: "error", text: "Veuillez saisir un nouveau mot de passe." })
      return
    }

    if (password.length < 8) {
      setMessage({ type: "error", text: "Le mot de passe doit contenir au moins 8 caractères." })
      return
    }

    if (password !== confirmPassword) {
      setMessage({ type: "error", text: "Les mots de passe ne correspondent pas." })
      return
    }

    setIsSubmitting(true)
    setMessage({ type: "", text: "" })

    try {
      const response = await authService.resetPassword(token, password)
      setMessage({
        type: "success",
        text: "Votre mot de passe a été réinitialisé avec succès. Vous allez être redirigé vers votre tableau de bord.",
      })

      // Redirect after a short delay
      setTimeout(() => {
        // Redirect based on user role
        if (response.user && response.user.role) {
          if (response.user.role === 'ADMIN') {
            navigate('/admin/analytics');
          } else if (response.user.role === 'ENTREPRISE') {
            navigate('/entreprise/analytics');
          } else if (response.user.role === 'VENDEUR') {
            navigate('/vendeur/analytics');
          } else {
            navigate('/client');
          }
        } else {
          navigate('/');
        }
      }, 3000)
    } catch (error) {
      setMessage({
        type: "error",
        text: error.error || "Une erreur est survenue lors de la réinitialisation du mot de passe.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isVerifying) {
    return (
      <Box
        component={motion.div}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        sx={{
          display: 'flex',
          minHeight: '100vh',
          backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%)',
          backgroundSize: '400% 400%',
          alignItems: 'center',
          justifyContent: 'center',
          padding: { xs: 2, md: 4 },
        }}
      >
        <Box sx={{ textAlign: "center" }}>
          <CircularProgress size={60} thickness={4} sx={{ color: theme.palette.primary.main }} />
          <Typography variant="h5" sx={{ mt: 3, fontWeight: 500, color: theme.palette.text.primary }}>
            Vérification du lien de réinitialisation...
          </Typography>
        </Box>
      </Box>
    )
  }

  return (
    <Box
      component={motion.div}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      sx={{
        display: 'flex',
        minHeight: '100vh',
        backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient 15s ease infinite',
        alignItems: 'center',
        justifyContent: 'center',
        padding: { xs: 2, md: 4 },
        '@keyframes gradient': {
          '0%': {
            backgroundPosition: '0% 50%'
          },
          '50%': {
            backgroundPosition: '100% 50%'
          },
          '100%': {
            backgroundPosition: '0% 50%'
          },
        },
      }}
    >
      <Card
        elevation={6}
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          width: '100%',
          maxWidth: '1000px',
          borderRadius: 4,
          overflow: 'hidden',
          background: '#fff',
        }}
      >
        {/* Left Side - Brand Section */}
        <Box
          component={motion.div}
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.45 },
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            backgroundSize: '200% 200%',
            animation: 'gradientBg 10s ease infinite',
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 4,
            position: 'relative',
            overflow: 'hidden',
            '@keyframes gradientBg': {
              '0%': {
                backgroundPosition: '0% 50%'
              },
              '50%': {
                backgroundPosition: '100% 50%'
              },
              '100%': {
                backgroundPosition: '0% 50%'
              },
            },
          }}
        >
          {/* Background Pattern */}
          <Box sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            opacity: 0.1,
            backgroundImage: `
              radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
              radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 40%)
            `,
            zIndex: 0,
          }} />

          <Box sx={{
            textAlign: 'center',
            position: 'relative',
            zIndex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}>
            <Box
              component={motion.img}
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              src={logo}
              alt="BENYOUNES WEB Logo"
              sx={{
                width: '200px',
                mb: 3,
                filter: 'brightness(1.05)',
                animation: 'float 6s ease-in-out infinite',
                '@keyframes float': {
                  '0%': {
                    transform: 'translateY(0px)'
                  },
                  '50%': {
                    transform: 'translateY(-10px)'
                  },
                  '100%': {
                    transform: 'translateY(0px)'
                  },
                },
              }}
            />

            <Typography
              component={motion.h1}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.5 }}
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 2,
                letterSpacing: '0.5px',
                textShadow: '0 2px 10px rgba(0,0,0,0.1)',
              }}
            >
              Bienvenue!
            </Typography>

            <Typography
              component={motion.p}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.5 }}
              variant="body1"
              sx={{
                mb: 3,
                opacity: 0.9,
                maxWidth: '350px',
                textAlign: 'center',
                lineHeight: 1.6,
              }}
            >
              Connectez-vous à votre espace de gestion de factures et devis pour gérer votre entreprise efficacement.
            </Typography>

            <Box sx={{ mt: 3, display: { xs: 'none', md: 'block' }}}>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                © {new Date().getFullYear()} BENYOUNES WEB - Tous droits réservés
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Right Side - Form */}
        <Box
          component={motion.div}
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.55 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            padding: { xs: 3, sm: 4, md: 5 },
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Box sx={{ maxWidth: '450px', width: '100%', mx: 'auto', position: 'relative' }}>
            <Button
              startIcon={<ArrowBack />}
              onClick={() => navigate('/login')}
              sx={{
                mb: 4,
                color: theme.palette.text.secondary,
                '&:hover': {
                  backgroundColor: 'transparent',
                  color: theme.palette.primary.main,
                }
              }}
            >
              Retour à la connexion
            </Button>

            <Typography
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 1,
                color: theme.palette.text.primary,
              }}
            >
              Réinitialisation du mot de passe
            </Typography>

            <Typography
              variant="body2"
              sx={{
                mb: 4,
                color: theme.palette.text.secondary,
              }}
            >
              Veuillez saisir votre nouveau mot de passe
            </Typography>

            {message.text && (
              <Alert
                severity={message.type}
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                {message.text}
              </Alert>
            )}

            {isTokenValid ? (
              <form onSubmit={handleSubmit}>
                <TextField
                  label="Nouveau mot de passe"
                  name="password"
                  type="password"
                  value={password}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                <TextField
                  label="Confirmer le mot de passe"
                  name="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  disabled={isSubmitting}
                  disableElevation
                  startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <Lock />}
                  sx={{
                    mt: 2,
                    mb: 3,
                    backgroundColor: theme.palette.primary.main,
                    color: '#fff',
                    borderRadius: 2,
                    padding: '12px',
                    fontWeight: 600,
                    textTransform: 'none',
                    fontSize: '1rem',
                    transition: 'all 0.3s',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                      transition: 'all 0.5s',
                    },
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                      boxShadow: '0 4px 12px rgba(63, 81, 255, 0.25)',
                      transform: 'translateY(-2px)',
                      '&::after': {
                        left: '100%',
                      }
                    },
                  }}
                >
                  {isSubmitting ? "Réinitialisation en cours..." : "Réinitialiser le mot de passe"}
                </Button>
              </form>
            ) : (
              <Box sx={{ textAlign: "center", mt: 3 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  component={RouterLink}
                  to="/forgot-password"
                  sx={{
                    borderRadius: 2,
                    padding: '10px 20px',
                    fontWeight: 600,
                    textTransform: 'none',
                    '&:hover': {
                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                      transform: 'translateY(-1px)'
                    }
                  }}
                >
                  Demander un nouveau lien de réinitialisation
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </Card>
    </Box>
  )
}

export default ResetPassword
