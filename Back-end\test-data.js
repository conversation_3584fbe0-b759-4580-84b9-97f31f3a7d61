const mongoose = require('mongoose');
require('./connectdb.js');

const User = require('./models/UserModel');
const Client = require('./models/ClientModel');
const Produit = require('./models/ProduitModel');
const Livreur = require('./models/LivreurModel');

async function testData() {
    try {
        console.log('🔍 Vérification des données dans la base...\n');

        // Test des utilisateurs
        const users = await User.find({});
        console.log(`👥 Utilisateurs: ${users.length}`);
        users.forEach(user => {
            console.log(`  - ${user.nom} ${user.prenom} (${user.role}) - ${user.email}`);
        });

        // Test des clients
        const clients = await Client.find({});
        console.log(`\n👤 Clients: ${clients.length}`);
        clients.forEach(client => {
            console.log(`  - ${client.nom} (${client.email || 'Pas d\'email'})`);
        });

        // Test des produits
        const produits = await Produit.find({});
        console.log(`\n📦 Produits: ${produits.length}`);
        produits.forEach(produit => {
            console.log(`  - ${produit.nom} - ${produit.prix} DT (Stock: ${produit.quantiteStock || 0})`);
        });

        // Test des livreurs
        const livreurs = await Livreur.find({});
        console.log(`\n🚚 Livreurs: ${livreurs.length}`);
        livreurs.forEach(livreur => {
            console.log(`  - ${livreur.prenom} ${livreur.nom} (${livreur.statut}) - Disponible: ${livreur.disponible}`);
        });

        // Test des livreurs disponibles
        const livreursDisponibles = await Livreur.find({ statut: 'ACTIF', disponible: true });
        console.log(`\n✅ Livreurs disponibles: ${livreursDisponibles.length}`);
        livreursDisponibles.forEach(livreur => {
            console.log(`  - ${livreur.prenom} ${livreur.nom} - ${livreur.telephone}`);
        });

        console.log('\n✅ Test terminé');
        process.exit(0);
    } catch (error) {
        console.error('❌ Erreur lors du test:', error);
        process.exit(1);
    }
}

testData();
