import api from "./api"

const clientService = {
  // Récupérer tous les clients
  getClients: async () => {
    try {
      const response = await api.get("/clients")
      return response.data
    } catch (error) {
      console.error("Erreur lors de la récupération des clients:", error)
      return []
    }
  },

  // Alias pour getAllClients (pour compatibilité avec BonLivraisonForm)
  getAllClients: async () => {
    return clientService.getClients();
  },

  // Récupérer un client par son ID
  getClientById: async (id) => {
    try {
      const response = await api.get(`/clients/${id}`)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de la récupération du client ${id}:`, error)
      throw error
    }
  },

  // Créer un nouveau client
  createClient: async (clientData) => {
    try {
      const response = await api.post("/clients", clientData)
      return response.data
    } catch (error) {
      console.error("Erreur lors de la création du client:", error)
      throw error
    }
  },

  // Mettre à jour un client
  updateClient: async (id, clientData) => {
    try {
      console.log(`Envoi de la requête PUT /clients/${id} avec les données:`, clientData);
      const response = await api.put(`/clients/${id}`, clientData);
      console.log(`Réponse de la mise à jour du client ${id}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du client ${id}:`, error);
      console.error('Détails de l\'erreur:', error.response?.data || error.message);
      throw error;
    }
  },

  // Supprimer un client
  deleteClient: async (id) => {
    try {
      const response = await api.delete(`/clients/${id}`)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de la suppression du client ${id}:`, error)
      throw error
    }
  },

  // Récupérer les factures d'un client
  getClientFactures: async (id) => {
    try {
      const response = await api.get(`/clients/${id}/factures`)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de la récupération des factures du client ${id}:`, error)
      throw error
    }
  },

  // Récupérer les devis d'un client
  getClientDevis: async (id) => {
    try {
      const response = await api.get(`/clients/${id}/devis`)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de la récupération des devis du client ${id}:`, error)
      throw error
    }
  },

  // Télécharger un logo pour un client
  uploadClientLogo: async (id, logoFile) => {
    try {
      const formData = new FormData()
      formData.append('logo', logoFile)

      const response = await api.post(`/clients/${id}/logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error(`Erreur lors du téléchargement du logo pour le client ${id}:`, error)
      throw error
    }
  },

  // Récupérer les activités d'un client
  getClientActivities: async (id) => {
    try {
      const response = await api.get(`/clients/${id}/activities`)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de la récupération des activités du client ${id}:`, error)
      throw error
    }
  },

  // Ajouter une activité pour un client
  addClientActivity: async (id, activityData) => {
    try {
      const response = await api.post(`/clients/${id}/activities`, activityData)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de l'ajout d'une activité pour le client ${id}:`, error)
      throw error
    }
  },

  // Récupérer les statistiques d'un client
  getClientStats: async (id) => {
    try {
      const response = await api.get(`/clients/${id}/stats`)
      return response.data
    } catch (error) {
      console.error(`Erreur lors de la récupération des statistiques du client ${id}:`, error)
      throw error
    }
  },
}

export default clientService
