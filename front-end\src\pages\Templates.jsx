  import React, { useState, useEffect } from 'react';
  import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    CardMedia,
    CardActionArea,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Tabs,
    Tab,
    Paper,
    Divider,
    useTheme,
    IconButton
  } from '@mui/material';
  import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    ContentCopy as DuplicateIcon,
    Palette as PaletteIcon,
    Check as CheckIcon,
    FormatColorFill as ColorIcon,
    Image as ImageIcon,
    BrandingWatermark as FooterIcon,
    Style as ThemeIcon
  } from '@mui/icons-material';
  import ColorPicker from '../components/ColorPicker';
  import TemplateCustomizer from '../components/TemplateCustomizer';

  const Templates = () => {
    const theme = useTheme();
    const [templates, setTemplates] = useState([
      {
        id: 1,
        nom: 'Template BenYounes',
        type: 'facture',
        couleurPrimaire: '#f57c00',
        couleurSecondaire: '#f5f5f5',
        police: 'Inter',
        image: '/templates/template1.jpg',
        isDefault: true,
        logo: '/templates/logos/logo5.svg',
        footerLogo: '/templates/footers/footer1.svg',
        theme: '/templates/themes/theme2.svg'
      },
      {
        id: 2,
        nom: 'Template Moderne',
        type: 'facture',
        couleurPrimaire: '#2e7d32',
        couleurSecondaire: '#e8f5e9',
        police: 'Roboto',
        image: '/templates/template2.jpg',
        isDefault: false,
        logo: '/templates/logos/logo2.svg',
        footerLogo: '/templates/footers/footer2.svg',
        theme: '/templates/themes/theme1.svg'
      },
      {
        id: 3,
        nom: 'Template Élégant',
        type: 'devis',
        couleurPrimaire: '#7b1fa2',
        couleurSecondaire: '#f3e5f5',
        police: 'Montserrat',
        image: '/templates/template3.jpg',
        isDefault: true,
        logo: '/templates/logos/logo3.svg',
        footerLogo: '/templates/footers/footer3.svg',
        theme: '/templates/themes/theme3.svg'
      }
    ]);

    const [openDialog, setOpenDialog] = useState(false);
    const [openCustomizer, setOpenCustomizer] = useState(false);
    const [currentTemplate, setCurrentTemplate] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [templateAspect, setTemplateAspect] = useState('couleur'); // 'couleur', 'logo', 'footerLogo', 'theme'

    // Color palette from the image
    const colorPalettes = [
      // Row 1
      ['#f57c00', '#e67e22', '#f39c12', '#d81b60', '#8e24aa', '#3f51b5'],
      // Row 2
      ['#ff9800', '#f1c40f', '#e91e63', '#9c27b0', '#5677fc', '#009688'],
    ];

    // Available logos
    const logos = [
      '/templates/logos/logo1.svg',
      '/templates/logos/logo2.svg',
      '/templates/logos/logo3.svg',
      '/templates/logos/logo4.svg',
      '/templates/logos/logo5.svg',
      '/templates/logos/logo6.svg',
      '/templates/logos/logo7.svg',
      '/templates/logos/logo8.svg',
    ];

    // Available footer logos
    const footerLogos = [
      '/templates/footers/footer1.svg',
      '/templates/footers/footer2.svg',
      '/templates/footers/footer3.svg',
    ];

    // Available themes
    const themeOptions = [
      '/templates/themes/theme1.svg',
      '/templates/themes/theme2.svg',
      '/templates/themes/theme3.svg',
    ];

    const handleTabChange = (event, newValue) => {
      setTabValue(newValue);
    };

    const handleOpenDialog = (template = null) => {
      if (template) {
        setCurrentTemplate({ ...template });
      } else {
        setCurrentTemplate({
          nom: 'Nouveau template',
          type: 'facture',
          couleurPrimaire: '#1976d2',
          couleurSecondaire: '#f5f5f5',
          police: 'Inter',
          isDefault: false
        });
      }
      setOpenDialog(true);
    };

    const handleCloseDialog = () => {
      setOpenDialog(false);
      setCurrentTemplate(null);
    };

    const handleChange = (e) => {
      const { name, value } = e.target;
      setCurrentTemplate(prev => ({
        ...prev,
        [name]: value
      }));
    };

    const handleColorChange = (colorName, value) => {
      setCurrentTemplate(prev => ({
        ...prev,
        [colorName]: value
      }));
    };

    const handleSaveTemplate = () => {
      if (currentTemplate.id) {
        // Mise à jour d'un template existant
        setTemplates(prev => prev.map(t =>
          t.id === currentTemplate.id ? currentTemplate : t
        ));
      } else {
        // Création d'un nouveau template
        const newId = Math.max(...templates.map(t => t.id), 0) + 1;
        setTemplates(prev => [...prev, { ...currentTemplate, id: newId }]);
      }
      handleCloseDialog();
    };

    const handleDeleteTemplate = (id) => {
      setTemplates(prev => prev.filter(t => t.id !== id));
    };

    const handleDuplicateTemplate = (template) => {
      const newId = Math.max(...templates.map(t => t.id), 0) + 1;
      const duplicatedTemplate = {
        ...template,
        id: newId,
        nom: `${template.nom} (copie)`,
        isDefault: false
      };
      setTemplates(prev => [...prev, duplicatedTemplate]);
    };

    const handleSetDefault = (id, type) => {
      setTemplates(prev => prev.map(t =>
        t.type === type ? { ...t, isDefault: t.id === id } : t
      ));
    };

    const handleOpenCustomizer = (template = null) => {
      if (template) {
        setCurrentTemplate({ ...template });
      } else {
        setCurrentTemplate({
          nom: 'Nouveau template',
          type: 'facture',
          couleurPrimaire: '#1976d2',
          couleurSecondaire: '#f5f5f5',
          police: 'Inter',
          isDefault: false
        });
      }
      setOpenCustomizer(true);
    };

    const handleCloseCustomizer = () => {
      setOpenCustomizer(false);
    };

    const handleSaveCustomizer = (updatedTemplate) => {
      const layout = updatedTemplate.layout || 'standard';
      const type = updatedTemplate.type || 'facture';
      const thumbnail = `/templates/thumbnails/template-${type.toLowerCase()}-${layout}.svg`;

      if (updatedTemplate.id) {
        // Update existing template
        setTemplates(prev => prev.map(t =>
          t.id === updatedTemplate.id ? {
            ...t,
            nom: updatedTemplate.name,
            couleurPrimaire: updatedTemplate.color,
            couleurSecondaire: updatedTemplate.secondaryColor || '#f5f5f5',
            police: updatedTemplate.font || 'Inter',
            thumbnail: thumbnail
          } : t
        ));
      } else {
        // Create new template
        const newId = Math.max(...templates.map(t => t.id), 0) + 1;
        setTemplates(prev => [...prev, {
          id: newId,
          nom: updatedTemplate.name,
          type: updatedTemplate.type || 'facture',
          couleurPrimaire: updatedTemplate.color,
          couleurSecondaire: updatedTemplate.secondaryColor || '#f5f5f5',
          police: updatedTemplate.font || 'Inter',
          thumbnail: thumbnail,
          isDefault: false
        }]);
      }
    };

    const filteredTemplates = tabValue === 0
      ? templates
      : templates.filter(t => t.type === (tabValue === 1 ? 'facture' : 'devis'));

    // Function to handle template aspect change
    const handleTemplateAspectChange = (aspect) => {
      setTemplateAspect(aspect);
    };

    // Function to handle color selection
    const handleColorSelect = (color) => {
      if (currentTemplate) {
        setCurrentTemplate({
          ...currentTemplate,
          couleurPrimaire: color
        });

        // Update the template in the templates array
        setTemplates(prev => prev.map(t =>
          t.id === currentTemplate.id ? { ...t, couleurPrimaire: color } : t
        ));
      }
    };

    // Function to handle logo selection
    const handleLogoSelect = (logoPath) => {
      if (currentTemplate) {
        setCurrentTemplate({
          ...currentTemplate,
          logo: logoPath
        });

        // Update the template in the templates array
        setTemplates(prev => prev.map(t =>
          t.id === currentTemplate.id ? { ...t, logo: logoPath } : t
        ));
      }
    };

    // Function to handle footer logo selection
    const handleFooterLogoSelect = (logoPath) => {
      if (currentTemplate) {
        setCurrentTemplate({
          ...currentTemplate,
          footerLogo: logoPath
        });

        // Update the template in the templates array
        setTemplates(prev => prev.map(t =>
          t.id === currentTemplate.id ? { ...t, footerLogo: logoPath } : t
        ));
      }
    };

    // Function to handle theme selection
    const handleThemeSelect = (themePath) => {
      if (currentTemplate) {
        setCurrentTemplate({
          ...currentTemplate,
          theme: themePath
        });

        // Update the template in the templates array
        setTemplates(prev => prev.map(t =>
          t.id === currentTemplate.id ? { ...t, theme: themePath } : t
        ));
      }
    };

    return (
      <Box sx={{ p: { xs: 1, md: 3 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
            Templates de documents
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<PaletteIcon />}
              onClick={() => handleOpenCustomizer()}
            >
              Personnaliser
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
            >
              Nouveau template
            </Button>
          </Box>
        </Box>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab label="Tous les templates" />
            <Tab label="Factures" />
            <Tab label="Devis" />
          </Tabs>
        </Paper>

        {currentTemplate ? (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5">
                {currentTemplate.nom}
              </Typography>
              <Button
                variant="outlined"
                onClick={() => setCurrentTemplate(null)}
              >
                Retour aux templates
              </Button>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} md={5}>
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                    <Button
                      variant={templateAspect === 'couleur' ? 'contained' : 'outlined'}
                      startIcon={<ColorIcon />}
                      onClick={() => handleTemplateAspectChange('couleur')}
                    >
                      Couleur
                    </Button>
                    <Button
                      variant={templateAspect === 'logo' ? 'contained' : 'outlined'}
                      startIcon={<ImageIcon />}
                      onClick={() => handleTemplateAspectChange('logo')}
                    >
                      Logo
                    </Button>
                    <Button
                      variant={templateAspect === 'footerLogo' ? 'contained' : 'outlined'}
                      startIcon={<FooterIcon />}
                      onClick={() => handleTemplateAspectChange('footerLogo')}
                    >
                      Pied de page
                    </Button>
                    <Button
                      variant={templateAspect === 'theme' ? 'contained' : 'outlined'}
                      startIcon={<ThemeIcon />}
                      onClick={() => handleTemplateAspectChange('theme')}
                    >
                      Thème
                    </Button>
                  </Box>

                  {templateAspect === 'couleur' && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Couleur
                      </Typography>
                      <Box sx={{ mb: 3 }}>
                        {colorPalettes.map((row, rowIndex) => (
                          <Box
                            key={rowIndex}
                            sx={{
                              display: 'flex',
                              gap: 1,
                              mb: 1
                            }}
                          >
                            {row.map((color) => (
                              <Box
                                key={color}
                                onClick={() => handleColorSelect(color)}
                                sx={{
                                  width: 40,
                                  height: 40,
                                  bgcolor: color,
                                  borderRadius: 1,
                                  cursor: 'pointer',
                                  border: currentTemplate.couleurPrimaire === color ? '3px solid #000' : 'none',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  '&:hover': {
                                    boxShadow: '0 0 0 3px rgba(0,0,0,0.2)'
                                  }
                                }}
                              >
                                {currentTemplate.couleurPrimaire === color && (
                                  <CheckIcon sx={{ color: '#fff' }} />
                                )}
                              </Box>
                            ))}
                          </Box>
                        ))}
                      </Box>
                      <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                        <Typography variant="body2">
                          La couleur sélectionnée sera appliquée aux éléments principaux de votre document.
                        </Typography>
                      </Box>
                    </Box>
                  )}

                  {templateAspect === 'logo' && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Logo
                      </Typography>
                      <Grid container spacing={2}>
                        {logos.map((logo, index) => (
                          <Grid item xs={4} key={index}>
                            <Box
                              onClick={() => handleLogoSelect(logo)}
                              sx={{
                                p: 2,
                                border: currentTemplate.logo === logo ? '2px solid #000' : '1px solid #ddd',
                                borderRadius: 1,
                                cursor: 'pointer',
                                height: '100px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                position: 'relative',
                                '&:hover': {
                                  boxShadow: '0 0 0 3px rgba(0,0,0,0.1)'
                                }
                              }}
                            >
                              <img
                                src={logo}
                                alt={`Logo ${index + 1}`}
                                style={{ maxWidth: '100%', maxHeight: '80px' }}
                              />
                              {currentTemplate.logo === logo && (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: 5,
                                    right: 5,
                                    bgcolor: 'primary.main',
                                    color: '#fff',
                                    borderRadius: '50%',
                                    width: 24,
                                    height: 24,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                  }}
                                >
                                  <CheckIcon fontSize="small" />
                                </Box>
                              )}
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}

                  {templateAspect === 'footerLogo' && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Logos de pieds de page
                      </Typography>
                      <Grid container spacing={2}>
                        {footerLogos.map((logo, index) => (
                          <Grid item xs={12} key={index}>
                            <Box
                              onClick={() => handleFooterLogoSelect(logo)}
                              sx={{
                                p: 2,
                                border: currentTemplate.footerLogo === logo ? '2px solid #000' : '1px solid #ddd',
                                borderRadius: 1,
                                cursor: 'pointer',
                                height: '80px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                position: 'relative',
                                '&:hover': {
                                  boxShadow: '0 0 0 3px rgba(0,0,0,0.1)'
                                }
                              }}
                            >
                              <img
                                src={logo}
                                alt={`Footer Logo ${index + 1}`}
                                style={{ maxWidth: '100%', maxHeight: '60px' }}
                              />
                              {currentTemplate.footerLogo === logo && (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: 5,
                                    right: 5,
                                    bgcolor: 'primary.main',
                                    color: '#fff',
                                    borderRadius: '50%',
                                    width: 24,
                                    height: 24,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                  }}
                                >
                                  <CheckIcon fontSize="small" />
                                </Box>
                              )}
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}

                  {templateAspect === 'theme' && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Thème
                      </Typography>
                      <Grid container spacing={2}>
                        {themeOptions.map((themePath, index) => (
                          <Grid item xs={12} sm={6} md={4} key={index}>
                            <Box
                              onClick={() => handleThemeSelect(themePath)}
                              sx={{
                                p: 1,
                                border: currentTemplate.theme === themePath ? '2px solid #000' : '1px solid #ddd',
                                borderRadius: 1,
                                cursor: 'pointer',
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                position: 'relative',
                                '&:hover': {
                                  boxShadow: '0 0 0 3px rgba(0,0,0,0.1)'
                                }
                              }}
                            >
                              <img
                                src={themePath}
                                alt={`Theme ${index + 1}`}
                                style={{ width: '100%', height: 'auto' }}
                              />
                              <Button
                                variant={currentTemplate.theme === themePath ? 'contained' : 'outlined'}
                                size="small"
                                sx={{ mt: 1, width: '80%' }}
                              >
                                {currentTemplate.theme === themePath ? 'Utilisé' : 'Choisir'}
                              </Button>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}
                </Paper>
              </Grid>

              <Grid item xs={12} md={7}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Aperçu
                  </Typography>
                  <Box
                    sx={{
                      border: '1px solid #ddd',
                      borderRadius: 1,
                      p: 2,
                      height: '600px',
                      overflow: 'auto',
                      bgcolor: '#fff'
                    }}
                  >
                    {/* Header with title and date */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h5" sx={{ color: currentTemplate.couleurPrimaire, fontWeight: 'bold' }}>
                        Facture F23012345
                      </Typography>
                      <Typography variant="body2">
                        22 mars 2025
                      </Typography>
                    </Box>

                    {/* Header with logo */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
                      <Box sx={{ width: '120px', height: '60px' }}>
                        <img
                          src={currentTemplate.logo}
                          alt="Logo"
                          style={{ maxWidth: '100%', maxHeight: '100%' }}
                        />
                      </Box>
                    </Box>

                    {/* Company and client info */}
                    <Grid container spacing={3} sx={{ mb: 3 }}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="textSecondary" sx={{ fontWeight: 'bold', color: currentTemplate.couleurPrimaire }}>Émetteur</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Société :</Typography>
                        <Typography variant="body2">Wonderland SARL</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Votre contact :</Typography>
                        <Typography variant="body2">WAJDI HB</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Adresse :</Typography>
                        <Typography variant="body2">12 rue du Lapin Blanc</Typography>
                        <Typography variant="body2">12345 Westerham</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Numéro d'entreprise :</Typography>
                        <Typography variant="body2">123 456 789 00012</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Adresse email :</Typography>
                        <Typography variant="body2">alice.liddell@wonderland</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="textSecondary" sx={{ fontWeight: 'bold', color: currentTemplate.couleurPrimaire }}>Destinataire</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Nom :</Typography>
                        <Typography variant="body2">John Doe</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Adresse :</Typography>
                        <Typography variant="body2">1 rue de la Paix</Typography>
                        <Typography variant="body2">74800 La Roche-sur-Foron</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Pays :</Typography>
                        <Typography variant="body2">France</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Adresse email :</Typography>
                        <Typography variant="body2"><EMAIL></Typography>
                      </Grid>
                    </Grid>

                    {/* Détail section */}
                    <Typography variant="body1" sx={{ fontWeight: 'bold', color: currentTemplate.couleurPrimaire, mb: 2 }}>
                      Détail
                    </Typography>

                    {/* Table header */}
                    <Box sx={{
                      display: 'flex',
                      bgcolor: currentTemplate.couleurPrimaire,
                      color: '#fff',
                      p: 1,
                      borderRadius: '4px 4px 0 0'
                    }}>
                      <Box sx={{ flex: '0 0 80px' }}>Type</Box>
                      <Box sx={{ flex: 1 }}>Description</Box>
                      <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>Prix unitaire HT</Box>
                      <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>Quantité</Box>
                      <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>TVA</Box>
                      <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>Total HT</Box>
                    </Box>

                    {/* Table rows */}
                    {[
                      { type: 'Service', desc: 'Installation du logiciel de gestion de factures', price: 1700.00, qty: 1, tva: '20%' },
                      { type: 'Service', desc: 'Formation de 2 jours', price: 2000.00, qty: 1, tva: '20%' },
                      { type: 'Produit', desc: 'Ordinateur de bureau PASOKON', price: 1200.00, qty: 3, tva: '20%' }
                    ].map((item, index) => (
                      <Box key={index} sx={{
                        display: 'flex',
                        p: 1,
                        borderBottom: '1px solid #eee',
                        '&:nth-of-type(even)': {
                          bgcolor: '#f9f9f9'
                        }
                      }}>
                        <Box sx={{ flex: '0 0 80px' }}>{item.type}</Box>
                        <Box sx={{ flex: 1 }}>{item.desc}</Box>
                        <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>{item.price.toFixed(2)} €</Box>
                        <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>{item.qty}</Box>
                        <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>{item.tva}</Box>
                        <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>{(item.qty * item.price).toFixed(2)} €</Box>
                      </Box>
                    ))}

                    {/* Totals */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mt: 3 }}>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '250px',
                        p: 1
                      }}>
                        <Typography variant="body2">Total HT</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>7 300,00 €</Typography>
                      </Box>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '250px',
                        p: 1
                      }}>
                        <Typography variant="body2">TVA (20%)</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>1 460,00 €</Typography>
                      </Box>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '250px',
                        p: 1,
                        bgcolor: '#f5f5f5',
                        borderRadius: 1
                      }}>
                        <Typography variant="body1" sx={{ fontWeight: 'bold' }}>Total TTC</Typography>
                        <Typography variant="body1" sx={{ fontWeight: 'bold' }}>8 760,00 €</Typography>
                      </Box>
                    </Box>

                    {/* Conditions */}
                    <Box sx={{ mt: 4 }}>
                      <Typography variant="body1" sx={{ color: currentTemplate.couleurPrimaire, fontWeight: 'bold' }}>
                        Conditions
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Conditions de règlement :</Typography>
                      <Typography variant="body2">45 jours fin de mois</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Mode de règlement :</Typography>
                      <Typography variant="body2">Virement bancaire</Typography>
                    </Box>

                    {/* Footer */}
                    <Box sx={{ mt: 4, textAlign: 'center' }}>
                      <img
                        src={currentTemplate.footerLogo}
                        alt="Footer Logo"
                        style={{ maxWidth: '200px', height: 'auto' }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        1/1
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {filteredTemplates.map(template => (
              <Grid item xs={12} sm={6} md={4} key={template.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardActionArea onClick={() => setCurrentTemplate(template)}>
                    <CardMedia
                      component="img"
                      height="180"
                      image={template.thumbnail || `/templates/thumbnails/template-${template.type}-standard.svg`}
                      alt={template.nom}
                      sx={{
                        objectFit: 'contain',
                        bgcolor: '#f5f5f5',
                        p: 1,
                        borderBottom: `1px solid ${template.couleurPrimaire}20`
                      }}
                    />
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="h6" component="div">
                          {template.nom}
                        </Typography>
                        {template.isDefault && (
                          <Typography
                            variant="caption"
                            sx={{
                              bgcolor: 'primary.main',
                              color: 'white',
                              px: 1,
                              py: 0.5,
                              borderRadius: 1
                            }}
                          >
                            Par défaut
                          </Typography>
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Type: {template.type === 'facture' ? 'Facture' : 'Devis'}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            bgcolor: template.couleurPrimaire,
                            borderRadius: '50%',
                            border: '1px solid #ddd'
                          }}
                        />
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            bgcolor: template.couleurSecondaire,
                            borderRadius: '50%',
                            border: '1px solid #ddd'
                          }}
                        />
                      </Box>
                    </CardContent>
                  </CardActionArea>
                  <Box sx={{ p: 2, mt: 'auto', display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleOpenDialog(template)}
                    >
                      Modifier
                    </Button>
                    <Button
                      size="small"
                      startIcon={<PaletteIcon />}
                      onClick={() => setCurrentTemplate(template)}
                      sx={{ color: template.couleurPrimaire }}
                    >
                      Style
                    </Button>
                    <Box sx={{ display: 'flex', gap: 1, mt: { xs: 1, sm: 0 }, width: { xs: '100%', sm: 'auto' } }}>
                      <Button
                        size="small"
                        startIcon={<DuplicateIcon />}
                        onClick={() => handleDuplicateTemplate(template)}
                      >
                        Dupliquer
                      </Button>
                      {!template.isDefault && (
                        <Button
                          size="small"
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={() => handleDeleteTemplate(template.id)}
                        >
                          Supprimer
                        </Button>
                      )}
                    </Box>
                  </Box>
                  {!template.isDefault && (
                    <Box sx={{ p: 2, pt: 0 }}>
                      <Button
                        fullWidth
                        variant="outlined"
                        onClick={() => handleSetDefault(template.id, template.type)}
                      >
                        Définir par défaut
                      </Button>
                    </Box>
                  )}
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Dialog pour créer/modifier un template */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>
            {currentTemplate?.id ? 'Modifier le template' : 'Créer un nouveau template'}
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="nom"
                  label="Nom du template"
                  value={currentTemplate?.nom || ''}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                />

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>Type de document</Typography>
                  <Tabs
                    value={currentTemplate?.type === 'devis' ? 1 : 0}
                    onChange={(e, val) => handleChange({ target: { name: 'type', value: val === 1 ? 'devis' : 'facture' } })}
                  >
                    <Tab label="Facture" />
                    <Tab label="Devis" />
                  </Tabs>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>Couleurs</Typography>
                <Box sx={{ mb: 2 }}>
                  <ColorPicker
                    label="Couleur principale"
                    value={currentTemplate?.couleurPrimaire || '#1976d2'}
                    onChange={(color) => handleColorChange('couleurPrimaire', color)}
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <ColorPicker
                    label="Couleur secondaire"
                    value={currentTemplate?.couleurSecondaire || '#f5f5f5'}
                    onChange={(color) => handleColorChange('couleurSecondaire', color)}
                  />
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>Police</Typography>
                <TextField
                  select
                  name="police"
                  label="Police de caractères"
                  value={currentTemplate?.police || 'Inter'}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  SelectProps={{
                    native: true,
                  }}
                >
                  <option value="Inter">Inter</option>
                  <option value="Roboto">Roboto</option>
                  <option value="Montserrat">Montserrat</option>
                  <option value="Open Sans">Open Sans</option>
                  <option value="Lato">Lato</option>
                </TextField>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>Aperçu</Typography>
                <Paper
                  elevation={3}
                  sx={{
                    p: 2,
                    height: '400px',
                    overflow: 'auto',
                    border: `1px solid ${theme.palette.divider}`,
                    backgroundColor: '#fff'
                  }}
                >
                  {/* Aperçu simplifié du template */}
                  <Box sx={{
                    fontFamily: currentTemplate?.police || 'Inter',
                    mb: 2
                  }}>
                    {/* Header with title and date */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="h6" sx={{ color: currentTemplate?.couleurPrimaire || '#f57c00', fontWeight: 'bold' }}>
                        Facture F23012345
                      </Typography>
                      <Typography variant="body2">
                        22 mars 2025
                      </Typography>
                    </Box>

                    {/* Logo */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                      <Box sx={{ width: '80px', height: '40px', bgcolor: '#f5f5f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Typography variant="caption">LOGO</Typography>
                      </Box>
                    </Box>

                    {/* Émetteur et Destinataire */}
                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: currentTemplate?.couleurPrimaire || '#f57c00' }}>
                          Émetteur
                        </Typography>
                        <Typography variant="caption">
                          Wonderland SARL<br />
                          WAJDI HB<br />
                          12 rue du Lapin Blanc
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: currentTemplate?.couleurPrimaire || '#f57c00' }}>
                          Destinataire
                        </Typography>
                        <Typography variant="caption">
                          John Doe<br />
                          1 rue de la Paix<br />
                          74800 La Roche-sur-Foron
                        </Typography>
                      </Grid>
                    </Grid>

                    {/* Détail */}
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: currentTemplate?.couleurPrimaire || '#f57c00', mb: 1 }}>
                      Détail
                    </Typography>

                    {/* Table header */}
                    <Box sx={{
                      display: 'flex',
                      bgcolor: currentTemplate?.couleurPrimaire || '#f57c00',
                      color: '#fff',
                      p: 0.5,
                      fontSize: '0.75rem',
                      borderRadius: '4px 4px 0 0'
                    }}>
                      <Box sx={{ flex: '0 0 40px' }}>Type</Box>
                      <Box sx={{ flex: 1 }}>Description</Box>
                      <Box sx={{ flex: '0 0 60px', textAlign: 'right' }}>Total</Box>
                    </Box>

                    {/* Table row */}
                    <Box sx={{
                      display: 'flex',
                      p: 0.5,
                      fontSize: '0.75rem',
                      borderBottom: '1px solid #eee'
                    }}>
                      <Box sx={{ flex: '0 0 40px' }}>Service</Box>
                      <Box sx={{ flex: 1 }}>Installation du logiciel</Box>
                      <Box sx={{ flex: '0 0 60px', textAlign: 'right' }}>1 700,00 €</Box>
                    </Box>

                    {/* Totals */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mt: 2, fontSize: '0.75rem' }}>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '150px'
                      }}>
                        <Typography variant="caption">Total HT</Typography>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>7 300,00 €</Typography>
                      </Box>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '150px'
                      }}>
                        <Typography variant="caption">TVA (20%)</Typography>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>1 460,00 €</Typography>
                      </Box>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '150px',
                        bgcolor: '#f5f5f5',
                        p: 0.5
                      }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Total TTC</Typography>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>8 760,00 €</Typography>
                      </Box>
                    </Box>

                    {/* Conditions */}
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', color: currentTemplate?.couleurPrimaire || '#f57c00' }}>
                        Conditions
                      </Typography>
                      <Typography variant="caption">
                        Conditions de règlement : 45 jours fin de mois<br />
                        Mode de règlement : Virement bancaire
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button
              variant="contained"
              onClick={handleSaveTemplate}
            >
              {currentTemplate?.id ? 'Mettre à jour' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Template Customizer */}
        <TemplateCustomizer
          open={openCustomizer}
          onClose={handleCloseCustomizer}
          template={{
            id: currentTemplate?.id,
            name: currentTemplate?.nom,
            type: currentTemplate?.type,
            color: currentTemplate?.couleurPrimaire
          }}
          onSave={handleSaveCustomizer}
        />
      </Box>
    );
  };

  export default Templates;