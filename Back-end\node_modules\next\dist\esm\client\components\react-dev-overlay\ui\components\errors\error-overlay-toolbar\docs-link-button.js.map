{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.tsx"], "sourcesContent": ["import {\n  NEXTJS_HYDRATION_ERROR_LINK,\n  REACT_HYDRATION_ERROR_LINK,\n} from '../../../../../is-hydration-error'\nimport { parseUrlFromText } from '../../../utils/parse-url-from-text'\n\nconst docsURLAllowlist = ['https://nextjs.org', 'https://react.dev']\n\nfunction docsLinkMatcher(text: string): boolean {\n  return docsURLAllowlist.some((url) => text.startsWith(url))\n}\n\nfunction getDocsURLFromErrorMessage(text: string): string | null {\n  const urls = parseUrlFromText(text, docsLinkMatcher)\n\n  if (urls.length === 0) {\n    return null\n  }\n\n  const href = urls[0]\n\n  // Replace react hydration error link with nextjs hydration error link\n  if (href === REACT_HYDRATION_ERROR_LINK) {\n    return NEXTJS_HYDRATION_ERROR_LINK\n  }\n\n  return href\n}\n\nexport function DocsLinkButton({ errorMessage }: { errorMessage: string }) {\n  const docsURL = getDocsURLFromErrorMessage(errorMessage)\n\n  if (!docsURL) {\n    return (\n      <button\n        title=\"No related documentation found\"\n        aria-label=\"No related documentation found\"\n        className=\"docs-link-button\"\n        disabled\n      >\n        <DocsIcon\n          className=\"error-overlay-toolbar-button-icon\"\n          width={14}\n          height={14}\n        />\n      </button>\n    )\n  }\n\n  return (\n    <a\n      title=\"Go to related documentation\"\n      aria-label=\"Go to related documentation\"\n      className=\"docs-link-button\"\n      href={docsURL}\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <DocsIcon\n        className=\"error-overlay-toolbar-button-icon\"\n        width={14}\n        height={14}\n      />\n    </a>\n  )\n}\n\nfunction DocsIcon(props: React.SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M0 .875h4.375C5.448.875 6.401 1.39 7 2.187A3.276 3.276 0 0 1 9.625.875H14v11.156H9.4c-.522 0-1.023.208-1.392.577l-.544.543h-.928l-.544-.543c-.369-.37-.87-.577-1.392-.577H0V.875zm6.344 3.281a1.969 1.969 0 0 0-1.969-1.968H1.312v8.53H4.6c.622 0 1.225.177 1.744.502V4.156zm1.312 7.064V4.156c0-1.087.882-1.968 1.969-1.968h3.063v8.53H9.4c-.622 0-1.225.177-1.744.502z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["NEXTJS_HYDRATION_ERROR_LINK", "REACT_HYDRATION_ERROR_LINK", "parseUrlFromText", "docsURLAllowlist", "docsLinkMatcher", "text", "some", "url", "startsWith", "getDocsURLFromErrorMessage", "urls", "length", "href", "DocsLinkButton", "errorMessage", "docsURL", "button", "title", "aria-label", "className", "disabled", "DocsIcon", "width", "height", "a", "target", "rel", "props", "svg", "viewBox", "fill", "xmlns", "path", "fillRule", "clipRule", "d"], "mappings": ";AAAA,SACEA,2BAA2B,EAC3BC,0BAA0B,QACrB,oCAAmC;AAC1C,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,MAAMC,mBAAmB;IAAC;IAAsB;CAAoB;AAEpE,SAASC,gBAAgBC,IAAY;IACnC,OAAOF,iBAAiBG,IAAI,CAAC,CAACC,MAAQF,KAAKG,UAAU,CAACD;AACxD;AAEA,SAASE,2BAA2BJ,IAAY;IAC9C,MAAMK,OAAOR,iBAAiBG,MAAMD;IAEpC,IAAIM,KAAKC,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAMC,OAAOF,IAAI,CAAC,EAAE;IAEpB,sEAAsE;IACtE,IAAIE,SAASX,4BAA4B;QACvC,OAAOD;IACT;IAEA,OAAOY;AACT;AAEA,OAAO,SAASC,eAAe,KAA0C;IAA1C,IAAA,EAAEC,YAAY,EAA4B,GAA1C;IAC7B,MAAMC,UAAUN,2BAA2BK;IAE3C,IAAI,CAACC,SAAS;QACZ,qBACE,KAACC;YACCC,OAAM;YACNC,cAAW;YACXC,WAAU;YACVC,QAAQ;sBAER,cAAA,KAACC;gBACCF,WAAU;gBACVG,OAAO;gBACPC,QAAQ;;;IAIhB;IAEA,qBACE,KAACC;QACCP,OAAM;QACNC,cAAW;QACXC,WAAU;QACVP,MAAMG;QACNU,QAAO;QACPC,KAAI;kBAEJ,cAAA,KAACL;YACCF,WAAU;YACVG,OAAO;YACPC,QAAQ;;;AAIhB;AAEA,SAASF,SAASM,KAAoC;IACpD,qBACE,KAACC;QACCN,OAAM;QACNC,QAAO;QACPM,SAAQ;QACRC,MAAK;QACLC,OAAM;QACL,GAAGJ,KAAK;kBAET,cAAA,KAACK;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFL,MAAK;;;AAIb"}