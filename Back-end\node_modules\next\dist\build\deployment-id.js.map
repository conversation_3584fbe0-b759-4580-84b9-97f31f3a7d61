{"version": 3, "sources": ["../../src/build/deployment-id.ts"], "sourcesContent": ["export function getDeploymentIdQueryOrEmptyString(): string {\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    return `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n  }\n  return ''\n}\n"], "names": ["getDeploymentIdQueryOrEmptyString", "process", "env", "NEXT_DEPLOYMENT_ID"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA;IACd,IAAIC,QAAQC,GAAG,CAACC,kBAAkB,EAAE;QAClC,OAAO,CAAC,KAAK,EAAEF,QAAQC,GAAG,CAACC,kBAAkB,EAAE;IACjD;IACA,OAAO;AACT"}