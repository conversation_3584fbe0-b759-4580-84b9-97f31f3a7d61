import { createTheme } from "@mui/material/styles";

const cegidTheme = createTheme({
  palette: {
    mode: "light",
    primary: {
      main: "#3f51ff",
      light: "#6573ff",
      dark: "#2c3bcc",
      contrastText: "#ffffff"
    },
    secondary: {
      main: "#FF6B49",
      light: "#ff8c6a",
      dark: "#e54a28",
      contrastText: "#ffffff"
    },
    success: {
      main: "#4CAF50",
      light: "#80e27e",
      dark: "#087f23"
    },
    warning: {
      main: "#FF9800",
      light: "#ffc947",
      dark: "#c66900"
    },
    error: {
      main: "#F44336",
      light: "#ff7961",
      dark: "#ba000d"
    },
    info: {
      main: "#2196F3",
      light: "#64b5f6",
      dark: "#0069c0"
    },
    background: {
      default: "#f5f7fa",
      paper: "#ffffff",
    },
    text: {
      primary: "#333333",
      secondary: "#637381",
    },
    divider: "rgba(0, 0, 0, 0.08)",
    action: {
      active: "rgba(0, 0, 0, 0.54)",
      hover: "rgba(0, 0, 0, 0.04)",
      selected: "rgba(63, 81, 255, 0.08)",
      disabled: "rgba(0, 0, 0, 0.26)",
      disabledBackground: "rgba(0, 0, 0, 0.12)",
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontSize: "2rem", fontWeight: 600, lineHeight: 1.2 },
    h2: { fontSize: "1.75rem", fontWeight: 600, lineHeight: 1.3 },
    h3: { fontSize: "1.5rem", fontWeight: 600, lineHeight: 1.35 },
    h4: { fontSize: "1.25rem", fontWeight: 600, lineHeight: 1.4 },
    h5: { fontSize: "1.125rem", fontWeight: 600, lineHeight: 1.4 },
    h6: { fontSize: "1rem", fontWeight: 600, lineHeight: 1.5 },
    subtitle1: { fontSize: "0.9375rem", lineHeight: 1.5, fontWeight: 500 },
    subtitle2: { fontSize: "0.875rem", lineHeight: 1.5, fontWeight: 500 },
    body1: { fontSize: "0.9375rem", lineHeight: 1.5 },
    body2: { fontSize: "0.875rem", lineHeight: 1.5 },
    button: { textTransform: "none", fontWeight: 500 },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: "#f5f7fa",
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: "8px 16px",
          boxShadow: "none",
          textTransform: "none",
          fontWeight: 500,
        },
        containedPrimary: {
          "&:hover": {
            boxShadow: "0 4px 12px rgba(63, 81, 255, 0.2)",
          },
        },
        containedSecondary: {
          "&:hover": {
            boxShadow: "0 4px 12px rgba(255, 107, 73, 0.2)",
          },
        },
        outlined: {
          borderWidth: 1,
        },
        sizeSmall: {
          padding: "6px 12px",
          fontSize: "0.8125rem",
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: "0 0 0 1px rgba(63, 63, 68, 0.05), 0 1px 3px 0 rgba(63, 63, 68, 0.15)",
          overflow: "hidden",
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: "20px",
          "&:last-child": {
            paddingBottom: "20px",
          },
        },
      },
    },
    MuiCardHeader: {
      styleOverrides: {
        root: {
          padding: "20px",
        },
        title: {
          fontSize: "1rem",
          fontWeight: 600,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: "16px 20px",
          borderColor: "rgba(0, 0, 0, 0.08)",
        },
        head: {
          fontWeight: 600,
          backgroundColor: "#F9FAFB",
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          "&:hover": {
            backgroundColor: "rgba(0, 0, 0, 0.02)",
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: "none",
          boxShadow: "0 0 0 1px rgba(63, 63, 68, 0.05), 0 1px 3px 0 rgba(63, 63, 68, 0.15)",
        },
        rounded: {
          borderRadius: 12,
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          "& fieldset": {
            borderColor: "rgba(0, 0, 0, 0.15)",
          },
          "&:hover fieldset": {
            borderColor: "rgba(0, 0, 0, 0.25)",
          },
          "&.Mui-focused fieldset": {
            borderColor: "#3f51ff",
          },
        },
        input: {
          padding: "12px 16px",
        },
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          fontSize: "0.875rem",
          fontWeight: 500,
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        outlined: {
          borderRadius: 8,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          fontWeight: 500,
          fontSize: "0.875rem",
          textTransform: "none",
          minWidth: 100,
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        indicator: {
          height: 3,
          borderTopLeftRadius: 3,
          borderTopRightRadius: 3,
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          fontWeight: 500,
          borderRadius: 16,
        },
        filled: {
          boxShadow: "none",
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: "rgba(0, 0, 0, 0.08)",
        },
      },
    },
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          borderRadius: 3,
          height: 6,
        },
      },
    },
    MuiBadge: {
      styleOverrides: {
        root: {
          padding: "0 6px",
        },
      },
    },
    MuiStepper: {
      styleOverrides: {
        root: {
          padding: "24px 0",
        },
      },
    },
    MuiStepConnector: {
      styleOverrides: {
        line: {
          borderColor: "rgba(0, 0, 0, 0.12)",
        },
      },
    },
    MuiStepLabel: {
      styleOverrides: {
        label: {
          fontSize: "0.875rem",
          fontWeight: 500,
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          backgroundColor: "#3f51ff",
          color: "#ffffff",
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 12,
          boxShadow: "0 8px 24px rgba(0, 0, 0, 0.15)",
        },
      },
    },
  },
});

export default cegidTheme;