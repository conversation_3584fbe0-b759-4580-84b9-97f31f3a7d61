import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  useTheme,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
  Avatar,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  CalendarToday as CalendarIcon,
  Visibility as VisibilityIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getUsersByRole, deleteUser, getUserByIdWithPassword } from '../services/userService';

// Mock data for vendeurs
const mockVendeurs = [
  {
    id: 1,
    nom: 'Jean Dupont',
    email: '<EMAIL>',
    dateCreation: '2023-05-15',
    telephone: '+33 6 12 34 56 78',
    status: 'active',
    avatar: null,
    factures: 12,
    devis: 8
  },
  {
    id: 2,
    nom: 'Marie Martin',
    email: '<EMAIL>',
    dateCreation: '2023-05-28',
    telephone: '+33 6 23 45 67 89',
    status: 'active',
    avatar: null,
    factures: 8,
    devis: 5
  },
  {
    id: 3,
    nom: 'Pierre Durand',
    email: '<EMAIL>',
    dateCreation: '2023-06-10',
    telephone: '+33 6 34 56 78 90',
    status: 'inactive',
    avatar: null,
    factures: 0,
    devis: 2
  },
  {
    id: 4,
    nom: 'Sophie Lefebvre',
    email: '<EMAIL>',
    dateCreation: '2023-06-15',
    telephone: '+33 6 45 67 89 01',
    status: 'active',
    avatar: null,
    factures: 15,
    devis: 10
  },
];

const VendeursPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // State
  const [vendeurs, setVendeurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [vendeurToDelete, setVendeurToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedVendeur, setSelectedVendeur] = useState(null);

  // Fetch vendeurs on component mount
  useEffect(() => {
    const fetchVendeurs = async () => {
      try {
        const data = await getUsersByRole('VENDEUR');
        setVendeurs(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching vendeurs:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors de la récupération des vendeurs',
          severity: 'error'
        });
        setLoading(false);
      }
    };

    fetchVendeurs();
  }, []);

  // Handle search
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Filter vendeurs based on search term
  const filteredVendeurs = vendeurs.filter(vendeur => {
    return (
      vendeur.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendeur.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendeur.telephone.includes(searchTerm)
    );
  });

  // Handle menu open
  const handleMenuOpen = (event, vendeur) => {
    setAnchorEl(event.currentTarget);
    setSelectedVendeur(vendeur);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedVendeur(null);
  };

  // Handle view details
  const handleViewDetails = async () => {
    try {
      // Fetch user details with password
      if (selectedVendeur && selectedVendeur._id) {
        const userWithPassword = await getUserByIdWithPassword(selectedVendeur._id);
        setSelectedVendeur({...selectedVendeur, ...userWithPassword});
      }
      setDetailsDialogOpen(true);
    } catch (error) {
      console.error('Error fetching user details with password:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la récupération des détails du vendeur',
        severity: 'error'
      });
    }
  };

  // Handle edit vendeur
  const handleEditVendeur = () => {
    if (selectedVendeur) {
      navigate(`/admin/utilisateurs/edit/${selectedVendeur._id}`);
    }
  };

  // Handle delete dialog open
  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setVendeurToDelete(null);
  };

  // Handle details dialog close
  const handleDetailsDialogClose = () => {
    setDetailsDialogOpen(false);
  };

  // Handle delete vendeur
  const handleDeleteVendeur = async () => {
    if (selectedVendeur) {
      try {
        setLoading(true);
        await deleteUser(selectedVendeur._id);

        // Update local state
        setVendeurs(vendeurs.filter(vendeur => vendeur._id !== selectedVendeur._id));

        // Show success message
        setSnackbar({
          open: true,
          message: `Le vendeur ${selectedVendeur.nom} a été supprimé avec succès.`,
          severity: 'success'
        });
      } catch (error) {
        console.error('Error deleting vendeur:', error);
        setSnackbar({
          open: true,
          message: `Erreur lors de la suppression du vendeur: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    }

    handleDeleteDialogClose();
  };

  // Handle add vendeur
  const handleAddVendeur = () => {
    navigate('/admin/utilisateurs/new?role=VENDEUR');
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };



  // Get initials for avatar
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Gestion des vendeurs
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gérez les comptes vendeurs de votre système.
          </Typography>
        </Box>

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddVendeur}
          sx={{ borderRadius: 2 }}
        >
          Ajouter un vendeur
        </Button>
      </Box>

      {/* Search */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{ mb: 3 }}
      >
        <Card elevation={1}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PersonIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="medium">
                  {vendeurs.length} Vendeurs
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TextField
                  placeholder="Rechercher un vendeur..."
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  sx={{ width: 250 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    )
                  }}
                />

              </Box>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Vendeurs Table */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }
        }}
      >
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Vendeur</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Téléphone</TableCell>
                <TableCell>Date d'ajout</TableCell>
                <TableCell>Factures</TableCell>
                <TableCell>Devis</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from(new Array(5)).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{
                          width: 36,
                          height: 36,
                          borderRadius: '50%',
                          bgcolor: '#f0f0f0',
                          mr: 2
                        }} />
                        <Box sx={{ height: 20, width: '70%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '90%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '60%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '50%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '30%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '30%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ height: 20, width: 20, bgcolor: '#f0f0f0', borderRadius: '50%', ml: 'auto' }} />
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredVendeurs.length === 0 ? (
                // No results
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                      Aucun vendeur trouvé
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                // Vendeur rows
                filteredVendeurs.map((vendeur) => (
                  <TableRow key={vendeur.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={vendeur.avatar}
                          alt={vendeur.nom}
                          sx={{
                            width: 36,
                            height: 36,
                            mr: 2,
                            bgcolor: theme.palette.primary.main
                          }}
                        >
                          {getInitials(vendeur.nom)}
                        </Avatar>
                        <Typography variant="body1" fontWeight="medium">
                          {vendeur.nom}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{vendeur.email}</TableCell>
                    <TableCell>{vendeur.telephone}</TableCell>
                    <TableCell>{vendeur.dateCreation}</TableCell>
                    <TableCell>{vendeur.factures || 0}</TableCell>
                    <TableCell>{vendeur.devis || 0}</TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Tooltip title="Modifier" arrow placement="top">
                          <IconButton
                            onClick={() => { setSelectedVendeur(vendeur); handleEditVendeur(); }}
                            size="small"
                            sx={{
                              bgcolor: 'primary.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'primary.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer" arrow placement="top">
                          <IconButton
                            onClick={() => { setSelectedVendeur(vendeur); handleDeleteDialogOpen(); }}
                            size="small"
                            sx={{
                              bgcolor: 'error.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'error.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Menu supprimé */}

      {/* Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleDetailsDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Détails du vendeur
          <IconButton
            aria-label="close"
            onClick={handleDetailsDialogClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedVendeur && (
            <Box sx={{ p: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    width: 64,
                    height: 64,
                    bgcolor: theme.palette.primary.main,
                    mr: 2
                  }}
                >
                  {getInitials(selectedVendeur.nom)}
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">{selectedVendeur.nom}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Ajouté le {selectedVendeur.dateCreation}
                  </Typography>
                </Box>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body1">{selectedVendeur.email}</Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Téléphone</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body1">{selectedVendeur.telephone}</Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Mot de passe</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      <Typography variant="body1" sx={{ fontFamily: 'monospace', bgcolor: '#f5f5f5', p: 0.5, borderRadius: 1 }}>
                        {selectedVendeur.motDePasse || 'Non disponible'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Factures créées</Typography>
                    <Typography variant="body1">{selectedVendeur.factures}</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Devis créés</Typography>
                    <Typography variant="body1">{selectedVendeur.devis}</Typography>
                  </Box>
                </Grid>

              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDetailsDialogClose}>Fermer</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              handleDetailsDialogClose();
              if (selectedVendeur) {
                navigate(`/admin/utilisateurs/edit/${selectedVendeur._id}`);
              }
            }}
          >
            Modifier
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer le vendeur "{selectedVendeur?.nom}" ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>Annuler</Button>
          <Button onClick={handleDeleteVendeur} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VendeursPage;
