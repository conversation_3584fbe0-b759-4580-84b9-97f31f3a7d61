import React from 'react';
import { Box } from '@mui/material';
import VendeurSidebar from '../components/VendeurSidebar';
import VendeurTopBar from '../components/VendeurTopBar';
import { useAuth } from '../contexts/AuthContext';

const VendeurLayout = ({ children, darkMode, toggleDarkMode }) => {
  const { currentUser } = useAuth();

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: (theme) => theme.palette.background.default
    }}>
      <VendeurSidebar />
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <VendeurTopBar
          title="Dashboard Vendeur"
          darkMode={darkMode}
          toggleDarkMode={toggleDarkMode}
          user={currentUser}
        />
        <Box sx={{
          flexGrow: 1,
          p: 3,
          overflow: 'auto',
          backgroundColor: (theme) => theme.palette.background.default
        }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default VendeurLayout;
