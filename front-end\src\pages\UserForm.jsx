import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Divider,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Switch,
  FormControlLabel,
  Avatar,
  IconButton,
  Snackbar,
  Alert,
  useTheme,
  Paper,
  Tabs,
  Tab
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  PhotoCamera as PhotoCameraIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getUserById, createUser, updateUser, uploadProfileImage, getUsersByRole } from '../services/userService';

// Mock user data for editing
const mockUsers = {
  1: {
    id: 1,
    nom: '<PERSON>',
    email: '<EMAIL>',
    role: 'VENDEUR',
    dateCreation: '2023-05-15',
    telephone: '+33 6 12 34 56 78',
    status: 'active',
    avatar: null,
    adresse: '123 Rue de Paris, 75001 Paris',
    contact: 'Jean Dupont'
  },
  2: {
    id: 2,
    nom: 'Marie Martin',
    email: '<EMAIL>',
    role: 'VENDEUR',
    dateCreation: '2023-05-28',
    telephone: '+33 6 23 45 67 89',
    status: 'active',
    avatar: null,
    adresse: '456 Avenue des Champs-Élysées, 75008 Paris',
    contact: 'Marie Martin'
  },
  3: {
    id: 3,
    nom: 'Entreprise ABC',
    email: '<EMAIL>',
    role: 'ENTREPRISE',
    dateCreation: '2023-06-10',
    telephone: '+33 1 23 45 67 89',
    status: 'active',
    avatar: null,
    adresse: '789 Boulevard Haussmann, 75009 Paris',
    contact: 'Pierre Durand',
    nomEntreprise: 'Entreprise ABC',
    adresseEntreprise: '789 Boulevard Haussmann, 75009 Paris',
    telephoneEntreprise: '+33 1 23 45 67 89',
    numeroFiscal: 'FR12345678900'
  },
  4: {
    id: 4,
    nom: 'Société XYZ',
    email: '<EMAIL>',
    role: 'ENTREPRISE',
    dateCreation: '2023-06-15',
    telephone: '+33 1 34 56 78 90',
    status: 'inactive',
    avatar: null,
    adresse: '321 Rue de Rivoli, 75004 Paris',
    contact: 'Sophie Lefebvre',
    nomEntreprise: 'Société XYZ',
    adresseEntreprise: '321 Rue de Rivoli, 75004 Paris',
    telephoneEntreprise: '+33 1 34 56 78 90',
    numeroFiscal: 'FR98765432100'
  },
};

const UserForm = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { currentUser } = useAuth();
  const isEditMode = !!id;

  // Get role from query params if creating a new user
  const queryParams = new URLSearchParams(location.search);
  const roleFromQuery = queryParams.get('role');

  // State
  const [formData, setFormData] = useState({
    nom: '',
    email: '',
    motDePasse: '',
    confirmMotDePasse: '',
    role: roleFromQuery || 'VENDEUR',
    telephone: '',
    adresse: '',
    contact: '',
    // Entreprise specific fields
    nomEntreprise: '',
    adresseEntreprise: '',
    telephoneEntreprise: '',
    numeroFiscal: '',
    // Relation fields
    entreprises: []
  });
  const [loading, setLoading] = useState(isEditMode);
  const [errors, setErrors] = useState({});
  const [avatar, setAvatar] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [entreprisesList, setEntreprisesList] = useState([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [tabValue, setTabValue] = useState(0);

  // Fetch user data if in edit mode
  useEffect(() => {
    if (isEditMode) {
      const fetchUser = async () => {
        try {
          const userData = await getUserById(id);
          if (userData) {
            setFormData({
              ...userData,
              motDePasse: '',
              confirmMotDePasse: ''
            });
            setAvatarPreview(userData.profileImage);
          }
          setLoading(false);
        } catch (error) {
          console.error('Error fetching user:', error);
          setLoading(false);
          setSnackbar({
            open: true,
            message: 'Erreur lors de la récupération des données utilisateur',
            severity: 'error'
          });
        }
      };

      fetchUser();
    }
  }, [id, isEditMode]);

  // Fetch responsables for dropdown
  useEffect(() => {
    const fetchResponsables = async () => {
      try {
        const responsables = await getUsersByRole('RESPONSABLE');
        setEntreprisesList(responsables);
      } catch (error) {
        console.error('Error fetching responsables:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors de la récupération des responsables d\'entreprise',
          severity: 'error'
        });
      }
    };

    // Only fetch responsables if creating or editing a vendeur
    if (formData.role === 'VENDEUR') {
      fetchResponsables();
    }
  }, [formData.role]);

  // Handle form input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };



  // Handle avatar change
  const handleAvatarChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatar(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle avatar remove
  const handleAvatarRemove = () => {
    setAvatar(null);
    setAvatarPreview(null);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.nom) newErrors.nom = 'Le nom est requis';
    if (!formData.email) newErrors.email = 'L\'email est requis';

    // Email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    // Password (only required for new users)
    if (!isEditMode) {
      if (!formData.motDePasse) newErrors.motDePasse = 'Le mot de passe est requis';
      if (formData.motDePasse && formData.motDePasse.length < 8) {
        newErrors.motDePasse = 'Le mot de passe doit contenir au moins 8 caractères';
      }
      if (!formData.confirmMotDePasse) newErrors.confirmMotDePasse = 'La confirmation du mot de passe est requise';
      if (formData.motDePasse !== formData.confirmMotDePasse) {
        newErrors.confirmMotDePasse = 'Les mots de passe ne correspondent pas';
      }
    } else if (formData.motDePasse) {
      // If password is provided in edit mode, validate it
      if (formData.motDePasse.length < 8) {
        newErrors.motDePasse = 'Le mot de passe doit contenir au moins 8 caractères';
      }
      if (!formData.confirmMotDePasse) {
        newErrors.confirmMotDePasse = 'La confirmation du mot de passe est requise';
      }
      if (formData.motDePasse !== formData.confirmMotDePasse) {
        newErrors.confirmMotDePasse = 'Les mots de passe ne correspondent pas';
      }
    }

    // Responsable d'entreprise specific validations
    if (formData.role === 'RESPONSABLE') {
      if (!formData.nomEntreprise) newErrors.nomEntreprise = 'Le nom de l\'entreprise est requis';
      if (!formData.adresseEntreprise) newErrors.adresseEntreprise = 'L\'adresse de l\'entreprise est requise';
      if (!formData.telephoneEntreprise) newErrors.telephoneEntreprise = 'Le téléphone de l\'entreprise est requis';
      if (!formData.numeroFiscal) newErrors.numeroFiscal = 'Le numéro fiscal est requis';
    }

    // Vendeur specific validations
    if (formData.role === 'VENDEUR') {
      // Make entreprise association required
      if (!formData.entreprises || formData.entreprises.length === 0) {
        console.log('No entreprise selected for vendeur');
        newErrors.entreprises = 'Veuillez sélectionner une entreprise';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setSnackbar({
        open: true,
        message: 'Veuillez corriger les erreurs dans le formulaire',
        severity: 'error'
      });
      return;
    }

    try {
      setLoading(true);

      // Prepare data for submission
      const userData = { ...formData };

      // Remove confirm password field
      delete userData.confirmMotDePasse;

      // If password is empty in edit mode, remove it
      if (isEditMode && !userData.motDePasse) {
        delete userData.motDePasse;
      }

      // For vendeurs, ensure the responsables field is set to the selected entreprise
      if (userData.role === 'VENDEUR' && userData.entreprises && userData.entreprises.length > 0) {
        // Set the responsables field to the selected entreprise ID
        userData.responsables = userData.entreprises;
        console.log('Setting responsables field for vendeur:', userData.responsables);
      }

      // Save user data
      let response;
      if (isEditMode) {
        response = await updateUser(id, userData);
      } else {
        response = await createUser(userData);
      }

      // If there's an avatar, upload it
      if (avatar) {
        const formData = new FormData();
        formData.append('avatar', avatar);
        await uploadProfileImage(isEditMode ? id : response._id, formData);
      }

      setSnackbar({
        open: true,
        message: isEditMode
          ? `L'utilisateur ${formData.nom} a été mis à jour avec succès`
          : `L'utilisateur ${formData.nom} a été créé avec succès`,
        severity: 'success'
      });

      // Redirect after a short delay
      setTimeout(() => {
        navigate(formData.role === 'RESPONSABLE' ? '/admin/utilisateurs/entreprises' : '/admin/utilisateurs/vendeurs');
      }, 2000);
    } catch (error) {
      console.error('Error saving user:', error);
      setSnackbar({
        open: true,
        message: isEditMode
          ? `Erreur lors de la mise à jour de l'utilisateur: ${error.message}`
          : `Erreur lors de la création de l'utilisateur: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Get page title
  const getPageTitle = () => {
    if (isEditMode) {
      return `Modifier ${formData.role === 'RESPONSABLE' ? 'le responsable d\'entreprise' : 'le vendeur'} ${formData.nom}`;
    }

    return formData.role === 'RESPONSABLE' ? 'Ajouter un responsable d\'entreprise' : 'Ajouter un vendeur';
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            {getPageTitle()}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {isEditMode
              ? 'Modifiez les informations de l\'utilisateur ci-dessous.'
              : 'Remplissez le formulaire ci-dessous pour créer un nouvel utilisateur.'}
          </Typography>
        </Box>

        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(-1)}
          sx={{ borderRadius: 2 }}
        >
          Retour
        </Button>
      </Box>

      {/* Form */}
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} md={8}>
            <Card
              component={motion.div}
              variants={itemVariants}
              elevation={1}
              sx={{
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                }
              }}
            >
              <CardHeader
                title="Informations de l'utilisateur"
                titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
              />
              <Divider />
              <CardContent>
                <Box sx={{ mb: 2 }}>
                  <Tabs
                    value={tabValue}
                    onChange={handleTabChange}
                    indicatorColor="primary"
                    textColor="primary"
                  >
                    <Tab
                      label={formData.role === 'RESPONSABLE' ? "Informations du responsable" : "Informations générales"}
                      icon={<PersonIcon />}
                      iconPosition="start"
                      sx={{ minHeight: 48, py: 1 }}
                    />
                    {formData.role === 'RESPONSABLE' && (
                      <Tab
                        label="Informations entreprise"
                        icon={<BusinessIcon />}
                        iconPosition="start"
                        sx={{ minHeight: 48, py: 1 }}
                      />
                    )}
                  </Tabs>
                </Box>

                {/* General Information Tab */}
                {tabValue === 0 && (
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Nom"
                        name="nom"
                        value={formData.nom}
                        onChange={handleChange}
                        fullWidth
                        required
                        error={!!errors.nom}
                        helperText={errors.nom}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        fullWidth
                        required
                        error={!!errors.email}
                        helperText={errors.email}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Mot de passe"
                        name="motDePasse"
                        type="password"
                        value={formData.motDePasse}
                        onChange={handleChange}
                        fullWidth
                        required={!isEditMode}
                        error={!!errors.motDePasse}
                        helperText={errors.motDePasse || (isEditMode ? 'Laissez vide pour conserver le mot de passe actuel' : '')}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Confirmer le mot de passe"
                        name="confirmMotDePasse"
                        type="password"
                        value={formData.confirmMotDePasse}
                        onChange={handleChange}
                        fullWidth
                        required={!isEditMode || !!formData.motDePasse}
                        error={!!errors.confirmMotDePasse}
                        helperText={errors.confirmMotDePasse}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth required error={!!errors.role} disabled={loading || isEditMode}>
                        <InputLabel>Type de compte</InputLabel>
                        <Select
                          name="role"
                          value={formData.role}
                          onChange={handleChange}
                          label="Type de compte"
                        >
                          <MenuItem value="VENDEUR">Vendeur</MenuItem>
                          <MenuItem value="RESPONSABLE">Responsable d'entreprise</MenuItem>
                        </Select>
                        {errors.role && <FormHelperText>{errors.role}</FormHelperText>}
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Téléphone"
                        name="telephone"
                        value={formData.telephone}
                        onChange={handleChange}
                        fullWidth
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        label="Adresse"
                        name="adresse"
                        value={formData.adresse}
                        onChange={handleChange}
                        fullWidth
                        multiline
                        rows={2}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        label="Contact"
                        name="contact"
                        value={formData.contact}
                        onChange={handleChange}
                        fullWidth
                        disabled={loading}
                      />
                    </Grid>

                    {formData.role === 'VENDEUR' && (
                      <Grid item xs={12}>
                        <FormControl fullWidth error={!!errors.entreprises} disabled={loading}>
                          <InputLabel>Entreprise associée</InputLabel>
                          <Select
                            name="entreprises"
                            value={formData.entreprises && formData.entreprises.length > 0 ? formData.entreprises[0] : ''}
                            onChange={(e) => {
                              const selectedEntrepriseId = e.target.value;
                              setFormData(prev => ({
                                ...prev,
                                entreprises: selectedEntrepriseId ? [selectedEntrepriseId] : []
                              }));

                              // Clear error for this field
                              if (errors.entreprises) {
                                setErrors(prev => ({
                                  ...prev,
                                  entreprises: null
                                }));
                              }
                            }}
                            label="Entreprise associée"
                          >
                            <MenuItem value="">
                              <em>Aucune</em>
                            </MenuItem>
                            {entreprisesList.map((entreprise) => (
                              <MenuItem key={entreprise._id} value={entreprise._id}>
                                {entreprise.nomEntreprise || entreprise.nom}
                              </MenuItem>
                            ))}
                          </Select>
                          {errors.entreprises && <FormHelperText>{errors.entreprises}</FormHelperText>}
                        </FormControl>
                      </Grid>
                    )}
                  </Grid>
                )}

                {/* Enterprise Information Tab */}
                {tabValue === 1 && formData.role === 'RESPONSABLE' && (
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        label="Nom de l'entreprise"
                        name="nomEntreprise"
                        value={formData.nomEntreprise}
                        onChange={handleChange}
                        fullWidth
                        required
                        error={!!errors.nomEntreprise}
                        helperText={errors.nomEntreprise}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        label="Adresse de l'entreprise"
                        name="adresseEntreprise"
                        value={formData.adresseEntreprise}
                        onChange={handleChange}
                        fullWidth
                        multiline
                        rows={2}
                        required
                        error={!!errors.adresseEntreprise}
                        helperText={errors.adresseEntreprise}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Téléphone de l'entreprise"
                        name="telephoneEntreprise"
                        value={formData.telephoneEntreprise}
                        onChange={handleChange}
                        fullWidth
                        required
                        error={!!errors.telephoneEntreprise}
                        helperText={errors.telephoneEntreprise}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Numéro fiscal"
                        name="numeroFiscal"
                        value={formData.numeroFiscal}
                        onChange={handleChange}
                        fullWidth
                        required
                        error={!!errors.numeroFiscal}
                        helperText={errors.numeroFiscal}
                        disabled={loading}
                      />
                    </Grid>
                  </Grid>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={4}>
            {/* Profile Picture Card */}
            <Card
              component={motion.div}
              variants={itemVariants}
              elevation={1}
              sx={{
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                },
                mb: 3
              }}
            >
              <CardHeader
                title="Photo de profil"
                titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
              />
              <Divider />
              <CardContent sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Box sx={{ position: 'relative', mb: 2 }}>
                  <Avatar
                    src={avatarPreview}
                    alt={formData.nom}
                    sx={{
                      width: 120,
                      height: 120,
                      bgcolor: formData.role === 'RESPONSABLE' ? theme.palette.secondary.main : theme.palette.primary.main
                    }}
                  >
                    {!avatarPreview && (formData.role === 'RESPONSABLE' ? <BusinessIcon fontSize="large" /> : <PersonIcon fontSize="large" />)}
                  </Avatar>
                  <Box sx={{ position: 'absolute', bottom: 0, right: 0 }}>
                    <input
                      accept="image/*"
                      style={{ display: 'none' }}
                      id="avatar-upload"
                      type="file"
                      onChange={handleAvatarChange}
                      disabled={loading}
                    />
                    <label htmlFor="avatar-upload">
                      <IconButton
                        component="span"
                        sx={{
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          '&:hover': {
                            bgcolor: theme.palette.primary.dark
                          }
                        }}
                        disabled={loading}
                      >
                        <PhotoCameraIcon fontSize="small" />
                      </IconButton>
                    </label>
                  </Box>
                </Box>

                {avatarPreview && (
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    startIcon={<DeleteIcon />}
                    onClick={handleAvatarRemove}
                    disabled={loading}
                    sx={{ mb: 2 }}
                  >
                    Supprimer
                  </Button>
                )}

                <Typography variant="body2" color="text.secondary" align="center">
                  Téléchargez une photo de profil pour cet utilisateur. Formats acceptés: JPG, PNG.
                </Typography>
              </CardContent>
            </Card>



            {/* Actions Card */}
            <Card
              component={motion.div}
              variants={itemVariants}
              elevation={1}
              sx={{
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                }
              }}
            >
              <CardHeader
                title="Actions"
                titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
              />
              <Divider />
              <CardContent>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  fullWidth
                  type="submit"
                  disabled={loading}
                  sx={{ mb: 2 }}
                >
                  {isEditMode ? 'Enregistrer les modifications' : 'Créer l\'utilisateur'}
                </Button>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => navigate(-1)}
                  disabled={loading}
                >
                  Annuler
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </form>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserForm;
