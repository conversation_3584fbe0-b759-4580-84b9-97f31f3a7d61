import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  useTheme,
  Divider,
  Snackbar,
  Alert,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import {
  Save as SaveIcon,
  Check as CheckIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import ColorPicker from '../components/ColorPicker';
import { updateTemplateSettings, getTemplateSettings } from '../services/templateService';

const TemplateSettings = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [templateSettings, setTemplateSettings] = useState({
    color: '#e91e63', // Default pink color from the image
    logo: null,
    logoPreview: null,
    type: 'facture' // Default type
  });

  // Color palette from the image
  const colorPalettes = [
    // Row 1
    ['#e67e22', '#f39c12', '#d81b60', '#8e24aa', '#3f51b5', '#00bcd4'],
    // Row 2
    ['#f1c40f', '#ff9800', '#e91e63', '#9c27b0', '#5677fc', '#009688'],
  ];

  // Fetch template settings when tab changes
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const type = tabValue === 0 ? 'facture' : 'devis';
        const settings = await getTemplateSettings(type);
        setTemplateSettings({
          color: settings.color || '#e91e63',
          logo: settings.logo || null,
          logoPreview: settings.logo ? `/uploads/${settings.logo.split('/').pop()}` : null,
          type: type
        });
      } catch (err) {
        console.error('Error fetching template settings:', err);
        setError('Erreur lors du chargement des paramètres de template');
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [tabValue]);

  // Handle color selection
  const handleColorSelect = (color) => {
    setTemplateSettings({
      ...templateSettings,
      color: color
    });
  };

  // Handle logo upload
  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setTemplateSettings({
          ...templateSettings,
          logo: file,
          logoPreview: reader.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    // Les paramètres seront chargés par l'effet useEffect qui dépend de tabValue
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      setError(null);

      const formData = new FormData();
      formData.append('color', templateSettings.color);

      // Utiliser le type en fonction de l'onglet actif
      const type = tabValue === 0 ? 'facture' : 'devis';
      formData.append('type', type);

      if (templateSettings.logo && typeof templateSettings.logo !== 'string') {
        formData.append('logo', templateSettings.logo);
      }

      await updateTemplateSettings(formData);
      setSuccess(true);

      // Recharger les paramètres pour voir les changements
      const settings = await getTemplateSettings(type);
      setTemplateSettings({
        color: settings.color || '#e91e63',
        logo: settings.logo || null,
        logoPreview: settings.logo ? `/uploads/${settings.logo.split('/').pop()}` : null,
        type: type
      });
    } catch (err) {
      console.error('Error saving template settings:', err);
      setError('Erreur lors de l\'enregistrement des paramètres de template');
    } finally {
      setSaving(false);
    }
  };

  // Handle close snackbar
  const handleCloseSnackbar = () => {
    setSuccess(false);
    setError(null);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, md: 3 } }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Templates
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
          disabled={saving}
        >
          {saving ? 'Enregistrement...' : 'Enregistrer'}
        </Button>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Factures" />
          <Tab label="Devis" />
        </Tabs>
      </Paper>

      <Grid container spacing={3}>
        <Grid item xs={12} md={5}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Couleur
            </Typography>
            <Box sx={{ mb: 3 }}>
              {colorPalettes.map((row, rowIndex) => (
                <Box
                  key={rowIndex}
                  sx={{
                    display: 'flex',
                    gap: 1,
                    mb: 1
                  }}
                >
                  {row.map((color) => (
                    <Box
                      key={color}
                      onClick={() => handleColorSelect(color)}
                      sx={{
                        width: 40,
                        height: 40,
                        bgcolor: color,
                        borderRadius: 1,
                        cursor: 'pointer',
                        border: templateSettings.color === color ? '3px solid #000' : 'none',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        '&:hover': {
                          boxShadow: '0 0 0 3px rgba(0,0,0,0.2)'
                        }
                      }}
                    >
                      {templateSettings.color === color && (
                        <CheckIcon sx={{ color: '#fff' }} />
                      )}
                    </Box>
                  ))}
                </Box>
              ))}
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Couleur personnalisée
              </Typography>
              <ColorPicker
                value={templateSettings.color}
                onChange={(color) => handleColorSelect(color)}
              />
            </Box>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Logo
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 2 }}>
              {templateSettings.logoPreview && (
                <Box
                  sx={{
                    width: '100%',
                    height: '120px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    mb: 2,
                    border: '1px solid #eee',
                    borderRadius: 1,
                    p: 2
                  }}
                >
                  <img
                    src={templateSettings.logoPreview}
                    alt="Logo"
                    style={{ maxWidth: '100%', maxHeight: '100%' }}
                  />
                </Box>
              )}
              <Button
                variant="outlined"
                component="label"
                startIcon={<CloudUploadIcon />}
              >
                {templateSettings.logoPreview ? "Changer le logo" : "Télécharger un logo"}
                <input
                  type="file"
                  hidden
                  accept="image/png,image/jpeg,image/svg+xml"
                  onChange={handleLogoChange}
                />
              </Button>
              <Typography variant="caption" sx={{ mt: 1 }}>
                Format recommandé: PNG, JPG ou SVG (max 2MB)
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={7}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Aperçu
            </Typography>
            <Box
              sx={{
                border: '1px solid #ddd',
                borderRadius: 1,
                p: 2,
                height: '600px',
                overflow: 'auto',
                bgcolor: '#fff'
              }}
            >
              {/* Header with logo and title */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                <Box sx={{ width: '120px', height: '80px' }}>
                  {templateSettings.logoPreview && (
                    <img
                      src={templateSettings.logoPreview}
                      alt="Logo"
                      style={{ maxWidth: '100%', maxHeight: '100%' }}
                    />
                  )}
                </Box>
                <Box sx={{
                  bgcolor: templateSettings.color,
                  color: '#fff',
                  p: 2,
                  borderRadius: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-end',
                  justifyContent: 'center'
                }}>
                  <Typography variant="h6">
                    {tabValue === 0 ? 'FACTURE' : 'DEVIS'}
                  </Typography>
                  <Typography variant="body2">
                    N° {tabValue === 0 ? 'FACT-0001' : 'DEV-0001'}
                  </Typography>
                </Box>
              </Box>

              {/* Company and client info */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">ÉMETTEUR</Typography>
                  <Typography variant="body1">Votre Entreprise</Typography>
                  <Typography variant="body2">Adresse de l'entreprise</Typography>
                  <Typography variant="body2"><EMAIL></Typography>
                  <Typography variant="body2">Téléphone</Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" color="textSecondary">DESTINATAIRE</Typography>
                  <Typography variant="body1">Nom du client</Typography>
                  <Typography variant="body2">Adresse du client</Typography>
                  <Typography variant="body2">Ville, Pays</Typography>
                </Grid>
              </Grid>

              {/* Dates */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2">Date d'émission: 01/01/2023</Typography>
                {tabValue === 0 && (
                  <Typography variant="body2">Date d'échéance: 01/02/2023</Typography>
                )}
              </Box>

              {/* Table header */}
              <Box sx={{
                display: 'flex',
                bgcolor: templateSettings.color,
                color: '#fff',
                p: 1,
                borderRadius: '4px 4px 0 0'
              }}>
                <Box sx={{ flex: 1 }}>Description</Box>
                <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>Quantité</Box>
                <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>Total</Box>
              </Box>

              {/* Table rows - simplified */}
              <Box sx={{
                display: 'flex',
                p: 1,
                borderBottom: '1px solid #eee'
              }}>
                <Box sx={{ flex: 1 }}>Produit ou service</Box>
                <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>1</Box>
                <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>100,00 €</Box>
              </Box>

              <Box sx={{
                display: 'flex',
                p: 1,
                borderBottom: '1px solid #eee',
                bgcolor: '#f9f9f9'
              }}>
                <Box sx={{ flex: 1 }}>Produit ou service</Box>
                <Box sx={{ flex: '0 0 80px', textAlign: 'center' }}>1</Box>
                <Box sx={{ flex: '0 0 100px', textAlign: 'right' }}>100,00 €</Box>
              </Box>

              {/* Totals - simplified */}
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mt: 3 }}>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  width: '250px',
                  p: 1,
                  bgcolor: templateSettings.color,
                  color: '#fff',
                  borderRadius: 1
                }}>
                  <Typography variant="body1">Total</Typography>
                  <Typography variant="body1">200,00 €</Typography>
                </Box>
              </Box>

              {/* Footer */}
              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                  Merci pour votre confiance
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Success/Error messages */}
      <Snackbar open={success} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Paramètres de template enregistrés avec succès
        </Alert>
      </Snackbar>

      <Snackbar open={!!error} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TemplateSettings;
