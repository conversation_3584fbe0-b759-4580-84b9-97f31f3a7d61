const express = require('express');
const router = express.Router();
const Produit = require('../models/ProduitModel');
const Facture = require('../models/FactureModel');
const StockHistory = require('../models/StockHistoryModel');
const { adjustProductStock, getProductStockHistory } = require('../utils/stockUtils');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../uploads/products');
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'product-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error("Seules les images sont autorisées"));
    }
});

// Create a product with image upload
router.post('/prods', upload.single('image'), async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const productData = req.body;

        // Add image URL if an image was uploaded
        if (req.file) {
            productData.imageUrl = `/uploads/products/${req.file.filename}`;
        }

        // Handle variants and volume pricing if provided
        if (productData.variants && typeof productData.variants === 'string') {
            productData.variants = JSON.parse(productData.variants);
        }

        if (productData.prixVolume && typeof productData.prixVolume === 'string') {
            productData.prixVolume = JSON.parse(productData.prixVolume);
        }

        const newProduct = new Produit(productData);
        await newProduct.save();
        res.status(200).json(newProduct);
    } catch (error) {
        console.error('Error adding product:', error.message);
        res.status(500).json({ error: 'Erreur lors de l\'ajout du produit', details: error.message });
    }
});

// Get all products with filtering options
router.get('/prods', async (req, res) => {
    try {
        const { category, search, minPrice, maxPrice, inStock, sort } = req.query;
        let query = {};

        // Apply filters
        if (category && category !== 'Tous') {
            query.category = category;
        }

        if (search) {
            query.$or = [
                { nom: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { reference: { $regex: search, $options: 'i' } }
            ];
        }

        if (minPrice) {
            query.prix = { ...query.prix, $gte: parseFloat(minPrice) };
        }

        if (maxPrice) {
            query.prix = { ...query.prix, $lte: parseFloat(maxPrice) };
        }

        if (inStock === 'true') {
            query.gestionStock = true;
            query.quantiteStock = { $gt: 0 };
        }

        // Build sort options
        let sortOptions = {};
        if (sort) {
            switch (sort) {
                case 'price_asc':
                    sortOptions.prix = 1;
                    break;
                case 'price_desc':
                    sortOptions.prix = -1;
                    break;
                case 'name_asc':
                    sortOptions.nom = 1;
                    break;
                case 'name_desc':
                    sortOptions.nom = -1;
                    break;
                case 'popular':
                    sortOptions['statistiques.nombreVentes'] = -1;
                    break;
                default:
                    sortOptions.dateCreation = -1;
            }
        } else {
            sortOptions.dateCreation = -1; // Default sort by newest
        }

        const produits = await Produit.find(query).sort(sortOptions);
        res.status(200).json(produits);
    } catch (error) {
        console.error('Error fetching products:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des produits' });
    }
});

// Get a single product by ID
router.get('/prods/:id', async (req, res) => {
    try {
        const produit = await Produit.findById(req.params.id);
        if (!produit) return res.status(404).json({ error: 'Produit non trouvé' });
        res.status(200).json(produit);
    } catch (error) {
        console.error('Error fetching product:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération du produit' });
    }
});

// Update a product
router.put('/prods/:id', upload.single('image'), async (req, res) => {
    try {
        const productData = req.body;

        // Add image URL if an image was uploaded
        if (req.file) {
            productData.imageUrl = `/uploads/products/${req.file.filename}`;

            // Delete old image if exists
            const oldProduct = await Produit.findById(req.params.id);
            if (oldProduct && oldProduct.imageUrl) {
                const oldImagePath = path.join(__dirname, '..', oldProduct.imageUrl);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                }
            }
        }

        // Handle variants and volume pricing if provided
        if (productData.variants && typeof productData.variants === 'string') {
            productData.variants = JSON.parse(productData.variants);
        }

        if (productData.prixVolume && typeof productData.prixVolume === 'string') {
            productData.prixVolume = JSON.parse(productData.prixVolume);
        }

        const produit = await Produit.findByIdAndUpdate(
            req.params.id,
            { ...productData, dateMiseAJour: Date.now() },
            { new: true }
        );

        if (!produit) return res.status(404).json({ error: 'Produit non trouvé' });
        res.status(200).json(produit);
    } catch (error) {
        console.error('Error updating product:', error.message);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du produit' });
    }
});

// Delete a product
router.delete('/prods/:id', async (req, res) => {
    try {
        const produit = await Produit.findById(req.params.id);
        if (!produit) return res.status(404).json({ error: 'Produit non trouvé' });

        // Delete associated image if exists
        if (produit.imageUrl) {
            const imagePath = path.join(__dirname, '..', produit.imageUrl);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        await Produit.findByIdAndDelete(req.params.id);
        res.status(204).send();
    } catch (error) {
        console.error('Error deleting product:', error.message);
        res.status(500).json({ error: 'Erreur lors de la suppression du produit' });
    }
});

// Get product categories
router.get('/categories', async (req, res) => {
    try {
        const categories = await Produit.distinct('category');
        res.status(200).json(categories);
    } catch (error) {
        console.error('Error fetching categories:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des catégories' });
    }
});

// Get product statistics
router.get('/stats', async (req, res) => {
    try {
        // Get top selling products
        const topProducts = await Produit.find()
            .sort({ 'statistiques.nombreVentes': -1 })
            .limit(5);

        // Get most profitable products
        const profitableProducts = await Produit.find()
            .sort({ 'statistiques.margeProfit': -1 })
            .limit(5);

        // Get products with low stock
        const lowStockProducts = await Produit.find({
            gestionStock: true,
            quantiteStock: { $lte: 5 } // Using a fixed value for now, should use seuilAlerte in production
        }).limit(5);

        // Get total products count by category
        const categoryCounts = await Produit.aggregate([
            { $group: { _id: '$category', count: { $sum: 1 } } }
        ]);

        // Format data for frontend
        const formattedData = {
            topProducts,
            profitableProducts,
            lowStockProducts,
            categoryCounts,
            // Add formatted data for dashboard
            productCategories: {
                products: categoryCounts.find(c => c._id === 'Produits')?.count || 0,
                services: categoryCounts.find(c => c._id === 'Services')?.count || 0,
                others: categoryCounts.filter(c => c._id !== 'Produits' && c._id !== 'Services')
                    .reduce((sum, cat) => sum + cat.count, 0)
            },
            products: {
                topSelling: topProducts.length > 0 ? {
                    id: topProducts[0]._id,
                    name: topProducts[0].nom,
                    sales: topProducts[0].statistiques?.nombreVentes || 35,
                    category: topProducts[0].category || 'Produits'
                } : null,
                mostProfitable: profitableProducts.length > 0 ? {
                    id: profitableProducts[0]._id,
                    name: profitableProducts[0].nom,
                    margin: profitableProducts[0].statistiques?.margeProfit || 52,
                    category: profitableProducts[0].category || 'Produits'
                } : null,
                lowStock: lowStockProducts.map(product => ({
                    id: product._id,
                    name: product.nom,
                    stock: product.quantiteStock
                }))
            }
        };

        res.status(200).json(formattedData);
    } catch (error) {
        console.error('Error fetching product statistics:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
    }
});

// Get bundle suggestions for a product
router.get('/prods/:id/bundles', async (req, res) => {
    try {
        const productId = req.params.id;

        // Find invoices containing this product
        const invoicesWithProduct = await Facture.find({
            'lignes.produit': productId
        });

        // Extract all product IDs from these invoices
        const relatedProductIds = new Set();
        invoicesWithProduct.forEach(invoice => {
            invoice.lignes.forEach(line => {
                if (line.produit && line.produit.toString() !== productId) {
                    relatedProductIds.add(line.produit.toString());
                }
            });
        });

        // Count occurrences of each product
        const productCounts = {};
        relatedProductIds.forEach(id => {
            productCounts[id] = (productCounts[id] || 0) + 1;
        });

        // Sort by frequency and get top 5
        const sortedProductIds = Object.keys(productCounts).sort((a, b) => {
            return productCounts[b] - productCounts[a];
        }).slice(0, 5);

        // Fetch product details
        const bundleSuggestions = await Produit.find({
            _id: { $in: sortedProductIds }
        });

        res.status(200).json(bundleSuggestions);
    } catch (error) {
        console.error('Error fetching bundle suggestions:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des suggestions de bundles' });
    }
});

// Update product stock with history tracking
router.patch('/prods/:id/stock', async (req, res) => {
    try {
        const { quantiteStock, motif } = req.body;

        if (quantiteStock === undefined) {
            return res.status(400).json({ error: 'La quantité en stock est requise' });
        }

        // Récupérer l'ID de l'utilisateur si disponible
        const userId = req.user ? req.user._id : null;

        // Utiliser la fonction d'ajustement de stock qui gère l'historique
        const result = await adjustProductStock(
            req.params.id,
            quantiteStock,
            motif || 'Ajustement manuel via l\'interface',
            userId
        );

        if (!result.success) {
            return res.status(400).json({ error: result.message });
        }

        res.status(200).json(result);
    } catch (error) {
        console.error('Error updating product stock:', error.message);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du stock' });
    }
});

// Get stock history for a product
router.get('/prods/:id/stock-history', async (req, res) => {
    try {
        const productId = req.params.id;

        // Vérifier si le produit existe
        const produit = await Produit.findById(productId);
        if (!produit) {
            return res.status(404).json({ error: 'Produit non trouvé' });
        }

        // Options de filtrage depuis les query params
        const options = {
            limit: req.query.limit ? parseInt(req.query.limit) : 20,
            skip: req.query.skip ? parseInt(req.query.skip) : 0,
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            typeOperation: req.query.typeOperation
        };

        // Récupérer l'historique des stocks
        const history = await getProductStockHistory(productId, options);

        res.status(200).json(history);
    } catch (error) {
        console.error('Error fetching stock history:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération de l\'historique des stocks' });
    }
});

// Export products to CSV
router.get('/export', async (req, res) => {
    try {
        const produits = await Produit.find();

        // Create CSV header
        let csv = 'ID,Nom,Description,Prix,Catégorie,Référence,Stock,Date de création\n';

        // Add product data
        produits.forEach(produit => {
            csv += `${produit._id},${produit.nom.replace(/,/g, ';')},"${produit.description ? produit.description.replace(/"/g, '""') : ''}",${produit.prix},${produit.category || 'Non classé'},${produit.reference || ''},${produit.gestionStock ? produit.quantiteStock : 'N/A'},${new Date(produit.dateCreation).toLocaleDateString()}\n`;
        });

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=produits.csv');
        res.status(200).send(csv);
    } catch (error) {
        console.error('Error exporting products:', error.message);
        res.status(500).json({ error: 'Erreur lors de l\'exportation des produits' });
    }
});

module.exports = router;