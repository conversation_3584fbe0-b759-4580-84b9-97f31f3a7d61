const mongoose = require('mongoose');
const { Schema } = mongoose;

const demandeRenouvellementSchema = new Schema({
  // Relations
  abonnementId: {
    type: Schema.Types.ObjectId,
    ref: 'Abonnement',
    required: true
  },
  responsableId: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    required: true
  },

  // Détails de la demande
  dureeChoisie: {
    type: String,
    enum: ['10_MIN', '1_JOUR', '7_JOURS', '3_MOIS', '6_MOIS', '1_AN'],
    required: true
  },

  // Statut
  statut: {
    type: String,
    enum: ['EN_ATTENTE', 'APPROUVEE', 'REJETEE'],
    default: 'EN_ATTENTE'
  },

  // Dates
  dateCreation: {
    type: Date,
    default: Date.now
  },
  dateTraitement: {
    type: Date
  },

  // Commentaires
  commentaires: {
    type: String
  },

  // Traitement
  traitePar: {
    type: Schema.Types.ObjectId,
    ref: 'users'
  },

  // Flag pour indiquer si la demande a été vue par l'admin
  vuParAdmin: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

module.exports = mongoose.model('DemandeRenouvellement', demandeRenouvellementSchema);
