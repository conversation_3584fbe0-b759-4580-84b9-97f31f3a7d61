{"version": 3, "sources": ["../../src/build/write-build-id.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport { BUILD_ID_FILE } from '../shared/lib/constants'\n\nexport async function writeBuildId(\n  distDir: string,\n  buildId: string\n): Promise<void> {\n  const buildIdPath = join(distDir, BUILD_ID_FILE)\n  await promises.writeFile(buildIdPath, buildId, 'utf8')\n}\n"], "names": ["promises", "join", "BUILD_ID_FILE", "writeBuildId", "distDir", "buildId", "buildIdPath", "writeFile"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,aAAa,QAAQ,0BAAyB;AAEvD,OAAO,eAAeC,aACpBC,OAAe,EACfC,OAAe;IAEf,MAAMC,cAAcL,KAAKG,SAASF;IAClC,MAAMF,SAASO,SAAS,CAACD,aAAaD,SAAS;AACjD"}