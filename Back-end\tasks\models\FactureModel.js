// backend/models/FactureModel.js
const mongoose = require('mongoose');
const { Schema } = mongoose;

const ligneSchema = new Schema({
  produit: { type: Schema.Types.ObjectId, ref: 'Produit', required: false },
  description: { type: String },
  quantite: { type: Number, required: true },
  prixUnitaire: { type: Number, required: true },
  montantHT: { type: Number, required: true },
  montantTTC: { type: Number, required: true },
  stockAjuste: { type: Boolean, default: false } // Indique si le stock a été ajusté pour cette ligne
});

const factureSchema = new Schema({
  // Informations de base
  numero: { type: String, required: true, unique: true },
  clientId: { type: Schema.Types.ObjectId, ref: 'Client', required: true },
  vendeurId: { type: Schema.Types.ObjectId, ref: 'users' }, // Référence au vendeur qui a créé la facture
  responsableId: { type: Schema.Types.ObjectId, ref: 'users' }, // Référence au responsable d'entreprise
  dateEmission: { type: Date, required: true },

  // Contenu de la facture
  lignes: [ligneSchema],
  notes: { type: String },

  // Statut et workflow
  statut: {
    type: String,
    enum: ['DRAFT', 'SENT', 'PAID', 'CANCELED', 'ACCEPTED', 'REJECTED', 'PARTIALLY_PAID'],
    default: 'DRAFT'
  },

  // Réponse client
  reponseClient: {
    date: Date,
    commentaires: String,
    motifRefus: String
  },

  // Paiement
  paiement: {
    datePaiement: Date,
    modePaiement: {
      type: String,
      enum: ['VIREMENT_BANCAIRE', 'CHEQUE', 'ESPECES']
    },
    reference: String,
    montantPaye: { type: Number, default: 0 }
  },

  // Suivi et conversion
  devisId: { type: Schema.Types.ObjectId, ref: 'Devis' }, // Référence au devis d'origine si converti
  dateAcceptation: Date,
  dateRefus: Date,

  // Montants
  total: { type: Number, required: true },
  totalHT: { type: Number },
  montantTVA: { type: Number },
  tauxTVA: { type: Number, required: true },

  // Métadonnées
  createdAt: { type: Date, default: Date.now },
  stockAjuste: { type: Boolean, default: false } // Indique si le stock a été ajusté pour toute la facture
}, {
  id: false,
  _id: true,
  timestamps: true
});

module.exports = mongoose.model('Facture', factureSchema);