import React, { useState, useEffect } from 'react';
import { useTemplateStore } from '../stores/templateStore';
import {
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  Chip,
  CircularProgress,
  Tabs,
  Tab,
  Box,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';

/**
 * Gestionnaire des templates de documents
 */
const TemplateManager = () => {
  const {
    templates,
    activeTab,
    loading,
    error,
    fetchTemplates,
    setActiveTab,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setDefaultTemplate
  } = useTemplateStore();

  // État local pour la gestion des formulaires et dialogues
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [confirmDeleteDialog, setConfirmDeleteDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    type: 'facture',
    layout: 'standard',
    isActive: true
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Charger les templates au démarrage
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // Filtrer les templates en fonction de l'onglet actif
  const filteredTemplates = React.useMemo(() => {
    if (activeTab === 'all') return templates;
    return templates.filter(t => t.type === activeTab);
  }, [templates, activeTab]);

  // Gérer le changement d'onglet
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Gérer les changements dans le formulaire
  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'isActive' || name === 'isDefault' ? checked : value
    }));
  };

  // Ouvrir le dialogue de création
  const handleOpenCreateDialog = () => {
    setFormData({
      name: '',
      type: 'facture',
      layout: 'standard',
      isActive: true
    });
    setOpenCreateDialog(true);
  };

  // Ouvrir le dialogue d'édition
  const handleOpenEditDialog = (template) => {
    setSelectedTemplate(template);
    setFormData({
      name: template.name,
      type: template.type,
      layout: template.layout || 'standard',
      isActive: template.isActive
    });
    setOpenEditDialog(true);
  };

  // Ouvrir le dialogue de confirmation de suppression
  const handleConfirmDelete = (template) => {
    setSelectedTemplate(template);
    setConfirmDeleteDialog(true);
  };

  // Créer un nouveau template
  const handleCreateTemplate = async () => {
    try {
      const success = await createTemplate(formData);

      if (success) {
        setSnackbar({
          open: true,
          message: 'Template créé avec succès!',
          severity: 'success'
        });
        setOpenCreateDialog(false);
      } else {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la création du template.',
          severity: 'error'
        });
      }
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur: ' + (err.message || 'Une erreur est survenue'),
        severity: 'error'
      });
    }
  };

  // Mettre à jour un template
  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const success = await updateTemplate(selectedTemplate.id || selectedTemplate._id, formData);

      if (success) {
        setSnackbar({
          open: true,
          message: 'Template mis à jour avec succès!',
          severity: 'success'
        });
        setOpenEditDialog(false);
      } else {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la mise à jour du template.',
          severity: 'error'
        });
      }
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur: ' + (err.message || 'Une erreur est survenue'),
        severity: 'error'
      });
    }
  };

  // Supprimer un template
  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const success = await deleteTemplate(selectedTemplate.id || selectedTemplate._id);

      if (success) {
        setSnackbar({
          open: true,
          message: 'Template supprimé avec succès!',
          severity: 'success'
        });
        setConfirmDeleteDialog(false);
      } else {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la suppression du template.',
          severity: 'error'
        });
      }
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur: ' + (err.message || 'Une erreur est survenue'),
        severity: 'error'
      });
    }
  };

  // Dupliquer un template
  const handleDuplicateTemplate = async (template) => {
    const newTemplate = {
      name: `${template.name} (copie)`,
      type: template.type,
      color: template.color,
      isActive: template.isActive,
      isDefault: false // Une copie n'est jamais par défaut
    };

    try {
      const success = await createTemplate(newTemplate);

      if (success) {
        setSnackbar({
          open: true,
          message: 'Template dupliqué avec succès!',
          severity: 'success'
        });
      } else {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la duplication du template.',
          severity: 'error'
        });
      }
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur: ' + (err.message || 'Une erreur est survenue'),
        severity: 'error'
      });
    }
  };

  // Définir un template comme par défaut
  const handleSetDefault = async (template) => {
    if (template.isDefault) return; // Déjà le template par défaut

    try {
      const success = await setDefaultTemplate(template.id || template._id, template.type);

      if (success) {
        setSnackbar({
          open: true,
          message: 'Template défini comme modèle par défaut!',
          severity: 'success'
        });
      } else {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la définition du template par défaut.',
          severity: 'error'
        });
      }
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Erreur: ' + (err.message || 'Une erreur est survenue'),
        severity: 'error'
      });
    }
  };

  // Fermer le snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <div>
      <Card>
        <CardHeader
          title="Gestion des templates"
          subheader="Personnalisez vos modèles de documents"
          action={
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateDialog}
            >
              Nouveau template
            </Button>
          }
        />
        <CardContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="template tabs"
            >
              <Tab label="Tous les modèles" value="all" />
              <Tab label="Factures" value="facture" />
              <Tab label="Devis" value="devis" />
            </Tabs>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {filteredTemplates.length === 0 ? (
                <Paper sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    Aucun template trouvé. Créez votre premier modèle de document!
                  </Typography>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleOpenCreateDialog}
                    sx={{ mt: 2 }}
                  >
                    Créer un template
                  </Button>
                </Paper>
              ) : (
                <Grid container spacing={3}>
                  {filteredTemplates.map((template) => (
                    <Grid item xs={12} sm={6} md={4} key={template.id || template._id}>
                      <Card
                        sx={{
                          position: 'relative',
                          borderTop: `4px solid ${template.color || '#3b82f6'}`,
                          boxShadow: template.isDefault ? '0 0 0 2px #ffc107' : 'none',
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                      >
                        {/* Template thumbnail preview */}
                        <CardMedia
                          component="img"
                          height="140"
                          image={`/templates/thumbnails/template-${template.type.toLowerCase()}-${template.layout || 'standard'}.png`}
                          alt={template.name}
                          sx={{
                            objectFit: 'contain',
                            p: 1,
                            bgcolor: '#f5f5f5',
                            borderBottom: '1px solid #e0e0e0'
                          }}
                        />
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="h6" component="div">
                              {template.name}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => handleSetDefault(template)}
                              color={template.isDefault ? 'warning' : 'default'}
                              title={template.isDefault ? 'Template par défaut' : 'Définir comme par défaut'}
                            >
                              {template.isDefault ? <StarIcon /> : <StarBorderIcon />}
                            </IconButton>
                          </Box>

                          <Box sx={{ display: 'flex', mb: 2 }}>
                            <Chip
                              label={template.type === 'facture' ? 'Facture' : template.type === 'devis' ? 'Devis' : 'Les deux'}
                              size="small"
                              color={template.type === 'facture' ? 'info' : template.type === 'devis' ? 'success' : 'secondary'}
                              sx={{ mr: 1 }}
                            />
                            {!template.isActive && (
                              <Chip label="Inactif" size="small" color="error" />
                            )}
                          </Box>

                          <Divider sx={{ my: 1 }} />

                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                            <IconButton
                              size="small"
                              onClick={() => handleOpenEditDialog(template)}
                              title="Modifier"
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDuplicateTemplate(template)}
                              title="Dupliquer"
                            >
                              <ContentCopyIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleConfirmDelete(template)}
                              title="Supprimer"
                              color="error"
                              disabled={template.isDefault}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialogue de création de template */}
      <Dialog open={openCreateDialog} onClose={() => setOpenCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Créer un nouveau template
          <IconButton
            aria-label="close"
            onClick={() => setOpenCreateDialog(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              fullWidth
              margin="normal"
              required
              label="Nom du template"
              name="name"
              value={formData.name}
              onChange={handleChange}
            />

            <FormControl fullWidth margin="normal">
              <InputLabel>Type de document</InputLabel>
              <Select
                name="type"
                value={formData.type}
                onChange={handleChange}
                label="Type de document"
              >
                <MenuItem value="facture">Facture</MenuItem>
                <MenuItem value="devis">Devis</MenuItem>
                <MenuItem value="both">Les deux</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <Typography variant="body2" sx={{ mr: 2 }}>
                Couleur du template:
              </Typography>
              <input
                type="color"
                name="color"
                value={formData.color}
                onChange={handleChange}
                style={{ width: '40px', height: '40px', border: 'none' }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCreateDialog(false)}>Annuler</Button>
          <Button
            onClick={handleCreateTemplate}
            variant="contained"
            color="primary"
            disabled={!formData.name}
          >
            Créer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue d'édition de template */}
      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Modifier le template
          <IconButton
            aria-label="close"
            onClick={() => setOpenEditDialog(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              fullWidth
              margin="normal"
              required
              label="Nom du template"
              name="name"
              value={formData.name}
              onChange={handleChange}
            />

            <FormControl fullWidth margin="normal">
              <InputLabel>Type de document</InputLabel>
              <Select
                name="type"
                value={formData.type}
                onChange={handleChange}
                label="Type de document"
              >
                <MenuItem value="facture">Facture</MenuItem>
                <MenuItem value="devis">Devis</MenuItem>
                <MenuItem value="both">Les deux</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <Typography variant="body2" sx={{ mr: 2 }}>
                Couleur du template:
              </Typography>
              <input
                type="color"
                name="color"
                value={formData.color}
                onChange={handleChange}
                style={{ width: '40px', height: '40px', border: 'none' }}
              />
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <Typography variant="body2" sx={{ mr: 2 }}>
                Template actif:
              </Typography>
              <input
                type="checkbox"
                name="isActive"
                checked={formData.isActive}
                onChange={handleChange}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>Annuler</Button>
          <Button
            onClick={handleUpdateTemplate}
            variant="contained"
            color="primary"
            disabled={!formData.name}
          >
            Mettre à jour
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation de suppression */}
      <Dialog open={confirmDeleteDialog} onClose={() => setConfirmDeleteDialog(false)}>
        <DialogTitle>Confirmation de suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer le template "{selectedTemplate?.name}" ?
            Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteDialog(false)}>Annuler</Button>
          <Button onClick={handleDeleteTemplate} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar pour les notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default TemplateManager;