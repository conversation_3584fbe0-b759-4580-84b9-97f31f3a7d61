import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Avatar,
  Grid,
  Divider,
  Chip,
  Button,
  IconButton,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  useTheme,
  CircularProgress,
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Email as EmailIcon,
  Language as LanguageIcon,
  Business as BusinessIcon,
  Receipt as ReceiptIcon,
  Edit as EditIcon,
  Assignment as AssignmentIcon,
  Send as SendIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { formatDate, formatCurrency } from '../utils/formatters';
import clientService from '../services/clientService';

const ClientDetail = ({ client, onEdit, onClose }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [clientStats, setClientStats] = useState(null);
  const [factures, setFactures] = useState([]);
  const [devis, setDevis] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClientData = async () => {
      if (client && client._id) {
        setLoading(true);
        try {
          // Fetch client statistics
          const stats = await clientService.getClientStats(client._id);
          setClientStats(stats);

          // Fetch client invoices
          const facturesData = await clientService.getClientFactures(client._id);
          setFactures(facturesData);

          // Fetch client quotes
          const devisData = await clientService.getClientDevis(client._id);
          setDevis(devisData);
        } catch (error) {
          console.error('Error fetching client data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchClientData();
  }, [client]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PAID':
      case 'ACCEPTED':
        return theme.palette.success.main;
      case 'SENT':
      case 'PENDING':
        return theme.palette.info.main;
      case 'DRAFT':
      case 'BROUILLON':
        return theme.palette.warning.main;
      case 'CANCELED':
      case 'REJECTED':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'PAID': return 'Payée';
      case 'SENT': return 'Envoyée';
      case 'DRAFT': return 'Brouillon';
      case 'CANCELED': return 'Annulée';
      case 'ACCEPTED': return 'Accepté';
      case 'PENDING': return 'En attente';
      case 'REJECTED': return 'Rejeté';
      case 'BROUILLON': return 'Brouillon';
      case 'ENVOYÉ': return 'Envoyé';
      default: return status;
    }
  };



  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // If client is null, show a loading state
  if (!client) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Client Header */}
      <Card
        elevation={0}
        sx={{
          mb: 2,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar
                src={client?.logo || ''}
                sx={{ width: 80, height: 80 }}
              >
                {client?.nom?.charAt(0) || '?'}
              </Avatar>
            </Grid>

            <Grid item xs>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h5" component="h1" sx={{ mr: 2 }}>
                  {client?.nom || 'Client sans nom'}
                </Typography>
                <Chip
                  label={client?.categorie || 'Standard'}
                  color={
                    client?.categorie === 'VIP' ? 'error' :
                    client?.categorie === 'Premium' ? 'warning' : 'info'
                  }
                  size="small"
                  sx={{ mr: 1 }}
                />
                <Chip
                  label={client?.actif ? 'Actif' : 'Inactif'}
                  color={client?.actif ? 'success' : 'default'}
                  size="small"
                />
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BusinessIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {client?.secteurActivite || 'Non spécifié'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PhoneIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      {client?.contact || 'Non spécifié'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      {client?.email || 'Non spécifié'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PersonIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      {client?.vendeurId ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {client.vendeurId.profileImage && (
                            <Avatar
                              src={`http://localhost:5000${client.vendeurId.profileImage}`}
                              sx={{ width: 20, height: 20 }}
                            />
                          )}
                          {client.vendeurId.nom}
                        </Box>
                      ) : (
                        'Non assigné'
                      )}
                    </Typography>
                  </Box>
                </Grid>

                {client?.siteWeb && (
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LanguageIcon color="action" sx={{ mr: 1 }} />
                      <Typography variant="body2">
                        {client.siteWeb}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Grid>

            <Grid item>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => onEdit && onEdit(client)}
                sx={{ mr: 1 }}
              >
                Modifier
              </Button>
              <Button
                variant="contained"
                onClick={() => onClose && onClose()}
              >
                Fermer
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Client Stats */}
      {clientStats && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} sm={4} md={2}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                textAlign: 'center',
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Typography variant="h4" color="primary">
                {clientStats.totalFactures}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Factures
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={6} sm={4} md={2}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                textAlign: 'center',
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Typography variant="h4" color="primary">
                {clientStats.totalDevis}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Devis
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={6} sm={4} md={2}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                textAlign: 'center',
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Typography variant="h4" sx={{ color: theme.palette.success.main }}>
                {clientStats.facturesPaid}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Payées
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={6} sm={4} md={2}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                textAlign: 'center',
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Typography variant="h4" sx={{ color: theme.palette.warning.main }}>
                {clientStats.facturesPending}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                En attente
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={8} md={4}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                textAlign: 'center',
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Typography variant="h4" color="primary">
                {formatCurrency(clientStats.montantTotal)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Montant total
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Factures" />
          <Tab label="Devis" />
          <Tab label="Détails" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {/* Invoices Tab */}
        {activeTab === 0 && (
          <List>
            {factures.length > 0 ? (
              factures.map((facture) => (
                <ListItem
                  key={facture._id}
                  sx={{
                    mb: 1,
                    borderRadius: 1,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                  secondaryAction={
                    <IconButton edge="end">
                      <SendIcon />
                    </IconButton>
                  }
                >
                  <ListItemIcon>
                    <ReceiptIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle1" sx={{ mr: 2 }}>
                          Facture #{facture.numero}
                        </Typography>
                        <Chip
                          label={getStatusLabel(facture.statut)}
                          size="small"
                          sx={{
                            backgroundColor: getStatusColor(facture.statut),
                            color: '#fff'
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="body2" color="text.secondary">
                          {formatDate(facture.dateEmission)}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(facture.total)}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                Aucune facture pour ce client
              </Typography>
            )}
          </List>
        )}

        {/* Quotes Tab */}
        {activeTab === 1 && (
          <List>
            {devis.length > 0 ? (
              devis.map((devis) => (
                <ListItem
                  key={devis._id}
                  sx={{
                    mb: 1,
                    borderRadius: 1,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                  secondaryAction={
                    <IconButton edge="end">
                      <SendIcon />
                    </IconButton>
                  }
                >
                  <ListItemIcon>
                    <AssignmentIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle1" sx={{ mr: 2 }}>
                          Devis #{devis.numéro}
                        </Typography>
                        <Chip
                          label={getStatusLabel(devis.statut)}
                          size="small"
                          sx={{
                            backgroundColor: getStatusColor(devis.statut),
                            color: '#fff'
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="body2" color="text.secondary">
                          {formatDate(devis.dateCréation)}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(devis.total)}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                Aucun devis pour ce client
              </Typography>
            )}
          </List>
        )}

        {/* Details Tab */}
        {activeTab === 2 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card
                elevation={0}
                sx={{
                  height: '100%',
                  borderRadius: 2,
                  border: `1px solid ${theme.palette.divider}`,
                }}
              >
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informations de contact
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Adresse</Typography>
                      <Typography variant="body2">{client.adresse}</Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Téléphone</Typography>
                      <Typography variant="body2">{client.contact}</Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Email</Typography>
                      <Typography variant="body2">{client.email}</Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2">Vendeur assigné</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {client.vendeurId ? (
                          <>
                            {client.vendeurId.profileImage && (
                              <Avatar
                                src={`http://localhost:5000${client.vendeurId.profileImage}`}
                                sx={{ width: 24, height: 24 }}
                              />
                            )}
                            <Typography variant="body2">{client.vendeurId.nom}</Typography>
                          </>
                        ) : (
                          <Typography variant="body2" color="text.secondary">Non assigné</Typography>
                        )}
                      </Box>
                    </Grid>

                    {client.siteWeb && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2">Site Web</Typography>
                        <Typography variant="body2">{client.siteWeb}</Typography>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card
                elevation={0}
                sx={{
                  height: '100%',
                  borderRadius: 2,
                  border: `1px solid ${theme.palette.divider}`,
                }}
              >
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Informations professionnelles
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    {client.secteurActivite && (
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2">Secteur d'activité</Typography>
                        <Typography variant="body2">{client.secteurActivite}</Typography>
                      </Grid>
                    )}

                    {client.formeJuridique && (
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2">Forme juridique</Typography>
                        <Typography variant="body2">{client.formeJuridique}</Typography>
                      </Grid>
                    )}

                    {client.numeroFiscal && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2">Numéro fiscal</Typography>
                        <Typography variant="body2">{client.numeroFiscal}</Typography>
                      </Grid>
                    )}

                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Client depuis</Typography>
                      <Typography variant="body2">{formatDate(client.createdAt)}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {client.notes && (
              <Grid item xs={12}>
                <Card
                  elevation={0}
                  sx={{
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                >
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Notes
                    </Typography>
                    <Divider sx={{ mb: 2 }} />

                    <Typography variant="body2">
                      {client.notes}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        )}
      </Box>
    </Box>
  );
};

export default ClientDetail;
