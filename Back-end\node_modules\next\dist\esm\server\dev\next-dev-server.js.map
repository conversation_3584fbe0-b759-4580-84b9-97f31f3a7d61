{"version": 3, "sources": ["../../../src/server/dev/next-dev-server.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>R<PERSON>ult, NodeRequestHandler } from '../next-server'\nimport type { LoadComponentsReturnType } from '../load-components'\nimport type { Options as ServerOptions } from '../next-server'\nimport type { Params } from '../request/params'\nimport type { ParsedUrl } from '../../shared/lib/router/utils/parse-url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { MiddlewareRoutingItem } from '../base-server'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { RouteMatcherManager } from '../route-matcher-managers/route-matcher-manager'\nimport {\n  getRequestMeta,\n  type NextParsedUrlQuery,\n  type NextUrlWithParsedQuery,\n} from '../request-meta'\nimport type { DevBundlerService } from '../lib/dev-bundler-service'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport type { NodeNextResponse, NodeNextRequest } from '../base-http/node'\nimport type { RouteEnsurer } from '../route-matcher-managers/dev-route-matcher-manager'\nimport type { PagesManifest } from '../../build/webpack/plugins/pages-manifest-plugin'\n\nimport fs from 'fs'\nimport { Worker } from 'next/dist/compiled/jest-worker'\nimport { join as pathJoin } from 'path'\nimport { ampValidation } from '../../build/output'\nimport {\n  INSTRUMENTATION_HOOK_FILENAME,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n} from '../../lib/constants'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PAGES_MANIFEST,\n  APP_PATHS_MANIFEST,\n  COMPILER_NAMES,\n} from '../../shared/lib/constants'\nimport Server, { WrappedBuildError } from '../next-server'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { Telemetry } from '../../telemetry/storage'\nimport { type Span, setGlobal, trace } from '../../trace'\nimport { findPageFile } from '../lib/find-page-file'\nimport { getFormattedNodeOptionsWithoutInspect } from '../lib/utils'\nimport { withCoalescedInvoke } from '../../lib/coalesced-function'\nimport { loadDefaultErrorComponents } from '../load-default-error-components'\nimport { DecodeError, MiddlewareNotFoundError } from '../../shared/lib/utils'\nimport * as Log from '../../build/output/log'\nimport isError, { getProperError } from '../../lib/is-error'\nimport { isMiddlewareFile } from '../../build/utils'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { DevRouteMatcherManager } from '../route-matcher-managers/dev-route-matcher-manager'\nimport { DevPagesRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-route-matcher-provider'\nimport { DevPagesAPIRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-api-route-matcher-provider'\nimport { DevAppPageRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-page-route-matcher-provider'\nimport { DevAppRouteRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-route-route-matcher-provider'\nimport { NodeManifestLoader } from '../route-matcher-providers/helpers/manifest-loaders/node-manifest-loader'\nimport { BatchedFileReader } from '../route-matcher-providers/dev/helpers/file-reader/batched-file-reader'\nimport { DefaultFileReader } from '../route-matcher-providers/dev/helpers/file-reader/default-file-reader'\nimport { LRUCache } from '../lib/lru-cache'\nimport { getMiddlewareRouteMatcher } from '../../shared/lib/router/utils/middleware-route-matcher'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { isPostpone } from '../lib/router-utils/is-postpone'\nimport { generateInterceptionRoutesRewrites } from '../../lib/generate-interception-routes-rewrites'\nimport { buildCustomRoute } from '../../lib/build-custom-route'\nimport { decorateServerError } from '../../shared/lib/error-source'\nimport type { ServerOnInstrumentationRequestError } from '../app-render/types'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport { logRequests } from './log-requests'\nimport { FallbackMode } from '../../lib/fallback'\nimport type { PagesDevOverlayType } from '../../client/components/react-dev-overlay/pages/pages-dev-overlay'\n\n// Load ReactDevOverlay only when needed\nlet ReactDevOverlayImpl: PagesDevOverlayType\nconst ReactDevOverlay: PagesDevOverlayType = (props) => {\n  if (ReactDevOverlayImpl === undefined) {\n    ReactDevOverlayImpl =\n      require('../../client/components/react-dev-overlay/pages/pages-dev-overlay')\n        .PagesDevOverlay as PagesDevOverlayType\n  }\n  return ReactDevOverlayImpl(props)\n}\n\nexport interface Options extends ServerOptions {\n  /**\n   * Tells of Next.js is running from the `next dev` command\n   */\n  isNextDevCommand?: boolean\n\n  /**\n   * Interface to the development bundler.\n   */\n  bundlerService: DevBundlerService\n\n  /**\n   * Trace span for server startup.\n   */\n  startServerSpan: Span\n}\n\nexport default class DevServer extends Server {\n  /**\n   * The promise that resolves when the server is ready. When this is unset\n   * the server is ready.\n   */\n  private ready? = new DetachedPromise<void>()\n  protected sortedRoutes?: string[]\n  private pagesDir?: string\n  private appDir?: string\n  private actualMiddlewareFile?: string\n  private actualInstrumentationHookFile?: string\n  private middleware?: MiddlewareRoutingItem\n  private originalFetch?: typeof fetch\n  private readonly bundlerService: DevBundlerService\n  private staticPathsCache: LRUCache<\n    UnwrapPromise<ReturnType<DevServer['getStaticPaths']>>\n  >\n  private startServerSpan: Span\n  private readonly serverComponentsHmrCache:\n    | ServerComponentsHmrCache\n    | undefined\n\n  protected staticPathsWorker?: { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  }\n\n  private getStaticPathsWorker(): { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  } {\n    const worker = new Worker(require.resolve('./static-paths-worker'), {\n      maxRetries: 1,\n      // For dev server, it's not necessary to spin up too many workers as long as you are not doing a load test.\n      // This helps reusing the memory a lot.\n      numWorkers: 1,\n      enableWorkerThreads: this.nextConfig.experimental.workerThreads,\n      forkOptions: {\n        env: {\n          ...process.env,\n          // discard --inspect/--inspect-brk flags from process.env.NODE_OPTIONS. Otherwise multiple Node.js debuggers\n          // would be started if user launch Next.js in debugging mode. The number of debuggers is linked to\n          // the number of workers Next.js tries to launch. The only worker users are interested in debugging\n          // is the main Next.js one\n          NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect(),\n        },\n      },\n    }) as Worker & {\n      loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n    }\n\n    worker.getStdout().pipe(process.stdout)\n    worker.getStderr().pipe(process.stderr)\n\n    return worker\n  }\n\n  constructor(options: Options) {\n    try {\n      // Increase the number of stack frames on the server\n      Error.stackTraceLimit = 50\n    } catch {}\n    super({ ...options, dev: true })\n    this.bundlerService = options.bundlerService\n    this.startServerSpan =\n      options.startServerSpan ?? trace('start-next-dev-server')\n    this.renderOpts.dev = true\n    this.renderOpts.ErrorDebug = ReactDevOverlay\n    this.staticPathsCache = new LRUCache(\n      // 5MB\n      5 * 1024 * 1024,\n      function length(value) {\n        return JSON.stringify(value.staticPaths)?.length ?? 0\n      }\n    )\n    this.renderOpts.ampSkipValidation =\n      this.nextConfig.experimental?.amp?.skipValidation ?? false\n    this.renderOpts.ampValidator = (html: string, pathname: string) => {\n      const validatorPath =\n        (this.nextConfig.experimental &&\n          this.nextConfig.experimental.amp &&\n          this.nextConfig.experimental.amp.validator) ||\n        require.resolve(\n          'next/dist/compiled/amphtml-validator/validator_wasm.js'\n        )\n\n      const AmpHtmlValidator =\n        require('next/dist/compiled/amphtml-validator') as typeof import('next/dist/compiled/amphtml-validator')\n      return AmpHtmlValidator.getInstance(validatorPath).then((validator) => {\n        const result = validator.validateString(html)\n        ampValidation(\n          pathname,\n          result.errors\n            .filter((e) => e.severity === 'ERROR')\n            .filter((e) => this._filterAmpDevelopmentScript(html, e)),\n          result.errors.filter((e) => e.severity !== 'ERROR')\n        )\n      })\n    }\n\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n\n    if (this.nextConfig.experimental.serverComponentsHmrCache) {\n      this.serverComponentsHmrCache = new LRUCache(\n        this.nextConfig.cacheMaxMemorySize,\n        function length(value) {\n          return JSON.stringify(value).length\n        }\n      )\n    }\n  }\n\n  protected override getServerComponentsHmrCache() {\n    return this.serverComponentsHmrCache\n  }\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n\n    const ensurer: RouteEnsurer = {\n      ensure: async (match, pathname) => {\n        await this.ensurePage({\n          definition: match.definition,\n          page: match.definition.page,\n          clientOnly: false,\n          url: pathname,\n        })\n      },\n    }\n\n    const matchers = new DevRouteMatcherManager(\n      super.getRouteMatchers(),\n      ensurer,\n      this.dir\n    )\n    const extensions = this.nextConfig.pageExtensions\n    const extensionsExpression = new RegExp(`\\\\.(?:${extensions.join('|')})$`)\n\n    // If the pages directory is available, then configure those matchers.\n    if (pagesDir) {\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Only allow files that have the correct extensions.\n          pathnameFilter: (pathname) => extensionsExpression.test(pathname),\n        })\n      )\n\n      matchers.push(\n        new DevPagesRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n      matchers.push(\n        new DevPagesAPIRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n    }\n\n    if (appDir) {\n      // We create a new file reader for the app directory because we don't want\n      // to include any folders or files starting with an underscore. This will\n      // prevent the reader from wasting time reading files that we know we\n      // don't care about.\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Ignore any directory prefixed with an underscore.\n          ignorePartFilter: (part) => part.startsWith('_'),\n        })\n      )\n\n      matchers.push(\n        new DevAppPageRouteMatcherProvider(appDir, extensions, fileReader)\n      )\n      matchers.push(\n        new DevAppRouteRouteMatcherProvider(appDir, extensions, fileReader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected getBuildId(): string {\n    return 'development'\n  }\n\n  protected async prepareImpl(): Promise<void> {\n    setGlobal('distDir', this.distDir)\n    setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n    const telemetry = new Telemetry({ distDir: this.distDir })\n\n    await super.prepareImpl()\n    await this.matchers.reload()\n\n    this.ready?.resolve()\n    this.ready = undefined\n\n    // In dev, this needs to be called after prepare because the build entries won't be known in the constructor\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // This is required by the tracing subsystem.\n    setGlobal('appDir', this.appDir)\n    setGlobal('pagesDir', this.pagesDir)\n    setGlobal('telemetry', telemetry)\n\n    process.on('unhandledRejection', (reason) => {\n      if (isPostpone(reason)) {\n        // React postpones that are unhandled might end up logged here but they're\n        // not really errors. They're just part of rendering.\n        return\n      }\n      this.logErrorWithOriginalStack(reason, 'unhandledRejection')\n    })\n    process.on('uncaughtException', (err) => {\n      this.logErrorWithOriginalStack(err, 'uncaughtException')\n    })\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    let normalizedPath: string\n    try {\n      normalizedPath = normalizePagePath(pathname)\n    } catch (err) {\n      console.error(err)\n      // if normalizing the page fails it means it isn't valid\n      // so it doesn't exist so don't throw and return false\n      // to ensure we return 404 instead of 500\n      return false\n    }\n\n    if (isMiddlewareFile(normalizedPath)) {\n      return findPageFile(\n        this.dir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      ).then(Boolean)\n    }\n\n    let appFile: string | null = null\n    let pagesFile: string | null = null\n\n    if (this.appDir) {\n      appFile = await findPageFile(\n        this.appDir,\n        normalizedPath + '/page',\n        this.nextConfig.pageExtensions,\n        true\n      )\n    }\n\n    if (this.pagesDir) {\n      pagesFile = await findPageFile(\n        this.pagesDir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      )\n    }\n    if (appFile && pagesFile) {\n      return false\n    }\n\n    return Boolean(appFile || pagesFile)\n  }\n\n  async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    middlewareList: MiddlewareRoutingItem[]\n  }) {\n    try {\n      const result = await super.runMiddleware({\n        ...params,\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n\n      if ('finished' in result) {\n        return result\n      }\n\n      result.waitUntil.catch((error) => {\n        this.logErrorWithOriginalStack(error, 'unhandledRejection')\n      })\n      return result\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n\n      /**\n       * We only log the error when it is not a MiddlewareNotFound error as\n       * in that case we should be already displaying a compilation error\n       * which is what makes the module not found.\n       */\n      if (!(error instanceof MiddlewareNotFoundError)) {\n        this.logErrorWithOriginalStack(error)\n      }\n\n      const err = getProperError(error)\n      decorateServerError(err, COMPILER_NAMES.edgeServer)\n      const { request, response, parsedUrl } = params\n\n      /**\n       * When there is a failure for an internal Next.js request from\n       * middleware we bypass the error without finishing the request\n       * so we can serve the required chunks to render the error.\n       */\n      if (\n        request.url.includes('/_next/static') ||\n        request.url.includes('/__nextjs_original-stack-frame') ||\n        request.url.includes('/__nextjs_source-map') ||\n        request.url.includes('/__nextjs_error_feedback')\n      ) {\n        return { finished: false }\n      }\n\n      response.statusCode = 500\n      await this.renderError(err, request, response, parsedUrl.pathname)\n      return { finished: true }\n    }\n  }\n\n  async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    isAppPath: boolean\n  }) {\n    try {\n      return super.runEdgeFunction({\n        ...params,\n        onError: (err) => this.logErrorWithOriginalStack(err, 'app-dir'),\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n      this.logErrorWithOriginalStack(error, 'warning')\n      const err = getProperError(error)\n      const { req, res, page } = params\n\n      res.statusCode = 500\n      await this.renderError(err, req, res, page)\n      return null\n    }\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) => {\n      const request = this.normalizeReq(req)\n      const response = this.normalizeRes(res)\n      const loggingConfig = this.nextConfig.logging\n\n      if (loggingConfig !== false) {\n        const start = Date.now()\n        const isMiddlewareRequest = getRequestMeta(req, 'middlewareInvoke')\n\n        if (!isMiddlewareRequest) {\n          response.originalResponse.once('close', () => {\n            // NOTE: The route match is only attached to the request's meta data\n            // after the request handler is created, so we need to check it in the\n            // close handler and not before.\n            const routeMatch = getRequestMeta(req).match\n\n            if (!routeMatch) {\n              return\n            }\n\n            logRequests({\n              request,\n              response,\n              loggingConfig,\n              requestDurationInMs: Date.now() - start,\n            })\n          })\n        }\n      }\n\n      return handler(request, response, parsedUrl)\n    }\n  }\n\n  public async handleRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    const span = trace('handle-request', undefined, { url: req.url })\n    const result = await span.traceAsyncFn(async () => {\n      await this.ready?.promise\n      return await super.handleRequest(req, res, parsedUrl)\n    })\n    const memoryUsage = process.memoryUsage()\n    span\n      .traceChild('memory-usage', {\n        url: req.url,\n        'memory.rss': String(memoryUsage.rss),\n        'memory.heapUsed': String(memoryUsage.heapUsed),\n        'memory.heapTotal': String(memoryUsage.heapTotal),\n      })\n      .stop()\n    return result\n  }\n\n  async run(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.ready?.promise\n\n    const { basePath } = this.nextConfig\n    let originalPathname: string | null = null\n\n    // TODO: see if we can remove this in the future\n    if (basePath && pathHasPrefix(parsedUrl.pathname || '/', basePath)) {\n      // strip basePath before handling dev bundles\n      // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n      originalPathname = parsedUrl.pathname\n      parsedUrl.pathname = removePathPrefix(parsedUrl.pathname || '/', basePath)\n    }\n\n    const { pathname } = parsedUrl\n\n    if (pathname!.startsWith('/_next')) {\n      if (fs.existsSync(pathJoin(this.publicDir, '_next'))) {\n        throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n      }\n    }\n\n    if (originalPathname) {\n      // restore the path before continuing so that custom-routes can accurately determine\n      // if they should match against the basePath or not\n      parsedUrl.pathname = originalPathname\n    }\n    try {\n      return await super.run(req, res, parsedUrl)\n    } catch (error) {\n      const err = getProperError(error)\n      formatServerError(err)\n      this.logErrorWithOriginalStack(err)\n      if (!res.sent) {\n        res.statusCode = 500\n        try {\n          return await this.renderError(err, req, res, pathname!, {\n            __NEXT_PAGE: (isError(err) && err.page) || pathname || '',\n          })\n        } catch (internalErr) {\n          console.error(internalErr)\n          res.body('Internal Server Error').send()\n        }\n      }\n    }\n  }\n\n  protected logErrorWithOriginalStack(\n    err?: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    this.bundlerService.logErrorWithOriginalStack(err, type)\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, PAGES_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, APP_PATHS_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    const rewrites = generateInterceptionRoutesRewrites(\n      Object.keys(this.appPathRoutes ?? {}),\n      this.nextConfig.basePath\n    ).map((route) => new RegExp(buildCustomRoute('rewrite', route).regex))\n\n    if (this.nextConfig.output === 'export' && rewrites.length > 0) {\n      Log.error(\n        'Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features'\n      )\n\n      process.exit(1)\n    }\n\n    return rewrites ?? []\n  }\n\n  protected async getMiddleware() {\n    // We need to populate the match\n    // field as it isn't serializable\n    if (this.middleware?.match === null) {\n      this.middleware.match = getMiddlewareRouteMatcher(\n        this.middleware.matchers || []\n      )\n    }\n    return this.middleware\n  }\n\n  protected getNextFontManifest() {\n    return undefined\n  }\n\n  protected async hasMiddleware(): Promise<boolean> {\n    return this.hasPage(this.actualMiddlewareFile!)\n  }\n\n  protected async ensureMiddleware(url: string) {\n    return this.ensurePage({\n      page: this.actualMiddlewareFile!,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  protected async loadInstrumentationModule(): Promise<any> {\n    let instrumentationModule: any\n    if (\n      this.actualInstrumentationHookFile &&\n      (await this.ensurePage({\n        page: this.actualInstrumentationHookFile!,\n        clientOnly: false,\n        definition: undefined,\n      })\n        .then(() => true)\n        .catch(() => false))\n    ) {\n      try {\n        instrumentationModule = await require(\n          pathJoin(this.distDir, 'server', INSTRUMENTATION_HOOK_FILENAME)\n        )\n      } catch (err: any) {\n        err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n        throw err\n      }\n    }\n    return instrumentationModule\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    await this.startServerSpan\n      .traceChild('run-instrumentation-hook')\n      .traceAsyncFn(() => this.instrumentation?.register?.())\n  }\n\n  protected async ensureEdgeFunction({\n    page,\n    appPaths,\n    url,\n  }: {\n    page: string\n    appPaths: string[] | null\n    url: string\n  }) {\n    return this.ensurePage({\n      page,\n      appPaths,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  generateRoutes(_dev?: boolean) {\n    // In development we expose all compiled files for react-error-overlay's line show feature\n    // We use unshift so that we're sure the routes is defined before Next's default routes\n    // routes.unshift({\n    //   match: getPathMatch('/_next/development/:path*'),\n    //   type: 'route',\n    //   name: '_next/development catchall',\n    //   fn: async (req, res, params) => {\n    //     const p = pathJoin(this.distDir, ...(params.path || []))\n    //     await this.serveStatic(req, res, p)\n    //     return {\n    //       finished: true,\n    //     }\n    //   },\n    // })\n  }\n\n  _filterAmpDevelopmentScript(\n    html: string,\n    event: { line: number; col: number; code: string }\n  ): boolean {\n    if (event.code !== 'DISALLOWED_SCRIPT_TAG') {\n      return true\n    }\n\n    const snippetChunks = html.split('\\n')\n\n    let snippet\n    if (\n      !(snippet = html.split('\\n')[event.line - 1]) ||\n      !(snippet = snippet.substring(event.col))\n    ) {\n      return true\n    }\n\n    snippet = snippet + snippetChunks.slice(event.line).join('\\n')\n    snippet = snippet.substring(0, snippet.indexOf('</script>'))\n\n    return !snippet.includes('data-amp-development-mode-only')\n  }\n\n  protected async getStaticPaths({\n    pathname,\n    requestHeaders,\n    page,\n    isAppPath,\n  }: {\n    pathname: string\n    requestHeaders: IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // we lazy load the staticPaths to prevent the user\n    // from waiting on them for the page to load in dev mode\n\n    const __getStaticPaths = async () => {\n      const {\n        configFileName,\n        publicRuntimeConfig,\n        serverRuntimeConfig,\n        httpAgentOptions,\n      } = this.nextConfig\n      const { locales, defaultLocale } = this.nextConfig.i18n || {}\n      const staticPathsWorker = this.getStaticPathsWorker()\n\n      try {\n        const pathsResult = await staticPathsWorker.loadStaticPaths({\n          dir: this.dir,\n          distDir: this.distDir,\n          pathname,\n          config: {\n            pprConfig: this.nextConfig.experimental.ppr,\n            configFileName,\n            publicRuntimeConfig,\n            serverRuntimeConfig,\n            dynamicIO: Boolean(this.nextConfig.experimental.dynamicIO),\n          },\n          httpAgentOptions,\n          locales,\n          defaultLocale,\n          page,\n          isAppPath,\n          requestHeaders,\n          cacheHandler: this.nextConfig.cacheHandler,\n          cacheHandlers: this.nextConfig.experimental.cacheHandlers,\n          cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n          fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n          isrFlushToDisk: this.nextConfig.experimental.isrFlushToDisk,\n          maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n          nextConfigOutput: this.nextConfig.output,\n          buildId: this.buildId,\n          authInterrupts: Boolean(this.nextConfig.experimental.authInterrupts),\n          sriEnabled: Boolean(this.nextConfig.experimental.sri?.algorithm),\n        })\n        return pathsResult\n      } finally {\n        // we don't re-use workers so destroy the used one\n        staticPathsWorker.end()\n      }\n    }\n    const result = this.staticPathsCache.get(pathname)\n\n    const nextInvoke = withCoalescedInvoke(__getStaticPaths)(\n      `staticPaths-${pathname}`,\n      []\n    )\n      .then((res) => {\n        const { prerenderedRoutes: staticPaths, fallbackMode: fallback } =\n          res.value\n        if (!isAppPath && this.nextConfig.output === 'export') {\n          if (fallback === FallbackMode.BLOCKING_STATIC_RENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: blocking\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          } else if (fallback === FallbackMode.PRERENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          }\n        }\n\n        const value: {\n          staticPaths: string[] | undefined\n          fallbackMode: FallbackMode | undefined\n        } = {\n          staticPaths: staticPaths?.map((route) => route.pathname),\n          fallbackMode: fallback,\n        }\n        this.staticPathsCache.set(pathname, value)\n        return value\n      })\n      .catch((err) => {\n        this.staticPathsCache.remove(pathname)\n        if (!result) throw err\n        Log.error(`Failed to generate static paths for ${pathname}:`)\n        console.error(err)\n      })\n\n    if (result) {\n      return result\n    }\n    return nextInvoke as NonNullable<typeof result>\n  }\n\n  protected async ensurePage(opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void> {\n    await this.bundlerService.ensurePage(opts)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    appPaths = null,\n    shouldEnsure,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    await this.ready?.promise\n\n    const compilationErr = await this.getCompilationError(page)\n    if (compilationErr) {\n      // Wrap build errors so that they don't get logged again\n      throw new WrappedBuildError(compilationErr)\n    }\n    if (shouldEnsure || this.serverOptions.customServer) {\n      await this.ensurePage({\n        page,\n        appPaths,\n        clientOnly: false,\n        definition: undefined,\n        url,\n      })\n    }\n\n    this.nextFontManifest = super.getNextFontManifest()\n\n    return await super.findPageComponents({\n      page,\n      query,\n      params,\n      locale,\n      isAppPath,\n      shouldEnsure,\n      url,\n    })\n  }\n\n  protected async getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    await this.bundlerService.getFallbackErrorComponents(url)\n    return await loadDefaultErrorComponents(this.distDir)\n  }\n\n  async getCompilationError(page: string): Promise<any> {\n    return await this.bundlerService.getCompilationError(page)\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    const err = args[0]\n    this.logErrorWithOriginalStack(err, 'app-dir')\n  }\n}\n"], "names": ["getRequestMeta", "fs", "Worker", "join", "pathJoin", "ampValidation", "INSTRUMENTATION_HOOK_FILENAME", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "findPagesDir", "PHASE_DEVELOPMENT_SERVER", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "COMPILER_NAMES", "Server", "WrappedBuildError", "normalizePagePath", "pathHasPrefix", "removePathPrefix", "Telemetry", "setGlobal", "trace", "findPageFile", "getFormattedNodeOptionsWithoutInspect", "withCoalescedInvoke", "loadDefaultErrorComponents", "DecodeError", "MiddlewareNotFoundError", "Log", "isError", "getProperError", "isMiddlewareFile", "formatServerError", "DevRouteMatcherManager", "DevPagesRouteMatcherProvider", "DevPagesAPIRouteMatcherProvider", "DevAppPageRouteMatcherProvider", "DevAppRouteRouteMatcherProvider", "NodeManifestLoader", "BatchedFileReader", "DefaultFileReader", "L<PERSON><PERSON><PERSON>", "getMiddlewareRouteMatcher", "Detached<PERSON>romise", "isPostpone", "generateInterceptionRoutesRewrites", "buildCustomRoute", "decorateServerError", "logRequests", "FallbackMode", "ReactDevOverlayImpl", "ReactDevOverlay", "props", "undefined", "require", "<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "DevServer", "getStaticPathsWorker", "worker", "resolve", "maxRetries", "numWorkers", "enableWorkerThreads", "nextConfig", "experimental", "workerThreads", "forkOptions", "env", "process", "NODE_OPTIONS", "getStdout", "pipe", "stdout", "getStderr", "stderr", "constructor", "options", "Error", "stackTraceLimit", "dev", "ready", "bundlerService", "startServerSpan", "renderOpts", "ErrorDebug", "staticPathsCache", "length", "value", "JSON", "stringify", "staticPaths", "ampSkipValidation", "amp", "skipValidation", "ampValidator", "html", "pathname", "validatorPath", "validator", "AmpHtmlValidator", "getInstance", "then", "result", "validateString", "errors", "filter", "e", "severity", "_filterAmpDevelopmentScript", "pagesDir", "appDir", "dir", "serverComponentsHmrCache", "cacheMaxMemorySize", "getServerComponentsHmrCache", "getRouteMatchers", "ensurer", "ensure", "match", "ensurePage", "definition", "page", "clientOnly", "url", "matchers", "extensions", "pageExtensions", "extensionsExpression", "RegExp", "fileReader", "pathnameFilter", "test", "push", "localeNormalizer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "startsWith", "getBuildId", "prepareImpl", "distDir", "telemetry", "reload", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "on", "reason", "logErrorWithOriginalStack", "err", "hasPage", "normalizedPath", "console", "error", "Boolean", "appFile", "pagesFile", "runMiddleware", "params", "onWarning", "warn", "waitUntil", "catch", "edgeServer", "request", "response", "parsedUrl", "includes", "finished", "statusCode", "renderError", "runEdgeFunction", "onError", "req", "res", "getRequestHandler", "handler", "normalizeReq", "normalizeRes", "loggingConfig", "logging", "start", "Date", "now", "isMiddlewareRequest", "originalResponse", "once", "routeMatch", "requestDurationInMs", "handleRequest", "span", "traceAsyncFn", "promise", "memoryUsage", "<PERSON><PERSON><PERSON><PERSON>", "String", "rss", "heapUsed", "heapTotal", "stop", "run", "basePath", "originalPathname", "existsSync", "publicDir", "sent", "__NEXT_PAGE", "internalErr", "body", "send", "type", "getPagesManifest", "serverDistDir", "getAppPathsManifest", "enabledDirectories", "app", "rewrites", "Object", "keys", "appPathRoutes", "map", "route", "regex", "output", "exit", "getMiddleware", "middleware", "getNextFontManifest", "hasMiddleware", "actualMiddlewareFile", "ensureMiddleware", "loadInstrumentationModule", "instrumentationModule", "actualInstrumentationHookFile", "message", "runInstrumentationHookIfAvailable", "instrumentation", "register", "ensureEdgeFunction", "appPaths", "generateRoutes", "_dev", "event", "code", "snippetChunks", "split", "snippet", "line", "substring", "col", "slice", "indexOf", "getStaticPaths", "requestHeaders", "isAppPath", "__getStaticPaths", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "httpAgentOptions", "locales", "defaultLocale", "i18n", "staticPathsWorker", "pathsResult", "loadStaticPaths", "config", "pprConfig", "ppr", "dynamicIO", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "cacheLife", "fetchCacheKeyPrefix", "isrFlushToDisk", "maxMemoryCacheSize", "nextConfigOutput", "buildId", "authInterrupts", "sriEnabled", "sri", "algorithm", "end", "get", "nextInvoke", "prerenderedRoutes", "fallbackMode", "fallback", "BLOCKING_STATIC_RENDER", "PRERENDER", "set", "remove", "opts", "findPageComponents", "locale", "query", "shouldEnsure", "compilationErr", "getCompilationError", "serverOptions", "customServer", "nextFontManifest", "getFallbackErrorComponents", "instrumentationOnRequestError", "args"], "mappings": "AAUA,SACEA,cAAc,QAGT,kBAAiB;AAQxB,OAAOC,QAAQ,KAAI;AACnB,SAASC,MAAM,QAAQ,iCAAgC;AACvD,SAASC,QAAQC,QAAQ,QAAQ,OAAM;AACvC,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SACEC,6BAA6B,EAC7BC,8BAA8B,QACzB,sBAAqB;AAC5B,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SACEC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,cAAc,QACT,6BAA4B;AACnC,OAAOC,UAAUC,iBAAiB,QAAQ,iBAAgB;AAC1D,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAAoBC,SAAS,EAAEC,KAAK,QAAQ,cAAa;AACzD,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SAASC,qCAAqC,QAAQ,eAAc;AACpE,SAASC,mBAAmB,QAAQ,+BAA8B;AAClE,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,WAAW,EAAEC,uBAAuB,QAAQ,yBAAwB;AAC7E,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,WAAWC,cAAc,QAAQ,qBAAoB;AAC5D,SAASC,gBAAgB,QAAQ,oBAAmB;AACpD,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,sBAAsB,QAAQ,sDAAqD;AAC5F,SAASC,4BAA4B,QAAQ,kEAAiE;AAC9G,SAASC,+BAA+B,QAAQ,sEAAqE;AACrH,SAASC,8BAA8B,QAAQ,qEAAoE;AACnH,SAASC,+BAA+B,QAAQ,sEAAqE;AACrH,SAASC,kBAAkB,QAAQ,2EAA0E;AAC7G,SAASC,iBAAiB,QAAQ,yEAAwE;AAC1G,SAASC,iBAAiB,QAAQ,yEAAwE;AAC1G,SAASC,QAAQ,QAAQ,mBAAkB;AAC3C,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,UAAU,QAAQ,kCAAiC;AAC5D,SAASC,kCAAkC,QAAQ,kDAAiD;AACpG,SAASC,gBAAgB,QAAQ,+BAA8B;AAC/D,SAASC,mBAAmB,QAAQ,gCAA+B;AAGnE,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,YAAY,QAAQ,qBAAoB;AAGjD,wCAAwC;AACxC,IAAIC;AACJ,MAAMC,kBAAuC,CAACC;IAC5C,IAAIF,wBAAwBG,WAAW;QACrCH,sBACEI,QAAQ,qEACLC,eAAe;IACtB;IACA,OAAOL,oBAAoBE;AAC7B;AAmBA,eAAe,MAAMI,kBAAkB1C;IA0B7B2C,uBAEN;QACA,MAAMC,SAAS,IAAIvD,OAAOmD,QAAQK,OAAO,CAAC,0BAA0B;YAClEC,YAAY;YACZ,2GAA2G;YAC3G,uCAAuC;YACvCC,YAAY;YACZC,qBAAqB,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,aAAa;YAC/DC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACd,4GAA4G;oBAC5G,kGAAkG;oBAClG,mGAAmG;oBACnG,0BAA0B;oBAC1BE,cAAc9C;gBAChB;YACF;QACF;QAIAmC,OAAOY,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACtCd,OAAOe,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEtC,OAAOhB;IACT;IAEAiB,YAAYC,OAAgB,CAAE;YAmB1B,mCAAA;QAlBF,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,OAAM,CAAC;QACT,KAAK,CAAC;YAAE,GAAGF,OAAO;YAAEG,KAAK;QAAK,IA3DhC;;;GAGC,QACOC,QAAS,IAAIrC;QAwDnB,IAAI,CAACsC,cAAc,GAAGL,QAAQK,cAAc;QAC5C,IAAI,CAACC,eAAe,GAClBN,QAAQM,eAAe,IAAI7D,MAAM;QACnC,IAAI,CAAC8D,UAAU,CAACJ,GAAG,GAAG;QACtB,IAAI,CAACI,UAAU,CAACC,UAAU,GAAGjC;QAC7B,IAAI,CAACkC,gBAAgB,GAAG,IAAI5C,SAC1B,MAAM;QACN,IAAI,OAAO,MACX,SAAS6C,OAAOC,KAAK;gBACZC;YAAP,OAAOA,EAAAA,kBAAAA,KAAKC,SAAS,CAACF,MAAMG,WAAW,sBAAhCF,gBAAmCF,MAAM,KAAI;QACtD;QAEF,IAAI,CAACH,UAAU,CAACQ,iBAAiB,GAC/B,EAAA,gCAAA,IAAI,CAAC5B,UAAU,CAACC,YAAY,sBAA5B,oCAAA,8BAA8B4B,GAAG,qBAAjC,kCAAmCC,cAAc,KAAI;QACvD,IAAI,CAACV,UAAU,CAACW,YAAY,GAAG,CAACC,MAAcC;YAC5C,MAAMC,gBACJ,AAAC,IAAI,CAAClC,UAAU,CAACC,YAAY,IAC3B,IAAI,CAACD,UAAU,CAACC,YAAY,CAAC4B,GAAG,IAChC,IAAI,CAAC7B,UAAU,CAACC,YAAY,CAAC4B,GAAG,CAACM,SAAS,IAC5C5C,QAAQK,OAAO,CACb;YAGJ,MAAMwC,mBACJ7C,QAAQ;YACV,OAAO6C,iBAAiBC,WAAW,CAACH,eAAeI,IAAI,CAAC,CAACH;gBACvD,MAAMI,SAASJ,UAAUK,cAAc,CAACR;gBACxCzF,cACE0F,UACAM,OAAOE,MAAM,CACVC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK,SAC7BF,MAAM,CAAC,CAACC,IAAM,IAAI,CAACE,2BAA2B,CAACb,MAAMW,KACxDJ,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;YAE/C;QACF;QAEA,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGrG,aAAa,IAAI,CAACsG,GAAG;QAClD,IAAI,CAACF,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAI,IAAI,CAAC/C,UAAU,CAACC,YAAY,CAACgD,wBAAwB,EAAE;YACzD,IAAI,CAACA,wBAAwB,GAAG,IAAIvE,SAClC,IAAI,CAACsB,UAAU,CAACkD,kBAAkB,EAClC,SAAS3B,OAAOC,KAAK;gBACnB,OAAOC,KAAKC,SAAS,CAACF,OAAOD,MAAM;YACrC;QAEJ;IACF;IAEmB4B,8BAA8B;QAC/C,OAAO,IAAI,CAACF,wBAAwB;IACtC;IAEUG,mBAAwC;QAChD,MAAM,EAAEN,QAAQ,EAAEC,MAAM,EAAE,GAAGrG,aAAa,IAAI,CAACsG,GAAG;QAElD,MAAMK,UAAwB;YAC5BC,QAAQ,OAAOC,OAAOtB;gBACpB,MAAM,IAAI,CAACuB,UAAU,CAAC;oBACpBC,YAAYF,MAAME,UAAU;oBAC5BC,MAAMH,MAAME,UAAU,CAACC,IAAI;oBAC3BC,YAAY;oBACZC,KAAK3B;gBACP;YACF;QACF;QAEA,MAAM4B,WAAW,IAAI3F,uBACnB,KAAK,CAACkF,oBACNC,SACA,IAAI,CAACL,GAAG;QAEV,MAAMc,aAAa,IAAI,CAAC9D,UAAU,CAAC+D,cAAc;QACjD,MAAMC,uBAAuB,IAAIC,OAAO,CAAC,MAAM,EAAEH,WAAWzH,IAAI,CAAC,KAAK,EAAE,CAAC;QAEzE,sEAAsE;QACtE,IAAIyG,UAAU;YACZ,MAAMoB,aAAa,IAAI1F,kBACrB,IAAIC,kBAAkB;gBACpB,qDAAqD;gBACrD0F,gBAAgB,CAAClC,WAAa+B,qBAAqBI,IAAI,CAACnC;YAC1D;YAGF4B,SAASQ,IAAI,CACX,IAAIlG,6BACF2E,UACAgB,YACAI,YACA,IAAI,CAACI,gBAAgB;YAGzBT,SAASQ,IAAI,CACX,IAAIjG,gCACF0E,UACAgB,YACAI,YACA,IAAI,CAACI,gBAAgB;QAG3B;QAEA,IAAIvB,QAAQ;YACV,0EAA0E;YAC1E,yEAAyE;YACzE,qEAAqE;YACrE,oBAAoB;YACpB,MAAMmB,aAAa,IAAI1F,kBACrB,IAAIC,kBAAkB;gBACpB,oDAAoD;gBACpD8F,kBAAkB,CAACC,OAASA,KAAKC,UAAU,CAAC;YAC9C;YAGFZ,SAASQ,IAAI,CACX,IAAIhG,+BAA+B0E,QAAQe,YAAYI;YAEzDL,SAASQ,IAAI,CACX,IAAI/F,gCAAgCyE,QAAQe,YAAYI;QAE5D;QAEA,OAAOL;IACT;IAEUa,aAAqB;QAC7B,OAAO;IACT;IAEA,MAAgBC,cAA6B;YAS3C;QARAtH,UAAU,WAAW,IAAI,CAACuH,OAAO;QACjCvH,UAAU,SAASV;QAEnB,MAAMkI,YAAY,IAAIzH,UAAU;YAAEwH,SAAS,IAAI,CAACA,OAAO;QAAC;QAExD,MAAM,KAAK,CAACD;QACZ,MAAM,IAAI,CAACd,QAAQ,CAACiB,MAAM;SAE1B,cAAA,IAAI,CAAC7D,KAAK,qBAAV,YAAYrB,OAAO;QACnB,IAAI,CAACqB,KAAK,GAAG3B;QAEb,4GAA4G;QAC5G,IAAI,CAACyF,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,6CAA6C;QAC7C3H,UAAU,UAAU,IAAI,CAAC0F,MAAM;QAC/B1F,UAAU,YAAY,IAAI,CAACyF,QAAQ;QACnCzF,UAAU,aAAawH;QAEvBxE,QAAQ4E,EAAE,CAAC,sBAAsB,CAACC;YAChC,IAAIrG,WAAWqG,SAAS;gBACtB,0EAA0E;gBAC1E,qDAAqD;gBACrD;YACF;YACA,IAAI,CAACC,yBAAyB,CAACD,QAAQ;QACzC;QACA7E,QAAQ4E,EAAE,CAAC,qBAAqB,CAACG;YAC/B,IAAI,CAACD,yBAAyB,CAACC,KAAK;QACtC;IACF;IAEA,MAAgBC,QAAQpD,QAAgB,EAAoB;QAC1D,IAAIqD;QACJ,IAAI;YACFA,iBAAiBrI,kBAAkBgF;QACrC,EAAE,OAAOmD,KAAK;YACZG,QAAQC,KAAK,CAACJ;YACd,wDAAwD;YACxD,sDAAsD;YACtD,yCAAyC;YACzC,OAAO;QACT;QAEA,IAAIpH,iBAAiBsH,iBAAiB;YACpC,OAAO/H,aACL,IAAI,CAACyF,GAAG,EACRsC,gBACA,IAAI,CAACtF,UAAU,CAAC+D,cAAc,EAC9B,OACAzB,IAAI,CAACmD;QACT;QAEA,IAAIC,UAAyB;QAC7B,IAAIC,YAA2B;QAE/B,IAAI,IAAI,CAAC5C,MAAM,EAAE;YACf2C,UAAU,MAAMnI,aACd,IAAI,CAACwF,MAAM,EACXuC,iBAAiB,SACjB,IAAI,CAACtF,UAAU,CAAC+D,cAAc,EAC9B;QAEJ;QAEA,IAAI,IAAI,CAACjB,QAAQ,EAAE;YACjB6C,YAAY,MAAMpI,aAChB,IAAI,CAACuF,QAAQ,EACbwC,gBACA,IAAI,CAACtF,UAAU,CAAC+D,cAAc,EAC9B;QAEJ;QACA,IAAI2B,WAAWC,WAAW;YACxB,OAAO;QACT;QAEA,OAAOF,QAAQC,WAAWC;IAC5B;IAEA,MAAMC,cAAcC,MAMnB,EAAE;QACD,IAAI;YACF,MAAMtD,SAAS,MAAM,KAAK,CAACqD,cAAc;gBACvC,GAAGC,MAAM;gBACTC,WAAW,CAACC;oBACV,IAAI,CAACZ,yBAAyB,CAACY,MAAM;gBACvC;YACF;YAEA,IAAI,cAAcxD,QAAQ;gBACxB,OAAOA;YACT;YAEAA,OAAOyD,SAAS,CAACC,KAAK,CAAC,CAACT;gBACtB,IAAI,CAACL,yBAAyB,CAACK,OAAO;YACxC;YACA,OAAOjD;QACT,EAAE,OAAOiD,OAAO;YACd,IAAIA,iBAAiB7H,aAAa;gBAChC,MAAM6H;YACR;YAEA;;;;OAIC,GACD,IAAI,CAAEA,CAAAA,iBAAiB5H,uBAAsB,GAAI;gBAC/C,IAAI,CAACuH,yBAAyB,CAACK;YACjC;YAEA,MAAMJ,MAAMrH,eAAeyH;YAC3BxG,oBAAoBoG,KAAKtI,eAAeoJ,UAAU;YAClD,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGR;YAEzC;;;;OAIC,GACD,IACEM,QAAQvC,GAAG,CAAC0C,QAAQ,CAAC,oBACrBH,QAAQvC,GAAG,CAAC0C,QAAQ,CAAC,qCACrBH,QAAQvC,GAAG,CAAC0C,QAAQ,CAAC,2BACrBH,QAAQvC,GAAG,CAAC0C,QAAQ,CAAC,6BACrB;gBACA,OAAO;oBAAEC,UAAU;gBAAM;YAC3B;YAEAH,SAASI,UAAU,GAAG;YACtB,MAAM,IAAI,CAACC,WAAW,CAACrB,KAAKe,SAASC,UAAUC,UAAUpE,QAAQ;YACjE,OAAO;gBAAEsE,UAAU;YAAK;QAC1B;IACF;IAEA,MAAMG,gBAAgBb,MAQrB,EAAE;QACD,IAAI;YACF,OAAO,KAAK,CAACa,gBAAgB;gBAC3B,GAAGb,MAAM;gBACTc,SAAS,CAACvB,MAAQ,IAAI,CAACD,yBAAyB,CAACC,KAAK;gBACtDU,WAAW,CAACC;oBACV,IAAI,CAACZ,yBAAyB,CAACY,MAAM;gBACvC;YACF;QACF,EAAE,OAAOP,OAAO;YACd,IAAIA,iBAAiB7H,aAAa;gBAChC,MAAM6H;YACR;YACA,IAAI,CAACL,yBAAyB,CAACK,OAAO;YACtC,MAAMJ,MAAMrH,eAAeyH;YAC3B,MAAM,EAAEoB,GAAG,EAAEC,GAAG,EAAEnD,IAAI,EAAE,GAAGmC;YAE3BgB,IAAIL,UAAU,GAAG;YACjB,MAAM,IAAI,CAACC,WAAW,CAACrB,KAAKwB,KAAKC,KAAKnD;YACtC,OAAO;QACT;IACF;IAEOoD,oBAAwC;QAC7C,MAAMC,UAAU,KAAK,CAACD;QAEtB,OAAO,CAACF,KAAKC,KAAKR;YAChB,MAAMF,UAAU,IAAI,CAACa,YAAY,CAACJ;YAClC,MAAMR,WAAW,IAAI,CAACa,YAAY,CAACJ;YACnC,MAAMK,gBAAgB,IAAI,CAAClH,UAAU,CAACmH,OAAO;YAE7C,IAAID,kBAAkB,OAAO;gBAC3B,MAAME,QAAQC,KAAKC,GAAG;gBACtB,MAAMC,sBAAsBrL,eAAe0K,KAAK;gBAEhD,IAAI,CAACW,qBAAqB;oBACxBnB,SAASoB,gBAAgB,CAACC,IAAI,CAAC,SAAS;wBACtC,oEAAoE;wBACpE,sEAAsE;wBACtE,gCAAgC;wBAChC,MAAMC,aAAaxL,eAAe0K,KAAKrD,KAAK;wBAE5C,IAAI,CAACmE,YAAY;4BACf;wBACF;wBAEAzI,YAAY;4BACVkH;4BACAC;4BACAc;4BACAS,qBAAqBN,KAAKC,GAAG,KAAKF;wBACpC;oBACF;gBACF;YACF;YAEA,OAAOL,QAAQZ,SAASC,UAAUC;QACpC;IACF;IAEA,MAAauB,cACXhB,GAAoB,EACpBC,GAAqB,EACrBR,SAAkC,EACnB;QACf,MAAMwB,OAAOvK,MAAM,kBAAkBgC,WAAW;YAAEsE,KAAKgD,IAAIhD,GAAG;QAAC;QAC/D,MAAMrB,SAAS,MAAMsF,KAAKC,YAAY,CAAC;gBAC/B;YAAN,QAAM,cAAA,IAAI,CAAC7G,KAAK,qBAAV,YAAY8G,OAAO;YACzB,OAAO,MAAM,KAAK,CAACH,cAAchB,KAAKC,KAAKR;QAC7C;QACA,MAAM2B,cAAc3H,QAAQ2H,WAAW;QACvCH,KACGI,UAAU,CAAC,gBAAgB;YAC1BrE,KAAKgD,IAAIhD,GAAG;YACZ,cAAcsE,OAAOF,YAAYG,GAAG;YACpC,mBAAmBD,OAAOF,YAAYI,QAAQ;YAC9C,oBAAoBF,OAAOF,YAAYK,SAAS;QAClD,GACCC,IAAI;QACP,OAAO/F;IACT;IAEA,MAAMgG,IACJ3B,GAAoB,EACpBC,GAAqB,EACrBR,SAA6B,EACd;YACT;QAAN,QAAM,cAAA,IAAI,CAACpF,KAAK,qBAAV,YAAY8G,OAAO;QAEzB,MAAM,EAAES,QAAQ,EAAE,GAAG,IAAI,CAACxI,UAAU;QACpC,IAAIyI,mBAAkC;QAEtC,gDAAgD;QAChD,IAAID,YAAYtL,cAAcmJ,UAAUpE,QAAQ,IAAI,KAAKuG,WAAW;YAClE,6CAA6C;YAC7C,uGAAuG;YACvGC,mBAAmBpC,UAAUpE,QAAQ;YACrCoE,UAAUpE,QAAQ,GAAG9E,iBAAiBkJ,UAAUpE,QAAQ,IAAI,KAAKuG;QACnE;QAEA,MAAM,EAAEvG,QAAQ,EAAE,GAAGoE;QAErB,IAAIpE,SAAUwC,UAAU,CAAC,WAAW;YAClC,IAAItI,GAAGuM,UAAU,CAACpM,SAAS,IAAI,CAACqM,SAAS,EAAE,WAAW;gBACpD,MAAM,qBAAyC,CAAzC,IAAI7H,MAAMrE,iCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,IAAIgM,kBAAkB;YACpB,oFAAoF;YACpF,mDAAmD;YACnDpC,UAAUpE,QAAQ,GAAGwG;QACvB;QACA,IAAI;YACF,OAAO,MAAM,KAAK,CAACF,IAAI3B,KAAKC,KAAKR;QACnC,EAAE,OAAOb,OAAO;YACd,MAAMJ,MAAMrH,eAAeyH;YAC3BvH,kBAAkBmH;YAClB,IAAI,CAACD,yBAAyB,CAACC;YAC/B,IAAI,CAACyB,IAAI+B,IAAI,EAAE;gBACb/B,IAAIL,UAAU,GAAG;gBACjB,IAAI;oBACF,OAAO,MAAM,IAAI,CAACC,WAAW,CAACrB,KAAKwB,KAAKC,KAAK5E,UAAW;wBACtD4G,aAAa,AAAC/K,QAAQsH,QAAQA,IAAI1B,IAAI,IAAKzB,YAAY;oBACzD;gBACF,EAAE,OAAO6G,aAAa;oBACpBvD,QAAQC,KAAK,CAACsD;oBACdjC,IAAIkC,IAAI,CAAC,yBAAyBC,IAAI;gBACxC;YACF;QACF;IACF;IAEU7D,0BACRC,GAAa,EACb6D,IAAyE,EACnE;QACN,IAAI,CAAC/H,cAAc,CAACiE,yBAAyB,CAACC,KAAK6D;IACrD;IAEUC,mBAA8C;QACtD,OACE3K,mBAAmBgB,OAAO,CACxBjD,SAAS,IAAI,CAAC6M,aAAa,EAAEvM,oBAC1B0C;IAET;IAEU8J,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACC,kBAAkB,CAACC,GAAG,EAAE,OAAOhK;QAEzC,OACEf,mBAAmBgB,OAAO,CACxBjD,SAAS,IAAI,CAAC6M,aAAa,EAAEtM,wBAC1ByC;IAET;IAEU0F,+BAAyC;QACjD,MAAMuE,WAAWzK,mCACf0K,OAAOC,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IACnC,IAAI,CAAC1J,UAAU,CAACwI,QAAQ,EACxBmB,GAAG,CAAC,CAACC,QAAU,IAAI3F,OAAOlF,iBAAiB,WAAW6K,OAAOC,KAAK;QAEpE,IAAI,IAAI,CAAC7J,UAAU,CAAC8J,MAAM,KAAK,YAAYP,SAAShI,MAAM,GAAG,GAAG;YAC9D1D,IAAI2H,KAAK,CACP;YAGFnF,QAAQ0J,IAAI,CAAC;QACf;QAEA,OAAOR,YAAY,EAAE;IACvB;IAEA,MAAgBS,gBAAgB;YAG1B;QAFJ,gCAAgC;QAChC,iCAAiC;QACjC,IAAI,EAAA,mBAAA,IAAI,CAACC,UAAU,qBAAf,iBAAiB1G,KAAK,MAAK,MAAM;YACnC,IAAI,CAAC0G,UAAU,CAAC1G,KAAK,GAAG5E,0BACtB,IAAI,CAACsL,UAAU,CAACpG,QAAQ,IAAI,EAAE;QAElC;QACA,OAAO,IAAI,CAACoG,UAAU;IACxB;IAEUC,sBAAsB;QAC9B,OAAO5K;IACT;IAEA,MAAgB6K,gBAAkC;QAChD,OAAO,IAAI,CAAC9E,OAAO,CAAC,IAAI,CAAC+E,oBAAoB;IAC/C;IAEA,MAAgBC,iBAAiBzG,GAAW,EAAE;QAC5C,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE,MAAM,IAAI,CAAC0G,oBAAoB;YAC/BzG,YAAY;YACZF,YAAYnE;YACZsE;QACF;IACF;IAEA,MAAgB0G,4BAA0C;QACxD,IAAIC;QACJ,IACE,IAAI,CAACC,6BAA6B,IACjC,MAAM,IAAI,CAAChH,UAAU,CAAC;YACrBE,MAAM,IAAI,CAAC8G,6BAA6B;YACxC7G,YAAY;YACZF,YAAYnE;QACd,GACGgD,IAAI,CAAC,IAAM,MACX2D,KAAK,CAAC,IAAM,QACf;YACA,IAAI;gBACFsE,wBAAwB,MAAMhL,QAC5BjD,SAAS,IAAI,CAACsI,OAAO,EAAE,UAAUpI;YAErC,EAAE,OAAO4I,KAAU;gBACjBA,IAAIqF,OAAO,GAAG,CAAC,sDAAsD,EAAErF,IAAIqF,OAAO,EAAE;gBACpF,MAAMrF;YACR;QACF;QACA,OAAOmF;IACT;IAEA,MAAgBG,oCAAoC;QAClD,MAAM,IAAI,CAACvJ,eAAe,CACvB8G,UAAU,CAAC,4BACXH,YAAY,CAAC;gBAAM,gCAAA;oBAAA,wBAAA,IAAI,CAAC6C,eAAe,sBAApB,iCAAA,sBAAsBC,QAAQ,qBAA9B,oCAAA;;IACxB;IAEA,MAAgBC,mBAAmB,EACjCnH,IAAI,EACJoH,QAAQ,EACRlH,GAAG,EAKJ,EAAE;QACD,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE;YACAoH;YACAnH,YAAY;YACZF,YAAYnE;YACZsE;QACF;IACF;IAEAmH,eAAeC,IAAc,EAAE;IAC7B,0FAA0F;IAC1F,uFAAuF;IACvF,mBAAmB;IACnB,sDAAsD;IACtD,mBAAmB;IACnB,wCAAwC;IACxC,sCAAsC;IACtC,+DAA+D;IAC/D,0CAA0C;IAC1C,eAAe;IACf,wBAAwB;IACxB,QAAQ;IACR,OAAO;IACP,KAAK;IACP;IAEAnI,4BACEb,IAAY,EACZiJ,KAAkD,EACzC;QACT,IAAIA,MAAMC,IAAI,KAAK,yBAAyB;YAC1C,OAAO;QACT;QAEA,MAAMC,gBAAgBnJ,KAAKoJ,KAAK,CAAC;QAEjC,IAAIC;QACJ,IACE,CAAEA,CAAAA,UAAUrJ,KAAKoJ,KAAK,CAAC,KAAK,CAACH,MAAMK,IAAI,GAAG,EAAE,AAAD,KAC3C,CAAED,CAAAA,UAAUA,QAAQE,SAAS,CAACN,MAAMO,GAAG,CAAA,GACvC;YACA,OAAO;QACT;QAEAH,UAAUA,UAAUF,cAAcM,KAAK,CAACR,MAAMK,IAAI,EAAEjP,IAAI,CAAC;QACzDgP,UAAUA,QAAQE,SAAS,CAAC,GAAGF,QAAQK,OAAO,CAAC;QAE/C,OAAO,CAACL,QAAQ/E,QAAQ,CAAC;IAC3B;IAEA,MAAgBqF,eAAe,EAC7B1J,QAAQ,EACR2J,cAAc,EACdlI,IAAI,EACJmI,SAAS,EAMV,EAGE;QACD,mDAAmD;QACnD,wDAAwD;QAExD,MAAMC,mBAAmB;YACvB,MAAM,EACJC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EACjB,GAAG,IAAI,CAAClM,UAAU;YACnB,MAAM,EAAEmM,OAAO,EAAEC,aAAa,EAAE,GAAG,IAAI,CAACpM,UAAU,CAACqM,IAAI,IAAI,CAAC;YAC5D,MAAMC,oBAAoB,IAAI,CAAC5M,oBAAoB;YAEnD,IAAI;oBA2BoB;gBA1BtB,MAAM6M,cAAc,MAAMD,kBAAkBE,eAAe,CAAC;oBAC1DxJ,KAAK,IAAI,CAACA,GAAG;oBACb4B,SAAS,IAAI,CAACA,OAAO;oBACrB3C;oBACAwK,QAAQ;wBACNC,WAAW,IAAI,CAAC1M,UAAU,CAACC,YAAY,CAAC0M,GAAG;wBAC3CZ;wBACAC;wBACAC;wBACAW,WAAWnH,QAAQ,IAAI,CAACzF,UAAU,CAACC,YAAY,CAAC2M,SAAS;oBAC3D;oBACAV;oBACAC;oBACAC;oBACA1I;oBACAmI;oBACAD;oBACAiB,cAAc,IAAI,CAAC7M,UAAU,CAAC6M,YAAY;oBAC1CC,eAAe,IAAI,CAAC9M,UAAU,CAACC,YAAY,CAAC6M,aAAa;oBACzDC,mBAAmB,IAAI,CAAC/M,UAAU,CAACC,YAAY,CAAC+M,SAAS;oBACzDC,qBAAqB,IAAI,CAACjN,UAAU,CAACC,YAAY,CAACgN,mBAAmB;oBACrEC,gBAAgB,IAAI,CAAClN,UAAU,CAACC,YAAY,CAACiN,cAAc;oBAC3DC,oBAAoB,IAAI,CAACnN,UAAU,CAACkD,kBAAkB;oBACtDkK,kBAAkB,IAAI,CAACpN,UAAU,CAAC8J,MAAM;oBACxCuD,SAAS,IAAI,CAACA,OAAO;oBACrBC,gBAAgB7H,QAAQ,IAAI,CAACzF,UAAU,CAACC,YAAY,CAACqN,cAAc;oBACnEC,YAAY9H,SAAQ,oCAAA,IAAI,CAACzF,UAAU,CAACC,YAAY,CAACuN,GAAG,qBAAhC,kCAAkCC,SAAS;gBACjE;gBACA,OAAOlB;YACT,SAAU;gBACR,kDAAkD;gBAClDD,kBAAkBoB,GAAG;YACvB;QACF;QACA,MAAMnL,SAAS,IAAI,CAACjB,gBAAgB,CAACqM,GAAG,CAAC1L;QAEzC,MAAM2L,aAAanQ,oBAAoBqO,kBACrC,CAAC,YAAY,EAAE7J,UAAU,EACzB,EAAE,EAEDK,IAAI,CAAC,CAACuE;YACL,MAAM,EAAEgH,mBAAmBlM,WAAW,EAAEmM,cAAcC,QAAQ,EAAE,GAC9DlH,IAAIrF,KAAK;YACX,IAAI,CAACqK,aAAa,IAAI,CAAC7L,UAAU,CAAC8J,MAAM,KAAK,UAAU;gBACrD,IAAIiE,aAAa7O,aAAa8O,sBAAsB,EAAE;oBACpD,MAAM,qBAEL,CAFK,IAAIlN,MACR,oKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAIiN,aAAa7O,aAAa+O,SAAS,EAAE;oBAC9C,MAAM,qBAEL,CAFK,IAAInN,MACR,gKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAMU,QAGF;gBACFG,WAAW,EAAEA,+BAAAA,YAAagI,GAAG,CAAC,CAACC,QAAUA,MAAM3H,QAAQ;gBACvD6L,cAAcC;YAChB;YACA,IAAI,CAACzM,gBAAgB,CAAC4M,GAAG,CAACjM,UAAUT;YACpC,OAAOA;QACT,GACCyE,KAAK,CAAC,CAACb;YACN,IAAI,CAAC9D,gBAAgB,CAAC6M,MAAM,CAAClM;YAC7B,IAAI,CAACM,QAAQ,MAAM6C;YACnBvH,IAAI2H,KAAK,CAAC,CAAC,oCAAoC,EAAEvD,SAAS,CAAC,CAAC;YAC5DsD,QAAQC,KAAK,CAACJ;QAChB;QAEF,IAAI7C,QAAQ;YACV,OAAOA;QACT;QACA,OAAOqL;IACT;IAEA,MAAgBpK,WAAW4K,IAM1B,EAAiB;QAChB,MAAM,IAAI,CAAClN,cAAc,CAACsC,UAAU,CAAC4K;IACvC;IAEA,MAAgBC,mBAAmB,EACjCC,MAAM,EACN5K,IAAI,EACJ6K,KAAK,EACL1I,MAAM,EACNgG,SAAS,EACTf,WAAW,IAAI,EACf0D,YAAY,EACZ5K,GAAG,EAWJ,EAAwC;YACjC;QAAN,QAAM,cAAA,IAAI,CAAC3C,KAAK,qBAAV,YAAY8G,OAAO;QAEzB,MAAM0G,iBAAiB,MAAM,IAAI,CAACC,mBAAmB,CAAChL;QACtD,IAAI+K,gBAAgB;YAClB,wDAAwD;YACxD,MAAM,IAAIzR,kBAAkByR;QAC9B;QACA,IAAID,gBAAgB,IAAI,CAACG,aAAa,CAACC,YAAY,EAAE;YACnD,MAAM,IAAI,CAACpL,UAAU,CAAC;gBACpBE;gBACAoH;gBACAnH,YAAY;gBACZF,YAAYnE;gBACZsE;YACF;QACF;QAEA,IAAI,CAACiL,gBAAgB,GAAG,KAAK,CAAC3E;QAE9B,OAAO,MAAM,KAAK,CAACmE,mBAAmB;YACpC3K;YACA6K;YACA1I;YACAyI;YACAzC;YACA2C;YACA5K;QACF;IACF;IAEA,MAAgBkL,2BACdlL,GAAY,EAC8B;QAC1C,MAAM,IAAI,CAAC1C,cAAc,CAAC4N,0BAA0B,CAAClL;QACrD,OAAO,MAAMlG,2BAA2B,IAAI,CAACkH,OAAO;IACtD;IAEA,MAAM8J,oBAAoBhL,IAAY,EAAgB;QACpD,OAAO,MAAM,IAAI,CAACxC,cAAc,CAACwN,mBAAmB,CAAChL;IACvD;IAEA,MAAgBqL,8BACd,GAAGC,IAAqD,EACxD;QACA,MAAM,KAAK,CAACD,iCAAiCC;QAE7C,MAAM5J,MAAM4J,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC7J,yBAAyB,CAACC,KAAK;IACtC;AACF"}