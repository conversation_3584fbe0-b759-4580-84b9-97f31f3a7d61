"use client"

import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Divider,
  Paper,
  Tab,
  Tabs,
  useTheme,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  alpha,
  CardHeader,
  CardActions,
  Skeleton,
} from "@mui/material"
import {
  Receipt as FactureIcon,
  Description as DevisIcon,
  People as ClientIcon,
  AttachMoney as RevenueIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
  MoreVert as MoreVertIcon,
  DateRange as DateRangeIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  ArrowForward as ArrowForwardIcon,
  RemoveRedEye as RemoveRedEyeIcon,
  GetApp as GetAppIcon,
  MailOutline as MailOutlineIcon,
} from "@mui/icons-material"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rid, <PERSON>lt<PERSON> as <PERSON><PERSON>rts<PERSON><PERSON><PERSON>, ResponsiveContainer, LineChart, Line, Legend, PieChart, Pie, Cell, AreaChart, Area } from "recharts"
import factureService from "../services/factureService"
import devisService from "../services/devisService"
import clientService from "../services/clientService"
import { useAuth } from "../contexts/AuthContext"
import { formatCurrency, formatDate } from "../utils/formatters"
import SimpleDateFilter from "../components/SimpleDateFilter"

// Styled components
const StatsCard = ({ icon, title, value, subtitle, color, trend, onClick }) => {
  const theme = useTheme();

  return (
    <Card
      onClick={onClick}
      sx={{
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: 90,
          height: 90,
          background: `linear-gradient(135deg, transparent 0%, transparent 50%, ${alpha(theme.palette[color].light, 0.1)} 50%, ${alpha(theme.palette[color].light, 0.1)} 100%)`,
          zIndex: 0,
        }}
      />
      <CardContent sx={{ position: 'relative', zIndex: 1, height: '100%' }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", mb: 2 }}>
          <Avatar
            variant="rounded"
            sx={{
              backgroundColor: alpha(theme.palette[color].main, 0.1),
              color: theme.palette[color].main,
              width: 48,
              height: 48,
              p: 1,
              borderRadius: 2,
            }}
          >
            {icon}
          </Avatar>
          {trend && (
            <Chip
              icon={<TrendingUpIcon fontSize="small" />}
              label={trend}
              size="small"
              color={color}
              sx={{
                fontWeight: 600,
                borderRadius: '6px',
                height: 24,
              }}
            />
          )}
        </Box>
        <Typography variant="h4" component="div" sx={{ fontWeight: 700, mb: 0.5 }}>
          {value}
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {title}
        </Typography>
        <Typography
          variant="caption"
          sx={{
            display: 'flex',
            alignItems: 'center',
            color: 'text.secondary',
            mt: 1,
            fontWeight: 600
          }}
        >
          {subtitle}
        </Typography>
      </CardContent>
    </Card>
  )
}

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState(null)
  const [chartData, setChartData] = useState([])
  const [tabValue, setTabValue] = useState(0)
  const [timeRange, setTimeRange] = useState("month")
  const [periodMenuAnchorEl, setPeriodMenuAnchorEl] = useState(null)
  const [chartType, setChartType] = useState("revenue") // revenue, invoices, clients
  const theme = useTheme()
  const { currentUser } = useAuth()

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [factures, devis, clients] = await Promise.all([
          factureService.getFactures(),
          devisService.getDevis(),
          clientService.getClients(),
        ])

        // Calcul des statistiques
        const paidInvoices = factures.filter((f) => f.statut === "PAID")
        const totalRevenue = paidInvoices.reduce((sum, f) => sum + f.total * 1.2, 0)
        const pendingInvoices = factures.filter((f) => f.statut === "SENT")
        const overdueInvoices = pendingInvoices.filter(
          (f) => new Date(f.dateÉmission) < new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        )

        setStats({
          factures: factures.length,
          paidInvoices: paidInvoices.length,
          pendingInvoices: pendingInvoices.length,
          overdueInvoices: overdueInvoices.length,
          devis: devis.length,
          acceptedDevis: devis.filter((d) => d.statut === "ACCEPTED").length,
          clients: clients.length,
          totalRevenue,
        })

        // Generate chart data with realistic trends
        generateChartData(timeRange);
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
      }
    }

    if (currentUser) {
      fetchData()
    }
  }, [currentUser, timeRange])

  const generateChartData = (range) => {
    let data = [];
    const currentYear = new Date().getFullYear();

    if (range === "month") {
      // Last 30 days data
      const daysInMonth = 30;
      for (let i = 0; i < daysInMonth; i++) {
        const day = new Date();
        day.setDate(day.getDate() - (daysInMonth - i - 1));

        // Create some realistic data patterns
        let revenue = 1000 + Math.floor(Math.random() * 3000);

        // Add weekly patterns (higher on certain days)
        if (day.getDay() === 1 || day.getDay() === 4) { // Monday and Thursday
          revenue *= 1.4;
        }

        // Add some randomness
        revenue = Math.floor(revenue * (0.85 + Math.random() * 0.3));

        data.push({
          name: formatDate(day),
          revenue: revenue,
          invoices: Math.floor(revenue / 1000),
          clients: Math.floor(revenue / 3000) + (Math.random() > 0.7 ? 1 : 0),
          year: currentYear,
        });
      }
    } else if (range === "quarter") {
      // Last 3 months weekly data
      const weeks = 12;
      for (let i = 0; i < weeks; i++) {
        const week = new Date();
        week.setDate(week.getDate() - (weeks - i - 1) * 7);

        // Create some realistic data patterns with growth trend
        let base = 4000 + (i * 200); // Increasing trend
        let revenue = base + Math.floor(Math.random() * 4000);

        data.push({
          name: `S${i+1}`,
          revenue: revenue,
          invoices: Math.floor(revenue / 800),
          clients: Math.floor(revenue / 4000) + (Math.random() > 0.6 ? 1 : 0),
          year: currentYear,
        });
      }
    } else if (range === "year") {
      // Monthly data for the year
      const months = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul", "Aoû", "Sep", "Oct", "Nov", "Déc"];
      for (let i = 0; i < 12; i++) {
        // Create seasonal patterns (Q4 is usually higher)
        let seasonalFactor = 1;
        if (i >= 9) { // Q4 (Oct-Dec)
          seasonalFactor = 1.5;
        } else if (i <= 2) { // Q1 (Jan-Mar)
          seasonalFactor = 0.8;
        }

        let revenue = 15000 + Math.floor(Math.random() * 10000 * seasonalFactor);

        data.push({
          name: months[i],
          revenue: revenue,
          invoices: Math.floor(revenue / 3000),
          clients: Math.floor(revenue / 15000) + Math.floor(Math.random() * 3),
          year: currentYear,
        });
      }
    }

    setChartData(data);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
    setPeriodMenuAnchorEl(null);
    generateChartData(range);
  };

  const handlePeriodMenuOpen = (event) => {
    setPeriodMenuAnchorEl(event.currentTarget);
  };

  const handlePeriodMenuClose = () => {
    setPeriodMenuAnchorEl(null);
  };

  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  // Recent Activities
  const recentActivities = [
    {
      id: 1,
      type: "invoice_paid",
      title: "Facture payée",
      description: "FAC-00123 - Société Dupont",
      time: "Il y a 25 min",
      amount: 1250.50,
      icon: <CheckCircleIcon sx={{ color: theme.palette.success.main }} />
    },
    {
      id: 2,
      type: "quote_accepted",
      title: "Devis accepté",
      description: "DEV-00234 - Jean Martin",
      time: "Il y a 3 heures",
      amount: 890.00,
      icon: <CheckCircleIcon sx={{ color: theme.palette.info.main }} />
    },
    {
      id: 3,
      type: "invoice_sent",
      title: "Facture envoyée",
      description: "FAC-00124 - Entreprise ABC",
      time: "Il y a 1 jour",
      amount: 1875.00,
      icon: <MailOutlineIcon sx={{ color: theme.palette.primary.main }} />
    },
    {
      id: 4,
      type: "invoice_overdue",
      title: "Facture en retard",
      description: "FAC-00095 - Client XYZ",
      time: "Il y a 2 jours",
      amount: 540.00,
      icon: <WarningIcon sx={{ color: theme.palette.warning.main }} />
    },
    {
      id: 5,
      type: "quote_sent",
      title: "Devis envoyé",
      description: "DEV-00235 - Marie Dubois",
      time: "Il y a 3 jours",
      amount: 2100.00,
      icon: <MailOutlineIcon sx={{ color: theme.palette.primary.main }} />
    }
  ];

  // Date ranges in French
  const dateRanges = {
    month: "Ce mois",
    quarter: "Ce trimestre",
    year: "Cette année"
  };

  // Chart colors
  const CHART_COLORS = {
    revenue: theme.palette.primary.main,
    revenue2: theme.palette.primary.light,
    invoices: theme.palette.secondary.main,
    clients: theme.palette.success.main,
  };

  // Status distribution
  const statusData = [
    { name: "Payée", value: 45, color: theme.palette.success.main },
    { name: "En attente", value: 30, color: theme.palette.warning.main },
    { name: "En retard", value: 15, color: theme.palette.error.main },
    { name: "Brouillon", value: 10, color: theme.palette.grey[500] },
  ];

  // Top clients
  const topClients = [
    { name: "Société Dupont", revenue: 12500, invoices: 5 },
    { name: "Entreprise ABC", revenue: 8900, invoices: 4 },
    { name: "Client XYZ", revenue: 7500, invoices: 3 },
    { name: "Jean Martin", revenue: 6200, invoices: 2 },
  ];

  if (!stats) {
    return (
      <Box sx={{ p: 3 }}>
        <Stack spacing={4}>
          <Box>
            <Skeleton variant="text" width="250px" height={40} sx={{ mb: 2 }} />
            <Skeleton variant="text" width="180px" height={24} />
          </Box>
          <Grid container spacing={3}>
            {[1, 2, 3, 4].map((item) => (
              <Grid item xs={12} sm={6} md={3} key={item}>
                <Skeleton variant="rectangular" height={160} sx={{ borderRadius: 3 }} />
              </Grid>
            ))}
          </Grid>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 3 }} />
            </Grid>
            <Grid item xs={12} md={4}>
              <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 3 }} />
            </Grid>
          </Grid>
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "flex-start",
        flexWrap: "wrap",
        mb: 2
      }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Tableau de Bord
          </Typography>
          {currentUser && (
            <Typography variant="subtitle1" color="text.secondary">
              Bienvenue, <Box component="span" sx={{ fontWeight: 600 }}>{currentUser.name}</Box>
            </Typography>
          )}
        </Box>

        <Box>
          <SimpleDateFilter
            onDateRangeChange={(rangeId, dateRange) => {
              // Map the new range format to the existing timeRange format
              let newTimeRange;
              switch(rangeId) {
                case 'thisMonth':
                  newTimeRange = 'month';
                  break;
                case 'thisQuarter':
                  newTimeRange = 'quarter';
                  break;
                case 'thisYear':
                  newTimeRange = 'year';
                  break;
                default:
                  // For custom ranges or other predefined ranges
                  newTimeRange = 'month';
              }
              handleTimeRangeChange(newTimeRange);
            }}
            onRefresh={() => generateChartData(timeRange)}
            initialRange={
              timeRange === 'month' ? 'thisMonth' :
              timeRange === 'quarter' ? 'thisQuarter' :
              timeRange === 'year' ? 'thisYear' : 'thisMonth'
            }
            compact={true}
          />
        </Box>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Carte Revenus */}
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            icon={<RevenueIcon fontSize="large" />}
            title="Total des revenus"
            value={formatCurrency(stats.totalRevenue)}
            subtitle={`${stats.paidInvoices} factures payées`}
            color="primary"
            trend="+12%"
            onClick={() => navigate("/factures?status=paid")}
          />
        </Grid>

        {/* Carte Factures */}
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            icon={<FactureIcon fontSize="large" />}
            title="Factures"
            value={stats.factures}
            subtitle={`${stats.pendingInvoices} en attente • ${stats.overdueInvoices} en retard`}
            color="secondary"
            trend="+5%"
            onClick={() => navigate("/factures")}
          />
        </Grid>

        {/* Carte Devis */}
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            icon={<DevisIcon fontSize="large" />}
            title="Devis"
            value={stats.devis}
            subtitle={`${stats.acceptedDevis} acceptés`}
            color="success"
            trend="+8%"
            onClick={() => navigate("/devis")}
          />
        </Grid>

        {/* Carte Clients */}
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            icon={<ClientIcon fontSize="large" />}
            title="Clients"
            value={stats.clients}
            subtitle="5 nouveaux ce mois"
            color="info"
            trend="+15%"
            onClick={() => navigate("/clients")}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Graphique principal */}
        <Grid item xs={12} lg={8}>
          <Card sx={{
            height: "100%",
            minHeight: 400,
          }}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Évolution des {chartType === "revenue" ? "revenus" : chartType === "invoices" ? "factures" : "clients"}
                  </Typography>
                  <Box>
                    <Button
                      size="small"
                      variant={chartType === "revenue" ? "contained" : "outlined"}
                      onClick={() => handleChartTypeChange("revenue")}
                      sx={{
                        mr: 1,
                        fontWeight: 600,
                        minWidth: 'auto'
                      }}
                    >
                      Revenus
                    </Button>
                    <Button
                      size="small"
                      variant={chartType === "invoices" ? "contained" : "outlined"}
                      onClick={() => handleChartTypeChange("invoices")}
                      sx={{
                        mr: 1,
                        fontWeight: 600,
                        minWidth: 'auto'
                      }}
                    >
                      Factures
                    </Button>
                    <Button
                      size="small"
                      variant={chartType === "clients" ? "contained" : "outlined"}
                      onClick={() => handleChartTypeChange("clients")}
                      sx={{
                        fontWeight: 600,
                        minWidth: 'auto'
                      }}
                    >
                      Clients
                    </Button>
                  </Box>
                </Box>
              }
            />
            <Divider />
            <CardContent sx={{ height: 360 }}>
              <ResponsiveContainer width="100%" height="100%">
                {chartType === "revenue" ? (
                  <AreaChart data={chartData}>
                    <defs>
                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={CHART_COLORS.revenue} stopOpacity={0.8}/>
                        <stop offset="95%" stopColor={CHART_COLORS.revenue} stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis
                      dataKey="name"
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <YAxis
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                      tickFormatter={(value) => `${new Intl.NumberFormat('fr-FR', { notation: 'compact', compactDisplay: 'short' }).format(value)} €`}
                    />
                    <RechartsTooltip
                      formatter={(value) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value)}
                      labelFormatter={(label) => `Date: ${label}`}
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: theme.shadows[3]
                      }}
                      itemStyle={{ color: theme.palette.text.primary }}
                      labelStyle={{ color: theme.palette.text.secondary, fontWeight: 600 }}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke={CHART_COLORS.revenue}
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorRevenue)"
                      activeDot={{ r: 6, fill: CHART_COLORS.revenue }}
                      name="Revenus"
                    />
                  </AreaChart>
                ) : chartType === "invoices" ? (
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis
                      dataKey="name"
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <YAxis
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <RechartsTooltip
                      formatter={(value) => `${value} factures`}
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: theme.shadows[3]
                      }}
                      itemStyle={{ color: theme.palette.text.primary }}
                      labelStyle={{ color: theme.palette.text.secondary, fontWeight: 600 }}
                    />
                    <Bar
                      dataKey="invoices"
                      fill={CHART_COLORS.invoices}
                      radius={[4, 4, 0, 0]}
                      barSize={timeRange === "month" ? 15 : 30}
                      name="Factures"
                    />
                  </BarChart>
                ) : (
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis
                      dataKey="name"
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <YAxis
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <RechartsTooltip
                      formatter={(value) => `${value} clients`}
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: theme.shadows[3]
                      }}
                      itemStyle={{ color: theme.palette.text.primary }}
                      labelStyle={{ color: theme.palette.text.secondary, fontWeight: 600 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="clients"
                      stroke={CHART_COLORS.clients}
                      strokeWidth={2}
                      dot={{ stroke: CHART_COLORS.clients, strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, fill: CHART_COLORS.clients }}
                      name="Clients"
                    />
                  </LineChart>
                )}
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Activités récentes et statistiques */}
        <Grid item xs={12} lg={4}>
          <Stack spacing={3} sx={{ height: '100%' }}>
            {/* Statut des factures */}
            <Card sx={{ height: "100%" }}>
              <CardHeader
                title={
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Statut des factures
                  </Typography>
                }
              />
              <Divider />
              <CardContent sx={{ pt: 1, height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={statusData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      labelLine={false}
                    >
                      {statusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip
                      formatter={(value) => `${value} factures (${(value / statusData.reduce((sum, item) => sum + item.value, 0) * 100).toFixed(0)}%)`}
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: theme.shadows[3]
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Activités récentes */}
            <Card sx={{ height: "100%" }}>
              <CardHeader
                title={
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Activités récentes
                  </Typography>
                }
                action={
                  <IconButton>
                    <MoreVertIcon />
                  </IconButton>
                }
              />
              <Divider />
              <CardContent sx={{ p: 0, pb: 0, overflowY: 'auto', maxHeight: 310 }}>
                <List disablePadding>
                  {recentActivities.map((activity) => (
                    <ListItem
                      key={activity.id}
                      alignItems="flex-start"
                      sx={{
                        px: 3,
                        py: 1.5,
                        '&:not(:last-child)': {
                          borderBottom: `1px solid ${theme.palette.divider}`,
                        },
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            bgcolor: 'transparent'
                          }}
                        >
                          {activity.icon}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {activity.title}
                            </Typography>
                            <Typography variant="subtitle2" color="text.secondary">
                              {formatCurrency(activity.amount)}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography variant="body2" color="text.primary" sx={{ mb: 0.5 }}>
                              {activity.description}
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                                fontWeight: 500
                              }}
                            >
                              <CalendarIcon fontSize="inherit" />
                              {activity.time}
                            </Typography>
                          </>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
              <CardActions sx={{ justifyContent: 'center', py: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
                <Button
                  endIcon={<ArrowForwardIcon />}
                  sx={{ fontWeight: 600 }}
                >
                  Voir toutes les activités
                </Button>
              </CardActions>
            </Card>
          </Stack>
        </Grid>

        {/* Meilleurs clients */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: "100%" }}>
            <CardHeader
              title={
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Top clients
                </Typography>
              }
              action={
                <IconButton>
                  <MoreVertIcon />
                </IconButton>
              }
            />
            <Divider />
            <CardContent sx={{ p: 0, pb: 0 }}>
              <List disablePadding>
                {topClients.map((client, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      px: 3,
                      py: 1.5,
                      '&:not(:last-child)': {
                        borderBottom: `1px solid ${theme.palette.divider}`,
                      },
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                      },
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: theme.palette.primary.main,
                          color: '#fff',
                          width: 36,
                          height: 36,
                          fontSize: 16,
                          fontWeight: 600,
                        }}
                      >
                        {client.name.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {client.name}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary">
                          {client.invoices} factures
                        </Typography>
                      }
                    />
                    <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                      {formatCurrency(client.revenue)}
                    </Typography>
                  </ListItem>
                ))}
              </List>
            </CardContent>
            <CardActions sx={{ justifyContent: 'center', py: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Button
                endIcon={<ArrowForwardIcon />}
                sx={{ fontWeight: 600 }}
              >
                Voir tous les clients
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Factures à payer */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: "100%" }}>
            <CardHeader
              title={
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Factures en attente
                </Typography>
              }
              action={
                <Chip
                  label="3 en retard"
                  color="error"
                  size="small"
                  sx={{ fontWeight: 600 }}
                />
              }
            />
            <Divider />
            <CardContent sx={{ p: 0, pb: 0 }}>
              <List disablePadding>
                {[...Array(4)].map((_, index) => (
                  <ListItem
                    key={index}
                    secondaryAction={
                      <Box>
                        <IconButton size="small" color="primary" sx={{ mr: 0.5 }}>
                          <RemoveRedEyeIcon fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="primary">
                          <GetAppIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    }
                    sx={{
                      px: 3,
                      py: 1.5,
                      '&:not(:last-child)': {
                        borderBottom: `1px solid ${theme.palette.divider}`,
                      },
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                      },
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            FAC-{(10120 + index).toString().padStart(5, '0')}
                          </Typography>
                          {index === 0 && (
                            <Chip
                              label="En retard"
                              color="error"
                              size="small"
                              sx={{
                                height: 20,
                                fontSize: '0.65rem',
                                fontWeight: 600
                              }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="caption" color="text.secondary">
                            Échéance: {formatDate(new Date(Date.now() + (index * 7 * 24 * 60 * 60 * 1000)))}
                          </Typography>
                          <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                            {formatCurrency(1250 + index * 800)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
            <CardActions sx={{ justifyContent: 'center', py: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Button
                endIcon={<ArrowForwardIcon />}
                sx={{ fontWeight: 600 }}
              >
                Voir toutes les factures
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Devis récents */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: "100%" }}>
            <CardHeader
              title={
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Devis récents
                </Typography>
              }
              action={
                <Chip
                  label="4 en attente"
                  color="warning"
                  size="small"
                  sx={{ fontWeight: 600 }}
                />
              }
            />
            <Divider />
            <CardContent sx={{ p: 0, pb: 0 }}>
              <List disablePadding>
                {[...Array(4)].map((_, index) => (
                  <ListItem
                    key={index}
                    secondaryAction={
                      <Box>
                        <IconButton size="small" color="primary" sx={{ mr: 0.5 }}>
                          <RemoveRedEyeIcon fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="primary">
                          <GetAppIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    }
                    sx={{
                      px: 3,
                      py: 1.5,
                      '&:not(:last-child)': {
                        borderBottom: `1px solid ${theme.palette.divider}`,
                      },
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                      },
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            DEV-{(20120 + index).toString().padStart(5, '0')}
                          </Typography>
                          {index === 1 && (
                            <Chip
                              label="Accepté"
                              color="success"
                              size="small"
                              sx={{
                                height: 20,
                                fontSize: '0.65rem',
                                fontWeight: 600
                              }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="caption" color="text.secondary">
                            Envoyé: {formatDate(new Date(Date.now() - (index * 3 * 24 * 60 * 60 * 1000)))}
                          </Typography>
                          <Typography variant="subtitle2" color="primary.main" sx={{ fontWeight: 600 }}>
                            {formatCurrency(1950 + index * 500)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
            <CardActions sx={{ justifyContent: 'center', py: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Button
                endIcon={<ArrowForwardIcon />}
                sx={{ fontWeight: 600 }}
              >
                Voir tous les devis
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard