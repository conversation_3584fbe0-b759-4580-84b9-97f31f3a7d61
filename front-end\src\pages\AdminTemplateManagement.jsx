import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  CardMedia,
  CardActionArea,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Paper,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assignment as AssignIcon,
  Business as BusinessIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import { baseTemplateService } from '../services/baseTemplateService';

const AdminTemplateManagement = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    type: 'facture',
    layout: 'standard'
  });

  useEffect(() => {
    fetchTemplates();
  }, [activeTab]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const type = activeTab === 'all' ? null : activeTab;
      const data = await baseTemplateService.getAllTemplates(type);
      setTemplates(data);
    } catch (err) {
      setError('Erreur lors du chargement des templates');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleOpenDialog = (template = null) => {
    if (template) {
      setEditingTemplate(template);
      setFormData({
        name: template.name,
        type: template.type,
        layout: template.layout
      });
    } else {
      setEditingTemplate(null);
      setFormData({
        name: '',
        type: 'facture',
        layout: 'standard'
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingTemplate(null);
  };

  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.name.trim()) {
        setError('Le nom du template est requis');
        return;
      }

      // Create the template data with simplified structure
      const templateData = {
        name: formData.name.trim(),
        type: formData.type,
        layout: formData.layout,
        isActive: true
      };

      if (editingTemplate) {
        await baseTemplateService.updateTemplate(editingTemplate._id, templateData);
      } else {
        await baseTemplateService.createTemplate(templateData);
      }
      handleCloseDialog();
      fetchTemplates();
      setError(null);
    } catch (err) {
      setError('Erreur lors de la sauvegarde du template');
      console.error(err);
    }
  };

  const handleDelete = async (templateId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce template ?')) {
      try {
        await baseTemplateService.deleteTemplate(templateId);
        fetchTemplates();
      } catch (err) {
        setError('Erreur lors de la suppression du template');
        console.error(err);
      }
    }
  };

  const filteredTemplates = templates.filter(template => {
    if (activeTab === 'all') return true;
    return template.type === activeTab;
  });

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
            Gestion des Templates de Base
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
            Créez et gérez les modèles de base pour les factures et devis
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            borderRadius: 2,
            px: 3,
            py: 1.5,
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            boxShadow: '0 3px 5px 2px rgba(25, 118, 210, .3)',
            '&:hover': {
              background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
            }
          }}
        >
          Nouveau Template
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card sx={{ mb: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.1)' }}>
        <CardContent sx={{ p: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              mb: 3,
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem'
              }
            }}
          >
            <Tab
              label="Tous les templates"
              value="all"
              icon={<BusinessIcon />}
              iconPosition="start"
            />
            <Tab
              label="Templates Facture"
              value="facture"
              icon={<DescriptionIcon />}
              iconPosition="start"
            />
            <Tab
              label="Templates Devis"
              value="devis"
              icon={<DescriptionIcon />}
              iconPosition="start"
            />
          </Tabs>

          <Grid container spacing={3}>
            {filteredTemplates.map((template) => (
              <Grid item xs={12} sm={6} md={4} key={template._id}>
                <Card sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardActionArea>
                    <Box sx={{
                      height: 200,
                      bgcolor: '#f8f9fa',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative'
                    }}>
                      <Box sx={{
                        width: '80%',
                        height: '80%',
                        bgcolor: 'white',
                        borderRadius: 1,
                        border: '2px solid #e0e0e0',
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1
                      }}>
                        {/* Template Preview */}
                        <Box sx={{
                          display: 'flex',
                          justifyContent: template.layout === 'standard' ? 'space-between' : 'flex-start',
                          alignItems: 'center',
                          mb: 1,
                          p: 1,
                          bgcolor: template.layout === 'standard' ? '#1976d2' : 'transparent',
                          color: template.layout === 'standard' ? 'white' : 'black',
                          borderRadius: 0.5
                        }}>
                          {template.layout === 'moderne' && (
                            <Box sx={{ width: 20, height: 20, bgcolor: '#1976d2', borderRadius: 0.5, mr: 1 }} />
                          )}
                          <Typography variant="caption" sx={{ fontSize: '0.6rem' }}>
                            {template.type.toUpperCase()}
                          </Typography>
                          {template.layout === 'standard' && (
                            <Box sx={{ width: 20, height: 20, bgcolor: 'white', borderRadius: 0.5 }} />
                          )}
                        </Box>
                        <Box sx={{
                          flex: 1,
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 0.5
                        }}>
                          {[1,2,3].map(i => (
                            <Box key={i} sx={{
                              height: 8,
                              bgcolor: template.layout === 'standard' ? '#f0f0f0' : '#e3f2fd',
                              borderRadius: 0.25,
                              border: template.layout === 'moderne' ? '1px solid #1976d2' : 'none'
                            }} />
                          ))}
                        </Box>
                      </Box>
                      <Chip
                        label={template.layout.toUpperCase()}
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          bgcolor: template.layout === 'standard' ? '#4caf50' : '#ff9800',
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Box>
                  </CardActionArea>
                  <CardContent sx={{ flexGrow: 1, p: 2 }}>
                    <Typography variant="h6" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
                      {template.name}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip
                        label={template.type.toUpperCase()}
                        color="primary"
                        size="small"
                        sx={{ fontWeight: 600 }}
                      />
                      <Chip
                        label={template.layout === 'standard' ? 'Logo à droite' : 'Logo à gauche'}
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Style: {template.layout === 'standard' ? 'En-têtes colorés, tableaux rayés' : 'Design minimal, tableaux bordés'}
                    </Typography>
                  </CardContent>
                  <Divider />
                  <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      {template.isActive ? 'Actif' : 'Inactif'}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Modifier">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(template)}
                          sx={{
                            color: '#1976d2',
                            '&:hover': { bgcolor: 'rgba(25, 118, 210, 0.1)' }
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Supprimer">
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(template._id)}
                          sx={{
                            color: '#d32f2f',
                            '&:hover': { bgcolor: 'rgba(211, 47, 47, 0.1)' }
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>

          {filteredTemplates.length === 0 && !loading && (
            <Paper sx={{
              textAlign: 'center',
              py: 6,
              bgcolor: '#f8f9fa',
              borderRadius: 2,
              border: '2px dashed #e0e0e0'
            }}>
              <BusinessIcon sx={{ fontSize: 48, color: '#bdbdbd', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Aucun template trouvé
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Créez votre premier template pour commencer
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog()}
                sx={{ borderRadius: 2 }}
              >
                Créer un template
              </Button>
            </Paper>
          )}
        </CardContent>
      </Card>

      {/* Simplified Dialog for creating/editing templates */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 3 }
        }}
      >
        <DialogTitle sx={{
          pb: 1,
          background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
          color: 'white',
          textAlign: 'center'
        }}>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            {editingTemplate ? 'Modifier le Template' : 'Nouveau Template'}
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 0.5 }}>
            {editingTemplate ? 'Modifiez les informations du template' : 'Créez un nouveau modèle de base'}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Grid container spacing={3} sx={{ mt: 0.5 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nom du template"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Template Standard Facture"
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Type de document</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  label="Type de document"
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="facture">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DescriptionIcon color="primary" />
                      Facture
                    </Box>
                  </MenuItem>
                  <MenuItem value="devis">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DescriptionIcon color="secondary" />
                      Devis
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Style de mise en page</InputLabel>
                <Select
                  value={formData.layout}
                  onChange={(e) => setFormData({ ...formData, layout: e.target.value })}
                  label="Style de mise en page"
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="standard">
                    <Box sx={{ display: 'flex', flexDirection: 'column', py: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        Standard
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Logo à droite, en-têtes colorés, tableaux rayés
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="moderne">
                    <Box sx={{ display: 'flex', flexDirection: 'column', py: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        Moderne
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Logo à gauche, design minimal, tableaux bordés
                      </Typography>
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Preview Section */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
              Aperçu du style:
            </Typography>
            <Paper sx={{
              p: 2,
              bgcolor: '#f8f9fa',
              borderRadius: 2,
              border: '1px solid #e0e0e0'
            }}>
              <Box sx={{
                width: '100%',
                height: 120,
                bgcolor: 'white',
                borderRadius: 1,
                border: '1px solid #e0e0e0',
                display: 'flex',
                flexDirection: 'column',
                p: 1.5
              }}>
                {/* Header Preview */}
                <Box sx={{
                  display: 'flex',
                  justifyContent: formData.layout === 'standard' ? 'space-between' : 'flex-start',
                  alignItems: 'center',
                  mb: 1.5,
                  p: 1,
                  bgcolor: formData.layout === 'standard' ? '#1976d2' : 'transparent',
                  color: formData.layout === 'standard' ? 'white' : 'black',
                  borderRadius: 0.5
                }}>
                  {formData.layout === 'moderne' && (
                    <Box sx={{ width: 30, height: 20, bgcolor: '#1976d2', borderRadius: 0.5, mr: 2 }} />
                  )}
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {formData.type.toUpperCase()} #{formData.layout === 'standard' ? '001' : 'DEV-001'}
                  </Typography>
                  {formData.layout === 'standard' && (
                    <Box sx={{ width: 30, height: 20, bgcolor: 'white', borderRadius: 0.5 }} />
                  )}
                </Box>
                {/* Table Preview */}
                <Box sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 0.5
                }}>
                  {[1,2,3].map(i => (
                    <Box key={i} sx={{
                      height: 12,
                      bgcolor: formData.layout === 'standard' ? (i % 2 === 0 ? '#f5f5f5' : 'white') : '#e3f2fd',
                      borderRadius: 0.25,
                      border: formData.layout === 'moderne' ? '1px solid #1976d2' : 'none',
                      display: 'flex',
                      alignItems: 'center',
                      px: 1
                    }}>
                      <Typography variant="caption" sx={{ fontSize: '0.6rem' }}>
                        Article {i}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            </Paper>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            onClick={handleCloseDialog}
            sx={{ borderRadius: 2, px: 3 }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            sx={{
              borderRadius: 2,
              px: 3,
              background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)'
            }}
          >
            {editingTemplate ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminTemplateManagement;
