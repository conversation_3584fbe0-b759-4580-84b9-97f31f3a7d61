{"version": 3, "sources": ["../../src/server/revalidation-utils.ts"], "sourcesContent": ["import type { WorkStore } from './app-render/work-async-storage.external'\nimport type { IncrementalCache } from './lib/incremental-cache'\nimport { getCacheHandlers } from './use-cache/handlers'\n\n/** Run a callback, and execute any *new* revalidations added during its runtime. */\nexport async function withExecuteRevalidates<T>(\n  store: WorkStore | undefined,\n  callback: () => Promise<T>\n): Promise<T> {\n  if (!store) {\n    return callback()\n  }\n  // If we executed any revalidates during the request, then we don't want to execute them again.\n  // save the state so we can check if anything changed after we're done running callbacks.\n  const savedRevalidationState = cloneRevalidationState(store)\n  try {\n    return await callback()\n  } finally {\n    // Check if we have any new revalidates, and if so, wait until they are all resolved.\n    const newRevalidates = diffRevalidationState(\n      savedRevalidationState,\n      cloneRevalidationState(store)\n    )\n    await executeRevalidates(store, newRevalidates)\n  }\n}\n\ntype RevalidationState = Required<\n  Pick<\n    WorkStore,\n    'pendingRevalidatedTags' | 'pendingRevalidates' | 'pendingRevalidateWrites'\n  >\n>\n\nfunction cloneRevalidationState(store: WorkStore): RevalidationState {\n  return {\n    pendingRevalidatedTags: store.pendingRevalidatedTags\n      ? [...store.pendingRevalidatedTags]\n      : [],\n    pendingRevalidates: { ...store.pendingRevalidates },\n    pendingRevalidateWrites: store.pendingRevalidateWrites\n      ? [...store.pendingRevalidateWrites]\n      : [],\n  }\n}\n\nfunction diffRevalidationState(\n  prev: RevalidationState,\n  curr: RevalidationState\n): RevalidationState {\n  const prevTags = new Set(prev.pendingRevalidatedTags)\n  const prevRevalidateWrites = new Set(prev.pendingRevalidateWrites)\n  return {\n    pendingRevalidatedTags: curr.pendingRevalidatedTags.filter(\n      (tag) => !prevTags.has(tag)\n    ),\n    pendingRevalidates: Object.fromEntries(\n      Object.entries(curr.pendingRevalidates).filter(\n        ([key]) => !(key in prev.pendingRevalidates)\n      )\n    ),\n    pendingRevalidateWrites: curr.pendingRevalidateWrites.filter(\n      (promise) => !prevRevalidateWrites.has(promise)\n    ),\n  }\n}\n\nasync function revalidateTags(\n  tags: string[],\n  incrementalCache: IncrementalCache | undefined\n): Promise<void> {\n  if (tags.length === 0) {\n    return\n  }\n\n  const promises: Promise<void>[] = []\n\n  if (incrementalCache) {\n    promises.push(incrementalCache.revalidateTag(tags))\n  }\n\n  const handlers = getCacheHandlers()\n  if (handlers) {\n    for (const handler of handlers) {\n      promises.push(handler.expireTags(...tags))\n    }\n  }\n\n  await Promise.all(promises)\n}\n\nexport async function executeRevalidates(\n  workStore: WorkStore,\n  state?: RevalidationState\n) {\n  const pendingRevalidatedTags =\n    state?.pendingRevalidatedTags ?? workStore.pendingRevalidatedTags ?? []\n\n  const pendingRevalidates =\n    state?.pendingRevalidates ?? workStore.pendingRevalidates ?? {}\n\n  const pendingRevalidateWrites =\n    state?.pendingRevalidateWrites ?? workStore.pendingRevalidateWrites ?? []\n\n  return Promise.all([\n    revalidateTags(pendingRevalidatedTags, workStore.incrementalCache),\n    ...Object.values(pendingRevalidates),\n    ...pendingRevalidateWrites,\n  ])\n}\n"], "names": ["executeRevalidates", "withExecuteRevalidates", "store", "callback", "savedRevalidationState", "cloneRevalidationState", "newRevalidates", "diffRevalidationState", "pendingRevalidatedTags", "pendingRevalidates", "pendingRevalidateWrites", "prev", "curr", "prevTags", "Set", "prevRevalidateWrites", "filter", "tag", "has", "Object", "fromEntries", "entries", "key", "promise", "revalidateTags", "tags", "incrementalCache", "length", "promises", "push", "revalidateTag", "handlers", "getCacheHandlers", "handler", "expireTags", "Promise", "all", "workStore", "state", "values"], "mappings": ";;;;;;;;;;;;;;;IA2FsBA,kBAAkB;eAAlBA;;IAtFAC,sBAAsB;eAAtBA;;;0BAHW;AAG1B,eAAeA,uBACpBC,KAA4B,EAC5BC,QAA0B;IAE1B,IAAI,CAACD,OAAO;QACV,OAAOC;IACT;IACA,+FAA+F;IAC/F,yFAAyF;IACzF,MAAMC,yBAAyBC,uBAAuBH;IACtD,IAAI;QACF,OAAO,MAAMC;IACf,SAAU;QACR,qFAAqF;QACrF,MAAMG,iBAAiBC,sBACrBH,wBACAC,uBAAuBH;QAEzB,MAAMF,mBAAmBE,OAAOI;IAClC;AACF;AASA,SAASD,uBAAuBH,KAAgB;IAC9C,OAAO;QACLM,wBAAwBN,MAAMM,sBAAsB,GAChD;eAAIN,MAAMM,sBAAsB;SAAC,GACjC,EAAE;QACNC,oBAAoB;YAAE,GAAGP,MAAMO,kBAAkB;QAAC;QAClDC,yBAAyBR,MAAMQ,uBAAuB,GAClD;eAAIR,MAAMQ,uBAAuB;SAAC,GAClC,EAAE;IACR;AACF;AAEA,SAASH,sBACPI,IAAuB,EACvBC,IAAuB;IAEvB,MAAMC,WAAW,IAAIC,IAAIH,KAAKH,sBAAsB;IACpD,MAAMO,uBAAuB,IAAID,IAAIH,KAAKD,uBAAuB;IACjE,OAAO;QACLF,wBAAwBI,KAAKJ,sBAAsB,CAACQ,MAAM,CACxD,CAACC,MAAQ,CAACJ,SAASK,GAAG,CAACD;QAEzBR,oBAAoBU,OAAOC,WAAW,CACpCD,OAAOE,OAAO,CAACT,KAAKH,kBAAkB,EAAEO,MAAM,CAC5C,CAAC,CAACM,IAAI,GAAK,CAAEA,CAAAA,OAAOX,KAAKF,kBAAkB,AAAD;QAG9CC,yBAAyBE,KAAKF,uBAAuB,CAACM,MAAM,CAC1D,CAACO,UAAY,CAACR,qBAAqBG,GAAG,CAACK;IAE3C;AACF;AAEA,eAAeC,eACbC,IAAc,EACdC,gBAA8C;IAE9C,IAAID,KAAKE,MAAM,KAAK,GAAG;QACrB;IACF;IAEA,MAAMC,WAA4B,EAAE;IAEpC,IAAIF,kBAAkB;QACpBE,SAASC,IAAI,CAACH,iBAAiBI,aAAa,CAACL;IAC/C;IAEA,MAAMM,WAAWC,IAAAA,0BAAgB;IACjC,IAAID,UAAU;QACZ,KAAK,MAAME,WAAWF,SAAU;YAC9BH,SAASC,IAAI,CAACI,QAAQC,UAAU,IAAIT;QACtC;IACF;IAEA,MAAMU,QAAQC,GAAG,CAACR;AACpB;AAEO,eAAe5B,mBACpBqC,SAAoB,EACpBC,KAAyB;IAEzB,MAAM9B,yBACJ8B,CAAAA,yBAAAA,MAAO9B,sBAAsB,KAAI6B,UAAU7B,sBAAsB,IAAI,EAAE;IAEzE,MAAMC,qBACJ6B,CAAAA,yBAAAA,MAAO7B,kBAAkB,KAAI4B,UAAU5B,kBAAkB,IAAI,CAAC;IAEhE,MAAMC,0BACJ4B,CAAAA,yBAAAA,MAAO5B,uBAAuB,KAAI2B,UAAU3B,uBAAuB,IAAI,EAAE;IAE3E,OAAOyB,QAAQC,GAAG,CAAC;QACjBZ,eAAehB,wBAAwB6B,UAAUX,gBAAgB;WAC9DP,OAAOoB,MAAM,CAAC9B;WACdC;KACJ;AACH"}