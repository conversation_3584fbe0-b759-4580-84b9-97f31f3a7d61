{"version": 3, "file": "pages-api-turbo.runtime.dev.js", "mappings": ";;iFACA,IAAIA,UAAYC,OAAOC,cAAc,CACjCC,iBAAmBF,OAAOG,wBAAwB,CAClDC,kBAAoBJ,OAAOK,mBAAmB,CAC9CC,aAAeN,OAAOO,SAAS,CAACC,cAAc,CAgB9CC,YAAc,CAAC,EAWnB,SAASC,gBAAgBC,CAAC,EACxB,IAAIC,GACJ,IAAMC,MAAQ,CACZ,SAAUF,GAAKA,EAAEG,IAAI,EAAI,CAAC,KAAK,EAAEH,EAAEG,IAAI,CAAC,CAAC,CACzC,YAAaH,GAAMA,CAAAA,EAAEI,OAAO,EAAIJ,IAAAA,EAAEI,OAAO,GAAW,CAAC,QAAQ,EAAE,CAAC,iBAAOJ,EAAEI,OAAO,CAAgB,IAAIC,KAAKL,EAAEI,OAAO,EAAIJ,EAAEI,OAAO,EAAEE,WAAW,GAAG,CAAC,CAChJ,WAAYN,GAAK,iBAAOA,EAAEO,MAAM,EAAiB,CAAC,QAAQ,EAAEP,EAAEO,MAAM,CAAC,CAAC,CACtE,WAAYP,GAAKA,EAAEQ,MAAM,EAAI,CAAC,OAAO,EAAER,EAAEQ,MAAM,CAAC,CAAC,CACjD,WAAYR,GAAKA,EAAES,MAAM,EAAI,SAC7B,aAAcT,GAAKA,EAAEU,QAAQ,EAAI,WACjC,aAAcV,GAAKA,EAAEW,QAAQ,EAAI,CAAC,SAAS,EAAEX,EAAEW,QAAQ,CAAC,CAAC,CACzD,gBAAiBX,GAAKA,EAAEY,WAAW,EAAI,cACvC,aAAcZ,GAAKA,EAAEa,QAAQ,EAAI,CAAC,SAAS,EAAEb,EAAEa,QAAQ,CAAC,CAAC,CAC1D,CAACC,MAAM,CAACC,SACHC,YAAc,CAAC,EAAEhB,EAAEiB,IAAI,CAAC,CAAC,EAAEC,mBAAmB,MAACjB,CAAAA,GAAKD,EAAEmB,KAAK,EAAYlB,GAAK,IAAI,CAAC,CACvF,OAAOC,IAAAA,MAAMkB,MAAM,CAASJ,YAAc,CAAC,EAAEA,YAAY,EAAE,EAAEd,MAAMmB,IAAI,CAAC,MAAM,CAAC,CAEjF,SAASC,YAAYC,MAAM,EACzB,IAAMC,IAAM,aAAa,EAAG,IAAIC,IAChC,IAAK,IAAMC,QAAQH,OAAOI,KAAK,CAAC,OAAQ,CACtC,GAAI,CAACD,KACH,SACF,IAAME,QAAUF,KAAKG,OAAO,CAAC,KAC7B,GAAID,KAAAA,QAAgB,CAClBJ,IAAIM,GAAG,CAACJ,KAAM,QACd,QACF,CACA,GAAM,CAACK,IAAKZ,MAAM,CAAG,CAACO,KAAKM,KAAK,CAAC,EAAGJ,SAAUF,KAAKM,KAAK,CAACJ,QAAU,GAAG,CACtE,GAAI,CACFJ,IAAIM,GAAG,CAACC,IAAKE,mBAAmBd,MAAAA,MAAgBA,MAAQ,QAC1D,CAAE,KAAM,CACR,CACF,CACA,OAAOK,GACT,CACA,SAASU,eAAeC,SAAS,MA8CVC,OAKAA,QAlDrB,GAAI,CAACD,UACH,OAEF,GAAM,CAAC,CAAClB,KAAME,MAAM,CAAE,GAAGkB,WAAW,CAAGf,YAAYa,WAC7C,CACJ3B,MAAM,CACNJ,OAAO,CACPkC,QAAQ,CACRC,MAAM,CACNpC,IAAI,CACJqC,QAAQ,CACR/B,MAAM,CACNG,WAAW,CACXC,QAAQ,CACT,CAAGxB,OAAOoD,WAAW,CACpBJ,WAAWb,GAAG,CAAC,CAAC,CAACO,IAAKW,OAAO,GAAK,CAChCX,IAAIY,WAAW,GAAGC,OAAO,CAAC,KAAM,IAChCF,OACD,GAeH,OAAOG,SAEQC,CAAC,EAChB,IAAMC,KAAO,CAAC,EACd,IAAK,IAAMhB,OAAOe,EACZA,CAAC,CAACf,IAAI,EACRgB,CAAAA,IAAI,CAAChB,IAAI,CAAGe,CAAC,CAACf,IAAI,EAGtB,OAAOgB,IACT,EAvBiB,CACb9B,KACAE,MAAOc,mBAAmBd,OAC1BX,OACA,GAAGJ,SAAW,CAAEA,QAAS,IAAIC,KAAKD,QAAS,CAAC,CAC5C,GAAGkC,UAAY,CAAE5B,SAAU,EAAK,CAAC,CACjC,GAAG,iBAAO6B,QAAuB,CAAEhC,OAAQyC,OAAOT,OAAQ,CAAC,CAC3DpC,KACA,GAAGqC,UAAY,CAAE7B,QAAQ,CAmBpBsC,UAAUC,QAAQ,CADzBd,OAASA,CADYA,OAjBsBI,UAkB3BG,WAAW,IACSP,OAAS,KAAK,CAnBG,CAAC,CACpD,GAAG3B,QAAU,CAAEA,OAAQ,EAAK,CAAC,CAC7B,GAAGI,UAAY,CAAEA,QAAQ,CAsBpBsC,SAASD,QAAQ,CADxBd,QAASA,CADYA,QApBsBvB,UAqB3B8B,WAAW,IACQP,QAAS,KAAK,CAtBI,CAAC,CACpD,GAAGxB,aAAe,CAAEA,YAAa,EAAK,CAAC,EAG3C,CA/EAwC,CAhBe,CAACC,OAAQC,OACtB,IAAK,IAAIrC,QAAQqC,IACflE,UAAUiE,OAAQpC,KAAM,CAAEsC,IAAKD,GAAG,CAACrC,KAAK,CAAEuC,WAAY,EAAK,EAC/D,GAaS1D,YAAa,CACpB2D,eAAgB,IAAMA,eACtBC,gBAAiB,IAAMA,gBACvBpC,YAAa,IAAMA,YACnBY,eAAgB,IAAMA,eACtBnC,gBAAiB,IAAMA,eACzB,GACA4D,QAAOC,OAAO,CAnBI,EAACC,GAAIC,KAAMC,OAAQC,QACnC,GAAIF,MAAQ,iBAAOA,MAAqB,mBAAOA,KAC7C,IAAK,IAAI/B,OAAOtC,kBAAkBqE,MAC3BnE,aAAasE,IAAI,CAACJ,GAAI9B,MAAQA,KAHZgC,IAGYhC,KACjC3C,UAAUyE,GAAI9B,IAAK,CAAEwB,IAAK,IAAMO,IAAI,CAAC/B,IAAI,CAAEyB,WAAY,CAAEQ,CAAAA,KAAOzE,iBAAiBuE,KAAM/B,IAAG,GAAMiC,KAAKR,UAAU,GAErH,OAAOK,EACT,GACwCzE,UAAU,CAAC,EAAG,aAAc,CAAE+B,MAAO,EAAK,GAWpDrB,aAkF9B,IAAImD,UAAY,CAAC,SAAU,MAAO,OAAO,CAKrCE,SAAW,CAAC,MAAO,SAAU,OAAO,CA0DpCM,eAAiB,MACnBS,YAAYC,cAAc,CAAE,CAE1B,IAAI,CAACC,OAAO,CAAG,aAAa,EAAG,IAAI3C,IACnC,IAAI,CAAC4C,QAAQ,CAAGF,eAChB,IAAMG,OAASH,eAAeZ,GAAG,CAAC,UAClC,GAAIe,OAEF,IAAK,GAAM,CAACrD,KAAME,MAAM,GADTG,YAAYgD,QAEzB,IAAI,CAACF,OAAO,CAACtC,GAAG,CAACb,KAAM,CAAEA,KAAME,KAAM,EAG3C,CACA,CAACoD,OAAOC,QAAQ,CAAC,EAAG,CAClB,OAAO,IAAI,CAACJ,OAAO,CAACG,OAAOC,QAAQ,CAAC,EACtC,CAIA,IAAIC,MAAO,CACT,OAAO,IAAI,CAACL,OAAO,CAACK,IAAI,CAE1BlB,IAAI,GAAGmB,IAAI,CAAE,CACX,IAAMzD,KAAO,iBAAOyD,IAAI,CAAC,EAAE,CAAgBA,IAAI,CAAC,EAAE,CAAGA,IAAI,CAAC,EAAE,CAACzD,IAAI,CACjE,OAAO,IAAI,CAACmD,OAAO,CAACb,GAAG,CAACtC,KAC1B,CACA0D,OAAO,GAAGD,IAAI,CAAE,CACd,IAAIzE,GACJ,IAAMqD,IAAMsB,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,EACnC,GAAI,CAACM,KAAKtD,MAAM,CACd,OAAOkC,IAAI9B,GAAG,CAAC,CAAC,CAACqD,EAAG1D,MAAM,GAAKA,OAEjC,IAAMF,KAAO,iBAAOyD,IAAI,CAAC,EAAE,CAAgBA,IAAI,CAAC,EAAE,CAAG,MAACzE,CAAAA,GAAKyE,IAAI,CAAC,EAAE,EAAY,KAAK,EAAIzE,GAAGgB,IAAI,CAC9F,OAAOqC,IAAIxC,MAAM,CAAC,CAAC,CAACgE,EAAE,GAAKA,IAAM7D,MAAMO,GAAG,CAAC,CAAC,CAACqD,EAAG1D,MAAM,GAAKA,MAC7D,CACA4D,IAAI9D,IAAI,CAAE,CACR,OAAO,IAAI,CAACmD,OAAO,CAACW,GAAG,CAAC9D,KAC1B,CACAa,IAAI,GAAG4C,IAAI,CAAE,CACX,GAAM,CAACzD,KAAME,MAAM,CAAGuD,IAAAA,KAAKtD,MAAM,CAAS,CAACsD,IAAI,CAAC,EAAE,CAACzD,IAAI,CAAEyD,IAAI,CAAC,EAAE,CAACvD,KAAK,CAAC,CAAGuD,KACpElD,IAAM,IAAI,CAAC4C,OAAO,CAMxB,OALA5C,IAAIM,GAAG,CAACb,KAAM,CAAEA,KAAME,KAAM,GAC5B,IAAI,CAACkD,QAAQ,CAACvC,GAAG,CACf,SACA8C,MAAMd,IAAI,CAACtC,KAAKA,GAAG,CAAC,CAAC,CAACqD,EAAGnC,OAAO,GAAK3C,gBAAgB2C,SAASrB,IAAI,CAAC,OAE9D,IAAI,CAKb2D,OAAOC,KAAK,CAAE,CACZ,IAAMzD,IAAM,IAAI,CAAC4C,OAAO,CAClBc,OAAS,MAAOC,OAAO,CAACF,OAA6BA,MAAMzD,GAAG,CAAC,MAAUA,IAAIwD,MAAM,CAAC/D,OAAnDO,IAAIwD,MAAM,CAACC,OAKlD,OAJA,IAAI,CAACZ,QAAQ,CAACvC,GAAG,CACf,SACA8C,MAAMd,IAAI,CAACtC,KAAKA,GAAG,CAAC,CAAC,CAACqD,EAAG1D,MAAM,GAAKpB,gBAAgBoB,QAAQE,IAAI,CAAC,OAE5D6D,MACT,CAIAE,OAAQ,CAEN,OADA,IAAI,CAACJ,MAAM,CAACJ,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,CAACiB,IAAI,KACjC,IAAI,CAKb,CAACd,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CAAC,eAAe,EAAEC,KAAKC,SAAS,CAACnG,OAAOoD,WAAW,CAAC,IAAI,CAAC2B,OAAO,GAAG,CAAC,CAE7EqB,UAAW,CACT,MAAO,IAAI,IAAI,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAClE,GAAG,CAAC,GAAO,CAAC,EAAEmE,EAAE1E,IAAI,CAAC,CAAC,EAAEC,mBAAmByE,EAAExE,KAAK,EAAE,CAAC,EAAEE,IAAI,CAAC,KAChG,CACF,EAGIqC,gBAAkB,MACpBQ,YAAY0B,eAAe,CAAE,KAGvB3F,GAAI4F,GAAIC,EADZ,KAAI,CAAC1B,OAAO,CAAG,aAAa,EAAG,IAAI3C,IAEnC,IAAI,CAAC4C,QAAQ,CAAGuB,gBAChB,IAAMzD,UAAY,MAAC2D,CAAAA,GAAK,MAACD,CAAAA,GAAK,MAAC5F,CAAAA,GAAK2F,gBAAgBG,YAAY,EAAY,KAAK,EAAI9F,GAAGgE,IAAI,CAAC2B,gBAAe,EAAaC,GAAKD,gBAAgBrC,GAAG,CAAC,aAAY,EAAauC,GAAK,EAAE,CAElL,IAAK,IAAME,gBADWpB,MAAMO,OAAO,CAAChD,WAAaA,UAAY8D,SA3IrCC,aAAa,EACvC,GAAI,CAACA,cACH,MAAO,EAAE,CACX,IAEIC,MACAC,GACAC,UACAC,UACAC,sBANAC,eAAiB,EAAE,CACnBC,IAAM,EAMV,SAASC,iBACP,KAAOD,IAAMP,cAAc9E,MAAM,EAAI,KAAKuF,IAAI,CAACT,cAAcU,MAAM,CAACH,OAClEA,KAAO,EAET,OAAOA,IAAMP,cAAc9E,MAAM,CAMnC,KAAOqF,IAAMP,cAAc9E,MAAM,EAAE,CAGjC,IAFA+E,MAAQM,IACRF,sBAAwB,GACjBG,kBAEL,GAAIN,MADJA,CAAAA,GAAKF,cAAcU,MAAM,CAACH,IAAG,EACb,CAKd,IAJAJ,UAAYI,IACZA,KAAO,EACPC,iBACAJ,UAAYG,IACLA,IAAMP,cAAc9E,MAAM,EAZ9BgF,MADPA,CAAAA,GAAKF,cAAcU,MAAM,CAACH,IAAG,GACRL,MAAAA,IAAcA,MAAAA,IAa7BK,KAAO,CAELA,CAAAA,IAAMP,cAAc9E,MAAM,EAAI8E,MAAAA,cAAcU,MAAM,CAACH,MACrDF,sBAAwB,GACxBE,IAAMH,UACNE,eAAeK,IAAI,CAACX,cAAcY,SAAS,CAACX,MAAOE,YACnDF,MAAQM,KAERA,IAAMJ,UAAY,CAEtB,MACEI,KAAO,EAGP,EAACF,uBAAyBE,KAAOP,cAAc9E,MAAM,GACvDoF,eAAeK,IAAI,CAACX,cAAcY,SAAS,CAACX,MAAOD,cAAc9E,MAAM,EAE3E,CACA,OAAOoF,cACT,EAyFoFrE,WACtC,CACxC,IAAM4E,OAAS7E,eAAe8D,cAC1Be,QACF,IAAI,CAAC3C,OAAO,CAACtC,GAAG,CAACiF,OAAO9F,IAAI,CAAE8F,OAClC,CACF,CAIAxD,IAAI,GAAGmB,IAAI,CAAE,CACX,IAAM3C,IAAM,iBAAO2C,IAAI,CAAC,EAAE,CAAgBA,IAAI,CAAC,EAAE,CAAGA,IAAI,CAAC,EAAE,CAACzD,IAAI,CAChE,OAAO,IAAI,CAACmD,OAAO,CAACb,GAAG,CAACxB,IAC1B,CAIA4C,OAAO,GAAGD,IAAI,CAAE,CACd,IAAIzE,GACJ,IAAMqD,IAAMsB,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,CAACsB,MAAM,IAC1C,GAAI,CAAChB,KAAKtD,MAAM,CACd,OAAOkC,IAET,IAAMvB,IAAM,iBAAO2C,IAAI,CAAC,EAAE,CAAgBA,IAAI,CAAC,EAAE,CAAG,MAACzE,CAAAA,GAAKyE,IAAI,CAAC,EAAE,EAAY,KAAK,EAAIzE,GAAGgB,IAAI,CAC7F,OAAOqC,IAAIxC,MAAM,CAAC,GAAOd,EAAEiB,IAAI,GAAKc,IACtC,CACAgD,IAAI9D,IAAI,CAAE,CACR,OAAO,IAAI,CAACmD,OAAO,CAACW,GAAG,CAAC9D,KAC1B,CAIAa,IAAI,GAAG4C,IAAI,CAAE,CACX,GAAM,CAACzD,KAAME,MAAOI,OAAO,CAAGmD,IAAAA,KAAKtD,MAAM,CAAS,CAACsD,IAAI,CAAC,EAAE,CAACzD,IAAI,CAAEyD,IAAI,CAAC,EAAE,CAACvD,KAAK,CAAEuD,IAAI,CAAC,EAAE,CAAC,CAAGA,KACrFlD,IAAM,IAAI,CAAC4C,OAAO,CAGxB,OAFA5C,IAAIM,GAAG,CAACb,KAAM+F,SAyBOzF,OAAS,CAAEN,KAAM,GAAIE,MAAO,EAAG,CAAC,EAUvD,MAT8B,UAA1B,OAAOI,OAAOnB,OAAO,EACvBmB,CAAAA,OAAOnB,OAAO,CAAG,IAAIC,KAAKkB,OAAOnB,OAAO,GAEtCmB,OAAOhB,MAAM,EACfgB,CAAAA,OAAOnB,OAAO,CAAG,IAAIC,KAAKA,KAAK4G,GAAG,GAAK1F,IAAAA,OAAOhB,MAAM,CAAM,EAExDgB,CAAAA,OAAAA,OAAOpB,IAAI,EAAaoB,KAAqB,IAArBA,OAAOpB,IAAI,GACrCoB,CAAAA,OAAOpB,IAAI,CAAG,GAAE,EAEXoB,MACT,EApCkC,CAAEN,KAAME,MAAO,GAAGI,MAAM,IACtDqB,SAiBasE,GAAG,CAAEC,OAAO,EAE3B,IAAK,GAAM,EAAGhG,MAAM,GADpBgG,QAAQnC,MAAM,CAAC,cACSkC,KAAK,CAC3B,IAAME,WAAarH,gBAAgBoB,OACnCgG,QAAQE,MAAM,CAAC,aAAcD,WAC/B,CACF,EAvBY5F,IAAK,IAAI,CAAC6C,QAAQ,EACnB,IAAI,CAKbW,OAAO,GAAGN,IAAI,CAAE,CACd,GAAM,CAACzD,KAAMqG,QAAQ,CAAG,iBAAO5C,IAAI,CAAC,EAAE,CAAgB,CAACA,IAAI,CAAC,EAAE,CAAC,CAAG,CAACA,IAAI,CAAC,EAAE,CAACzD,IAAI,CAAEyD,IAAI,CAAC,EAAE,CAAC,CACzF,OAAO,IAAI,CAAC5C,GAAG,CAAC,CAAE,GAAGwF,OAAO,CAAErG,KAAME,MAAO,GAAIf,QAAS,aAAa,EAAG,IAAIC,KAAK,EAAG,EACtF,CACA,CAACkE,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CAAC,gBAAgB,EAAEC,KAAKC,SAAS,CAACnG,OAAOoD,WAAW,CAAC,IAAI,CAAC2B,OAAO,GAAG,CAAC,CAE9EqB,UAAW,CACT,MAAO,IAAI,IAAI,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAClE,GAAG,CAACzB,iBAAiBsB,IAAI,CAAC,KAC9D,CACF,C;;oDCvTA,CAAC,KAAK,aAAa,IAAIkG,EAAE,CAAC,GAAGA,IAC7B;;;;;CAKC,EACDA,EAAE3D,OAAO,CAAsP,SAAe2D,CAAC,CAACC,CAAC,QAAE,UAAG,OAAOD,EAAqBE,MAAMF,GAAM,iBAAOA,EAAqBG,OAAOH,EAAEC,GAAU,IAAI,EAAjWD,EAAE3D,OAAO,CAAC8D,MAAM,CAACA,OAAOH,EAAE3D,OAAO,CAAC6D,KAAK,CAACA,MAAM,IAAID,EAAE,wBAA4BG,EAAE,wBAA4B7E,EAAE,CAAC8E,EAAE,EAAEC,GAAG,KAAMC,GAAG,QAAMC,GAAG,WAAMC,GAAGC,cAAiBC,GAAGD,eAAgB,EAAME,EAAE,gDAAmK,SAAST,OAAOH,CAAC,CAACY,CAAC,EAAE,GAAG,CAACnF,OAAOoF,QAAQ,CAACb,GAAI,OAAO,KAAK,IAAIzC,EAAEmD,KAAKI,GAAG,CAACd,GAAOe,EAAEH,GAAGA,EAAEI,kBAAkB,EAAE,GAAOC,EAAEL,GAAGA,EAAEM,aAAa,EAAE,GAAOC,EAAEP,GAAGA,KAAkBQ,IAAlBR,EAAES,aAAa,CAAaT,EAAES,aAAa,CAAC,EAAMC,EAAE9H,CAAAA,CAAQoH,CAAAA,GAAGA,EAAEW,aAAa,EAAMC,EAAEZ,GAAGA,EAAEa,IAAI,EAAE,GAAOD,GAAIjG,CAAC,CAACiG,EAAEpG,WAAW,GAAG,GAAcoG,EAATjE,GAAGhC,EAAEoF,EAAE,CAAI,KAAapD,GAAGhC,EAAEkF,EAAE,CAAI,KAAalD,GAAGhC,EAAEiF,EAAE,CAAI,KAAajD,GAAGhC,EAAEgF,EAAE,CAAI,KAAahD,GAAGhC,EAAE+E,EAAE,CAAI,KAAY,KAAgC,IAAIoB,EAAErB,CAA3BL,EAAEzE,CAAC,CAACiG,EAAEpG,WAAW,GAAG,EAASuG,OAAO,CAACR,GAAiH,OAA1GG,GAAGI,CAAAA,EAAEA,EAAErG,OAAO,CAAC+E,EAAE,KAAI,EAAKW,GAAGW,CAAAA,EAAEA,EAAEtH,KAAK,CAAC,KAAKH,GAAG,CAAE,SAAS+F,CAAC,CAACI,CAAC,EAAE,OAAOA,IAAAA,EAAMJ,EAAE3E,OAAO,CAAC4E,EAAEc,GAAGf,CAAC,GAAIlG,IAAI,CAAC,IAAG,EAAS4H,EAAET,EAAEO,CAAC,CAAC,SAAStB,MAAMF,CAAC,EAAE,GAAG,iBAAOA,GAAc,CAAC4B,MAAM5B,GAAI,OAAOA,EAAE,GAAG,iBAAOA,EAAc,OAAO,KAAK,IAAoBI,EAAhBH,EAAEW,EAAEiB,IAAI,CAAC7B,GAAazC,EAAE,IAA+E,OAAvE0C,GAA+BG,EAAE0B,WAAW7B,CAAC,CAAC,EAAE,EAAE1C,EAAE0C,CAAC,CAAC,EAAE,CAAC7E,WAAW,KAAjEgF,EAAE2B,SAAS/B,EAAE,IAAIzC,EAAE,KAAwDmD,KAAKsB,KAAK,CAACzG,CAAC,CAACgC,EAAE,CAAC6C,EAAE,CAAC,CAAC,EAAMH,EAAE,CAAC,EAAE,SAASgC,qBAAoB7B,CAAC,EAAE,IAAI7E,EAAE0E,CAAC,CAACG,EAAE,CAAC,GAAG7E,KAAI6F,IAAJ7F,EAAe,OAAOA,EAAEc,OAAO,CAAC,IAAIuE,EAAEX,CAAC,CAACG,EAAE,CAAC,CAAC/D,QAAQ,CAAC,CAAC,EAAMkB,EAAE,GAAK,GAAG,CAACyC,CAAC,CAACI,EAAE,CAACQ,EAAEA,EAAEvE,OAAO,CAAC4F,sBAAqB1E,EAAE,EAAK,QAAQ,CAAIA,GAAE,OAAO0C,CAAC,CAACG,EAAE,CAAC,OAAOQ,EAAEvE,OAAO,CAA6C4F,qBAAoBC,EAAE,CAACC,UAAU,IAAI,IAAI/B,EAAE6B,qBAAoB,GAAI7F,CAAAA,QAAOC,OAAO,CAAC+D,CAAC,I;;2DCP5+C,CAAC,KAAK,YAA6C,cAA7B,OAAO6B,qBAAkCA,CAAAA,oBAAoBC,EAAE,CAACC,UAAU,GAAE,EAAE,IAAInC,EAAE,CAAC,EAAE,CAAC,KAC9G;;;;CAIC,EAAE,IAAIzE,EAAE,mKAAuK6E,EAAE,wCAA4C7C,EAAE,gCAAoCqD,EAAE,6BAAiCG,EAAE,WAAeI,EAAE,6DAAukD,SAASiB,YAAYpC,CAAC,EAAE,IAAI,CAACqC,UAAU,CAACvK,OAAOwK,MAAM,CAAC,MAAM,IAAI,CAACC,IAAI,CAACvC,CAAC,CAAjlDC,EAAEE,MAAM,CAAsB,SAAgBH,CAAC,EAAE,GAAG,CAACA,GAAG,iBAAOA,EAAc,MAAM,UAAc,4BAA4B,IAAIC,EAAED,EAAEqC,UAAU,CAAK9G,EAAEyE,EAAEuC,IAAI,CAAC,GAAG,CAAChH,GAAG,CAAC4F,EAAE/B,IAAI,CAAC7D,GAAI,MAAM,UAAc,gBAAgB,IAAI6E,GAAE7E,EAAE,GAAG0E,GAAG,iBAAOA,EAAgD,IAAI,IAAlCW,EAAMG,GAAEjJ,OAAOgG,IAAI,CAACmC,GAAGuC,IAAI,GAAWlB,EAAE,EAAEA,EAAEP,GAAElH,MAAM,CAACyH,IAAI,CAAQ,GAAPV,EAAEG,EAAC,CAACO,EAAE,CAAI,CAAC/D,EAAE6B,IAAI,CAACwB,GAAI,MAAM,UAAc,0BAA0BR,IAAG,KAAKQ,EAAE,IAAI6B,SAA6+BzC,CAAC,EAAE,IAAIC,EAAEyC,OAAO1C,GAAG,GAAGzC,EAAE6B,IAAI,CAACa,GAAI,OAAOA,EAAE,GAAGA,EAAEpG,MAAM,CAAC,GAAG,CAACuG,EAAEhB,IAAI,CAACa,GAAI,MAAM,UAAc,2BAA2B,MAAM,IAAIA,EAAE5E,OAAO,CAAC0F,EAAE,QAAQ,GAAG,EAA1nCd,CAAC,CAACW,EAAE,CAAC,CAAE,OAAOR,EAAC,EAA9YH,EAAEC,KAAK,CAAwY,SAAeF,CAAC,EAAE,GAAG,CAACA,EAAG,MAAM,UAAc,+BAA+B,IAAuTsB,EAAME,EAAMP,EAA/ThB,EAAE,iBAAOD,EAAa2C,SAAomB3C,CAAC,EAAE,IAAIC,EAAgJ,GAA3I,mBAAOD,EAAE4C,SAAS,CAAe3C,EAAED,EAAE4C,SAAS,CAAC,gBAA2C,UAAnB,OAAO5C,EAAEJ,OAAO,EAAaK,CAAAA,EAAED,EAAEJ,OAAO,EAAEI,EAAEJ,OAAO,CAAC,eAAe,EAAI,iBAAOK,EAAc,MAAM,UAAc,8CAA8C,OAAOA,CAAC,EAA90BD,GAAGA,EAAE,GAAG,iBAAOC,EAAc,MAAM,UAAc,8CAA8C,IAAIG,EAAEH,EAAE3F,OAAO,CAAC,KAASiD,EAAE6C,KAAAA,EAAOH,EAAE4C,MAAM,CAAC,EAAEzC,GAAG0C,IAAI,GAAG7C,EAAE6C,IAAI,GAAG,GAAG,CAAC3B,EAAE/B,IAAI,CAAC7B,GAAI,MAAM,UAAc,sBAAsB,IAAIwD,EAAE,IAAIqB,YAAY7E,EAAEnC,WAAW,IAAI,GAAGgF,KAAAA,EAAO,CAAiC,IAAd7E,EAAEwH,SAAS,CAAC3C,EAAQoB,EAAEjG,EAAEsG,IAAI,CAAC5B,IAAG,CAAC,GAAGuB,EAAEwB,KAAK,GAAG5C,EAAG,MAAM,UAAc,4BAA4BA,GAAGoB,CAAC,CAAC,EAAE,CAAC3H,MAAM,CAACyH,EAAEE,CAAC,CAAC,EAAE,CAACpG,WAAW,GAAoB,MAAP6F,CAAVA,EAAEO,CAAC,CAAC,EAAE,CAAK,CAAC,EAAE,EAAQP,CAAAA,EAAEA,EAAE4B,MAAM,CAAC,EAAE5B,EAAEpH,MAAM,CAAC,GAAGwB,OAAO,CAACuF,EAAE,KAAI,EAAEG,EAAEsB,UAAU,CAACf,EAAE,CAACL,CAAC,CAAC,GAAGb,IAAIH,EAAEpG,MAAM,CAAE,MAAM,UAAc,2BAA4B,CAAC,OAAOkH,CAAC,CAAkgB,KAAK3E,QAAOC,OAAO,CAAC2D,CAAC,I;;qDCL99D,CAAC,KAAK,YAA6C,cAA7B,OAAOiC,qBAAkCA,CAAAA,oBAAoBC,EAAE,CAACC,UAAU,GAAE,EAAE,IAAInC,EAAE,CAAC,EAAE,CAAC,KAC9G;;;;;CAKC,EAAEC,EAAEC,KAAK,CAAyI,SAAeF,CAAC,CAACC,CAAC,EAAE,GAAG,iBAAOD,EAAc,MAAM,UAAc,iCAAyF,IAAI,IAAxDzE,EAAE,CAAC,EAAkBwF,EAAEf,EAAE5F,KAAK,CAACgG,GAAOa,EAAE1D,CAA7B0C,GAAG,CAAC,GAA2BgD,MAAM,EAAErC,EAAUY,EAAE,EAAEA,EAAET,EAAElH,MAAM,CAAC2H,IAAI,CAAC,IAAIL,EAAEJ,CAAC,CAACS,EAAE,CAAKF,EAAEH,EAAE7G,OAAO,CAAC,KAAK,IAAGgH,CAAAA,EAAE,IAAY,IAAIlD,EAAE+C,EAAE0B,MAAM,CAAC,EAAEvB,GAAGwB,IAAI,GAAOrK,EAAE0I,EAAE0B,MAAM,CAAC,EAAEvB,EAAEH,EAAEtH,MAAM,EAAEiJ,IAAI,EAAM,MAAKrK,CAAC,CAAC,EAAE,EAAEA,CAAAA,EAAEA,EAAEgC,KAAK,CAAC,EAAE,GAAE,EAAK2G,KAAAA,GAAW7F,CAAC,CAAC6C,EAAE,EAAE7C,CAAAA,CAAC,CAAC6C,EAAE,CAAC8E,SAA8qClD,CAAC,CAACC,CAAC,EAAE,GAAG,CAAC,OAAOA,EAAED,EAAE,CAAC,MAAMC,EAAE,CAAC,OAAOD,CAAC,CAAC,EAA3sCvH,EAAEwI,EAAC,EAAE,CAAC,OAAO1F,CAAC,EAAtf0E,EAAEkD,SAAS,CAA4e,SAAmBnD,CAAC,CAACC,CAAC,CAACW,CAAC,EAAE,IAAIR,EAAEQ,GAAG,CAAC,EAAMG,EAAEX,EAAEgD,MAAM,EAAE7H,EAAE,GAAG,mBAAOwF,EAAgB,MAAM,UAAc,4BAA4B,GAAG,CAACxD,EAAE6B,IAAI,CAACY,GAAI,MAAM,UAAc,4BAA4B,IAAIiB,EAAEF,EAAEd,GAAG,GAAGgB,GAAG,CAAC1D,EAAE6B,IAAI,CAAC6B,GAAI,MAAM,UAAc,2BAA2B,IAAIO,EAAExB,EAAE,IAAIiB,EAAE,GAAG,MAAMb,EAAEpH,MAAM,CAAC,CAAC,IAAImI,EAAEf,EAAEpH,MAAM,CAAC,EAAE,GAAG4I,MAAMT,IAAI,CAACN,SAASM,GAAI,MAAM,UAAc,4BAA4BK,GAAG,aAAad,KAAKsB,KAAK,CAACb,EAAE,CAAC,GAAGf,EAAEnH,MAAM,CAAC,CAAC,GAAG,CAACsE,EAAE6B,IAAI,CAACgB,EAAEnH,MAAM,EAAG,MAAM,UAAc,4BAA4BuI,GAAG,YAAYpB,EAAEnH,MAAM,CAAC,GAAGmH,EAAExH,IAAI,CAAC,CAAC,GAAG,CAAC2E,EAAE6B,IAAI,CAACgB,EAAExH,IAAI,EAAG,MAAM,UAAc,0BAA0B4I,GAAG,UAAUpB,EAAExH,IAAI,CAAC,GAAGwH,EAAEvH,OAAO,CAAC,CAAC,GAAG,mBAAOuH,EAAEvH,OAAO,CAACE,WAAW,CAAe,MAAM,UAAc,6BAA6ByI,GAAG,aAAapB,EAAEvH,OAAO,CAACE,WAAW,EAAE,CAA2D,GAAvDqH,EAAEjH,QAAQ,EAAEqI,CAAAA,GAAG,YAAW,EAAKpB,EAAElH,MAAM,EAAEsI,CAAAA,GAAG,UAAS,EAAKpB,EAAEhH,QAAQ,CAAyE,OAAjE,iBAAOgH,EAAEhH,QAAQ,CAAYgH,EAAEhH,QAAQ,CAACgC,WAAW,GAAGgF,EAAEhH,QAAQ,EAAW,IAAK,GAAsE,IAAI,SAArEoI,GAAG,oBAAoB,KAAM,KAAI,MAAMA,GAAG,iBAAiB,KAAgD,KAAI,OAAOA,GAAG,kBAAkB,KAAM,SAAQ,MAAM,UAAc,6BAA6B,CAAE,OAAOA,CAAC,EAAlmD,IAAIZ,EAAElG,mBAAuBa,EAAE5B,mBAAuByG,EAAE,MAAU7C,EAAE,uCAA0lD,KAAKnB,QAAOC,OAAO,CAAC2D,CAAC,I;;oDCN1tD,CAAC,KAAK,aAAa,IAAIA,EAAE,CAAC,IAAIA,IAC9B;;;;;CAKC,EACD,IAAIC,EAAE,iCAA2f,SAASoD,cAAcrD,CAAC,EAAE,IAAIC,EAAED,GAAGlH,KAAKoH,KAAK,CAACF,GAAG,MAAO,iBAAOC,EAAaA,EAAEqD,GAAG,CAA3iBtD,EAAE3D,OAAO,CAAO,SAAe2D,CAAC,CAACI,CAAC,EAAE,IAAI7E,EAAEyE,CAAC,CAAC,oBAAoB,CAAKiB,EAAEjB,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAACzE,GAAG,CAAC0F,EAAG,MAAO,GAAM,IAAIL,EAAEZ,CAAC,CAAC,gBAAgB,CAAC,GAAGY,GAAGX,EAAEb,IAAI,CAACwB,GAAI,MAAO,GAAM,GAAGK,GAAGA,MAAAA,EAAQ,CAAC,IAAIE,EAAEf,EAAE,IAAO,CAAC,GAAG,CAACe,EAAG,MAAO,GAAyC,IAAI,IAAnC5D,EAAE,GAAS+D,EAAEiC,SAAuVvD,CAAC,EAA2B,IAAI,IAAzBC,EAAE,EAAMG,EAAE,EAAE,CAAK7E,EAAE,EAAU0F,EAAE,EAAEL,EAAEZ,EAAEnG,MAAM,CAACoH,EAAEL,EAAEK,IAAK,OAAOjB,EAAEwD,UAAU,CAACvC,IAAI,KAAK,GAAM1F,IAAI0E,GAAG1E,CAAAA,EAAE0E,EAAEgB,EAAE,GAAE,KAAM,MAAK,GAAGb,EAAEd,IAAI,CAACU,EAAET,SAAS,CAAChE,EAAE0E,IAAI1E,EAAE0E,EAAEgB,EAAE,EAAE,KAAM,SAAQhB,EAAEgB,EAAE,CAAO,CAA2B,OAAzBb,EAAEd,IAAI,CAACU,EAAET,SAAS,CAAChE,EAAE0E,IAAWG,CAAC,EAAjiBa,GAAW3D,EAAE,EAAEA,EAAEgE,EAAEzH,MAAM,CAACyD,IAAI,CAAC,IAAIyD,EAAEO,CAAC,CAAChE,EAAE,CAAC,GAAGyD,IAAII,GAAGJ,IAAI,KAAKI,GAAG,KAAKJ,IAAII,EAAE,CAAC5D,EAAE,GAAM,KAAK,CAAC,CAAC,GAAGA,EAAG,MAAO,EAAM,CAAC,GAAGhC,EAAE,CAAC,IAAIiG,EAAEpB,CAAC,CAAC,gBAAgB,CAAiD,GAA1C,CAACoB,GAAG,CAAE6B,CAAAA,cAAc7B,IAAI6B,cAAc9H,EAAC,EAAS,MAAO,EAAM,CAAC,MAAO,EAAI,CAAqU,CAAC,EAAM0E,EAAE,CAAC,EAAE,SAASgC,qBAAoB7B,CAAC,EAAE,IAAI7E,EAAE0E,CAAC,CAACG,EAAE,CAAC,GAAG7E,KAAI6F,IAAJ7F,EAAe,OAAOA,EAAEc,OAAO,CAAC,IAAI4E,EAAEhB,CAAC,CAACG,EAAE,CAAC,CAAC/D,QAAQ,CAAC,CAAC,EAAMuE,EAAE,GAAK,GAAG,CAACZ,CAAC,CAACI,EAAE,CAACa,EAAEA,EAAE5E,OAAO,CAAC4F,sBAAqBrB,EAAE,EAAK,QAAQ,CAAIA,GAAE,OAAOX,CAAC,CAACG,EAAE,CAAC,OAAOa,EAAE5E,OAAO,CAA6C4F,qBAAoBC,EAAE,CAACC,UAAU,IAAI,IAAI/B,EAAE6B,qBAAoB,IAAK7F,CAAAA,QAAOC,OAAO,CAAC+D,CAAC,I;;kTCP9pC,IAAM,gCAA+BqD,QAAQ,U,gGCK7C,IAAMC,iBAAmB,cAQlB,SAASC,kBAAkBC,MAAc,CAAEC,IAAY,EAC5D,IAAMC,GAAKC,0BAAAA,WAAkB,CAPV,IAQbC,KAAOD,0BAAAA,WAAkB,CANV,IASfvJ,IAAMuJ,0BAAAA,UAAiB,CAC3BH,OACAI,KATsB,IALJ,GAiBlB,UAGIC,OAASF,0BAAAA,cAAqB,CAACL,iBAAkBlJ,IAAKsJ,IACtDI,UAAYC,OAAOC,MAAM,CAAC,CAACH,OAAOI,MAAM,CAACR,KAAM,QAASI,OAAOK,KAAK,GAAG,EAGvEC,IAAMN,OAAOO,UAAU,GAE7B,OAAOL,OAAOC,MAAM,CAAC,CAKnBJ,KACAF,GACAS,IACAL,UACD,EAAEhG,QAAQ,CAAC,MACd,CAEO,SAASuG,kBACdb,MAAc,CACdc,aAAqB,EAErB,IAAMC,OAASR,OAAO5H,IAAI,CAACmI,cAAe,OAEpCV,KAAOW,OAAOlK,KAAK,CAAC,EAzCL,IA0CfqJ,GAAKa,OAAOlK,KAAK,CA1CF,GA4CnBmK,IAEIL,IAAMI,OAAOlK,KAAK,CACtBmK,GACAA,IAEIV,UAAYS,OAAOlK,KAAK,CAC5BmK,IAIIpK,IAAMuJ,0BAAAA,UAAiB,CAC3BH,OACAI,KAvDsB,IALJ,GA+DlB,UAGIa,SAAWd,0BAAAA,gBAAuB,CAACL,iBAAkBlJ,IAAKsJ,IAGhE,OAFAe,SAASC,UAAU,CAACP,KAEbM,SAASR,MAAM,CAACH,WAAaW,SAASP,KAAK,CAAC,OACrD,C;;4EC5EAlI,CAAAA,QAAOC,OAAO,CAAGoH,QAAQ,kC;;wECAzBrH,CAAAA,QAAOC,OAAO,CAAGoH,QAAQ,8B;;wDCAzBrH,CAAAA,QAAOC,OAAO,CAAGoH,QAAQ,c,GCCrBsB,yBAA2B,CAAC,EAGhC,SAASC,oBAAoBC,QAAQ,EAEpC,IAAIC,aAAeH,wBAAwB,CAACE,SAAS,CACrD,GAAIC,KAAiB9D,IAAjB8D,aACH,OAAOA,aAAa7I,OAAO,CAG5B,IAAID,QAAS2I,wBAAwB,CAACE,SAAS,CAAG,CAGjD5I,QAAS,CAAC,CACX,EAMA,OAHA8I,mBAAmB,CAACF,SAAS,CAAC7I,QAAQA,QAAOC,OAAO,CAAE2I,qBAG/C5I,QAAOC,OAAO,CCpBtB2I,oBAAoBzH,CAAC,CAAG,UACvB,IAAI6H,OAAShJ,SAAUA,QAAOiJ,UAAU,CACvC,IAAOjJ,QAAO,OAAU,CACxB,IAAOA,QAER,OADA4I,oBAAoBM,CAAC,CAACF,OAAQ,CAAEhF,EAAGgF,MAAO,GACnCA,MACR,ECNAJ,oBAAoBM,CAAC,CAAG,CAACjJ,QAASkJ,cACjC,IAAI,IAAI/K,OAAO+K,WACXP,oBAAoBjE,CAAC,CAACwE,WAAY/K,MAAQ,CAACwK,oBAAoBjE,CAAC,CAAC1E,QAAS7B,MAC5E1C,OAAOC,cAAc,CAACsE,QAAS7B,IAAK,CAAEyB,WAAY,GAAMD,IAAKuJ,UAAU,CAAC/K,IAAI,EAG/E,ECPAwK,oBAAoBjE,CAAC,CAAG,CAACyE,IAAKC,OAAU3N,OAAOO,SAAS,CAACC,cAAc,CAACoE,IAAI,CAAC8I,IAAKC,MCClFT,oBAAoB/E,CAAC,CAAG,UACF,aAAlB,OAAOjD,QAA0BA,OAAO0I,WAAW,EACrD5N,OAAOC,cAAc,CAACsE,QAASW,OAAO0I,WAAW,CAAE,CAAE9L,MAAO,QAAS,GAEtE9B,OAAOC,cAAc,CAACsE,QAAS,aAAc,CAAEzC,MAAO,EAAK,EAC5D,E;;4OCNO,OAAM+L,eACX,OAAO3J,IACLF,MAAS,CACT2J,IAAqB,CACrBG,QAAiB,CACZ,CACL,IAAMhM,MAAQiM,QAAQ7J,GAAG,CAACF,OAAQ2J,KAAMG,gBACxC,YAAI,OAAOhM,MACFA,MAAMkM,IAAI,CAAChK,QAGblC,KACT,CAEA,OAAOW,IACLuB,MAAS,CACT2J,IAAqB,CACrB7L,KAAU,CACVgM,QAAa,CACJ,CACT,OAAOC,QAAQtL,GAAG,CAACuB,OAAQ2J,KAAM7L,MAAOgM,SAC1C,CAEA,OAAOpI,IAAsB1B,MAAS,CAAE2J,IAAqB,CAAW,CACtE,OAAOI,QAAQrI,GAAG,CAAC1B,OAAQ2J,KAC7B,CAEA,OAAOM,eACLjK,MAAS,CACT2J,IAAqB,CACZ,CACT,OAAOI,QAAQE,cAAc,CAACjK,OAAQ2J,KACxC,CACF,CC1BO,MAAMO,6BAA6BC,MACxCtJ,aAAc,CACZ,KAAK,CACH,qGAEJ,CAEA,OAAcuJ,UAAW,CACvB,MAAM,IAAIF,oBACZ,CACF,CAUO,MAAMG,uBAAuBC,QAGlCzJ,YAAYiD,OAA4B,CAAE,CAGxC,KAAK,GAEL,IAAI,CAACA,OAAO,CAAG,IAAIyG,MAAMzG,QAAS,CAChC5D,IAAIF,MAAM,CAAE2J,IAAI,CAAEG,QAAQ,EAIxB,GAAI,iBAAOH,KACT,OAAOE,eAAe3J,GAAG,CAACF,OAAQ2J,KAAMG,UAG1C,IAAMU,WAAab,KAAKrK,WAAW,GAK7BmL,SAAWzO,OAAOgG,IAAI,CAAC8B,SAAS4G,IAAI,CACxC,GAAOzF,EAAE3F,WAAW,KAAOkL,YAI7B,GAAI,KAAoB,IAAbC,SAGX,OAAOZ,eAAe3J,GAAG,CAACF,OAAQyK,SAAUX,SAC9C,EACArL,IAAIuB,MAAM,CAAE2J,IAAI,CAAE7L,KAAK,CAAEgM,QAAQ,EAC/B,GAAI,iBAAOH,KACT,OAAOE,eAAepL,GAAG,CAACuB,OAAQ2J,KAAM7L,MAAOgM,UAGjD,IAAMU,WAAab,KAAKrK,WAAW,GAK7BmL,SAAWzO,OAAOgG,IAAI,CAAC8B,SAAS4G,IAAI,CACxC,GAAOzF,EAAE3F,WAAW,KAAOkL,YAI7B,OAAOX,eAAepL,GAAG,CAACuB,OAAQyK,UAAYd,KAAM7L,MAAOgM,SAC7D,EACApI,IAAI1B,MAAM,CAAE2J,IAAI,EACd,GAAI,iBAAOA,KAAmB,OAAOE,eAAenI,GAAG,CAAC1B,OAAQ2J,MAEhE,IAAMa,WAAab,KAAKrK,WAAW,GAK7BmL,SAAWzO,OAAOgG,IAAI,CAAC8B,SAAS4G,IAAI,CACxC,GAAOzF,EAAE3F,WAAW,KAAOkL,mBAI7B,KAAwB,IAAbC,UAGJZ,eAAenI,GAAG,CAAC1B,OAAQyK,SACpC,EACAR,eAAejK,MAAM,CAAE2J,IAAI,EACzB,GAAI,iBAAOA,KACT,OAAOE,eAAeI,cAAc,CAACjK,OAAQ2J,MAE/C,IAAMa,WAAab,KAAKrK,WAAW,GAK7BmL,SAAWzO,OAAOgG,IAAI,CAAC8B,SAAS4G,IAAI,CACxC,GAAOzF,EAAE3F,WAAW,KAAOkL,mBAI7B,KAAwB,IAAbC,UAGJZ,eAAeI,cAAc,CAACjK,OAAQyK,SAC/C,CACF,EACF,CAMA,OAAcE,KAAK7G,OAAgB,CAAmB,CACpD,OAAO,IAAIyG,MAAuBzG,QAAS,CACzC5D,IAAIF,MAAM,CAAE2J,IAAI,CAAEG,QAAQ,EACxB,OAAQH,MACN,IAAK,SACL,IAAK,SACL,IAAK,MACH,OAAOO,qBAAqBE,QAAQ,SAEpC,OAAOP,eAAe3J,GAAG,CAACF,OAAQ2J,KAAMG,SAC5C,CACF,CACF,EACF,CASA,MAAchM,KAAwB,CAAU,QAC9C,MAAUgE,OAAO,CAAChE,OAAeA,MAAME,IAAI,CAAC,MAErCF,KACT,CAQA,OAAc2C,KAAKqD,OAAsC,CAAW,QAClE,mBAAuBwG,QAAgBxG,QAEhC,IAAIuG,eAAevG,QAC5B,CAEOE,OAAOpG,IAAY,CAAEE,KAAa,CAAQ,CAC/C,IAAM8M,SAAW,IAAI,CAAC9G,OAAO,CAAClG,KAAK,CACX,UAApB,OAAOgN,SACT,IAAI,CAAC9G,OAAO,CAAClG,KAAK,CAAG,CAACgN,SAAU9M,MAAM,CAC7ByD,MAAMO,OAAO,CAAC8I,UACvBA,SAASpH,IAAI,CAAC1F,OAEd,IAAI,CAACgG,OAAO,CAAClG,KAAK,CAAGE,KAEzB,CAEO6D,OAAO/D,IAAY,CAAQ,CAChC,OAAO,IAAI,CAACkG,OAAO,CAAClG,KAAK,CAGpBsC,IAAItC,IAAY,CAAiB,CACtC,IAAME,MAAQ,IAAI,CAACgG,OAAO,CAAClG,KAAK,QAChC,KAAqB,IAAVE,MAA8B,IAAI,CAAC+M,KAAK,CAAC/M,OAE7C,IACT,CAEO4D,IAAI9D,IAAY,CAAW,CAChC,OAAO,KAA8B,IAAvB,IAAI,CAACkG,OAAO,CAAClG,KAAK,CAG3Ba,IAAIb,IAAY,CAAEE,KAAa,CAAQ,CAC5C,IAAI,CAACgG,OAAO,CAAClG,KAAK,CAAGE,KACvB,CAEOgN,QACLC,UAAkE,CAClEC,OAAa,CACP,CACN,IAAK,GAAM,CAACpN,KAAME,MAAM,GAAI,IAAI,CAACmN,OAAO,GACtCF,WAAWnK,IAAI,CAACoK,QAASlN,MAAOF,KAAM,IAAI,CAE9C,CAEA,CAAQqN,SAA6C,CACnD,IAAK,IAAMvM,OAAO1C,OAAOgG,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAC3C,IAAMlG,KAAOc,IAAIY,WAAW,GAGtBxB,MAAQ,IAAI,CAACoC,GAAG,CAACtC,KAEvB,MAAM,CAACA,KAAME,MAAM,CAEvB,CAEA,CAAQkE,MAAgC,CACtC,IAAK,IAAMtD,OAAO1C,OAAOgG,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAC3C,IAAMlG,KAAOc,IAAIY,WAAW,EAC5B,OAAM1B,IACR,CACF,CAEA,CAAQyE,QAAkC,CACxC,IAAK,IAAM3D,OAAO1C,OAAOgG,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAG3C,IAAMhG,MAAQ,IAAI,CAACoC,GAAG,CAACxB,IAEvB,OAAMZ,KACR,CACF,CAEO,CAACoD,OAAOC,QAAQ,CAAC,EAAsC,CAC5D,OAAO,IAAI,CAAC8J,OAAO,EACrB,CACF,CChOO,IAAMC,4BAA8B,yBAC9BC,2CACX,sCA6FIC,qBAAuB,CAI3BC,OAAQ,SAKRC,sBAAuB,MAIvBC,oBAAqB,MAIrBC,cAAe,iBAIfC,QAAS,WAITC,QAAS,WAITC,WAAY,aAIZC,WAAY,aAIZC,UAAW,aAIXC,gBAAiB,oBAIjBC,gBAAiB,oBAIjBC,aAAc,iBAIdC,aAAc,gBAChB,EAKuB,EACrB,GAAGb,oBAAoB,CACvBc,MAAO,CACLC,aAAc,CACZf,qBAAqBE,qBAAqB,CAC1CF,qBAAqBI,aAAa,CACnC,CACDY,WAAY,CACVhB,qBAAqBE,qBAAqB,CAC1CF,qBAAqBI,aAAa,CAClCJ,qBAAqBQ,UAAU,CAC/BR,qBAAqBO,UAAU,CAChC,CACDU,cAAe,CAEbjB,qBAAqBK,OAAO,CAC5BL,qBAAqBM,OAAO,CAC7B,CACDY,WAAY,CACVlB,qBAAqBG,mBAAmB,CACxCH,qBAAqBU,eAAe,CACrC,CACDS,QAAS,CACPnB,qBAAqBE,qBAAqB,CAC1CF,qBAAqBI,aAAa,CAClCJ,qBAAqBG,mBAAmB,CACxCH,qBAAqBU,eAAe,CACpCV,qBAAqBC,MAAM,CAC3BD,qBAAqBQ,UAAU,CAC/BR,qBAAqBO,UAAU,CAChC,CACDa,SAAU,CAERpB,qBAAqBE,qBAAqB,CAC1CF,qBAAqBG,mBAAmB,CACxCH,qBAAqBU,eAAe,CACpCV,qBAAqBI,aAAa,CACnC,CAEL,GCvMA,IAAM,uBAA+B7D,QAAQ,qCCU7C,yCAAK8E,cAAc,E,qqBAAdA,c,EAAAA,gBAAAA,CAAAA,GAeL,yCAAKC,kBAAkB,E,mKAAlBA,kB,EAAAA,oBAAAA,CAAAA,GAKL,qCAAKC,cAAc,E,2PAAdA,c,EAAAA,gBAAAA,CAAAA,GAOL,yCAAKC,kBAAkB,E,u6DAAlBA,kB,EAAAA,oBAAAA,CAAAA,GAmCL,sCAAKC,eAAe,E,6DAAfA,e,EAAAA,iBAAAA,CAAAA,GAIL,iCAAKC,UAAU,E,6QAAVA,U,EAAAA,YAAAA,CAAAA,GAQL,oCAAKC,aAAa,E,mOAAbA,a,EAAAA,eAAAA,CAAAA,GAOL,iCAAKC,UAAU,E,qDAAVA,U,EAAAA,YAAAA,CAAAA,GAIL,+BAAKC,QAAQ,E,6CAARA,Q,EAAAA,UAAAA,CAAAA,GAIL,gDAAKC,yBAAyB,E,+EAAzBA,yB,EAAAA,2BAAAA,CAAAA,GAIL,0CAAKC,mBAAmB,E,uJAAnBA,mB,EAAAA,qBAAAA,CAAAA,GAKL,qCAAKC,cAAc,E,mDAAdA,c,EAAAA,gBAAAA,CAAAA,GCXE,IAAMC,6BAA+B,qBAC/BC,2BAA6B,sBAI7BC,oBAAsBrM,OAAOoM,4BAC7BE,uBAAyBtM,OAAOmM,8BAEtC,SAASI,iBACdC,GAAuB,CACvBzJ,QAEI,CAAC,CAAC,EAEN,GAAIuJ,0BAA0BE,IAC5B,OAAOA,IAGT,GAAM,CAAErG,SAAS,CAAE,CACjBM,oBAAQ,mEACJgG,SAAWD,IAAI5G,SAAS,CAAC,cAuC/B,OAtCA4G,IAAIE,SAAS,CAAC,aAAc,IACtB,iBAAOD,SACP,CAACA,SAAS,CACVpM,MAAMO,OAAO,CAAC6L,UACZA,SACA,EAAE,CACRtG,UAAUgG,6BAA8B,GAAI,CAI1CtQ,QAAS,IAAIC,KAAK,GAClBK,SAAU,GACVC,SAA4D,MAC5DF,OAAQyQ,CAAAA,EACR/Q,KAAM,IACN,GAAImH,KAAiBqB,IAAjBrB,QAAQnH,IAAI,CACX,CAAEA,KAAMmH,QAAQnH,IAAI,EACrBwI,KAAAA,CAAS,GAEf+B,UAAUiG,2BAA4B,GAAI,CAIxCvQ,QAAS,IAAIC,KAAK,GAClBK,SAAU,GACVC,SAA4D,MAC5DF,OAAQyQ,CAAAA,EACR/Q,KAAM,IACN,GAAImH,KAAiBqB,IAAjBrB,QAAQnH,IAAI,CACX,CAAEA,KAAMmH,QAAQnH,IAAI,EACrBwI,KAAAA,CAAS,GAEhB,EAEDtJ,OAAOC,cAAc,CAACyR,IAAKF,uBAAwB,CACjD1P,MAAO,GACPqC,WAAY,EACd,GACOuN,GACT,CAKO,MAAMI,iBAAiB3D,MAG5BtJ,YAAYkN,UAAkB,CAAEC,OAAe,CAAE,CAC/C,KAAK,CAACA,SACN,IAAI,CAACD,UAAU,CAAGA,UACpB,CACF,CAQO,SAASE,UACdP,GAAoB,CACpBK,UAAkB,CAClBC,OAAe,EAEfN,IAAIK,UAAU,CAAGA,WACjBL,IAAIQ,aAAa,CAAGF,QACpBN,IAAIS,GAAG,CAACH,QACV,CAYO,SAASI,YACd,CAAEC,GAAG,CAAa,CAClB1E,IAAY,CACZL,MAAe,EAEf,IAAMgF,KAAO,CAAEC,aAAc,GAAMpO,WAAY,EAAK,EAC9CqO,UAAY,CAAE,GAAGF,IAAI,CAAEG,SAAU,EAAK,EAE5CzS,OAAOC,cAAc,CAACoS,IAAK1E,KAAM,CAC/B,GAAG2E,IAAI,CACPpO,IAAK,KACH,IAAMpC,MAAQwL,SAGd,OADAtN,OAAOC,cAAc,CAACoS,IAAK1E,KAAM,CAAE,GAAG6E,SAAS,CAAE1Q,KAAM,GAChDA,KACT,EACAW,IAAK,QACHzC,OAAOC,cAAc,CAACoS,IAAK1E,KAAM,CAAE,GAAG6E,SAAS,CAAE1Q,KAAM,EACzD,CACF,EACF,CC3LO,MAAe4Q,YAqBpB7N,YAAY,CAAE8N,QAAQ,CAAElF,UAAU,CAA4B,CAAE,CAC9D,IAAI,CAACkF,QAAQ,CAAGA,SAChB,IAAI,CAAClF,UAAU,CAAGA,UACpB,CACF,C,wHC9CO,IAAMmF,QAAU,MACrB,IAAMC,IAAMC,IAAI/Q,MAAM,CAClB+G,EAAI,EACNiK,GAAK,EACLC,GAAK,KACLC,GAAK,EACLC,GAAK,MACLC,GAAK,EACLC,GAAK,MACLC,GAAK,EACLC,GAAK,MAEP,KAAOxK,EAAI+J,KACTG,IAAMF,IAAIpH,UAAU,CAAC5C,KACrBiK,GAAKC,IAAAA,GACLC,GAAKC,IAAAA,GACLC,GAAKC,IAAAA,GACLC,GAAKC,IAAAA,GACLH,IAAMH,IAAM,EACZK,IAAMH,IAAM,EACZD,IAAMF,KAAO,GACbC,GAAKD,MAAAA,GACLI,IAAMF,KAAO,GACbC,GAAKD,MAAAA,GACLK,GAAK,GAAOH,CAAAA,KAAO,EAAC,EAAM,MAC1BC,GAAKD,MAAAA,GAGP,MACE,CAACG,GAAAA,EAAM,EAAK,gBACZF,YAAAA,GACAF,MAAAA,GACCF,CAAAA,GAAMM,IAAM,EAEjB,EAEaC,aAAe,CAACC,QAAiBC,KAAO,EAAK,GAGtDC,CAFaD,KAAO,MAAQ,GAAE,EAErBb,QAAQY,SAASpN,QAAQ,CAAC,IAAMoN,QAAQzR,MAAM,CAACqE,QAAQ,CAAC,IAAM,GC+WzEuN,CAFuC,aAAvB,OAAOC,aAGvB,CAAE,OAAQ,UAAW,mBAAmB,CAAWC,KAAK,CACtD,QAAY,mBAAOD,WAAW,CAACE,OAAO,E,wHCja1C,IAAM,gCAA+BnI,QAAQ,UCe9B,SAASoI,QAAQC,GAAY,EAC1C,MACE,iBAAOA,KAAoBA,OAAAA,KAAgB,SAAUA,KAAO,YAAaA,GAE7E,C,0KCSO,eAAeC,UACpB5B,GAAoB,CACpB6B,KAAgB,MAEZC,YASAtH,OARJ,GAAI,CACFsH,YAAc/L,CAAAA,EAAAA,aAAAA,KAAAA,EAAMiK,IAAIvK,OAAO,CAAC,eAAe,EAAI,aACrD,CAAE,KAAM,CACNqM,YAAc/L,CAAAA,EAAAA,aAAAA,KAAAA,EAAM,aACtB,CACA,GAAM,CAAEqC,IAAI,CAAEF,UAAU,CAAE,CAAG4J,YACvBC,SAAW7J,WAAW8J,OAAO,EAAI,QAIvC,GAAI,CACF,IAAMC,WACJ3I,oBAAQ,iEACVkB,OAAS,MAAMyH,WAAWjC,IAAK,CAAE+B,SAAUF,KAAM,EACnD,CAAE,MAAOhM,EAAG,CACV,GAAI6L,QAAQ7L,IAAMA,qBAAAA,EAAEuC,IAAI,CACtB,MAAM,qBAAiD,CAAjD,IAAIqH,SAAS,IAAK,CAAC,cAAc,EAAEoC,MAAM,MAAM,CAAC,EAAhD,qB,MAAA,O,WAAA,G,aAAA,EAAgD,EAEtD,OAAM,qBAAiC,CAAjC,IAAIpC,SAAS,IAAK,gBAAlB,qB,MAAA,O,WAAA,G,aAAA,EAAgC,EAE1C,CAEA,IAAMyC,KAAO1H,OAAOzG,QAAQ,SAE5B,qBAAIqE,MAA+BA,wBAAAA,KAC1B+J,SA/CQ1B,GAAW,EAC5B,GAAIA,IAAAA,IAAI/Q,MAAM,CAEZ,MAAO,CAAC,EAGV,GAAI,CACF,OAAOmE,KAAKkC,KAAK,CAAC0K,IACpB,CAAE,MAAO5K,EAAG,CACV,MAAM,qBAAiC,CAAjC,IAAI4J,SAAS,IAAK,gBAAlB,qB,MAAA,O,WAAA,G,aAAA,EAAgC,EACxC,CACF,EAoCqByC,MACR9J,sCAAAA,KAEFgK,oBADY,iCACTtJ,MAAM,CAACoJ,MAEVA,IAEX,CCiEA,SAASG,YAAY5B,GAAQ,EAC3B,MAAO,iBAAOA,KAAoBA,IAAI/Q,MAAM,EAAI,EAClD,CAuHA,eAAe4S,WACbC,OAAe,CACftC,IAEC,CACDD,GAAoB,CACpBwC,OAAmB,EAEnB,GAAI,iBAAOD,SAAwB,CAACA,QAAQE,UAAU,CAAC,KACrD,MAAM,qBAEL,CAFK,MACJ,CAAC,qFAAqF,EAAEF,QAAQ,CAAC,EAD7F,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF,IAAMG,kBAAiC,CACrC,CAAC7F,4BAA4B,CAAE2F,QAAQG,aAAa,CACpD,GAAI1C,KAAK2C,sBAAsB,CAC3B,CACE,CAAC9F,2CAA2C,CAAE,GAChD,EACA,CAAC,CAAC,EAEF+F,4BAA8B,IAC9BL,QAAQK,2BAA2B,EAAI,EAAE,CAC9C,CAUD,IAAK,IAAMxS,OARPmS,CAAAA,QAAQM,eAAe,EAAIN,QAAQO,GAAG,GACxCF,4BAA4B1N,IAAI,CAAC,UAG/BqN,QAAQM,eAAe,EACzBD,4BAA4B1N,IAAI,CAAC,8BAGjBxH,OAAOgG,IAAI,CAACqM,IAAIvK,OAAO,GACnCoN,4BAA4BrR,QAAQ,CAACnB,MACvCqS,CAAAA,iBAAiB,CAACrS,IAAI,CAAG2P,IAAIvK,OAAO,CAACpF,IAAI,EAI7C,GAAI,CACF,GAAImS,QAAQM,eAAe,CAAE,CAC3B,IAAMzD,IAAM,MAAM2D,MAAM,CAAC,QAAQ,EAAEhD,IAAIvK,OAAO,CAACwN,IAAI,CAAC,EAAEV,QAAQ,CAAC,CAAE,CAC/Dd,OAAQ,OACRhM,QAASiN,iBACX,GAIMQ,YACJ7D,IAAI5J,OAAO,CAAC5D,GAAG,CAAC,mBAAqBwN,IAAI5J,OAAO,CAAC5D,GAAG,CAAC,kBAEvD,GACEqR,CAAAA,MAAAA,YAAAA,KAAAA,EAAAA,YAAaC,WAAW,EAAC,IAAM,eAC/B9D,MAAAA,IAAI+D,MAAM,EACV,CAAE/D,CAAAA,MAAAA,IAAI+D,MAAM,EAAYnD,KAAK2C,sBAAsB,EAEnD,MAAM,qBAA2C,CAA3C,MAAU,CAAC,iBAAiB,EAAEvD,IAAI+D,MAAM,CAAC,CAAC,EAA1C,qB,MAAA,O,WAAA,G,aAAA,EAA0C,EAEpD,MAAO,GAAIZ,QAAQF,UAAU,CAC3B,MAAME,QAAQF,UAAU,CAAC,CACvBC,QACAG,kBACAzC,IACF,QAEA,MAAM,qBAEL,CAFK,MACJ,0EADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAEJ,CAAE,MAAO0B,IAAc,CACrB,MAAM,qBAEL,CAFK,MACJ,CAAC,qBAAqB,EAAEY,QAAQ,EAAE,EAAEb,QAAQC,KAAOA,IAAIhC,OAAO,CAAGgC,IAAI,CAAC,EADlE,qB,MAAA,O,WAAA,G,aAAA,EAEN,EACF,CACF,CAEO,eAAe0B,YACpBrD,GAAoB,CACpBX,GAAmB,CACnBiE,KAAU,CACVC,cAAmB,CACnBC,UAAsB,CACtBC,cAAuB,CACvBV,GAAa,CACbW,IAAa,CACbC,OAA6C,EAK7C,GAAI,KAOiBC,YACGA,aACGA,aCvVGnO,QD+U5B,GAAI,CAAC8N,eAAgB,CACnBlE,IAAIK,UAAU,CAAG,IACjBL,IAAIS,GAAG,CAAC,aACR,MACF,CACA,IAAM8D,OAAqBL,eAAeK,MAAM,EAAI,CAAC,EAC/CC,WAAaD,CAAAA,MAAAA,CAAAA,YAAAA,OAAOE,GAAG,SAAVF,YAAYC,UAAU,IAAK,GACxCE,cAAgBH,CAAAA,MAAAA,CAAAA,aAAAA,OAAOE,GAAG,SAAVF,aAAYG,aAAa,GAAI,GAC7CC,iBAAmBJ,CAAAA,MAAAA,CAAAA,aAAAA,OAAOE,GAAG,SAAVF,aAAYI,gBAAgB,GAAI,GAGzDjE,YAAY,CAAEC,IAfDA,GAea,EAAG,WC1VDvK,QD0V4BuK,IAAIvK,OAAO,CCvV9D,WACL,GAAM,CAAE5F,MAAM,CAAE,CAAG4F,QAEnB,GAAI,CAAC5F,OACH,MAAO,CAAC,EAGV,GAAM,CAAEkG,MAAOkO,aAAa,CAAE,CAAG3K,oBAAQ,mEACzC,OAAO2K,cAAc/Q,MAAMO,OAAO,CAAC5D,QAAUA,OAAOF,IAAI,CAAC,MAAQE,OACnE,IDgVEqU,IAAOZ,KAAK,CAAGA,MAEfvD,YAAY,CAAEC,IAnBDA,GAmBa,EAAG,cAAe,IAC1CmE,CEtVC,SACLnE,GAAgD,CAChDX,GAAsC,CACtCzJ,OAA0B,CAC1BwO,kBAA2B,MAiBLC,aACGA,kBAwCrBC,qBAtDJ,GAAI1O,SAAW2O,STuDfvE,GAAgD,CAChDwE,YAA+B,EAK/B,IAAM/O,QAAUuG,eAAe5J,IAAI,CAAC4N,IAAIvK,OAAO,EAS/C,MAAO,CAAEgP,qBANoB9B,QADC9Q,GAAG,CAACgL,+BACa2H,aAAa7B,aAAa,CAM1C+B,wBAJCjP,QAAQpC,GAAG,CACzCyJ,2CAGqD,CACzD,ESvE2CkD,IAAKpK,SAAS6O,oBAAoB,CACzE,MAAO,GAKT,GAAIvF,uBAAuBc,IACzB,OAAO,GAAY,CAACd,oBAAoB,CAG1C,IAAMzJ,QAAUuG,eAAe5J,IAAI,CAAC4N,IAAIvK,OAAO,EACzC4O,QAAU,IAAItS,sBAAAA,cAAcA,CAAC0D,SAE7BkN,cAAgB,MAAA0B,CAAAA,aAAAA,QAAQxS,GAAG,CAACmN,6BAA4BA,EAAAA,KAAAA,EAAxCqF,aAA2C5U,KAAK,CAChEkV,iBAAmB,MAAAN,CAAAA,cAAAA,QAAQxS,GAAG,CAACoN,2BAA0BA,EAAAA,KAAAA,EAAtCoF,cAAyC5U,KAAK,CAGvE,GACEkT,eACA,CAACgC,kBACDhC,gBAAkB/M,QAAQ+M,aAAa,CACvC,CAIA,IAAMjJ,KAAO,CAAC,EAKd,OAJA/L,OAAOC,cAAc,CAACoS,IAAKd,oBAAqB,CAC9CzP,MAAOiK,KACP5H,WAAY,EACd,GACO4H,IACT,CAGA,GAAI,CAACiJ,eAAiB,CAACgC,iBACrB,MAAO,GAIT,GAAI,CAAChC,eAAiB,CAACgC,kBAQnBhC,gBAAkB/M,QAAQ+M,aAAa,CAJzC,OAHKyB,oBACHhF,iBAAiBC,KAEZ,GAcT,GAAI,CAGFiF,qBAAuBM,oBADb,yEAC0BC,MAAM,CACxCF,iBACA/O,QAAQkP,qBAAqB,CAEjC,CAAE,KAAM,CAGN,OADA1F,iBAAiBC,KACV,EACT,CAEA,GAAM,CAAE/E,iBAAiB,CAAE,CACzBhB,oBAAQ,8DACJyL,qBAAuBzK,kBAC3BN,OAAO5H,IAAI,CAACwD,QAAQoP,wBAAwB,EAC5CV,qBAAqB5K,IAAI,EAG3B,GAAI,CAEF,IAAMA,KAAO7F,KAAKkC,KAAK,CAACgP,sBAMxB,OAJApX,OAAOC,cAAc,CAACoS,IAAKd,oBAAqB,CAC9CzP,MAAOiK,KACP5H,WAAY,EACd,GACO4H,IACT,CAAE,KAAM,CACN,MAAO,EACT,CACF,GFqPwBsG,IAAKX,IAAKmE,WAAY,CAAC,CAACA,WAAWY,kBAAkB,GAGzErE,YAAY,CAAEC,IAvBDA,GAuBa,EAAG,UAAW,IACtCkE,CAAuB,IAAvBA,IAAOe,WAAW,EAAoBhO,KAAAA,GAGxC8I,YAAY,CAAEC,IA3BDA,GA2Ba,EAAG,YAAa,IAAMkE,IAAOgB,OAAO,EAG1DrB,YAAc,CAACK,IAAOhC,IAAI,EAC5BgC,CAAAA,IAAOhC,IAAI,CAAG,MAAMN,UA/BT5B,IAiCT4D,OAAOE,GAAG,EAAIF,OAAOE,GAAG,CAACD,UAAU,EAAID,OAAOE,GAAG,CAACD,UAAU,CAACsB,SAAS,CAClEvB,OAAOE,GAAG,CAACD,UAAU,CAACsB,SAAS,CAC/B,MAAK,EAIb,IAAIC,cAAgB,EACdC,iBAzUR,eAAqB,kBAyU0BtB,cAxUtCuB,gBAAAA,KAAW,CAwU2BvB,ePtRX,QOuR5BwB,UAAYC,IAAOC,KAAK,CACxBC,YAAcF,IAAO1F,GAAG,CAzCjBT,IA0CNoG,KAAK,CAAG,CAAC,GAAGzS,QACjBoS,eAAiBpL,OAAO2L,UAAU,CAAC3S,IAAI,CAAC,EAAE,EAAI,IACvCuS,UAAUK,KAAK,CA5CXvG,IA4CoBrM,OAEjCwS,IAAO1F,GAAG,CAAG,CAAC,GAAG9M,QACXA,KAAKtD,MAAM,EAAI,mBAAOsD,IAAI,CAAC,EAAE,EAC/BoS,CAAAA,eAAiBpL,OAAO2L,UAAU,CAAC3S,IAAI,CAAC,EAAE,EAAI,GAAE,EAG9C+Q,eAAiBqB,eAAiBC,kBACpCQ,QAAQC,IAAI,CACV,CAAC,iBAAiB,EAAE9F,IAAI+F,GAAG,CAAC,SAAS,EAAET,gBAAAA,MAAY,CACjDD,kBACA,0GAA0G,CAAC,EAI1GK,YAAYE,KAAK,CA3DbvG,IA2DsBrM,OAEnCwS,IAAOpC,MAAM,CAAG,aPhWlB/D,IAAIK,UAAU,COgW2CA,WA7D1CL,KA8DbmG,IAAOQ,IAAI,CAAG,MAAUC,CApV5B,SAAkBjG,GAAmB,CAAEX,GAAoB,CAAE6C,IAAS,MGhDpEgE,KHiDA,GAAIhE,MAAAA,KAAqC,CACvC7C,IAAIS,GAAG,GACP,MACF,CAGA,GAAIT,MAAAA,IAAIK,UAAU,EAAYL,MAAAA,IAAIK,UAAU,CAAU,CACpDL,IAAI8G,YAAY,CAAC,gBACjB9G,IAAI8G,YAAY,CAAC,kBACjB9G,IAAI8G,YAAY,CAAC,qBAE6BjE,MAC5C2D,QAAQC,IAAI,CACV,CAAC,yDAAyD,EAAE9F,IAAI+F,GAAG,CAAC;AAA6C,4EAAC,EAItH1G,IAAIS,GAAG,GACP,MACF,CAEA,IAAMgC,YAAczC,IAAI5G,SAAS,CAAC,gBAElC,GAAIyJ,gBAAgBkE,gCAAAA,MAAMA,CAAE,CACrBtE,aACHzC,IAAIE,SAAS,CAAC,eAAgB,4BAEhC2C,KAAKmE,IAAI,CAAChH,KACV,MACF,CAEA,IAAMiH,WAAa,CAAC,SAAU,SAAU,UAAU,CAAC9U,QAAQ,CAAC,OAAO0Q,MAC7DqE,gBAAkBD,WAAazS,KAAKC,SAAS,CAACoO,MAAQA,KAE5D,IGnFAgE,KHkFahF,aAAaqF,mBGzExBlH,IAAIE,SAAS,CAAC,OAAQ2G,OAGpBM,gBAAMxG,IAAIvK,OAAO,CAAE,CAAEyQ,IAAK,KAC5B7G,IAAIK,UAAU,CAAG,IACjBL,IAAIS,GAAG,OHyET,GAAI9F,OAAOyM,QAAQ,CAACvE,MAAO,CACpBJ,aACHzC,IAAIE,SAAS,CAAC,eAAgB,4BAEhCF,IAAIE,SAAS,CAAC,iBAAkB2C,KAAKxS,MAAM,EAC3C2P,IAAIS,GAAG,CAACoC,MACR,MACF,CAEIoE,YACFjH,IAAIE,SAAS,CAAC,eAAgB,mCAGhCF,IAAIE,SAAS,CAAC,iBAAkBvF,OAAO2L,UAAU,CAACY,kBAClDlH,IAAIS,GAAG,CAACyG,iBACV,GA+NiBvG,IACAX,IA8DoC3F,MACjD8L,IAAOkB,IAAI,CAAG,OAtRhBrH,IAAIE,SAAS,CAAC,eAAgB,mCAG9BF,IAAI2G,IAAI,CAACnS,KAAKC,SAAS,CAmRoB4F,QACzC8L,IAAOmB,QAAQ,CAAG,CAACC,YAA8Bb,MAC/CY,CP1VC,SACLtH,GAAoB,CACpBuH,WAA4B,CAC5Bb,GAAY,EAMZ,GAJ2B,UAAvB,OAAOa,cACTb,IAAMa,YACNA,YAAc,KAEZ,iBAAOA,aAA4B,iBAAOb,IAC5C,MAAM,qBAEL,CAFK,MACJ,yKADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAKF,OAHA1G,IAAIwH,SAAS,CAACD,YAAa,CAAEE,SAAUf,GAAI,GAC3C1G,IAAIoG,KAAK,CAACM,KACV1G,IAAIS,GAAG,GACAT,GACT,GOuQiBA,IAiEMuH,YAAab,KAChCP,IAAOuB,YAAY,CAAG,CAACnR,QAAU,CAAEoR,OAAQ,EAAK,CAAC,GAC/CD,CAhRN,SACE1H,GAAuB,CACvBzJ,OAGC,EAED,GAAI,CAACyM,YAAYzM,QAAQ+M,aAAa,EACpC,MAAM,qBAA6C,CAA7C,MAAU,oCAAV,qB,MAAA,O,WAAA,G,aAAA,EAA4C,GAEpD,IAAMjU,QAAUkH,QAAQoR,MAAM,CAAG/P,KAAAA,EAAY,IAAItI,KAAK,GAIhD,CAAEqK,SAAS,CAAE,CACjBM,oBAAQ,mEACJgG,SAAWD,IAAI5G,SAAS,CAAC,cAe/B,OAdA4G,IAAIE,SAAS,CAAC,aAAc,IACtB,iBAAOD,SACP,CAACA,SAAS,CACVpM,MAAMO,OAAO,CAAC6L,UACZA,SACA,EAAE,CACRtG,UAAUgG,6BAA8BpJ,QAAQ+M,aAAa,CAAE,CAC7D3T,SAAU,GACVC,SAA4D,MAC5DF,OAAQyQ,CAAAA,EACR/Q,KAAM,IACNC,OACF,GACD,EACM2Q,GACT,GA6KiBA,IAmEU1R,OAAOsZ,MAAM,CAAC,CAAC,EAAGzD,WAAY5N,UACrD4P,IAAO0B,cAAc,CAAG,CAACxN,KAAM9D,QAAU,CAAC,CAAC,GACzCsR,CAhPN,SACE7H,GAAuB,CACvB3F,IAAqB,CACrB9D,OAGqB,EAErB,GAAI,CAACyM,YAAYzM,QAAQ+M,aAAa,EACpC,MAAM,qBAA6C,CAA7C,MAAU,oCAAV,qB,MAAA,O,WAAA,G,aAAA,EAA4C,GAEpD,GAAI,CAACN,YAAYzM,QAAQoP,wBAAwB,EAC/C,MAAM,qBAAwD,CAAxD,MAAU,+CAAV,qB,MAAA,O,WAAA,G,aAAA,EAAuD,GAE/D,GAAI,CAAC3C,YAAYzM,QAAQkP,qBAAqB,EAC5C,MAAM,qBAAqD,CAArD,MAAU,4CAAV,qB,MAAA,O,WAAA,G,aAAA,EAAoD,GAG5D,IAAMF,aACJtL,oBAAQ,yEACJ,CAAEE,iBAAiB,CAAE,CACzBF,oBAAQ,8DACJ6H,QAAUyD,aAAauC,IAAI,CAC/B,CACEzN,KAAMF,kBACJQ,OAAO5H,IAAI,CAACwD,QAAQoP,wBAAwB,EAC5CnR,KAAKC,SAAS,CAAC4F,MAEnB,EACA9D,QAAQkP,qBAAqB,CAC7B,CACEsC,UAAW,QACX,GAAIxR,KAAmBqB,IAAnBrB,QAAQ/G,MAAM,CACd,CAAEwY,UAAWzR,QAAQ/G,MAAM,EAC3BoI,KAAAA,CAAS,GAMjB,GAAIkK,QAAQzR,MAAM,CAAG,KACnB,MAAM,qBAEL,CAFK,MACJ,8GADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAM,CAAEsJ,SAAS,CAAE,CACjBM,oBAAQ,mEACJgG,SAAWD,IAAI5G,SAAS,CAAC,cAgC/B,OA/BA4G,IAAIE,SAAS,CAAC,aAAc,IACtB,iBAAOD,SACP,CAACA,SAAS,CACVpM,MAAMO,OAAO,CAAC6L,UACZA,SACA,EAAE,CACRtG,UAAUgG,6BAA8BpJ,QAAQ+M,aAAa,CAAE,CAC7D3T,SAAU,GACVC,SAA4D,MAC5DF,OAAQyQ,CAAAA,EACR/Q,KAAM,IACN,GAAImH,KAAmBqB,IAAnBrB,QAAQ/G,MAAM,CACb,CAAEA,OAAQ+G,QAAQ/G,MAAM,EACzBoI,KAAAA,CAAS,CACb,GAAIrB,KAAiBqB,IAAjBrB,QAAQnH,IAAI,CACX,CAAEA,KAAMmH,QAAQnH,IAAI,EACrBwI,KAAAA,CAAS,GAEf+B,UAAUiG,2BAA4BkC,QAAS,CAC7CnS,SAAU,GACVC,SAA4D,MAC5DF,OAAQyQ,CAAAA,EACR/Q,KAAM,IACN,GAAImH,KAAmBqB,IAAnBrB,QAAQ/G,MAAM,CACb,CAAEA,OAAQ+G,QAAQ/G,MAAM,EACzBoI,KAAAA,CAAS,CACb,GAAIrB,KAAiBqB,IAAjBrB,QAAQnH,IAAI,CACX,CAAEA,KAAMmH,QAAQnH,IAAI,EACrBwI,KAAAA,CAAS,GAEhB,EACMoI,GACT,GA0FiBA,IAqEY3F,KAAM/L,OAAOsZ,MAAM,CAAC,CAAC,EAAGzD,WAAY5N,UAC7D4P,IAAOpG,gBAAgB,CAAG,CAACxJ,QAAU,CAAC,CAAC,GACrCwJ,iBAvEWC,IAuEczJ,SAC3B4P,IAAOlD,UAAU,CAAG,CAClBC,QACAtC,OAGGqC,WAAWC,QAAStC,MAAQ,CAAC,EAAGD,IAAKwD,YAE1C,IAAM8D,SIjaDC,eAAIC,OAAO,EJiagBjE,eAC5BkE,SAAW,GAIbpI,IAAIqI,IAAI,CAAC,OAAQ,IAAOD,SAAW,IAGrC,IAAME,eAAiB,MAAML,SAAStH,IAAKX,KAGzC,GAAI,KAA0B,IAAnBsI,eAAgC,CACzC,GAAIA,0BAA0BC,SAC5B,MAAM,qBAEL,CAFK,MACJ,gLADI,qB,MAAA,M,WAAA,G,aAAA,EAEN,GAEF/B,QAAQC,IAAI,CACV,CAAC,gDAAgD,EAAE,OAAO6B,eAAe,CAAC,CAAC,CAE/E,CAEK3D,kBJhGF3E,IAAIwI,QAAQ,EAAIxI,IAAIyI,WAAW,EIgGWL,UAC3C5B,QAAQC,IAAI,CACV,CAAC,4CAA4C,EAAE9F,IAAI+F,GAAG,CAAC,sCAAsC,CAAC,CAItG,CAAE,MAAOpE,IAAK,CAQZ,GAPAgC,MAAAA,SAAAA,QAAUhC,IAAK3B,IAAK,CAClB+H,WAAY,eACZC,UAAWtE,MAAQ,GACnBuE,UAAW,QACXC,iBAAkBjR,KAAAA,CACpB,GAEI0K,eAAelC,SACjBG,UApHWP,IAoHOsC,IAAIjC,UAAU,CAAEiC,IAAIhC,OAAO,MACxC,CACL,GAAIoD,IAIF,MAHIrB,QAAQC,MACVA,CAAAA,IAAI+B,IAAI,CAAGA,IAAG,EAEV/B,IAIR,GADAkE,QAAQsC,KAAK,CAACxG,KACV8B,eACF,MAAM9B,IAER/B,UAjIWP,IAiIO,IAAK,wBACzB,CACF,CACF,CKxWO,MAAM+I,4BAA4B/H,YAMvC7N,YAAYoD,OAAmC,CAAE,CAG/C,GAFA,KAAK,CAACA,SAEF,mBAAOA,QAAQ0K,QAAQ,CAACkH,OAAO,CACjC,MAAM,qBAEL,CAFK,MACJ,CAAC,KAAK,EAAE5R,QAAQwF,UAAU,CAACsI,IAAI,CAAC,oCAAoC,CAAC,EADjE,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAGF,KAAI,CAAC2E,kBAAkB,CAAGC,SZvG5B5E,IAAY,CACZ6E,OAAU,EAEV,MAAQ,CAAC,GAAGvV,QACVwV,CAAAA,EAAAA,uBAAAA,SAAAA,IAAYC,oBAAoB,CAAC,aAAc/E,MAExC8E,CAAAA,EAAAA,uBAAAA,SAAAA,IAAYE,KAAK,CACtB9J,SAAS+J,UAAU,CACnB,CACEC,SAAU,CAAC,4BAA4B,EAAElF,KAAK,CAAC,EAEjD,IAAM6E,WAAWvV,OAGvB,EY0FM4C,QAAQwF,UAAU,CAACsI,IAAI,CACvBL,YAEJ,CAQA,MAAawF,OACX7I,GAAoB,CACpBX,GAAmB,CACnBmD,OAAoC,CACrB,CACf,GAAM,CAAE6F,kBAAkB,CAAE,CAAG,IAAI,OAC7BA,mBACJrI,IACAX,IACAmD,QAAQc,KAAK,CACb,IAAI,CAAChD,QAAQ,CACb,CACE,GAAGkC,QAAQgC,YAAY,CACvBlC,WAAYE,QAAQF,UAAU,CAC9BQ,gBAAiBN,QAAQM,eAAe,CACxCD,4BAA6BL,QAAQK,2BAA2B,CAChEiG,SAAUtG,QAAQsG,QAAQ,CAC1B1E,mBAAoB5B,QAAQ4B,kBAAkB,CAC9CrB,IAAKP,QAAQO,GAAG,EAElBP,QAAQuG,WAAW,CACnBvG,QAAQO,GAAG,CACXP,QAAQkB,IAAI,CACZlB,QAAQmB,OAAO,CAEnB,CACF,CAEA,qBAAeyE,mB", "sources": ["webpack://next/./dist/compiled/@edge-runtime/cookies/index.js", "webpack://next/./dist/compiled/bytes/index.js", "webpack://next/./dist/compiled/content-type/index.js", "webpack://next/./dist/compiled/cookie/index.js", "webpack://next/./dist/compiled/fresh/index.js", "webpack://next/external node-commonjs \"crypto\"", "webpack://next/./dist/src/server/crypto-utils.ts", "webpack://next/external commonjs2 \"next/dist/compiled/jsonwebtoken\"", "webpack://next/external commonjs2 \"next/dist/compiled/raw-body\"", "webpack://next/external node-commonjs \"querystring\"", "webpack://next/webpack/bootstrap", "webpack://next/webpack/runtime/compat get default export", "webpack://next/webpack/runtime/define property getters", "webpack://next/webpack/runtime/hasOwnProperty shorthand", "webpack://next/webpack/runtime/make namespace object", "webpack://next/./dist/src/server/web/spec-extension/adapters/reflect.ts", "webpack://next/./dist/src/server/web/spec-extension/adapters/headers.ts", "webpack://next/./dist/src/lib/constants.ts", "webpack://next/external commonjs \"next/dist/server/lib/trace/tracer\"", "webpack://next/./dist/src/server/lib/trace/constants.ts", "webpack://next/./dist/src/server/api-utils/index.ts", "webpack://next/./dist/src/server/route-modules/route-module.ts", "webpack://next/./dist/src/server/lib/etag.ts", "webpack://next/./dist/src/shared/lib/utils.ts", "webpack://next/external node-commonjs \"stream\"", "webpack://next/./dist/src/lib/is-error.ts", "webpack://next/./dist/src/server/api-utils/node/parse-body.ts", "webpack://next/./dist/src/server/api-utils/node/api-resolver.ts", "webpack://next/./dist/src/server/api-utils/get-cookie-parser.ts", "webpack://next/./dist/src/server/api-utils/node/try-get-preview-data.ts", "webpack://next/./dist/src/server/send-payload.ts", "webpack://next/./dist/src/lib/interop-default.ts", "webpack://next/./dist/src/server/route-modules/pages-api/module.ts"], "sourcesContent": ["\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  RequestCookies,\n  ResponseCookies,\n  parseCookie,\n  parseSetCookie,\n  stringifyCookie\n});\n", "(()=>{\"use strict\";var e={56:e=>{\n/*!\n * bytes\n * Copyright(c) 2012-2014 <PERSON><PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\ne.exports=bytes;e.exports.format=format;e.exports.parse=parse;var r=/\\B(?=(\\d{3})+(?!\\d))/g;var a=/(?:\\.0*|(\\.[^0]+)0+)$/;var t={b:1,kb:1<<10,mb:1<<20,gb:1<<30,tb:Math.pow(1024,4),pb:Math.pow(1024,5)};var i=/^((-|\\+)?(\\d+(?:\\.\\d+)?)) *(kb|mb|gb|tb|pb)$/i;function bytes(e,r){if(typeof e===\"string\"){return parse(e)}if(typeof e===\"number\"){return format(e,r)}return null}function format(e,i){if(!Number.isFinite(e)){return null}var n=Math.abs(e);var o=i&&i.thousandsSeparator||\"\";var s=i&&i.unitSeparator||\"\";var f=i&&i.decimalPlaces!==undefined?i.decimalPlaces:2;var u=Boolean(i&&i.fixedDecimals);var p=i&&i.unit||\"\";if(!p||!t[p.toLowerCase()]){if(n>=t.pb){p=\"PB\"}else if(n>=t.tb){p=\"TB\"}else if(n>=t.gb){p=\"GB\"}else if(n>=t.mb){p=\"MB\"}else if(n>=t.kb){p=\"KB\"}else{p=\"B\"}}var b=e/t[p.toLowerCase()];var l=b.toFixed(f);if(!u){l=l.replace(a,\"$1\")}if(o){l=l.split(\".\").map((function(e,a){return a===0?e.replace(r,o):e})).join(\".\")}return l+s+p}function parse(e){if(typeof e===\"number\"&&!isNaN(e)){return e}if(typeof e!==\"string\"){return null}var r=i.exec(e);var a;var n=\"b\";if(!r){a=parseInt(e,10);n=\"b\"}else{a=parseFloat(r[1]);n=r[4].toLowerCase()}return Math.floor(t[n]*a)}}};var r={};function __nccwpck_require__(a){var t=r[a];if(t!==undefined){return t.exports}var i=r[a]={exports:{}};var n=true;try{e[a](i,i.exports,__nccwpck_require__);n=false}finally{if(n)delete r[a]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var a=__nccwpck_require__(56);module.exports=a})();", "(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;\n/*!\n * content-type\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */var t=/; *([!#$%&'*+.^_`|~0-9A-Za-z-]+) *= *(\"(?:[\\u000b\\u0020\\u0021\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\u000b\\u0020-\\u00ff])*\"|[!#$%&'*+.^_`|~0-9A-Za-z-]+) */g;var a=/^[\\u000b\\u0020-\\u007e\\u0080-\\u00ff]+$/;var n=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;var i=/\\\\([\\u000b\\u0020-\\u00ff])/g;var o=/([\\\\\"])/g;var f=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+\\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;r.format=format;r.parse=parse;function format(e){if(!e||typeof e!==\"object\"){throw new TypeError(\"argument obj is required\")}var r=e.parameters;var t=e.type;if(!t||!f.test(t)){throw new TypeError(\"invalid type\")}var a=t;if(r&&typeof r===\"object\"){var i;var o=Object.keys(r).sort();for(var u=0;u<o.length;u++){i=o[u];if(!n.test(i)){throw new TypeError(\"invalid parameter name\")}a+=\"; \"+i+\"=\"+qstring(r[i])}}return a}function parse(e){if(!e){throw new TypeError(\"argument string is required\")}var r=typeof e===\"object\"?getcontenttype(e):e;if(typeof r!==\"string\"){throw new TypeError(\"argument string is required to be a string\")}var a=r.indexOf(\";\");var n=a!==-1?r.substr(0,a).trim():r.trim();if(!f.test(n)){throw new TypeError(\"invalid media type\")}var o=new ContentType(n.toLowerCase());if(a!==-1){var u;var p;var s;t.lastIndex=a;while(p=t.exec(r)){if(p.index!==a){throw new TypeError(\"invalid parameter format\")}a+=p[0].length;u=p[1].toLowerCase();s=p[2];if(s[0]==='\"'){s=s.substr(1,s.length-2).replace(i,\"$1\")}o.parameters[u]=s}if(a!==r.length){throw new TypeError(\"invalid parameter format\")}}return o}function getcontenttype(e){var r;if(typeof e.getHeader===\"function\"){r=e.getHeader(\"content-type\")}else if(typeof e.headers===\"object\"){r=e.headers&&e.headers[\"content-type\"]}if(typeof r!==\"string\"){throw new TypeError(\"content-type header is missing from object\")}return r}function qstring(e){var r=String(e);if(n.test(r)){return r}if(r.length>0&&!a.test(r)){throw new TypeError(\"invalid parameter value\")}return'\"'+r.replace(o,\"\\\\$1\")+'\"'}function ContentType(e){this.parameters=Object.create(null);this.type=e}})();module.exports=e})();", "(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;\n/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */r.parse=parse;r.serialize=serialize;var i=decodeURIComponent;var t=encodeURIComponent;var a=/; */;var n=/^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;function parse(e,r){if(typeof e!==\"string\"){throw new TypeError(\"argument str must be a string\")}var t={};var n=r||{};var o=e.split(a);var s=n.decode||i;for(var p=0;p<o.length;p++){var f=o[p];var u=f.indexOf(\"=\");if(u<0){continue}var v=f.substr(0,u).trim();var c=f.substr(++u,f.length).trim();if('\"'==c[0]){c=c.slice(1,-1)}if(undefined==t[v]){t[v]=tryDecode(c,s)}}return t}function serialize(e,r,i){var a=i||{};var o=a.encode||t;if(typeof o!==\"function\"){throw new TypeError(\"option encode is invalid\")}if(!n.test(e)){throw new TypeError(\"argument name is invalid\")}var s=o(r);if(s&&!n.test(s)){throw new TypeError(\"argument val is invalid\")}var p=e+\"=\"+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f)){throw new TypeError(\"option maxAge is invalid\")}p+=\"; Max-Age=\"+Math.floor(f)}if(a.domain){if(!n.test(a.domain)){throw new TypeError(\"option domain is invalid\")}p+=\"; Domain=\"+a.domain}if(a.path){if(!n.test(a.path)){throw new TypeError(\"option path is invalid\")}p+=\"; Path=\"+a.path}if(a.expires){if(typeof a.expires.toUTCString!==\"function\"){throw new TypeError(\"option expires is invalid\")}p+=\"; Expires=\"+a.expires.toUTCString()}if(a.httpOnly){p+=\"; HttpOnly\"}if(a.secure){p+=\"; Secure\"}if(a.sameSite){var u=typeof a.sameSite===\"string\"?a.sameSite.toLowerCase():a.sameSite;switch(u){case true:p+=\"; SameSite=Strict\";break;case\"lax\":p+=\"; SameSite=Lax\";break;case\"strict\":p+=\"; SameSite=Strict\";break;case\"none\":p+=\"; SameSite=None\";break;default:throw new TypeError(\"option sameSite is invalid\")}}return p}function tryDecode(e,r){try{return r(e)}catch(r){return e}}})();module.exports=e})();", "(()=>{\"use strict\";var e={695:e=>{\n/*!\n * fresh\n * Copyright(c) 2012 <PERSON><PERSON>\n * Copyright(c) 2016-2017 <PERSON>\n * MIT Licensed\n */\nvar r=/(?:^|,)\\s*?no-cache\\s*?(?:,|$)/;e.exports=fresh;function fresh(e,a){var t=e[\"if-modified-since\"];var s=e[\"if-none-match\"];if(!t&&!s){return false}var i=e[\"cache-control\"];if(i&&r.test(i)){return false}if(s&&s!==\"*\"){var f=a[\"etag\"];if(!f){return false}var n=true;var u=parseTokenList(s);for(var _=0;_<u.length;_++){var o=u[_];if(o===f||o===\"W/\"+f||\"W/\"+o===f){n=false;break}}if(n){return false}}if(t){var p=a[\"last-modified\"];var v=!p||!(parseHttpDate(p)<=parseHttpDate(t));if(v){return false}}return true}function parseHttpDate(e){var r=e&&Date.parse(e);return typeof r===\"number\"?r:NaN}function parseTokenList(e){var r=0;var a=[];var t=0;for(var s=0,i=e.length;s<i;s++){switch(e.charCodeAt(s)){case 32:if(t===r){t=r=s+1}break;case 44:a.push(e.substring(t,r));t=r=s+1;break;default:r=s+1;break}}a.push(e.substring(t,r));return a}}};var r={};function __nccwpck_require__(a){var t=r[a];if(t!==undefined){return t.exports}var s=r[a]={exports:{}};var i=true;try{e[a](s,s.exports,__nccwpck_require__);i=false}finally{if(i)delete r[a]}return s.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var a=__nccwpck_require__(695);module.exports=a})();", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"crypto\");", "import crypto from 'crypto'\n\n// Background:\n// https://security.stackexchange.com/questions/184305/why-would-i-ever-use-aes-256-cbc-if-aes-256-gcm-is-more-secure\n\nconst CIPHER_ALGORITHM = `aes-256-gcm`,\n  CIPHER_KEY_LENGTH = 32, // https://stackoverflow.com/a/28307668/4397028\n  CIPHER_IV_LENGTH = 16, // https://stackoverflow.com/a/28307668/4397028\n  CIPHER_TAG_LENGTH = 16,\n  CIPHER_SALT_LENGTH = 64\n\nconst PBKDF2_ITERATIONS = 100_000 // https://support.1password.com/pbkdf2/\n\nexport function encryptWithSecret(secret: Buffer, data: string): string {\n  const iv = crypto.randomBytes(CIPHER_IV_LENGTH)\n  const salt = crypto.randomBytes(CIPHER_SALT_LENGTH)\n\n  // https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n  const key = crypto.pbkdf2Sync(\n    secret,\n    salt,\n    PBKDF2_ITERATIONS,\n    CIPHER_KEY_LENGTH,\n    `sha512`\n  )\n\n  const cipher = crypto.createCipheriv(CIPHER_ALGORITHM, key, iv)\n  const encrypted = Buffer.concat([cipher.update(data, `utf8`), cipher.final()])\n\n  // https://nodejs.org/api/crypto.html#crypto_cipher_getauthtag\n  const tag = cipher.getAuthTag()\n\n  return Buffer.concat([\n    // Data as required by:\n    // Salt for Key: https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n    // IV: https://nodejs.org/api/crypto.html#crypto_class_decipher\n    // Tag: https://nodejs.org/api/crypto.html#crypto_decipher_setauthtag_buffer\n    salt,\n    iv,\n    tag,\n    encrypted,\n  ]).toString(`hex`)\n}\n\nexport function decryptWithSecret(\n  secret: Buffer,\n  encryptedData: string\n): string {\n  const buffer = Buffer.from(encryptedData, `hex`)\n\n  const salt = buffer.slice(0, CIPHER_SALT_LENGTH)\n  const iv = buffer.slice(\n    CIPHER_SALT_LENGTH,\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH\n  )\n  const tag = buffer.slice(\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH,\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH + CIPHER_TAG_LENGTH\n  )\n  const encrypted = buffer.slice(\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH + CIPHER_TAG_LENGTH\n  )\n\n  // https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n  const key = crypto.pbkdf2Sync(\n    secret,\n    salt,\n    PBKDF2_ITERATIONS,\n    CIPHER_KEY_LENGTH,\n    `sha512`\n  )\n\n  const decipher = crypto.createDecipheriv(CIPHER_ALGORITHM, key, iv)\n  decipher.setAuthTag(tag)\n\n  return decipher.update(encrypted) + decipher.final(`utf8`)\n}\n", "module.exports = require(\"next/dist/compiled/jsonwebtoken\");", "module.exports = require(\"next/dist/compiled/raw-body\");", "module.exports = require(\"querystring\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n", "import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n", "import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"next/dist/server/lib/trace/tracer\");", "/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n", "import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n", "import type { RouteDefinition } from '../route-definitions/route-definition'\n\n/**\n * RouteModuleOptions is the options that are passed to the route module, other\n * route modules should extend this class to add specific options for their\n * route.\n */\nexport interface RouteModuleOptions<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  readonly definition: Readonly<D>\n  readonly userland: Readonly<U>\n}\n\n/**\n * RouteHandlerContext is the base context for a route handler.\n */\nexport interface RouteModuleHandleContext {\n  /**\n   * Any matched parameters for the request. This is only defined for dynamic\n   * routes.\n   */\n  params: Record<string, string | string[] | undefined> | undefined\n}\n\n/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */\nexport abstract class RouteModule<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  /**\n   * The userland module. This is the module that is exported from the user's\n   * code. This is marked as readonly to ensure that the module is not mutated\n   * because the module (when compiled) only provides getters.\n   */\n  public readonly userland: Readonly<U>\n\n  /**\n   * The definition of the route.\n   */\n  public readonly definition: Readonly<D>\n\n  /**\n   * The shared modules that are exposed and required for the route module.\n   */\n  public static readonly sharedModules: any\n\n  constructor({ userland, definition }: RouteModuleOptions<D, U>) {\n    this.userland = userland\n    this.definition = definition\n  }\n}\n", "/**\n * FNV-1a Hash implementation\n * <AUTHOR> (tjwebb) <<EMAIL>>\n *\n * Ported from https://github.com/tjwebb/fnv-plus/blob/master/index.js\n *\n * Simplified, optimized and add modified for 52 bit, which provides a larger hash space\n * and still making use of Javascript's 53-bit integer space.\n */\nexport const fnv1a52 = (str: string) => {\n  const len = str.length\n  let i = 0,\n    t0 = 0,\n    v0 = 0x2325,\n    t1 = 0,\n    v1 = 0x8422,\n    t2 = 0,\n    v2 = 0x9ce4,\n    t3 = 0,\n    v3 = 0xcbf2\n\n  while (i < len) {\n    v0 ^= str.charCodeAt(i++)\n    t0 = v0 * 435\n    t1 = v1 * 435\n    t2 = v2 * 435\n    t3 = v3 * 435\n    t2 += v0 << 8\n    t3 += v1 << 8\n    t1 += t0 >>> 16\n    v0 = t0 & 65535\n    t2 += t1 >>> 16\n    v1 = t1 & 65535\n    v3 = (t3 + (t2 >>> 16)) & 65535\n    v2 = t2 & 65535\n  }\n\n  return (\n    (v3 & 15) * 281474976710656 +\n    v2 * 4294967296 +\n    v1 * 65536 +\n    (v0 ^ (v3 >> 4))\n  )\n}\n\nexport const generateETag = (payload: string, weak = false) => {\n  const prefix = weak ? 'W/\"' : '\"'\n  return (\n    prefix + fnv1a52(payload).toString(36) + payload.length.toString(36) + '\"'\n  )\n}\n", "import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"stream\");", "import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n", "import type { IncomingMessage } from 'http'\n\nimport { parse } from 'next/dist/compiled/content-type'\nimport isError from '../../../lib/is-error'\nimport type { SizeLimit } from '../../../types'\nimport { ApiError } from '../index'\n\n/**\n * Parse `JSON` and handles invalid `JSON` strings\n * @param str `JSON` string\n */\nfunction parseJson(str: string): object {\n  if (str.length === 0) {\n    // special-case empty json body, as it's a common client-side mistake\n    return {}\n  }\n\n  try {\n    return JSON.parse(str)\n  } catch (e) {\n    throw new ApiError(400, 'Invalid JSON')\n  }\n}\n\n/**\n * Parse incoming message like `json` or `urlencoded`\n * @param req request object\n */\nexport async function parseBody(\n  req: IncomingMessage,\n  limit: SizeLimit\n): Promise<any> {\n  let contentType\n  try {\n    contentType = parse(req.headers['content-type'] || 'text/plain')\n  } catch {\n    contentType = parse('text/plain')\n  }\n  const { type, parameters } = contentType\n  const encoding = parameters.charset || 'utf-8'\n\n  let buffer\n\n  try {\n    const getRawBody =\n      require('next/dist/compiled/raw-body') as typeof import('next/dist/compiled/raw-body')\n    buffer = await getRawBody(req, { encoding, limit })\n  } catch (e) {\n    if (isError(e) && e.type === 'entity.too.large') {\n      throw new ApiError(413, `Body exceeded ${limit} limit`)\n    } else {\n      throw new ApiError(400, 'Invalid body')\n    }\n  }\n\n  const body = buffer.toString()\n\n  if (type === 'application/json' || type === 'application/ld+json') {\n    return parseJson(body)\n  } else if (type === 'application/x-www-form-urlencoded') {\n    const qs = require('querystring')\n    return qs.decode(body)\n  } else {\n    return body\n  }\n}\n", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiRequest, NextApiResponse } from '../../../shared/lib/utils'\nimport type { PageConfig, ResponseLimit } from '../../../types'\nimport type { __ApiPreviewProps } from '../.'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { ServerOnInstrumentationRequestError } from '../../app-render/types'\n\nimport bytes from 'next/dist/compiled/bytes'\nimport { generateETag } from '../../lib/etag'\nimport { sendEtagResponse } from '../../send-payload'\nimport { Stream } from 'stream'\nimport isError from '../../../lib/is-error'\nimport { isResSent } from '../../../shared/lib/utils'\nimport { interopDefault } from '../../../lib/interop-default'\nimport {\n  setLazyProp,\n  sendStatusCode,\n  redirect,\n  clearPreviewData,\n  sendError,\n  A<PERSON><PERSON><PERSON>r,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  RESPONSE_LIMIT_DEFAULT,\n} from './../index'\nimport { getCookieParser } from './../get-cookie-parser'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../../lib/constants'\nimport { tryGetPreviewData } from './try-get-preview-data'\nimport { parseBody } from './parse-body'\n\ntype RevalidateFn = (config: {\n  urlPath: string\n  revalidateHeaders: { [key: string]: string | string[] }\n  opts: { unstable_onlyGenerated?: boolean }\n}) => Promise<void>\n\ntype ApiContext = __ApiPreviewProps & {\n  trustHostHeader?: boolean\n  allowedRevalidateHeaderKeys?: string[]\n  hostname?: string\n  revalidate?: RevalidateFn\n  multiZoneDraftMode?: boolean\n  dev: boolean\n}\n\nfunction getMaxContentLength(responseLimit?: ResponseLimit) {\n  if (responseLimit && typeof responseLimit !== 'boolean') {\n    return bytes.parse(responseLimit)\n  }\n  return RESPONSE_LIMIT_DEFAULT\n}\n\n/**\n * Send `any` body to response\n * @param req request object\n * @param res response object\n * @param body of response\n */\nfunction sendData(req: NextApiRequest, res: NextApiResponse, body: any): void {\n  if (body === null || body === undefined) {\n    res.end()\n    return\n  }\n\n  // strip irrelevant headers/body\n  if (res.statusCode === 204 || res.statusCode === 304) {\n    res.removeHeader('Content-Type')\n    res.removeHeader('Content-Length')\n    res.removeHeader('Transfer-Encoding')\n\n    if (process.env.NODE_ENV === 'development' && body) {\n      console.warn(\n        `A body was attempted to be set with a 204 statusCode for ${req.url}, this is invalid and the body was ignored.\\n` +\n          `See more info here https://nextjs.org/docs/messages/invalid-api-status-body`\n      )\n    }\n    res.end()\n    return\n  }\n\n  const contentType = res.getHeader('Content-Type')\n\n  if (body instanceof Stream) {\n    if (!contentType) {\n      res.setHeader('Content-Type', 'application/octet-stream')\n    }\n    body.pipe(res)\n    return\n  }\n\n  const isJSONLike = ['object', 'number', 'boolean'].includes(typeof body)\n  const stringifiedBody = isJSONLike ? JSON.stringify(body) : body\n  const etag = generateETag(stringifiedBody)\n  if (sendEtagResponse(req, res, etag)) {\n    return\n  }\n\n  if (Buffer.isBuffer(body)) {\n    if (!contentType) {\n      res.setHeader('Content-Type', 'application/octet-stream')\n    }\n    res.setHeader('Content-Length', body.length)\n    res.end(body)\n    return\n  }\n\n  if (isJSONLike) {\n    res.setHeader('Content-Type', 'application/json; charset=utf-8')\n  }\n\n  res.setHeader('Content-Length', Buffer.byteLength(stringifiedBody))\n  res.end(stringifiedBody)\n}\n\n/**\n * Send `JSON` object\n * @param res response object\n * @param jsonBody of data\n */\nfunction sendJson(res: NextApiResponse, jsonBody: any): void {\n  // Set header to application/json\n  res.setHeader('Content-Type', 'application/json; charset=utf-8')\n\n  // Use send to handle request\n  res.send(JSON.stringify(jsonBody))\n}\n\nfunction isValidData(str: any): str is string {\n  return typeof str === 'string' && str.length >= 16\n}\n\nfunction setDraftMode<T>(\n  res: NextApiResponse<T>,\n  options: {\n    enable: boolean\n    previewModeId?: string\n  }\n): NextApiResponse<T> {\n  if (!isValidData(options.previewModeId)) {\n    throw new Error('invariant: invalid previewModeId')\n  }\n  const expires = options.enable ? undefined : new Date(0)\n  // To delete a cookie, set `expires` to a date in the past:\n  // https://tools.ietf.org/html/rfc6265#section-4.1.1\n  // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, options.previewModeId, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      expires,\n    }),\n  ])\n  return res\n}\n\nfunction setPreviewData<T>(\n  res: NextApiResponse<T>,\n  data: object | string, // TODO: strict runtime type checking\n  options: {\n    maxAge?: number\n    path?: string\n  } & __ApiPreviewProps\n): NextApiResponse<T> {\n  if (!isValidData(options.previewModeId)) {\n    throw new Error('invariant: invalid previewModeId')\n  }\n  if (!isValidData(options.previewModeEncryptionKey)) {\n    throw new Error('invariant: invalid previewModeEncryptionKey')\n  }\n  if (!isValidData(options.previewModeSigningKey)) {\n    throw new Error('invariant: invalid previewModeSigningKey')\n  }\n\n  const jsonwebtoken =\n    require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n  const { encryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const payload = jsonwebtoken.sign(\n    {\n      data: encryptWithSecret(\n        Buffer.from(options.previewModeEncryptionKey),\n        JSON.stringify(data)\n      ),\n    },\n    options.previewModeSigningKey,\n    {\n      algorithm: 'HS256',\n      ...(options.maxAge !== undefined\n        ? { expiresIn: options.maxAge }\n        : undefined),\n    }\n  )\n\n  // limit preview mode cookie to 2KB since we shouldn't store too much\n  // data here and browsers drop cookies over 4KB\n  if (payload.length > 2048) {\n    throw new Error(\n      `Preview data is limited to 2KB currently, reduce how much data you are storing as preview data to continue`\n    )\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, options.previewModeId, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.maxAge !== undefined\n        ? ({ maxAge: options.maxAge } as CookieSerializeOptions)\n        : undefined),\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, payload, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.maxAge !== undefined\n        ? ({ maxAge: options.maxAge } as CookieSerializeOptions)\n        : undefined),\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n  return res\n}\n\nasync function revalidate(\n  urlPath: string,\n  opts: {\n    unstable_onlyGenerated?: boolean\n  },\n  req: IncomingMessage,\n  context: ApiContext\n) {\n  if (typeof urlPath !== 'string' || !urlPath.startsWith('/')) {\n    throw new Error(\n      `Invalid urlPath provided to revalidate(), must be a path e.g. /blog/post-1, received ${urlPath}`\n    )\n  }\n  const revalidateHeaders: HeadersInit = {\n    [PRERENDER_REVALIDATE_HEADER]: context.previewModeId,\n    ...(opts.unstable_onlyGenerated\n      ? {\n          [PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER]: '1',\n        }\n      : {}),\n  }\n  const allowedRevalidateHeaderKeys = [\n    ...(context.allowedRevalidateHeaderKeys || []),\n  ]\n\n  if (context.trustHostHeader || context.dev) {\n    allowedRevalidateHeaderKeys.push('cookie')\n  }\n\n  if (context.trustHostHeader) {\n    allowedRevalidateHeaderKeys.push('x-vercel-protection-bypass')\n  }\n\n  for (const key of Object.keys(req.headers)) {\n    if (allowedRevalidateHeaderKeys.includes(key)) {\n      revalidateHeaders[key] = req.headers[key] as string\n    }\n  }\n\n  try {\n    if (context.trustHostHeader) {\n      const res = await fetch(`https://${req.headers.host}${urlPath}`, {\n        method: 'HEAD',\n        headers: revalidateHeaders,\n      })\n      // we use the cache header to determine successful revalidate as\n      // a non-200 status code can be returned from a successful revalidate\n      // e.g. notFound: true returns 404 status code but is successful\n      const cacheHeader =\n        res.headers.get('x-vercel-cache') || res.headers.get('x-nextjs-cache')\n\n      if (\n        cacheHeader?.toUpperCase() !== 'REVALIDATED' &&\n        res.status !== 200 &&\n        !(res.status === 404 && opts.unstable_onlyGenerated)\n      ) {\n        throw new Error(`Invalid response ${res.status}`)\n      }\n    } else if (context.revalidate) {\n      await context.revalidate({\n        urlPath,\n        revalidateHeaders,\n        opts,\n      })\n    } else {\n      throw new Error(\n        `Invariant: required internal revalidate method not passed to api-utils`\n      )\n    }\n  } catch (err: unknown) {\n    throw new Error(\n      `Failed to revalidate ${urlPath}: ${isError(err) ? err.message : err}`\n    )\n  }\n}\n\nexport async function apiResolver(\n  req: IncomingMessage,\n  res: ServerResponse,\n  query: any,\n  resolverModule: any,\n  apiContext: ApiContext,\n  propagateError: boolean,\n  dev?: boolean,\n  page?: string,\n  onError?: ServerOnInstrumentationRequestError\n): Promise<void> {\n  const apiReq = req as NextApiRequest\n  const apiRes = res as NextApiResponse\n\n  try {\n    if (!resolverModule) {\n      res.statusCode = 404\n      res.end('Not Found')\n      return\n    }\n    const config: PageConfig = resolverModule.config || {}\n    const bodyParser = config.api?.bodyParser !== false\n    const responseLimit = config.api?.responseLimit ?? true\n    const externalResolver = config.api?.externalResolver || false\n\n    // Parsing of cookies\n    setLazyProp({ req: apiReq }, 'cookies', getCookieParser(req.headers))\n    // Parsing query string\n    apiReq.query = query\n    // Parsing preview data\n    setLazyProp({ req: apiReq }, 'previewData', () =>\n      tryGetPreviewData(req, res, apiContext, !!apiContext.multiZoneDraftMode)\n    )\n    // Checking if preview mode is enabled\n    setLazyProp({ req: apiReq }, 'preview', () =>\n      apiReq.previewData !== false ? true : undefined\n    )\n    // Set draftMode to the same value as preview\n    setLazyProp({ req: apiReq }, 'draftMode', () => apiReq.preview)\n\n    // Parsing of body\n    if (bodyParser && !apiReq.body) {\n      apiReq.body = await parseBody(\n        apiReq,\n        config.api && config.api.bodyParser && config.api.bodyParser.sizeLimit\n          ? config.api.bodyParser.sizeLimit\n          : '1mb'\n      )\n    }\n\n    let contentLength = 0\n    const maxContentLength = getMaxContentLength(responseLimit)\n    const writeData = apiRes.write\n    const endResponse = apiRes.end\n    apiRes.write = (...args: any[2]) => {\n      contentLength += Buffer.byteLength(args[0] || '')\n      return writeData.apply(apiRes, args)\n    }\n    apiRes.end = (...args: any[2]) => {\n      if (args.length && typeof args[0] !== 'function') {\n        contentLength += Buffer.byteLength(args[0] || '')\n      }\n\n      if (responseLimit && contentLength >= maxContentLength) {\n        console.warn(\n          `API response for ${req.url} exceeds ${bytes.format(\n            maxContentLength\n          )}. API Routes are meant to respond quickly. https://nextjs.org/docs/messages/api-routes-response-size-limit`\n        )\n      }\n\n      return endResponse.apply(apiRes, args)\n    }\n    apiRes.status = (statusCode) => sendStatusCode(apiRes, statusCode)\n    apiRes.send = (data) => sendData(apiReq, apiRes, data)\n    apiRes.json = (data) => sendJson(apiRes, data)\n    apiRes.redirect = (statusOrUrl: number | string, url?: string) =>\n      redirect(apiRes, statusOrUrl, url)\n    apiRes.setDraftMode = (options = { enable: true }) =>\n      setDraftMode(apiRes, Object.assign({}, apiContext, options))\n    apiRes.setPreviewData = (data, options = {}) =>\n      setPreviewData(apiRes, data, Object.assign({}, apiContext, options))\n    apiRes.clearPreviewData = (options = {}) =>\n      clearPreviewData(apiRes, options)\n    apiRes.revalidate = (\n      urlPath: string,\n      opts?: {\n        unstable_onlyGenerated?: boolean\n      }\n    ) => revalidate(urlPath, opts || {}, req, apiContext)\n\n    const resolver = interopDefault(resolverModule)\n    let wasPiped = false\n\n    if (process.env.NODE_ENV !== 'production') {\n      // listen for pipe event and don't show resolve warning\n      res.once('pipe', () => (wasPiped = true))\n    }\n\n    const apiRouteResult = await resolver(req, res)\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof apiRouteResult !== 'undefined') {\n        if (apiRouteResult instanceof Response) {\n          throw new Error(\n            'API route returned a Response object in the Node.js runtime, this is not supported. Please use `runtime: \"edge\"` instead: https://nextjs.org/docs/api-routes/edge-api-routes'\n          )\n        }\n        console.warn(\n          `API handler should not return a value, received ${typeof apiRouteResult}.`\n        )\n      }\n\n      if (!externalResolver && !isResSent(res) && !wasPiped) {\n        console.warn(\n          `API resolved without sending a response for ${req.url}, this may result in stalled requests.`\n        )\n      }\n    }\n  } catch (err) {\n    onError?.(err, req, {\n      routerKind: 'Pages Router',\n      routePath: page || '',\n      routeType: 'route',\n      revalidateReason: undefined,\n    })\n\n    if (err instanceof ApiError) {\n      sendError(apiRes, err.statusCode, err.message)\n    } else {\n      if (dev) {\n        if (isError(err)) {\n          err.page = page\n        }\n        throw err\n      }\n\n      console.error(err)\n      if (propagateError) {\n        throw err\n      }\n      sendError(apiRes, 500, 'Internal Server Error')\n    }\n  }\n}\n", "import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } = require('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiResponse } from '../../../shared/lib/utils'\nimport { checkIsOnDemandRevalidate } from '../.'\nimport type { __ApiPreviewProps } from '../.'\nimport type { BaseNextRequest, BaseNextResponse } from '../../base-http'\nimport type { PreviewData } from '../../../types'\n\nimport {\n  clearPreviewData,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  SYMBOL_PREVIEW_DATA,\n} from '../index'\nimport { RequestCookies } from '../../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\n\nexport function tryGetPreviewData(\n  req: IncomingMessage | BaseNextRequest | Request,\n  res: ServerResponse | BaseNextResponse,\n  options: __ApiPreviewProps,\n  multiZoneDraftMode: boolean\n): PreviewData {\n  // if an On-Demand revalidation is being done preview mode\n  // is disabled\n  if (options && checkIsOnDemandRevalidate(req, options).isOnDemandRevalidate) {\n    return false\n  }\n\n  // Read cached preview data if present\n  // TODO: use request metadata instead of a symbol\n  if (SYMBOL_PREVIEW_DATA in req) {\n    return (req as any)[SYMBOL_PREVIEW_DATA] as any\n  }\n\n  const headers = HeadersAdapter.from(req.headers)\n  const cookies = new RequestCookies(headers)\n\n  const previewModeId = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)?.value\n  const tokenPreviewData = cookies.get(COOKIE_NAME_PRERENDER_DATA)?.value\n\n  // Case: preview mode cookie set but data cookie is not set\n  if (\n    previewModeId &&\n    !tokenPreviewData &&\n    previewModeId === options.previewModeId\n  ) {\n    // This is \"Draft Mode\" which doesn't use\n    // previewData, so we return an empty object\n    // for backwards compat with \"Preview Mode\".\n    const data = {}\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  }\n\n  // Case: neither cookie is set.\n  if (!previewModeId && !tokenPreviewData) {\n    return false\n  }\n\n  // Case: one cookie is set, but not the other.\n  if (!previewModeId || !tokenPreviewData) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  // Case: preview session is for an old build.\n  if (previewModeId !== options.previewModeId) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  let encryptedPreviewData: {\n    data: string\n  }\n  try {\n    const jsonwebtoken =\n      require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n    encryptedPreviewData = jsonwebtoken.verify(\n      tokenPreviewData,\n      options.previewModeSigningKey\n    ) as typeof encryptedPreviewData\n  } catch {\n    // TODO: warn\n    clearPreviewData(res as NextApiResponse)\n    return false\n  }\n\n  const { decryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const decryptedPreviewData = decryptWithSecret(\n    Buffer.from(options.previewModeEncryptionKey),\n    encryptedPreviewData.data\n  )\n\n  try {\n    // TODO: strict runtime type checking\n    const data = JSON.parse(decryptedPreviewData)\n    // Cache lookup\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  } catch {\n    return false\n  }\n}\n", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type RenderResult from './render-result'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { isResSent } from '../shared/lib/utils'\nimport { generateETag } from './lib/etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { RSC_CONTENT_TYPE_HEADER } from '../client/components/app-router-headers'\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  result,\n  type,\n  generateEtags,\n  poweredByHeader,\n  cacheControl,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  result: RenderResult\n  type: 'html' | 'json' | 'rsc'\n  generateEtags: boolean\n  poweredByHeader: boolean\n  cacheControl: CacheControl | undefined\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  // If cache control is already set on the response we don't\n  // override it to allow users to customize it via next.config\n  if (cacheControl && !res.getHeader('Cache-Control')) {\n    res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n  }\n\n  const payload = result.isDynamic ? null : result.toUnchunkedString()\n\n  if (generateEtags && payload !== null) {\n    const etag = generateETag(payload)\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      result.contentType\n        ? result.contentType\n        : type === 'rsc'\n          ? RSC_CONTENT_TYPE_HEADER\n          : type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n    )\n  }\n\n  if (payload) {\n    res.setHeader('Content-Length', Buffer.byteLength(payload))\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n    return\n  }\n\n  if (payload !== null) {\n    res.end(payload)\n    return\n  }\n\n  // Pipe the render result to the response after we get a writer for it.\n  await result.pipeToNodeResponse(res)\n}\n", "export function interopDefault(mod: any) {\n  return mod.default || mod\n}\n", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type { PagesAPIRouteDefinition } from '../../route-definitions/pages-api-route-definition'\nimport type { PageConfig } from '../../../types'\nimport type { ParsedUrlQuery } from 'querystring'\nimport { wrapApiHandler, type __ApiPreviewProps } from '../../api-utils'\nimport type { RouteModuleOptions } from '../route-module'\n\nimport { RouteModule, type RouteModuleHandleContext } from '../route-module'\nimport { apiResolver } from '../../api-utils/node/api-resolver'\n\ntype PagesAPIHandleFn = (\n  req: IncomingMessage,\n  res: ServerResponse\n) => Promise<void>\n\n/**\n * The PagesAPIModule is the type of the module exported by the bundled Pages\n * API module.\n */\nexport type PagesAPIModule = typeof import('../../../build/templates/pages-api')\n\ntype PagesAPIUserlandModule = {\n  /**\n   * The exported handler method.\n   */\n  readonly default: PagesAPIHandleFn\n\n  /**\n   * The exported page config.\n   */\n  readonly config?: PageConfig\n}\n\ntype PagesAPIRouteHandlerContext = RouteModuleHandleContext & {\n  /**\n   * The incoming server request in non-edge runtime.\n   */\n  req?: IncomingMessage\n\n  /**\n   * The outgoing server response in non-edge runtime.\n   */\n  res?: ServerResponse\n\n  /**\n   * The revalidate method used by the `revalidate` API.\n   *\n   * @param config the configuration for the revalidation\n   */\n  revalidate: (config: {\n    urlPath: string\n    revalidateHeaders: { [key: string]: string | string[] }\n    opts: { unstable_onlyGenerated?: boolean }\n  }) => Promise<void>\n\n  /**\n   * The hostname for the request.\n   */\n  hostname?: string\n\n  /**\n   * Keys allowed in the revalidate call.\n   */\n  allowedRevalidateHeaderKeys?: string[]\n\n  /**\n   * Whether to trust the host header.\n   */\n  trustHostHeader?: boolean\n\n  /**\n   * The query for the request.\n   */\n  query: ParsedUrlQuery\n\n  /**\n   * The preview props used by the `preview` API.\n   */\n  previewProps: __ApiPreviewProps\n\n  /**\n   * True if the server is in development mode.\n   */\n  dev: boolean\n\n  /**\n   * True if the server is in minimal mode.\n   */\n  minimalMode: boolean\n\n  /**\n   * The page that's being rendered.\n   */\n  page: string\n\n  /**\n   * The error handler for the request.\n   */\n  onError?: Parameters<typeof apiResolver>[8]\n\n  /**\n   * whether multi-zone flag is enabled for draft mode\n   */\n  multiZoneDraftMode?: boolean\n}\n\nexport type PagesAPIRouteModuleOptions = RouteModuleOptions<\n  PagesAPIRouteDefinition,\n  PagesAPIUserlandModule\n>\n\nexport class PagesAPIRouteModule extends RouteModule<\n  PagesAPIRouteDefinition,\n  PagesAPIUserlandModule\n> {\n  private apiResolverWrapped: typeof apiResolver\n\n  constructor(options: PagesAPIRouteModuleOptions) {\n    super(options)\n\n    if (typeof options.userland.default !== 'function') {\n      throw new Error(\n        `Page ${options.definition.page} does not export a default function.`\n      )\n    }\n\n    this.apiResolverWrapped = wrapApiHandler(\n      options.definition.page,\n      apiResolver\n    )\n  }\n\n  /**\n   *\n   * @param req the incoming server request\n   * @param res the outgoing server response\n   * @param context the context for the render\n   */\n  public async render(\n    req: IncomingMessage,\n    res: ServerResponse,\n    context: PagesAPIRouteHandlerContext\n  ): Promise<void> {\n    const { apiResolverWrapped } = this\n    await apiResolverWrapped(\n      req,\n      res,\n      context.query,\n      this.userland,\n      {\n        ...context.previewProps,\n        revalidate: context.revalidate,\n        trustHostHeader: context.trustHostHeader,\n        allowedRevalidateHeaderKeys: context.allowedRevalidateHeaderKeys,\n        hostname: context.hostname,\n        multiZoneDraftMode: context.multiZoneDraftMode,\n        dev: context.dev,\n      },\n      context.minimalMode,\n      context.dev,\n      context.page,\n      context.onError\n    )\n  }\n}\n\nexport default PagesAPIRouteModule\n"], "names": ["__defProp", "Object", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__hasOwnProp", "prototype", "hasOwnProperty", "src_exports", "string<PERSON><PERSON><PERSON><PERSON>", "c", "_a", "attrs", "path", "expires", "Date", "toUTCString", "maxAge", "domain", "secure", "httpOnly", "sameSite", "partitioned", "priority", "filter", "Boolean", "stringified", "name", "encodeURIComponent", "value", "length", "join", "parse<PERSON><PERSON><PERSON>", "cookie", "map", "Map", "pair", "split", "splitAt", "indexOf", "set", "key", "slice", "decodeURIComponent", "parseSetCookie", "<PERSON><PERSON><PERSON><PERSON>", "string", "attributes", "httponly", "maxage", "samesite", "fromEntries", "value2", "toLowerCase", "replace", "compact", "t", "newT", "Number", "SAME_SITE", "includes", "PRIORITY", "__export", "target", "all", "get", "enumerable", "RequestCookies", "ResponseCookies", "module", "exports", "to", "from", "except", "desc", "call", "constructor", "requestHeaders", "_parsed", "_headers", "header", "Symbol", "iterator", "size", "args", "getAll", "Array", "_", "n", "has", "delete", "names", "result", "isArray", "clear", "keys", "for", "JSON", "stringify", "toString", "values", "v", "responseHeaders", "_b", "_c", "getSetCookie", "cookieString", "splitCookiesString", "cookiesString", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "cookiesStrings", "pos", "skipWhitespace", "test", "char<PERSON>t", "push", "substring", "parsed", "normalizeCookie", "now", "bag", "headers", "serialized", "append", "options", "e", "r", "parse", "format", "a", "b", "kb", "mb", "gb", "tb", "Math", "pb", "i", "isFinite", "abs", "o", "thousandsSeparator", "s", "unitSeparator", "f", "undefined", "decimalPlaces", "u", "fixedDecimals", "p", "unit", "l", "toFixed", "isNaN", "exec", "parseFloat", "parseInt", "floor", "__nccwpck_require__", "ab", "__dirname", "ContentType", "parameters", "create", "type", "sort", "qstring", "String", "getcontenttype", "<PERSON><PERSON><PERSON><PERSON>", "substr", "trim", "lastIndex", "index", "decode", "tryDecode", "serialize", "encode", "parseHttpDate", "NaN", "parseTokenList", "charCodeAt", "require", "CIPHER_ALGORITHM", "encryptWithSecret", "secret", "data", "iv", "crypto", "salt", "cipher", "encrypted", "<PERSON><PERSON><PERSON>", "concat", "update", "final", "tag", "getAuthTag", "decryptWithSecret", "encryptedData", "buffer", "CIPHER_SALT_LENGTH", "decipher", "setAuthTag", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "__esModule", "d", "definition", "obj", "prop", "toStringTag", "ReflectAdapter", "receiver", "Reflect", "bind", "deleteProperty", "ReadonlyHeadersError", "Error", "callable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Headers", "Proxy", "lowercased", "original", "find", "seal", "existing", "merge", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "BaseServerSpan", "LoadComponentsSpan", "NextServerSpan", "NextNodeServerSpan", "StartServerSpan", "RenderSpan", "AppRenderSpan", "RouterSpan", "NodeSpan", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "MiddlewareSpan", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "SYMBOL_PREVIEW_DATA", "SYMBOL_CLEARED_COOKIES", "clearPreviewData", "res", "previous", "<PERSON><PERSON><PERSON><PERSON>", "process", "ApiError", "statusCode", "message", "sendError", "statusMessage", "end", "setLazyProp", "req", "opts", "configurable", "optsReset", "writable", "RouteModule", "userland", "fnv1a52", "len", "str", "t0", "v0", "t1", "v1", "t2", "v2", "t3", "v3", "generateETag", "payload", "weak", "prefix", "SP", "performance", "every", "method", "isError", "err", "parseBody", "limit", "contentType", "encoding", "charset", "getRawBody", "body", "parseJson", "qs", "isValidData", "revalidate", "url<PERSON><PERSON>", "context", "startsWith", "revalidateHeaders", "previewModeId", "unstable_onlyGenerated", "allowedRevalidateHeaderKeys", "trustHostHeader", "dev", "fetch", "host", "cacheHeader", "toUpperCase", "status", "apiResolver", "query", "resolverModule", "apiContext", "propagateError", "page", "onError", "config", "<PERSON><PERSON><PERSON><PERSON>", "api", "responseLimit", "externalResolver", "parseCookieFn", "apiReq", "tryGetPreviewData", "multiZoneDraftMode", "cookies", "encryptedPreviewData", "checkIsOnDemandRevalidate", "previewProps", "isOnDemandRevalidate", "revalidateOnlyGenerated", "tokenPreviewData", "jsonwebtoken", "verify", "previewModeSigningKey", "decryptedPreviewData", "previewModeEncryptionKey", "previewData", "preview", "sizeLimit", "contentLength", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytes", "writeData", "apiRes", "write", "endResponse", "byteLength", "apply", "console", "warn", "url", "send", "sendData", "etag", "removeHeader", "Stream", "pipe", "isJSONLike", "stringifiedBody", "fresh", "<PERSON><PERSON><PERSON><PERSON>", "json", "redirect", "statusOrUrl", "writeHead", "Location", "setDraftMode", "enable", "assign", "setPreviewData", "sign", "algorithm", "expiresIn", "resolver", "mod", "default", "wasPiped", "once", "apiRouteResult", "Response", "finished", "headersSent", "routerKind", "routePath", "routeType", "revalidateReason", "error", "PagesAPIRouteModule", "apiResolverWrapped", "wrapApiHandler", "handler", "getTracer", "setRootSpanAttribute", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "render", "hostname", "minimalMode"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]}