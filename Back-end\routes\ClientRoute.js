// backend/routes/ClientRoute.js
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const Client = require('../models/ClientModel');
const ClientActivity = require('../models/ClientActivityModel');
const Facture = require('../models/FactureModel');
const Devis = require('../models/DevisModel');
const User = require('../models/UserModel');
const bcrypt = require('bcrypt');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/clients');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'client-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: function (req, file, cb) {
    const filetypes = /jpeg|jpg|png|gif/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error("Only image files are allowed!"));
  }
});

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Récupérer tous les clients
router.get('/clients', verifyToken, async (req, res) => {
  try {
    console.log('Fetching clients for user:', req.userId, 'with role:', req.userRole);

    // Get all clients with vendeur information
    const clients = await Client.find().populate('vendeurId', 'nom email profileImage');

    // If the user is a RESPONSABLE, show clients assigned to them and their vendeurs
    if (req.userRole === 'RESPONSABLE') {
      const User = require('../models/UserModel');

      // Get the current responsable user to find its email
      const responsableUser = await User.findById(req.userId);
      if (!responsableUser) {
        return res.status(404).json({ error: 'Utilisateur responsable non trouvé' });
      }

      console.log('Responsable user email:', responsableUser.email);

      // Find all vendeurs associated with this responsable
      const vendeurs = await User.find({
        role: 'VENDEUR',
        responsables: { $in: [req.userId] }
      });

      console.log(`Found ${vendeurs.length} vendeurs associated with responsable ${req.userId}`);

      // Get the IDs of all vendeurs
      const vendeurIds = vendeurs.map(v => v._id.toString());
      console.log('Vendeur IDs:', vendeurIds);

      // Filter clients to show those assigned to this responsable or their vendeurs
      const filteredClients = clients.filter(client => {
        // Include clients that have this responsable in their responsables array
        if (client.responsables && client.responsables.some(respId => respId.toString() === req.userId)) {
          console.log(`Including client ${client.nom} - assigned to responsable`);
          return true;
        }

        // Include clients assigned to vendeurs under this responsable
        if (client.vendeurId && vendeurIds.includes(client.vendeurId._id.toString())) {
          console.log(`Including client ${client.nom} - assigned to vendeur under responsable`);
          return true;
        }

        return false;
      });

      console.log(`Filtered ${clients.length} clients to ${filteredClients.length} for responsable`);

      // Remove duplicates based on email
      const uniqueClients = [];
      const emailsSet = new Set();

      for (const client of filteredClients) {
        if (client.email && !emailsSet.has(client.email)) {
          emailsSet.add(client.email);
          uniqueClients.push(client);
        } else if (!client.email) {
          // Si le client n'a pas d'email, on le garde quand même
          uniqueClients.push(client);
        }
      }

      console.log(`Removed ${filteredClients.length - uniqueClients.length} duplicate clients`);

      return res.status(200).json(uniqueClients);
    }

    // If the user is a VENDEUR, only return clients assigned to them
    if (req.userRole === 'VENDEUR') {
      console.log(`Filtering clients for vendeur: ${req.userId}`);

      // Filter clients where vendeurId matches the current user's ID
      const vendeurClients = clients.filter(client =>
        client.vendeurId && client.vendeurId._id && client.vendeurId._id.toString() === req.userId
      );

      console.log(`Found ${vendeurClients.length} clients assigned to vendeur ${req.userId}`);

      // Éliminer les doublons en se basant sur l'email
      const uniqueClients = [];
      const emailsSet = new Set();

      for (const client of vendeurClients) {
        if (client.email && !emailsSet.has(client.email)) {
          emailsSet.add(client.email);
          uniqueClients.push(client);
        } else if (!client.email) {
          // Si le client n'a pas d'email, on le garde quand même
          uniqueClients.push(client);
        }
      }

      console.log(`Removed ${vendeurClients.length - uniqueClients.length} duplicate clients`);

      return res.status(200).json(uniqueClients);
    }

    // For ADMIN role, return all clients but remove duplicates
    const uniqueClients = [];
    const emailsSet = new Set();

    for (const client of clients) {
      if (client.email && !emailsSet.has(client.email)) {
        emailsSet.add(client.email);
        uniqueClients.push(client);
      } else if (!client.email) {
        // Si le client n'a pas d'email, on le garde quand même
        uniqueClients.push(client);
      }
    }

    console.log(`Removed ${clients.length - uniqueClients.length} duplicate clients`);

    res.status(200).json(uniqueClients);
  } catch (error) {
    console.error('Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des clients' });
  }
});

// Récupérer un client par ID
router.get('/clients/:id', verifyToken, async (req, res) => {
  try {
    const client = await Client.findById(req.params.id).populate('vendeurId', 'nom email profileImage');
    if (!client) return res.status(404).json({ error: 'Client non trouvé' });
    res.status(200).json(client);
  } catch (error) {
    console.error('Erreur lors de la récupération du client:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération du client' });
  }
});

// Créer un nouveau client
router.post('/clients', verifyToken, async (req, res) => {
  try {
    const clientData = req.body;
    console.log('Création/mise à jour de client avec email:', clientData.email);

    // Si l'utilisateur est un vendeur, assigner automatiquement son ID comme vendeurId
    if (req.userRole === 'VENDEUR' && !clientData.vendeurId) {
      console.log('Assignation automatique du vendeur côté serveur:', req.userId);
      clientData.vendeurId = req.userId;

      // Trouver les responsables de ce vendeur et les assigner au client
      const vendeurUser = await User.findById(req.userId);
      if (vendeurUser && vendeurUser.responsables && vendeurUser.responsables.length > 0) {
        console.log('Assignation des responsables du vendeur au client:', vendeurUser.responsables);
        if (!clientData.responsables) {
          clientData.responsables = [];
        }
        // Ajouter les responsables du vendeur sans doublons
        vendeurUser.responsables.forEach(respId => {
          if (!clientData.responsables.includes(respId.toString())) {
            clientData.responsables.push(respId.toString());
          }
        });
      }
    }

    // Si l'utilisateur est un responsable, assigner automatiquement son ID aux responsables
    if (req.userRole === 'RESPONSABLE') {
      console.log('Assignation automatique du responsable côté serveur:', req.userId);
      if (!clientData.responsables) {
        clientData.responsables = [];
      }
      if (!clientData.responsables.includes(req.userId)) {
        clientData.responsables.push(req.userId);
      }
    }

    // Vérifier si un client avec cet email existe déjà
    let client = null;
    if (clientData.email) {
      client = await Client.findOne({ email: clientData.email });
    }

    let newClient;
    let isNewClient = false;

    if (client) {
      // Mettre à jour le client existant
      console.log(`Client existant trouvé avec email ${clientData.email}, ID: ${client._id}`);

      // Préparer les données de mise à jour
      const updateData = {
        ...clientData,
        updatedAt: Date.now()
      };

      // Si le client existant a déjà un vendeurId et que l'utilisateur actuel n'est pas un vendeur,
      // conserver le vendeurId existant sauf si explicitement modifié
      if (client.vendeurId && req.userRole !== 'VENDEUR' && !clientData.vendeurId) {
        updateData.vendeurId = client.vendeurId;
      }

      // Fusionner les responsables existants avec les nouveaux
      if (client.responsables && client.responsables.length > 0) {
        const existingResponsables = client.responsables.map(id => id.toString());
        const newResponsables = updateData.responsables || [];
        updateData.responsables = [...new Set([...existingResponsables, ...newResponsables])];
      }

      // Mettre à jour le client
      newClient = await Client.findByIdAndUpdate(
        client._id,
        updateData,
        { new: true, runValidators: true }
      );

      console.log('Client mis à jour:', newClient);
    } else {
      // Créer un nouveau client
      console.log(`Création d'un nouveau client avec email ${clientData.email}`);
      newClient = new Client(clientData);
      await newClient.save();
      isNewClient = true;
      console.log('Nouveau client créé:', newClient);
    }

    // Si le client a un email, mettre à jour les utilisateurs de type RESPONSABLE avec cet email
    if (clientData.email) {
      const responsableUsers = await User.find({
        role: 'RESPONSABLE',
        email: clientData.email
      });

      console.log(`Found ${responsableUsers.length} responsable users with email ${clientData.email}`);

      // Update all matching responsable users with this client ID
      if (responsableUsers.length > 0) {
        for (const user of responsableUsers) {
          user.responsableId = newClient._id;
          await user.save();
          console.log(`Updated responsable user ${user._id} with client ID ${newClient._id}`);
        }
      }
    }

    // Créer un compte utilisateur si demandé
    if (clientData.createUserAccount && clientData.motDePasse) {
      console.log('Création d\'un compte utilisateur pour le client:', newClient._id);

      // Vérifier si un utilisateur avec cet email existe déjà
      const existingUser = await User.findOne({ email: clientData.email });

      if (existingUser) {
        console.log(`Un utilisateur avec l'email ${clientData.email} existe déjà, ID: ${existingUser._id}`);

        // Mettre à jour l'utilisateur existant
        existingUser.nom = clientData.nom;
        existingUser.adresse = clientData.adresse;
        existingUser.contact = clientData.contact;
        existingUser.cin = clientData.cin;
        existingUser.responsableId = newClient._id;

        // Mettre à jour le mot de passe uniquement s'il est fourni
        if (clientData.motDePasse) {
          const hashedPassword = await bcrypt.hash(clientData.motDePasse, 10);
          existingUser.motDePasse = hashedPassword;
        }

        // Ajouter le responsable actuel aux responsables de l'utilisateur si c'est un responsable d'entreprise
        if (req.userRole === 'RESPONSABLE' && !existingUser.responsables.includes(req.userId)) {
          existingUser.responsables.push(req.userId);
        }

        await existingUser.save();
        console.log('Utilisateur existant mis à jour:', existingUser._id);
      } else {
        // Créer un nouvel utilisateur
        const hashedPassword = await bcrypt.hash(clientData.motDePasse, 10);

        const newUser = new User({
          nom: clientData.nom,
          email: clientData.email,
          motDePasse: hashedPassword,
          role: 'CLIENT',
          adresse: clientData.adresse,
          contact: clientData.contact,
          telephone: clientData.contact,
          cin: clientData.cin,
          responsableId: newClient._id,
          createdBy: req.userId,
          dateCreation: new Date()
        });

        // Ajouter le responsable actuel aux responsables de l'utilisateur si c'est un responsable d'entreprise
        if (req.userRole === 'RESPONSABLE') {
          newUser.responsables = [req.userId];
        }

        // Si des responsables sont spécifiés, les ajouter
        if (clientData.responsables && Array.isArray(clientData.responsables) && clientData.responsables.length > 0) {
          // Fusionner avec les responsables existants sans doublons
          const allResponsables = [...new Set([...(newUser.responsables || []), ...clientData.responsables])];
          newUser.responsables = allResponsables;
        }

        await newUser.save();
        console.log('Nouvel utilisateur créé pour le client:', newUser._id);
      }
    }

    // Retourner le client avec un statut 201 si nouveau, 200 si mis à jour
    res.status(isNewClient ? 201 : 200).json(newClient);
  } catch (error) {
    console.error('Erreur lors de la création/mise à jour du client:', error.message);
    res.status(500).json({ error: 'Erreur lors de la création/mise à jour du client', details: error.message });
  }
});

// Mettre à jour un client
router.put('/clients/:id', verifyToken, async (req, res) => {
  try {
    console.log('Mise à jour du client avec ID:', req.params.id);
    console.log('Données reçues pour la mise à jour:', req.body);

    // Vérifier si le client existe avant la mise à jour
    const existingClient = await Client.findById(req.params.id);
    if (!existingClient) {
      console.log('Client non trouvé avec ID:', req.params.id);
      return res.status(404).json({ error: 'Client non trouvé' });
    }

    // Préparer les données de mise à jour en conservant les champs requis
    const updateData = {
      ...req.body,
      // S'assurer que les champs requis sont présents
      nom: req.body.nom || existingClient.nom,
      adresse: req.body.adresse || existingClient.adresse,
      email: req.body.email || existingClient.email,
      contact: req.body.contact || existingClient.contact,
      // Mettre à jour la date de modification
      updatedAt: Date.now()
    };

    // Effectuer la mise à jour
    const client = await Client.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    console.log('Client mis à jour avec succès:', client);

    // Si l'email du client a été mis à jour, mettre à jour également l'utilisateur correspondant
    if (req.body.email) {
      try {
        // Trouver les utilisateurs de type CLIENT avec cet ID client
        const users = await User.find({
          role: 'CLIENT',
          responsableId: client._id
        });

        console.log(`Found ${users.length} client users with responsableId ${client._id}`);

        // Mettre à jour les utilisateurs trouvés
        for (const user of users) {
          // Mettre à jour les champs correspondants
          user.nom = client.nom;
          user.email = client.email;
          user.adresse = client.adresse;
          user.telephone = client.telephone || client.contact;
          user.contact = client.contact;
          user.cin = client.cin;

          // Mettre à jour le mot de passe si fourni
          if (req.body.motDePasse) {
            const hashedPassword = await bcrypt.hash(req.body.motDePasse, 10);
            user.motDePasse = hashedPassword;
          }

          // Ajouter le responsable actuel aux responsables de l'utilisateur si c'est un responsable d'entreprise
          if (req.userRole === 'RESPONSABLE' && !user.responsables.includes(req.userId)) {
            user.responsables.push(req.userId);
          }

          await user.save();
          console.log(`Updated client user ${user._id} with client data`);
        }

        // Si aucun utilisateur n'a été trouvé et que createUserAccount est demandé, créer un nouvel utilisateur
        if (users.length === 0 && req.body.createUserAccount && req.body.motDePasse) {
          console.log('Création d\'un compte utilisateur pour le client existant:', client._id);

          const hashedPassword = await bcrypt.hash(req.body.motDePasse, 10);

          const newUser = new User({
            nom: client.nom,
            email: client.email,
            motDePasse: hashedPassword,
            role: 'CLIENT',
            adresse: client.adresse,
            contact: client.contact,
            telephone: client.contact,
            cin: client.cin,
            responsableId: client._id,
            createdBy: req.userId,
            dateCreation: new Date()
          });

          // Ajouter le responsable actuel aux responsables de l'utilisateur si c'est un responsable d'entreprise
          if (req.userRole === 'RESPONSABLE') {
            newUser.responsables = [req.userId];
          }

          // Si des responsables sont spécifiés, les ajouter
          if (req.body.responsables && Array.isArray(req.body.responsables) && req.body.responsables.length > 0) {
            // Fusionner avec les responsables existants sans doublons
            const allResponsables = [...new Set([...(newUser.responsables || []), ...req.body.responsables])];
            newUser.responsables = allResponsables;
          }

          await newUser.save();
          console.log('Nouvel utilisateur créé pour le client existant:', newUser._id);
        }
      } catch (error) {
        console.error('Erreur lors de la mise à jour des utilisateurs associés:', error.message);
        // Ne pas échouer la requête si cette partie échoue
      }
    }

    res.status(200).json(client);
  } catch (error) {
    console.error('Erreur lors de la mise à jour du client:', error.message);
    console.error('Détails de l\'erreur:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour du client',
      details: error.message
    });
  }
});

// Supprimer un client
router.delete('/clients/:id', verifyToken, async (req, res) => {
  try {
    const client = await Client.findByIdAndDelete(req.params.id);
    if (!client) return res.status(404).json({ error: 'Client non trouvé' });
    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression du client:', error.message);
    res.status(500).json({ error: 'Erreur lors de la suppression du client' });
  }
});

// Récupérer les factures d'un client
router.get('/clients/:id/factures', verifyToken, async (req, res) => {
  try {
    const factures = await Facture.find({ clientId: req.params.id }).populate('clientId');
    res.status(200).json(factures);
  } catch (error) {
    console.error('Erreur lors de la récupération des factures:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des factures' });
  }
});

// DEBUG: Vérifier si un client existe par ID
router.get('/clients/check/:id', verifyToken, async (req, res) => {
  try {
    console.log(`Checking if client with ID ${req.params.id} exists`);
    const client = await Client.findById(req.params.id);
    if (client) {
      console.log('Client found:', client);
      res.status(200).json({ exists: true, client });
    } else {
      console.log(`No client found with ID ${req.params.id}`);
      res.status(200).json({ exists: false });
    }
  } catch (error) {
    console.error(`Error checking client ${req.params.id}:`, error.message);
    res.status(500).json({ error: 'Error checking client' });
  }
});

// Vérifier si un client existe par email
router.get('/clients/check-email', verifyToken, async (req, res) => {
  try {
    const { email } = req.query;
    if (!email) {
      return res.status(400).json({ error: 'Email parameter is required' });
    }

    console.log(`Checking if client with email ${email} exists`);
    const client = await Client.findOne({ email }).populate('vendeurId', 'nom email profileImage');

    if (client) {
      console.log('Client found with email:', client);
      res.status(200).json({ exists: true, client });
    } else {
      console.log(`No client found with email ${email}`);
      res.status(200).json({ exists: false });
    }
  } catch (error) {
    console.error(`Error checking client with email:`, error.message);
    res.status(500).json({ error: 'Error checking client' });
  }
});

// Récupérer les devis d'un client
router.get('/clients/:id/devis', verifyToken, async (req, res) => {
  try {
    const devis = await Devis.find({ clientId: req.params.id }).populate('clientId');
    res.status(200).json(devis);
  } catch (error) {
    console.error('Erreur lors de la récupération des devis:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des devis' });
  }
});

// Upload client logo
router.post('/clients/:id/logo', verifyToken, upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Aucun fichier téléchargé' });
    }

    const logoUrl = `/uploads/clients/${req.file.filename}`;

    const client = await Client.findByIdAndUpdate(
      req.params.id,
      { logo: logoUrl, updatedAt: Date.now() },
      { new: true }
    );

    if (!client) {
      return res.status(404).json({ error: 'Client non trouvé' });
    }

    // Create activity for logo update
    await new ClientActivity({
      clientId: req.params.id,
      type: 'NOTE',
      titre: 'Logo mis à jour',
      description: 'Le logo du client a été mis à jour',
      userId: req.userId
    }).save();

    res.status(200).json({
      success: true,
      logoUrl: logoUrl,
      client: client
    });
  } catch (error) {
    console.error('Erreur lors du téléchargement du logo:', error.message);
    res.status(500).json({ error: 'Erreur lors du téléchargement du logo' });
  }
});

// Get client activities
router.get('/clients/:id/activities', verifyToken, async (req, res) => {
  try {
    const activities = await ClientActivity.find({ clientId: req.params.id })
      .sort({ date: -1 })
      .populate('userId', 'nom email');

    res.status(200).json(activities);
  } catch (error) {
    console.error('Erreur lors de la récupération des activités:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des activités' });
  }
});

// Add client activity
router.post('/clients/:id/activities', verifyToken, async (req, res) => {
  try {
    const { type, titre, description, date, documentId, documentType } = req.body;

    const newActivity = new ClientActivity({
      clientId: req.params.id,
      type,
      titre,
      description,
      date: date || Date.now(),
      userId: req.userId,
      documentId,
      documentType
    });

    await newActivity.save();

    // Populate user information before returning
    const populatedActivity = await ClientActivity.findById(newActivity._id)
      .populate('userId', 'nom email');

    res.status(201).json(populatedActivity);
  } catch (error) {
    console.error('Erreur lors de l\'ajout d\'une activité:', error.message);
    res.status(500).json({ error: 'Erreur lors de l\'ajout d\'une activité' });
  }
});

// Get client statistics
router.get('/clients/:id/stats', verifyToken, async (req, res) => {
  try {
    const clientId = req.params.id;

    // Get all invoices for this client
    const factures = await Facture.find({ clientId });

    // Get all quotes for this client
    const devis = await Devis.find({ clientId });

    // Calculate statistics
    const totalFactures = factures.length;
    const totalDevis = devis.length;
    const montantTotal = factures.reduce((sum, facture) => sum + facture.total, 0);
    const facturesPaid = factures.filter(f => f.statut === 'PAID').length;
    const facturesPending = factures.filter(f => f.statut !== 'PAID' && f.statut !== 'CANCELED').length;

    // Get most recent activity
    const recentActivity = await ClientActivity.findOne({ clientId })
      .sort({ date: -1 })
      .populate('userId', 'nom email');

    res.status(200).json({
      totalFactures,
      totalDevis,
      montantTotal,
      facturesPaid,
      facturesPending,
      recentActivity
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
  }
});

module.exports = router;