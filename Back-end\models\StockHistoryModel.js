// backend/models/StockHistoryModel.js
const mongoose = require('mongoose');
const { Schema } = mongoose;

const stockHistorySchema = new Schema({
    produitId: {
        type: Schema.Types.ObjectId,
        ref: 'Produit',
        required: true
    },
    factureId: {
        type: Schema.Types.ObjectId,
        ref: 'Facture',
        required: false
    },
    quantiteAvant: {
        type: Number,
        required: true
    },
    quantiteApres: {
        type: Number,
        required: true
    },
    difference: {
        type: Number,
        required: true
    },
    typeOperation: {
        type: String,
        enum: ['VENTE', 'AJUSTEMENT', 'RETOUR', 'INVENTAIRE', 'AUTRE'],
        default: 'AUTRE'
    },
    motif: {
        type: String
    },
    utilisateurId: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: false
    },
    date: {
        type: Date,
        default: Date.now
    }
}, { timestamps: true });

// Index pour améliorer les performances des requêtes
stockHistorySchema.index({ produitId: 1, date: -1 });
stockHistorySchema.index({ factureId: 1 });

module.exports = mongoose.model('StockHistory', stockHistorySchema);
