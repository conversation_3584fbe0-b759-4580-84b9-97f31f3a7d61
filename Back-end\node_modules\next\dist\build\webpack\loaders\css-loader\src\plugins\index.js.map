{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/index.ts"], "sourcesContent": ["import importParser from './postcss-import-parser'\nimport icssParser from './postcss-icss-parser'\nimport urlParser from './postcss-url-parser'\n\nexport { importParser, icssParser, urlParser }\n"], "names": ["icss<PERSON><PERSON><PERSON>", "importParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;IAIuBA,UAAU;eAAVA,0BAAU;;IAAxBC,YAAY;eAAZA,4BAAY;;IAAcC,SAAS;eAATA,yBAAS;;;4EAJnB;0EACF;yEACD"}