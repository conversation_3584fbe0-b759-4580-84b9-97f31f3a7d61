{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "sourcesContent": ["import { computeChangedPath } from './compute-changed-path'\nimport type {\n  Mu<PERSON>,\n  ReadonlyReducerState,\n  ReducerState,\n} from './router-reducer-types'\n\nfunction isNotUndefined<T>(value: T): value is Exclude<T, undefined> {\n  return typeof value !== 'undefined'\n}\n\nexport function handleMutable(\n  state: ReadonlyReducerState,\n  mutable: Mutable\n): ReducerState {\n  // shouldScroll is true by default, can override to false.\n  const shouldScroll = mutable.shouldScroll ?? true\n\n  let nextUrl = state.nextUrl\n\n  if (isNotUndefined(mutable.patchedTree)) {\n    // If we received a patched tree, we need to compute the changed path.\n    const changedPath = computeChangedPath(state.tree, mutable.patchedTree)\n    if (changedPath) {\n      // If the tree changed, we need to update the nextUrl\n      nextUrl = changedPath\n    } else if (!nextUrl) {\n      // if the tree ends up being the same (ie, no changed path), and we don't have a nextUrl, then we should use the canonicalUrl\n      nextUrl = state.canonicalUrl\n    }\n    // otherwise this will be a no-op and continue to use the existing nextUrl\n  }\n\n  return {\n    // Set href.\n    canonicalUrl: isNotUndefined(mutable.canonicalUrl)\n      ? mutable.canonicalUrl === state.canonicalUrl\n        ? state.canonicalUrl\n        : mutable.canonicalUrl\n      : state.canonicalUrl,\n    pushRef: {\n      pendingPush: isNotUndefined(mutable.pendingPush)\n        ? mutable.pendingPush\n        : state.pushRef.pendingPush,\n      mpaNavigation: isNotUndefined(mutable.mpaNavigation)\n        ? mutable.mpaNavigation\n        : state.pushRef.mpaNavigation,\n      preserveCustomHistoryState: isNotUndefined(\n        mutable.preserveCustomHistoryState\n      )\n        ? mutable.preserveCustomHistoryState\n        : state.pushRef.preserveCustomHistoryState,\n    },\n    // All navigation requires scroll and focus management to trigger.\n    focusAndScrollRef: {\n      apply: shouldScroll\n        ? isNotUndefined(mutable?.scrollableSegments)\n          ? true\n          : state.focusAndScrollRef.apply\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          false,\n      onlyHashChange: mutable.onlyHashChange || false,\n      hashFragment: shouldScroll\n        ? // Empty hash should trigger default behavior of scrolling layout into view.\n          // #top is handled in layout-router.\n          mutable.hashFragment && mutable.hashFragment !== ''\n          ? // Remove leading # and decode hash to make non-latin hashes work.\n            decodeURIComponent(mutable.hashFragment.slice(1))\n          : state.focusAndScrollRef.hashFragment\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          null,\n      segmentPaths: shouldScroll\n        ? mutable?.scrollableSegments ?? state.focusAndScrollRef.segmentPaths\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          [],\n    },\n    // Apply cache.\n    cache: mutable.cache ? mutable.cache : state.cache,\n    prefetchCache: mutable.prefetchCache\n      ? mutable.prefetchCache\n      : state.prefetchCache,\n    // Apply patched router state.\n    tree: isNotUndefined(mutable.patchedTree)\n      ? mutable.patchedTree\n      : state.tree,\n    nextUrl,\n  }\n}\n"], "names": ["handleMutable", "isNotUndefined", "value", "state", "mutable", "shouldScroll", "nextUrl", "patchedTree", "changedPath", "computeChangedPath", "tree", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;oCAXmB;AAOnC,SAASC,eAAkBC,KAAQ;IACjC,OAAO,OAAOA,UAAU;AAC1B;AAEO,SAASF,cACdG,KAA2B,EAC3BC,OAAgB;QAGKA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;IAE7C,IAAIE,UAAUH,MAAMG,OAAO;IAE3B,IAAIL,eAAeG,QAAQG,WAAW,GAAG;QACvC,sEAAsE;QACtE,MAAMC,cAAcC,IAAAA,sCAAkB,EAACN,MAAMO,IAAI,EAAEN,QAAQG,WAAW;QACtE,IAAIC,aAAa;YACf,qDAAqD;YACrDF,UAAUE;QACZ,OAAO,IAAI,CAACF,SAAS;YACnB,6HAA6H;YAC7<PERSON>,UAAUH,MAAMQ,YAAY;QAC9B;IACA,0EAA0E;IAC5E;QAyCQP;IAvCR,OAAO;QACL,YAAY;QACZO,cAAcV,eAAeG,QAAQO,YAAY,IAC7CP,QAAQO,YAAY,KAAKR,MAAMQ,YAAY,GACzCR,MAAMQ,YAAY,GAClBP,QAAQO,YAAY,GACtBR,MAAMQ,YAAY;QACtBC,SAAS;YACPC,aAAaZ,eAAeG,QAAQS,WAAW,IAC3CT,QAAQS,WAAW,GACnBV,MAAMS,OAAO,CAACC,WAAW;YAC7BC,eAAeb,eAAeG,QAAQU,aAAa,IAC/CV,QAAQU,aAAa,GACrBX,MAAMS,OAAO,CAACE,aAAa;YAC/BC,4BAA4Bd,eAC1BG,QAAQW,0BAA0B,IAEhCX,QAAQW,0BAA0B,GAClCZ,MAAMS,OAAO,CAACG,0BAA0B;QAC9C;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOZ,eACHJ,eAAeG,2BAAAA,QAASc,kBAAkB,IACxC,OACAf,MAAMa,iBAAiB,CAACC,KAAK,GAE/B;YACJE,gBAAgBf,QAAQe,cAAc,IAAI;YAC1CC,cAAcf,eAEV,oCAAoC;YACpCD,QAAQgB,YAAY,IAAIhB,QAAQgB,YAAY,KAAK,KAE/CC,mBAAmBjB,QAAQgB,YAAY,CAACE,KAAK,CAAC,MAC9CnB,MAAMa,iBAAiB,CAACI,YAAY,GAEtC;YACJG,cAAclB,eACVD,CAAAA,8BAAAA,2BAAAA,QAASc,kBAAkB,YAA3Bd,8BAA+BD,MAAMa,iBAAiB,CAACO,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOpB,QAAQoB,KAAK,GAAGpB,QAAQoB,KAAK,GAAGrB,MAAMqB,KAAK;QAClDC,eAAerB,QAAQqB,aAAa,GAChCrB,QAAQqB,aAAa,GACrBtB,MAAMsB,aAAa;QACvB,8BAA8B;QAC9Bf,MAAMT,eAAeG,QAAQG,WAAW,IACpCH,QAAQG,WAAW,GACnBJ,MAAMO,IAAI;QACdJ;IACF;AACF"}