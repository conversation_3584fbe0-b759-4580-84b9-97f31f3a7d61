import React, { useState, useEffect, useCallback } from 'react';
import {
  Box, Container, Grid, Card, CardHeader, CardContent, Typography,
  Divider, IconButton, Tooltip, useTheme, alpha, TableContainer, Table, TableHead,
  TableBody, TableRow, TableCell, Avatar, Stack, TextField, InputAdornment,
  Button, Chip
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  Business as BusinessIcon,
  SupervisorAccount as SupervisorIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon
} from '@mui/icons-material';
import {
  Pie<PERSON><PERSON>, Pie, ResponsiveContainer, Legend, Cell
} from 'recharts';
import userMetricsService from '../services/userMetricsService';
import SimpleDateFilter from '../components/SimpleDateFilter';
import { formatCurrency } from '../utils/formatters';
import PowerBIExport from '../components/PowerBIExport';

// Chart colors
const CHART_COLORS = {
  primary: '#6C5CE7',
  secondary: '#00B894',
  info: '#0984E3',
  success: '#00CEC9',
  warning: '#FDCB6E',
  error: '#FF7675',
  purple: '#A29BFE',
  pink: '#FD79A8',
  orange: '#E17055',
  teal: '#55EFC4'
};

const AdminAnalyticsDashboard = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('all-time');

  // State for all dashboard data
  const [userMetrics, setUserMetrics] = useState(null);
  const [vendorPerformance, setVendorPerformance] = useState([]);
  const [userDistribution, setUserDistribution] = useState([]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 }
    }
  };

  const cardVariants = {
    hidden: { scale: 0.95, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    },
    hover: {
      scale: 1.02,
      boxShadow: "0px 8px 25px rgba(0, 0, 0, 0.1)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }
  };

  // Fetch all dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      console.log('AdminAnalyticsDashboard - fetchDashboardData called with period:', period);
      setLoading(true);

      // Récupérer les données des utilisateurs
      const userMetricsData = await userMetricsService.getUserMetrics()
        .catch(err => {
          console.error('Error fetching user metrics:', err);
          return {
            totalUsers: 1, // Au moins l'administrateur
            activeUsers: 1,
            inactiveUsers: 0,
            newUsers: 0,
            userGrowth: 0,
            usersByRole: {
              admin: 1,
              vendeur: 0,
              entreprise: 0
            }
          };
        });

      // Récupérer la distribution des utilisateurs par rôle
      const userDistributionResponse = await userMetricsService.getUsersByRole()
        .catch(err => {
          console.error('Error fetching user distribution:', err);
          return {
            chartData: [
              { name: 'Administrateurs', value: 1, color: CHART_COLORS.warning },
              { name: 'Vendeurs', value: 0, color: CHART_COLORS.primary },
              { name: 'Entreprises', value: 0, color: CHART_COLORS.secondary }
            ]
          };
        });

      // Récupérer les performances des vendeurs
      const vendorPerformanceData = await userMetricsService.getTopVendors(10, period)
        .catch(err => {
          console.error('Error fetching vendor performance:', err);
          return [];
        });

      // Préparer les données de distribution des utilisateurs
      const userDistributionData = userDistributionResponse?.chartData || [
        {
          name: 'Vendeurs',
          value: userMetricsData?.usersByRole?.vendeur || 0,
          color: CHART_COLORS.primary
        },
        {
          name: 'Entreprises',
          value: userMetricsData?.usersByRole?.entreprise || 0,
          color: CHART_COLORS.secondary
        },
        {
          name: 'Administrateurs',
          value: userMetricsData?.usersByRole?.admin || 0,
          color: CHART_COLORS.warning
        }
      ];

      // Mettre à jour l'état avec les données réelles
      setUserMetrics(userMetricsData);
      setVendorPerformance(vendorPerformanceData || []);
      setUserDistribution(userDistributionData);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  }, [period]);

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Handle date filter change
  const handleDateRangeChange = (rangeId, dateRange) => {
    // Map the range ID to period format
    let newPeriod;
    switch(rangeId) {
      case 'today':
      case 'yesterday':
        newPeriod = 'daily';
        break;
      case 'thisWeek':
      case 'lastWeek':
        newPeriod = 'weekly';
        break;
      case 'thisMonth':
      case 'lastMonth':
        newPeriod = 'monthly';
        break;
      case 'thisQuarter':
        newPeriod = 'quarterly';
        break;
      case 'thisYear':
      case 'lastYear':
        newPeriod = 'yearly';
        break;
      case 'allTime':
        newPeriod = 'all-time';
        break;
      case 'custom':
        newPeriod = 'custom';
        break;
      default:
        newPeriod = 'monthly';
    }

    // Mettre à jour la période et déclencher le chargement des données
    setPeriod(newPeriod);

    // Utiliser setTimeout pour s'assurer que la mise à jour de l'état est prise en compte
    setTimeout(() => {
      setLoading(true);
      fetchDashboardData();
    }, 0);
  };

  // Handle refresh
  const handleRefresh = () => {
    // Utiliser la période actuellement sélectionnée pour actualiser les données
    fetchDashboardData();
  };

  // KPI Card Component based on the PowerBIDashboard design
  const KpiCard = ({ title, value, subtitle, icon, color, trend, trendValue }) => {
    const isTrendPositive = trendValue > 0;
    const trendIcon = isTrendPositive ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />;
    const trendColor = isTrendPositive ? 'success' : 'error';

    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <Card
          elevation={0}
          sx={{
            height: '100%',
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette[color].light, 0.2)} 0%, ${alpha(theme.palette[color].main, 0.05)} 100%)`,
            border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette[color].main, 0.03)} 100%)`,
              zIndex: 0
            }
          }}
        >
          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  backgroundColor: alpha(theme.palette[color].main, 0.15),
                  color: theme.palette[color].main,
                  mr: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette[color].main, 0.2)}`
                }}
              >
                {icon}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="500" color="text.primary">
                  {title}
                </Typography>
                {trend && (
                  <Chip
                    icon={trendIcon}
                    label={`${Math.abs(trendValue).toFixed(1)}%`}
                    size="small"
                    color={trendColor}
                    sx={{
                      height: 24,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      mt: 0.5
                    }}
                  />
                )}
              </Box>
            </Box>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                color: theme.palette[color].dark,
                textShadow: `0 2px 4px ${alpha(theme.palette[color].main, 0.2)}`
              }}
            >
              {value}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                opacity: 0.8,
                maxWidth: '90%'
              }}
            >
              {subtitle}
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Skip loading state and render the dashboard immediately
  // This removes the loading spinner and message

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 2, sm: 3 },
        background: `linear-gradient(180deg, ${alpha(theme.palette.background.default, 0.6)} 0%, ${theme.palette.background.default} 100%)`,
        minHeight: '100vh'
      }}
    >
      <Container maxWidth="xl">
        {/* Header */}
        <Box
          component={motion.div}
          variants={itemVariants}
          sx={{
            mb: 4,
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', md: 'center' }
          }}
        >
          <Box>
            <Typography
              variant="h3"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}
            >
              Tableau de Bord Analytique
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
              Visualisez et analysez vos données commerciales
            </Typography>
          </Box>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mt: { xs: 2, md: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            <TextField
              placeholder="Rechercher..."
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                sx: { borderRadius: 2 }
              }}
              sx={{ width: { xs: '100%', sm: 220 } }}
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                minWidth: 130
              }}
            >
              Actualiser
            </Button>

            <SimpleDateFilter
              onDateRangeChange={handleDateRangeChange}
              onRefresh={handleRefresh}
              initialRange="allTime"
              showRefreshButton={false}
            />
          </Stack>
        </Box>

        {/* Dashboard Content */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* KPI Cards */}
            <Grid
              container
              spacing={3}
              sx={{ mb: 4 }}
              component={motion.div}
              variants={itemVariants}
            >
              <Grid item xs={12} sm={6}>
                <KpiCard
                  title="Vendeurs"
                  value={userMetrics?.usersByRole?.vendeur?.toString() || "0"}
                  subtitle="Nombre total de vendeurs"
                  icon={<SupervisorIcon />}
                  color="primary"
                  trend={false}
                  trendValue={0}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <KpiCard
                  title="Entreprises"
                  value={userMetrics?.usersByRole?.entreprise?.toString() || "0"}
                  subtitle="Nombre total d'entreprises"
                  icon={<BusinessIcon />}
                  color="secondary"
                  trend={false}
                  trendValue={0}
                />
              </Grid>
            </Grid>

            {/* Main Content */}
            <Grid
              container
              spacing={3}
              component={motion.div}
              variants={itemVariants}
            >
              {/* User Distribution */}
              <Grid item xs={12} md={6}>
                <motion.div variants={cardVariants}>
                  <Card
                    elevation={0}
                    sx={{
                      mb: 3,
                      borderRadius: 3,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                      overflow: 'hidden'
                    }}
                  >
                    <CardHeader
                      title={
                        <Typography variant="h6" fontWeight="600">
                          Distribution des Utilisateurs
                        </Typography>
                      }
                      action={
                        <Tooltip title="Actualiser">
                          <IconButton size="small" onClick={handleRefresh}>
                            <RefreshIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      }
                      sx={{ px: 3, pt: 3, pb: 2 }}
                    />
                    <Divider />
                    <CardContent sx={{ height: 400, p: 3 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={userDistribution}
                            cx="50%"
                            cy="50%"
                            innerRadius={70}
                            outerRadius={90}
                            paddingAngle={3}
                            dataKey="value"
                            label={({ name }) => `${name}`}
                            labelLine={false}
                          >
                            {userDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Legend
                            verticalAlign="bottom"
                            height={36}
                            formatter={(value) => <span style={{ color: theme.palette.text.primary, fontWeight: 500 }}>{value}</span>}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>

              {/* Top Vendeurs */}
              <Grid item xs={12} md={6}>
                <motion.div variants={cardVariants}>
                  <Card
                    elevation={0}
                    sx={{
                      mb: 3,
                      borderRadius: 3,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                      overflow: 'hidden'
                    }}
                  >
                    <CardHeader
                      title={
                        <Typography variant="h6" fontWeight="600">
                          Top Vendeurs
                        </Typography>
                      }
                      action={
                        <Tooltip title="Actualiser">
                          <IconButton size="small" onClick={handleRefresh}>
                            <RefreshIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      }
                      sx={{ px: 3, pt: 3, pb: 2 }}
                    />
                    <Divider />
                    <CardContent sx={{ height: 400, p: 3, overflow: 'auto' }}>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Vendeur</TableCell>
                              <TableCell align="right">Chiffre d'affaires</TableCell>
                              <TableCell align="right">Factures</TableCell>
                              <TableCell align="right">Clients</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {vendorPerformance.length > 0 ? (
                              vendorPerformance.map((vendor) => (
                                <TableRow key={vendor.id} hover>
                                  <TableCell>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <Avatar
                                        sx={{
                                          width: 32,
                                          height: 32,
                                          mr: 1,
                                          bgcolor: theme.palette.primary.main
                                        }}
                                      >
                                        {vendor.name ? vendor.name.charAt(0) : 'V'}
                                      </Avatar>
                                      <Typography variant="body2" fontWeight={500}>
                                        {vendor.name || 'Vendeur'}
                                      </Typography>
                                    </Box>
                                  </TableCell>
                                  <TableCell align="right">
                                    {formatCurrency(vendor.revenue || 0)}
                                  </TableCell>
                                  <TableCell align="right">
                                    {vendor.invoices || 0}
                                  </TableCell>
                                  <TableCell align="right">
                                    {vendor.clients || 0}
                                  </TableCell>
                                </TableRow>
                              ))
                            ) : (
                              <TableRow>
                                <TableCell colSpan={4} align="center">
                                  <Typography variant="body2" color="text.secondary">
                                    Aucun vendeur trouvé
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            </Grid>

            {/* Power BI Export Component */}
            <Grid container spacing={3} sx={{ mt: 3 }}>
              <Grid item xs={12}>
                <motion.div variants={cardVariants}>
                  <Card
                    elevation={0}
                    sx={{
                      borderRadius: 3,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                      overflow: 'hidden',
                      background: `linear-gradient(135deg, ${alpha(theme.palette.info.light, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`
                    }}
                  >
                    <CardHeader
                      title={
                        <Typography variant="h6" fontWeight="600">
                          Exporter les données pour Power BI
                        </Typography>
                      }
                      subheader="Exportez vos données au format CSV pour les importer dans Power BI"
                      sx={{ px: 3, pt: 3, pb: 2 }}
                    />
                    <Divider />
                    <CardContent sx={{ p: 3 }}>
                      <PowerBIExport />
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            </Grid>

          </motion.div>
      </Container>
    </Box>
  );
};

export default AdminAnalyticsDashboard;