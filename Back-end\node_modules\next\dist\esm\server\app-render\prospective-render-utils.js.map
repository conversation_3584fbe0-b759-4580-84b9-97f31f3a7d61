{"version": 3, "sources": ["../../../src/server/app-render/prospective-render-utils.ts"], "sourcesContent": ["import { getDigestForWellKnownError } from './create-error-handler'\n\nexport function printDebugThrownValueForProspectiveRender(\n  thrownValue: unknown,\n  route: string\n) {\n  // We don't need to print well-known Next.js errors.\n  if (getDigestForWellKnownError(thrownValue)) {\n    return\n  }\n\n  let message: undefined | string\n  if (\n    typeof thrownValue === 'object' &&\n    thrownValue !== null &&\n    typeof (thrownValue as any).message === 'string'\n  ) {\n    message = (thrownValue as any).message\n    if (typeof (thrownValue as any).stack === 'string') {\n      const originalErrorStack: string = (thrownValue as any).stack\n      const stackStart = originalErrorStack.indexOf('\\n')\n      if (stackStart > -1) {\n        const error = new Error(\n          `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled.\n          \nOriginal Error: ${message}`\n        )\n        error.stack =\n          'Error: ' + error.message + originalErrorStack.slice(stackStart)\n        console.error(error)\n        return\n      }\n    }\n  } else if (typeof thrownValue === 'string') {\n    message = thrownValue\n  }\n\n  if (message) {\n    console.error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. No stack was provided.\n          \nOriginal Message: ${message}`)\n    return\n  }\n\n  console.error(\n    `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. The thrown value is logged just following this message`\n  )\n  console.error(thrownValue)\n  return\n}\n"], "names": ["getDigestForWellKnownError", "printDebugThrownValueForProspectiveRender", "thrownValue", "route", "message", "stack", "originalErrorStack", "stackStart", "indexOf", "error", "Error", "slice", "console"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,yBAAwB;AAEnE,OAAO,SAASC,0CACdC,WAAoB,EACpBC,KAAa;IAEb,oDAAoD;IACpD,IAAIH,2BAA2BE,cAAc;QAC3C;IACF;IAEA,IAAIE;IACJ,IACE,OAAOF,gBAAgB,YACvBA,gBAAgB,QAChB,OAAO,AAACA,YAAoBE,OAAO,KAAK,UACxC;QACAA,UAAU,AAACF,YAAoBE,OAAO;QACtC,IAAI,OAAO,AAACF,YAAoBG,KAAK,KAAK,UAAU;YAClD,MAAMC,qBAA6B,AAACJ,YAAoBG,KAAK;YAC7D,MAAME,aAAaD,mBAAmBE,OAAO,CAAC;YAC9C,IAAID,aAAa,CAAC,GAAG;gBACnB,MAAME,QAAQ,qBAIb,CAJa,IAAIC,MAChB,CAAC,MAAM,EAAEP,MAAM;;gBAET,EAAEC,SAAS,GAHL,qBAAA;2BAAA;gCAAA;kCAAA;gBAId;gBACAK,MAAMJ,KAAK,GACT,YAAYI,MAAML,OAAO,GAAGE,mBAAmBK,KAAK,CAACJ;gBACvDK,QAAQH,KAAK,CAACA;gBACd;YACF;QACF;IACF,OAAO,IAAI,OAAOP,gBAAgB,UAAU;QAC1CE,UAAUF;IACZ;IAEA,IAAIE,SAAS;QACXQ,QAAQH,KAAK,CAAC,CAAC,MAAM,EAAEN,MAAM;;kBAEf,EAAEC,SAAS;QACzB;IACF;IAEAQ,QAAQH,KAAK,CACX,CAAC,MAAM,EAAEN,MAAM,wOAAwO,CAAC;IAE1PS,QAAQH,KAAK,CAACP;IACd;AACF"}