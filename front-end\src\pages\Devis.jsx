import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Tooltip,
  Snackbar,
  Alert,
  Divider,
  useTheme,
  alpha,
  Card,
  CardHeader,
  Stack,
  Badge,
  Avatar,
  InputAdornment,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  PictureAsPdf as PdfIcon,
  FileCopy as ConvertIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Description as DescriptionIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import DevisCard from '../components/DevisCard';
import EmailDialog from '../components/EmailDialog';
import devisService from '../services/devisService';
import clientService from '../services/clientService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';
import { useAuth } from '../contexts/AuthContext';

const statuts = [
  { value: 'all', label: 'Tous les statuts' },
  { value: 'DRAFT', label: 'Brouillon' },
  { value: 'SENT', label: 'Envoyé' },
  { value: 'PENDING', label: 'En attente' },
  { value: 'WAITING_APPROVAL', label: 'En attente de validation' },
  { value: 'APPROVED_INTERNAL', label: 'Validé en interne' },
  { value: 'ACCEPTED', label: 'Accepté' },
  { value: 'REJECTED', label: 'Rejeté' },
  { value: 'EXPIRED', label: 'Expiré' }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

const Devis = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [devis, setDevis] = useState([]);
  const [clients, setClients] = useState([]);
  const [filteredDevis, setFilteredDevis] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statutFilter, setStatutFilter] = useState('all');
  const [clientFilter, setClientFilter] = useState('all');
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openEmailDialog, setOpenEmailDialog] = useState(false);
  const [selectedDevis, setSelectedDevis] = useState(null);
  const [viewMode, setViewMode] = useState('list');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Get status color with proper opacity for backgrounds
  const getStatusColor = (status) => {
    switch(status) {
      case 'ACCEPTED':
      case 'ACCEPTÉ':
        return alpha(theme.palette.success.main, 0.1);
      case 'REJECTED':
      case 'REFUSÉ':
        return alpha(theme.palette.error.main, 0.1);
      case 'DRAFT':
      case 'BROUILLON':
        return alpha(theme.palette.info.main, 0.1);
      case 'EXPIRED':
      case 'EXPIRÉ':
        return alpha(theme.palette.error.main, 0.1);
      default:
        return alpha(theme.palette.warning.main, 0.1);
    }
  };

  // Get status text color
  const getStatusTextColor = (status) => {
    switch(status) {
      case 'ACCEPTED':
      case 'ACCEPTÉ':
        return theme.palette.success.main;
      case 'REJECTED':
      case 'REFUSÉ':
        return theme.palette.error.main;
      case 'DRAFT':
      case 'BROUILLON':
        return theme.palette.info.main;
      case 'EXPIRED':
      case 'EXPIRÉ':
        return theme.palette.error.main;
      default:
        return theme.palette.warning.main;
    }
  };

  // Déterminer le préfixe de route en fonction du rôle de l'utilisateur
  const getRoutePrefix = () => {
    if (!currentUser) return '/';

    switch (currentUser.role) {
      case 'ADMIN':
        return '/admin';
      case 'VENDEUR':
        return '/vendeur';
      case 'RESPONSABLE':
        return '/responsable';
      case 'ENTREPRISE':
        return '/entreprise';
      default:
        return '/client';
    }
  };

  const [demandesDevis, setDemandesDevis] = useState([]);
  const [showDemandesSection, setShowDemandesSection] = useState(false);

  useEffect(() => {
    fetchDevis();
    fetchClients();

    // Si l'utilisateur est un responsable, récupérer les demandes de devis
    if (currentUser && currentUser.role === 'RESPONSABLE') {
      fetchDemandesDevis();
    }
  }, [currentUser]);

  useEffect(() => {
    filterDevis();
  }, [devis, searchTerm, statutFilter, clientFilter]);

  const fetchDevis = async () => {
    try {
      const data = await devisService.getDevis();
      setDevis(data);
    } catch (error) {
      console.error('Error fetching devis:', error);
    }
  };

  const fetchDemandesDevis = async () => {
    try {
      const data = await devisService.getDemandesDevis();
      setDemandesDevis(data);

      // Afficher la section des demandes s'il y a des demandes en attente
      if (data && data.length > 0) {
        setShowDemandesSection(true);
      }
    } catch (error) {
      console.error('Error fetching demandes devis:', error);
    }
  };

  const fetchClients = async () => {
    try {
      const data = await clientService.getClients();
      setClients(data);
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const filterDevis = () => {
    let filtered = [...devis];

    if (searchTerm) {
      filtered = filtered.filter(
        (devi) =>
          devi.numéro.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (devi.clientId?.nom?.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statutFilter !== 'all') {
      // Handle both English and French status values
      const statusMap = {
        'DRAFT': ['DRAFT', 'BROUILLON'],
        'SENT': ['SENT', 'ENVOYÉ'],
        'PENDING': ['PENDING'],
        'WAITING_APPROVAL': ['WAITING_APPROVAL', 'EN_ATTENTE_VALIDATION'],
        'APPROVED_INTERNAL': ['APPROVED_INTERNAL', 'VALIDÉ_INTERNE'],
        'ACCEPTED': ['ACCEPTED', 'ACCEPTÉ'],
        'REJECTED': ['REJECTED', 'REFUSÉ'],
        'EXPIRED': ['EXPIRED', 'EXPIRÉ']
      };

      filtered = filtered.filter((devi) =>
        statusMap[statutFilter] && statusMap[statutFilter].includes(devi.statut)
      );
    }

    if (clientFilter !== 'all') {
      filtered = filtered.filter((devi) => devi.clientId?._id === clientFilter);
    }

    setFilteredDevis(filtered);
  };

  const handleDelete = async () => {
    try {
      await devisService.deleteDevis(selectedDevis._id);
      fetchDevis();
      setOpenDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting devis:', error);
    }
  };

  const handleCreateDevis = () => {
    const routePrefix = getRoutePrefix();
    navigate(`${routePrefix}/devis/new`);
  };

  const handleEditDevis = (id) => {
    const routePrefix = getRoutePrefix();
    navigate(`${routePrefix}/devis/${id}`);
  };

  const handleGeneratePdf = async (id) => {
    try {
      await devisService.generatePdf(id);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  const handleOpenEmailDialog = (devis) => {
    setSelectedDevis(devis);
    setOpenEmailDialog(true);
  };

  const handleCloseEmailDialog = () => {
    setOpenEmailDialog(false);
  };

  const handleSendEmail = async (emailData) => {
    try {
      if (!selectedDevis) return;

      await devisService.sendEmail(selectedDevis._id, emailData);

      setSnackbar({
        open: true,
        message: 'Email envoyé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error sending email:', error);
      setSnackbar({
        open: true,
        message: `Erreur lors de l'envoi de l'email: ${error.message}`,
        severity: 'error'
      });
      throw error;
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handlePrint = async (id) => {
    try {
      await devisService.printDevis(id);
    } catch (error) {
      console.error('Error printing:', error);
    }
  };

  const handleValidateDevis = (id) => {
    // Rediriger vers la page de validation du devis
    const routePrefix = getRoutePrefix();
    navigate(`${routePrefix}/devis/validation/${id}`);
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header Section with Background */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={0}
        sx={{
          mb: 4,
          p: 3,
          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                width: 48,
                height: 48,
                mr: 2
              }}
            >
              <DescriptionIcon fontSize="medium" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                Gestion des Devis
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Créez, modifiez et gérez vos devis
              </Typography>
            </Box>
          </Box>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateDevis}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
              }
            }}
          >
            Nouveau Devis
          </Button>
        </Box>
      </Card>

      {/* Section des demandes de devis en attente de validation (pour les responsables) */}
      {showDemandesSection && currentUser && currentUser.role === 'RESPONSABLE' && (
        <Card
          component={motion.div}
          variants={itemVariants}
          elevation={1}
          sx={{
            mb: 3,
            p: 2,
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`,
            backgroundColor: alpha(theme.palette.warning.main, 0.05)
          }}
        >
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" color="warning.main" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Badge badgeContent={demandesDevis.length} color="warning" sx={{ mr: 2 }} />
              Demandes de devis en attente de validation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Ces demandes nécessitent votre validation avant d'être traitées par les vendeurs.
            </Typography>
          </Box>

          <TableContainer component={Paper} elevation={0}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Client</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Date de demande</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Produits</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {demandesDevis.length > 0 ? (
                  demandesDevis.map((demande) => (
                    <TableRow key={demande._id} hover>
                      <TableCell>
                        {demande.clientId?.nom || 'Client inconnu'}
                      </TableCell>
                      <TableCell>
                        {formatDate(demande.dateCréation)}
                      </TableCell>
                      <TableCell>
                        {demande.lignes?.length || 0} produit(s)
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="contained"
                          color="warning"
                          size="small"
                          onClick={() => handleValidateDevis(demande._id)}
                          sx={{ mr: 1 }}
                        >
                          Valider
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      Aucune demande de devis en attente
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      )}

      {/* Search and Filter Section */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <TextField
            variant="outlined"
            placeholder="Rechercher par numéro, client..."
            size="small"
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              maxWidth: { xs: '100%', sm: 400 },
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.common.white, 0.9)
              }
            }}
          />

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ flexGrow: 0 }}>
            <TextField
              select
              label="Statut"
              variant="outlined"
              size="small"
              value={statutFilter}
              onChange={(e) => setStatutFilter(e.target.value)}
              sx={{
                minWidth: 150,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            >
              {statuts.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>

            <TextField
              select
              label="Client"
              variant="outlined"
              size="small"
              value={clientFilter}
              onChange={(e) => setClientFilter(e.target.value)}
              sx={{
                minWidth: 180,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            >
              <MenuItem value="all">Tous les clients</MenuItem>
              {clients.map((client) => (
                <MenuItem key={client._id} value={client._id}>
                  {client.nom}
                </MenuItem>
              ))}
            </TextField>

            <Stack direction="row" spacing={1}>
              <Button
                variant={viewMode === 'list' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('list')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Liste
              </Button>
              <Button
                variant={viewMode === 'card' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('card')}
                sx={{ borderRadius: 2, minWidth: 80 }}
              >
                Cartes
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Card>

      {/* Table Section */}
      {viewMode === 'list' ? (
        <Card
          component={motion.div}
          variants={itemVariants}
          elevation={1}
          sx={{
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Numéro</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Client</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Montant</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Statut</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 2 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredDevis.map((devi) => (
                  <TableRow
                    key={devi._id}
                    hover
                    sx={{
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.03),
                        cursor: 'pointer'
                      }
                    }}
                  >
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {devi.numéro}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2">
                        {devi.clientId?.nom || (devi.estDemandeClient ? 'Demande client' : 'Client non spécifié')}
                      </Typography>
                      {devi.clientId?.email && (
                        <Typography variant="caption" color="text.secondary">
                          {devi.clientId.email}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2">
                        {formatDate(devi.dateCréation)}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {devi.statut === 'WAITING_APPROVAL' ? 'À déterminer' : formatCurrency(devi.total)}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Chip
                        label={formatStatut(devi.statut)}
                        color={
                          devi.statut === 'ACCEPTED' || devi.statut === 'ACCEPTÉ' ? 'success' :
                          devi.statut === 'REJECTED' || devi.statut === 'REFUSÉ' ? 'error' :
                          devi.statut === 'EXPIRED' || devi.statut === 'EXPIRÉ' ? 'error' : 'warning'
                        }
                        size="small"
                        sx={{
                          fontWeight: 'medium',
                          borderRadius: 1,
                          px: 1
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{ py: 1.5 }}>
                      <Stack direction="row" spacing={0.5}>
                        <Tooltip title="Modifier">
                          <IconButton
                            onClick={() => handleEditDevis(devi._id)}
                            color="primary"
                            size="small"
                            sx={{
                              backgroundColor: alpha(theme.palette.primary.main, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.2) }
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Supprimer">
                          <IconButton
                            onClick={() => {
                              setSelectedDevis(devi);
                              setOpenDeleteDialog(true);
                            }}
                            color="error"
                            size="small"
                            sx={{
                              backgroundColor: alpha(theme.palette.error.main, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.2) }
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Générer PDF">
                          <IconButton
                            onClick={() => handleGeneratePdf(devi._id)}
                            size="small"
                            sx={{
                              color: theme.palette.text.secondary,
                              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                            }}
                          >
                            <PdfIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Envoyer par email">
                          <IconButton
                            onClick={() => handleOpenEmailDialog(devi)}
                            size="small"
                            sx={{
                              color: theme.palette.info.main,
                              backgroundColor: alpha(theme.palette.info.main, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.info.main, 0.2) }
                            }}
                          >
                            <EmailIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Imprimer">
                          <IconButton
                            onClick={() => handlePrint(devi._id)}
                            size="small"
                            sx={{
                              color: theme.palette.text.secondary,
                              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                              '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                            }}
                          >
                            <PrintIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        {(devi.statut === 'ACCEPTED' || devi.statut === 'ACCEPTÉ') && (
                          <Tooltip title="Convertir en facture">
                            <IconButton
                              onClick={() => devisService.convertToFacture(devi._id)
                                .then(() => {
                                  fetchDevis();
                                  setSnackbar({
                                    open: true,
                                    message: 'Devis converti en facture avec succès',
                                    severity: 'success'
                                  });
                                })
                                .catch(err => {
                                  console.error('Erreur lors de la conversion:', err);
                                  setSnackbar({
                                    open: true,
                                    message: 'Erreur lors de la conversion en facture',
                                    severity: 'error'
                                  });
                                })
                              }
                              color="success"
                              size="small"
                              sx={{
                                backgroundColor: alpha(theme.palette.success.main, 0.1),
                                '&:hover': { backgroundColor: alpha(theme.palette.success.main, 0.2) }
                              }}
                            >
                              <ConvertIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      ) : (
        <Box component={motion.div} variants={itemVariants}>
          <Grid container spacing={3}>
            {filteredDevis.map((devi) => (
              <Grid item xs={12} sm={6} md={4} key={devi._id} component={motion.div} variants={itemVariants}>
                <DevisCard
                  devis={devi}
                  onEdit={() => handleEditDevis(devi._id)}
                  onDelete={() => {
                    setSelectedDevis(devi);
                    setOpenDeleteDialog(true);
                  }}
                  onPdf={() => handleGeneratePdf(devi._id)}
                  onEmail={() => handleOpenEmailDialog(devi)}
                  onPrint={() => handlePrint(devi._id)}
                  onConvert={(devi.statut === 'ACCEPTED' || devi.statut === 'ACCEPTÉ') ?
                    () => devisService.convertToFacture(devi._id)
                      .then(() => {
                        fetchDevis();
                        setSnackbar({
                          open: true,
                          message: 'Devis converti en facture avec succès',
                          severity: 'success'
                        });
                      })
                      .catch(err => {
                        console.error('Erreur lors de la conversion:', err);
                        setSnackbar({
                          open: true,
                          message: 'Erreur lors de la conversion en facture',
                          severity: 'error'
                        });
                      })
                    : null}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Empty state when no quotes */}
      {filteredDevis.length === 0 && (
        <Card
          component={motion.div}
          variants={itemVariants}
          sx={{
            p: 5,
            textAlign: 'center',
            borderRadius: 2,
            backgroundColor: alpha(theme.palette.background.paper, 0.8),
            border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`
          }}
        >
          <Box sx={{ mb: 2 }}>
            <Avatar
              sx={{
                width: 70,
                height: 70,
                margin: '0 auto',
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main
              }}
            >
              <DescriptionIcon sx={{ fontSize: 40 }} />
            </Avatar>
          </Box>
          <Typography variant="h6" gutterBottom>
            Aucun devis trouvé
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400, mx: 'auto' }}>
            Aucun devis ne correspond à vos critères de recherche. Essayez de modifier vos filtres ou créez un nouveau devis.
          </Typography>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateDevis}
            sx={{ borderRadius: 2 }}
          >
            Créer un devis
          </Button>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ bgcolor: alpha(theme.palette.error.main, 0.1), color: theme.palette.error.main, mr: 2 }}>
              <DeleteIcon />
            </Avatar>
            <Typography variant="h6">
              Confirmer la suppression
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mt: 1 }}>
            Êtes-vous sûr de vouloir supprimer le devis <strong>{selectedDevis?.numéro}</strong> ?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Cette action est irréversible et toutes les données associées seront définitivement supprimées.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setOpenDeleteDialog(false)}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 14px 0 rgba(244,67,54,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(244,67,54,0.23)'
              }
            }}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Email Dialog */}
      <EmailDialog
        open={openEmailDialog}
        onClose={handleCloseEmailDialog}
        onSend={handleSendEmail}
        documentType="devis"
        documentNumber={selectedDevis?.numéro}
        recipientEmail={selectedDevis?.clientId?.email}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{
            width: '100%',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Devis;