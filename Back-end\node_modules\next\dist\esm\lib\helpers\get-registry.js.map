{"version": 3, "sources": ["../../../src/lib/helpers/get-registry.ts"], "sourcesContent": ["import { execSync } from 'child_process'\nimport { getPkgManager } from './get-pkg-manager'\nimport { getFormattedNodeOptionsWithoutInspect } from '../../server/lib/utils'\n\n/**\n * Returns the package registry using the user's package manager.\n * The URL will have a trailing slash.\n * @default https://registry.npmjs.org/\n */\nexport function getRegistry(baseDir: string = process.cwd()) {\n  const pkgManager = getPkgManager(baseDir)\n  // Since `npm config` command fails in npm workspace to prevent workspace config conflicts,\n  // add `--no-workspaces` flag to run under the context of the root project only.\n  // Safe for non-workspace projects as it's equivalent to default `--workspaces=false`.\n  // x-ref: https://github.com/vercel/next.js/issues/47121#issuecomment-1499044345\n  // x-ref: https://github.com/npm/statusboard/issues/371#issue-920669998\n  const resolvedFlags = pkgManager === 'npm' ? '--no-workspaces' : ''\n  let registry = `https://registry.npmjs.org/`\n\n  try {\n    const output = execSync(\n      `${pkgManager} config get registry ${resolvedFlags}`,\n      {\n        env: {\n          ...process.env,\n          NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect(),\n        },\n      }\n    )\n      .toString()\n      .trim()\n\n    if (output.startsWith('http')) {\n      registry = output.endsWith('/') ? output : `${output}/`\n    }\n  } catch (err) {\n    throw new Error(`Failed to get registry from \"${pkgManager}\".`, {\n      cause: err,\n    })\n  }\n\n  return registry\n}\n"], "names": ["execSync", "getPkgManager", "getFormattedNodeOptionsWithoutInspect", "getRegistry", "baseDir", "process", "cwd", "pkgManager", "resolvedFlags", "registry", "output", "env", "NODE_OPTIONS", "toString", "trim", "startsWith", "endsWith", "err", "Error", "cause"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAe;AACxC,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,qCAAqC,QAAQ,yBAAwB;AAE9E;;;;CAIC,GACD,OAAO,SAASC,YAAYC,UAAkBC,QAAQC,GAAG,EAAE;IACzD,MAAMC,aAAaN,cAAcG;IACjC,2FAA2F;IAC3F,gFAAgF;IAChF,sFAAsF;IACtF,gFAAgF;IAChF,uEAAuE;IACvE,MAAMI,gBAAgBD,eAAe,QAAQ,oBAAoB;IACjE,IAAIE,WAAW,CAAC,2BAA2B,CAAC;IAE5C,IAAI;QACF,MAAMC,SAASV,SACb,GAAGO,WAAW,qBAAqB,EAAEC,eAAe,EACpD;YACEG,KAAK;gBACH,GAAGN,QAAQM,GAAG;gBACdC,cAAcV;YAChB;QACF,GAECW,QAAQ,GACRC,IAAI;QAEP,IAAIJ,OAAOK,UAAU,CAAC,SAAS;YAC7BN,WAAWC,OAAOM,QAAQ,CAAC,OAAON,SAAS,GAAGA,OAAO,CAAC,CAAC;QACzD;IACF,EAAE,OAAOO,KAAK;QACZ,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,6BAA6B,EAAEX,WAAW,EAAE,CAAC,EAAE;YAC9DY,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IAEA,OAAOR;AACT"}