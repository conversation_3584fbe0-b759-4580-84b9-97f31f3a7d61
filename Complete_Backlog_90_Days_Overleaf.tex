\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[french]{babel}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{colortbl}
\usepackage{array}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{hhline}

% Configuration de la page
\geometry{margin=1.5cm}

% Définition des couleurs
\definecolor{headerblue}{RGB}{52, 73, 94}
\definecolor{sprint1}{RGB}{174, 214, 241}
\definecolor{sprint2}{RGB}{212, 237, 218}
\definecolor{sprint3}{RGB}{253, 235, 208}
\definecolor{sprint4}{RGB}{255, 195, 195}
\definecolor{darkgreen}{RGB}{39, 174, 96}
\definecolor{orange}{RGB}{230, 126, 34}
\definecolor{red}{RGB}{231, 76, 60}
\definecolor{purple}{RGB}{142, 68, 173}
\definecolor{lightgray}{RGB}{236, 240, 241}

\begin{document}

\title{\textbf{\huge BACKLOG RÉALIGNÉ - 4 SPRINTS}\\
\Large Système de Gestion de Factures et Devis\\
\large Aligné avec les Diagrammes de Cas d'Utilisation}
\author{}
\date{}
\maketitle

\section*{Backlog Produit Réaligné - 4 Sprints}

\begin{longtable}{|>{\centering\arraybackslash}p{0.8cm}|p{3cm}|>{\centering\arraybackslash}p{1.2cm}|p{6.5cm}|>{\centering\arraybackslash}p{1.8cm}|>{\centering\arraybackslash}p{1.2cm}|}

\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} &
\textcolor{white}{\textbf{Thème}} &
\textcolor{white}{\textbf{Id Story}} &
\textcolor{white}{\textbf{User story}} &
\textcolor{white}{\textbf{Importance}} &
\textcolor{white}{\textbf{période(j)}} \\
\hline
\endfirsthead

\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{ID}} &
\textcolor{white}{\textbf{Thème}} &
\textcolor{white}{\textbf{Id Story}} &
\textcolor{white}{\textbf{User story}} &
\textcolor{white}{\textbf{Importance}} &
\textcolor{white}{\textbf{période(j)}} \\
\hline
\endhead

\textbf{1} & Authentification & \textbf{1.1} & En tant qu'administrateur, je souhaite pouvoir me connecter à l'application de manière sécurisée en utilisant mon adresse email et mon mot de passe & Élevée & \textbf{9j} \\
\hline

\textbf{1} & Authentification & \textbf{1.2} & En tant qu'utilisateur, je souhaite pouvoir réinitialiser mon mot de passe en cas d'oubli afin de me reconnecter facilement & Élevée & \textbf{7j} \\
\hline

\textbf{1} & Gestion Utilisateurs & \textbf{1.3} & En tant qu'administrateur, je souhaite gérer les comptes des entreprises et des vendeurs en leur attribuant des autorisations spécifiques & Élevée & \textbf{8j} \\
\hline

\textbf{1} & Inscription Client & \textbf{1.4} & En tant que client, je souhaite m'inscrire pour accéder au système & Élevée & \textbf{6j} \\
\hline

\textbf{2} & Création Documents & \textbf{2.1} & En tant que vendeur, j'aspire à générer des factures et des devis à travers une interface conviviale et intuitive & Élevée & \textbf{8j} \\
\hline

\textbf{2} & Envoi Documents & \textbf{2.2} & En tant que vendeur, je souhaite envoyer des devis et factures par email et permettre leur téléchargement en PDF & Élevée & \textbf{6j} \\
\hline

\textbf{2} & Acceptation Devis & \textbf{2.3} & En tant que client, je souhaite accepter ou refuser un devis & Élevée & \textbf{4j} \\
\hline

\textbf{2} & Gestion Produits & \textbf{2.4} & En tant que vendeur, je veux consulter le catalogue de produits et services & Élevée & \textbf{5j} \\
\hline

\textbf{2} & Gestion Clients & \textbf{2.5} & En tant qu'entreprise ou vendeur, je veux gérer les informations des clients & Élevée & \textbf{7j} \\
\hline

\textbf{3} & Gestion Clients & \textbf{3.1} & En tant qu'entreprise ou vendeur, je veux gérer les informations des clients & Élevée & \textbf{4j} \\
\hline

\textbf{3} & Profil Vendeur & \textbf{3.2} & En tant que vendeur, je souhaite gérer mon profil et modifier mes informations & Élevée & \textbf{5j} \\
\hline

\textbf{3} & Catalogue Produits & \textbf{3.3} & En tant qu'entreprise, je souhaite administrer un catalogue de produits et de services & Élevée & \textbf{5j} \\
\hline

\textbf{3} & Création Abonnements & \textbf{3.4} & En tant qu'administrateur, je souhaite créer des abonnements avec durées variables & Élevée & \textbf{5j} \\
\hline

\textbf{3} & Surveillance Abonnements & \textbf{3.5} & En tant qu'administrateur, je souhaite surveiller et bloquer automatiquement les comptes expirés & Élevée & \textbf{4j} \\
\hline

\textbf{3} & Gestion Paiements & \textbf{3.6} & En tant que responsable, je souhaite enregistrer les paiements reçus et suivre les factures payées/impayées & Élevée & \textbf{7j} \\
\hline

\textbf{4} & Gestion Paiements & \textbf{4.1} & En tant que responsable, je souhaite enregistrer les paiements reçus et suivre les factures payées/impayées (finalisation) & Élevée & \textbf{2j} \\
\hline

\textbf{4} & Configuration Système & \textbf{4.2} & En tant qu'administrateur, je souhaite configurer les options du système et gérer les paramètres de sécurité & Élevée & \textbf{4j} \\
\hline

\textbf{4} & Paramètres Entreprise & \textbf{4.3} & En tant qu'entreprise, je souhaite prendre en main la gestion des paramètres de mon activité, notamment en ce qui concerne mes coordonnées et les aspects fiscaux & Élevée & \textbf{4j} \\
\hline

\textbf{4} & Templates Documents & \textbf{4.4} & En tant qu'administrateur, je souhaite gérer des templates standard et moderne avec options configurables & Élevée & \textbf{4j} \\
\hline

\textbf{4} & Gestion Livreurs & \textbf{4.5} & En tant que responsable, je souhaite créer et gérer les livreurs et assigner des véhicules & Élevée & \textbf{2j} \\
\hline

\textbf{4} & Statistiques Livreurs & \textbf{4.6} & En tant que responsable, je souhaite suivre les statistiques et la disponibilité des livreurs & Élevée & \textbf{4j} \\
\hline

\textbf{4} & Bons de Livraison & \textbf{4.7} & En tant que responsable, je souhaite créer des bons de livraison et assigner des livreurs & Élevée & \textbf{2j} \\
\hline

\textbf{4} & Suivi Livraisons & \textbf{4.8} & En tant que responsable, je souhaite suivre le statut des livraisons et gérer les signatures & Élevée & \textbf{4j} \\
\hline

\end{longtable}

\newpage

\section*{Résumé par Sprint - Aligné avec les Diagrammes de Cas d'Utilisation}

\begin{table}[h!]
\centering
\begin{tabular}{|>{\centering\arraybackslash}p{1.8cm}|>{\centering\arraybackslash}p{3.5cm}|>{\centering\arraybackslash}p{1.5cm}|>{\centering\arraybackslash}p{1.5cm}|>{\centering\arraybackslash}p{1.5cm}|>{\centering\arraybackslash}p{1.5cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{Sprint}} &
\textcolor{white}{\textbf{Objectif Principal}} &
\textcolor{white}{\textbf{Stories}} &
\textcolor{white}{\textbf{Critique}} &
\textcolor{white}{\textbf{Élevée}} &
\textcolor{white}{\textbf{Durée}} \\
\hline
\textbf{Sprint 1} & Gestion des Utilisateurs & 4 & 0 & 4 & 30j \\
\hline
\textbf{Sprint 2} & Création de Documents & 5 & 0 & 5 & 30j \\
\hline
\textbf{Sprint 3} & Clients, Produits \& Abonnements & 6 & 0 & 6 & 30j \\
\hline
\textbf{Sprint 4} & Configuration \& Livraisons & 8 & 0 & 8 & 26j \\
\hline
\textbf{TOTAL} & \textbf{Système Complet} & \textbf{23} & \textbf{0} & \textbf{23} & \textbf{112j} \\
\hline
\end{tabular}
\caption{Répartition des stories par sprint et priorité - Version Réalignée}
\end{table}

\section*{Répartition des Efforts - Version Réalignée}

\begin{table}[h!]
\centering
\begin{tabular}{|>{\centering\arraybackslash}p{2.2cm}|>{\centering\arraybackslash}p{2.2cm}|>{\centering\arraybackslash}p{4cm}|>{\centering\arraybackslash}p{2.2cm}|}
\hline
\rowcolor{headerblue}
\textcolor{white}{\textbf{Sprint}} &
\textcolor{white}{\textbf{Durée}} &
\textcolor{white}{\textbf{Focus Principal}} &
\textcolor{white}{\textbf{Pourcentage}} \\
\hline
\textbf{Sprint 1} & 30 jours & Gestion des Utilisateurs & 26\% \\
\hline
\textbf{Sprint 2} & 30 jours & Création de Documents & 26\% \\
\hline
\textbf{Sprint 3} & 30 jours & Clients, Produits \& Abonnements & 26\% \\
\hline
\textbf{Sprint 4} & 26 jours & Configuration \& Livraisons & 22\% \\
\hline
\textbf{TOTAL} & \textbf{112 jours} & \textbf{Système Complet Aligné} & \textbf{100\%} \\
\hline
\end{tabular}
\caption{Répartition temporelle des sprints - Version Réalignée}
\end{table}

\section*{Objectifs par Sprint}

\subsection*{🎯 Sprint 1 - Gestion des Utilisateurs (Jours 1-30)}
\begin{itemize}
    \item[●] Authentification sécurisée pour administrateurs
    \item[●] Réinitialisation de mot de passe par OTP
    \item[●] Gestion des comptes entreprises et vendeurs
    \item[●] Inscription des clients
\end{itemize}

\subsection*{🎯 Sprint 2 - Création de Documents (Jours 31-60)}
\begin{itemize}
    \item[●] Interface de génération factures et devis
    \item[●] Envoi par email et téléchargement PDF
    \item[●] Acceptation/refus de devis par clients
    \item[●] Consultation catalogue produits et services
    \item[●] Gestion complète des informations clients
\end{itemize}

\subsection*{🎯 Sprint 3 - Clients, Produits \& Abonnements (Jours 61-90)}
\begin{itemize}
    \item[●] Gestion avancée des informations clients
    \item[●] Profil vendeur et modification informations
    \item[●] Administration catalogue produits/services
    \item[●] Création abonnements avec durées variables
    \item[●] Surveillance et blocage automatique comptes expirés
    \item[●] Enregistrement paiements et suivi factures
\end{itemize}

\subsection*{🎯 Sprint 4 - Configuration \& Livraisons (Jours 91-116)}
\begin{itemize}
    \item[●] Finalisation gestion des paiements
    \item[●] Configuration système et paramètres sécurité
    \item[●] Paramètres entreprise et aspects fiscaux
    \item[●] Templates standard/moderne configurables
    \item[●] Création et gestion des livreurs
    \item[●] Statistiques et disponibilité livreurs
    \item[●] Création bons de livraison et assignation
    \item[●] Suivi statut livraisons et signatures
\end{itemize}

\vspace{1cm}
\begin{center}
\colorbox{headerblue}{\textcolor{white}{\textbf{PROJET COMPLET : 112 JOURS | 23 STORIES | SYSTÈME OPÉRATIONNEL}}}
\end{center}

\end{document}
