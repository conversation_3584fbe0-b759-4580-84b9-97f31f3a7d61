{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.tsx"], "sourcesContent": ["import { useEffect, useTransition } from 'react'\nimport { devRenderIndicator } from './dev-render-indicator'\n\nexport const useSyncDevRenderIndicator = () => {\n  const [isPending, startTransition] = useTransition()\n\n  useEffect(() => {\n    if (isPending) {\n      devRenderIndicator.show()\n    } else {\n      devRenderIndicator.hide()\n    }\n  }, [isPending])\n\n  return startTransition\n}\n"], "names": ["useEffect", "useTransition", "devRenderIndicator", "useSyncDevRenderIndicator", "isPending", "startTransition", "show", "hide"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,QAAQ,QAAO;AAChD,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,OAAO,MAAMC,4BAA4B;IACvC,MAAM,CAACC,WAAWC,gBAAgB,GAAGJ;IAErCD,UAAU;QACR,IAAII,WAAW;YACbF,mBAAmBI,IAAI;QACzB,OAAO;YACLJ,mBAAmBK,IAAI;QACzB;IACF,GAAG;QAACH;KAAU;IAEd,OAAOC;AACT,EAAC"}