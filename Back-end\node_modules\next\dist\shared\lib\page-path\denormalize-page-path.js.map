{"version": 3, "sources": ["../../../../src/shared/lib/page-path/denormalize-page-path.ts"], "sourcesContent": ["import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n"], "names": ["denormalizePagePath", "page", "_page", "normalizePathSep", "startsWith", "isDynamicRoute", "slice"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;uBAXe;kCACE;AAU1B,SAASA,oBAAoBC,IAAY;IAC9C,IAAIC,QAAQC,IAAAA,kCAAgB,EAACF;IAC7B,OAAOC,MAAME,UAAU,CAAC,cAAc,CAACC,IAAAA,qBAAc,EAACH,SAClDA,MAAMI,KAAK,CAAC,KACZJ,UAAU,WACRA,QACA;AACR"}