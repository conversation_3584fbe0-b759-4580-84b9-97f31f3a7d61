'use strict';
require('../../modules/es.error.cause');
require('../../modules/es.error.to-string');
require('../../modules/es.object.to-string');
require('../../modules/esnext.suppressed-error.constructor');
require('../../modules/esnext.disposable-stack.constructor');
require('../../modules/esnext.iterator.dispose');
var path = require('../../internals/path');

module.exports = path.DisposableStack;
