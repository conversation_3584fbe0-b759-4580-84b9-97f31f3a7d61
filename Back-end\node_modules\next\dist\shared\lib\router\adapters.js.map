{"version": 3, "sources": ["../../../../src/shared/lib/router/adapters.tsx"], "sourcesContent": ["import type { AppRouterInstance } from '../app-router-context.shared-runtime'\nimport type { Params } from '../../../server/request/params'\nimport type { NextRouter } from './router'\n\nimport React, { useMemo, useRef } from 'react'\nimport { PathnameContext } from '../hooks-client-context.shared-runtime'\nimport { isDynamicRoute } from './utils'\nimport { asPathToSearchParams } from './utils/as-path-to-search-params'\nimport { getRouteRegex } from './utils/route-regex'\n\n/** It adapts a Pages Router (`NextRouter`) to the App Router Instance. */\nexport function adaptForAppRouterInstance(\n  pagesRouter: NextRouter\n): AppRouterInstance {\n  return {\n    back() {\n      pagesRouter.back()\n    },\n    forward() {\n      pagesRouter.forward()\n    },\n    refresh() {\n      pagesRouter.reload()\n    },\n    hmrRefresh() {},\n    push(href, { scroll } = {}) {\n      void pagesRouter.push(href, undefined, { scroll })\n    },\n    replace(href, { scroll } = {}) {\n      void pagesRouter.replace(href, undefined, { scroll })\n    },\n    prefetch(href) {\n      void pagesRouter.prefetch(href)\n    },\n  }\n}\n\n/**\n * adaptForSearchParams transforms the ParsedURLQuery into URLSearchParams.\n *\n * @param router the router that contains the query.\n * @returns the search params in the URLSearchParams format\n */\nexport function adaptForSearchParams(\n  router: Pick<NextRouter, 'isReady' | 'query' | 'asPath'>\n): URLSearchParams {\n  if (!router.isReady || !router.query) {\n    return new URLSearchParams()\n  }\n\n  return asPathToSearchParams(router.asPath)\n}\n\nexport function adaptForPathParams(\n  router: Pick<NextRouter, 'isReady' | 'pathname' | 'query' | 'asPath'>\n): Params | null {\n  if (!router.isReady || !router.query) {\n    return null\n  }\n  const pathParams: Params = {}\n  const routeRegex = getRouteRegex(router.pathname)\n  const keys = Object.keys(routeRegex.groups)\n  for (const key of keys) {\n    pathParams[key] = router.query[key]!\n  }\n  return pathParams\n}\n\nexport function PathnameContextProviderAdapter({\n  children,\n  router,\n  ...props\n}: React.PropsWithChildren<{\n  router: Pick<NextRouter, 'pathname' | 'asPath' | 'isReady' | 'isFallback'>\n  isAutoExport: boolean\n}>) {\n  const ref = useRef(props.isAutoExport)\n  const value = useMemo(() => {\n    // isAutoExport is only ever `true` on the first render from the server,\n    // so reset it to `false` after we read it for the first time as `true`. If\n    // we don't use the value, then we don't need it.\n    const isAutoExport = ref.current\n    if (isAutoExport) {\n      ref.current = false\n    }\n\n    // When the route is a dynamic route, we need to do more processing to\n    // determine if we need to stop showing the pathname.\n    if (isDynamicRoute(router.pathname)) {\n      // When the router is rendering the fallback page, it can't possibly know\n      // the path, so return `null` here. Read more about fallback pages over\n      // at:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-static-paths#fallback-pages\n      if (router.isFallback) {\n        return null\n      }\n\n      // When `isAutoExport` is true, meaning this is a page page has been\n      // automatically statically optimized, and the router is not ready, then\n      // we can't know the pathname yet. Read more about automatic static\n      // optimization at:\n      // https://nextjs.org/docs/advanced-features/automatic-static-optimization\n      if (isAutoExport && !router.isReady) {\n        return null\n      }\n    }\n\n    // The `router.asPath` contains the pathname seen by the browser (including\n    // any query strings), so it should have that stripped. Read more about the\n    // `asPath` option over at:\n    // https://nextjs.org/docs/api-reference/next/router#router-object\n    let url: URL\n    try {\n      url = new URL(router.asPath, 'http://f')\n    } catch (_) {\n      // fallback to / for invalid asPath values e.g. //\n      return '/'\n    }\n\n    return url.pathname\n  }, [router.asPath, router.isFallback, router.isReady, router.pathname])\n\n  return (\n    <PathnameContext.Provider value={value}>\n      {children}\n    </PathnameContext.Provider>\n  )\n}\n"], "names": ["PathnameContextProviderAdapter", "adaptForAppRouterInstance", "adaptForPathParams", "adaptForSearchParams", "pagesRouter", "back", "forward", "refresh", "reload", "hmrRefresh", "push", "href", "scroll", "undefined", "replace", "prefetch", "router", "isReady", "query", "URLSearchParams", "asPathToSearchParams", "<PERSON><PERSON><PERSON>", "pathParams", "routeRegex", "getRouteRegex", "pathname", "keys", "Object", "groups", "key", "children", "props", "ref", "useRef", "isAutoExport", "value", "useMemo", "current", "isDynamicRoute", "<PERSON><PERSON><PERSON><PERSON>", "url", "URL", "_", "PathnameContext", "Provider"], "mappings": ";;;;;;;;;;;;;;;;;IAoEgBA,8BAA8B;eAA9BA;;IAzDAC,yBAAyB;eAAzBA;;IA0CAC,kBAAkB;eAAlBA;;IAVAC,oBAAoB;eAApBA;;;;;iEAvCuB;iDACP;uBACD;sCACM;4BACP;AAGvB,SAASF,0BACdG,WAAuB;IAEvB,OAAO;QACLC;YACED,YAAYC,IAAI;QAClB;QACAC;YACEF,YAAYE,OAAO;QACrB;QACAC;YACEH,YAAYI,MAAM;QACpB;QACAC,eAAc;QACdC,MAAKC,IAAI,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAE,GAAV,mBAAa,CAAC,IAAd;YACT,KAAKR,YAAYM,IAAI,CAACC,MAAME,WAAW;gBAAED;YAAO;QAClD;QACAE,SAAQH,IAAI,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAE,GAAV,mBAAa,CAAC,IAAd;YACZ,KAAKR,YAAYU,OAAO,CAACH,MAAME,WAAW;gBAAED;YAAO;QACrD;QACAG,UAASJ,IAAI;YACX,KAAKP,YAAYW,QAAQ,CAACJ;QAC5B;IACF;AACF;AAQO,SAASR,qBACda,MAAwD;IAExD,IAAI,CAACA,OAAOC,OAAO,IAAI,CAACD,OAAOE,KAAK,EAAE;QACpC,OAAO,IAAIC;IACb;IAEA,OAAOC,IAAAA,0CAAoB,EAACJ,OAAOK,MAAM;AAC3C;AAEO,SAASnB,mBACdc,MAAqE;IAErE,IAAI,CAACA,OAAOC,OAAO,IAAI,CAACD,OAAOE,KAAK,EAAE;QACpC,OAAO;IACT;IACA,MAAMI,aAAqB,CAAC;IAC5B,MAAMC,aAAaC,IAAAA,yBAAa,EAACR,OAAOS,QAAQ;IAChD,MAAMC,OAAOC,OAAOD,IAAI,CAACH,WAAWK,MAAM;IAC1C,KAAK,MAAMC,OAAOH,KAAM;QACtBJ,UAAU,CAACO,IAAI,GAAGb,OAAOE,KAAK,CAACW,IAAI;IACrC;IACA,OAAOP;AACT;AAEO,SAAStB,+BAA+B,KAO7C;IAP6C,IAAA,EAC7C8B,QAAQ,EACRd,MAAM,EACN,GAAGe,OAIH,GAP6C;IAQ7C,MAAMC,MAAMC,IAAAA,aAAM,EAACF,MAAMG,YAAY;IACrC,MAAMC,QAAQC,IAAAA,cAAO,EAAC;QACpB,wEAAwE;QACxE,2EAA2E;QAC3E,iDAAiD;QACjD,MAAMF,eAAeF,IAAIK,OAAO;QAChC,IAAIH,cAAc;YAChBF,IAAIK,OAAO,GAAG;QAChB;QAEA,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC,IAAAA,qBAAc,EAACtB,OAAOS,QAAQ,GAAG;YACnC,yEAAyE;YACzE,uEAAuE;YACvE,MAAM;YACN,sFAAsF;YACtF,IAAIT,OAAOuB,UAAU,EAAE;gBACrB,OAAO;YACT;YAEA,oEAAoE;YACpE,wEAAwE;YACxE,mEAAmE;YACnE,mBAAmB;YACnB,0EAA0E;YAC1E,IAAIL,gBAAgB,CAAClB,OAAOC,OAAO,EAAE;gBACnC,OAAO;YACT;QACF;QAEA,2EAA2E;QAC3E,2EAA2E;QAC3E,2BAA2B;QAC3B,kEAAkE;QAClE,IAAIuB;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIzB,OAAOK,MAAM,EAAE;QAC/B,EAAE,OAAOqB,GAAG;YACV,kDAAkD;YAClD,OAAO;QACT;QAEA,OAAOF,IAAIf,QAAQ;IACrB,GAAG;QAACT,OAAOK,MAAM;QAAEL,OAAOuB,UAAU;QAAEvB,OAAOC,OAAO;QAAED,OAAOS,QAAQ;KAAC;IAEtE,qBACE,qBAACkB,gDAAe,CAACC,QAAQ;QAACT,OAAOA;kBAC9BL;;AAGP"}