{"version": 3, "sources": ["../../../src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["ACTION_HEADER", "FLIGHT_HEADERS", "NEXT_DID_POSTPONE_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_HMR_REFRESH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IACaA,aAAa;eAAbA;;IAiBAC,cAAc;eAAdA;;IAWAC,wBAAwB;eAAxBA;;IAfAC,4BAA4B;eAA5BA;;IADAC,uBAAuB;eAAvBA;;IAmBAC,wBAAwB;eAAxBA;;IAFAC,0BAA0B;eAA1BA;;IACAC,2BAA2B;eAA3BA;;IAzBAC,2BAA2B;eAA3BA;;IAKAC,mCAAmC;eAAnCA;;IAiBAC,6BAA6B;eAA7BA;;IAvBAC,6BAA6B;eAA7BA;;IAqBAC,oBAAoB;eAApBA;;IAXAC,QAAQ;eAARA;;IACAC,uBAAuB;eAAvBA;;IAhBAC,UAAU;eAAVA;;;AAAN,MAAMA,aAAa;AACnB,MAAMf,gBAAgB;AAItB,MAAMW,gCAAgC;AACtC,MAAMH,8BAA8B;AAKpC,MAAMC,sCACX;AACK,MAAML,0BAA0B;AAChC,MAAMD,+BAA+B;AACrC,MAAMU,WAAW;AACjB,MAAMC,0BAA0B;AAEhC,MAAMb,iBAAiB;IAC5Bc;IACAJ;IACAH;IACAJ;IACAK;CACD;AAEM,MAAMG,uBAAuB;AAE7B,MAAMF,gCAAgC;AACtC,MAAMR,2BAA2B;AACjC,MAAMI,6BAA6B;AACnC,MAAMC,8BAA8B;AACpC,MAAMF,2BAA2B"}