/**
 * Server startup script with memory optimization
 * 
 * This script starts the server with increased memory allocation
 * to prevent "JavaScript heap out of memory" errors.
 */

const { spawn } = require('child_process');
const path = require('path');

// Configuration
const NODE_MEMORY = 8192; // 8GB memory allocation
const SERVER_FILE = path.join(__dirname, 'server.js');

console.log(`Starting server with ${NODE_MEMORY}MB memory allocation...`);

// Spawn the server process with increased memory
const serverProcess = spawn('node', [
  `--max-old-space-size=${NODE_MEMORY}`,
  SERVER_FILE
], {
  stdio: 'inherit'
});

// Handle process events
serverProcess.on('error', (err) => {
  console.error('Failed to start server process:', err);
});

serverProcess.on('close', (code) => {
  if (code !== 0) {
    console.log(`Server process exited with code ${code}`);
  }
});

// Handle termination signals
process.on('SIGINT', () => {
  console.log('Received SIGINT. Shutting down server...');
  serverProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM. Shutting down server...');
  serverProcess.kill('SIGTERM');
});
