import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  IconButton,
  Box,
  Tab,
  Tabs,
  Card,
  CardMedia,
  InputAdornment,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  PhotoCamera as PhotoCameraIcon,
} from '@mui/icons-material';
import axios from 'axios';

const categories = [
  'Service',
  'Produit physique',
  'Logiciel',
  'Abonnement',
  'Formation',
  'Non classé'
];

const ProduitForm = ({ open, onClose, produit, onSave }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [imagePreview, setImagePreview] = useState(null);
  const [currencySymbol, setCurrencySymbol] = useState('TND');

  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    descriptionDetaille: '',
    prix: 0,
    category: 'Non classé',
    unite: 'unité',
    gestionStock: false,
    quantiteStock: 0,
    seuilAlerte: 5,
    image: null,
    imageUrl: '',
  });

  // Initialize from existing product and get currency settings
  useEffect(() => {
    // Reset form data when produit changes
    if (produit) {
      setFormData({
        nom: produit.nom || '',
        description: produit.description || '',
        descriptionDetaille: produit.descriptionDetaille || '',
        prix: produit.prix || 0,
        category: produit.category || 'Non classé',
        unite: produit.unite || 'unité',
        gestionStock: produit.gestionStock || false,
        quantiteStock: produit.quantiteStock || 0,
        seuilAlerte: produit.seuilAlerte || 5,
        image: null,
        imageUrl: produit.imageUrl || '',
      });

      // Set image preview if product has an image
      if (produit.imageUrl) {
        setImagePreview(produit.imageUrl);
      } else {
        setImagePreview(null);
      }
    } else {
      // Reset form for new product
      setFormData({
        nom: '',
        description: '',
        descriptionDetaille: '',
        prix: 0,
        category: 'Non classé',
        unite: 'unité',
        gestionStock: false,
        quantiteStock: 0,
        seuilAlerte: 5,
        image: null,
        imageUrl: '',
      });
      setImagePreview(null);
    }

    // Get currency settings from admin parameters
    const fetchCurrencySettings = async () => {
      try {
        const response = await axios.get('/api/parametres');
        if (response.data && response.data.defaultCurrency) {
          // Set currency symbol based on admin settings
          switch(response.data.defaultCurrency) {
            case 'EUR':
              setCurrencySymbol('€');
              break;
            case 'USD':
              setCurrencySymbol('$');
              break;
            case 'TND':
              setCurrencySymbol('TND');
              break;
            default:
              setCurrencySymbol('TND');
          }
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des paramètres:', error);
        // Default to TND if there's an error
        setCurrencySymbol('TND');
      }
    };

    fetchCurrencySettings();
  }, [produit]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({ ...prev, image: file }));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSubmit = () => {
    const finalData = {
      ...formData,
      prix: parseFloat(formData.prix),
    };

    onSave(finalData);
    onClose();
  };

  // Reset form when dialog is closed
  const handleClose = () => {
    setActiveTab(0); // Reset to first tab
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {produit ? 'Modifier Produit' : 'Nouveau Produit'}
      </DialogTitle>

      <DialogContent>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth" sx={{ mb: 2 }}>
          <Tab label="Informations générales" />
          <Tab label="Description détaillée" />
          <Tab label="Gestion des stocks" />
        </Tabs>

        {/* Tab 1: General Information */}
        {activeTab === 0 && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={8}>
              <TextField
                name="nom"
                label="Nom du produit"
                value={formData.nom}
                onChange={handleChange}
                fullWidth
                required
                margin="normal"
              />

              <TextField
                name="description"
                label="Description courte"
                value={formData.description}
                onChange={handleChange}
                fullWidth
                multiline
                rows={3}
                margin="normal"
                helperText="Brève description pour les listes et aperçus"
              />

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Catégorie</InputLabel>
                    <Select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      label="Catégorie"
                    >
                      {categories.map((cat) => (
                        <MenuItem key={cat} value={cat}>
                          {cat}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="prix"
                    label="Prix unitaire"
                    type="number"
                    value={formData.prix}
                    onChange={handleChange}
                    fullWidth
                    required
                    margin="normal"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">{currencySymbol}</InputAdornment>,
                      inputProps: { min: 0, step: 1 }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="unite"
                    label="Unité"
                    value={formData.unite}
                    onChange={handleChange}
                    fullWidth
                    margin="normal"
                    placeholder="Ex: unité, heure, kg, m²..."
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ mb: 2, height: 200, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {imagePreview ? (
                  <CardMedia
                    component="img"
                    image={imagePreview}
                    alt={formData.nom}
                    sx={{ height: '100%', objectFit: 'contain' }}
                  />
                ) : (
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <PhotoCameraIcon sx={{ fontSize: 60, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      Aucune image
                    </Typography>
                  </Box>
                )}
              </Card>

              <Button
                variant="outlined"
                component="label"
                startIcon={<PhotoCameraIcon />}
                fullWidth
              >
                {imagePreview ? 'Changer l\'image' : 'Ajouter une image'}
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={handleImageChange}
                />
              </Button>
            </Grid>
          </Grid>
        )}

        {/* Tab 2: Detailed Description */}
        {activeTab === 1 && (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                name="descriptionDetaille"
                label="Description détaillée"
                value={formData.descriptionDetaille}
                onChange={handleChange}
                fullWidth
                multiline
                rows={10}
                margin="normal"
                helperText="Description complète du produit ou service avec toutes les caractéristiques"
              />
            </Grid>
          </Grid>
        )}

        {/* Tab 3: Stock Management */}
        {activeTab === 2 && (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Gestion des stocks
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={formData.gestionStock}
                    onChange={handleChange}
                    name="gestionStock"
                    color="primary"
                  />
                }
                label="Activer la gestion des stocks"
                sx={{ mb: 2 }}
              />

              {formData.gestionStock && (
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      name="quantiteStock"
                      label="Quantité en stock"
                      type="number"
                      value={formData.quantiteStock}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      InputProps={{
                        inputProps: { min: 0, step: 1 }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      name="seuilAlerte"
                      label="Seuil d'alerte"
                      type="number"
                      value={formData.seuilAlerte}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      helperText="Vous serez alerté quand le stock passe sous ce seuil"
                      InputProps={{
                        inputProps: { min: 0, step: 1 }
                      }}
                    />
                  </Grid>
                </Grid>
              )}
            </Grid>
          </Grid>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>Annuler</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={!formData.nom || formData.prix <= 0}
        >
          Enregistrer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProduitForm;