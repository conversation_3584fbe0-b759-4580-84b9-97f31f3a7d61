import axios from 'axios';

const API_URL = 'http://localhost:5000/api';

// Get auth token from localStorage
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Configure axios with auth token
const axiosWithAuth = () => {
  const token = getAuthToken();
  return axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    },
  });
};

// Get all users
export const getAllUsers = async () => {
  try {
    const response = await axiosWithAuth().get('/user/users');
    return response.data;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

// Get users by role
export const getUsersByRole = async (role) => {
  try {
    // Si on cherche des responsables d'entreprise et qu'on est sur la page d'inscription (pas de token)
    if (role === 'RESPONSABLE' && !localStorage.getItem('token')) {
      return await getPublicEntreprises();
    }

    const allUsers = await getAllUsers();
    const filteredUsers = allUsers.filter(user => user.role === role);

    // Ajouter le nombre de factures et devis pour chaque utilisateur
    if (role === 'RESPONSABLE' || role === 'VENDEUR') {
      try {
        // Récupérer toutes les factures et devis
        const factureResponse = await axiosWithAuth().get('/factures');
        const devisResponse = await axiosWithAuth().get('/devis');

        const factures = factureResponse.data || [];
        const devis = devisResponse.data || [];

        // Compter les factures et devis pour chaque utilisateur
        return filteredUsers.map(user => {
          const userId = user._id;

          // Compter les factures créées par cet utilisateur
          const userFactures = factures.filter(facture => {
            if (role === 'RESPONSABLE') {
              // Pour les responsables, compter les factures créées par les vendeurs associés
              return facture.responsableId === userId ||
                     (facture.vendeurId && user.vendeurs && user.vendeurs.includes(facture.vendeurId));
            } else {
              // Pour les vendeurs, compter les factures qu'ils ont créées
              return facture.vendeurId === userId;
            }
          });

          // Compter les devis créés par cet utilisateur
          const userDevis = devis.filter(devis => {
            if (role === 'RESPONSABLE') {
              // Pour les responsables, compter les devis créés par les vendeurs associés
              return devis.responsableId === userId ||
                     (devis.vendeurId && user.vendeurs && user.vendeurs.includes(devis.vendeurId));
            } else {
              // Pour les vendeurs, compter les devis qu'ils ont créés
              return devis.vendeurId === userId;
            }
          });

          return {
            ...user,
            factures: userFactures.length,
            devis: userDevis.length
          };
        });
      } catch (error) {
        console.error('Error fetching factures and devis:', error);
        // En cas d'erreur, retourner les utilisateurs sans les compteurs
        return filteredUsers;
      }
    }

    return filteredUsers;
  } catch (error) {
    console.error(`Error fetching ${role} users:`, error);

    // Si on cherche des responsables et qu'on a une erreur 401, essayer l'endpoint public
    if (role === 'RESPONSABLE' && error.response && error.response.status === 401) {
      try {
        return await getPublicEntreprises();
      } catch (publicError) {
        console.error('Error fetching public responsables:', publicError);
        return []; // Retourner un tableau vide en cas d'erreur
      }
    }

    return []; // Retourner un tableau vide en cas d'erreur
  }
};

// Get public responsables d'entreprise (without authentication)
export const getPublicEntreprises = async () => {
  try {
    console.log('Fetching public responsables for signup');
    const response = await axios.get(`${API_URL}/user/public/responsables`);
    return response.data;
  } catch (error) {
    console.error('Error fetching public responsables:', error);
    return []; // Retourner un tableau vide en cas d'erreur
  }
};

// Get vendeurs by responsable d'entreprise
export const getVendeursByEntreprise = async (responsableId) => {
  try {
    if (!responsableId) {
      console.error('No responsableId provided to getVendeursByEntreprise');
      return [];
    }

    console.log('Fetching vendeurs for responsable:', responsableId);

    // Get all users directly from the API to ensure we have the latest data
    const response = await axiosWithAuth().get('/user/users');
    const allUsers = response.data;
    console.log('All users from API:', allUsers.length);

    // Debug: Log all vendeurs and their responsables
    const allVendeurs = allUsers.filter(user => user.role === 'VENDEUR');
    console.log('All vendeurs from API:', allVendeurs.length);
    allVendeurs.forEach(v => {
      const vendeurId = v._id || v.id;
      console.log(`Vendeur ${v.nom} (${vendeurId}) has responsables:`, v.responsables);
    });

    // Filter vendeurs associated with this responsable
    const vendeurs = allUsers.filter(user => {
      const isVendeur = user.role === 'VENDEUR';

      // Check if user has responsables array
      if (!isVendeur) {
        return false;
      }

      // Convert responsables to string for comparison if it's an array
      const responsablesArray = Array.isArray(user.responsables) ? user.responsables : (user.responsables ? [user.responsables] : []);

      // Check if any responsable ID matches
      const isAssociated = responsablesArray.some(id => {
        if (!id) return false;

        // Handle both string and object IDs
        const responsableIdStr = typeof responsableId === 'object' ? responsableId.toString() : responsableId;
        const idStr = typeof id === 'object' ? id.toString() : id;

        // Also check if the id matches the responsableId without the 'new' prefix that MongoDB sometimes adds
        const cleanResponsableId = responsableIdStr.replace(/^new /, '');
        const cleanIdStr = idStr.replace(/^new /, '');

        console.log(`Comparing: ${idStr} with ${responsableIdStr}`);
        return idStr === responsableIdStr || cleanIdStr === cleanResponsableId;
      });

      console.log(`Vendeur ${user.nom}: isVendeur=${isVendeur}, hasResponsables=${!!user.responsables}, isAssociated=${isAssociated}`);

      return isVendeur && isAssociated;
    });

    console.log('Found vendeurs for responsable:', vendeurs.length);
    vendeurs.forEach(v => {
      const vendeurId = v._id || v.id;
      console.log(`- ${v.nom} (${vendeurId})`);
    });

    return vendeurs;
  } catch (error) {
    console.error('Error fetching vendeurs by responsable:', error);
    throw error;
  }
};

// Get user by ID
export const getUserById = async (id) => {
  try {
    const response = await axiosWithAuth().get(`/user/users/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

// Get user by ID with password (admin only)
export const getUserByIdWithPassword = async (id) => {
  try {
    const response = await axiosWithAuth().get(`/user/users/${id}/with-password`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user with password:', error);
    throw error;
  }
};

// Create a new user
export const createUser = async (userData) => {
  try {
    // Clean up the userData to ensure responsables is valid
    const cleanedUserData = { ...userData };

    // Handle responsables array - ensure it's a valid array with no null values
    if (cleanedUserData.responsables) {
      if (Array.isArray(cleanedUserData.responsables)) {
        // Filter out null or undefined values
        cleanedUserData.responsables = cleanedUserData.responsables.filter(id => id);
      } else if (cleanedUserData.responsables) {
        // If it's a single non-null value, convert to array
        cleanedUserData.responsables = [cleanedUserData.responsables];
      } else {
        // If it's null or undefined, set to empty array
        cleanedUserData.responsables = [];
      }
    } else {
      cleanedUserData.responsables = [];
    }

    console.log('Creating user with cleaned data:', cleanedUserData);
    const response = await axiosWithAuth().post('/user/users', cleanedUserData);
    return response.data;
  } catch (error) {
    console.error('Error creating user:', error);
    // Add more detailed error information
    if (error.response && error.response.data) {
      throw {
        ...error,
        message: error.response.data.error || error.message
      };
    }
    throw error;
  }
};

// Update a user
export const updateUser = async (id, userData) => {
  try {
    // Clean up the userData to ensure responsables is valid
    const cleanedUserData = { ...userData };

    // Handle responsables array - ensure it's a valid array with no null values
    if (cleanedUserData.responsables) {
      if (Array.isArray(cleanedUserData.responsables)) {
        // Filter out null or undefined values
        cleanedUserData.responsables = cleanedUserData.responsables.filter(id => id);
      } else if (cleanedUserData.responsables) {
        // If it's a single non-null value, convert to array
        cleanedUserData.responsables = [cleanedUserData.responsables];
      } else {
        // If it's null or undefined, set to empty array
        cleanedUserData.responsables = [];
      }
    } else {
      cleanedUserData.responsables = [];
    }

    console.log(`Updating user ${id} with cleaned data:`, cleanedUserData);
    const response = await axiosWithAuth().put(`/user/users/${id}`, cleanedUserData);
    return response.data;
  } catch (error) {
    console.error('Error updating user:', error);
    // Add more detailed error information
    if (error.response && error.response.data) {
      throw {
        ...error,
        message: error.response.data.error || error.message
      };
    }
    throw error;
  }
};

// Delete a user
export const deleteUser = async (id) => {
  try {
    await axiosWithAuth().delete(`/user/users/${id}`);
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

// Upload user profile image
export const uploadProfileImage = async (id, formData) => {
  try {
    const response = await axiosWithAuth().post(`/profile/upload/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error uploading profile image:', error);
    throw error;
  }
};

export default {
  getAllUsers,
  getUsersByRole,
  getVendeursByEntreprise,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  uploadProfileImage
};
