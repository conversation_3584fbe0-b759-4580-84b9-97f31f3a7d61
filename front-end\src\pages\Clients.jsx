import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  Dialog,
  Avatar,
  Tooltip,
  Grid,
  Menu,
  MenuItem,
  ListItemIcon,
  Tabs,
  Tab,
  Chip,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Snackbar,
  Alert,
  Divider,
  Card,
  CardContent,
  GlobalStyles,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Delete as DeleteOutlineIcon,
  Close as CloseIcon,
  Info as InfoIcon,
  Business as BusinessIcon,
  Category as CategoryIcon,
  Person as PersonIcon,
} from "@mui/icons-material";
import ClientForm from "../components/ClientForm";
import ClientCard from "../components/ClientCard";
import ClientDetail from "../components/ClientDetail";
import clientService from "../services/clientService";
import userService from "../services/userService";
import { formatDate, formatCurrency } from "../utils/formatters";
import { useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

const Clients = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // State for clients and filtering
  const [clients, setClients] = useState([]);
  const [clientsWithStats, setClientsWithStats] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'grid'
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [vendeurFilter, setVendeurFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [vendeurs, setVendeurs] = useState([]);

  // State for dialogs and menus
  const [openFormDialog, setOpenFormDialog] = useState(false);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentClient, setCurrentClient] = useState(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useState(null);
  const [sortMenuAnchorEl, setSortMenuAnchorEl] = useState(null);

  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const location = useLocation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // Déterminer le préfixe de route en fonction du rôle de l'utilisateur
  const getRoutePrefix = () => {
    if (!currentUser) return '/';

    switch (currentUser.role) {
      case 'ADMIN':
        return '/admin';
      case 'VENDEUR':
        return '/vendeur';
      case 'ENTREPRISE':
        return '/entreprise';
      default:
        return '/client';
    }
  };

  useEffect(() => {
    fetchClients();
    fetchVendeurs();
  }, []);

  useEffect(() => {
    applyFiltersAndSort();
  }, [clientsWithStats, searchTerm, categoryFilter, statusFilter, vendeurFilter, sortBy]);

  // Fetch vendeurs associated with the current entreprise
  const fetchVendeurs = async () => {
    if (currentUser && (currentUser.role === 'ENTREPRISE' || currentUser.role === 'ADMIN')) {
      try {
        const entrepriseId = currentUser._id || currentUser.id;
        const vendeursList = await userService.getVendeursByEntreprise(entrepriseId);
        setVendeurs(vendeursList);
      } catch (error) {
        console.error('Error fetching vendeurs:', error);
      }
    }
  };

  useEffect(() => {
    // Check for ?new=true in URL
    const params = new URLSearchParams(location.search);
    if (params.get("new") === "true") {
      handleOpenFormDialog();
      // Clear query param after opening dialog
      const routePrefix = getRoutePrefix();
      navigate(`${routePrefix}/clients`, { replace: true });
    }
  }, [location, navigate]);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const data = await clientService.getClients();
      setClients(data);

      // Fetch stats for each client
      const clientsWithStatsData = await Promise.all(
        data.map(async (client) => {
          try {
            const stats = await clientService.getClientStats(client._id);
            return { ...client, stats };
          } catch (error) {
            console.error(`Error fetching stats for client ${client._id}:`, error);
            return client;
          }
        })
      );

      setClientsWithStats(clientsWithStatsData);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching clients:", error);
      setLoading(false);
      setSnackbar({
        open: true,
        message: 'Erreur lors du chargement des clients',
        severity: 'error'
      });
    }
  };

  const applyFiltersAndSort = () => {
    let filtered = [...clientsWithStats];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (client) =>
          client.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
          client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          client.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (client.secteurActivite && client.secteurActivite.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(client => client.categorie === categoryFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      const isActive = statusFilter === 'active';
      filtered = filtered.filter(client =>
        client.actif === isActive
      );
    }

    // Apply vendeur filter
    if (vendeurFilter !== 'all') {
      filtered = filtered.filter(client =>
        client.vendeurId === vendeurFilter
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.nom.localeCompare(b.nom);
        case 'date':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'category':
          return a.categorie?.localeCompare(b.categorie || 'Standard');
        case 'revenue':
          const aRevenue = a.stats?.montantTotal || 0;
          const bRevenue = b.stats?.montantTotal || 0;
          return bRevenue - aRevenue;
        default:
          return 0;
      }
    });

    setFilteredClients(filtered);
  };

  // Dialog handlers
  const handleOpenFormDialog = (client = null) => {
    setCurrentClient(client);
    setOpenFormDialog(true);
  };

  const handleCloseFormDialog = () => {
    setOpenFormDialog(false);
    setCurrentClient(null);
  };

  const handleOpenDetailDialog = (client) => {
    setCurrentClient(client);
    setOpenDetailDialog(true);
  };

  const handleCloseDetailDialog = () => {
    setOpenDetailDialog(false);
    setCurrentClient(null);
  };

  const handleOpenDeleteDialog = (client) => {
    setCurrentClient(client);
    setOpenDeleteDialog(true);
    handleCloseMenu();
  };

  // Fonction pour supprimer directement un client
  const handleDeleteClient = (client) => {
    setCurrentClient(client);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  // Menu handlers
  const handleOpenMenu = (event, client) => {
    setCurrentClient(client);
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
  };

  const handleOpenFilterMenu = (event) => {
    setFilterMenuAnchorEl(event.currentTarget);
  };

  const handleCloseFilterMenu = () => {
    setFilterMenuAnchorEl(null);
  };

  const handleOpenSortMenu = (event) => {
    setSortMenuAnchorEl(event.currentTarget);
  };

  const handleCloseSortMenu = () => {
    setSortMenuAnchorEl(null);
  };

  // Snackbar handlers
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // CRUD operations
  const handleSave = async (clientData) => {
    try {
      let savedClient;

      if (currentClient && currentClient._id) {
        savedClient = await clientService.updateClient(currentClient._id, clientData);
        setSnackbar({
          open: true,
          message: 'Client modifié avec succès',
          severity: 'success'
        });
      } else {
        savedClient = await clientService.createClient(clientData);
        setSnackbar({
          open: true,
          message: 'Client créé avec succès',
          severity: 'success'
        });
      }

      await fetchClients();
      return savedClient;
    } catch (error) {
      console.error("Error saving client:", error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'enregistrement du client',
        severity: 'error'
      });
      return null;
    }
  };

  const handleUploadLogo = async (clientId, logoFile) => {
    try {
      await clientService.uploadClientLogo(clientId, logoFile);
      await fetchClients();
    } catch (error) {
      console.error("Error uploading logo:", error);
      setSnackbar({
        open: true,
        message: 'Erreur lors du téléchargement du logo',
        severity: 'error'
      });
    }
  };

  const handleDelete = async () => {
    if (!currentClient) return;

    try {
      await clientService.deleteClient(currentClient._id);
      await fetchClients();
      handleCloseDeleteDialog();
      setSnackbar({
        open: true,
        message: 'Client supprimé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error("Error deleting client:", error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la suppression du client',
        severity: 'error'
      });
    }
  };

  const generateAvatar = (name) => {
    const initials = name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
    return (
      <Avatar sx={{ bgcolor: theme.palette.primary.main, width: 32, height: 32, fontSize: 14 }}>
        {initials}
      </Avatar>
    );
  };

  // Global styles for animations
  const globalStyles = (
    <GlobalStyles
      styles={{
        '@keyframes fadeIn': {
          '0%': {
            opacity: 0,
            transform: 'translateY(10px)'
          },
          '100%': {
            opacity: 1,
            transform: 'translateY(0)'
          },
        },
      }}
    />
  );

  return (
    <Box sx={{ p: 3 }}>
      {globalStyles}
      {/* Header Card */}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2,
          background: 'white',
          position: 'relative',
          overflow: 'hidden',
          borderLeft: '4px solid',
          borderColor: 'primary.main',
          animation: 'fadeIn 0.5s ease-out',
        }}
      >
        <CardContent sx={{ p: 1, position: 'relative', zIndex: 2 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}>
            <Box>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 0.5,
                  letterSpacing: 0.7
                }}
              >
                GESTION DES CLIENTS
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600
                }}
              >
                Créez, modifiez et gérez vos clients pour les associer à vos factures et devis
              </Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenFormDialog()}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: '0 4px 10px rgba(0,0,0,0.15)',
                fontWeight: 'bold',
                letterSpacing: 0.5,
                textTransform: 'none',
                fontSize: '0.95rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 6px 15px rgba(0,0,0,0.2)',
                  transform: 'translateY(-2px)',
                }
              }}
            >
              Ajouter un client
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Toolbar */}
      <Card
        elevation={2}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2,
          background: 'linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95)), linear-gradient(to right, #f5f7fa, #e4e7eb)',
          boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
          border: '1px solid rgba(0,0,0,0.05)',
          animation: 'fadeIn 0.5s ease-out',
          animationDelay: '0.1s',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap', flex: 1 }}>
            <TextField
              variant="outlined"
              placeholder="Rechercher clients..."
              size="small"
              InputProps={{
                startAdornment: <SearchIcon color="primary" sx={{ mr: 1 }} />,
                sx: {
                  borderRadius: 2,
                  backgroundColor: 'white',
                  '& fieldset': {
                    borderColor: 'rgba(0,0,0,0.1)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'primary.main',
                  },
                }
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{
                width: { xs: '100%', sm: 300 },
                '& .MuiOutlinedInput-root': {
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  },
                  '&.Mui-focused': {
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                  }
                }
              }}
            />

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                size="small"
                onClick={handleOpenFilterMenu}
                sx={{
                  borderRadius: 2,
                  borderColor: 'rgba(0,0,0,0.1)',
                  color: 'text.primary',
                  backgroundColor: 'white',
                  px: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.02)',
                    borderColor: 'primary.main',
                  }
                }}
              >
                Filtrer
              </Button>
              <Menu
                anchorEl={filterMenuAnchorEl}
                open={Boolean(filterMenuAnchorEl)}
                onClose={handleCloseFilterMenu}
                PaperProps={{
                  sx: {
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  }
                }}
              >
                <MenuItem sx={{ pointerEvents: 'none', opacity: 0.7, bgcolor: 'rgba(0,0,0,0.03)' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    Catégorie
                  </Typography>
                </MenuItem>
                <MenuItem
                  onClick={() => { setCategoryFilter('all'); handleCloseFilterMenu(); }}
                  selected={categoryFilter === 'all'}
                >
                  Toutes
                </MenuItem>
                <MenuItem
                  onClick={() => { setCategoryFilter('Standard'); handleCloseFilterMenu(); }}
                  selected={categoryFilter === 'Standard'}
                >
                  Standard
                </MenuItem>
                <MenuItem
                  onClick={() => { setCategoryFilter('Premium'); handleCloseFilterMenu(); }}
                  selected={categoryFilter === 'Premium'}
                >
                  Premium
                </MenuItem>
                <MenuItem
                  onClick={() => { setCategoryFilter('VIP'); handleCloseFilterMenu(); }}
                  selected={categoryFilter === 'VIP'}
                >
                  VIP
                </MenuItem>
                <Divider />
                <MenuItem sx={{ pointerEvents: 'none', opacity: 0.7, bgcolor: 'rgba(0,0,0,0.03)' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    Statut
                  </Typography>
                </MenuItem>
                <MenuItem
                  onClick={() => { setStatusFilter('all'); handleCloseFilterMenu(); }}
                  selected={statusFilter === 'all'}
                >
                  Tous
                </MenuItem>
                <MenuItem
                  onClick={() => { setStatusFilter('active'); handleCloseFilterMenu(); }}
                  selected={statusFilter === 'active'}
                >
                  Actifs
                </MenuItem>
                <MenuItem
                  onClick={() => { setStatusFilter('inactive'); handleCloseFilterMenu(); }}
                  selected={statusFilter === 'inactive'}
                >
                  Inactifs
                </MenuItem>

                {vendeurs.length > 0 && (
                  <>
                    <Divider />
                    <MenuItem sx={{ pointerEvents: 'none', opacity: 0.7, bgcolor: 'rgba(0,0,0,0.03)' }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                        Vendeur assigné
                      </Typography>
                    </MenuItem>
                    <MenuItem
                      onClick={() => { setVendeurFilter('all'); handleCloseFilterMenu(); }}
                      selected={vendeurFilter === 'all'}
                    >
                      Tous les vendeurs
                    </MenuItem>
                    {vendeurs.map((vendeur) => (
                      <MenuItem
                        key={vendeur._id}
                        onClick={() => { setVendeurFilter(vendeur._id); handleCloseFilterMenu(); }}
                        selected={vendeurFilter === vendeur._id}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar
                            src={vendeur.profileImage ? `http://localhost:5000${vendeur.profileImage}` : ''}
                            sx={{ width: 24, height: 24 }}
                          >
                            {!vendeur.profileImage && vendeur.nom.charAt(0)}
                          </Avatar>
                          {vendeur.nom}
                        </Box>
                      </MenuItem>
                    ))}
                  </>
                )}
              </Menu>

              <Button
                variant="outlined"
                startIcon={<SortIcon />}
                size="small"
                onClick={handleOpenSortMenu}
                sx={{
                  borderRadius: 2,
                  borderColor: 'rgba(0,0,0,0.1)',
                  color: 'text.primary',
                  backgroundColor: 'white',
                  px: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.02)',
                    borderColor: 'primary.main',
                  }
                }}
              >
                Trier
              </Button>
              <Menu
                anchorEl={sortMenuAnchorEl}
                open={Boolean(sortMenuAnchorEl)}
                onClose={handleCloseSortMenu}
                PaperProps={{
                  sx: {
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  }
                }}
              >
                <MenuItem
                  onClick={() => { setSortBy('name'); handleCloseSortMenu(); }}
                  selected={sortBy === 'name'}
                >
                  Nom
                </MenuItem>
                <MenuItem
                  onClick={() => { setSortBy('date'); handleCloseSortMenu(); }}
                  selected={sortBy === 'date'}
                >
                  Date d'ajout
                </MenuItem>
                <MenuItem
                  onClick={() => { setSortBy('category'); handleCloseSortMenu(); }}
                  selected={sortBy === 'category'}
                >
                  Catégorie
                </MenuItem>
                <MenuItem
                  onClick={() => { setSortBy('revenue'); handleCloseSortMenu(); }}
                  selected={sortBy === 'revenue'}
                >
                  Chiffre d'affaires
                </MenuItem>
              </Menu>
            </Box>
          </Box>

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            bgcolor: 'rgba(0,0,0,0.03)',
            borderRadius: 2,
            p: 0.5
          }}>
            <Tooltip title="Vue liste">
              <IconButton
                color={viewMode === 'list' ? 'primary' : 'default'}
                onClick={() => setViewMode('list')}
                sx={{
                  bgcolor: viewMode === 'list' ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                  '&:hover': {
                    bgcolor: viewMode === 'list' ? 'rgba(25, 118, 210, 0.15)' : 'rgba(0,0,0,0.05)',
                  }
                }}
              >
                <ViewListIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Vue grille">
              <IconButton
                color={viewMode === 'grid' ? 'primary' : 'default'}
                onClick={() => setViewMode('grid')}
                sx={{
                  bgcolor: viewMode === 'grid' ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                  '&:hover': {
                    bgcolor: viewMode === 'grid' ? 'rgba(25, 118, 210, 0.15)' : 'rgba(0,0,0,0.05)',
                  }
                }}
              >
                <ViewModuleIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Card>

      {/* Applied Filters */}
      {(categoryFilter !== 'all' || statusFilter !== 'all' || vendeurFilter !== 'all') && (
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            mb: 2,
            flexWrap: 'wrap',
            p: 1.5,
            borderRadius: 2,
            bgcolor: 'rgba(25, 118, 210, 0.05)',
            border: '1px dashed rgba(25, 118, 210, 0.3)',
            animation: 'fadeIn 0.5s ease-out',
            animationDelay: '0.2s',
          }}
        >
          <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'medium', mr: 1, display: 'flex', alignItems: 'center' }}>
            <FilterListIcon fontSize="small" sx={{ mr: 0.5 }} /> Filtres actifs:
          </Typography>

          {categoryFilter !== 'all' && (
            <Chip
              label={`Catégorie: ${categoryFilter}`}
              onDelete={() => setCategoryFilter('all')}
              size="small"
              color="primary"
              variant="outlined"
              sx={{
                borderRadius: 1.5,
                '& .MuiChip-deleteIcon': {
                  color: 'primary.light',
                  '&:hover': {
                    color: 'primary.main',
                  }
                }
              }}
            />
          )}

          {statusFilter !== 'all' && (
            <Chip
              label={`Statut: ${statusFilter === 'active' ? 'Actifs' : 'Inactifs'}`}
              onDelete={() => setStatusFilter('all')}
              size="small"
              color="primary"
              variant="outlined"
              sx={{
                borderRadius: 1.5,
                '& .MuiChip-deleteIcon': {
                  color: 'primary.light',
                  '&:hover': {
                    color: 'primary.main',
                  }
                }
              }}
            />
          )}

          {vendeurFilter !== 'all' && (
            <Chip
              label={`Vendeur: ${vendeurs.find(v => v._id === vendeurFilter)?.nom || 'Inconnu'}`}
              onDelete={() => setVendeurFilter('all')}
              size="small"
              color="primary"
              variant="outlined"
              sx={{
                borderRadius: 1.5,
                '& .MuiChip-deleteIcon': {
                  color: 'primary.light',
                  '&:hover': {
                    color: 'primary.main',
                  }
                }
              }}
            />
          )}

          <Button
            size="small"
            variant="text"
            color="primary"
            onClick={() => {
              setCategoryFilter('all');
              setStatusFilter('all');
              setVendeurFilter('all');
            }}
            sx={{
              ml: 'auto',
              textTransform: 'none',
              fontWeight: 'medium',
            }}
          >
            Effacer tous les filtres
          </Button>
        </Box>
      )}

      {/* Loading Indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* No Results Message */}
      {!loading && filteredClients.length === 0 && (
        <Card
          elevation={2}
          sx={{
            p: 4,
            textAlign: 'center',
            borderRadius: 2,
            background: 'linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95)), linear-gradient(to right, #f5f7fa, #e4e7eb)',
            boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
            border: '1px solid rgba(0,0,0,0.05)',
            animation: 'fadeIn 0.5s ease-out',
          }}
        >
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            maxWidth: 500,
            mx: 'auto',
            py: 2
          }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'primary.light',
                mb: 2
              }}
            >
              <BusinessIcon sx={{ fontSize: 40 }} />
            </Avatar>
            <Typography variant="h5" color="primary.main" gutterBottom fontWeight="bold">
              Aucun client trouvé
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Modifiez vos critères de recherche ou cliquez sur "Ajouter un client" pour créer un nouveau client dans votre base de données.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenFormDialog()}
              sx={{
                px: 3,
                py: 1.5,
                borderRadius: 2,
                boxShadow: '0 4px 10px rgba(0,0,0,0.15)',
                fontWeight: 'bold',
                letterSpacing: 0.5,
                textTransform: 'none',
                fontSize: '0.95rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 6px 15px rgba(0,0,0,0.2)',
                  transform: 'translateY(-2px)',
                }
              }}
            >
              Ajouter un client
            </Button>
          </Box>
        </Card>
      )}

      {/* List View */}
      {!loading && filteredClients.length > 0 && viewMode === 'list' && (
        <Card
          elevation={3}
          sx={{
            mb: 3,
            borderRadius: 2,
            background: 'linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95)), linear-gradient(to right, #f5f7fa, #e4e7eb)',
            boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
            border: '1px solid rgba(0,0,0,0.05)',
            overflow: 'hidden',
            animation: 'fadeIn 0.5s ease-out',
            animationDelay: '0.3s',
          }}
        >
          <TableContainer sx={{ overflow: 'auto' }}>
            <Table>
              <TableHead>
                <TableRow sx={{
                  background: 'white',
                  borderBottom: '2px solid',
                  borderColor: 'primary.main',
                }}>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BusinessIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>CLIENT</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PhoneIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>CONTACT</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <EmailIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>EMAIL</Typography>
                    </Box>
                  </TableCell>
                  {!isMobile && (
                    <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CategoryIcon fontSize="small" color="primary" />
                        <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>CATÉGORIE</Typography>
                      </Box>
                    </TableCell>
                  )}
                  {!isMobile && !isTablet && (
                    <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <InfoIcon fontSize="small" color="primary" />
                        <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>STATUT</Typography>
                      </Box>
                    </TableCell>
                  )}

                  {!isMobile && (
                    <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PersonIcon fontSize="small" color="primary" />
                        <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>VENDEUR</Typography>
                      </Box>
                    </TableCell>
                  )}
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CloseIcon fontSize="small" color="primary" />
                      <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>DATE</Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                    <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>ACTIONS</Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredClients.map((client, index) => (
                  <TableRow
                    key={client._id}
                    hover
                    sx={{
                      backgroundColor: index % 2 === 0 ? 'white' : 'rgba(0, 0, 0, 0.02)',
                      transition: 'background-color 0.2s ease',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      }
                    }}
                  >
                    <TableCell sx={{ py: 2 }}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                        {client.logo ? (
                          <Avatar
                            src={client.logo}
                            sx={{
                              width: 40,
                              height: 40,
                              border: '1px solid',
                              borderColor: 'divider'
                            }}
                          />
                        ) : (
                          <Avatar
                            sx={{
                              bgcolor: theme.palette.primary.main,
                              width: 40,
                              height: 40,
                              fontSize: 16,
                              border: '1px solid',
                              borderColor: 'divider'
                            }}
                          >
                            {client.nom.charAt(0).toUpperCase()}
                          </Avatar>
                        )}
                        <Box>
                          <Typography
                            sx={{
                              fontWeight: 'medium',
                              fontSize: '1rem',
                              color: 'primary.dark'
                            }}
                          >
                            {client.nom}
                          </Typography>
                          {client.secteurActivite && (
                            <Typography variant="caption" color="text.secondary">
                              {client.secteurActivite}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <PhoneIcon fontSize="small" sx={{ color: theme.palette.primary.light }} />
                        <Typography variant="body2">{client.contact}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <EmailIcon fontSize="small" sx={{ color: theme.palette.primary.light }} />
                        <Typography variant="body2">{client.email}</Typography>
                      </Box>
                    </TableCell>
                    {!isMobile && (
                      <TableCell sx={{ py: 2 }}>
                        <Chip
                          label={client.categorie || 'Standard'}
                          size="small"
                          color={
                            client.categorie === 'VIP' ? 'error' :
                            client.categorie === 'Premium' ? 'warning' : 'info'
                          }
                          sx={{
                            fontWeight: 'medium',
                            borderRadius: 1,
                            '& .MuiChip-label': { px: 1.5 }
                          }}
                        />
                      </TableCell>
                    )}
                    {!isMobile && !isTablet && (
                      <TableCell sx={{ py: 2 }}>
                        <Chip
                          label={client.actif ? 'Actif' : 'Inactif'}
                          size="small"
                          color={client.actif ? 'success' : 'default'}
                          sx={{
                            fontWeight: 'medium',
                            borderRadius: 1,
                            '& .MuiChip-label': { px: 1.5 }
                          }}
                        />
                      </TableCell>
                    )}

                    {!isMobile && (
                      <TableCell sx={{ py: 2 }}>
                        {client.vendeurId ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {(() => {
                              const vendeur = vendeurs.find(v => v._id === client.vendeurId);
                              return vendeur ? (
                                <>
                                  <Avatar
                                    src={vendeur.profileImage ? `http://localhost:5000${vendeur.profileImage}` : ''}
                                    sx={{ width: 24, height: 24 }}
                                  >
                                    {!vendeur.profileImage && vendeur.nom.charAt(0)}
                                  </Avatar>
                                  <Typography variant="body2">{vendeur.nom}</Typography>
                                </>
                              ) : (
                                <Typography variant="body2" color="text.secondary">Non assigné</Typography>
                              );
                            })()}
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">Non assigné</Typography>
                        )}
                      </TableCell>
                    )}
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(client.createdAt)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right" sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Tooltip title="Voir détails" arrow placement="top">
                          <IconButton
                            onClick={() => handleOpenDetailDialog(client)}
                            size="small"
                            sx={{
                              bgcolor: 'info.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'info.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Modifier" arrow placement="top">
                          <IconButton
                            onClick={() => handleOpenFormDialog(client)}
                            size="small"
                            sx={{
                              bgcolor: 'primary.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'primary.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer" arrow placement="top">
                          <IconButton
                            onClick={() => handleDeleteClient(client)}
                            size="small"
                            sx={{
                              bgcolor: 'error.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'error.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      )}

      {/* Grid View */}
      {!loading && filteredClients.length > 0 && viewMode === 'grid' && (
        <Box sx={{ animation: 'fadeIn 0.5s ease-out', animationDelay: '0.3s' }}>
          <Grid container spacing={3}>
            {filteredClients.map((client) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={client._id}>
                <ClientCard
                  client={client}
                  onView={handleOpenDetailDialog}
                  onEdit={handleOpenFormDialog}
                  onDelete={handleDeleteClient}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => { handleOpenDetailDialog(currentClient); handleCloseMenu(); }}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          Voir détails
        </MenuItem>
        <MenuItem onClick={() => { handleOpenFormDialog(currentClient); handleCloseMenu(); }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          Modifier
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleOpenDeleteDialog(currentClient)}>
          <ListItemIcon>
            <DeleteOutlineIcon fontSize="small" color="error" />
          </ListItemIcon>
          <Typography color="error">Supprimer</Typography>
        </MenuItem>
      </Menu>

      {/* Dialogs */}
      <ClientForm
        open={openFormDialog}
        onClose={handleCloseFormDialog}
        client={currentClient}
        onSave={handleSave}
        onUploadLogo={handleUploadLogo}
      />

      <Dialog
        open={openDetailDialog}
        onClose={handleCloseDetailDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
          }
        }}
      >
        <ClientDetail
          client={currentClient}
          onEdit={handleOpenFormDialog}
          onClose={handleCloseDetailDialog}
        />
      </Dialog>

      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
          }
        }}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Confirmer la suppression
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Êtes-vous sûr de vouloir supprimer le client "{currentClient?.nom}" ? Cette action est irréversible.
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button onClick={handleCloseDeleteDialog} variant="outlined">
              Annuler
            </Button>
            <Button onClick={handleDelete} variant="contained" color="error">
              Supprimer
            </Button>
          </Box>
        </Box>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Clients;