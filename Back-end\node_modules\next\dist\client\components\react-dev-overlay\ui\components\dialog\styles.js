"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "styles", {
    enumerable: true,
    get: function() {
        return styles;
    }
});
const styles = "\n  [data-nextjs-dialog-root] {\n    --next-dialog-radius: var(--rounded-xl);\n    --next-dialog-max-width: 960px;\n    --next-dialog-row-padding: 16px;\n    --next-dialog-padding-x: 12px;\n    --next-dialog-notch-height: 42px;\n    --next-dialog-border-width: 1px;\n\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    max-height: calc(100% - 56px);\n    max-width: var(--next-dialog-max-width);\n    margin-right: auto;\n    margin-left: auto;\n    scale: 0.98;\n    opacity: 0;\n    transition-property: scale, opacity;\n    transition-duration: var(--transition-duration);\n    transition-timing-function: var(--timing-overlay);\n\n    &[data-rendered='true'] {\n      opacity: 1;\n      scale: 1;\n    }\n\n    [data-nextjs-scroll-fader][data-side=\"top\"] {\n      left: 1px;\n      top: calc(var(--next-dialog-notch-height) + var(--next-dialog-border-width));\n      width: calc(100% - var(--next-dialog-padding-x));\n      opacity: 0;\n    }\n  }\n\n  [data-nextjs-dialog] {\n    outline: 0;\n  }\n\n  [data-nextjs-dialog], [data-nextjs-dialog] * {\n    &::-webkit-scrollbar {\n      width: 6px;\n      height: 6px;\n      border-radius: 0 0 1rem 1rem;\n      margin-bottom: 1rem;\n    }\n\n    &::-webkit-scrollbar-button {\n      display: none;\n    }\n\n    &::-webkit-scrollbar-track {\n      border-radius: 0 0 1rem 1rem;\n      background-color: var(--color-background-100);\n    }\n      \n    &::-webkit-scrollbar-thumb {\n      border-radius: 1rem;\n      background-color: var(--color-gray-500);\n    }\n  }\n\n  /* Place overflow: hidden on this so we can break out from [data-nextjs-dialog] */\n  [data-nextjs-dialog-sizer] {\n    overflow: hidden;\n    border-radius: inherit;\n  }\n\n  [data-nextjs-dialog-backdrop] {\n    opacity: 0;\n    transition: opacity var(--transition-duration) var(--timing-overlay);\n  }\n\n  [data-nextjs-dialog-overlay][data-rendered='true']\n    [data-nextjs-dialog-backdrop] {\n    opacity: 1;\n  }\n\n  [data-nextjs-dialog-content] {\n    border: none;\n    margin: 0;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n    padding: 16px var(--next-dialog-padding-x);\n  }\n\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\n    flex-shrink: 0;\n    margin-bottom: 8px;\n  }\n\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  @media (max-height: 812px) {\n    [data-nextjs-dialog-overlay] {\n      max-height: calc(100% - 15px);\n    }\n  }\n\n  @media (min-width: 576px) {\n    [data-nextjs-dialog-root] {\n      --next-dialog-max-width: 540px;\n    }\n  }\n\n  @media (min-width: 768px) {\n    [data-nextjs-dialog-root] {\n      --next-dialog-max-width: 720px;\n    }\n  }\n\n  @media (min-width: 992px) {\n    [data-nextjs-dialog-root] {\n      --next-dialog-max-width: 960px;\n    }\n  }\n";

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=styles.js.map