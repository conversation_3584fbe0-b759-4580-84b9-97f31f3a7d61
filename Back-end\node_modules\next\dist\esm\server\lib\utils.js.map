{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "sourcesContent": ["import { parseArgs } from 'node:util'\nimport { InvalidArgumentError } from 'next/dist/compiled/commander'\n\nexport function printAndExit(message: string, code = 1) {\n  if (code === 0) {\n    console.log(message)\n  } else {\n    console.error(message)\n  }\n\n  return process.exit(code)\n}\n\nconst parseNodeArgs = (args: string[]) => {\n  const { values, tokens } = parseArgs({ args, strict: false, tokens: true })\n\n  // For the `NODE_OPTIONS`, we support arguments with values without the `=`\n  // sign. We need to parse them manually.\n  let orphan = null\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i]\n\n    if (token.kind === 'option-terminator') {\n      break\n    }\n\n    // When we encounter an option, if it's value is undefined, we should check\n    // to see if the following tokens are positional parameters. If they are,\n    // then the option is orphaned, and we can assign it.\n    if (token.kind === 'option') {\n      orphan = typeof token.value === 'undefined' ? token : null\n      continue\n    }\n\n    // If the token isn't a positional one, then we can't assign it to the found\n    // orphaned option.\n    if (token.kind !== 'positional') {\n      orphan = null\n      continue\n    }\n\n    // If we don't have an orphan, then we can skip this token.\n    if (!orphan) {\n      continue\n    }\n\n    // If the token is a positional one, and it has a value, so add it to the\n    // values object. If it already exists, append it with a space.\n    if (orphan.name in values && typeof values[orphan.name] === 'string') {\n      values[orphan.name] += ` ${token.value}`\n    } else {\n      values[orphan.name] = token.value\n    }\n  }\n\n  return values\n}\n\n/**\n * Tokenizes the arguments string into an array of strings, supporting quoted\n * values and escaped characters.\n * Converted from: https://github.com/nodejs/node/blob/c29d53c5cfc63c5a876084e788d70c9e87bed880/src/node_options.cc#L1401\n *\n * @param input The arguments string to be tokenized.\n * @returns An array of strings with the tokenized arguments.\n */\nexport const tokenizeArgs = (input: string): string[] => {\n  let args: string[] = []\n  let isInString = false\n  let willStartNewArg = true\n\n  for (let i = 0; i < input.length; i++) {\n    let char = input[i]\n\n    // Skip any escaped characters in strings.\n    if (char === '\\\\' && isInString) {\n      // Ensure we don't have an escape character at the end.\n      if (input.length === i + 1) {\n        throw new Error('Invalid escape character at the end.')\n      }\n\n      // Skip the next character.\n      char = input[++i]\n    }\n    // If we find a space outside of a string, we should start a new argument.\n    else if (char === ' ' && !isInString) {\n      willStartNewArg = true\n      continue\n    }\n\n    // If we find a quote, we should toggle the string flag.\n    else if (char === '\"') {\n      isInString = !isInString\n      continue\n    }\n\n    // If we're starting a new argument, we should add it to the array.\n    if (willStartNewArg) {\n      args.push(char)\n      willStartNewArg = false\n    }\n    // Otherwise, add it to the last argument.\n    else {\n      args[args.length - 1] += char\n    }\n  }\n\n  if (isInString) {\n    throw new Error('Unterminated string')\n  }\n\n  return args\n}\n\n/**\n * Get the node options from the environment variable `NODE_OPTIONS` and returns\n * them as an array of strings.\n *\n * @returns An array of strings with the node options.\n */\nconst getNodeOptionsArgs = () => {\n  if (!process.env.NODE_OPTIONS) return []\n\n  return tokenizeArgs(process.env.NODE_OPTIONS)\n}\n\n/**\n * The debug address is in the form of `[host:]port`. The host is optional.\n */\ntype DebugAddress = {\n  host: string | undefined\n  port: number\n}\n\n/**\n * Formats the debug address into a string.\n */\nexport const formatDebugAddress = ({ host, port }: DebugAddress): string => {\n  if (host) return `${host}:${port}`\n  return `${port}`\n}\n\n/**\n * Get's the debug address from the `NODE_OPTIONS` environment variable. If the\n * address is not found, it returns the default host (`undefined`) and port\n * (`9229`).\n *\n * @returns An object with the host and port of the debug address.\n */\nexport const getParsedDebugAddress = (): DebugAddress => {\n  const args = getNodeOptionsArgs()\n  if (args.length === 0) return { host: undefined, port: 9229 }\n\n  const parsed = parseNodeArgs(args)\n\n  // We expect to find the debug port in one of these options. The first one\n  // found will be used.\n  const address =\n    parsed.inspect ?? parsed['inspect-brk'] ?? parsed['inspect_brk']\n\n  if (!address || typeof address !== 'string') {\n    return { host: undefined, port: 9229 }\n  }\n\n  // The address is in the form of `[host:]port`. Let's parse the address.\n  if (address.includes(':')) {\n    const [host, port] = address.split(':')\n    return { host, port: parseInt(port, 10) }\n  }\n\n  return { host: undefined, port: parseInt(address, 10) }\n}\n\n/**\n * Get the debug address from the `NODE_OPTIONS` environment variable and format\n * it into a string.\n *\n * @returns A string with the formatted debug address.\n */\nexport const getFormattedDebugAddress = () =>\n  formatDebugAddress(getParsedDebugAddress())\n\n/**\n * Stringify the arguments to be used in a command line. It will ignore any\n * argument that has a value of `undefined`.\n *\n * @param args The arguments to be stringified.\n * @returns A string with the arguments.\n */\nexport function formatNodeOptions(\n  args: Record<string, string | boolean | undefined>\n): string {\n  return Object.entries(args)\n    .map(([key, value]) => {\n      if (value === true) {\n        return `--${key}`\n      }\n\n      if (value) {\n        return `--${key}=${\n          // Values with spaces need to be quoted. We use JSON.stringify to\n          // also escape any nested quotes.\n          value.includes(' ') && !value.startsWith('\"')\n            ? JSON.stringify(value)\n            : value\n        }`\n      }\n\n      return null\n    })\n    .filter((arg) => arg !== null)\n    .join(' ')\n}\n\n/**\n * Get the node options from the `NODE_OPTIONS` environment variable and parse\n * them into an object without the inspect options.\n *\n * @returns An object with the parsed node options.\n */\nexport function getParsedNodeOptionsWithoutInspect() {\n  const args = getNodeOptionsArgs()\n  if (args.length === 0) return {}\n\n  const parsed = parseNodeArgs(args)\n\n  // Remove inspect options.\n  delete parsed.inspect\n  delete parsed['inspect-brk']\n  delete parsed['inspect_brk']\n\n  return parsed\n}\n\n/**\n * Get the node options from the `NODE_OPTIONS` environment variable and format\n * them into a string without the inspect options.\n *\n * @returns A string with the formatted node options.\n */\nexport function getFormattedNodeOptionsWithoutInspect() {\n  const args = getParsedNodeOptionsWithoutInspect()\n  if (Object.keys(args).length === 0) return ''\n\n  return formatNodeOptions(args)\n}\n\n/**\n * Check if the value is a valid positive integer and parse it. If it's not, it will throw an error.\n *\n * @param value The value to be parsed.\n */\nexport function parseValidPositiveInteger(value: string): number {\n  const parsedValue = parseInt(value, 10)\n\n  if (isNaN(parsedValue) || !isFinite(parsedValue) || parsedValue < 0) {\n    throw new InvalidArgumentError(`'${value}' is not a non-negative number.`)\n  }\n  return parsedValue\n}\n\nexport const RESTART_EXIT_CODE = 77\n\nexport type NodeInspectType = 'inspect' | 'inspect-brk' | undefined\n\n/**\n * Get the debug type from the `NODE_OPTIONS` environment variable.\n */\nexport function getNodeDebugType(): NodeInspectType {\n  const args = [...process.execArgv, ...getNodeOptionsArgs()]\n  if (args.length === 0) return\n\n  const parsed = parseNodeArgs(args)\n\n  if (parsed.inspect) return 'inspect'\n  if (parsed['inspect-brk'] || parsed['inspect_brk']) return 'inspect-brk'\n}\n\n/**\n * Get the `max-old-space-size` value from the `NODE_OPTIONS` environment\n * variable.\n *\n * @returns The value of the `max-old-space-size` option as a number.\n */\nexport function getMaxOldSpaceSize() {\n  const args = getNodeOptionsArgs()\n  if (args.length === 0) return\n\n  const parsed = parseNodeArgs(args)\n\n  const size = parsed['max-old-space-size'] || parsed['max_old_space_size']\n  if (!size || typeof size !== 'string') return\n\n  return parseInt(size, 10)\n}\n"], "names": ["parseArgs", "InvalidArgumentError", "printAndExit", "message", "code", "console", "log", "error", "process", "exit", "parseNodeArgs", "args", "values", "tokens", "strict", "orphan", "i", "length", "token", "kind", "value", "name", "tokenizeArgs", "input", "isInString", "willStartNewArg", "char", "Error", "push", "getNodeOptionsArgs", "env", "NODE_OPTIONS", "formatDebugAddress", "host", "port", "getParsedDebugAddress", "undefined", "parsed", "address", "inspect", "includes", "split", "parseInt", "getFormattedDebugAddress", "formatNodeOptions", "Object", "entries", "map", "key", "startsWith", "JSON", "stringify", "filter", "arg", "join", "getParsedNodeOptionsWithoutInspect", "getFormattedNodeOptionsWithoutInspect", "keys", "parseValidPositiveInteger", "parsedValue", "isNaN", "isFinite", "RESTART_EXIT_CODE", "getNodeDebugType", "execArgv", "getMaxOldSpaceSize", "size"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAW;AACrC,SAASC,oBAAoB,QAAQ,+BAA8B;AAEnE,OAAO,SAASC,aAAaC,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEA,OAAOK,QAAQC,IAAI,CAACL;AACtB;AAEA,MAAMM,gBAAgB,CAACC;IACrB,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAGb,UAAU;QAAEW;QAAMG,QAAQ;QAAOD,QAAQ;IAAK;IAEzE,2EAA2E;IAC3E,wCAAwC;IACxC,IAAIE,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIH,OAAOI,MAAM,EAAED,IAAK;QACtC,MAAME,QAAQL,MAAM,CAACG,EAAE;QAEvB,IAAIE,MAAMC,IAAI,KAAK,qBAAqB;YACtC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,qDAAqD;QACrD,IAAID,MAAMC,IAAI,KAAK,UAAU;YAC3BJ,SAAS,OAAOG,MAAME,KAAK,KAAK,cAAcF,QAAQ;YACtD;QACF;QAEA,4EAA4E;QAC5E,mBAAmB;QACnB,IAAIA,MAAMC,IAAI,KAAK,cAAc;YAC/BJ,SAAS;YACT;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACA,QAAQ;YACX;QACF;QAEA,yEAAyE;QACzE,+DAA+D;QAC/D,IAAIA,OAAOM,IAAI,IAAIT,UAAU,OAAOA,MAAM,CAACG,OAAOM,IAAI,CAAC,KAAK,UAAU;YACpET,MAAM,CAACG,OAAOM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEH,MAAME,KAAK,EAAE;QAC1C,OAAO;YACLR,MAAM,CAACG,OAAOM,IAAI,CAAC,GAAGH,MAAME,KAAK;QACnC;IACF;IAEA,OAAOR;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,MAAMU,eAAe,CAACC;IAC3B,IAAIZ,OAAiB,EAAE;IACvB,IAAIa,aAAa;IACjB,IAAIC,kBAAkB;IAEtB,IAAK,IAAIT,IAAI,GAAGA,IAAIO,MAAMN,MAAM,EAAED,IAAK;QACrC,IAAIU,OAAOH,KAAK,CAACP,EAAE;QAEnB,0CAA0C;QAC1C,IAAIU,SAAS,QAAQF,YAAY;YAC/B,uDAAuD;YACvD,IAAID,MAAMN,MAAM,KAAKD,IAAI,GAAG;gBAC1B,MAAM,qBAAiD,CAAjD,IAAIW,MAAM,yCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAgD;YACxD;YAEA,2BAA2B;YAC3BD,OAAOH,KAAK,CAAC,EAAEP,EAAE;QACnB,OAEK,IAAIU,SAAS,OAAO,CAACF,YAAY;YACpCC,kBAAkB;YAClB;QACF,OAGK,IAAIC,SAAS,KAAK;YACrBF,aAAa,CAACA;YACd;QACF;QAEA,mEAAmE;QACnE,IAAIC,iBAAiB;YACnBd,KAAKiB,IAAI,CAACF;YACVD,kBAAkB;QACpB,OAEK;YACHd,IAAI,CAACA,KAAKM,MAAM,GAAG,EAAE,IAAIS;QAC3B;IACF;IAEA,IAAIF,YAAY;QACd,MAAM,qBAAgC,CAAhC,IAAIG,MAAM,wBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+B;IACvC;IAEA,OAAOhB;AACT,EAAC;AAED;;;;;CAKC,GACD,MAAMkB,qBAAqB;IACzB,IAAI,CAACrB,QAAQsB,GAAG,CAACC,YAAY,EAAE,OAAO,EAAE;IAExC,OAAOT,aAAad,QAAQsB,GAAG,CAACC,YAAY;AAC9C;AAUA;;CAEC,GACD,OAAO,MAAMC,qBAAqB,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAgB;IAC7D,IAAID,MAAM,OAAO,GAAGA,KAAK,CAAC,EAAEC,MAAM;IAClC,OAAO,GAAGA,MAAM;AAClB,EAAC;AAED;;;;;;CAMC,GACD,OAAO,MAAMC,wBAAwB;IACnC,MAAMxB,OAAOkB;IACb,IAAIlB,KAAKM,MAAM,KAAK,GAAG,OAAO;QAAEgB,MAAMG;QAAWF,MAAM;IAAK;IAE5D,MAAMG,SAAS3B,cAAcC;IAE7B,0EAA0E;IAC1E,sBAAsB;IACtB,MAAM2B,UACJD,OAAOE,OAAO,IAAIF,MAAM,CAAC,cAAc,IAAIA,MAAM,CAAC,cAAc;IAElE,IAAI,CAACC,WAAW,OAAOA,YAAY,UAAU;QAC3C,OAAO;YAAEL,MAAMG;YAAWF,MAAM;QAAK;IACvC;IAEA,wEAAwE;IACxE,IAAII,QAAQE,QAAQ,CAAC,MAAM;QACzB,MAAM,CAACP,MAAMC,KAAK,GAAGI,QAAQG,KAAK,CAAC;QACnC,OAAO;YAAER;YAAMC,MAAMQ,SAASR,MAAM;QAAI;IAC1C;IAEA,OAAO;QAAED,MAAMG;QAAWF,MAAMQ,SAASJ,SAAS;IAAI;AACxD,EAAC;AAED;;;;;CAKC,GACD,OAAO,MAAMK,2BAA2B,IACtCX,mBAAmBG,yBAAwB;AAE7C;;;;;;CAMC,GACD,OAAO,SAASS,kBACdjC,IAAkD;IAElD,OAAOkC,OAAOC,OAAO,CAACnC,MACnBoC,GAAG,CAAC,CAAC,CAACC,KAAK5B,MAAM;QAChB,IAAIA,UAAU,MAAM;YAClB,OAAO,CAAC,EAAE,EAAE4B,KAAK;QACnB;QAEA,IAAI5B,OAAO;YACT,OAAO,CAAC,EAAE,EAAE4B,IAAI,CAAC,EACf,iEAAiE;YACjE,iCAAiC;YACjC5B,MAAMoB,QAAQ,CAAC,QAAQ,CAACpB,MAAM6B,UAAU,CAAC,OACrCC,KAAKC,SAAS,CAAC/B,SACfA,OACJ;QACJ;QAEA,OAAO;IACT,GACCgC,MAAM,CAAC,CAACC,MAAQA,QAAQ,MACxBC,IAAI,CAAC;AACV;AAEA;;;;;CAKC,GACD,OAAO,SAASC;IACd,MAAM5C,OAAOkB;IACb,IAAIlB,KAAKM,MAAM,KAAK,GAAG,OAAO,CAAC;IAE/B,MAAMoB,SAAS3B,cAAcC;IAE7B,0BAA0B;IAC1B,OAAO0B,OAAOE,OAAO;IACrB,OAAOF,MAAM,CAAC,cAAc;IAC5B,OAAOA,MAAM,CAAC,cAAc;IAE5B,OAAOA;AACT;AAEA;;;;;CAKC,GACD,OAAO,SAASmB;IACd,MAAM7C,OAAO4C;IACb,IAAIV,OAAOY,IAAI,CAAC9C,MAAMM,MAAM,KAAK,GAAG,OAAO;IAE3C,OAAO2B,kBAAkBjC;AAC3B;AAEA;;;;CAIC,GACD,OAAO,SAAS+C,0BAA0BtC,KAAa;IACrD,MAAMuC,cAAcjB,SAAStB,OAAO;IAEpC,IAAIwC,MAAMD,gBAAgB,CAACE,SAASF,gBAAgBA,cAAc,GAAG;QACnE,MAAM,IAAI1D,qBAAqB,CAAC,CAAC,EAAEmB,MAAM,+BAA+B,CAAC;IAC3E;IACA,OAAOuC;AACT;AAEA,OAAO,MAAMG,oBAAoB,GAAE;AAInC;;CAEC,GACD,OAAO,SAASC;IACd,MAAMpD,OAAO;WAAIH,QAAQwD,QAAQ;WAAKnC;KAAqB;IAC3D,IAAIlB,KAAKM,MAAM,KAAK,GAAG;IAEvB,MAAMoB,SAAS3B,cAAcC;IAE7B,IAAI0B,OAAOE,OAAO,EAAE,OAAO;IAC3B,IAAIF,MAAM,CAAC,cAAc,IAAIA,MAAM,CAAC,cAAc,EAAE,OAAO;AAC7D;AAEA;;;;;CAKC,GACD,OAAO,SAAS4B;IACd,MAAMtD,OAAOkB;IACb,IAAIlB,KAAKM,MAAM,KAAK,GAAG;IAEvB,MAAMoB,SAAS3B,cAAcC;IAE7B,MAAMuD,OAAO7B,MAAM,CAAC,qBAAqB,IAAIA,MAAM,CAAC,qBAAqB;IACzE,IAAI,CAAC6B,QAAQ,OAAOA,SAAS,UAAU;IAEvC,OAAOxB,SAASwB,MAAM;AACxB"}