{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/font/get-dev-overlay-font-middleware.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport path from 'path'\nimport * as fs from 'fs/promises'\nimport { constants } from 'fs'\nimport * as Log from '../../../../build/output/log'\nimport { middlewareResponse } from '../server/middleware-response'\n\nconst FONT_PREFIX = '/__nextjs_font/'\n\nconst VALID_FONTS = [\n  'geist-latin-ext.woff2',\n  'geist-mono-latin-ext.woff2',\n  'geist-latin.woff2',\n  'geist-mono-latin.woff2',\n]\n\nconst FONT_HEADERS = {\n  'Content-Type': 'font/woff2',\n  'Cache-Control': 'public, max-age=31536000, immutable',\n} as const\n\nexport function getDevOverlayFontMiddleware() {\n  return async function devOverlayFontMiddleware(\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    try {\n      const { pathname } = new URL(`http://n${req.url}`)\n\n      if (!pathname.startsWith(FONT_PREFIX)) {\n        return next()\n      }\n\n      const fontFile = pathname.replace(FONT_PREFIX, '')\n      if (!VALID_FONTS.includes(fontFile)) {\n        return middlewareResponse.notFound(res)\n      }\n\n      const fontPath = path.resolve(__dirname, fontFile)\n      const fileExists = await checkFileExists(fontPath)\n\n      if (!fileExists) {\n        return middlewareResponse.notFound(res)\n      }\n\n      const fontData = await fs.readFile(fontPath)\n      Object.entries(FONT_HEADERS).forEach(([key, value]) => {\n        res.setHeader(key, value)\n      })\n      res.end(fontData)\n    } catch (err) {\n      Log.error(\n        'Failed to serve font:',\n        err instanceof Error ? err.message : err\n      )\n      return middlewareResponse.internalServerError(res)\n    }\n  }\n}\n\nasync function checkFileExists(filePath: string): Promise<boolean> {\n  try {\n    await fs.access(filePath, constants.F_OK)\n    return true\n  } catch {\n    return false\n  }\n}\n"], "names": ["path", "fs", "constants", "Log", "middlewareResponse", "FONT_PREFIX", "VALID_FONTS", "FONT_HEADERS", "getDevOverlayFontMiddleware", "devOverlayFontMiddleware", "req", "res", "next", "pathname", "URL", "url", "startsWith", "fontFile", "replace", "includes", "notFound", "fontPath", "resolve", "__dirname", "fileExists", "checkFileExists", "fontData", "readFile", "Object", "entries", "for<PERSON>ach", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "end", "err", "error", "Error", "message", "internalServerError", "filePath", "access", "F_OK"], "mappings": "AACA,OAAOA,UAAU,OAAM;AACvB,YAAYC,QAAQ,cAAa;AACjC,SAASC,SAAS,QAAQ,KAAI;AAC9B,YAAYC,SAAS,+BAA8B;AACnD,SAASC,kBAAkB,QAAQ,gCAA+B;AAElE,MAAMC,cAAc;AAEpB,MAAMC,cAAc;IAClB;IACA;IACA;IACA;CACD;AAED,MAAMC,eAAe;IACnB,gBAAgB;IAChB,iBAAiB;AACnB;AAEA,OAAO,SAASC;IACd,OAAO,eAAeC,yBACpBC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUJ,IAAIK,GAAG;YAE/C,IAAI,CAACF,SAASG,UAAU,CAACX,cAAc;gBACrC,OAAOO;YACT;YAEA,MAAMK,WAAWJ,SAASK,OAAO,CAACb,aAAa;YAC/C,IAAI,CAACC,YAAYa,QAAQ,CAACF,WAAW;gBACnC,OAAOb,mBAAmBgB,QAAQ,CAACT;YACrC;YAEA,MAAMU,WAAWrB,KAAKsB,OAAO,CAACC,WAAWN;YACzC,MAAMO,aAAa,MAAMC,gBAAgBJ;YAEzC,IAAI,CAACG,YAAY;gBACf,OAAOpB,mBAAmBgB,QAAQ,CAACT;YACrC;YAEA,MAAMe,WAAW,MAAMzB,GAAG0B,QAAQ,CAACN;YACnCO,OAAOC,OAAO,CAACtB,cAAcuB,OAAO,CAAC;oBAAC,CAACC,KAAKC,MAAM;gBAChDrB,IAAIsB,SAAS,CAACF,KAAKC;YACrB;YACArB,IAAIuB,GAAG,CAACR;QACV,EAAE,OAAOS,KAAK;YACZhC,IAAIiC,KAAK,CACP,yBACAD,eAAeE,QAAQF,IAAIG,OAAO,GAAGH;YAEvC,OAAO/B,mBAAmBmC,mBAAmB,CAAC5B;QAChD;IACF;AACF;AAEA,eAAec,gBAAgBe,QAAgB;IAC7C,IAAI;QACF,MAAMvC,GAAGwC,MAAM,CAACD,UAAUtC,UAAUwC,IAAI;QACxC,OAAO;IACT,EAAE,UAAM;QACN,OAAO;IACT;AACF"}