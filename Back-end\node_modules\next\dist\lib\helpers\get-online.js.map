{"version": 3, "sources": ["../../../src/lib/helpers/get-online.ts"], "sourcesContent": ["import { execSync } from 'child_process'\nimport dns from 'dns/promises'\n\nfunction getProxy(): string | undefined {\n  if (process.env.https_proxy) {\n    return process.env.https_proxy\n  }\n\n  try {\n    const httpsProxy = execSync('npm config get https-proxy', {\n      encoding: 'utf8',\n    }).trim()\n    return httpsProxy !== 'null' ? httpsProxy : undefined\n  } catch (e) {\n    return\n  }\n}\n\nexport async function getOnline(): Promise<boolean> {\n  try {\n    await dns.lookup('registry.yarnpkg.com')\n    return true\n  } catch {\n    const proxy = getProxy()\n    if (!proxy) {\n      return false\n    }\n\n    try {\n      const { hostname } = new URL(proxy)\n      await dns.lookup(hostname)\n      return true\n    } catch {\n      return false\n    }\n  }\n}\n"], "names": ["getOnline", "getProxy", "process", "env", "https_proxy", "httpsProxy", "execSync", "encoding", "trim", "undefined", "e", "dns", "lookup", "proxy", "hostname", "URL"], "mappings": ";;;;+BAkBsBA;;;eAAAA;;;+BAlBG;iEACT;;;;;;AAEhB,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,WAAW,EAAE;QAC3B,OAAOF,QAAQC,GAAG,CAACC,WAAW;IAChC;IAEA,IAAI;QACF,MAAMC,aAAaC,IAAAA,uBAAQ,EAAC,8BAA8B;YACxDC,UAAU;QACZ,GAAGC,IAAI;QACP,OAAOH,eAAe,SAASA,aAAaI;IAC9C,EAAE,OAAOC,GAAG;QACV;IACF;AACF;AAEO,eAAeV;IACpB,IAAI;QACF,MAAMW,iBAAG,CAACC,MAAM,CAAC;QACjB,OAAO;IACT,EAAE,OAAM;QACN,MAAMC,QAAQZ;QACd,IAAI,CAACY,OAAO;YACV,OAAO;QACT;QAEA,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAIF;YAC7B,MAAMF,iBAAG,CAACC,MAAM,CAACE;YACjB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF"}