{"version": 3, "sources": ["../../../../src/lib/metadata/types/extra-types.ts"], "sourcesContent": ["// When rendering applink meta tags add a namespace tag before each array instance\n// if more than one member exists.\n// ref: https://developers.facebook.com/docs/applinks/metadata-reference\n\nexport type AppLinks = {\n  ios?: AppLinksApple | Array<AppLinksApple> | undefined\n  iphone?: AppLinksApple | Array<AppLinksApple> | undefined\n  ipad?: AppLinksApple | Array<AppLinksApple> | undefined\n  android?: AppLinksAndroid | Array<AppLinksAndroid> | undefined\n  windows_phone?: AppLinksWindows | Array<AppLinksWindows> | undefined\n  windows?: AppLinksWindows | Array<AppLinksWindows> | undefined\n  windows_universal?: AppLinksWindows | Array<AppLinksWindows> | undefined\n  web?: AppLinksWeb | Array<AppLinksWeb> | undefined\n}\nexport type ResolvedAppLinks = {\n  ios?: Array<AppLinksApple> | undefined\n  iphone?: Array<AppLinksApple> | undefined\n  ipad?: Array<AppLinksApple> | undefined\n  android?: Array<AppLinksAndroid> | undefined\n  windows_phone?: Array<AppLinksWindows> | undefined\n  windows?: Array<AppLinksWindows> | undefined\n  windows_universal?: Array<AppLinksWindows> | undefined\n  web?: Array<AppLinksWeb> | undefined\n}\nexport type AppLinksApple = {\n  url: string | URL\n  app_store_id?: string | number | undefined\n  app_name?: string | undefined\n}\nexport type AppLinksAndroid = {\n  package: string\n  url?: string | URL | undefined\n  class?: string | undefined\n  app_name?: string | undefined\n}\nexport type AppLinksWindows = {\n  url: string | URL\n  app_id?: string | undefined\n  app_name?: string | undefined\n}\nexport type AppLinksWeb = {\n  url: string | URL\n  should_fallback?: boolean | undefined\n}\n\n// Apple Itunes APp\n// https://developer.apple.com/documentation/webkit/promoting_apps_with_smart_app_banners\nexport type ItunesApp = {\n  appId: string\n  appArgument?: string | undefined\n}\n\n// Viewport meta structure\n// https://developer.mozilla.org/docs/Web/HTML/Viewport_meta_tag\n// intentionally leaving out user-scalable, use a string if you want that behavior\nexport type ViewportLayout = {\n  width?: string | number | undefined\n  height?: string | number | undefined\n  initialScale?: number | undefined\n  minimumScale?: number | undefined\n  maximumScale?: number | undefined\n  userScalable?: boolean | undefined\n  viewportFit?: 'auto' | 'cover' | 'contain' | undefined\n  interactiveWidget?:\n    | 'resizes-visual'\n    | 'resizes-content'\n    | 'overlays-content'\n    | undefined\n}\n\n// Apple Web App\n// https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html\n// https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/ConfiguringWebApplications/ConfiguringWebApplications.html\nexport type AppleWebApp = {\n  // default true\n  capable?: boolean | undefined\n  title?: string | undefined\n  startupImage?: AppleImage | Array<AppleImage> | undefined\n  // default \"default\"\n  statusBarStyle?: 'default' | 'black' | 'black-translucent' | undefined\n}\nexport type AppleImage = string | AppleImageDescriptor\nexport type AppleImageDescriptor = {\n  url: string\n  media?: string | undefined\n}\n\nexport type ResolvedAppleWebApp = {\n  capable: boolean\n  title?: string | null | undefined\n  startupImage?: AppleImageDescriptor[] | null | undefined\n  statusBarStyle?: 'default' | 'black' | 'black-translucent' | undefined\n}\n\nexport type Facebook = FacebookAppId | FacebookAdmins\nexport type FacebookAppId = {\n  appId: string\n  admins?: never | undefined\n}\nexport type FacebookAdmins = {\n  appId?: never | undefined\n  admins: string | string[]\n}\nexport type ResolvedFacebook = {\n  appId?: string | undefined\n  admins?: string[] | undefined\n}\n\nexport type Pinterest = PinterestRichPin\nexport type PinterestRichPin = {\n  richPin: string | boolean\n}\n\nexport type ResolvedPinterest = {\n  richPin?: string\n}\n\n// Format Detection\n// This is a poorly specified metadata export type that is supposed to\n// control whether the device attempts to conver text that matches\n// certain formats into links for action. The most supported example\n// is how mobile devices detect phone numbers and make them into links\n// that can initiate a phone call\n// https://www.goodemailcode.com/email-code/template.html\nexport type FormatDetection = {\n  telephone?: boolean | undefined\n  date?: boolean | undefined\n  address?: boolean | undefined\n  email?: boolean | undefined\n  url?: boolean | undefined\n}\n"], "names": [], "mappings": "AAAA,kFAAkF;AAClF,kCAAkC;AAClC,wEAAwE"}