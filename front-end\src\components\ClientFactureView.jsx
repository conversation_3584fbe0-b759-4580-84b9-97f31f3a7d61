import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Box,
  Typography,
  Button,
  Grid,
  Paper,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  alpha,
  Avatar,
  Stack,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  PictureAsPdf as PdfIcon,
  Print as PrintIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  Notes as NotesIcon,
  Payment as PaymentIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import factureService from '../services/factureService';
import { formatDate, formatCurrency, formatStatut } from '../utils/formatters';

const ClientFactureView = () => {
  const theme = useTheme();
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [facture, setFacture] = useState(null);
  const [error, setError] = useState(null);
  const [openRefuseDialog, setOpenRefuseDialog] = useState(false);
  const [motifRefus, setMotifRefus] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    const fetchFacture = async () => {
      try {
        setLoading(true);
        const response = await factureService.getFactureById(id);
        console.log('Facture récupérée du serveur:', response);
        console.log('ID client connecté:', currentUser.clientId);

        // Vérifier que la facture appartient bien au client connecté
        if (!response.clientId) {
          console.error('La facture n\'a pas de clientId');
          setError('Erreur: Cette facture n\'est pas associée à un client');
          setLoading(false);
          return;
        }

        console.log('Email de l\'utilisateur connecté:', currentUser.email);

        // Extraire les IDs en tant que chaînes de caractères
        let factureClientId;
        let factureClientEmail = '';

        if (typeof response.clientId === 'object' && response.clientId !== null) {
          factureClientId = response.clientId._id ? response.clientId._id.toString() : '';
          factureClientEmail = response.clientId.email || '';
          console.log('ID client de la facture (objet):', factureClientId);
          console.log('Email du client de la facture:', factureClientEmail);
        } else {
          factureClientId = response.clientId.toString();
          console.log('ID client de la facture (chaîne):', factureClientId);
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId._id) {
          userClientId = currentUser.clientId._id.toString();
          console.log('ID client de l\'utilisateur (objet):', userClientId);
        } else {
          userClientId = currentUser.clientId.toString();
          console.log('ID client de l\'utilisateur (chaîne):', userClientId);
        }

        console.log('Comparaison des IDs:', factureClientId, '===', userClientId);
        console.log('Comparaison des emails:', factureClientEmail, '===', currentUser.email);

        // Vérifier si les IDs correspondent OU si les emails correspondent
        const matchById = factureClientId === userClientId;
        const matchByEmail = factureClientEmail && currentUser.email &&
                            factureClientEmail.toLowerCase() === currentUser.email.toLowerCase();

        if (matchById || matchByEmail) {
          console.log('Accès autorisé (correspondance par ID ou email)');
          setFacture(response);
        } else {
          console.error('Ni les IDs ni les emails ne correspondent');
          console.log('ID client de la facture:', factureClientId);
          console.log('ID client de l\'utilisateur:', userClientId);
          console.log('Email client de la facture:', factureClientEmail);
          console.log('Email de l\'utilisateur:', currentUser.email);
          setError('Vous n\'avez pas accès à cette facture');
        }

        setLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement de la facture:', error);
        setError('Erreur lors du chargement de la facture');
        setLoading(false);
      }
    };

    if (id && currentUser && currentUser.clientId) {
      fetchFacture();
    } else {
      console.error('Utilisateur non connecté ou sans clientId');
      setError('Erreur: Vous devez être connecté pour accéder à cette facture');
      setLoading(false);
    }
  }, [id, currentUser]);

  const handleGoBack = () => {
    navigate('/client/factures');
  };

  const handleGeneratePdf = async () => {
    try {
      await factureService.generatePdf(id);
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
    }
  };

  const handlePrint = async () => {
    try {
      await factureService.printFacture(id);
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
    }
  };

  const handleAccepterFacture = async () => {
    try {
      const response = await factureService.accepterFacture(id);
      setFacture(response);
      setSnackbar({
        open: true,
        message: 'Facture acceptée avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de l\'acceptation de la facture:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'acceptation de la facture',
        severity: 'error'
      });
    }
  };

  const handleRefuserFacture = async () => {
    try {
      const response = await factureService.refuserFacture(id, motifRefus);
      setFacture(response);
      setOpenRefuseDialog(false);
      setMotifRefus('');
      setSnackbar({
        open: true,
        message: 'Facture refusée avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors du refus de la facture:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors du refus de la facture',
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          sx={{ mt: 2 }}
        >
          Retour aux factures
        </Button>
      </Box>
    );
  }

  if (!facture) {
    return (
      <Box sx={{ mt: 4 }}>
        <Alert severity="warning">Facture non trouvée</Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          sx={{ mt: 2 }}
        >
          Retour aux factures
        </Button>
      </Box>
    );
  }

  return (
    <Box component={motion.div}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
        >
          Retour aux factures
        </Button>

        <Box>
          <Button
            variant="outlined"
            startIcon={<PdfIcon />}
            onClick={handleGeneratePdf}
            sx={{ mr: 1 }}
          >
            Télécharger PDF
          </Button>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
          >
            Imprimer
          </Button>
        </Box>
      </Box>

      {/* En-tête de la facture */}
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <Box sx={{
          p: 3,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          bgcolor: alpha(theme.palette.primary.main, 0.03)
        }}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item>
              <Avatar
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  width: 48,
                  height: 48
                }}
              >
                <ReceiptIcon />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h5" fontWeight="bold">
                Facture {facture.numero}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                <CalendarIcon fontSize="small" sx={{ color: 'text.secondary', mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Émise le {formatDate(facture.dateEmission)}
                </Typography>
                <Chip
                  label={formatStatut(facture.statut)}
                  color={
                    facture.statut === 'PAID' ? 'success' :
                    facture.statut === 'DRAFT' ? 'info' : 'warning'
                  }
                  size="small"
                  sx={{ ml: 2 }}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>

        <CardContent>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Émetteur
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {facture.vendeurId ? facture.vendeurId.nom : 'N/A'}
              </Typography>
              {facture.vendeurId && (
                <>
                  <Typography variant="body2">{facture.vendeurId.email || 'N/A'}</Typography>
                  <Typography variant="body2">{facture.vendeurId.telephone || 'N/A'}</Typography>
                </>
              )}
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Client
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {facture.clientId ? facture.clientId.nom : 'N/A'}
              </Typography>
              {facture.clientId && (
                <>
                  <Typography variant="body2">{facture.clientId.email || 'N/A'}</Typography>
                  <Typography variant="body2">{facture.clientId.adresse || 'N/A'}</Typography>
                  <Typography variant="body2">{facture.clientId.telephone || 'N/A'}</Typography>
                </>
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Détails de la facture */}
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Détails de la facture
          </Typography>

          <TableContainer component={Paper} elevation={0} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.03) }}>
                  <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Quantité</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Prix unitaire</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Total</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {facture.lignes && facture.lignes.map((ligne, index) => (
                  <TableRow key={index}>
                    <TableCell>{ligne.description}</TableCell>
                    <TableCell align="right">{ligne.quantite}</TableCell>
                    <TableCell align="right">{formatCurrency(ligne.prixUnitaire)}</TableCell>
                    <TableCell align="right">{formatCurrency(ligne.quantite * ligne.prixUnitaire)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Grid container spacing={1} sx={{ maxWidth: 300 }}>
              <Grid item xs={6}>
                <Typography variant="body2" align="right">
                  Sous-total:
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" align="right" fontWeight="medium">
                  {formatCurrency(facture.totalHT || 0)}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body2" align="right">
                  TVA ({facture.tauxTVA}%):
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" align="right" fontWeight="medium">
                  {formatCurrency(facture.montantTVA || 0)}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="subtitle1" align="right" fontWeight="bold">
                  Total:
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1" align="right" fontWeight="bold">
                  {formatCurrency(facture.total)}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>

      {/* Notes */}
      {facture.notes && (
        <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <NotesIcon sx={{ color: theme.palette.text.secondary, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold">
                Notes
              </Typography>
            </Box>
            <Typography variant="body2">
              {facture.notes}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Boutons d'action (Accepter/Refuser) */}
      {facture.statut !== 'PAID' && facture.statut !== 'ACCEPTED' && facture.statut !== 'REJECTED' && (
        <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PaymentIcon sx={{ color: theme.palette.text.secondary, mr: 1 }} />
              <Typography variant="h6" fontWeight="bold">
                Actions
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Veuillez accepter ou refuser cette facture.
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                color="error"
                startIcon={<CancelIcon />}
                onClick={() => setOpenRefuseDialog(true)}
              >
                Refuser
              </Button>
              <Button
                variant="contained"
                color="success"
                startIcon={<CheckCircleIcon />}
                onClick={handleAccepterFacture}
              >
                Accepter
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Dialog pour le refus de facture */}
      <Dialog open={openRefuseDialog} onClose={() => setOpenRefuseDialog(false)}>
        <DialogTitle>Refuser la facture</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Veuillez indiquer le motif de refus de cette facture.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label="Motif de refus"
            type="text"
            fullWidth
            multiline
            rows={4}
            value={motifRefus}
            onChange={(e) => setMotifRefus(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenRefuseDialog(false)}>Annuler</Button>
          <Button onClick={handleRefuserFacture} color="error">Confirmer le refus</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar pour les notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={snackbar.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Box>
  );
};

export default ClientFactureView;
