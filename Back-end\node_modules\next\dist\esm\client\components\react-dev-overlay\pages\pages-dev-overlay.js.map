{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/pages-dev-overlay.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { PagesDevOverlayErrorBoundary } from './pages-dev-overlay-error-boundary'\nimport { usePagesDevOverlay } from './hooks'\nimport { FontStyles } from '../font/font-styles'\nimport { DevOverlay } from '../ui/dev-overlay'\n\nexport type ErrorType = 'runtime' | 'build'\n\nexport type PagesDevOverlayType = typeof PagesDevOverlay\n\ninterface PagesDevOverlayProps {\n  children?: React.ReactNode\n}\n\nexport function PagesDevOverlay({ children }: PagesDevOverlayProps) {\n  const { state, onComponentError } = usePagesDevOverlay()\n\n  const [isErrorOverlayOpen, setIsErrorOverlayOpen] = useState(true)\n\n  return (\n    <>\n      <PagesDevOverlayErrorBoundary onError={onComponentError}>\n        {children ?? null}\n      </PagesDevOverlayErrorBoundary>\n\n      {/* Fonts can only be loaded outside the Shadow DOM. */}\n      <FontStyles />\n      <DevOverlay\n        state={state}\n        isErrorOverlayOpen={isErrorOverlayOpen}\n        setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n      />\n    </>\n  )\n}\n"], "names": ["useState", "PagesDevOverlayErrorBoundary", "usePagesDevOverlay", "FontStyles", "DevOverlay", "<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "state", "onComponentError", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "onError"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,QAAO;AAChC,SAASC,4BAA4B,QAAQ,qCAAoC;AACjF,SAASC,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,UAAU,QAAQ,oBAAmB;AAU9C,OAAO,SAASC,gBAAgB,KAAkC;IAAlC,IAAA,EAAEC,QAAQ,EAAwB,GAAlC;IAC9B,MAAM,EAAEC,KAAK,EAAEC,gBAAgB,EAAE,GAAGN;IAEpC,MAAM,CAACO,oBAAoBC,sBAAsB,GAAGV,SAAS;IAE7D,qBACE;;0BACE,KAACC;gBAA6BU,SAASH;0BACpCF,mBAAAA,WAAY;;0BAIf,KAACH;0BACD,KAACC;gBACCG,OAAOA;gBACPE,oBAAoBA;gBACpBC,uBAAuBA;;;;AAI/B"}