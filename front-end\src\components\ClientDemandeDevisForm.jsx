import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Divider,
  IconButton,
  MenuItem,
  Snackbar,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Autocomplete,
  CircularProgress,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Send as SendIcon,
  ArrowBack as ArrowBackIcon,
  ShoppingCart as ShoppingCartIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import devisService from '../services/devisService';
import produitService from '../services/produitService';
import { formatCurrency } from '../utils/formatters';

const ClientDemandeDevisForm = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [produits, setProduits] = useState([]);
  const [selectedProduit, setSelectedProduit] = useState(null);
  const [quantite, setQuantite] = useState(1);

  const [demande, setDemande] = useState({
    lignes: [],
    notes: '',
    estDemandeClient: true
  });

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  useEffect(() => {
    const fetchProduits = async () => {
      try {
        setLoading(true);
        const data = await produitService.getProduits();
        setProduits(data || []);
      } catch (error) {
        console.error('Erreur lors du chargement des produits:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors du chargement des produits',
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProduits();
  }, []);

  const handleAddLigne = () => {
    if (!selectedProduit) return;

    const newLigne = {
      produit: selectedProduit._id,
      description: selectedProduit.nom,
      quantite: parseInt(quantite, 10) || 1,
      prixUnitaire: 0, // Le prix sera défini par le vendeur
      montantHT: 0,
      montantTTC: 0
    };

    setDemande(prev => ({
      ...prev,
      lignes: [...prev.lignes, newLigne]
    }));

    setSelectedProduit(null);
    setQuantite(1);
  };

  const handleRemoveLigne = (index) => {
    setDemande(prev => ({
      ...prev,
      lignes: prev.lignes.filter((_, i) => i !== index)
    }));
  };



  const handleSubmit = async () => {
    try {
      if (demande.lignes.length === 0) {
        setSnackbar({
          open: true,
          message: 'Veuillez ajouter au moins un produit à votre demande',
          severity: 'error'
        });
        return;
      }

      setLoading(true);

      // Préparer les données de la demande
      const demandeData = {
        ...demande,
        clientId: currentUser.id,
        statut: 'WAITING_APPROVAL',
        total: 0, // Sera calculé par le vendeur
        tauxTVA: 19 // Taux par défaut
      };

      // Envoyer la demande
      await devisService.createDemandeDevis(demandeData);

      setSnackbar({
        open: true,
        message: 'Votre demande de devis a été envoyée avec succès',
        severity: 'success'
      });

      // Rediriger vers la liste des devis après un court délai
      setTimeout(() => {
        navigate('/client/devis');
      }, 2000);

    } catch (error) {
      console.error('Erreur lors de l\'envoi de la demande:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'envoi de la demande de devis',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ mb: 4 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/client/devis')}
          sx={{ mb: 2 }}
        >
          Retour aux devis
        </Button>

        <Typography variant="h4" component="h1" gutterBottom>
          Demande de devis
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          Remplissez ce formulaire pour demander un devis personnalisé. Notre équipe vous répondra dans les plus brefs délais.
        </Typography>
      </Box>

      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <ShoppingCartIcon sx={{ mr: 1 }} />
            Produits et services
          </Typography>

          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={produits}
                getOptionLabel={(option) => option.nom}
                value={selectedProduit}
                onChange={(_, newValue) => setSelectedProduit(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Sélectionner un produit"
                    variant="outlined"
                    fullWidth
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                label="Quantité"
                type="number"
                value={quantite}
                onChange={(e) => setQuantite(e.target.value)}
                InputProps={{
                  inputProps: { min: 1 }
                }}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddLigne}
                disabled={!selectedProduit}
                fullWidth
                sx={{ height: '100%' }}
              >
                Ajouter
              </Button>
            </Grid>
          </Grid>

          {demande.lignes.length > 0 ? (
            <TableContainer component={Paper} elevation={0} sx={{ mb: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Produit</TableCell>
                    <TableCell align="center">Quantité</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {demande.lignes.map((ligne, index) => {
                    const produit = produits.find(p => p._id === ligne.produit);
                    return (
                      <TableRow key={index}>
                        <TableCell>{produit ? produit.nom : ligne.description}</TableCell>
                        <TableCell align="center">{ligne.quantite}</TableCell>
                        <TableCell align="right">
                          <IconButton
                            color="error"
                            onClick={() => handleRemoveLigne(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
              <Typography>Aucun produit ajouté</Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <DescriptionIcon sx={{ mr: 1 }} />
            Informations complémentaires
          </Typography>

          <TextField
            label="Notes et commentaires"
            multiline
            rows={4}
            value={demande.notes}
            onChange={(e) => setDemande(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Précisez vos besoins spécifiques, questions ou toute autre information utile pour votre devis..."
            fullWidth
            sx={{ mb: 2 }}
          />
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button
          variant="outlined"
          onClick={() => navigate('/client/devis')}
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          startIcon={<SendIcon />}
          onClick={handleSubmit}
          disabled={loading || demande.lignes.length === 0}
        >
          {loading ? <CircularProgress size={24} /> : 'Envoyer la demande'}
        </Button>
      </Box>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </motion.div>
  );
};

export default ClientDemandeDevisForm;
