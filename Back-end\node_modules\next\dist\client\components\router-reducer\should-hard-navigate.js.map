{"version": 3, "sources": ["../../../../src/client/components/router-reducer/should-hard-navigate.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  FlightDataPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\n\n// TODO-APP: flightSegmentPath will be empty in case of static response, needs to be handled.\nexport function shouldHardNavigate(\n  flightSegmentPath: FlightDataPath,\n  flightRouterState: FlightRouterState\n): boolean {\n  const [segment, parallelRoutes] = flightRouterState\n  // TODO-APP: Check if `as` can be replaced.\n  const [currentSegment, parallelRouteKey] = flightSegmentPath as [\n    Segment,\n    string,\n  ]\n\n  // Check if current segment matches the existing segment.\n  if (!matchSegment(currentSegment, segment)) {\n    // If dynamic parameter in tree doesn't match up with segment path a hard navigation is triggered.\n    if (Array.isArray(currentSegment)) {\n      return true\n    }\n\n    // If the existing segment did not match soft navigation is triggered.\n    return false\n  }\n  const lastSegment = flightSegmentPath.length <= 2\n\n  if (lastSegment) {\n    return false\n  }\n\n  return shouldHardNavigate(\n    getNextFlightSegmentPath(flightSegmentPath),\n    parallelRoutes[parallelRouteKey]\n  )\n}\n"], "names": ["shouldHardNavigate", "flightSegmentPath", "flightRouterState", "segment", "parallelRoutes", "currentSegment", "parallelRouteKey", "matchSegment", "Array", "isArray", "lastSegment", "length", "getNextFlightSegmentPath"], "mappings": ";;;;+BASgBA;;;eAAAA;;;mCAJyB;+BACZ;AAGtB,SAASA,mBACdC,iBAAiC,EACjCC,iBAAoC;IAEpC,MAAM,CAACC,SAASC,eAAe,GAAGF;IAClC,2CAA2C;IAC3C,MAAM,CAACG,gBAAgBC,iBAAiB,GAAGL;IAK3C,yDAAyD;IACzD,IAAI,CAACM,IAAAA,2BAAY,EAACF,gBAAgBF,UAAU;QAC1C,kGAAkG;QAClG,IAAIK,MAAMC,OAAO,CAACJ,iBAAiB;YACjC,OAAO;QACT;QAEA,sEAAsE;QACtE,OAAO;IACT;IACA,MAAMK,cAAcT,kBAAkBU,MAAM,IAAI;IAEhD,IAAID,aAAa;QACf,OAAO;IACT;IAEA,OAAOV,mBACLY,IAAAA,2CAAwB,EAACX,oBACzBG,cAAc,CAACE,iBAAiB;AAEpC"}