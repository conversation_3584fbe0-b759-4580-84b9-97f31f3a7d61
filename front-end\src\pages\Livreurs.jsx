import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  MenuItem,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  LocalShipping as TruckIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import livreurService from '../services/livreurService';
import LivreurForm from '../components/LivreurForm';

const Livreurs = () => {
  const { currentUser } = useAuth();
  const [livreurs, setLivreurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statutFilter, setStatutFilter] = useState('');
  const [disponibleFilter, setDisponibleFilter] = useState('');
  
  // Dialog states
  const [openForm, setOpenForm] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedLivreur, setSelectedLivreur] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  // Charger les livreurs
  const loadLivreurs = async () => {
    try {
      setLoading(true);
      const params = {};
      if (searchTerm) params.search = searchTerm;
      if (statutFilter) params.statut = statutFilter;
      if (disponibleFilter !== '') params.disponible = disponibleFilter;

      const data = await livreurService.getAllLivreurs(params);
      setLivreurs(data);
    } catch (error) {
      console.error('Erreur lors du chargement des livreurs:', error);
      setError('Erreur lors du chargement des livreurs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLivreurs();
  }, [searchTerm, statutFilter, disponibleFilter]);

  // Gérer l'ajout d'un livreur
  const handleAddLivreur = () => {
    setSelectedLivreur(null);
    setIsEditing(false);
    setOpenForm(true);
  };

  // Gérer la modification d'un livreur
  const handleEditLivreur = (livreur) => {
    setSelectedLivreur(livreur);
    setIsEditing(true);
    setOpenForm(true);
  };

  // Gérer la suppression d'un livreur
  const handleDeleteLivreur = (livreur) => {
    setSelectedLivreur(livreur);
    setOpenDeleteDialog(true);
  };

  // Confirmer la suppression
  const confirmDelete = async () => {
    try {
      await livreurService.deleteLivreur(selectedLivreur._id);
      setSuccess('Livreur supprimé avec succès');
      loadLivreurs();
      setOpenDeleteDialog(false);
      setSelectedLivreur(null);
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      setError(error.response?.data?.message || 'Erreur lors de la suppression du livreur');
    }
  };

  // Gérer la soumission du formulaire
  const handleFormSubmit = async (livreurData) => {
    try {
      if (isEditing) {
        await livreurService.updateLivreur(selectedLivreur._id, livreurData);
        setSuccess('Livreur modifié avec succès');
      } else {
        await livreurService.createLivreur(livreurData);
        setSuccess('Livreur créé avec succès');
      }
      loadLivreurs();
      setOpenForm(false);
      setSelectedLivreur(null);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(error.response?.data?.message || 'Erreur lors de la sauvegarde du livreur');
    }
  };

  // Vérifier si l'utilisateur peut modifier les livreurs
  const canModify = currentUser?.role === 'RESPONSABLE' || currentUser?.role === 'ADMIN';

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          Gestion des Livreurs
        </Typography>
        {canModify && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddLivreur}
            sx={{ borderRadius: 2 }}
          >
            Nouveau Livreur
          </Button>
        )}
      </Box>

      {/* Filtres */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Rechercher un livreur..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Statut"
                value={statutFilter}
                onChange={(e) => setStatutFilter(e.target.value)}
              >
                <MenuItem value="">Tous les statuts</MenuItem>
                {livreurService.getStatutOptions().map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Disponibilité"
                value={disponibleFilter}
                onChange={(e) => setDisponibleFilter(e.target.value)}
              >
                <MenuItem value="">Toutes</MenuItem>
                <MenuItem value="true">Disponible</MenuItem>
                <MenuItem value="false">Non disponible</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm('');
                  setStatutFilter('');
                  setDisponibleFilter('');
                }}
              >
                Réinitialiser
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Liste des livreurs */}
      <Card>
        <CardContent>
          {loading ? (
            <Typography>Chargement...</Typography>
          ) : livreurs.length === 0 ? (
            <Typography>Aucun livreur trouvé</Typography>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Livreur</TableCell>
                    <TableCell>Contact</TableCell>
                    <TableCell>Véhicule</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Disponibilité</TableCell>
                    <TableCell>Statistiques</TableCell>
                    {canModify && <TableCell align="center">Actions</TableCell>}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {livreurs.map((livreur) => {
                    const formatted = livreurService.formatLivreurForDisplay(livreur);
                    return (
                      <TableRow key={livreur._id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                              <PersonIcon />
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="bold">
                                {formatted.nomComplet}
                              </Typography>
                              {livreur.cin && (
                                <Typography variant="caption" color="text.secondary">
                                  CIN: {livreur.cin}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <PhoneIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2">{livreur.telephone}</Typography>
                            </Box>
                            {livreur.email && (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <EmailIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2">{livreur.email}</Typography>
                              </Box>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <TruckIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">{formatted.vehiculeInfo}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={formatted.statutLabel}
                            color={livreurService.getStatutColor(livreur.statut)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={formatted.disponibiliteLabel}
                            color={livreur.disponible ? 'success' : 'default'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {livreur.statistiques?.nombreLivraisons || 0} livraisons
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Taux: {formatted.tauxReussiteFormate}
                          </Typography>
                        </TableCell>
                        {canModify && (
                          <TableCell align="center">
                            <Tooltip title="Modifier">
                              <IconButton
                                size="small"
                                onClick={() => handleEditLivreur(livreur)}
                                color="primary"
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Supprimer">
                              <IconButton
                                size="small"
                                onClick={() => handleDeleteLivreur(livreur)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Dialog de formulaire */}
      <Dialog open={openForm} onClose={() => setOpenForm(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {isEditing ? 'Modifier le livreur' : 'Nouveau livreur'}
        </DialogTitle>
        <DialogContent>
          <LivreurForm
            livreur={selectedLivreur}
            onSubmit={handleFormSubmit}
            onCancel={() => setOpenForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog de confirmation de suppression */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer le livreur{' '}
            <strong>
              {selectedLivreur && `${selectedLivreur.prenom} ${selectedLivreur.nom}`}
            </strong> ?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Annuler</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifications */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
      >
        <Alert severity="error" onClose={() => setError('')}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess('')}
      >
        <Alert severity="success" onClose={() => setSuccess('')}>
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Livreurs;
