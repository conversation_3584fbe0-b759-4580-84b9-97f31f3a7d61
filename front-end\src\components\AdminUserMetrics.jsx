import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  useTheme,
  alpha,
  Button,
  Avatar,
  Stack,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  PeopleAlt as PeopleAltIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  SupervisorAccount as SupervisorAccountIcon,
  MoreVert as MoreVertIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  <PERSON>Chart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { formatCurrency } from '../utils/formatters';
import { useNavigate } from 'react-router-dom';
import userMetricsService from '../services/userMetricsService';

// Fallback mock data in case API fails
const mockUserRoleData = [
  { name: 'Vendeurs', value: 35, color: '#4CAF50' },
  { name: 'Entreprises/Clients', value: 110, color: '#2196F3' },
];

const mockTopVendors = [
  { id: '1', name: 'Martin Dupont', revenue: 45680, invoiceCount: 32, clientCount: 8, growth: 12 },
  { id: '2', name: 'Sophie Laurent', revenue: 38250, invoiceCount: 28, clientCount: 6, growth: 8 },
  { id: '3', name: 'Thomas Blanc', revenue: 32780, invoiceCount: 24, clientCount: 5, growth: -3 },
  { id: '4', name: 'Julie Moreau', revenue: 28950, invoiceCount: 22, clientCount: 7, growth: 5 },
  { id: '5', name: 'Alexandre Petit', revenue: 25340, invoiceCount: 18, clientCount: 4, growth: 2 },
];

const mockClientGrowthData = [
  { month: 'Jan', newClients: 8, totalClients: 65 },
  { month: 'Fév', newClients: 5, totalClients: 70 },
  { month: 'Mar', newClients: 10, totalClients: 80 },
  { month: 'Avr', newClients: 7, totalClients: 87 },
  { month: 'Mai', newClients: 12, totalClients: 99 },
  { month: 'Juin', newClients: 11, totalClients: 110 },
];

const CHART_COLORS = {
  vendeurs: '#4CAF50',
  clients: '#2196F3',
  revenue: '#FF9800',
  growth: '#9C27B0',
};

const AdminUserMetrics = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [userRoleData, setUserRoleData] = useState(mockUserRoleData);
  const [topVendors, setTopVendors] = useState(mockTopVendors);
  const [clientGrowthData, setClientGrowthData] = useState(mockClientGrowthData);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch data from API
        const [userRoleResponse, topVendorsResponse, clientGrowthResponse] = await Promise.all([
          userMetricsService.getUsersByRole(),
          userMetricsService.getTopVendors(5, 'all-time'),
          userMetricsService.getClientGrowth(6)
        ]);

        // Update state with API data
        setUserRoleData(userRoleResponse);
        setTopVendors(topVendorsResponse);
        setClientGrowthData(clientGrowthResponse);

        setLoading(false);
      } catch (error) {
        console.error('Error fetching user metrics:', error);
        // Fall back to mock data if API fails
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const totalUsers = userRoleData.reduce((sum, item) => sum + item.value, 0);

  return (
    <Box component={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Gestion des Utilisateurs
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Users by Role */}
          <Grid item xs={12} md={6} component={motion.div} variants={itemVariants}>
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                height: '100%',
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h6" fontWeight="600">
                    Utilisateurs par Rôle
                  </Typography>
                }
                subheader={`Total: ${totalUsers} utilisateurs actifs`}
                sx={{ px: 3, pt: 3, pb: 1 }}
              />
              <Divider />
              <CardContent sx={{ height: 300, p: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={userRoleData}
                      cx="50%"
                      cy="50%"
                      innerRadius={70}
                      outerRadius={90}
                      paddingAngle={3}
                      dataKey="value"
                      label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                      labelLine={true}
                    >
                      {userRoleData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      formatter={(value, entry, index) => (
                        <span style={{ color: theme.palette.text.primary, fontWeight: 500 }}>
                          {value}
                        </span>
                      )}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Top Performing Vendors */}
          <Grid item xs={12} md={6} component={motion.div} variants={itemVariants}>
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                height: '100%',
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h6" fontWeight="600">
                    Top Vendeurs
                  </Typography>
                }
                subheader="Classement par chiffre d'affaires"
                sx={{ px: 3, pt: 3, pb: 1 }}
                action={
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => navigate('/admin/utilisateurs')}
                    sx={{ fontWeight: 'medium', mr: 1 }}
                  >
                    Voir tout
                  </Button>
                }
              />
              <Divider />
              <CardContent sx={{ p: 0 }}>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Vendeur</TableCell>
                        <TableCell align="right">Chiffre d'affaires</TableCell>
                        <TableCell align="right">Factures</TableCell>
                        <TableCell align="right">Clients</TableCell>
                        <TableCell align="right">Évolution</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {topVendors.map((vendor) => (
                        <TableRow key={vendor.id} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Avatar
                                sx={{
                                  width: 32,
                                  height: 32,
                                  mr: 1,
                                  bgcolor: theme.palette.primary.main
                                }}
                              >
                                {vendor.name.charAt(0)}
                              </Avatar>
                              {vendor.name}
                            </Box>
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(vendor.revenue)}
                          </TableCell>
                          <TableCell align="right">{vendor.invoiceCount}</TableCell>
                          <TableCell align="right">{vendor.clientCount}</TableCell>
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                              {vendor.growth > 0 ? (
                                <ArrowUpwardIcon fontSize="small" color="success" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" color="error" />
                              )}
                              <Typography
                                variant="body2"
                                color={vendor.growth > 0 ? 'success.main' : 'error.main'}
                                fontWeight="medium"
                              >
                                {Math.abs(vendor.growth)}%
                              </Typography>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Client Growth Rate */}
          <Grid item xs={12} component={motion.div} variants={itemVariants}>
            <Card
              elevation={0}
              sx={{
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h6" fontWeight="600">
                    Croissance des Clients
                  </Typography>
                }
                subheader="Évolution du nombre de clients sur les 6 derniers mois"
                sx={{ px: 3, pt: 3, pb: 1 }}
              />
              <Divider />
              <CardContent sx={{ height: 300, p: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={clientGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis
                      dataKey="month"
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <YAxis
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickLine={false}
                      axisLine={{ stroke: theme.palette.divider }}
                    />
                    <RechartsTooltip
                      formatter={(value, name) => [value, name === 'newClients' ? 'Nouveaux Clients' : 'Total Clients']}
                      labelFormatter={(label) => `Mois: ${label}`}
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: theme.shadows[3]
                      }}
                    />
                    <Legend
                      formatter={(value) => value === 'newClients' ? 'Nouveaux Clients' : 'Total Clients'}
                    />
                    <Bar
                      dataKey="newClients"
                      fill={CHART_COLORS.growth}
                      radius={[4, 4, 0, 0]}
                      name="newClients"
                    />
                    <Bar
                      dataKey="totalClients"
                      fill={CHART_COLORS.clients}
                      radius={[4, 4, 0, 0]}
                      name="totalClients"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default AdminUserMetrics;
