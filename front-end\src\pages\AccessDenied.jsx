import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Button, Paper, CircularProgress } from '@mui/material';
import { Block as BlockIcon, ArrowBack, Home } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const AccessDenied = () => {
  const navigate = useNavigate();
  const { currentUser, isAuthenticated, loading } = useAuth();

  // Vérifier l'état d'authentification au chargement
  useEffect(() => {
    // Si l'utilisateur n'est pas authentifié et que le chargement est terminé,
    // rediriger vers la page de connexion
    if (!isAuthenticated && !loading) {
      // Attendre un court instant pour permettre à l'utilisateur de voir la page
      const timer = setTimeout(() => {
        navigate('/login');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, loading, navigate]);

  const handleGoHome = () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (currentUser && currentUser.role) {
      if (currentUser.role === 'ADMIN') {
        navigate('/admin/analytics');
      } else if (currentUser.role === 'ENTREPRISE') {
        navigate('/entreprise/analytics');
      } else if (currentUser.role === 'VENDEUR') {
        navigate('/vendeur/analytics');
      } else if (currentUser.role === 'CLIENT') {
        navigate('/client/dashboard');
      } else {
        navigate('/');
      }
    } else {
      navigate('/');
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%)',
        padding: 3,
      }}
    >
      <Paper
        elevation={6}
        sx={{
          padding: 4,
          maxWidth: 500,
          textAlign: 'center',
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        }}
      >
        <BlockIcon
          color="error"
          sx={{
            fontSize: 80,
            mb: 2,
            p: 1,
            borderRadius: '50%',
            backgroundColor: 'rgba(244, 67, 54, 0.1)',
          }}
        />
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Accès refusé
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Vous n'avez pas les permissions nécessaires pour accéder à cette page.
          Veuillez contacter l'administrateur si vous pensez qu'il s'agit d'une erreur.
        </Typography>

        {!isAuthenticated && !loading && (
          <Typography variant="body2" color="primary" sx={{ mt: 2, mb: 2 }}>
            Vous allez être redirigé vers la page de connexion dans quelques secondes...
            {loading && <CircularProgress size={16} sx={{ ml: 1 }} />}
          </Typography>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 3 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            startIcon={<Home />}
            onClick={handleGoHome}
            sx={{
              borderRadius: 2,
              padding: '10px 20px',
              fontWeight: 600,
              textTransform: 'none',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              '&:hover': {
                boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s',
            }}
          >
            Retour à l'accueil
          </Button>

          <Button
            variant="outlined"
            color="primary"
            size="large"
            startIcon={<ArrowBack />}
            onClick={() => navigate(-1)}
            sx={{
              borderRadius: 2,
              padding: '10px 20px',
              fontWeight: 600,
              textTransform: 'none',
              '&:hover': {
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s',
            }}
          >
            Page précédente
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default AccessDenied;
