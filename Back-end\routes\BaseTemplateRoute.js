const express = require('express');
const router = express.Router();
const BaseTemplate = require('../models/BaseTemplateModel');
const { verifyToken } = require('../middleware/authMiddleware');

// Get all base templates (Admin only)
router.get('/base-templates', verifyToken, async (req, res) => {
    try {
        const { type } = req.query;
        let query = { isActive: true };

        if (type && ['facture', 'devis'].includes(type)) {
            query.type = type;
        }

        const templates = await BaseTemplate.find(query).sort({ type: 1, layout: 1 });
        res.status(200).json(templates);
    } catch (error) {
        console.error('Error fetching base templates:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des templates de base' });
    }
});

// Get a specific base template
router.get('/base-templates/:id', verifyToken, async (req, res) => {
    try {
        const template = await BaseTemplate.findById(req.params.id);
        if (!template) {
            return res.status(404).json({ error: 'Template de base non trouvé' });
        }
        res.status(200).json(template);
    } catch (error) {
        console.error('Error fetching base template:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération du template de base' });
    }
});

// Create a new base template (Admin only)
router.post('/base-templates', verifyToken, async (req, res) => {
    try {
        // Check if user is admin
        if (req.userRole !== 'ADMIN') {
            return res.status(403).json({ error: 'Accès refusé. Seuls les administrateurs peuvent créer des templates de base.' });
        }

        const templateData = req.body;

        // Validate required fields
        if (!templateData.name || !templateData.type || !templateData.layout) {
            return res.status(400).json({ error: 'Nom, type et layout sont requis' });
        }

        // Check if template with same name already exists
        const existingTemplate = await BaseTemplate.findOne({
            name: templateData.name
        });

        if (existingTemplate) {
            return res.status(400).json({ error: 'Un template avec ce nom existe déjà' });
        }

        const newTemplate = new BaseTemplate(templateData);
        await newTemplate.save();

        res.status(201).json(newTemplate);
    } catch (error) {
        console.error('Error creating base template:', error);
        res.status(500).json({ error: 'Erreur lors de la création du template de base' });
    }
});

// Update a base template (Admin only)
router.put('/base-templates/:id', verifyToken, async (req, res) => {
    try {
        // Check if user is admin
        if (req.userRole !== 'ADMIN') {
            return res.status(403).json({ error: 'Accès refusé. Seuls les administrateurs peuvent modifier des templates de base.' });
        }

        const template = await BaseTemplate.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        );

        if (!template) {
            return res.status(404).json({ error: 'Template de base non trouvé' });
        }

        res.status(200).json(template);
    } catch (error) {
        console.error('Error updating base template:', error);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du template de base' });
    }
});

// Delete a base template (Admin only)
router.delete('/base-templates/:id', verifyToken, async (req, res) => {
    try {
        // Check if user is admin
        if (req.userRole !== 'ADMIN') {
            return res.status(403).json({ error: 'Accès refusé. Seuls les administrateurs peuvent supprimer des templates de base.' });
        }

        const template = await BaseTemplate.findByIdAndDelete(req.params.id);

        if (!template) {
            return res.status(404).json({ error: 'Template de base non trouvé' });
        }

        res.status(200).json({ message: 'Template de base supprimé avec succès' });
    } catch (error) {
        console.error('Error deleting base template:', error);
        res.status(500).json({ error: 'Erreur lors de la suppression du template de base' });
    }
});

// Get templates by type and style for assignment
router.get('/base-templates/type/:type', verifyToken, async (req, res) => {
    try {
        const { type } = req.params;

        if (!['facture', 'devis'].includes(type)) {
            return res.status(400).json({ error: 'Type de template invalide' });
        }

        const templates = await BaseTemplate.find({
            type: type,
            isActive: true
        }).sort({ layout: 1 });

        res.status(200).json(templates);
    } catch (error) {
        console.error('Error fetching templates by type:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des templates par type' });
    }
});

module.exports = router;
