import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  useTheme,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
  Avatar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Business as BusinessIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  CalendarToday as CalendarIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getUsersByRole, deleteUser, getUserByIdWithPassword } from '../services/userService';

// Mock data for entreprises
const mockEntreprises = [
  {
    id: 1,
    nom: 'Entreprise ABC',
    email: '<EMAIL>',
    dateCreation: '2023-05-15',
    telephone: '+33 1 23 45 67 89',
    status: 'active',
    logo: null,
    factures: 12,
    devis: 8,
    adresse: '123 Rue de Paris, 75001 Paris',
    numeroFiscal: 'FR12345678900'
  },
  {
    id: 2,
    nom: 'Société XYZ',
    email: '<EMAIL>',
    dateCreation: '2023-05-28',
    telephone: '+33 1 34 56 78 90',
    status: 'active',
    logo: null,
    factures: 8,
    devis: 5,
    adresse: '456 Avenue des Champs-Élysées, 75008 Paris',
    numeroFiscal: 'FR98765432100'
  },
  {
    id: 3,
    nom: 'Tech Solutions',
    email: '<EMAIL>',
    dateCreation: '2023-06-10',
    telephone: '+33 1 45 67 89 01',
    status: 'inactive',
    logo: null,
    factures: 0,
    devis: 2,
    adresse: '789 Boulevard Haussmann, 75009 Paris',
    numeroFiscal: 'FR45678912300'
  },
  {
    id: 4,
    nom: 'Global Services',
    email: '<EMAIL>',
    dateCreation: '2023-06-15',
    telephone: '+33 1 56 78 90 12',
    status: 'active',
    logo: null,
    factures: 15,
    devis: 10,
    adresse: '321 Rue de Rivoli, 75004 Paris',
    numeroFiscal: 'FR78901234500'
  },
];

const EntreprisesPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // State
  const [entreprises, setEntreprises] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [entrepriseToDelete, setEntrepriseToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedEntreprise, setSelectedEntreprise] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Fetch entreprises on component mount
  useEffect(() => {
    const fetchEntreprises = async () => {
      try {
        const data = await getUsersByRole('RESPONSABLE');
        setEntreprises(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching entreprises:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors de la récupération des entreprises',
          severity: 'error'
        });
        setLoading(false);
      }
    };

    fetchEntreprises();
  }, []);

  // Handle search
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Filter entreprises based on search term
  const filteredEntreprises = entreprises.filter(entreprise => {
    return (
      entreprise.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entreprise.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entreprise.telephone.includes(searchTerm) ||
      entreprise.numeroFiscal.includes(searchTerm)
    );
  });

  // Handle menu open
  const handleMenuOpen = (event, entreprise) => {
    setAnchorEl(event.currentTarget);
    setSelectedEntreprise(entreprise);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedEntreprise(null);
  };

  // Handle view details
  const handleViewDetails = async () => {
    try {
      // Fetch user details with password
      if (selectedEntreprise && selectedEntreprise._id) {
        const userWithPassword = await getUserByIdWithPassword(selectedEntreprise._id);
        setSelectedEntreprise({...selectedEntreprise, ...userWithPassword});
      }
      setDetailsDialogOpen(true);
    } catch (error) {
      console.error('Error fetching user details with password:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la récupération des détails de l\'entreprise',
        severity: 'error'
      });
    }
  };

  // Handle edit entreprise
  const handleEditEntreprise = () => {
    if (selectedEntreprise) {
      navigate(`/admin/utilisateurs/edit/${selectedEntreprise._id}`);
    }
  };

  // Handle delete dialog open
  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setEntrepriseToDelete(null);
  };

  // Handle details dialog close
  const handleDetailsDialogClose = () => {
    setDetailsDialogOpen(false);
  };

  // Handle delete entreprise
  const handleDeleteEntreprise = async () => {
    if (selectedEntreprise) {
      try {
        setLoading(true);
        await deleteUser(selectedEntreprise._id);

        // Update local state
        setEntreprises(entreprises.filter(entreprise => entreprise._id !== selectedEntreprise._id));

        // Show success message
        setSnackbar({
          open: true,
          message: `L'entreprise ${selectedEntreprise.nom} a été supprimée avec succès.`,
          severity: 'success'
        });
      } catch (error) {
        console.error('Error deleting entreprise:', error);
        setSnackbar({
          open: true,
          message: `Erreur lors de la suppression de l'entreprise: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    }

    handleDeleteDialogClose();
  };

  // Handle add responsable d'entreprise
  const handleAddEntreprise = () => {
    navigate('/admin/utilisateurs/new?role=RESPONSABLE');
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };



  // Get initials for avatar
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Gestion des entreprises
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gérez les comptes entreprises de votre système.
          </Typography>
        </Box>

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddEntreprise}
          sx={{ borderRadius: 2 }}
        >
          Ajouter une entreprise
        </Button>
      </Box>

      {/* Search */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{ mb: 3 }}
      >
        <Card elevation={1}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <BusinessIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="medium">
                  {entreprises.length} Entreprises
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TextField
                  placeholder="Rechercher une entreprise..."
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  sx={{ width: 250 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    )
                  }}
                />

              </Box>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Entreprises Table */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }
        }}
      >
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Entreprise</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Téléphone</TableCell>
                <TableCell>N° Fiscal</TableCell>
                <TableCell>Factures</TableCell>
                <TableCell>Devis</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from(new Array(5)).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{
                          width: 36,
                          height: 36,
                          borderRadius: '50%',
                          bgcolor: '#f0f0f0',
                          mr: 2
                        }} />
                        <Box sx={{ height: 20, width: '70%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '90%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '60%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '50%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '30%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '30%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ height: 20, width: 20, bgcolor: '#f0f0f0', borderRadius: '50%', ml: 'auto' }} />
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredEntreprises.length === 0 ? (
                // No results
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                      Aucune entreprise trouvée
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                // Entreprise rows
                filteredEntreprises.map((entreprise) => (
                  <TableRow key={entreprise.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={entreprise.logo}
                          alt={entreprise.nom}
                          sx={{
                            width: 36,
                            height: 36,
                            mr: 2,
                            bgcolor: theme.palette.secondary.main
                          }}
                        >
                          {getInitials(entreprise.nom)}
                        </Avatar>
                        <Typography variant="body1" fontWeight="medium">
                          {entreprise.nom}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{entreprise.email}</TableCell>
                    <TableCell>{entreprise.telephone}</TableCell>
                    <TableCell>{entreprise.numeroFiscal}</TableCell>
                    <TableCell>{entreprise.factures || 0}</TableCell>
                    <TableCell>{entreprise.devis || 0}</TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Tooltip title="Modifier" arrow placement="top">
                          <IconButton
                            onClick={() => { setSelectedEntreprise(entreprise); handleEditEntreprise(); }}
                            size="small"
                            sx={{
                              bgcolor: 'primary.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'primary.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer" arrow placement="top">
                          <IconButton
                            onClick={() => { setSelectedEntreprise(entreprise); handleDeleteDialogOpen(); }}
                            size="small"
                            sx={{
                              bgcolor: 'error.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'error.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Menu supprimé */}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer l'entreprise "{selectedEntreprise?.nom}" ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>Annuler</Button>
          <Button onClick={handleDeleteEntreprise} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleDetailsDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Détails de l'entreprise
          <IconButton
            aria-label="close"
            onClick={handleDetailsDialogClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <MoreVertIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {selectedEntreprise && (
            <Box sx={{ pt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar
                  src={selectedEntreprise.logo}
                  alt={selectedEntreprise.nom}
                  sx={{
                    width: 64,
                    height: 64,
                    mr: 2,
                    bgcolor: theme.palette.secondary.main
                  }}
                >
                  {getInitials(selectedEntreprise.nom)}
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    {selectedEntreprise.nom}
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ mb: 2 }} />

              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Informations de contact
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">{selectedEntreprise.email}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">{selectedEntreprise.telephone}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                  <BusinessIcon fontSize="small" sx={{ mr: 1, mt: 0.5, color: 'text.secondary' }} />
                  <Typography variant="body2">{selectedEntreprise.adresse}</Typography>
                </Box>
              </Box>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Informations fiscales
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>N° Fiscal:</strong> {selectedEntreprise.numeroFiscal}
                </Typography>
              </Box>

              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Informations de connexion
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Mot de passe:</strong> <span style={{ fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: '2px 5px', borderRadius: '3px' }}>{selectedEntreprise.motDePasse || 'Non disponible'}</span>
                </Typography>
              </Box>



              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Statistiques
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Factures:</strong> {selectedEntreprise.factures}
                </Typography>
                <Typography variant="body2">
                  <strong>Devis:</strong> {selectedEntreprise.devis}
                </Typography>
                <Typography variant="body2">
                  <strong>Date d'ajout:</strong> {selectedEntreprise.dateCreation}
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDetailsDialogClose}>Fermer</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              handleDetailsDialogClose();
              if (selectedEntreprise) {
                navigate(`/admin/utilisateurs/edit/${selectedEntreprise._id}`);
              }
            }}
          >
            Modifier
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EntreprisesPage;
