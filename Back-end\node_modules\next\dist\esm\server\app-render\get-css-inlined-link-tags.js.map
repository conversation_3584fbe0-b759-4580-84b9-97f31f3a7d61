{"version": 3, "sources": ["../../../src/server/app-render/get-css-inlined-link-tags.tsx"], "sourcesContent": ["import type {\n  ClientReferenceManifest,\n  CssResource,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\n\n/**\n * Get external stylesheet link hrefs based on server CSS manifest.\n */\nexport function getLinkAndScriptTags(\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>,\n  filePath: string,\n  injectedCSS: Set<string>,\n  injectedScripts: Set<string>,\n  collectNewImports?: boolean\n): { styles: CssResource[]; scripts: string[] } {\n  const filePathWithoutExt = filePath.replace(/\\.[^.]+$/, '')\n  const cssChunks = new Set<CssResource>()\n  const jsChunks = new Set<string>()\n\n  const entryCSSFiles =\n    clientReferenceManifest.entryCSSFiles[filePathWithoutExt]\n  const entryJSFiles =\n    clientReferenceManifest.entryJSFiles?.[filePathWithoutExt] ?? []\n\n  if (entryCSSFiles) {\n    for (const css of entryCSSFiles) {\n      if (!injectedCSS.has(css.path)) {\n        if (collectNewImports) {\n          injectedCSS.add(css.path)\n        }\n        cssChunks.add(css)\n      }\n    }\n  }\n\n  if (entryJSFiles) {\n    for (const file of entryJSFiles) {\n      if (!injectedScripts.has(file)) {\n        if (collectNewImports) {\n          injectedScripts.add(file)\n        }\n        jsChunks.add(file)\n      }\n    }\n  }\n\n  return { styles: [...cssChunks], scripts: [...jsChunks] }\n}\n"], "names": ["getLinkAndScriptTags", "clientReferenceManifest", "filePath", "injectedCSS", "injectedScripts", "collectNewImports", "filePathWithoutExt", "replace", "cssChunks", "Set", "jsChunks", "entryCSSFiles", "entryJSFiles", "css", "has", "path", "add", "file", "styles", "scripts"], "mappings": "AAMA;;CAEC,GACD,OAAO,SAASA,qBACdC,uBAA8D,EAC9DC,QAAgB,EAChBC,WAAwB,EACxBC,eAA4B,EAC5BC,iBAA2B;QASzBJ;IAPF,MAAMK,qBAAqBJ,SAASK,OAAO,CAAC,YAAY;IACxD,MAAMC,YAAY,IAAIC;IACtB,MAAMC,WAAW,IAAID;IAErB,MAAME,gBACJV,wBAAwBU,aAAa,CAACL,mBAAmB;IAC3D,MAAMM,eACJX,EAAAA,wCAAAA,wBAAwBW,YAAY,qBAApCX,qCAAsC,CAACK,mBAAmB,KAAI,EAAE;IAElE,IAAIK,eAAe;QACjB,KAAK,MAAME,OAAOF,cAAe;YAC/B,IAAI,CAACR,YAAYW,GAAG,CAACD,IAAIE,IAAI,GAAG;gBAC9B,IAAIV,mBAAmB;oBACrBF,YAAYa,GAAG,CAACH,IAAIE,IAAI;gBAC1B;gBACAP,UAAUQ,GAAG,CAACH;YAChB;QACF;IACF;IAEA,IAAID,cAAc;QAChB,KAAK,MAAMK,QAAQL,aAAc;YAC/B,IAAI,CAACR,gBAAgBU,GAAG,CAACG,OAAO;gBAC9B,IAAIZ,mBAAmB;oBACrBD,gBAAgBY,GAAG,CAACC;gBACtB;gBACAP,SAASM,GAAG,CAACC;YACf;QACF;IACF;IAEA,OAAO;QAAEC,QAAQ;eAAIV;SAAU;QAAEW,SAAS;eAAIT;SAAS;IAAC;AAC1D"}