{"name": "@types/next", "version": "8.0.7", "description": "TypeScript definitions for next", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/dru89", "githubUsername": "dru89"}, {"name": "Brice BERNARD", "url": "https://github.com/brikou", "githubUsername": "brikou"}, {"name": "<PERSON>", "url": "https://github.com/jthegedus", "githubUsername": "j<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/resir014", "githubUsername": "resir014"}, {"name": "<PERSON>", "url": "https://github.com/scottdj92", "githubUsername": "scottdj92"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/joaovieira", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ajliv", "githubUsername": "a<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/next"}, "scripts": {}, "dependencies": {"@types/next-server": "*", "@types/node": "*", "@types/node-fetch": "*", "@types/react": "*"}, "typesPublisherContentHash": "cfb330b495d8f9175ba1b9dd44e859038852aa43b8a3487fa0acf6c278dcf282", "typeScriptVersion": "2.8"}