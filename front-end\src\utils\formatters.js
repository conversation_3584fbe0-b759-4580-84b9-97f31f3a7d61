/**
 * Formate une date au format français JJ/MM/AAAA
 * @param {Date|string} date - Date à formater
 * @returns {string} Date formatée
 */
export const formatDate = (date) => {
  if (!date) return 'N/A';

  const d = new Date(date);
  if (isNaN(d.getTime())) return 'Date invalide';

  return d.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

/**
 * Formate une date et heure au format français
 * @param {Date|string} date - Date à formater
 * @returns {string} Date et heure formatées
 */
export const formatDateTime = (date) => {
  if (!date) return 'N/A';

  const d = new Date(date);
  if (isNaN(d.getTime())) return 'Date invalide';

  return d.toLocaleString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Récupère la devise par défaut (toujours TND)
 * @returns {string} Code de la devise (TND)
 */
export const getDefaultCurrency = () => {
  return 'TND';
};

/**
 * Formate un montant en Dinar Tunisien (DT)
 * @param {number} amount - Montant à formater
 * @param {number} decimals - Nombre de décimales (par défaut 2)
 * @returns {string} Montant formaté en DT
 */
export const formatCurrency = (amount, decimals = 2) => {
  if (amount === undefined || amount === null) return 'N/A';

  // Ensure we're working with a number
  let numericAmount = amount;

  // If it's not already a number, try to convert it
  if (typeof amount !== 'number') {
    // If it's a string, clean it up
    if (typeof amount === 'string') {
      // Remove any non-numeric characters except decimal separators
      const cleanedAmount = amount.replace(/[^\d.,]/g, '');

      // Handle slash format if present (e.g. "1/908,00 DT")
      if (amount.includes('/')) {
        const parts = amount.split('/');
        if (parts.length === 2) {
          // Replace comma with dot for parsing
          numericAmount = parseFloat(parts[1].replace(',', '.'));
        } else {
          // Try to parse the cleaned string
          numericAmount = parseFloat(cleanedAmount.replace(',', '.'));
        }
      } else {
        // Try to parse the cleaned string
        numericAmount = parseFloat(cleanedAmount.replace(',', '.'));
      }
    } else {
      // Try to convert to number directly
      numericAmount = Number(amount);
    }
  }

  // If we still don't have a valid number after all conversions
  if (isNaN(numericAmount)) {
    console.warn('Invalid amount for formatting:', amount);
    return 'Montant invalide';
  }

  try {
    // Use French format with dot as thousands separator
    // and comma as decimal separator
    let formattedNumber = new Intl.NumberFormat('fr-FR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
      useGrouping: true // Enable thousands separator
    }).format(numericAmount);

    // Replace the space thousands separator with a dot
    formattedNumber = formattedNumber.replace(/\s/g, '.');

    // Manually add DT symbol after the number
    return `${formattedNumber} DT`;
  } catch (error) {
    console.error('Error formatting currency:', error);

    // Fallback in case of error: format the number and manually add the symbol
    let formattedNumber = numericAmount.toFixed(decimals);
    return `${formattedNumber} DT`;
  }
};

/**
 * Formate un statut en texte lisible
 * @param {string} status - Statut à formater
 * @returns {string} Statut formaté
 */
export const formatStatut = (status) => {
  if (!status) return 'Inconnu';

  // Normaliser le statut pour la recherche (majuscules)
  const normalizedStatus = status.toUpperCase();

  const statuts = {
    // Statuts en anglais
    'DRAFT': 'Brouillon',
    'SENT': 'Envoyé',
    'PAID': 'Payé',
    'CANCELED': 'Annulé',
    'PENDING': 'En attente',
    'WAITING_APPROVAL': 'En attente de traitement',
    'APPROVED_INTERNAL': 'Validé en interne',
    'ACCEPTED': 'Accepté',
    'REJECTED': 'Rejeté',
    'PARTIALLY_PAID': 'Partiellement payé',
    'OVERDUE': 'En retard',
    'EXPIRED': 'Expiré',

    // Statuts en français
    'BROUILLON': 'Brouillon',
    'ENVOYÉ': 'Envoyé',
    'PAYÉ': 'Payé',
    'ANNULÉ': 'Annulé',
    'EN ATTENTE DE TRAITEMENT': 'En attente de traitement',
    'VALIDÉ EN INTERNE': 'Validé en interne',
    'ACCEPTÉ': 'Accepté',
    'REFUSÉ': 'Rejeté',
    'EXPIRÉ': 'Expiré'
  };

  return statuts[normalizedStatus] || status;
};

/**
 * Normalise un statut pour le stockage en base de données
 * @param {string} status - Statut à normaliser
 * @returns {string} Statut normalisé en anglais
 */
export const normalizeStatus = (status) => {
  if (!status) return 'DRAFT';

  // Normaliser le statut pour la recherche (majuscules)
  const normalizedStatus = status.toUpperCase();

  // Mapping des statuts français vers anglais
  const statusMapping = {
    // Français vers anglais
    'BROUILLON': 'DRAFT',
    'ENVOYÉ': 'SENT',
    'PAYÉ': 'PAID',
    'ANNULÉ': 'CANCELED',
    'ACCEPTÉ': 'ACCEPTED',
    'REFUSÉ': 'REJECTED',
    'EXPIRÉ': 'EXPIRED',
    'EN ATTENTE': 'PENDING',

    // Anglais vers anglais (pour être complet)
    'DRAFT': 'DRAFT',
    'SENT': 'SENT',
    'PAID': 'PAID',
    'CANCELED': 'CANCELED',
    'ACCEPTED': 'ACCEPTED',
    'REJECTED': 'REJECTED',
    'EXPIRED': 'EXPIRED',
    'PENDING': 'PENDING'
  };

  return statusMapping[normalizedStatus] || 'DRAFT';
};

/**
 * Formate un numéro de facture/devis avec padding
 * @param {number} num - Numéro à formater
 * @param {string} prefix - Préfixe (ex: "FAC-")
 * @param {number} length - Longueur totale (par défaut 5)
 * @returns {string} Numéro formaté
 */
export const formatDocumentNumber = (num, prefix = '', length = 5) => {
  const paddedNumber = String(num).padStart(length, '0');
  return `${prefix}${paddedNumber}`;
};

/**
 * Formate une durée en heures:minutes
 * @param {number} minutes - Durée en minutes
 * @returns {string} Durée formatée
 */
export const formatDuration = (minutes) => {
  if (!minutes || isNaN(minutes)) return '00:00';

  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
};

/**
 * Tronque un texte avec une ellipse si nécessaire
 * @param {string} text - Texte à tronquer
 * @param {number} maxLength - Longueur maximale
 * @returns {string} Texte tronqué
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;

  return `${text.substring(0, maxLength)}...`;
};

/**
 * Formate un numéro de téléphone français
 * @param {string} phone - Numéro de téléphone
 * @returns {string} Numéro formaté
 */
export const formatPhoneNumber = (phone) => {
  if (!phone) return 'N/A';

  const cleaned = phone.replace(/\D/g, '');

  if (cleaned.length !== 10 || !cleaned.startsWith('0')) {
    return phone;
  }

  const formatted = cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
  return formatted;
};

/**
 * Formate une quantité de stock en nombre entier
 * @param {number} quantity - Quantité à formater
 * @param {string} unit - Unité à afficher (par défaut 'unités')
 * @returns {string} Quantité formatée
 */
export const formatStockQuantity = (quantity, unit = 'unités') => {
  if (quantity === undefined || quantity === null) return '0 ' + unit;

  // Convert to integer by rounding
  const intQuantity = Math.round(Number(quantity));

  // Return formatted quantity with unit
  return `${intQuantity} ${unit}`;
};

/**
 * Formate un nombre avec séparateurs de milliers
 * @param {number} number - Nombre à formater
 * @param {number} decimals - Nombre de décimales (par défaut 0)
 * @returns {string} Nombre formaté
 */
export const formatNumber = (number, decimals = 0) => {
  if (number === undefined || number === null) return 'N/A';

  // Ensure we're working with a number
  const numericValue = Number(number);

  if (isNaN(numericValue)) {
    console.warn('Invalid number for formatting:', number);
    return 'Valeur invalide';
  }

  // Format the number using French locale (space as thousands separator)
  return new Intl.NumberFormat('fr-FR', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true
  }).format(numericValue);
};