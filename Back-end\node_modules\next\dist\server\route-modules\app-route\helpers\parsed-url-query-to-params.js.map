{"version": 3, "sources": ["../../../../../src/server/route-modules/app-route/helpers/parsed-url-query-to-params.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\n/**\n * Converts the query into params.\n *\n * @param query the query to convert to params\n * @returns the params\n */\nexport function parsedUrlQueryToParams(\n  query: ParsedUrlQuery\n): Record<string, string | string[]> {\n  const params: Record<string, string | string[]> = {}\n\n  for (const [key, value] of Object.entries(query)) {\n    if (typeof value === 'undefined') continue\n    params[key] = value\n  }\n\n  return params\n}\n"], "names": ["parsedUrlQueryToParams", "query", "params", "key", "value", "Object", "entries"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;AAAT,SAASA,uBACdC,KAAqB;IAErB,MAAMC,SAA4C,CAAC;IAEnD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,OAAQ;QAChD,IAAI,OAAOG,UAAU,aAAa;QAClCF,MAAM,CAACC,IAAI,GAAGC;IAChB;IAEA,OAAOF;AACT"}