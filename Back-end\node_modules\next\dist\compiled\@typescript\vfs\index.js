(()=>{"use strict";var t={188:(t,r,i)=>{if(process.env.NODE_ENV==="production"){t.exports=i(994)}else{t.exports=i(121)}},121:(t,r,i)=>{Object.defineProperty(r,"__esModule",{value:true});function _extends(){_extends=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var p in i){if(Object.prototype.hasOwnProperty.call(i,p)){t[p]=i[p]}}}return t};return _extends.apply(this,arguments)}var p=false;try{p=typeof localStorage!=="undefined"}catch(t){}var m=typeof process!=="undefined";var g=p&&localStorage.getItem("DEBUG")||m&&process.env.DEBUG;var v=g?console.log:function(t){return""};function createVirtualTypeScriptEnvironment(t,r,i,p,m){if(p===void 0){p={}}var g=_extends({},w(i),p);var v=createVirtualLanguageServiceHost(t,r,g,i,m),y=v.languageServiceHost,h=v.updateFile,F=v.deleteFile;var x=i.createLanguageService(y);var S=x.getCompilerOptionsDiagnostics();if(S.length){var E=createVirtualCompilerHost(t,p,i);throw new Error(i.formatDiagnostics(S,E.compilerHost))}return{name:"vfs",sys:t,languageService:x,getSourceFile:function getSourceFile(t){var r;return(r=x.getProgram())==null?void 0:r.getSourceFile(t)},createFile:function createFile(t,r){h(i.createSourceFile(t,r,g.target,false))},updateFile:function updateFile(t,r,p){var m=x.getProgram().getSourceFile(t);if(!m){throw new Error("Did not find a source file for "+t)}var g=m.text;var v=p!=null?p:i.createTextSpan(0,g.length);var y=g.slice(0,v.start)+r+g.slice(v.start+v.length);var F=i.updateSourceFile(m,y,{span:v,newLength:r.length});h(F)},deleteFile:function deleteFile(t){var r=x.getProgram().getSourceFile(t);if(r){F(r)}}}}var y=function knownLibFilesForCompilerOptions(t,r){var i=t.target||r.ScriptTarget.ES5;var p=t.lib||[];var m=["lib.d.ts","lib.core.d.ts","lib.decorators.d.ts","lib.decorators.legacy.d.ts","lib.dom.asynciterable.d.ts","lib.dom.d.ts","lib.dom.iterable.d.ts","lib.webworker.asynciterable.d.ts","lib.webworker.d.ts","lib.webworker.importscripts.d.ts","lib.webworker.iterable.d.ts","lib.scripthost.d.ts","lib.es5.d.ts","lib.es6.d.ts","lib.es7.d.ts","lib.core.es6.d.ts","lib.core.es7.d.ts","lib.es2015.collection.d.ts","lib.es2015.core.d.ts","lib.es2015.d.ts","lib.es2015.generator.d.ts","lib.es2015.iterable.d.ts","lib.es2015.promise.d.ts","lib.es2015.proxy.d.ts","lib.es2015.reflect.d.ts","lib.es2015.symbol.d.ts","lib.es2015.symbol.wellknown.d.ts","lib.es2016.array.include.d.ts","lib.es2016.d.ts","lib.es2016.full.d.ts","lib.es2016.intl.d.ts","lib.es2017.arraybuffer.d.ts","lib.es2017.d.ts","lib.es2017.date.d.ts","lib.es2017.full.d.ts","lib.es2017.intl.d.ts","lib.es2017.object.d.ts","lib.es2017.sharedmemory.d.ts","lib.es2017.string.d.ts","lib.es2017.typedarrays.d.ts","lib.es2018.asyncgenerator.d.ts","lib.es2018.asynciterable.d.ts","lib.es2018.d.ts","lib.es2018.full.d.ts","lib.es2018.intl.d.ts","lib.es2018.promise.d.ts","lib.es2018.regexp.d.ts","lib.es2019.array.d.ts","lib.es2019.d.ts","lib.es2019.full.d.ts","lib.es2019.intl.d.ts","lib.es2019.object.d.ts","lib.es2019.string.d.ts","lib.es2019.symbol.d.ts","lib.es2020.bigint.d.ts","lib.es2020.d.ts","lib.es2020.date.d.ts","lib.es2020.full.d.ts","lib.es2020.intl.d.ts","lib.es2020.number.d.ts","lib.es2020.promise.d.ts","lib.es2020.sharedmemory.d.ts","lib.es2020.string.d.ts","lib.es2020.symbol.wellknown.d.ts","lib.es2021.d.ts","lib.es2021.full.d.ts","lib.es2021.intl.d.ts","lib.es2021.promise.d.ts","lib.es2021.string.d.ts","lib.es2021.weakref.d.ts","lib.es2022.array.d.ts","lib.es2022.d.ts","lib.es2022.error.d.ts","lib.es2022.full.d.ts","lib.es2022.intl.d.ts","lib.es2022.object.d.ts","lib.es2022.regexp.d.ts","lib.es2022.sharedmemory.d.ts","lib.es2022.string.d.ts","lib.es2023.array.d.ts","lib.es2023.collection.d.ts","lib.es2023.d.ts","lib.es2023.full.d.ts","lib.es2023.intl.d.ts","lib.es2024.arraybuffer.d.ts","lib.es2024.collection.d.ts","lib.es2024.d.ts","lib.es2024.full.d.ts","lib.es2024.object.d.ts","lib.es2024.promise.d.ts","lib.es2024.regexp.d.ts","lib.es2024.sharedmemory.d.ts","lib.es2024.string.d.ts","lib.esnext.array.d.ts","lib.esnext.asynciterable.d.ts","lib.esnext.bigint.d.ts","lib.esnext.collection.d.ts","lib.esnext.d.ts","lib.esnext.decorators.d.ts","lib.esnext.disposable.d.ts","lib.esnext.float16.d.ts","lib.esnext.full.d.ts","lib.esnext.intl.d.ts","lib.esnext.iterator.d.ts","lib.esnext.object.d.ts","lib.esnext.promise.d.ts","lib.esnext.regexp.d.ts","lib.esnext.string.d.ts","lib.esnext.symbol.d.ts","lib.esnext.weakref.d.ts"];var g=r.ScriptTarget[i];var v=m.filter((function(t){return t.startsWith("lib."+g.toLowerCase())}));var y=m.indexOf(v.pop());var h=function getMax(t){return t&&t.length?t.reduce((function(t,r){return r>t?r:t})):undefined};var F=p.map((function(t){var r=m.filter((function(r){return r.startsWith("lib."+t.toLowerCase())}));if(r.length===0)return 0;var i=m.indexOf(r.pop());return i}));var x=h(F)||0;var S=Math.max(y,x);return m.slice(0,S+1)};var h=function createDefaultMapFromNodeModules(t,r,p){var m=D();var g=C();var v=function getLib(t){var r=p||m.dirname(i.ab+"typescript.js");return g.readFileSync(m.join(r,t),"utf8")};var y=function isDtsFile(t){return/\.d\.([^\.]+\.)?[cm]?ts$/i.test(t)};var h=g.readdirSync(p||m.dirname(i.ab+"typescript.js"));var F=h.filter((function(t){return t.startsWith("lib.")&&y(t)}));var x=new Map;F.forEach((function(t){x.set("/"+t,v(t))}));return x};var F=function addAllFilesFromFolder(t,r){var i=D();var p=C();var m=function walk(t){var r=[];var m=p.readdirSync(t);m.forEach((function(m){m=i.join(t,m);var g=p.statSync(m);if(g&&g.isDirectory()){r=r.concat(walk(m))}else{r.push(m)}}));return r};var g=m(r);g.forEach((function(m){var g="/node_modules/@types"+m.replace(r,"");var v=p.readFileSync(m,"utf8");var y=[".ts",".tsx"];if(y.includes(i.extname(g))){t.set(g,v)}}))};var x=function addFilesForTypesIntoFolder(t){return F(t,"node_modules/@types")};var S=function createDefaultMapFromCDN(t,r,i,p,m,g,v){var h=g||fetch;var F=new Map;var x=y(t,p);var S="https://playgroundcdn.typescriptlang.org/cdn/"+r+"/typescript/lib/";function zip(t){return m?m.compressToUTF16(t):t}function unzip(t){return m?m.decompressFromUTF16(t):t}function uncached(){return Promise.all(x.map((function(t){return h(S+t).then((function(t){return t.text()}))}))).then((function(t){t.forEach((function(t,r){return F.set("/"+x[r],t)}))}))["catch"]((function(){}))}function cached(){var t=v||localStorage;var i=Object.keys(t);i.forEach((function(i){if(i.startsWith("ts-lib-")&&!i.startsWith("ts-lib-"+r)){t.removeItem(i)}}));return Promise.all(x.map((function(i){var p="ts-lib-"+r+"-"+i;var m=t.getItem(p);if(!m){return h(S+i).then((function(t){return t.text()})).then((function(r){t.setItem(p,zip(r));return r}))["catch"]((function(){}))}else{return Promise.resolve(unzip(m))}}))).then((function(t){t.forEach((function(t,r){if(t){var i="/"+x[r];F.set(i,t)}}))}))}var w=i?cached:uncached;return w().then((function(){return F}))};function notImplemented(t){throw new Error("Method '"+t+"' is not implemented.")}function audit(t,r){return function(){for(var i=arguments.length,p=new Array(i),m=0;m<i;m++){p[m]=arguments[m]}var g=r.apply(void 0,p);var y=typeof g==="string"?g.slice(0,80)+"...":g;v.apply(void 0,["> "+t].concat(p));v("< "+y);return g}}var w=function defaultCompilerOptions(t){return _extends({},t.getDefaultCompilerOptions(),{jsx:t.JsxEmit.React,strict:true,esModuleInterop:true,module:t.ModuleKind.ESNext,suppressOutputPathCheck:true,skipLibCheck:true,skipDefaultLibCheck:true,moduleResolution:t.ModuleResolutionKind.NodeJs})};var E=function libize(t){return t.replace("/","/lib.").toLowerCase()};function createSystem(t){return{args:[],createDirectory:function createDirectory(){return notImplemented("createDirectory")},directoryExists:audit("directoryExists",(function(r){return Array.from(t.keys()).some((function(t){return t.startsWith(r)}))})),exit:function exit(){return notImplemented("exit")},fileExists:audit("fileExists",(function(r){return t.has(r)||t.has(E(r))})),getCurrentDirectory:function getCurrentDirectory(){return"/"},getDirectories:function getDirectories(){return[]},getExecutingFilePath:function getExecutingFilePath(){return notImplemented("getExecutingFilePath")},readDirectory:audit("readDirectory",(function(r){return r==="/"?Array.from(t.keys()):[]})),readFile:audit("readFile",(function(r){var i;return(i=t.get(r))!=null?i:t.get(E(r))})),resolvePath:function resolvePath(t){return t},newLine:"\n",useCaseSensitiveFileNames:true,write:function write(){return notImplemented("write")},writeFile:function writeFile(r,i){t.set(r,i)},deleteFile:function deleteFile(r){t["delete"](r)}}}function createFSBackedSystem(t,r,p,m){var g=r+"/vfs";var v=D();var y=p.sys;var h=m!=null?m:v.dirname(i.ab+"typescript.js");return{name:"fs-vfs",root:g,args:[],createDirectory:function createDirectory(){return notImplemented("createDirectory")},directoryExists:audit("directoryExists",(function(r){return Array.from(t.keys()).some((function(t){return t.startsWith(r)}))||y.directoryExists(r)})),exit:y.exit,fileExists:audit("fileExists",(function(r){if(t.has(r))return true;if(r.includes("tsconfig.json")||r.includes("tsconfig.json"))return false;if(r.startsWith("/lib")){var i=h+"/"+r.replace("/","");return y.fileExists(i)}return y.fileExists(r)})),getCurrentDirectory:function getCurrentDirectory(){return g},getDirectories:y.getDirectories,getExecutingFilePath:function getExecutingFilePath(){return notImplemented("getExecutingFilePath")},readDirectory:audit("readDirectory",(function(){if((arguments.length<=0?undefined:arguments[0])==="/"){return Array.from(t.keys())}else{return y.readDirectory.apply(y,arguments)}})),readFile:audit("readFile",(function(r){if(t.has(r))return t.get(r);if(r.startsWith("/lib")){var i=h+"/"+r.replace("/","");var p=y.readFile(i);if(!p){var m=y.readDirectory(h);throw new Error("TSVFS: A request was made for "+i+" but there wasn't a file found in the file map. You likely have a mismatch in the compiler options for the CDN download vs the compiler program. Existing Libs: "+m+".")}return p}return y.readFile(r)})),resolvePath:function resolvePath(r){if(t.has(r))return r;return y.resolvePath(r)},newLine:"\n",useCaseSensitiveFileNames:true,write:function write(){return notImplemented("write")},writeFile:function writeFile(r,i){t.set(r,i)},deleteFile:function deleteFile(r){t["delete"](r)},realpath:y.realpath}}function createVirtualCompilerHost(t,r,i){var p=new Map;var m=function save(t){p.set(t.fileName,t);return t};var g={compilerHost:_extends({},t,{getCanonicalFileName:function getCanonicalFileName(t){return t},getDefaultLibFileName:function getDefaultLibFileName(){return"/"+i.getDefaultLibFileName(r)},getNewLine:function getNewLine(){return t.newLine},getSourceFile:function getSourceFile(g,v){var y;return p.get(g)||m(i.createSourceFile(g,t.readFile(g),(y=v!=null?v:r.target)!=null?y:w(i).target,false))},useCaseSensitiveFileNames:function useCaseSensitiveFileNames(){return t.useCaseSensitiveFileNames}}),updateFile:function updateFile(r){var i=p.has(r.fileName);t.writeFile(r.fileName,r.text);p.set(r.fileName,r);return i},deleteFile:function deleteFile(r){var i=p.has(r.fileName);p["delete"](r.fileName);t.deleteFile(r.fileName);return i}};return g}function createVirtualLanguageServiceHost(t,r,i,p,m){var g=[].concat(r);var v=createVirtualCompilerHost(t,i,p),y=v.compilerHost,h=v.updateFile,F=v.deleteFile;var x=new Map;var S=0;var w=_extends({},y,{getProjectVersion:function getProjectVersion(){return S.toString()},getCompilationSettings:function getCompilationSettings(){return i},getCustomTransformers:function getCustomTransformers(){return m},getScriptFileNames:function getScriptFileNames(){return g.slice()},getScriptSnapshot:function getScriptSnapshot(r){var i=t.readFile(r);if(i&&typeof i==="string"){return p.ScriptSnapshot.fromString(i)}return},getScriptVersion:function getScriptVersion(t){return x.get(t)||"0"},writeFile:t.writeFile});var E={languageServiceHost:w,updateFile:function updateFile(t){S++;x.set(t.fileName,S.toString());if(!g.includes(t.fileName)){g.push(t.fileName)}h(t)},deleteFile:function deleteFile(t){S++;x.set(t.fileName,S.toString());var r=g.indexOf(t.fileName);if(r!==-1){g.splice(r,1)}F(t)}};return E}var D=function requirePath(){return require(String.fromCharCode(112,97,116,104))};var C=function requireFS(){return require(String.fromCharCode(102,115))};r.addAllFilesFromFolder=F;r.addFilesForTypesIntoFolder=x;r.createDefaultMapFromCDN=S;r.createDefaultMapFromNodeModules=h;r.createFSBackedSystem=createFSBackedSystem;r.createSystem=createSystem;r.createVirtualCompilerHost=createVirtualCompilerHost;r.createVirtualLanguageServiceHost=createVirtualLanguageServiceHost;r.createVirtualTypeScriptEnvironment=createVirtualTypeScriptEnvironment;r.knownLibFilesForCompilerOptions=y},994:(t,r,i)=>{function e(){return e=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var p in i)Object.prototype.hasOwnProperty.call(i,p)&&(t[p]=i[p])}return t},e.apply(this,arguments)}Object.defineProperty(r,"__esModule",{value:!0});var p=!1;try{p="undefined"!=typeof localStorage}catch(e){}var m="undefined"!=typeof process,g=p&&localStorage.getItem("DEBUG")||m&&process.env.DEBUG?console.log:function(t){return""},s=function(t,r){var i,p=t.lib||[],m=["lib.d.ts","lib.core.d.ts","lib.decorators.d.ts","lib.decorators.legacy.d.ts","lib.dom.asynciterable.d.ts","lib.dom.d.ts","lib.dom.iterable.d.ts","lib.webworker.asynciterable.d.ts","lib.webworker.d.ts","lib.webworker.importscripts.d.ts","lib.webworker.iterable.d.ts","lib.scripthost.d.ts","lib.es5.d.ts","lib.es6.d.ts","lib.es7.d.ts","lib.core.es6.d.ts","lib.core.es7.d.ts","lib.es2015.collection.d.ts","lib.es2015.core.d.ts","lib.es2015.d.ts","lib.es2015.generator.d.ts","lib.es2015.iterable.d.ts","lib.es2015.promise.d.ts","lib.es2015.proxy.d.ts","lib.es2015.reflect.d.ts","lib.es2015.symbol.d.ts","lib.es2015.symbol.wellknown.d.ts","lib.es2016.array.include.d.ts","lib.es2016.d.ts","lib.es2016.full.d.ts","lib.es2016.intl.d.ts","lib.es2017.arraybuffer.d.ts","lib.es2017.d.ts","lib.es2017.date.d.ts","lib.es2017.full.d.ts","lib.es2017.intl.d.ts","lib.es2017.object.d.ts","lib.es2017.sharedmemory.d.ts","lib.es2017.string.d.ts","lib.es2017.typedarrays.d.ts","lib.es2018.asyncgenerator.d.ts","lib.es2018.asynciterable.d.ts","lib.es2018.d.ts","lib.es2018.full.d.ts","lib.es2018.intl.d.ts","lib.es2018.promise.d.ts","lib.es2018.regexp.d.ts","lib.es2019.array.d.ts","lib.es2019.d.ts","lib.es2019.full.d.ts","lib.es2019.intl.d.ts","lib.es2019.object.d.ts","lib.es2019.string.d.ts","lib.es2019.symbol.d.ts","lib.es2020.bigint.d.ts","lib.es2020.d.ts","lib.es2020.date.d.ts","lib.es2020.full.d.ts","lib.es2020.intl.d.ts","lib.es2020.number.d.ts","lib.es2020.promise.d.ts","lib.es2020.sharedmemory.d.ts","lib.es2020.string.d.ts","lib.es2020.symbol.wellknown.d.ts","lib.es2021.d.ts","lib.es2021.full.d.ts","lib.es2021.intl.d.ts","lib.es2021.promise.d.ts","lib.es2021.string.d.ts","lib.es2021.weakref.d.ts","lib.es2022.array.d.ts","lib.es2022.d.ts","lib.es2022.error.d.ts","lib.es2022.full.d.ts","lib.es2022.intl.d.ts","lib.es2022.object.d.ts","lib.es2022.regexp.d.ts","lib.es2022.sharedmemory.d.ts","lib.es2022.string.d.ts","lib.es2023.array.d.ts","lib.es2023.collection.d.ts","lib.es2023.d.ts","lib.es2023.full.d.ts","lib.es2023.intl.d.ts","lib.es2024.arraybuffer.d.ts","lib.es2024.collection.d.ts","lib.es2024.d.ts","lib.es2024.full.d.ts","lib.es2024.object.d.ts","lib.es2024.promise.d.ts","lib.es2024.regexp.d.ts","lib.es2024.sharedmemory.d.ts","lib.es2024.string.d.ts","lib.esnext.array.d.ts","lib.esnext.asynciterable.d.ts","lib.esnext.bigint.d.ts","lib.esnext.collection.d.ts","lib.esnext.d.ts","lib.esnext.decorators.d.ts","lib.esnext.disposable.d.ts","lib.esnext.float16.d.ts","lib.esnext.full.d.ts","lib.esnext.intl.d.ts","lib.esnext.iterator.d.ts","lib.esnext.object.d.ts","lib.esnext.promise.d.ts","lib.esnext.regexp.d.ts","lib.esnext.string.d.ts","lib.esnext.symbol.d.ts","lib.esnext.weakref.d.ts"],g=r.ScriptTarget[t.target||r.ScriptTarget.ES5],v=m.filter((function(t){return t.startsWith("lib."+g.toLowerCase())})),y=m.indexOf(v.pop()),h=p.map((function(t){var r=m.filter((function(r){return r.startsWith("lib."+t.toLowerCase())}));return 0===r.length?0:m.indexOf(r.pop())})),F=((i=h)&&i.length?i.reduce((function(t,r){return r>t?r:t})):void 0)||0,x=Math.max(y,F);return m.slice(0,x+1)},n=function(t,r){var i=f(),p=b();(function e(t){var r=[];return p.readdirSync(t).forEach((function(m){m=i.join(t,m);var g=p.statSync(m);g&&g.isDirectory()?r=r.concat(e(m)):r.push(m)})),r})(r).forEach((function(m){var g="/node_modules/@types"+m.replace(r,""),v=p.readFileSync(m,"utf8");[".ts",".tsx"].includes(i.extname(g))&&t.set(g,v)}))};function l(t){throw new Error("Method '"+t+"' is not implemented.")}function o(t,r){return function(){for(var i=arguments.length,p=new Array(i),m=0;m<i;m++)p[m]=arguments[m];var v=r.apply(void 0,p),y="string"==typeof v?v.slice(0,80)+"...":v;return g.apply(void 0,["> "+t].concat(p)),g("< "+y),v}}var a=function(t){return e({},t.getDefaultCompilerOptions(),{jsx:t.JsxEmit.React,strict:!0,esModuleInterop:!0,module:t.ModuleKind.ESNext,suppressOutputPathCheck:!0,skipLibCheck:!0,skipDefaultLibCheck:!0,moduleResolution:t.ModuleResolutionKind.NodeJs})},c=function(t){return t.replace("/","/lib.").toLowerCase()};function u(t,r,i){var p=new Map;return{compilerHost:e({},t,{getCanonicalFileName:function(t){return t},getDefaultLibFileName:function(){return"/"+i.getDefaultLibFileName(r)},getNewLine:function(){return t.newLine},getSourceFile:function(m,g){var v,y;return p.get(m)||(y=i.createSourceFile(m,t.readFile(m),null!=(v=null!=g?g:r.target)?v:a(i).target,!1),p.set(y.fileName,y),y)},useCaseSensitiveFileNames:function(){return t.useCaseSensitiveFileNames}}),updateFile:function(r){var i=p.has(r.fileName);return t.writeFile(r.fileName,r.text),p.set(r.fileName,r),i},deleteFile:function(r){var i=p.has(r.fileName);return p.delete(r.fileName),t.deleteFile(r.fileName),i}}}function d(t,r,i,p,m){var g=[].concat(r),v=u(t,i,p),y=v.compilerHost,h=v.updateFile,F=v.deleteFile,x=new Map,S=0;return{languageServiceHost:e({},y,{getProjectVersion:function(){return S.toString()},getCompilationSettings:function(){return i},getCustomTransformers:function(){return m},getScriptFileNames:function(){return g.slice()},getScriptSnapshot:function(r){var i=t.readFile(r);if(i&&"string"==typeof i)return p.ScriptSnapshot.fromString(i)},getScriptVersion:function(t){return x.get(t)||"0"},writeFile:t.writeFile}),updateFile:function(t){S++,x.set(t.fileName,S.toString()),g.includes(t.fileName)||g.push(t.fileName),h(t)},deleteFile:function(t){S++,x.set(t.fileName,S.toString());var r=g.indexOf(t.fileName);-1!==r&&g.splice(r,1),F(t)}}}var f=function(){return require(String.fromCharCode(112,97,116,104))},b=function(){return require(String.fromCharCode(102,115))};r.addAllFilesFromFolder=n,r.addFilesForTypesIntoFolder=function(t){return n(t,"node_modules/@types")},r.createDefaultMapFromCDN=function(t,r,i,p,m,g,v){var y=g||fetch,h=new Map,F=s(t,p),x="https://playgroundcdn.typescriptlang.org/cdn/"+r+"/typescript/lib/";return(i?function(){var t=v||localStorage;return Object.keys(t).forEach((function(i){i.startsWith("ts-lib-")&&!i.startsWith("ts-lib-"+r)&&t.removeItem(i)})),Promise.all(F.map((function(i){var p,g="ts-lib-"+r+"-"+i,v=t.getItem(g);return v?Promise.resolve((p=v,m?m.decompressFromUTF16(p):p)):y(x+i).then((function(t){return t.text()})).then((function(r){var i;return t.setItem(g,(i=r,m?m.compressToUTF16(i):i)),r})).catch((function(){}))}))).then((function(t){t.forEach((function(t,r){t&&h.set("/"+F[r],t)}))}))}:function(){return Promise.all(F.map((function(t){return y(x+t).then((function(t){return t.text()}))}))).then((function(t){t.forEach((function(t,r){return h.set("/"+F[r],t)}))})).catch((function(){}))})().then((function(){return h}))},r.createDefaultMapFromNodeModules=function(t,r,p){var m=f(),g=b(),v=g.readdirSync(p||m.dirname(i.ab+"typescript.js")).filter((function(t){return t.startsWith("lib.")&&/\.d\.([^\.]+\.)?[cm]?ts$/i.test(t)})),y=new Map;return v.forEach((function(t){y.set("/"+t,function(t){var r=p||m.dirname(i.ab+"typescript.js");return g.readFileSync(m.join(r,t),"utf8")}(t))})),y},r.createFSBackedSystem=function(t,r,p,m){var g=r+"/vfs",v=f(),y=p.sys,h=null!=m?m:v.dirname(i.ab+"typescript.js");return{name:"fs-vfs",root:g,args:[],createDirectory:function(){return l("createDirectory")},directoryExists:o("directoryExists",(function(r){return Array.from(t.keys()).some((function(t){return t.startsWith(r)}))||y.directoryExists(r)})),exit:y.exit,fileExists:o("fileExists",(function(r){if(t.has(r))return!0;if(r.includes("tsconfig.json")||r.includes("tsconfig.json"))return!1;if(r.startsWith("/lib")){var i=h+"/"+r.replace("/","");return y.fileExists(i)}return y.fileExists(r)})),getCurrentDirectory:function(){return g},getDirectories:y.getDirectories,getExecutingFilePath:function(){return l("getExecutingFilePath")},readDirectory:o("readDirectory",(function(){return"/"===(arguments.length<=0?void 0:arguments[0])?Array.from(t.keys()):y.readDirectory.apply(y,arguments)})),readFile:o("readFile",(function(r){if(t.has(r))return t.get(r);if(r.startsWith("/lib")){var i=h+"/"+r.replace("/",""),p=y.readFile(i);if(!p){var m=y.readDirectory(h);throw new Error("TSVFS: A request was made for "+i+" but there wasn't a file found in the file map. You likely have a mismatch in the compiler options for the CDN download vs the compiler program. Existing Libs: "+m+".")}return p}return y.readFile(r)})),resolvePath:function(r){return t.has(r)?r:y.resolvePath(r)},newLine:"\n",useCaseSensitiveFileNames:!0,write:function(){return l("write")},writeFile:function(r,i){t.set(r,i)},deleteFile:function(r){t.delete(r)},realpath:y.realpath}},r.createSystem=function(t){return{args:[],createDirectory:function(){return l("createDirectory")},directoryExists:o("directoryExists",(function(r){return Array.from(t.keys()).some((function(t){return t.startsWith(r)}))})),exit:function(){return l("exit")},fileExists:o("fileExists",(function(r){return t.has(r)||t.has(c(r))})),getCurrentDirectory:function(){return"/"},getDirectories:function(){return[]},getExecutingFilePath:function(){return l("getExecutingFilePath")},readDirectory:o("readDirectory",(function(r){return"/"===r?Array.from(t.keys()):[]})),readFile:o("readFile",(function(r){var i;return null!=(i=t.get(r))?i:t.get(c(r))})),resolvePath:function(t){return t},newLine:"\n",useCaseSensitiveFileNames:!0,write:function(){return l("write")},writeFile:function(r,i){t.set(r,i)},deleteFile:function(r){t.delete(r)}}},r.createVirtualCompilerHost=u,r.createVirtualLanguageServiceHost=d,r.createVirtualTypeScriptEnvironment=function(t,r,i,p,m){void 0===p&&(p={});var g=e({},a(i),p),v=d(t,r,g,i,m),y=v.updateFile,h=v.deleteFile,F=i.createLanguageService(v.languageServiceHost),x=F.getCompilerOptionsDiagnostics();if(x.length){var S=u(t,p,i);throw new Error(i.formatDiagnostics(x,S.compilerHost))}return{name:"vfs",sys:t,languageService:F,getSourceFile:function(t){var r;return null==(r=F.getProgram())?void 0:r.getSourceFile(t)},createFile:function(t,r){y(i.createSourceFile(t,r,g.target,!1))},updateFile:function(t,r,p){var m=F.getProgram().getSourceFile(t);if(!m)throw new Error("Did not find a source file for "+t);var g=m.text,v=null!=p?p:i.createTextSpan(0,g.length),h=g.slice(0,v.start)+r+g.slice(v.start+v.length),x=i.updateSourceFile(m,h,{span:v,newLength:r.length});y(x)},deleteFile:function(t){var r=F.getProgram().getSourceFile(t);r&&h(r)}}},r.knownLibFilesForCompilerOptions=s}};var r={};function __nccwpck_require__(i){var p=r[i];if(p!==undefined){return p.exports}var m=r[i]={exports:{}};var g=true;try{t[i](m,m.exports,__nccwpck_require__);g=false}finally{if(g)delete r[i]}return m.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var i=__nccwpck_require__(188);module.exports=i})();