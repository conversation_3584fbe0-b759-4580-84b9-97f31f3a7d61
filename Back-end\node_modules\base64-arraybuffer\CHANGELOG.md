# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.0.2](https://github.com/niklasvh/base64-arraybuffer/compare/v1.0.1...v1.0.2) (2022-01-22)


### fix

* source maps (#33) ([bd5a8ef](https://github.com/niklasvh/base64-arraybuffer/commit/bd5a8eff3a44ea07f9b68533c477076124220ddd)), closes [#33](https://github.com/niklasvh/base64-arraybuffer/issues/33)



## [1.0.1](https://github.com/niklasvh/base64-arraybuffer/compare/v1.0.0...v1.0.1) (2021-08-10)


### fix

* make lib loadable on ie9 (#30) ([a618d14](https://github.com/niklasvh/base64-arraybuffer/commit/a618d14d323f4eb230321a3609bfbc9f23f430c0)), closes [#30](https://github.com/niklasvh/base64-arraybuffer/issues/30)



# [1.0.0](https://github.com/niklasvh/base64-arraybuffer/compare/v0.2.0...v1.0.0) (2021-08-10)


### docs

* update readme (#29) ([0a0253d](https://github.com/niklasvh/base64-arraybuffer/commit/0a0253dcc2e3f01a1f6d04fa81d578f714fce27f)), closes [#29](https://github.com/niklasvh/base64-arraybuffer/issues/29)
