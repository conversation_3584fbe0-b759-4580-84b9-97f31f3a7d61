{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "sourcesContent": ["import type {\n  CssImports,\n  ClientComponentImports,\n} from '../loaders/next-flight-client-entry-loader'\n\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { parse, stringify } from 'querystring'\nimport path from 'path'\nimport { sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getInvalidator,\n  getEntries,\n  EntryTypes,\n  getEntryKey,\n} from '../../../server/dev/on-demand-entry-handler'\nimport {\n  WEBPACK_LAYERS,\n  WEBPACK_RESOURCE_QUERIES,\n} from '../../../lib/constants'\nimport {\n  APP_CLIENT_INTERNALS,\n  BARREL_OPTIMIZATION_PREFIX,\n  COMPILER_NAMES,\n  DEFAULT_RUNTIME_WEBPACK,\n  EDGE_RUNTIME_WEBPACK,\n  SERVER_REFERENCE_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../../../shared/lib/constants'\nimport {\n  isClientComponentEntryModule,\n  isCSSMod,\n  regexCSS,\n} from '../loaders/utils'\nimport {\n  traverseModules,\n  forEachEntryModule,\n  formatBarrelOptimizedResource,\n  getModuleReferencesInOrder,\n} from '../utils'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { getProxiedPluginState } from '../../build-context'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getAssumedSourceType } from '../loaders/next-flight-loader'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\nimport {\n  DEFAULT_METADATA_ROUTE_EXTENSIONS,\n  isMetadataRouteFile,\n} from '../../../lib/metadata/is-metadata-route'\nimport type { MetadataRouteLoaderOptions } from '../loaders/next-metadata-route-loader'\nimport type { FlightActionEntryLoaderActions } from '../loaders/next-flight-action-entry-loader'\nimport getWebpackBundler from '../../../shared/lib/get-webpack-bundler'\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  isEdgeServer: boolean\n  encryptionKey: string\n}\n\nconst PLUGIN_NAME = 'FlightClientEntryPlugin'\n\ntype Actions = {\n  [actionId: string]: {\n    workers: {\n      [name: string]: { moduleId: string | number; async: boolean }\n    }\n    // Record which layer the action is in (rsc or sc_action), in the specific entry.\n    layer: {\n      [name: string]: string\n    }\n  }\n}\n\ntype ActionIdNamePair = { id: string; exportedName: string }\n\nexport type ActionManifest = {\n  // Assign a unique encryption key during production build.\n  encryptionKey: string\n  node: Actions\n  edge: Actions\n}\n\nexport interface ModuleInfo {\n  moduleId: string | number\n  async: boolean\n}\n\nconst pluginState = getProxiedPluginState({\n  // A map to track \"action\" -> \"list of bundles\".\n  serverActions: {} as ActionManifest['node'],\n  edgeServerActions: {} as ActionManifest['edge'],\n\n  serverActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  edgeServerActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n\n  injectedClientEntries: {} as Record<string, string>,\n})\n\nfunction deduplicateCSSImportsForEntry(mergedCSSimports: CssImports) {\n  // If multiple entry module connections are having the same CSS import,\n  // we only need to have one module to keep track of that CSS import.\n  // It is based on the fact that if a page or a layout is rendered in the\n  // given entry, all its parent layouts are always rendered too.\n  // This can avoid duplicate CSS imports in the generated CSS manifest,\n  // for example, if a page and its parent layout are both using the same\n  // CSS import, we only need to have the layout to keep track of that CSS\n  // import.\n  // To achieve this, we need to first collect all the CSS imports from\n  // every connection, and deduplicate them in the order of layers from\n  // top to bottom. The implementation can be generally described as:\n  // - Sort by number of `/` in the request path (the more `/`, the deeper)\n  // - When in the same depth, sort by the filename (template < layout < page and others)\n\n  // Sort the connections as described above.\n  const sortedCSSImports = Object.entries(mergedCSSimports).sort((a, b) => {\n    const [aPath] = a\n    const [bPath] = b\n\n    const aDepth = aPath.split('/').length\n    const bDepth = bPath.split('/').length\n\n    if (aDepth !== bDepth) {\n      return aDepth - bDepth\n    }\n\n    const aName = path.parse(aPath).name\n    const bName = path.parse(bPath).name\n\n    const indexA = ['template', 'layout'].indexOf(aName)\n    const indexB = ['template', 'layout'].indexOf(bName)\n\n    if (indexA === -1) return 1\n    if (indexB === -1) return -1\n    return indexA - indexB\n  })\n\n  const dedupedCSSImports: CssImports = {}\n  const trackedCSSImports = new Set<string>()\n  for (const [entryName, cssImports] of sortedCSSImports) {\n    for (const cssImport of cssImports) {\n      if (trackedCSSImports.has(cssImport)) continue\n\n      // Only track CSS imports that are in files that can inherit CSS.\n      const filename = path.parse(entryName).name\n      if (['template', 'layout'].includes(filename)) {\n        trackedCSSImports.add(cssImport)\n      }\n\n      if (!dedupedCSSImports[entryName]) {\n        dedupedCSSImports[entryName] = []\n      }\n      dedupedCSSImports[entryName].push(cssImport)\n    }\n  }\n\n  return dedupedCSSImports\n}\n\nexport class FlightClientEntryPlugin {\n  dev: boolean\n  appDir: string\n  encryptionKey: string\n  isEdgeServer: boolean\n  assetPrefix: string\n  webpackRuntime: string\n\n  constructor(options: Options) {\n    this.dev = options.dev\n    this.appDir = options.appDir\n    this.isEdgeServer = options.isEdgeServer\n    this.assetPrefix = !this.dev && !this.isEdgeServer ? '../' : ''\n    this.encryptionKey = options.encryptionKey\n    this.webpackRuntime = this.isEdgeServer\n      ? EDGE_RUNTIME_WEBPACK\n      : DEFAULT_RUNTIME_WEBPACK\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.finishMake.tapPromise(PLUGIN_NAME, (compilation) =>\n      this.createClientEntries(compiler, compilation)\n    )\n\n    compiler.hooks.afterCompile.tap(PLUGIN_NAME, (compilation) => {\n      const recordModule = (modId: string, mod: any) => {\n        // Match Resource is undefined unless an import is using the inline match resource syntax\n        // https://webpack.js.org/api/loaders/#inline-matchresource\n        const modPath = mod.matchResource || mod.resourceResolveData?.path\n        const modQuery = mod.resourceResolveData?.query || ''\n        // query is already part of mod.resource\n        // so it's only necessary to add it for matchResource or mod.resourceResolveData\n        const modResource = modPath\n          ? modPath.startsWith(BARREL_OPTIMIZATION_PREFIX)\n            ? formatBarrelOptimizedResource(mod.resource, modPath)\n            : modPath + modQuery\n          : mod.resource\n\n        if (typeof modId !== 'undefined' && modResource) {\n          if (mod.layer === WEBPACK_LAYERS.reactServerComponents) {\n            const key = path\n              .relative(compiler.context, modResource)\n              .replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n\n            const moduleInfo: ModuleInfo = {\n              moduleId: modId,\n              async: compilation.moduleGraph.isAsync(mod),\n            }\n\n            if (this.isEdgeServer) {\n              pluginState.edgeRscModules[key] = moduleInfo\n            } else {\n              pluginState.rscModules[key] = moduleInfo\n            }\n          }\n        }\n\n        if (mod.layer !== WEBPACK_LAYERS.serverSideRendering) {\n          return\n        }\n\n        // Check mod resource to exclude the empty resource module like virtual module created by next-flight-client-entry-loader\n        if (typeof modId !== 'undefined' && modResource) {\n          // Note that this isn't that reliable as webpack is still possible to assign\n          // additional queries to make sure there's no conflict even using the `named`\n          // module ID strategy.\n          let ssrNamedModuleId = path.relative(compiler.context, modResource)\n\n          if (!ssrNamedModuleId.startsWith('.')) {\n            // TODO use getModuleId instead\n            ssrNamedModuleId = `./${normalizePathSep(ssrNamedModuleId)}`\n          }\n\n          const moduleInfo: ModuleInfo = {\n            moduleId: modId,\n            async: compilation.moduleGraph.isAsync(mod),\n          }\n\n          if (this.isEdgeServer) {\n            pluginState.edgeSsrModules[\n              ssrNamedModuleId.replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n            ] = moduleInfo\n          } else {\n            pluginState.ssrModules[ssrNamedModuleId] = moduleInfo\n          }\n        }\n      }\n\n      traverseModules(compilation, (mod, _chunk, _chunkGroup, modId) => {\n        if (modId) recordModule(modId, mod)\n      })\n    })\n\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        () => this.createActionAssets(compilation)\n      )\n    })\n  }\n\n  async createClientEntries(\n    compiler: webpack.Compiler,\n    compilation: webpack.Compilation\n  ) {\n    const addClientEntryAndSSRModulesList: Array<\n      ReturnType<typeof this.injectClientEntryAndSSRModules>\n    > = []\n    const createdSSRDependenciesForEntry: Record<\n      string,\n      ReturnType<typeof this.injectClientEntryAndSSRModules>[3][]\n    > = {}\n\n    const addActionEntryList: Array<ReturnType<typeof this.injectActionEntry>> =\n      []\n    const actionMapsPerEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n    const createdActionIds = new Set<string>()\n\n    // For each SC server compilation entry, we need to create its corresponding\n    // client component entry.\n    forEachEntryModule(compilation, ({ name, entryModule }) => {\n      const internalClientComponentEntryImports: ClientComponentImports = {}\n      const actionEntryImports = new Map<string, ActionIdNamePair[]>()\n      const clientEntriesToInject = []\n      const mergedCSSimports: CssImports = {}\n\n      for (const connection of getModuleReferencesInOrder(\n        entryModule,\n        compilation.moduleGraph\n      )) {\n        // Entry can be any user defined entry files such as layout, page, error, loading, etc.\n        let entryRequest = (\n          connection.dependency as unknown as webpack.NormalModule\n        ).request\n\n        if (entryRequest.endsWith(WEBPACK_RESOURCE_QUERIES.metadataRoute)) {\n          const { filePath, isDynamicRouteExtension } =\n            getMetadataRouteResource(entryRequest)\n\n          if (isDynamicRouteExtension === '1') {\n            entryRequest = filePath\n          }\n        }\n\n        const { clientComponentImports, actionImports, cssImports } =\n          this.collectComponentInfoFromServerEntryDependency({\n            entryRequest,\n            compilation,\n            resolvedModule: connection.resolvedModule,\n          })\n\n        actionImports.forEach(([dep, actions]) =>\n          actionEntryImports.set(dep, actions)\n        )\n\n        const isAbsoluteRequest = path.isAbsolute(entryRequest)\n\n        // Next.js internals are put into a separate entry.\n        if (!isAbsoluteRequest) {\n          Object.keys(clientComponentImports).forEach(\n            (value) => (internalClientComponentEntryImports[value] = new Set())\n          )\n          continue\n        }\n\n        // TODO-APP: Enable these lines. This ensures no entrypoint is created for layout/page when there are no client components.\n        // Currently disabled because it causes test failures in CI.\n        // if (clientImports.length === 0 && actionImports.length === 0) {\n        //   continue\n        // }\n\n        const relativeRequest = isAbsoluteRequest\n          ? path.relative(compilation.options.context!, entryRequest)\n          : entryRequest\n\n        // Replace file suffix as `.js` will be added.\n        // bundlePath will have app/ prefix but not src/.\n        // e.g. src/app/foo/page.js -> app/foo/page\n        let bundlePath = normalizePathSep(\n          relativeRequest.replace(/\\.[^.\\\\/]+$/, '').replace(/^src[\\\\/]/, '')\n        )\n\n        // For metadata routes, the entry name can be used as the bundle path,\n        // as it has been normalized already.\n        // e.g.\n        // When `relativeRequest` is 'src/app/sitemap.js',\n        // `appDirRelativeRequest` will be '/sitemap.js'\n        // then `isMetadataEntryFile` will be `true`\n        const appDirRelativeRequest = relativeRequest\n          .replace(/^src[\\\\/]/, '')\n          .replace(/^app[\\\\/]/, '/')\n        const isMetadataEntryFile = isMetadataRouteFile(\n          appDirRelativeRequest,\n          DEFAULT_METADATA_ROUTE_EXTENSIONS,\n          true\n        )\n        if (isMetadataEntryFile) {\n          bundlePath = name\n        }\n\n        Object.assign(mergedCSSimports, cssImports)\n        clientEntriesToInject.push({\n          compiler,\n          compilation,\n          entryName: name,\n          clientComponentImports,\n          bundlePath,\n          absolutePagePath: entryRequest,\n        })\n\n        // The webpack implementation of writing the client reference manifest relies on all entrypoints writing a page.js even when there is no client components in the page.\n        // It needs the file in order to write the reference manifest for the path in the `.next/server` folder.\n        // TODO-APP: This could be better handled, however Turbopack does not have the same problem as we resolve client components in a single graph.\n        if (\n          name === `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}` &&\n          bundlePath === 'app/not-found'\n        ) {\n          clientEntriesToInject.push({\n            compiler,\n            compilation,\n            entryName: name,\n            clientComponentImports: {},\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            absolutePagePath: entryRequest,\n          })\n        }\n      }\n\n      // Make sure CSS imports are deduplicated before injecting the client entry\n      // and SSR modules.\n      const dedupedCSSImports = deduplicateCSSImportsForEntry(mergedCSSimports)\n      for (const clientEntryToInject of clientEntriesToInject) {\n        const injected = this.injectClientEntryAndSSRModules({\n          ...clientEntryToInject,\n          clientImports: {\n            ...clientEntryToInject.clientComponentImports,\n            ...(\n              dedupedCSSImports[clientEntryToInject.absolutePagePath] || []\n            ).reduce<ClientComponentImports>((res, curr) => {\n              res[curr] = new Set()\n              return res\n            }, {}),\n          },\n        })\n\n        // Track all created SSR dependencies for each entry from the server layer.\n        if (!createdSSRDependenciesForEntry[clientEntryToInject.entryName]) {\n          createdSSRDependenciesForEntry[clientEntryToInject.entryName] = []\n        }\n        createdSSRDependenciesForEntry[clientEntryToInject.entryName].push(\n          injected[3]\n        )\n\n        addClientEntryAndSSRModulesList.push(injected)\n      }\n\n      if (!isAppRouteRoute(name)) {\n        // Create internal app\n        addClientEntryAndSSRModulesList.push(\n          this.injectClientEntryAndSSRModules({\n            compiler,\n            compilation,\n            entryName: name,\n            clientImports: { ...internalClientComponentEntryImports },\n            bundlePath: APP_CLIENT_INTERNALS,\n          })\n        )\n      }\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerEntry[name]) {\n          actionMapsPerEntry[name] = new Map()\n        }\n        actionMapsPerEntry[name] = new Map([\n          ...actionMapsPerEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    })\n\n    for (const [name, actionEntryImports] of Object.entries(\n      actionMapsPerEntry\n    )) {\n      addActionEntryList.push(\n        this.injectActionEntry({\n          compiler,\n          compilation,\n          actions: actionEntryImports,\n          entryName: name,\n          bundlePath: name,\n          createdActionIds,\n        })\n      )\n    }\n\n    // Invalidate in development to trigger recompilation\n    const invalidator = getInvalidator(compiler.outputPath)\n    // Check if any of the entry injections need an invalidation\n    if (\n      invalidator &&\n      addClientEntryAndSSRModulesList.some(\n        ([shouldInvalidate]) => shouldInvalidate === true\n      )\n    ) {\n      invalidator.invalidate([COMPILER_NAMES.client])\n    }\n\n    // Client compiler is invalidated before awaiting the compilation of the SSR\n    // and RSC client component entries so that the client compiler is running\n    // in parallel to the server compiler.\n    await Promise.all(\n      addClientEntryAndSSRModulesList.flatMap((addClientEntryAndSSRModules) => [\n        addClientEntryAndSSRModules[1],\n        addClientEntryAndSSRModules[2],\n      ])\n    )\n\n    // Wait for action entries to be added.\n    await Promise.all(addActionEntryList)\n\n    const addedClientActionEntryList: Promise<any>[] = []\n    const actionMapsPerClientEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n\n    // We need to create extra action entries that are created from the\n    // client layer.\n    // Start from each entry's created SSR dependency from our previous step.\n    for (const [name, ssrEntryDependencies] of Object.entries(\n      createdSSRDependenciesForEntry\n    )) {\n      // Collect from all entries, e.g. layout.js, page.js, loading.js, ...\n      // add aggregate them.\n      const actionEntryImports = this.collectClientActionsFromDependencies({\n        compilation,\n        dependencies: ssrEntryDependencies,\n      })\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerClientEntry[name]) {\n          actionMapsPerClientEntry[name] = new Map()\n        }\n        actionMapsPerClientEntry[name] = new Map([\n          ...actionMapsPerClientEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    }\n\n    for (const [entryName, actionEntryImports] of Object.entries(\n      actionMapsPerClientEntry\n    )) {\n      // If an action method is already created in the server layer, we don't\n      // need to create it again in the action layer.\n      // This is to avoid duplicate action instances and make sure the module\n      // state is shared.\n      let remainingClientImportedActions = false\n      const remainingActionEntryImports = new Map<string, ActionIdNamePair[]>()\n      for (const [dep, actions] of actionEntryImports) {\n        const remainingActionNames = []\n        for (const action of actions) {\n          if (!createdActionIds.has(entryName + '@' + action.id)) {\n            remainingActionNames.push(action)\n          }\n        }\n        if (remainingActionNames.length > 0) {\n          remainingActionEntryImports.set(dep, remainingActionNames)\n          remainingClientImportedActions = true\n        }\n      }\n\n      if (remainingClientImportedActions) {\n        addedClientActionEntryList.push(\n          this.injectActionEntry({\n            compiler,\n            compilation,\n            actions: remainingActionEntryImports,\n            entryName,\n            bundlePath: entryName,\n            fromClient: true,\n            createdActionIds,\n          })\n        )\n      }\n    }\n\n    await Promise.all(addedClientActionEntryList)\n  }\n\n  collectClientActionsFromDependencies({\n    compilation,\n    dependencies,\n  }: {\n    compilation: webpack.Compilation\n    dependencies: ReturnType<typeof webpack.EntryPlugin.createDependency>[]\n  }) {\n    // action file path -> action names\n    const collectedActions = new Map<string, ActionIdNamePair[]>()\n\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedModule = new Set<string>()\n    const visitedEntry = new Set<string>()\n\n    const collectActions = ({\n      entryRequest,\n      resolvedModule,\n    }: {\n      entryRequest: string\n      resolvedModule: any\n    }) => {\n      const collectActionsInDep = (mod: webpack.NormalModule): void => {\n        if (!mod) return\n\n        const modResource = getModuleResource(mod)\n\n        if (!modResource) return\n\n        if (visitedModule.has(modResource)) return\n        visitedModule.add(modResource)\n\n        const actionIds = getModuleBuildInfo(mod).rsc?.actionIds\n        if (actionIds) {\n          collectedActions.set(\n            modResource,\n            Object.entries(actionIds).map(([id, exportedName]) => ({\n              id,\n              exportedName,\n            }))\n          )\n        }\n\n        // Collect used exported actions transversely.\n        getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n          (connection: any) => {\n            collectActionsInDep(\n              connection.resolvedModule as webpack.NormalModule\n            )\n          }\n        )\n      }\n\n      // Don't traverse the module graph anymore once hitting the action layer.\n      if (\n        entryRequest &&\n        !entryRequest.includes('next-flight-action-entry-loader')\n      ) {\n        // Traverse the module graph to find all client components.\n        collectActionsInDep(resolvedModule)\n      }\n    }\n\n    for (const entryDependency of dependencies) {\n      const ssrEntryModule =\n        compilation.moduleGraph.getResolvedModule(entryDependency)!\n      for (const connection of getModuleReferencesInOrder(\n        ssrEntryModule,\n        compilation.moduleGraph\n      )) {\n        const depModule = connection.dependency\n        const request = (depModule as unknown as webpack.NormalModule).request\n\n        // It is possible that the same entry is added multiple times in the\n        // connection graph. We can just skip these to speed up the process.\n        if (visitedEntry.has(request)) continue\n        visitedEntry.add(request)\n\n        collectActions({\n          entryRequest: request,\n          resolvedModule: connection.resolvedModule,\n        })\n      }\n    }\n\n    return collectedActions\n  }\n\n  collectComponentInfoFromServerEntryDependency({\n    entryRequest,\n    compilation,\n    resolvedModule,\n  }: {\n    entryRequest: string\n    compilation: webpack.Compilation\n    resolvedModule: any /* Dependency */\n  }): {\n    cssImports: CssImports\n    clientComponentImports: ClientComponentImports\n    actionImports: [string, ActionIdNamePair[]][]\n  } {\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedOfClientComponentsTraverse = new Set()\n\n    // Info to collect.\n    const clientComponentImports: ClientComponentImports = {}\n    const actionImports: [string, ActionIdNamePair[]][] = []\n    const CSSImports = new Set<string>()\n\n    const filterClientComponents = (\n      mod: webpack.NormalModule,\n      importedIdentifiers: string[]\n    ): void => {\n      if (!mod) return\n\n      const modResource = getModuleResource(mod)\n\n      if (!modResource) return\n      if (visitedOfClientComponentsTraverse.has(modResource)) {\n        if (clientComponentImports[modResource]) {\n          addClientImport(\n            mod,\n            modResource,\n            clientComponentImports,\n            importedIdentifiers,\n            false\n          )\n        }\n        return\n      }\n      visitedOfClientComponentsTraverse.add(modResource)\n\n      const actionIds = getModuleBuildInfo(mod).rsc?.actionIds\n      if (actionIds) {\n        actionImports.push([\n          modResource,\n          Object.entries(actionIds).map(([id, exportedName]) => ({\n            id,\n            exportedName,\n          })),\n        ])\n      }\n\n      if (isCSSMod(mod)) {\n        const sideEffectFree =\n          mod.factoryMeta && (mod.factoryMeta as any).sideEffectFree\n\n        if (sideEffectFree) {\n          const unused = !compilation.moduleGraph\n            .getExportsInfo(mod)\n            .isModuleUsed(this.webpackRuntime)\n\n          if (unused) return\n        }\n\n        CSSImports.add(modResource)\n      } else if (isClientComponentEntryModule(mod)) {\n        if (!clientComponentImports[modResource]) {\n          clientComponentImports[modResource] = new Set()\n        }\n        addClientImport(\n          mod,\n          modResource,\n          clientComponentImports,\n          importedIdentifiers,\n          true\n        )\n\n        return\n      }\n\n      getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n        (connection: any) => {\n          let dependencyIds: string[] = []\n\n          // `ids` are the identifiers that are imported from the dependency,\n          // if it's present, it's an array of strings.\n          if (connection.dependency?.ids) {\n            dependencyIds.push(...connection.dependency.ids)\n          } else {\n            dependencyIds = ['*']\n          }\n\n          filterClientComponents(connection.resolvedModule, dependencyIds)\n        }\n      )\n    }\n\n    // Traverse the module graph to find all client components.\n    filterClientComponents(resolvedModule, [])\n\n    return {\n      clientComponentImports,\n      cssImports: CSSImports.size\n        ? {\n            [entryRequest]: Array.from(CSSImports),\n          }\n        : {},\n      actionImports,\n    }\n  }\n\n  injectClientEntryAndSSRModules({\n    compiler,\n    compilation,\n    entryName,\n    clientImports,\n    bundlePath,\n    absolutePagePath,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    entryName: string\n    clientImports: ClientComponentImports\n    bundlePath: string\n    absolutePagePath?: string\n  }): [\n    shouldInvalidate: boolean,\n    addSSREntryPromise: Promise<void>,\n    addRSCEntryPromise: Promise<void>,\n    ssrDep: ReturnType<typeof webpack.EntryPlugin.createDependency>,\n  ] {\n    const bundler = getWebpackBundler()\n    let shouldInvalidate = false\n\n    const modules = Object.keys(clientImports)\n      .sort((a, b) => (regexCSS.test(b) ? 1 : a.localeCompare(b)))\n      .map((clientImportPath) => ({\n        request: clientImportPath,\n        ids: [...clientImports[clientImportPath]],\n      }))\n\n    // For the client entry, we always use the CJS build of Next.js. If the\n    // server is using the ESM build (when using the Edge runtime), we need to\n    // replace them.\n    const clientBrowserLoader = `next-flight-client-entry-loader?${stringify({\n      modules: (this.isEdgeServer\n        ? modules.map(({ request, ids }) => ({\n            request: request.replace(\n              /[\\\\/]next[\\\\/]dist[\\\\/]esm[\\\\/]/,\n              '/next/dist/'.replace(/\\//g, path.sep)\n            ),\n            ids,\n          }))\n        : modules\n      ).map((x) => JSON.stringify(x)),\n      server: false,\n    })}!`\n\n    const clientServerLoader = `next-flight-client-entry-loader?${stringify({\n      modules: modules.map((x) => JSON.stringify(x)),\n      server: true,\n    })}!`\n\n    // Add for the client compilation\n    // Inject the entry to the client compiler.\n    if (this.dev) {\n      const entries = getEntries(compiler.outputPath)\n      const pageKey = getEntryKey(\n        COMPILER_NAMES.client,\n        PAGE_TYPES.APP,\n        bundlePath\n      )\n\n      if (!entries[pageKey]) {\n        entries[pageKey] = {\n          type: EntryTypes.CHILD_ENTRY,\n          parentEntries: new Set([entryName]),\n          absoluteEntryFilePath: absolutePagePath,\n          bundlePath,\n          request: clientBrowserLoader,\n          dispose: false,\n          lastActiveTime: Date.now(),\n        }\n        shouldInvalidate = true\n      } else {\n        const entryData = entries[pageKey]\n        // New version of the client loader\n        if (entryData.request !== clientBrowserLoader) {\n          entryData.request = clientBrowserLoader\n          shouldInvalidate = true\n        }\n        if (entryData.type === EntryTypes.CHILD_ENTRY) {\n          entryData.parentEntries.add(entryName)\n        }\n        entryData.dispose = false\n        entryData.lastActiveTime = Date.now()\n      }\n    } else {\n      pluginState.injectedClientEntries[bundlePath] = clientBrowserLoader\n    }\n\n    const clientComponentSSREntryDep = bundler.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    const clientComponentRSCEntryDep = bundler.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    return [\n      shouldInvalidate,\n      // Add the entries to the server compiler for the SSR and RSC layers. The\n      // promises are awaited later using `Promise.all` in order to parallelize\n      // adding the entries.\n      this.addEntry(compilation, compiler.context, clientComponentSSREntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.serverSideRendering,\n      }),\n      this.addEntry(compilation, compiler.context, clientComponentRSCEntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.reactServerComponents,\n      }),\n      clientComponentSSREntryDep,\n    ]\n  }\n\n  injectActionEntry({\n    compiler,\n    compilation,\n    actions,\n    entryName,\n    bundlePath,\n    fromClient,\n    createdActionIds,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    actions: Map<string, ActionIdNamePair[]>\n    entryName: string\n    bundlePath: string\n    createdActionIds: Set<string>\n    fromClient?: boolean\n  }) {\n    const bundler = getWebpackBundler()\n    const actionsArray = Array.from(actions.entries())\n    for (const [, actionsFromModule] of actions) {\n      for (const { id } of actionsFromModule) {\n        createdActionIds.add(entryName + '@' + id)\n      }\n    }\n\n    if (actionsArray.length === 0) {\n      return Promise.resolve()\n    }\n\n    const actionLoader = `next-flight-action-entry-loader?${stringify({\n      actions: JSON.stringify(\n        actionsArray satisfies FlightActionEntryLoaderActions\n      ),\n      __client_imported__: fromClient,\n    })}!`\n\n    const currentCompilerServerActions = this.isEdgeServer\n      ? pluginState.edgeServerActions\n      : pluginState.serverActions\n\n    for (const [, actionsFromModule] of actionsArray) {\n      for (const { id } of actionsFromModule) {\n        if (typeof currentCompilerServerActions[id] === 'undefined') {\n          currentCompilerServerActions[id] = {\n            workers: {},\n            layer: {},\n          }\n        }\n        currentCompilerServerActions[id].workers[bundlePath] = {\n          moduleId: '', // TODO: What's the meaning of this?\n          async: false,\n        }\n\n        currentCompilerServerActions[id].layer[bundlePath] = fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents\n      }\n    }\n\n    // Inject the entry to the server compiler\n    const actionEntryDep = bundler.EntryPlugin.createDependency(actionLoader, {\n      name: bundlePath,\n    })\n\n    return this.addEntry(\n      compilation,\n      // Reuse compilation context.\n      compiler.context,\n      actionEntryDep,\n      {\n        name: entryName,\n        layer: fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents,\n      }\n    )\n  }\n\n  addEntry(\n    compilation: webpack.Compilation,\n    context: string,\n    dependency: webpack.Dependency,\n    options: webpack.EntryOptions\n  ): Promise<any> /* Promise<module> */ {\n    return new Promise((resolve, reject) => {\n      if ('rspack' in compilation.compiler) {\n        compilation.addInclude(context, dependency, options, (err, module) => {\n          if (err) {\n            return reject(err)\n          }\n\n          compilation.moduleGraph\n            .getExportsInfo(module!)\n            .setUsedInUnknownWay(\n              this.isEdgeServer ? EDGE_RUNTIME_WEBPACK : DEFAULT_RUNTIME_WEBPACK\n            )\n          return resolve(module)\n        })\n      } else {\n        const entry = compilation.entries.get(options.name!)!\n        entry.includeDependencies.push(dependency)\n        compilation.hooks.addEntry.call(entry as any, options)\n        compilation.addModuleTree(\n          {\n            context,\n            dependency,\n            contextInfo: { issuerLayer: options.layer },\n          },\n          (err: any, module: any) => {\n            if (err) {\n              compilation.hooks.failedEntry.call(dependency, options, err)\n              return reject(err)\n            }\n\n            compilation.hooks.succeedEntry.call(dependency, options, module)\n\n            compilation.moduleGraph\n              .getExportsInfo(module)\n              .setUsedInUnknownWay(\n                this.isEdgeServer\n                  ? EDGE_RUNTIME_WEBPACK\n                  : DEFAULT_RUNTIME_WEBPACK\n              )\n\n            return resolve(module)\n          }\n        )\n      }\n    })\n  }\n\n  async createActionAssets(compilation: webpack.Compilation) {\n    const serverActions: ActionManifest['node'] = {}\n    const edgeServerActions: ActionManifest['edge'] = {}\n\n    traverseModules(compilation, (mod, _chunk, chunkGroup, modId) => {\n      // Go through all action entries and record the module ID for each entry.\n      if (\n        chunkGroup.name &&\n        mod.request &&\n        modId &&\n        /next-flight-action-entry-loader/.test(mod.request)\n      ) {\n        const fromClient = /&__client_imported__=true/.test(mod.request)\n\n        const mapping = this.isEdgeServer\n          ? pluginState.edgeServerActionModules\n          : pluginState.serverActionModules\n\n        if (!mapping[chunkGroup.name]) {\n          mapping[chunkGroup.name] = {}\n        }\n        mapping[chunkGroup.name][fromClient ? 'client' : 'server'] = {\n          moduleId: modId,\n          async: compilation.moduleGraph.isAsync(mod),\n        }\n      }\n    })\n\n    for (let id in pluginState.serverActions) {\n      const action = pluginState.serverActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.serverActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      serverActions[id] = action\n    }\n\n    for (let id in pluginState.edgeServerActions) {\n      const action = pluginState.edgeServerActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.edgeServerActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      edgeServerActions[id] = action\n    }\n\n    const serverManifest = {\n      node: serverActions,\n      edge: edgeServerActions,\n      encryptionKey: this.encryptionKey,\n    }\n    const edgeServerManifest = {\n      ...serverManifest,\n      encryptionKey: 'process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY',\n    }\n\n    const json = JSON.stringify(serverManifest, null, this.dev ? 2 : undefined)\n    const edgeJson = JSON.stringify(\n      edgeServerManifest,\n      null,\n      this.dev ? 2 : undefined\n    )\n\n    compilation.emitAsset(\n      `${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.js`,\n      new sources.RawSource(\n        `self.__RSC_SERVER_MANIFEST=${JSON.stringify(edgeJson)}`\n      ) as unknown as webpack.sources.RawSource\n    )\n    compilation.emitAsset(\n      `${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.json`,\n      new sources.RawSource(json) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n\nfunction addClientImport(\n  mod: webpack.NormalModule,\n  modRequest: string,\n  clientComponentImports: ClientComponentImports,\n  importedIdentifiers: string[],\n  isFirstVisitModule: boolean\n) {\n  const clientEntryType = getModuleBuildInfo(mod).rsc?.clientEntryType\n  const isCjsModule = clientEntryType === 'cjs'\n  const assumedSourceType = getAssumedSourceType(\n    mod,\n    isCjsModule ? 'commonjs' : 'auto'\n  )\n\n  const clientImportsSet = clientComponentImports[modRequest]\n\n  if (importedIdentifiers[0] === '*') {\n    // If there's collected import path with named import identifiers,\n    // or there's nothing in collected imports are empty.\n    // we should include the whole module.\n    if (!isFirstVisitModule && [...clientImportsSet][0] !== '*') {\n      clientComponentImports[modRequest] = new Set(['*'])\n    }\n  } else {\n    const isAutoModuleSourceType = assumedSourceType === 'auto'\n    if (isAutoModuleSourceType) {\n      clientComponentImports[modRequest] = new Set(['*'])\n    } else {\n      // If it's not analyzed as named ESM exports, e.g. if it's mixing `export *` with named exports,\n      // We'll include all modules since it's not able to do tree-shaking.\n      for (const name of importedIdentifiers) {\n        // For cjs module default import, we include the whole module since\n        const isCjsDefaultImport = isCjsModule && name === 'default'\n\n        // Always include __esModule along with cjs module default export,\n        // to make sure it work with client module proxy from React.\n        if (isCjsDefaultImport) {\n          clientComponentImports[modRequest].add('__esModule')\n        }\n\n        clientComponentImports[modRequest].add(name)\n      }\n    }\n  }\n}\n\nfunction getModuleResource(mod: webpack.NormalModule): string {\n  const modPath: string = mod.resourceResolveData?.path || ''\n  const modQuery = mod.resourceResolveData?.query || ''\n  // We have to always use the resolved request here to make sure the\n  // server and client are using the same module path (required by RSC), as\n  // the server compiler and client compiler have different resolve configs.\n  let modResource: string = modPath + modQuery\n\n  // Context modules don't have a resource path, we use the identifier instead.\n  if (mod.constructor.name === 'ContextModule') {\n    modResource = mod.identifier()\n  }\n\n  // For the barrel optimization, we need to use the match resource instead\n  // because there will be 2 modules for the same file (same resource path)\n  // but they're different modules and can't be deduped via `visitedModule`.\n  // The first module is a virtual re-export module created by the loader.\n  if (mod.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n    modResource = mod.matchResource + ':' + modResource\n  }\n\n  if (mod.resource === `?${WEBPACK_RESOURCE_QUERIES.metadataRoute}`) {\n    return getMetadataRouteResource(mod.rawRequest).filePath\n  }\n\n  return modResource\n}\n\nfunction getMetadataRouteResource(request: string): MetadataRouteLoaderOptions {\n  // e.g. next-metadata-route-loader?filePath=<some-url-encoded-path>&isDynamicRouteExtension=1!?__next_metadata_route__\n  const query = request.split('!')[0].split('next-metadata-route-loader?')[1]\n\n  return parse(query) as MetadataRouteLoaderOptions\n}\n"], "names": ["FlightClientEntryPlugin", "PLUGIN_NAME", "pluginState", "getProxiedPluginState", "serverActions", "edgeServerActions", "serverActionModules", "edgeServerActionModules", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "path", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "webpackRuntime", "EDGE_RUNTIME_WEBPACK", "DEFAULT_RUNTIME_WEBPACK", "apply", "compiler", "hooks", "finishMake", "tapPromise", "compilation", "createClientEntries", "afterCompile", "tap", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "resource", "layer", "WEBPACK_LAYERS", "reactServerComponents", "key", "relative", "context", "replace", "moduleInfo", "moduleId", "async", "moduleGraph", "isAsync", "serverSideRendering", "ssrNamedModuleId", "normalizePathSep", "traverseModules", "_chunk", "_chunkGroup", "make", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActionIds", "forEachEntryModule", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "getModuleReferencesInOrder", "entryRequest", "dependency", "request", "endsWith", "WEBPACK_RESOURCE_QUERIES", "metadataRoute", "filePath", "isDynamicRouteExtension", "getMetadataRouteResource", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "actions", "set", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "appDirRelativeRequest", "isMetadataEntryFile", "isMetadataRouteFile", "DEFAULT_METADATA_ROUTE_EXTENSIONS", "assign", "absolutePagePath", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "isAppRouteRoute", "APP_CLIENT_INTERNALS", "size", "injectActionEntry", "invalidator", "getInvalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "COMPILER_NAMES", "client", "Promise", "all", "flatMap", "addClientEntryAndSSRModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "dependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "action", "id", "fromClient", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "getModuleBuildInfo", "getModuleResource", "actionIds", "rsc", "map", "exportedName", "entryDependency", "ssrEntryModule", "getResolvedModule", "depModule", "visitedOfClientComponentsTraverse", "CSSImports", "filterClientComponents", "importedIdentifiers", "addClientImport", "isCSSMod", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "isClientComponentEntryModule", "dependencyIds", "ids", "Array", "from", "bundler", "getWebpackBundler", "modules", "regexCSS", "test", "localeCompare", "clientImportPath", "clientBrowserLoader", "stringify", "sep", "x", "JSON", "server", "clientServerLoader", "getEntries", "page<PERSON><PERSON>", "getEntry<PERSON>ey", "PAGE_TYPES", "APP", "type", "EntryTypes", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentSSREntryDep", "EntryPlugin", "createDependency", "clientComponentRSCEntryDep", "addEntry", "actionsArray", "actionsFromModule", "resolve", "actionLoader", "__client_imported__", "currentCompilerServerActions", "workers", "<PERSON><PERSON><PERSON><PERSON>", "actionEntryDep", "reject", "addInclude", "err", "module", "setUsedInUnknownWay", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "serverManifest", "node", "edge", "edgeServerManifest", "json", "undefined", "edgeJson", "emitAsset", "SERVER_REFERENCE_MANIFEST", "sources", "RawSource", "modRequest", "isFirstVisitModule", "clientEntryType", "isCjsModule", "assumedSourceType", "getAssumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport", "identifier", "rawRequest"], "mappings": ";;;;+BA0KaA;;;eAAAA;;;yBArKW;6BACS;6DAChB;sCAOV;2BAIA;4BASA;uBAKA;wBAMA;kCAC0B;8BACK;2BACX;oCACQ;kCACE;iCACL;iCAIzB;0EAGuB;;;;;;AAS9B,MAAMC,cAAc;AA4BpB,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxC,gDAAgD;IAChDC,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,qBAAqB,CAAC;IAItBC,yBAAyB,CAAC;IAI1BC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQC,aAAI,CAACC,KAAK,CAACR,OAAOS,IAAI;QACpC,MAAMC,QAAQH,aAAI,CAACC,KAAK,CAACP,OAAOQ,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACN;QAC9C,MAAMO,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIxB,iBAAkB;QACtD,KAAK,MAAMyB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWd,aAAI,CAACC,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEO,MAAMnC;IAQX8C,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;QAC1C,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,YAAY,GACnCI,gCAAoB,GACpBC,mCAAuB;IAC7B;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,UAAU,CAACC,UAAU,CAAC3D,aAAa,CAAC4D,cACjD,IAAI,CAACC,mBAAmB,CAACL,UAAUI;QAGrCJ,SAASC,KAAK,CAACK,YAAY,CAACC,GAAG,CAAC/D,aAAa,CAAC4D;YAC5C,MAAMI,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBvC,IAAI;gBAClE,MAAM2C,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACC,sCAA0B,IAC3CC,IAAAA,qCAA6B,EAACT,IAAIU,QAAQ,EAAET,WAC5CA,UAAUG,WACZJ,IAAIU,QAAQ;gBAEhB,IAAI,OAAOX,UAAU,eAAeO,aAAa;oBAC/C,IAAIN,IAAIW,KAAK,KAAKC,yBAAc,CAACC,qBAAqB,EAAE;wBACtD,MAAMC,MAAMrD,aAAI,CACbsD,QAAQ,CAACzB,SAAS0B,OAAO,EAAEV,aAC3BW,OAAO,CAAC,uBAAuB;wBAElC,MAAMC,aAAyB;4BAC7BC,UAAUpB;4BACVqB,OAAO1B,YAAY2B,WAAW,CAACC,OAAO,CAACtB;wBACzC;wBAEA,IAAI,IAAI,CAACjB,YAAY,EAAE;4BACrBhD,YAAYS,cAAc,CAACsE,IAAI,GAAGI;wBACpC,OAAO;4BACLnF,YAAYQ,UAAU,CAACuE,IAAI,GAAGI;wBAChC;oBACF;gBACF;gBAEA,IAAIlB,IAAIW,KAAK,KAAKC,yBAAc,CAACW,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOxB,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIkB,mBAAmB/D,aAAI,CAACsD,QAAQ,CAACzB,SAAS0B,OAAO,EAAEV;oBAEvD,IAAI,CAACkB,iBAAiBjB,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BiB,mBAAmB,CAAC,EAAE,EAAEC,IAAAA,kCAAgB,EAACD,mBAAmB;oBAC9D;oBAEA,MAAMN,aAAyB;wBAC7BC,UAAUpB;wBACVqB,OAAO1B,YAAY2B,WAAW,CAACC,OAAO,CAACtB;oBACzC;oBAEA,IAAI,IAAI,CAACjB,YAAY,EAAE;wBACrBhD,YAAYO,cAAc,CACxBkF,iBAAiBP,OAAO,CAAC,uBAAuB,eACjD,GAAGC;oBACN,OAAO;wBACLnF,YAAYM,UAAU,CAACmF,iBAAiB,GAAGN;oBAC7C;gBACF;YACF;YAEAQ,IAAAA,uBAAe,EAAChC,aAAa,CAACM,KAAK2B,QAAQC,aAAa7B;gBACtD,IAAIA,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAV,SAASC,KAAK,CAACsC,IAAI,CAAChC,GAAG,CAAC/D,aAAa,CAAC4D;YACpCA,YAAYH,KAAK,CAACuC,aAAa,CAACrC,UAAU,CACxC;gBACE9B,MAAM7B;gBACNiG,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,IAAM,IAAI,CAACC,kBAAkB,CAACzC;QAElC;IACF;IAEA,MAAMC,oBACJL,QAA0B,EAC1BI,WAAgC,EAChC;QACA,MAAM0C,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAGF,CAAC;QACL,MAAMC,mBAAmB,IAAItE;QAE7B,4EAA4E;QAC5E,0BAA0B;QAC1BuE,IAAAA,0BAAkB,EAAC/C,aAAa,CAAC,EAAE/B,IAAI,EAAE+E,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAMnG,mBAA+B,CAAC;YAEtC,KAAK,MAAMoG,cAAcC,IAAAA,kCAA0B,EACjDN,aACAhD,YAAY2B,WAAW,EACtB;gBACD,uFAAuF;gBACvF,IAAI4B,eAAe,AACjBF,WAAWG,UAAU,CACrBC,OAAO;gBAET,IAAIF,aAAaG,QAAQ,CAACC,mCAAwB,CAACC,aAAa,GAAG;oBACjE,MAAM,EAAEC,QAAQ,EAAEC,uBAAuB,EAAE,GACzCC,yBAAyBR;oBAE3B,IAAIO,4BAA4B,KAAK;wBACnCP,eAAeM;oBACjB;gBACF;gBAEA,MAAM,EAAEG,sBAAsB,EAAEC,aAAa,EAAEvF,UAAU,EAAE,GACzD,IAAI,CAACwF,6CAA6C,CAAC;oBACjDX;oBACAvD;oBACAmE,gBAAgBd,WAAWc,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,QAAQ,GACnCpB,mBAAmBqB,GAAG,CAACF,KAAKC;gBAG9B,MAAME,oBAAoBzG,aAAI,CAAC0G,UAAU,CAAClB;gBAE1C,mDAAmD;gBACnD,IAAI,CAACiB,mBAAmB;oBACtBrH,OAAOuH,IAAI,CAACV,wBAAwBI,OAAO,CACzC,CAACO,QAAW1B,mCAAmC,CAAC0B,MAAM,GAAG,IAAInG;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAMoG,kBAAkBJ,oBACpBzG,aAAI,CAACsD,QAAQ,CAACrB,YAAYd,OAAO,CAACoC,OAAO,EAAGiC,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,iDAAiD;gBACjD,2CAA2C;gBAC3C,IAAIsB,aAAa9C,IAAAA,kCAAgB,EAC/B6C,gBAAgBrD,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlE,sEAAsE;gBACtE,qCAAqC;gBACrC,OAAO;gBACP,kDAAkD;gBAClD,gDAAgD;gBAChD,4CAA4C;gBAC5C,MAAMuD,wBAAwBF,gBAC3BrD,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,aAAa;gBACxB,MAAMwD,sBAAsBC,IAAAA,oCAAmB,EAC7CF,uBACAG,kDAAiC,EACjC;gBAEF,IAAIF,qBAAqB;oBACvBF,aAAa5G;gBACf;gBAEAd,OAAO+H,MAAM,CAACjI,kBAAkByB;gBAChC0E,sBAAsBpE,IAAI,CAAC;oBACzBY;oBACAI;oBACAvB,WAAWR;oBACX+F;oBACAa;oBACAM,kBAAkB5B;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACEtF,SAAS,CAAC,GAAG,EAAEmH,4CAAgC,EAAE,IACjDP,eAAe,iBACf;oBACAzB,sBAAsBpE,IAAI,CAAC;wBACzBY;wBACAI;wBACAvB,WAAWR;wBACX+F,wBAAwB,CAAC;wBACzBa,YAAY,CAAC,GAAG,EAAEO,4CAAgC,EAAE;wBACpDD,kBAAkB5B;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAMjF,oBAAoBtB,8BAA8BC;YACxD,KAAK,MAAMoI,uBAAuBjC,sBAAuB;gBACvD,MAAMkC,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBrB,sBAAsB;wBAC7C,GAAG,AACD1F,CAAAA,iBAAiB,CAAC+G,oBAAoBF,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DM,MAAM,CAAyB,CAACC,KAAKC;4BACrCD,GAAG,CAACC,KAAK,GAAG,IAAInH;4BAChB,OAAOkH;wBACT,GAAG,CAAC,EAAE;oBACR;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAAC/C,8BAA8B,CAAC0C,oBAAoB5G,SAAS,CAAC,EAAE;oBAClEkE,8BAA8B,CAAC0C,oBAAoB5G,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACAkE,8BAA8B,CAAC0C,oBAAoB5G,SAAS,CAAC,CAACO,IAAI,CAChEsG,QAAQ,CAAC,EAAE;gBAGb5C,gCAAgC1D,IAAI,CAACsG;YACvC;YAEA,IAAI,CAACM,IAAAA,gCAAe,EAAC3H,OAAO;gBAC1B,sBAAsB;gBACtByE,gCAAgC1D,IAAI,CAClC,IAAI,CAACuG,8BAA8B,CAAC;oBAClC3F;oBACAI;oBACAvB,WAAWR;oBACXuH,eAAe;wBAAE,GAAGvC,mCAAmC;oBAAC;oBACxD4B,YAAYgB,gCAAoB;gBAClC;YAEJ;YAEA,IAAI3C,mBAAmB4C,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACjD,kBAAkB,CAAC5E,KAAK,EAAE;oBAC7B4E,kBAAkB,CAAC5E,KAAK,GAAG,IAAIkF;gBACjC;gBACAN,kBAAkB,CAAC5E,KAAK,GAAG,IAAIkF,IAAI;uBAC9BN,kBAAkB,CAAC5E,KAAK;uBACxBiF;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACjF,MAAMiF,mBAAmB,IAAI/F,OAAOC,OAAO,CACrDyF,oBACC;YACDD,mBAAmB5D,IAAI,CACrB,IAAI,CAAC+G,iBAAiB,CAAC;gBACrBnG;gBACAI;gBACAsE,SAASpB;gBACTzE,WAAWR;gBACX4G,YAAY5G;gBACZ6E;YACF;QAEJ;QAEA,qDAAqD;QACrD,MAAMkD,cAAcC,IAAAA,oCAAc,EAACrG,SAASsG,UAAU;QACtD,4DAA4D;QAC5D,IACEF,eACAtD,gCAAgCyD,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAJ,YAAYK,UAAU,CAAC;gBAACC,0BAAc,CAACC,MAAM;aAAC;QAChD;QAEA,4EAA4E;QAC5E,0EAA0E;QAC1E,sCAAsC;QACtC,MAAMC,QAAQC,GAAG,CACf/D,gCAAgCgE,OAAO,CAAC,CAACC,8BAAgC;gBACvEA,2BAA2B,CAAC,EAAE;gBAC9BA,2BAA2B,CAAC,EAAE;aAC/B;QAGH,uCAAuC;QACvC,MAAMH,QAAQC,GAAG,CAAC7D;QAElB,MAAMgE,6BAA6C,EAAE;QACrD,MAAMC,2BAGF,CAAC;QAEL,mEAAmE;QACnE,gBAAgB;QAChB,yEAAyE;QACzE,KAAK,MAAM,CAAC5I,MAAM6I,qBAAqB,IAAI3J,OAAOC,OAAO,CACvDuF,gCACC;YACD,qEAAqE;YACrE,sBAAsB;YACtB,MAAMO,qBAAqB,IAAI,CAAC6D,oCAAoC,CAAC;gBACnE/G;gBACAgH,cAAcF;YAChB;YAEA,IAAI5D,mBAAmB4C,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACe,wBAAwB,CAAC5I,KAAK,EAAE;oBACnC4I,wBAAwB,CAAC5I,KAAK,GAAG,IAAIkF;gBACvC;gBACA0D,wBAAwB,CAAC5I,KAAK,GAAG,IAAIkF,IAAI;uBACpC0D,wBAAwB,CAAC5I,KAAK;uBAC9BiF;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACzE,WAAWyE,mBAAmB,IAAI/F,OAAOC,OAAO,CAC1DyJ,0BACC;YACD,uEAAuE;YACvE,+CAA+C;YAC/C,uEAAuE;YACvE,mBAAmB;YACnB,IAAII,iCAAiC;YACrC,MAAMC,8BAA8B,IAAI/D;YACxC,KAAK,MAAM,CAACkB,KAAKC,QAAQ,IAAIpB,mBAAoB;gBAC/C,MAAMiE,uBAAuB,EAAE;gBAC/B,KAAK,MAAMC,UAAU9C,QAAS;oBAC5B,IAAI,CAACxB,iBAAiBlE,GAAG,CAACH,YAAY,MAAM2I,OAAOC,EAAE,GAAG;wBACtDF,qBAAqBnI,IAAI,CAACoI;oBAC5B;gBACF;gBACA,IAAID,qBAAqBvJ,MAAM,GAAG,GAAG;oBACnCsJ,4BAA4B3C,GAAG,CAACF,KAAK8C;oBACrCF,iCAAiC;gBACnC;YACF;YAEA,IAAIA,gCAAgC;gBAClCL,2BAA2B5H,IAAI,CAC7B,IAAI,CAAC+G,iBAAiB,CAAC;oBACrBnG;oBACAI;oBACAsE,SAAS4C;oBACTzI;oBACAoG,YAAYpG;oBACZ6I,YAAY;oBACZxE;gBACF;YAEJ;QACF;QAEA,MAAM0D,QAAQC,GAAG,CAACG;IACpB;IAEAG,qCAAqC,EACnC/G,WAAW,EACXgH,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAMO,mBAAmB,IAAIpE;QAE7B,gFAAgF;QAChF,MAAMqE,gBAAgB,IAAIhJ;QAC1B,MAAMiJ,eAAe,IAAIjJ;QAEzB,MAAMkJ,iBAAiB,CAAC,EACtBnE,YAAY,EACZY,cAAc,EAIf;YACC,MAAMwD,sBAAsB,CAACrH;oBAUTsH;gBATlB,IAAI,CAACtH,KAAK;gBAEV,MAAMM,cAAciH,kBAAkBvH;gBAEtC,IAAI,CAACM,aAAa;gBAElB,IAAI4G,cAAc5I,GAAG,CAACgC,cAAc;gBACpC4G,cAAczI,GAAG,CAAC6B;gBAElB,MAAMkH,aAAYF,0BAAAA,IAAAA,sCAAkB,EAACtH,KAAKyH,GAAG,qBAA3BH,wBAA6BE,SAAS;gBACxD,IAAIA,WAAW;oBACbP,iBAAiBhD,GAAG,CAClB3D,aACAzD,OAAOC,OAAO,CAAC0K,WAAWE,GAAG,CAAC,CAAC,CAACX,IAAIY,aAAa,GAAM,CAAA;4BACrDZ;4BACAY;wBACF,CAAA;gBAEJ;gBAEA,8CAA8C;gBAC9C3E,IAAAA,kCAA0B,EAAChD,KAAKN,YAAY2B,WAAW,EAAEyC,OAAO,CAC9D,CAACf;oBACCsE,oBACEtE,WAAWc,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEZ,gBACA,CAACA,aAAazE,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3D6I,oBAAoBxD;YACtB;QACF;QAEA,KAAK,MAAM+D,mBAAmBlB,aAAc;YAC1C,MAAMmB,iBACJnI,YAAY2B,WAAW,CAACyG,iBAAiB,CAACF;YAC5C,KAAK,MAAM7E,cAAcC,IAAAA,kCAA0B,EACjD6E,gBACAnI,YAAY2B,WAAW,EACtB;gBACD,MAAM0G,YAAYhF,WAAWG,UAAU;gBACvC,MAAMC,UAAU,AAAC4E,UAA8C5E,OAAO;gBAEtE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIgE,aAAa7I,GAAG,CAAC6E,UAAU;gBAC/BgE,aAAa1I,GAAG,CAAC0E;gBAEjBiE,eAAe;oBACbnE,cAAcE;oBACdU,gBAAgBd,WAAWc,cAAc;gBAC3C;YACF;QACF;QAEA,OAAOoD;IACT;IAEArD,8CAA8C,EAC5CX,YAAY,EACZvD,WAAW,EACXmE,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAMmE,oCAAoC,IAAI9J;QAE9C,mBAAmB;QACnB,MAAMwF,yBAAiD,CAAC;QACxD,MAAMC,gBAAgD,EAAE;QACxD,MAAMsE,aAAa,IAAI/J;QAEvB,MAAMgK,yBAAyB,CAC7BlI,KACAmI;gBAqBkBb;YAnBlB,IAAI,CAACtH,KAAK;YAEV,MAAMM,cAAciH,kBAAkBvH;YAEtC,IAAI,CAACM,aAAa;YAClB,IAAI0H,kCAAkC1J,GAAG,CAACgC,cAAc;gBACtD,IAAIoD,sBAAsB,CAACpD,YAAY,EAAE;oBACvC8H,gBACEpI,KACAM,aACAoD,wBACAyE,qBACA;gBAEJ;gBACA;YACF;YACAH,kCAAkCvJ,GAAG,CAAC6B;YAEtC,MAAMkH,aAAYF,0BAAAA,IAAAA,sCAAkB,EAACtH,KAAKyH,GAAG,qBAA3BH,wBAA6BE,SAAS;YACxD,IAAIA,WAAW;gBACb7D,cAAcjF,IAAI,CAAC;oBACjB4B;oBACAzD,OAAOC,OAAO,CAAC0K,WAAWE,GAAG,CAAC,CAAC,CAACX,IAAIY,aAAa,GAAM,CAAA;4BACrDZ;4BACAY;wBACF,CAAA;iBACD;YACH;YAEA,IAAIU,IAAAA,eAAQ,EAACrI,MAAM;gBACjB,MAAMsI,iBACJtI,IAAIuI,WAAW,IAAI,AAACvI,IAAIuI,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAC9I,YAAY2B,WAAW,CACpCoH,cAAc,CAACzI,KACf0I,YAAY,CAAC,IAAI,CAACxJ,cAAc;oBAEnC,IAAIsJ,QAAQ;gBACd;gBAEAP,WAAWxJ,GAAG,CAAC6B;YACjB,OAAO,IAAIqI,IAAAA,mCAA4B,EAAC3I,MAAM;gBAC5C,IAAI,CAAC0D,sBAAsB,CAACpD,YAAY,EAAE;oBACxCoD,sBAAsB,CAACpD,YAAY,GAAG,IAAIpC;gBAC5C;gBACAkK,gBACEpI,KACAM,aACAoD,wBACAyE,qBACA;gBAGF;YACF;YAEAnF,IAAAA,kCAA0B,EAAChD,KAAKN,YAAY2B,WAAW,EAAEyC,OAAO,CAC9D,CAACf;oBAKKA;gBAJJ,IAAI6F,gBAA0B,EAAE;gBAEhC,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAI7F,yBAAAA,WAAWG,UAAU,qBAArBH,uBAAuB8F,GAAG,EAAE;oBAC9BD,cAAclK,IAAI,IAAIqE,WAAWG,UAAU,CAAC2F,GAAG;gBACjD,OAAO;oBACLD,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAV,uBAAuBnF,WAAWc,cAAc,EAAE+E;YACpD;QAEJ;QAEA,2DAA2D;QAC3DV,uBAAuBrE,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACAtF,YAAY6J,WAAWzC,IAAI,GACvB;gBACE,CAACvC,aAAa,EAAE6F,MAAMC,IAAI,CAACd;YAC7B,IACA,CAAC;YACLtE;QACF;IACF;IAEAsB,+BAA+B,EAC7B3F,QAAQ,EACRI,WAAW,EACXvB,SAAS,EACT+G,aAAa,EACbX,UAAU,EACVM,gBAAgB,EAQjB,EAKC;QACA,MAAMmE,UAAUC,IAAAA,0BAAiB;QACjC,IAAInD,mBAAmB;QAEvB,MAAMoD,UAAUrM,OAAOuH,IAAI,CAACc,eACzBnI,IAAI,CAAC,CAACC,GAAGC,IAAOkM,eAAQ,CAACC,IAAI,CAACnM,KAAK,IAAID,EAAEqM,aAAa,CAACpM,IACvDyK,GAAG,CAAC,CAAC4B,mBAAsB,CAAA;gBAC1BnG,SAASmG;gBACTT,KAAK;uBAAI3D,aAAa,CAACoE,iBAAiB;iBAAC;YAC3C,CAAA;QAEF,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAEC,IAAAA,sBAAS,EAAC;YACvEN,SAAS,AAAC,CAAA,IAAI,CAACnK,YAAY,GACvBmK,QAAQxB,GAAG,CAAC,CAAC,EAAEvE,OAAO,EAAE0F,GAAG,EAAE,GAAM,CAAA;oBACjC1F,SAASA,QAAQlC,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAOxD,aAAI,CAACgM,GAAG;oBAEvCZ;gBACF,CAAA,KACAK,OAAM,EACRxB,GAAG,CAAC,CAACgC,IAAMC,KAAKH,SAAS,CAACE;YAC5BE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMC,qBAAqB,CAAC,gCAAgC,EAAEL,IAAAA,sBAAS,EAAC;YACtEN,SAASA,QAAQxB,GAAG,CAAC,CAACgC,IAAMC,KAAKH,SAAS,CAACE;YAC3CE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAAC/K,GAAG,EAAE;YACZ,MAAM/B,UAAUgN,IAAAA,gCAAU,EAACxK,SAASsG,UAAU;YAC9C,MAAMmE,UAAUC,IAAAA,iCAAW,EACzBhE,0BAAc,CAACC,MAAM,EACrBgE,qBAAU,CAACC,GAAG,EACd3F;YAGF,IAAI,CAACzH,OAAO,CAACiN,QAAQ,EAAE;gBACrBjN,OAAO,CAACiN,QAAQ,GAAG;oBACjBI,MAAMC,gCAAU,CAACC,WAAW;oBAC5BC,eAAe,IAAIpM,IAAI;wBAACC;qBAAU;oBAClCoM,uBAAuB1F;oBACvBN;oBACApB,SAASoG;oBACTiB,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACA7E,mBAAmB;YACrB,OAAO;gBACL,MAAM8E,YAAY9N,OAAO,CAACiN,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIa,UAAUzH,OAAO,KAAKoG,qBAAqB;oBAC7CqB,UAAUzH,OAAO,GAAGoG;oBACpBzD,mBAAmB;gBACrB;gBACA,IAAI8E,UAAUT,IAAI,KAAKC,gCAAU,CAACC,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAAC7L,GAAG,CAACN;gBAC9B;gBACAyM,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACL5O,YAAYU,qBAAqB,CAAC8H,WAAW,GAAGgF;QAClD;QAEA,MAAMsB,6BAA6B7B,QAAQ8B,WAAW,CAACC,gBAAgB,CACrElB,oBACA;YAAElM,MAAM4G;QAAW;QAGrB,MAAMyG,6BAA6BhC,QAAQ8B,WAAW,CAACC,gBAAgB,CACrElB,oBACA;YAAElM,MAAM4G;QAAW;QAGrB,OAAO;YACLuB;YACA,yEAAyE;YACzE,yEAAyE;YACzE,sBAAsB;YACtB,IAAI,CAACmF,QAAQ,CAACvL,aAAaJ,SAAS0B,OAAO,EAAE6J,4BAA4B;gBACvElN,MAAMQ;gBACNwC,OAAOC,yBAAc,CAACW,mBAAmB;YAC3C;YACA,IAAI,CAAC0J,QAAQ,CAACvL,aAAaJ,SAAS0B,OAAO,EAAEgK,4BAA4B;gBACvErN,MAAMQ;gBACNwC,OAAOC,yBAAc,CAACC,qBAAqB;YAC7C;YACAgK;SACD;IACH;IAEApF,kBAAkB,EAChBnG,QAAQ,EACRI,WAAW,EACXsE,OAAO,EACP7F,SAAS,EACToG,UAAU,EACVyC,UAAU,EACVxE,gBAAgB,EASjB,EAAE;QACD,MAAMwG,UAAUC,IAAAA,0BAAiB;QACjC,MAAMiC,eAAepC,MAAMC,IAAI,CAAC/E,QAAQlH,OAAO;QAC/C,KAAK,MAAM,GAAGqO,kBAAkB,IAAInH,QAAS;YAC3C,KAAK,MAAM,EAAE+C,EAAE,EAAE,IAAIoE,kBAAmB;gBACtC3I,iBAAiB/D,GAAG,CAACN,YAAY,MAAM4I;YACzC;QACF;QAEA,IAAImE,aAAa5N,MAAM,KAAK,GAAG;YAC7B,OAAO4I,QAAQkF,OAAO;QACxB;QAEA,MAAMC,eAAe,CAAC,gCAAgC,EAAE7B,IAAAA,sBAAS,EAAC;YAChExF,SAAS2F,KAAKH,SAAS,CACrB0B;YAEFI,qBAAqBtE;QACvB,GAAG,CAAC,CAAC;QAEL,MAAMuE,+BAA+B,IAAI,CAACxM,YAAY,GAClDhD,YAAYG,iBAAiB,GAC7BH,YAAYE,aAAa;QAE7B,KAAK,MAAM,GAAGkP,kBAAkB,IAAID,aAAc;YAChD,KAAK,MAAM,EAAEnE,EAAE,EAAE,IAAIoE,kBAAmB;gBACtC,IAAI,OAAOI,4BAA4B,CAACxE,GAAG,KAAK,aAAa;oBAC3DwE,4BAA4B,CAACxE,GAAG,GAAG;wBACjCyE,SAAS,CAAC;wBACV7K,OAAO,CAAC;oBACV;gBACF;gBACA4K,4BAA4B,CAACxE,GAAG,CAACyE,OAAO,CAACjH,WAAW,GAAG;oBACrDpD,UAAU;oBACVC,OAAO;gBACT;gBAEAmK,4BAA4B,CAACxE,GAAG,CAACpG,KAAK,CAAC4D,WAAW,GAAGyC,aACjDpG,yBAAc,CAAC6K,aAAa,GAC5B7K,yBAAc,CAACC,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAM6K,iBAAiB1C,QAAQ8B,WAAW,CAACC,gBAAgB,CAACM,cAAc;YACxE1N,MAAM4G;QACR;QAEA,OAAO,IAAI,CAAC0G,QAAQ,CAClBvL,aACA,6BAA6B;QAC7BJ,SAAS0B,OAAO,EAChB0K,gBACA;YACE/N,MAAMQ;YACNwC,OAAOqG,aACHpG,yBAAc,CAAC6K,aAAa,GAC5B7K,yBAAc,CAACC,qBAAqB;QAC1C;IAEJ;IAEAoK,SACEvL,WAAgC,EAChCsB,OAAe,EACfkC,UAA8B,EAC9BtE,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIsH,QAAQ,CAACkF,SAASO;YAC3B,IAAI,YAAYjM,YAAYJ,QAAQ,EAAE;gBACpCI,YAAYkM,UAAU,CAAC5K,SAASkC,YAAYtE,SAAS,CAACiN,KAAKC;oBACzD,IAAID,KAAK;wBACP,OAAOF,OAAOE;oBAChB;oBAEAnM,YAAY2B,WAAW,CACpBoH,cAAc,CAACqD,QACfC,mBAAmB,CAClB,IAAI,CAAChN,YAAY,GAAGI,gCAAoB,GAAGC,mCAAuB;oBAEtE,OAAOgM,QAAQU;gBACjB;YACF,OAAO;gBACL,MAAME,QAAQtM,YAAY5C,OAAO,CAACmP,GAAG,CAACrN,QAAQjB,IAAI;gBAClDqO,MAAME,mBAAmB,CAACxN,IAAI,CAACwE;gBAC/BxD,YAAYH,KAAK,CAAC0L,QAAQ,CAACkB,IAAI,CAACH,OAAcpN;gBAC9Cc,YAAY0M,aAAa,CACvB;oBACEpL;oBACAkC;oBACAmJ,aAAa;wBAAEC,aAAa1N,QAAQ+B,KAAK;oBAAC;gBAC5C,GACA,CAACkL,KAAUC;oBACT,IAAID,KAAK;wBACPnM,YAAYH,KAAK,CAACgN,WAAW,CAACJ,IAAI,CAACjJ,YAAYtE,SAASiN;wBACxD,OAAOF,OAAOE;oBAChB;oBAEAnM,YAAYH,KAAK,CAACiN,YAAY,CAACL,IAAI,CAACjJ,YAAYtE,SAASkN;oBAEzDpM,YAAY2B,WAAW,CACpBoH,cAAc,CAACqD,QACfC,mBAAmB,CAClB,IAAI,CAAChN,YAAY,GACbI,gCAAoB,GACpBC,mCAAuB;oBAG/B,OAAOgM,QAAQU;gBACjB;YAEJ;QACF;IACF;IAEA,MAAM3J,mBAAmBzC,WAAgC,EAAE;QACzD,MAAMzD,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDwF,IAAAA,uBAAe,EAAChC,aAAa,CAACM,KAAK2B,QAAQ8K,YAAY1M;YACrD,yEAAyE;YACzE,IACE0M,WAAW9O,IAAI,IACfqC,IAAImD,OAAO,IACXpD,SACA,kCAAkCqJ,IAAI,CAACpJ,IAAImD,OAAO,GAClD;gBACA,MAAM6D,aAAa,4BAA4BoC,IAAI,CAACpJ,IAAImD,OAAO;gBAE/D,MAAMuJ,UAAU,IAAI,CAAC3N,YAAY,GAC7BhD,YAAYK,uBAAuB,GACnCL,YAAYI,mBAAmB;gBAEnC,IAAI,CAACuQ,OAAO,CAACD,WAAW9O,IAAI,CAAC,EAAE;oBAC7B+O,OAAO,CAACD,WAAW9O,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACA+O,OAAO,CAACD,WAAW9O,IAAI,CAAC,CAACqJ,aAAa,WAAW,SAAS,GAAG;oBAC3D7F,UAAUpB;oBACVqB,OAAO1B,YAAY2B,WAAW,CAACC,OAAO,CAACtB;gBACzC;YACF;QACF;QAEA,IAAK,IAAI+G,MAAMhL,YAAYE,aAAa,CAAE;YACxC,MAAM6K,SAAS/K,YAAYE,aAAa,CAAC8K,GAAG;YAC5C,IAAK,IAAIpJ,QAAQmJ,OAAO0E,OAAO,CAAE;gBAC/B,MAAMzL,QACJhE,YAAYI,mBAAmB,CAACwB,KAAK,CACnCmJ,OAAOnG,KAAK,CAAChD,KAAK,KAAKiD,yBAAc,CAAC6K,aAAa,GAC/C,WACA,SACL;gBACH3E,OAAO0E,OAAO,CAAC7N,KAAK,GAAGoC;YACzB;YACA9D,aAAa,CAAC8K,GAAG,GAAGD;QACtB;QAEA,IAAK,IAAIC,MAAMhL,YAAYG,iBAAiB,CAAE;YAC5C,MAAM4K,SAAS/K,YAAYG,iBAAiB,CAAC6K,GAAG;YAChD,IAAK,IAAIpJ,QAAQmJ,OAAO0E,OAAO,CAAE;gBAC/B,MAAMzL,QACJhE,YAAYK,uBAAuB,CAACuB,KAAK,CACvCmJ,OAAOnG,KAAK,CAAChD,KAAK,KAAKiD,yBAAc,CAAC6K,aAAa,GAC/C,WACA,SACL;gBACH3E,OAAO0E,OAAO,CAAC7N,KAAK,GAAGoC;YACzB;YACA7D,iBAAiB,CAAC6K,GAAG,GAAGD;QAC1B;QAEA,MAAM6F,iBAAiB;YACrBC,MAAM3Q;YACN4Q,MAAM3Q;YACN+C,eAAe,IAAI,CAACA,aAAa;QACnC;QACA,MAAM6N,qBAAqB;YACzB,GAAGH,cAAc;YACjB1N,eAAe;QACjB;QAEA,MAAM8N,OAAOpD,KAAKH,SAAS,CAACmD,gBAAgB,MAAM,IAAI,CAAC9N,GAAG,GAAG,IAAImO;QACjE,MAAMC,WAAWtD,KAAKH,SAAS,CAC7BsD,oBACA,MACA,IAAI,CAACjO,GAAG,GAAG,IAAImO;QAGjBtN,YAAYwN,SAAS,CACnB,GAAG,IAAI,CAAClO,WAAW,GAAGmO,qCAAyB,CAAC,GAAG,CAAC,EACpD,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,2BAA2B,EAAE1D,KAAKH,SAAS,CAACyD,WAAW;QAG5DvN,YAAYwN,SAAS,CACnB,GAAG,IAAI,CAAClO,WAAW,GAAGmO,qCAAyB,CAAC,KAAK,CAAC,EACtD,IAAIC,gBAAO,CAACC,SAAS,CAACN;IAE1B;AACF;AAEA,SAAS3E,gBACPpI,GAAyB,EACzBsN,UAAkB,EAClB5J,sBAA8C,EAC9CyE,mBAA6B,EAC7BoF,kBAA2B;QAEHjG;IAAxB,MAAMkG,mBAAkBlG,0BAAAA,IAAAA,sCAAkB,EAACtH,KAAKyH,GAAG,qBAA3BH,wBAA6BkG,eAAe;IACpE,MAAMC,cAAcD,oBAAoB;IACxC,MAAME,oBAAoBC,IAAAA,sCAAoB,EAC5C3N,KACAyN,cAAc,aAAa;IAG7B,MAAMG,mBAAmBlK,sBAAsB,CAAC4J,WAAW;IAE3D,IAAInF,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACoF,sBAAsB;eAAIK;SAAiB,CAAC,EAAE,KAAK,KAAK;YAC3DlK,sBAAsB,CAAC4J,WAAW,GAAG,IAAIpP,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAM2P,yBAAyBH,sBAAsB;QACrD,IAAIG,wBAAwB;YAC1BnK,sBAAsB,CAAC4J,WAAW,GAAG,IAAIpP,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQwK,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM2F,qBAAqBL,eAAe9P,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAImQ,oBAAoB;oBACtBpK,sBAAsB,CAAC4J,WAAW,CAAC7O,GAAG,CAAC;gBACzC;gBAEAiF,sBAAsB,CAAC4J,WAAW,CAAC7O,GAAG,CAACd;YACzC;QACF;IACF;AACF;AAEA,SAAS4J,kBAAkBvH,GAAyB;QAC1BA,0BACPA,2BAebA;IAhBJ,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBvC,IAAI,KAAI;IACzD,MAAM2C,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;IACnD,mEAAmE;IACnE,yEAAyE;IACzE,0EAA0E;IAC1E,IAAIC,cAAsBL,UAAUG;IAEpC,6EAA6E;IAC7E,IAAIJ,IAAIrB,WAAW,CAAChB,IAAI,KAAK,iBAAiB;QAC5C2C,cAAcN,IAAI+N,UAAU;IAC9B;IAEA,yEAAyE;IACzE,yEAAyE;IACzE,0EAA0E;IAC1E,wEAAwE;IACxE,KAAI/N,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACC,sCAA0B,GAAG;QAC7DF,cAAcN,IAAIE,aAAa,GAAG,MAAMI;IAC1C;IAEA,IAAIN,IAAIU,QAAQ,KAAK,CAAC,CAAC,EAAE2C,mCAAwB,CAACC,aAAa,EAAE,EAAE;QACjE,OAAOG,yBAAyBzD,IAAIgO,UAAU,EAAEzK,QAAQ;IAC1D;IAEA,OAAOjD;AACT;AAEA,SAASmD,yBAAyBN,OAAe;IAC/C,sHAAsH;IACtH,MAAM9C,QAAQ8C,QAAQ9F,KAAK,CAAC,IAAI,CAAC,EAAE,CAACA,KAAK,CAAC,8BAA8B,CAAC,EAAE;IAE3E,OAAOK,IAAAA,kBAAK,EAAC2C;AACf"}