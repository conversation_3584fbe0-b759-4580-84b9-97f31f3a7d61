{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/hooks/use-measure-height.ts"], "sourcesContent": ["import { useEffect, useState } from 'react'\n\nexport function useMeasureHeight(\n  ref: React.RefObject<HTMLDivElement | null>\n): [number, boolean] {\n  const [pristine, setPristine] = useState<boolean>(true)\n  const [height, setHeight] = useState<number>(0)\n\n  useEffect(() => {\n    const el = ref.current\n\n    if (!el) {\n      return\n    }\n\n    const observer = new ResizeObserver(() => {\n      const { height: h } = el.getBoundingClientRect()\n      setHeight((prevHeight) => {\n        if (prevHeight !== 0) {\n          setPristine(false)\n        }\n        return h\n      })\n    })\n\n    observer.observe(el)\n    return () => {\n      observer.disconnect()\n      setPristine(true)\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [])\n\n  return [height, pristine]\n}\n"], "names": ["useMeasureHeight", "ref", "pristine", "setPristine", "useState", "height", "setHeight", "useEffect", "el", "current", "observer", "ResizeObserver", "h", "getBoundingClientRect", "prevHeight", "observe", "disconnect"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;uBAFoB;AAE7B,SAASA,iBACdC,GAA2C;IAE3C,MAAM,CAACC,UAAUC,YAAY,GAAGC,IAAAA,eAAQ,EAAU;IAClD,MAAM,CAACC,QAAQC,UAAU,GAAGF,IAAAA,eAAQ,EAAS;IAE7CG,IAAAA,gBAAS,EAAC;QACR,MAAMC,KAAKP,IAAIQ,OAAO;QAEtB,IAAI,CAACD,IAAI;YACP;QACF;QAEA,MAAME,WAAW,IAAIC,eAAe;YAClC,MAAM,EAAEN,QAAQO,CAAC,EAAE,GAAGJ,GAAGK,qBAAqB;YAC9CP,UAAU,CAACQ;gBACT,IAAIA,eAAe,GAAG;oBACpBX,YAAY;gBACd;gBACA,OAAOS;YACT;QACF;QAEAF,SAASK,OAAO,CAACP;QACjB,OAAO;Y<PERSON>LE,SAASM,UAAU;YACnBb,YAAY;QACd;IACA,uDAAuD;IACzD,GAAG,EAAE;IAEL,OAAO;QAACE;QAAQH;KAAS;AAC3B"}