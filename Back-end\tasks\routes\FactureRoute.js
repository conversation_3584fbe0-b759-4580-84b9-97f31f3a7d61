const express = require("express");
const router = express.Router();
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const Facture = require("../models/FactureModel");
const User = require("../models/UserModel");
const Client = require("../models/ClientModel");
const { updateStockFromInvoice } = require('../utils/stockUtils');
const PDFDocument = require('pdfkit');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Function to generate a unique invoice number based on company settings
const generateInvoiceNumber = async () => {
  try {
    // Récupérer les paramètres d'entreprise
    const Entreprise = require('../models/EntrepriseModel');
    const entreprise = await Entreprise.findOne();

    // Utiliser les paramètres d'entreprise ou des valeurs par défaut
    const prefix = entreprise?.prefixeFacture || "FACT-";
    const nextNum = entreprise?.prochainNumeroFacture || 1;
    const date = new Date();
    const year = date.getFullYear().toString();

    // Formater le numéro avec padding (4 chiffres)
    const formattedNum = String(nextNum).padStart(4, '0');
    const numero = `${prefix}${year}-${formattedNum}`;

    // Vérifier si ce numéro existe déjà
    const existingFacture = await Facture.findOne({ numero });
    if (existingFacture) {
      // Si le numéro existe déjà, incrémenter et réessayer
      if (entreprise) {
        entreprise.prochainNumeroFacture = nextNum + 1;
        await entreprise.save();
      }
      // Appel récursif pour générer un nouveau numéro
      return generateInvoiceNumber();
    }

    // Incrémenter le compteur pour la prochaine facture
    if (entreprise) {
      entreprise.prochainNumeroFacture = nextNum + 1;
      await entreprise.save();
      console.log(`Prochain numéro de facture mis à jour: ${entreprise.prochainNumeroFacture}`);
    }

    return numero;
  } catch (error) {
    console.error("Erreur lors de la génération du numéro de facture:", error);
    // Fallback en cas d'erreur
    const date = new Date();
    const year = date.getFullYear().toString();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const count = await Facture.countDocuments();
    return `FACT-${year}-${String(count + 1).padStart(4, '0')}`;
  }
};

// Create an invoice
router.post("/factures", verifyToken, async (req, res) => {
  try {
    console.log("Received POST /factures request with body:", req.body);
    const factureData = req.body;

    // Vérifier si l'utilisateur est un RESPONSABLE et définir le vendeurId correctement
    if (req.userRole === 'RESPONSABLE') {
      console.log('RESPONSABLE creating a facture, setting vendeurId to user ID:', req.userId);
      factureData.vendeurId = req.userId;
      factureData.responsableId = req.userId;
    }

    // Si l'utilisateur est un VENDEUR, assigner le responsableId
    if (req.userRole === 'VENDEUR') {
      const vendeurUser = await User.findById(req.userId);
      if (vendeurUser && vendeurUser.responsables && vendeurUser.responsables.length > 0) {
        factureData.responsableId = vendeurUser.responsables[0]; // Prendre le premier responsable
        console.log('VENDEUR creating a facture, setting responsableId to:', factureData.responsableId);
      }
    }

    if (!factureData.clientId) {
      console.log("Validation failed: clientId is required");
      return res.status(400).json({ error: "Client is required" });
    }

    // Normalize clientId (it might be an object with _id property)
    const mongoose = require('mongoose');
    let clientIdToUse;

    if (typeof factureData.clientId === 'object' && factureData.clientId._id) {
      clientIdToUse = factureData.clientId._id;
    } else if (typeof factureData.clientId === 'string') {
      clientIdToUse = factureData.clientId;
    } else {
      clientIdToUse = factureData.clientId;
    }

    console.log("Normalized clientId:", clientIdToUse);

    // Vérifier si le client existe
    const Client = require('../models/ClientModel');
    const client = await Client.findById(clientIdToUse);
    if (!client) {
      console.log(`Client with ID ${clientIdToUse} not found`);
      return res.status(404).json({ error: "Client not found" });
    }
    console.log("Client found:", client);

    if (!factureData.numero) {
      factureData.numero = await generateInvoiceNumber();
      console.log("Generated invoice number:", factureData.numero);
    }

    // Convert to ObjectId
    factureData.clientId = new mongoose.Types.ObjectId(clientIdToUse);
    console.log("Final clientId for invoice:", factureData.clientId);

    // Check if there are any enterprise users associated with this client
    const enterpriseUsers = await User.find({
      role: 'ENTREPRISE',
      $or: [
        { entrepriseId: factureData.clientId },
        { email: client.email }
      ]
    });

    console.log(`Found ${enterpriseUsers.length} enterprise users associated with this client`);

    // If no enterprise user is associated with this client, update any enterprise user with matching email
    if (enterpriseUsers.length === 0) {
      const userWithSameEmail = await User.findOne({
        role: 'ENTREPRISE',
        email: client.email
      });

      if (userWithSameEmail) {
        console.log(`Found enterprise user with matching email: ${userWithSameEmail.email}`);
        userWithSameEmail.entrepriseId = client._id;
        await userWithSameEmail.save();
        console.log(`Updated enterprise user with client ID: ${client._id}`);
      } else {
        console.log(`No enterprise user found with email: ${client.email}`);
      }
    }

    const newFacture = new Facture(factureData);
    await newFacture.save();

    console.log("Invoice created successfully:", newFacture);

    res.status(201).json({
      message: "Invoice added successfully",
      facture: newFacture,
    });
  } catch (error) {
    console.error("Error adding invoice:", error.message);
    res.status(500).json({
      error: "Error while adding invoice",
      details: error.message,
    });
  }
});

// Middleware pour filtrer les factures par entreprise ou responsable
const filterByEntreprise = async (req, res, next) => {
  try {
    if (req.userRole === 'RESPONSABLE') {
      // Récupérer l'ID du responsable associé à l'utilisateur
      const Client = require('../models/ClientModel');
      const mongoose = require('mongoose');

      const user = await User.findById(req.userId);
      console.log('User found:', user);

      // Find all vendeurs associated with this RESPONSABLE user
      const vendeurs = await User.find({
        role: 'VENDEUR',
        responsables: { $in: [user._id] }
      });
      console.log(`Found ${vendeurs.length} vendeurs associated with this RESPONSABLE user`);

      // Store vendeur IDs for later use
      const vendeurIds = vendeurs.map(v => v._id);
      req.vendeurIds = vendeurIds;

      if (user && user.entrepriseId) {
        // Vérifier si le client existe
        const client = await Client.findById(user.entrepriseId);
        if (client) {
          req.entrepriseId = user.entrepriseId;
          console.log('Using entrepriseId from user:', req.entrepriseId);
          console.log('Client found:', client);

          // DEBUG: Vérifier les factures pour ce client
          const factures = await Facture.find({
            clientId: new mongoose.Types.ObjectId(user.entrepriseId)
          });
          console.log(`Found ${factures.length} invoices for client ID ${user.entrepriseId}`);

          // Check for invoices created by vendeurs associated with this ENTREPRISE
          if (vendeurIds.length > 0) {
            const vendeurFactures = await Facture.find({
              vendeurId: { $in: vendeurIds }
            });
            console.log(`Found ${vendeurFactures.length} invoices created by vendeurs associated with this ENTREPRISE`);
          }

          // Si aucune facture n'est trouvée, vérifier toutes les factures
          if (factures.length === 0) {
            const allFactures = await Facture.find();
            console.log(`Total invoices in system: ${allFactures.length}`);

            if (allFactures.length > 0) {
              console.log('Sample invoice clientId:', allFactures[0].clientId);

              // Check if any invoices match this client by string comparison
              const matchingInvoices = allFactures.filter(inv =>
                inv.clientId && inv.clientId.toString() === user.entrepriseId.toString()
              );
              console.log(`Found ${matchingInvoices.length} matching invoices by string comparison`);

              if (matchingInvoices.length > 0) {
                console.log('First matching invoice:', matchingInvoices[0]);
              }
            }
          }
        } else {
          console.log(`Client with ID ${user.entrepriseId} not found`);

          // Try to find a client with the same email
          const clientByEmail = await Client.findOne({ email: user.email });
          if (clientByEmail) {
            req.entrepriseId = clientByEmail._id;
            console.log('Found client by email:', clientByEmail._id);

            // Update user with the correct client ID
            await User.findByIdAndUpdate(user._id, { entrepriseId: clientByEmail._id });
            console.log('Updated user with entrepriseId from email match:', clientByEmail._id);
          }
        }
      } else {
        // Si l'entrepriseId n'est pas défini, essayer de trouver le client par email
        const client = await Client.findOne({ email: user.email });
        if (client) {
          req.entrepriseId = client._id;
          console.log('Found client by email:', client._id);

          // Mettre à jour l'utilisateur avec l'ID du client
          await User.findByIdAndUpdate(user._id, { entrepriseId: client._id });
          console.log('Updated user with entrepriseId:', client._id);
        } else {
          console.log('No client found for user with email:', user.email);

          // Créer un nouveau client pour cet utilisateur
          const newClient = new Client({
            nom: user.nomEntreprise || user.nom,
            adresse: user.adresseEntreprise || user.adresse || 'Adresse non spécifiée',
            email: user.email,
            contact: user.contact || user.nom
          });

          await newClient.save();
          console.log('Created new client for user:', newClient);

          // Mettre à jour l'utilisateur avec l'ID du nouveau client
          await User.findByIdAndUpdate(user._id, { entrepriseId: newClient._id });
          req.entrepriseId = newClient._id;
          console.log('Updated user with new entrepriseId:', newClient._id);
        }
      }
    }
    next();
  } catch (error) {
    console.error('Erreur lors du filtrage par entreprise:', error);
    next();
  }
};

// Get all invoices
router.get("/factures", verifyToken, filterByEntreprise, async (req, res) => {
  try {
    console.log("Received GET /factures request");

    let query = {};

    // Si l'utilisateur est un responsable, filtrer par les factures qui lui sont associées
    if (req.userRole === 'RESPONSABLE') {
      console.log("Filtering invoices for responsable ID:", req.userId);

      const mongoose = require('mongoose');

      // Find all vendeurs associated with this responsable
      const vendeurs = await User.find({
        role: 'VENDEUR',
        responsables: { $in: [req.userId] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log('Found vendeurs for responsable:', vendeurIds);

      // Create a combined query that includes:
      // 1. Invoices created by this responsable
      // 2. Invoices created by vendeurs associated with this responsable
      let orConditions = [
        { vendeurId: new mongoose.Types.ObjectId(req.userId) }, // Invoices created by responsable
        { responsableId: new mongoose.Types.ObjectId(req.userId) } // Invoices assigned to responsable
      ];

      // Add condition for invoices created by vendeurs associated with this responsable
      if (vendeurIds.length > 0) {
        orConditions.push({ vendeurId: { $in: vendeurIds } });
      }

      if (orConditions.length > 0) {
        query = { $or: orConditions };
        console.log("Using combined query for RESPONSABLE user:", JSON.stringify(query));
      } else {
        console.log('No conditions found for responsable, returning empty result');
        return res.status(200).json([]);
      }
    }

    const factures = await Facture.find(query).populate("clientId");

    // Compter les factures par statut pour le débogage
    const statusCounts = {};
    factures.forEach(facture => {
      statusCounts[facture.statut] = (statusCounts[facture.statut] || 0) + 1;
    });

    console.log(`Returning ${factures.length} invoices to client with status counts:`, statusCounts);

    // Vérifier spécifiquement les factures avec le statut "SENT"
    const sentInvoices = factures.filter(f => f.statut === 'SENT');
    console.log(`Found ${sentInvoices.length} invoices with status SENT`);

    if (sentInvoices.length > 0) {
      console.log("Example SENT invoice:", {
        id: sentInvoices[0]._id,
        numero: sentInvoices[0].numero,
        clientId: sentInvoices[0].clientId?._id || sentInvoices[0].clientId,
        statut: sentInvoices[0].statut,
        total: sentInvoices[0].total
      });
    }

    res.status(200).json(factures);
  } catch (error) {
    console.error("Error fetching invoices:", error.message);
    res.status(500).json({ error: "Error while retrieving invoices" });
  }
});

// Get a single invoice by ID
router.get("/factures/:id", verifyToken, filterByEntreprise, async (req, res) => {
  try {
    console.log(`Received GET /factures/${req.params.id} request`);
    const facture = await Facture.findById(req.params.id).populate("clientId");

    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    // Accès autorisé pour tous les utilisateurs authentifiés
    // Restriction d'accès désactivée

    console.log("Invoice:", facture);
    res.status(200).json(facture);
  } catch (error) {
    console.error("Error fetching invoice:", error.message);
    res.status(500).json({ error: "Error while retrieving invoice" });
  }
});

// Update an invoice
router.put("/factures/:id", verifyToken, async (req, res) => {
  try {
    console.log(`Received PUT /factures/${req.params.id} request with body:`, req.body);

    // Accès autorisé pour tous les utilisateurs authentifiés
    // Restriction d'accès désactivée

    if (req.body.numero) {
      delete req.body.numero; // Prevent updating the invoice number
      console.log("Invoice number update not allowed - removed from update data");
    }

    // Récupérer l'ancienne facture pour vérifier le changement de statut
    const oldFacture = await Facture.findById(req.params.id);
    if (!oldFacture) return res.status(404).json({ error: "Invoice not found" });

    const oldStatus = oldFacture.statut;

    // Mettre à jour la facture
    const facture = await Facture.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    }).populate("clientId");

    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    console.log("Invoice updated:", facture);

    // Vérifier si le statut a changé
    if (oldStatus !== facture.statut) {
      console.log(`Statut de la facture changé: ${oldStatus} -> ${facture.statut}`);

      // Si la facture passe à PAID, mettre à jour le stock
      if (facture.statut === 'PAID' && !facture.stockAjuste) {
        const stockResult = await updateStockFromInvoice(facture._id);
        console.log('Résultat de la mise à jour du stock:', stockResult);
      }

      // Si la facture passe à CANCELED et que le stock a été ajusté, réaugmenter le stock
      if (facture.statut === 'CANCELED' && facture.stockAjuste) {
        const stockResult = await updateStockFromInvoice(facture._id, true); // true = annulation
        console.log('Résultat de la réaugmentation du stock:', stockResult);
      }
    }

    res.status(200).json(facture);
  } catch (error) {
    console.error("Error updating invoice:", error.message);
    res.status(500).json({ error: "Error while updating invoice" });
  }
});

// Delete an invoice
router.delete("/factures/:id", verifyToken, async (req, res) => {
  try {
    console.log(`Received DELETE /factures/${req.params.id} request`);

    // Accès autorisé pour tous les utilisateurs authentifiés
    // Restriction d'accès désactivée

    const facture = await Facture.findByIdAndDelete(req.params.id);
    if (!facture) return res.status(404).json({ error: "Invoice not found" });
    console.log("Invoice deleted:", facture);

    res.status(204).send();
  } catch (error) {
    console.error("Error deleting invoice:", error.message);
    res.status(500).json({ error: "Error while deleting invoice" });
  }
});

// Accept an invoice (client)
router.put("/factures/:id/accepter", verifyToken, async (req, res) => {
  try {
    console.log(`Received PUT /factures/${req.params.id}/accepter request`);

    // Vérifier que l'utilisateur est un client
    if (req.userRole !== 'CLIENT') {
      return res.status(403).json({
        error: "Seuls les clients peuvent accepter une facture"
      });
    }

    // Récupérer la facture
    const facture = await Facture.findById(req.params.id);
    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    // Vérifier que la facture est dans un état qui permet l'acceptation
    if (facture.statut === 'ACCEPTED') {
      return res.status(400).json({ error: "Cette facture a déjà été acceptée" });
    }

    if (facture.statut === 'REJECTED') {
      return res.status(400).json({ error: "Cette facture a déjà été refusée" });
    }

    if (facture.statut === 'PAID') {
      return res.status(400).json({ error: "Cette facture a déjà été payée" });
    }

    // Mettre à jour le statut de la facture
    facture.statut = "ACCEPTED";
    facture.dateAcceptation = new Date();
    facture.reponseClient = {
      date: new Date(),
      commentaires: req.body.commentaires || "Facture acceptée par le client"
    };
    await facture.save();

    // Notifier le vendeur et le responsable (à implémenter plus tard)
    // TODO: Ajouter la notification par email

    console.log("Invoice accepted:", facture);
    res.status(200).json(facture);
  } catch (error) {
    console.error("Error accepting invoice:", error.message);
    res.status(500).json({ error: "Error while accepting invoice" });
  }
});

// Reject an invoice (client)
router.put("/factures/:id/refuser", verifyToken, async (req, res) => {
  try {
    console.log(`Received PUT /factures/${req.params.id}/refuser request with body:`, req.body);

    // Vérifier que l'utilisateur est un client
    if (req.userRole !== 'CLIENT') {
      return res.status(403).json({
        error: "Seuls les clients peuvent refuser une facture"
      });
    }

    // Récupérer la facture
    const facture = await Facture.findById(req.params.id);
    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    // Vérifier que la facture est dans un état qui permet le refus
    if (facture.statut === 'ACCEPTED') {
      return res.status(400).json({ error: "Cette facture a déjà été acceptée" });
    }

    if (facture.statut === 'REJECTED') {
      return res.status(400).json({ error: "Cette facture a déjà été refusée" });
    }

    if (facture.statut === 'PAID') {
      return res.status(400).json({ error: "Cette facture a déjà été payée" });
    }

    // Vérifier que le motif de refus est fourni
    if (!req.body.motifRefus) {
      return res.status(400).json({ error: "Le motif de refus est obligatoire" });
    }

    // Mettre à jour le statut de la facture
    facture.statut = "REJECTED";
    facture.dateRefus = new Date();
    facture.reponseClient = {
      date: new Date(),
      commentaires: req.body.motifRefus,
      motifRefus: req.body.motifRefus
    };
    await facture.save();

    // Notifier le vendeur et le responsable (à implémenter plus tard)
    // TODO: Ajouter la notification par email

    console.log("Invoice rejected:", facture);
    res.status(200).json(facture);
  } catch (error) {
    console.error("Error rejecting invoice:", error.message);
    res.status(500).json({ error: "Error while rejecting invoice" });
  }
});

// Generate PDF for an invoice
router.get("/factures/:id/pdf", verifyToken, async (req, res) => {
  try {
    console.log(`Received GET /factures/${req.params.id}/pdf request`);
    const facture = await Facture.findById(req.params.id).populate("clientId");

    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    // Generate PDF directly on the server
    const pdfBuffer = await generateInvoicePDF(facture);

    // Set the response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="facture-${facture.numero}.pdf"`);

    // Send the PDF buffer directly to the client
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error generating PDF for invoice:", error.message);
    res.status(500).json({ error: "Error while generating PDF for invoice" });
  }
});

// Generate PDF for an invoice
const generateInvoicePDF = async (facture) => {
  return new Promise(async (resolve, reject) => {
    try {
      // Get template settings and parameters
      const ResponsableTemplate = require('../models/ResponsableTemplateModel');
      const BaseTemplate = require('../models/BaseTemplateModel');
      const Parametres = require('../models/ParametresModel');

      let responsableTemplate = null;
      let baseTemplate = null;
      let params = await Parametres.findOne();

      // Try to get responsable template settings based on the invoice context
      let entrepriseId = null;

      // First, try to get enterprise ID from responsableId (if invoice was created by responsable)
      if (facture.responsableId) {
        entrepriseId = facture.responsableId;
        console.log('Using responsableId as entrepriseId:', entrepriseId);
      }
      // If not, try to get enterprise ID from vendeurId (get the vendeur's enterprise)
      else if (facture.vendeurId) {
        const vendeur = await User.findById(facture.vendeurId);
        if (vendeur && vendeur.entrepriseId) {
          entrepriseId = vendeur.entrepriseId;
          console.log('Using vendeur entrepriseId:', entrepriseId);
        }
      }
      // Fallback: try to get from client's enterprise (old logic)
      else if (facture.clientId && facture.clientId.entrepriseId) {
        entrepriseId = facture.clientId.entrepriseId;
        console.log('Using client entrepriseId (fallback):', entrepriseId);
      }

      if (entrepriseId) {
        responsableTemplate = await ResponsableTemplate.findOne({
          entrepriseId: entrepriseId
        }).populate('factureTemplate.baseTemplateId');

        if (responsableTemplate && responsableTemplate.factureTemplate.baseTemplateId) {
          baseTemplate = responsableTemplate.factureTemplate.baseTemplateId;
          console.log('Found responsable template for enterprise:', entrepriseId);
        }
      } else {
        console.log('No enterprise ID found for template lookup');
      }

      // Default colors and settings from template
      // Priority: ResponsableTemplate > TemplateSettings > Default
      let primaryColor = '#f57c00'; // Default BenYounes Web orange color
      let logoPath = null;

      if (responsableTemplate && responsableTemplate.factureTemplate) {
        // Use responsable customized colors and logo
        primaryColor = responsableTemplate.factureTemplate.color || primaryColor;
        logoPath = responsableTemplate.factureTemplate.logo;
      }

      // Fallback to global parameters
      if (!logoPath && params) {
        logoPath = params.logo;
      }

      // Nettoyer la couleur si elle est entourée de guillemets
      if (primaryColor && typeof primaryColor === 'string') {
        primaryColor = primaryColor.replace(/^["'](.+)["']$/, '$1');
      }

      // Validate the color format
      const isValidHex = /^#?([a-f\d]{3}|[a-f\d]{6})$/i.test(primaryColor);
      if (!isValidHex) {
        console.warn(`Invalid hex color: ${primaryColor}, falling back to default #f57c00`);
        primaryColor = '#f57c00'; // Fallback to BenYounes Web orange color
      }

      // Ensure the hex color has a # prefix
      if (!primaryColor.startsWith('#')) {
        primaryColor = '#' + primaryColor;
      }

      // Handle 3-digit hex colors
      if (primaryColor.length === 4) {
        primaryColor = '#' + primaryColor[1] + primaryColor[1] + primaryColor[2] + primaryColor[2] + primaryColor[3] + primaryColor[3];
      }

      console.log('Using primary color for invoice (validated):', primaryColor);

      // Determine template layout (standard vs moderne)
      let templateLayout = 'standard'; // Default layout
      if (baseTemplate && baseTemplate.layout) {
        templateLayout = baseTemplate.layout;
      }
      console.log('Using template layout:', templateLayout);

      const defaultCurrency = params?.defaultCurrency || 'EUR';

      console.log('Using template settings for invoice:', {
        type: 'facture',
        color: primaryColor,
        currency: defaultCurrency,
        logo: logoPath || 'No logo',
        layout: templateLayout || 'standard'
      });

      // Format currency - always use DT (Tunisian Dinar)
      const formatCurrency = (amount) => {
        if (!amount && amount !== 0) return '0';

        // Format avec des points comme séparateurs de milliers et virgule pour les décimales
        const parts = amount.toFixed(2).split('.');
        const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        const decimalPart = parts[1];

        // Ajouter manuellement le symbole DT après le nombre
        return `${integerPart},${decimalPart} DT`;
      };

      // Create a PDF document with a single page and compressed content
      const doc = new PDFDocument({
        margin: 30, // Reduced margins to fit more content
        autoFirstPage: true,
        size: 'A4',
        layout: 'portrait',
        compress: true, // Enable compression
        bufferPages: true // Enable buffer pages to prevent automatic page breaks
      });

      // Create a buffer to store the PDF
      const buffers = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Calculate totals correctly
      let totalHT = 0;
      if (facture.lignes && facture.lignes.length > 0) {
        facture.lignes.forEach(ligne => {
          const montantHT = (ligne.montantHT || ligne.prixUnitaire * ligne.quantite);
          totalHT += montantHT;
        });
      }

      const tauxTVA = facture.tauxTVA || 0;
      const montantTVA = totalHT * (tauxTVA / 100);
      const totalTTC = totalHT + montantTVA;

      // White background for the whole document
      doc.rect(0, 0, doc.page.width, doc.page.height).fill('#FFFFFF');

      // No colored header background - using orange text instead
      console.log('Using orange text for header instead of background color');

      // Apply different layouts based on template
      if (templateLayout === 'moderne') {
        // MODERNE LAYOUT: Logo on left, Invoice reference on right
        console.log('Applying MODERNE layout');

        // Try to add logo on the LEFT side
        try {
          console.log('Logo path from database:', logoPath);

          if (logoPath) {
            // Nettoyer le chemin du logo si nécessaire
            const cleanLogoPath = logoPath.replace(/^["'](.+)["']$/, '$1');
            console.log('Cleaned logo path:', cleanLogoPath);

            // Construire différentes variantes de chemins pour trouver le logo
            const possiblePaths = [];

            // Chemin absolu si le chemin commence par /uploads/
            if (cleanLogoPath.startsWith('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Chemin relatif si le chemin commence par uploads/ sans le slash initial
            if (cleanLogoPath.startsWith('uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Essayer avec le nom de fichier uniquement
            const filename = path.basename(cleanLogoPath);
            possiblePaths.push(path.join(__dirname, '..', 'uploads', filename));

            // Essayer avec une recherche partielle basée sur l'ID du fichier
            if (filename.includes('-')) {
              const parts = filename.split('-');
              if (parts.length >= 2) {
                const uniqueId = parts[parts.length - 1];
                try {
                  const uploadFiles = fs.readdirSync(path.join(__dirname, '..', 'uploads'));
                  const matchingFile = uploadFiles.find(file => file.includes(uniqueId));
                  if (matchingFile) {
                    console.log('Found matching file by unique ID:', matchingFile);
                    possiblePaths.push(path.join(__dirname, '..', 'uploads', matchingFile));
                  }
                } catch (err) {
                  console.warn('Error listing upload directory:', err.message);
                }
              }
            }

            // Essayer dans le dossier uploads/logos
            possiblePaths.push(path.join(__dirname, '..', 'uploads', 'logos', filename));

            // Essayer sans le préfixe /uploads/
            if (cleanLogoPath.includes('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath.replace('/uploads/', '')));
            }

            // Essayer le chemin direct
            possiblePaths.push(cleanLogoPath);

            // Essayer avec le chemin complet
            possiblePaths.push(path.join(__dirname, '..', 'uploads', cleanLogoPath));

            console.log('Trying these paths for logo:', possiblePaths);

            // Essayer chaque chemin possible
            let logoAdded = false;
            for (const tryPath of possiblePaths) {
              console.log('Checking if logo exists at:', tryPath);
              if (fs.existsSync(tryPath)) {
                console.log('Logo found at:', tryPath);
                // Position the logo on the LEFT side for moderne layout
                doc.image(tryPath, 50, 15, { width: 70, height: 70 });
                console.log('Logo added to PDF successfully on LEFT side (moderne layout)');
                logoAdded = true;
                break;
              }
            }

            if (!logoAdded) {
              console.warn('Logo file not found at any path. Tried:', possiblePaths.join(', '));
            }
          } else {
            console.log('No logo specified for this template');
          }
        } catch (logoError) {
          console.warn('Could not add logo to PDF:', logoError.message);
          console.error('Logo error stack:', logoError.stack);
        }

        // Add invoice title and date on the RIGHT side for moderne layout
        doc.fillColor(primaryColor)
           .fontSize(24)  // Larger font for moderne style
           .font('Helvetica-Bold')
           .text(`Facture`, 400, 30, { align: 'right' });

        doc.fillColor('#000000')
           .fontSize(14)
           .font('Helvetica')
           .text(`${facture.numero}`, 400, 55, { align: 'right' });

        doc.fontSize(12)
           .text(`Date d'émission: ${new Date(facture.dateEmission).toLocaleDateString()}`, 400, 75, { align: 'right' });

      } else {
        // STANDARD LAYOUT: Logo on right, Invoice reference on left (current layout)
        console.log('Applying STANDARD layout');

        // Try to add logo on the RIGHT side (current behavior)
        try {
          console.log('Logo path from database:', logoPath);

          if (logoPath) {
            // Nettoyer le chemin du logo si nécessaire
            const cleanLogoPath = logoPath.replace(/^["'](.+)["']$/, '$1');
            console.log('Cleaned logo path:', cleanLogoPath);

            // Construire différentes variantes de chemins pour trouver le logo
            const possiblePaths = [];

            // Chemin absolu si le chemin commence par /uploads/
            if (cleanLogoPath.startsWith('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Chemin relatif si le chemin commence par uploads/ sans le slash initial
            if (cleanLogoPath.startsWith('uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Essayer avec le nom de fichier uniquement
            const filename = path.basename(cleanLogoPath);
            possiblePaths.push(path.join(__dirname, '..', 'uploads', filename));

            // Essayer avec une recherche partielle basée sur l'ID du fichier
            if (filename.includes('-')) {
              const parts = filename.split('-');
              if (parts.length >= 2) {
                const uniqueId = parts[parts.length - 1];
                try {
                  const uploadFiles = fs.readdirSync(path.join(__dirname, '..', 'uploads'));
                  const matchingFile = uploadFiles.find(file => file.includes(uniqueId));
                  if (matchingFile) {
                    console.log('Found matching file by unique ID:', matchingFile);
                    possiblePaths.push(path.join(__dirname, '..', 'uploads', matchingFile));
                  }
                } catch (err) {
                  console.warn('Error listing upload directory:', err.message);
                }
              }
            }

            // Essayer dans le dossier uploads/logos
            possiblePaths.push(path.join(__dirname, '..', 'uploads', 'logos', filename));

            // Essayer sans le préfixe /uploads/
            if (cleanLogoPath.includes('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath.replace('/uploads/', '')));
            }

            // Essayer le chemin direct
            possiblePaths.push(cleanLogoPath);

            // Essayer avec le chemin complet
            possiblePaths.push(path.join(__dirname, '..', 'uploads', cleanLogoPath));

            console.log('Trying these paths for logo:', possiblePaths);

            // Essayer chaque chemin possible
            let logoAdded = false;
            for (const tryPath of possiblePaths) {
              console.log('Checking if logo exists at:', tryPath);
              if (fs.existsSync(tryPath)) {
                console.log('Logo found at:', tryPath);
                // Position the logo on the RIGHT side for standard layout
                doc.image(tryPath, 480, 15, { width: 70, height: 70 });
                console.log('Logo added to PDF successfully on RIGHT side (standard layout)');
                logoAdded = true;
                break;
              }
            }

            if (!logoAdded) {
              console.warn('Logo file not found at any path. Tried:', possiblePaths.join(', '));
            }
          } else {
            console.log('No logo specified for this template');
          }
        } catch (logoError) {
          console.warn('Could not add logo to PDF:', logoError.message);
          console.error('Logo error stack:', logoError.stack);
        }

        // Add invoice title and date on the LEFT side for standard layout
        doc.fillColor(primaryColor)
           .fontSize(16)  // Standard font size
           .font('Helvetica-Bold')
           .text(`Facture ${facture.numero}`, 50, 50);

        doc.fontSize(10)
           .font('Helvetica')
           .text(`Date d'émission: ${new Date(facture.dateEmission).toLocaleDateString()}`, 50, 75);
      }

      // Reset text color
      doc.fillColor('#000000');

      // Émetteur and Destinataire sections side by side - more compact with smaller fonts
      const leftColumnX = 40;
      const rightColumnX = 300;
      const sectionY = 100;
      const lineHeight = 14; // Reduced line height to fit more content

      // Émetteur section
      doc.fillColor(primaryColor)
         .fontSize(12)  // Reduced from 14
         .font('Helvetica-Bold')
         .text('Émetteur', leftColumnX, sectionY);

      doc.fillColor('#000000')
         .fontSize(9)  // Reduced from 12
         .font('Helvetica-Bold')
         .text('Société :', leftColumnX, sectionY + lineHeight);

      doc.font('Helvetica')
         .text('Wonderland SARL', leftColumnX + 80, sectionY + lineHeight);  // Reduced spacing

      doc.font('Helvetica-Bold')
         .text('Votre contact :', leftColumnX, sectionY + lineHeight*2);

      doc.font('Helvetica')
         .text('WAJDI HB', leftColumnX + 80, sectionY + lineHeight*2);  // Reduced spacing

      doc.font('Helvetica-Bold')
         .text('Adresse :', leftColumnX, sectionY + lineHeight*3);

      doc.font('Helvetica')
         .text('12 rue du Lapin Blanc', leftColumnX + 80, sectionY + lineHeight*3)  // Reduced spacing
         .text('12345 Westerham', leftColumnX + 80, sectionY + lineHeight*4);  // Reduced spacing

      // Destinataire section
      doc.fillColor(primaryColor)
         .fontSize(12)  // Reduced from 14
         .font('Helvetica-Bold')
         .text('Destinataire', rightColumnX, sectionY);

      if (facture.clientId) {
        doc.fillColor('#000000')
           .fontSize(9)  // Reduced from 12
           .font('Helvetica-Bold')
           .text('Nom :', rightColumnX, sectionY + lineHeight);

        doc.font('Helvetica')
           .text(facture.clientId.nom || 'N/A', rightColumnX + 60, sectionY + lineHeight);  // Reduced spacing

        doc.font('Helvetica-Bold')
           .text('Adresse :', rightColumnX, sectionY + lineHeight*2);

        doc.font('Helvetica')
           .text(facture.clientId.adresse || 'N/A', rightColumnX + 60, sectionY + lineHeight*2);  // Reduced spacing

        doc.font('Helvetica-Bold')
           .text('Email :', rightColumnX, sectionY + lineHeight*3);

        doc.font('Helvetica')
           .text(facture.clientId.email || 'N/A', rightColumnX + 60, sectionY + lineHeight*3);  // Reduced spacing
      }

      // Détail section title
      const tableTop = 240;  // Moved up to save space

      doc.fillColor(primaryColor)
         .fontSize(12)  // Reduced from 14
         .font('Helvetica-Bold')
         .text('Détail', 50, tableTop - 25);  // Adjusted position

      // Apply different table styles based on template layout
      if (templateLayout === 'moderne') {
        // MODERNE TABLE STYLE: Professional elegant design with subtle shadows and clean lines
        console.log('Applying MODERNE table style');

        // Define column widths and positions for better alignment
        const colType = { x: 60, width: 80 };
        const colDesc = { x: 140, width: 160 };
        const colPrice = { x: 300, width: 80 };
        const colQty = { x: 380, width: 50 };
        const colTVA = { x: 430, width: 40 };
        const colTotal = { x: 470, width: 70 };

        // Create a subtle shadow effect by drawing a slightly offset darker rectangle
        doc.fillColor('#e0e0e0').rect(52, tableTop + 2, 500, 22).fill();

        // Main header background with gradient-like effect
        doc.fillColor('#f8f9fa').rect(50, tableTop, 500, 22).fill();

        // Draw main table border with rounded corners effect
        doc.strokeColor('#d1d5db').lineWidth(1.5);
        doc.rect(50, tableTop, 500, 22).stroke();

        // Draw elegant vertical separators with subtle styling
        doc.strokeColor('#e5e7eb').lineWidth(1);
        doc.moveTo(colDesc.x - 10, tableTop).lineTo(colDesc.x - 10, tableTop + 22).stroke();
        doc.moveTo(colPrice.x - 10, tableTop).lineTo(colPrice.x - 10, tableTop + 22).stroke();
        doc.moveTo(colQty.x - 10, tableTop).lineTo(colQty.x - 10, tableTop + 22).stroke();
        doc.moveTo(colTVA.x - 10, tableTop).lineTo(colTVA.x - 10, tableTop + 22).stroke();
        doc.moveTo(colTotal.x - 10, tableTop).lineTo(colTotal.x - 10, tableTop + 22).stroke();

        // Add a subtle bottom border for the header
        doc.strokeColor(primaryColor).lineWidth(2);
        doc.moveTo(50, tableTop + 22).lineTo(550, tableTop + 22).stroke();

        // Table headers with professional typography
        doc.fillColor('#374151').fontSize(9).font('Helvetica-Bold');
        doc.text('TYPE', colType.x, tableTop + 8, { width: colType.width, align: 'left' });
        doc.text('DESCRIPTION', colDesc.x, tableTop + 8, { width: colDesc.width, align: 'left' });
        doc.text('PRIX UNIT.', colPrice.x, tableTop + 8, { width: colPrice.width, align: 'right' });
        doc.text('QTÉ', colQty.x, tableTop + 8, { width: colQty.width, align: 'center' });
        doc.text('TVA', colTVA.x, tableTop + 8, { width: colTVA.width, align: 'center' });
        doc.text('TOTAL HT', colTotal.x, tableTop + 8, { width: colTotal.width, align: 'right' });

      } else {
        // STANDARD TABLE STYLE: Background color header (current style)
        console.log('Applying STANDARD table style');
        console.log('Adding table header with background color:', primaryColor);
        doc.rect(50, tableTop, 500, 20).fill(primaryColor);  // Reduced height from 25 to 20
        console.log('Table header background color applied successfully');

        // Define column widths and positions for better alignment
        const colType = { x: 60, width: 80 };
        const colDesc = { x: 140, width: 160 };
        const colPrice = { x: 300, width: 80 };
        const colQty = { x: 380, width: 50 };
        const colTVA = { x: 430, width: 40 };
        const colTotal = { x: 470, width: 70 };

        // Table headers with white text on colored background
        doc.fillColor('#FFFFFF').fontSize(9);  // Reduced from 12
        doc.text('Type', colType.x, tableTop + 6, { width: colType.width, align: 'left' });  // Adjusted position
        doc.text('Description', colDesc.x, tableTop + 6, { width: colDesc.width, align: 'left' });  // Adjusted position
        doc.text('Prix unitaire', colPrice.x, tableTop + 6, { width: colPrice.width, align: 'right' });  // Adjusted position
        doc.text('Quantité', colQty.x, tableTop + 6, { width: colQty.width, align: 'center' });  // Adjusted position
        doc.text('TVA', colTVA.x, tableTop + 6, { width: colTVA.width, align: 'center' });  // Adjusted position
        doc.text('Total HT', colTotal.x, tableTop + 6, { width: colTotal.width, align: 'right' });  // Adjusted position
      }

      // Define column widths and positions for table rows (same for both layouts)
      const colType = { x: 60, width: 80 };
      const colDesc = { x: 140, width: 160 };
      const colPrice = { x: 300, width: 80 };
      const colQty = { x: 380, width: 50 };
      const colTVA = { x: 430, width: 40 };
      const colTotal = { x: 470, width: 70 };

      // Reset text color
      doc.fillColor('#000000');

      // Table rows - more compact with smaller fonts
      let y = templateLayout === 'moderne' ? tableTop + 22 : tableTop + 20;  // Adjusted starting position for moderne
      const rowHeight = templateLayout === 'moderne' ? 18 : 16;   // Slightly taller rows for moderne

      if (facture.lignes && facture.lignes.length > 0) {
        facture.lignes.forEach((ligne, i) => {
          if (templateLayout === 'moderne') {
            // MODERNE LAYOUT: Professional row styling with subtle alternating backgrounds
            if (i % 2 === 0) {
              // Even rows: very light background
              doc.fillColor('#fafbfc').rect(50, y - 2, 500, rowHeight).fill();
            } else {
              // Odd rows: white background
              doc.fillColor('#ffffff').rect(50, y - 2, 500, rowHeight).fill();
            }

            // Draw subtle row borders
            doc.strokeColor('#f1f3f4').lineWidth(0.5);
            doc.rect(50, y - 2, 500, rowHeight).stroke();

            // Draw elegant vertical separators
            doc.strokeColor('#e8eaed').lineWidth(0.5);
            doc.moveTo(colDesc.x - 10, y - 2).lineTo(colDesc.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colPrice.x - 10, y - 2).lineTo(colPrice.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colQty.x - 10, y - 2).lineTo(colQty.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colTVA.x - 10, y - 2).lineTo(colTVA.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colTotal.x - 10, y - 2).lineTo(colTotal.x - 10, y - 2 + rowHeight).stroke();
          } else {
            // STANDARD LAYOUT: Alternate row background for better readability
            if (i % 2 === 0) {
              doc.rect(50, y - 3, 500, rowHeight).fill('#f5f5f5');  // Adjusted position and height
              doc.fillColor('#000000');
            }
          }

          const montantHT = (ligne.montantHT || ligne.prixUnitaire * ligne.quantite);

          // Determine type (Service or Produit)
          const type = ligne.type || 'Service';

          if (templateLayout === 'moderne') {
            // MODERNE LAYOUT: Professional typography with better spacing
            doc.fillColor('#374151').fontSize(8).font('Helvetica');

            // Add content with proper vertical centering
            const textY = y + 6; // Center text vertically in the row
            doc.text(type, colType.x, textY, { width: colType.width, align: 'left' });
            doc.text(ligne.description || 'N/A', colDesc.x, textY, { width: colDesc.width, align: 'left' });

            // Numbers in a slightly different color for better readability
            doc.fillColor('#1f2937');
            doc.text(formatCurrency(ligne.prixUnitaire || 0), colPrice.x, textY, { width: colPrice.width, align: 'right' });
            doc.text(ligne.quantite?.toString() || '0', colQty.x, textY, { width: colQty.width, align: 'center' });
            doc.text(`${tauxTVA}%`, colTVA.x, textY, { width: colTVA.width, align: 'center' });

            // Total in primary color for emphasis
            doc.fillColor(primaryColor).font('Helvetica-Bold');
            doc.text(formatCurrency(montantHT), colTotal.x, textY, { width: colTotal.width, align: 'right' });
          } else {
            // STANDARD LAYOUT: Use smaller font for table content
            doc.fontSize(8);  // Reduced font size for table content

            // Ensure text doesn't overflow by using width constraints
            doc.text(type, colType.x, y, { width: colType.width, align: 'left' });
            doc.text(ligne.description || 'N/A', colDesc.x, y, { width: colDesc.width, align: 'left' });
            doc.text(formatCurrency(ligne.prixUnitaire || 0), colPrice.x, y, { width: colPrice.width, align: 'right' });
            doc.text(ligne.quantite?.toString() || '0', colQty.x, y, { width: colQty.width, align: 'center' });
            doc.text(`${tauxTVA}%`, colTVA.x, y, { width: colTVA.width, align: 'center' });
            doc.text(formatCurrency(montantHT), colTotal.x, y, { width: colTotal.width, align: 'right' });
          }

          y += rowHeight;
        });
      } else {
        doc.fontSize(8);  // Reduced font size
        doc.text('Aucune ligne de facturation', 60, y);
        y += rowHeight;
      }

      // Add a line
      y += 10;
      doc.moveTo(50, y).lineTo(550, y).stroke();
      y += 20;

      // Totals section - with right-aligned labels next to the values
      // Create a background for the totals section
      doc.rect(50, y, 500, 60).fill('#f9f9f9');  // Reduced height from 70 to 60
      doc.fillColor('#000000');

      // Define columns for labels and values - labels now positioned near values
      const valueX = 500;
      const labelWidth = 100;  // Width for the label

      // Total HT - label right-aligned before the value
      doc.fontSize(9);  // Reduced font size
      doc.font('Helvetica');  // Regular font
      doc.text('Total HT:', valueX - labelWidth - 10, y + 10, { width: labelWidth, align: 'right' });  // Right-aligned label
      doc.text(formatCurrency(totalHT), valueX - 10, y + 10, { align: 'right' });  // Right-aligned value

      // TVA - label right-aligned before the value
      doc.font('Helvetica');  // Regular font
      doc.text(`TVA (${tauxTVA}%):`, valueX - labelWidth - 10, y + 25, { width: labelWidth, align: 'right' });  // Right-aligned label
      doc.text(formatCurrency(montantTVA), valueX - 10, y + 25, { align: 'right' });  // Right-aligned value

      // Total TTC with primary color - keeping this one bold but with smaller font
      doc.fontSize(10);  // Slightly larger than other totals but still smaller than before
      doc.font('Helvetica-Bold');
      doc.fillColor(primaryColor);
      doc.text('Total TTC:', valueX - labelWidth - 10, y + 45, { width: labelWidth, align: 'right' });  // Right-aligned label
      doc.text(formatCurrency(totalTTC), valueX - 10, y + 45, { align: 'right' });  // Right-aligned value

      // Reset styles
      doc.fillColor('#000000');
      doc.font('Helvetica');

      // Update y position for next section
      y += 80;

      // Add Conditions section - more compact with smaller fonts
      doc.fillColor(primaryColor)
         .fontSize(11)  // Reduced from 14
         .font('Helvetica-Bold')
         .text('Conditions', 50, y);

      // Create a background for the conditions section
      const conditionsY = y + 15;  // Reduced from 20
      doc.rect(50, conditionsY, 500, 40).fill('#f9f9f9');  // Reduced height from 50 to 40

      // Reset text color
      doc.fillColor('#000000');

      // Conditions de règlement
      doc.fontSize(8)  // Reduced from 10
         .font('Helvetica-Bold')
         .text('Conditions de règlement :', 65, conditionsY + 8);  // Adjusted position

      doc.font('Helvetica')
         .text('45 jours fin de mois', 220, conditionsY + 8);  // Reduced spacing and adjusted position

      // Mode de règlement
      doc.font('Helvetica-Bold')
         .text('Mode de règlement :', 65, conditionsY + 24);  // Adjusted position

      doc.font('Helvetica')
         .text('Virement bancaire', 220, conditionsY + 24);  // Reduced spacing and adjusted position

      // Update y position
      y = conditionsY + 50;  // Reduced from 60

      // Notes section with smaller font
      if (facture.notes) {
        doc.fontSize(8)  // Reduced font size
           .font('Helvetica-Bold')
           .text('Notes:', 50, y, { underline: true });

        doc.font('Helvetica')
           .text(facture.notes, 50, y + 15);  // Reduced spacing

        y += 40; // Reduced space after notes from 60
      }

      // Add status indicator for all invoice statuses below the Conditions section
      if (facture.statut) {
        console.log('Adding status indicator below Conditions section for status:', facture.statut);

        // Ensure the color is valid and properly formatted
        let validColor = primaryColor;
        let statusText = '';

        // Determine color and text based on status
        switch(facture.statut) {
          case 'PAID':
            statusText = 'PAYÉE';
            validColor = '#e91e63'; // Rose/pink color as in the image
            break;
          case 'SENT':
            statusText = 'ENVOYÉE';
            validColor = '#ff9800'; // Orange/warning color
            break;
          case 'DRAFT':
            statusText = 'BROUILLON';
            validColor = '#2196f3'; // Blue/info color
            break;
          case 'CANCELED':
            statusText = 'ANNULÉE';
            validColor = '#f44336'; // Red/error color
            break;
          default:
            statusText = facture.statut;
            validColor = primaryColor;
        }

        // Validate the color format
        const isValidHex = /^#?([a-f\d]{3}|[a-f\d]{6})$/i.test(validColor);
        if (!isValidHex) {
          console.warn(`Invalid hex color for status: ${validColor}, falling back to default #f57c00`);
          validColor = '#f57c00'; // Fallback to BenYounes Web orange color
        }

        // Ensure the hex color has a # prefix
        if (!validColor.startsWith('#')) {
          validColor = '#' + validColor;
        }

        // Handle 3-digit hex colors
        if (validColor.length === 4) {
          validColor = '#' + validColor[1] + validColor[1] + validColor[2] + validColor[2] + validColor[3] + validColor[3];
        }

        console.log(`Using validated color for ${statusText} status:`, validColor);

        // Add status below the Conditions section - more compact with smaller size
        doc.rect(50, y, 60, 20).fill(validColor);  // Reduced size from 80x25 to 60x20
        doc.fillColor('#FFFFFF');
        doc.fontSize(9);  // Reduced from 12
        doc.text(statusText, 50, y + 5, { width: 60, align: 'center' });  // Adjusted position and translated to French
        doc.fillColor('#000000');

        console.log(`Added ${statusText} status with color:`, validColor);

        // Update y position
        y += 30;  // Reduced from 40
      }

      // Add footer as part of the main content instead of at the bottom of the page
      // This prevents the creation of a second page
      y += 20; // Add some space before the footer
      doc.fontSize(8) // Smaller font size
         .text(`Facture ${facture.numero}`, 50, y);

      // Disable automatic page breaks to ensure everything stays on one page
      console.log('Ensuring content fits on a single page');

      // Finalize the PDF
      doc.end();
    } catch (error) {
      console.error('Error generating PDF:', error);
      reject(error);
    }
  });
};

// Send invoice by email
router.post("/factures/:id/email", verifyToken, async (req, res) => {
  try {
    console.log(`Received POST /factures/${req.params.id}/email request`);
    const facture = await Facture.findById(req.params.id).populate("clientId");

    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    // Get email data from request body
    let { to, subject, message } = req.body;

    if (!to) {
      // If no recipient is specified, use the client's email
      if (facture.clientId && facture.clientId.email) {
        to = facture.clientId.email;
      } else {
        return res.status(400).json({ error: "Recipient email is required" });
      }
    }

    // Handle multiple recipients
    let recipients = to;
    if (typeof to === 'string' && to.includes(',')) {
      recipients = to.split(',').map(email => email.trim()).filter(email => email);
      console.log("Multiple recipients detected:", recipients);
    }

    // Generate PDF
    const pdfBuffer = await generateInvoicePDF(facture);

    // Create a nodemailer transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Get company information from parameters if available
    let companyName = 'Mon Entreprise';
    let emailSignature = 'Cordialement,\nL\'équipe de Mon Entreprise';
    let defaultCurrency = 'EUR';
    let primaryColor = '#f57c00'; // BenYounes Web orange color

    try {
      const Parametres = require('../models/ParametresModel');
      const ResponsableTemplate = require('../models/ResponsableTemplateModel');

      const params = await Parametres.findOne();
      let responsableTemplate = null;

      // Try to get responsable template settings based on the invoice context
      let entrepriseId = null;

      // First, try to get enterprise ID from responsableId (if invoice was created by responsable)
      if (facture.responsableId) {
        entrepriseId = facture.responsableId;
        console.log('Email: Using responsableId as entrepriseId:', entrepriseId);
      }
      // If not, try to get enterprise ID from vendeurId (get the vendeur's enterprise)
      else if (facture.vendeurId) {
        const User = require('../models/UserModel');
        const vendeur = await User.findById(facture.vendeurId);
        if (vendeur && vendeur.entrepriseId) {
          entrepriseId = vendeur.entrepriseId;
          console.log('Email: Using vendeur entrepriseId:', entrepriseId);
        }
      }

      if (entrepriseId) {
        responsableTemplate = await ResponsableTemplate.findOne({
          entrepriseId: entrepriseId
        });
        console.log('Email: Found responsable template for enterprise:', entrepriseId);
      }

      if (params) {
        companyName = params.companyName || companyName;
        emailSignature = params.emailSignature || emailSignature;
        defaultCurrency = params.defaultCurrency || 'EUR';
      }

      // Get template color for styling
      // Priority: ResponsableTemplate > TemplateSettings > Default
      if (responsableTemplate && responsableTemplate.factureTemplate && responsableTemplate.factureTemplate.color) {
        primaryColor = responsableTemplate.factureTemplate.color.replace(/^["'](.+)["']$/, '$1');
        console.log('Email: Using responsable template color:', primaryColor);
      } else if (templateSettings && templateSettings.color) {
        primaryColor = templateSettings.color.replace(/^["'](.+)["']$/, '$1');
        console.log('Email: Using admin template color:', primaryColor);
      }

      console.log('Email template settings:', {
        primaryColor,
        defaultCurrency,
        companyName,
        hasResponsableTemplate: !!responsableTemplate
      });
    } catch (err) {
      console.warn('Could not load parameters or template settings:', err.message);
    }

    // Define a currency formatter function for the email - always use DT
    const formatCurrency = (amount) => {
      if (!amount && amount !== 0) return '0';

      // Format avec des points comme séparateurs de milliers et virgule pour les décimales
      const parts = amount.toFixed(2).split('.');
      const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
      const decimalPart = parts[1];

      // Ajouter manuellement le symbole DT après le nombre
      return `${integerPart},${decimalPart} DT`;
    };

    // Calculate invoice totals
    const totalHT = facture.totalHT || 0;
    const tauxTVA = facture.tauxTVA || 0;
    const montantTVA = facture.montantTVA || 0;
    const totalTTC = facture.total || 0;

    // Email options
    const mailOptions = {
      from: `"${companyName}" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: recipients,
      subject: subject || `Facture ${facture.numero}`,
      text: (message || `Veuillez trouver ci-joint la facture ${facture.numero}.`) + `\n\n${emailSignature}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2 style="color: ${primaryColor}; margin: 0;">Facture ${facture.numero}</h2>
            <div style="text-align: right;">
              <p style="margin: 0;">Date d'émission: ${new Date(facture.dateEmission).toLocaleDateString()}</p>
            </div>
          </div>

          <p>${message || `Veuillez trouver ci-joint la facture ${facture.numero}.`}</p>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span style="font-weight: bold;">Total HT:</span>
              <span>${formatCurrency(totalHT)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span style="font-weight: bold;">TVA (${tauxTVA}%):</span>
              <span>${formatCurrency(montantTVA)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; color: ${primaryColor}; font-weight: bold;">
              <span>Total TTC:</span>
              <span>${formatCurrency(totalTTC)}</span>
            </div>
          </div>

          <div style="margin-top: 30px;">
            <h3 style="color: ${primaryColor}; margin-bottom: 10px;">Conditions</h3>
            <div style="display: flex; margin-bottom: 5px;">
              <span style="font-weight: bold; width: 200px;">Conditions de règlement:</span>
              <span>45 jours fin de mois</span>
            </div>
            <div style="display: flex;">
              <span style="font-weight: bold; width: 200px;">Mode de règlement:</span>
              <span>Virement bancaire</span>
            </div>
          </div>

          <hr style="border: 1px solid #eee; margin: 30px 0;">
          <p style="color: #666;">${emailSignature.replace(/\n/g, '<br>')}</p>
        </div>
      `,
      attachments: [
        {
          filename: `facture-${facture.numero}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }
      ]
    };

    // Send the email
    await transporter.sendMail(mailOptions);
    console.log("Email sent successfully to:", typeof recipients === 'object' ? recipients.join(', ') : recipients);



    res.status(200).json({
      message: "Email sent successfully",
      to,
      subject: mailOptions.subject
    });
  } catch (error) {
    console.error("Error sending email for invoice:", error.message);
    res.status(500).json({ error: "Error while sending email for invoice", details: error.message });
  }
});

// Prepare invoice for printing
router.get("/factures/:id/print", verifyToken, async (req, res) => {
  try {
    console.log(`Received GET /factures/${req.params.id}/print request`);
    const facture = await Facture.findById(req.params.id).populate("clientId");

    if (!facture) return res.status(404).json({ error: "Invoice not found" });

    // Generate PDF directly on the server
    const pdfBuffer = await generateInvoicePDF(facture);

    // Set the response headers for PDF (but not as attachment for printing)
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="facture-${facture.numero}.pdf"`);

    // Send the PDF buffer directly to the client
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error preparing print data for invoice:", error.message);
    res.status(500).json({ error: "Error while preparing print data for invoice" });
  }
});

module.exports = {
  router,
  generateInvoiceNumber
};