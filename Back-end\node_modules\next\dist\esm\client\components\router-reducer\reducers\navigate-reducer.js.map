{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "sourcesContent": ["import type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../../server/app-render/types'\nimport { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { invalidateCacheBelowFlightSegmentPath } from '../invalidate-cache-below-flight-segmentpath'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { shouldHardNavigate } from '../should-hard-navigate'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport {\n  PrefetchCacheEntryStatus,\n  type Mutable,\n  type NavigateAction,\n  type ReadonlyReducerState,\n  type ReducerState,\n} from '../router-reducer-types'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport { prefetchQueue } from './prefetch-reducer'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { DEFAULT_SEGMENT_KEY } from '../../../../shared/lib/segment'\nimport { listenForDynamicRequest, startPPRNavigation } from '../ppr-navigations'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nimport { clearCacheNodeDataForSegmentPath } from '../clear-cache-node-data-for-segment-path'\nimport { handleAliasedPrefetchEntry } from '../aliased-prefetch-navigations'\nimport {\n  navigate as navigateUsingSegmentCache,\n  NavigationResultTag,\n  type NavigationResult,\n} from '../../segment-cache'\n\nexport function handleExternalUrl(\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  url: string,\n  pendingPush: boolean\n) {\n  mutable.mpaNavigation = true\n  mutable.canonicalUrl = url\n  mutable.pendingPush = pendingPush\n  mutable.scrollableSegments = undefined\n\n  return handleMutable(state, mutable)\n}\n\nfunction generateSegmentsFromPatch(\n  flightRouterPatch: FlightRouterState\n): FlightSegmentPath[] {\n  const segments: FlightSegmentPath[] = []\n  const [segment, parallelRoutes] = flightRouterPatch\n\n  if (Object.keys(parallelRoutes).length === 0) {\n    return [[segment]]\n  }\n\n  for (const [parallelRouteKey, parallelRoute] of Object.entries(\n    parallelRoutes\n  )) {\n    for (const childSegment of generateSegmentsFromPatch(parallelRoute)) {\n      // If the segment is empty, it means we are at the root of the tree\n      if (segment === '') {\n        segments.push([parallelRouteKey, ...childSegment])\n      } else {\n        segments.push([segment, parallelRouteKey, ...childSegment])\n      }\n    }\n  }\n\n  return segments\n}\n\nfunction triggerLazyFetchForLeafSegments(\n  newCache: CacheNode,\n  currentCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath,\n  treePatch: FlightRouterState\n) {\n  let appliedPatch = false\n\n  newCache.rsc = currentCache.rsc\n  newCache.prefetchRsc = currentCache.prefetchRsc\n  newCache.loading = currentCache.loading\n  newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n  const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map(\n    (segment) => [...flightSegmentPath, ...segment]\n  )\n\n  for (const segmentPaths of segmentPathsToFill) {\n    clearCacheNodeDataForSegmentPath(newCache, currentCache, segmentPaths)\n\n    appliedPatch = true\n  }\n\n  return appliedPatch\n}\n\nfunction handleNavigationResult(\n  url: URL,\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  pendingPush: boolean,\n  result: NavigationResult\n): ReducerState {\n  switch (result.tag) {\n    case NavigationResultTag.MPA: {\n      // Perform an MPA navigation.\n      const newUrl = result.data\n      return handleExternalUrl(state, mutable, newUrl, pendingPush)\n    }\n    case NavigationResultTag.NoOp: {\n      // The server responded with no change to the current page. However, if\n      // the URL changed, we still need to update that.\n      const newCanonicalUrl = result.data.canonicalUrl\n      mutable.canonicalUrl = newCanonicalUrl\n\n      // Check if the only thing that changed was the hash fragment.\n      const oldUrl = new URL(state.canonicalUrl, url)\n      const onlyHashChange =\n        // We don't need to compare the origins, because client-driven\n        // navigations are always same-origin.\n        url.pathname === oldUrl.pathname &&\n        url.search === oldUrl.search &&\n        url.hash !== oldUrl.hash\n      if (onlyHashChange) {\n        // The only updated part of the URL is the hash.\n        mutable.onlyHashChange = true\n        mutable.shouldScroll = result.data.shouldScroll\n        mutable.hashFragment = url.hash\n        // Setting this to an empty array triggers a scroll for all new and\n        // updated segments. See `ScrollAndFocusHandler` for more details.\n        mutable.scrollableSegments = []\n      }\n\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Success: {\n      // Received a new result.\n      mutable.cache = result.data.cacheNode\n      mutable.patchedTree = result.data.flightRouterState\n      mutable.canonicalUrl = result.data.canonicalUrl\n      mutable.scrollableSegments = result.data.scrollableSegments\n      mutable.shouldScroll = result.data.shouldScroll\n      mutable.hashFragment = result.data.hash\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Async: {\n      return result.data.then(\n        (asyncResult) =>\n          handleNavigationResult(url, state, mutable, pendingPush, asyncResult),\n        // If the navigation failed, return the current state.\n        // TODO: This matches the current behavior but we need to do something\n        // better here if the network fails.\n        () => {\n          return state\n        }\n      )\n    }\n    default: {\n      result satisfies never\n      return state\n    }\n  }\n}\n\nexport function navigateReducer(\n  state: ReadonlyReducerState,\n  action: NavigateAction\n): ReducerState {\n  const { url, isExternalUrl, navigateType, shouldScroll, allowAliasing } =\n    action\n  const mutable: Mutable = {}\n  const { hash } = url\n  const href = createHrefFromUrl(url)\n  const pendingPush = navigateType === 'push'\n  // we want to prune the prefetch cache on every navigation to avoid it growing too large\n  prunePrefetchCache(state.prefetchCache)\n\n  mutable.preserveCustomHistoryState = false\n  mutable.pendingPush = pendingPush\n\n  if (isExternalUrl) {\n    return handleExternalUrl(state, mutable, url.toString(), pendingPush)\n  }\n\n  // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n  // which will trigger an MPA navigation.\n  if (document.getElementById('__next-page-redirect')) {\n    return handleExternalUrl(state, mutable, href, pendingPush)\n  }\n\n  if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // (Very Early Experimental Feature) Segment Cache\n    //\n    // Bypass the normal prefetch cache and use the new per-segment cache\n    // implementation instead. This is only supported if PPR is enabled, too.\n    //\n    // Temporary glue code between the router reducer and the new navigation\n    // implementation. Eventually we'll rewrite the router reducer to a\n    // state machine.\n    const result = navigateUsingSegmentCache(\n      url,\n      state.cache,\n      state.tree,\n      state.nextUrl,\n      shouldScroll\n    )\n    return handleNavigationResult(url, state, mutable, pendingPush, result)\n  }\n\n  const prefetchValues = getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    tree: state.tree,\n    prefetchCache: state.prefetchCache,\n    allowAliasing,\n  })\n  const { treeAtTimeOfPrefetch, data } = prefetchValues\n\n  prefetchQueue.bump(data)\n\n  return data.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride, postponed }) => {\n      const navigatedAt = Date.now()\n\n      let isFirstRead = false\n      // we only want to mark this once\n      if (!prefetchValues.lastUsedTime) {\n        // important: we should only mark the cache node as dirty after we unsuspend from the call above\n        prefetchValues.lastUsedTime = navigatedAt\n        isFirstRead = true\n      }\n\n      if (prefetchValues.aliased) {\n        const result = handleAliasedPrefetchEntry(\n          navigatedAt,\n          state,\n          flightData,\n          url,\n          mutable\n        )\n\n        // We didn't return new router state because we didn't apply the aliased entry for some reason.\n        // We'll re-invoke the navigation handler but ensure that we don't attempt to use the aliased entry. This\n        // will create an on-demand prefetch entry.\n        if (result === false) {\n          return navigateReducer(state, { ...action, allowAliasing: false })\n        }\n\n        return result\n      }\n\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(state, mutable, flightData, pendingPush)\n      }\n\n      const updatedCanonicalUrl = canonicalUrlOverride\n        ? createHrefFromUrl(canonicalUrlOverride)\n        : href\n\n      const onlyHashChange =\n        !!hash &&\n        state.canonicalUrl.split('#', 1)[0] ===\n          updatedCanonicalUrl.split('#', 1)[0]\n\n      // If only the hash has changed, the server hasn't sent us any new data. We can just update\n      // the mutable properties responsible for URL and scroll handling and return early.\n      if (onlyHashChange) {\n        mutable.onlyHashChange = true\n        mutable.canonicalUrl = updatedCanonicalUrl\n        mutable.shouldScroll = shouldScroll\n        mutable.hashFragment = hash\n        mutable.scrollableSegments = []\n        return handleMutable(state, mutable)\n      }\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n      let scrollableSegments: FlightSegmentPath[] = []\n      for (const normalizedFlightData of flightData) {\n        const {\n          pathToSegment: flightSegmentPath,\n          seedData,\n          head,\n          isHeadPartial,\n          isRootRender,\n        } = normalizedFlightData\n        let treePatch = normalizedFlightData.tree\n\n        // TODO-APP: remove ''\n        const flightSegmentPathWithLeadingEmpty = ['', ...flightSegmentPath]\n\n        // Create new tree based on the flightSegmentPath and router state patch\n        let newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          flightSegmentPathWithLeadingEmpty,\n          currentTree,\n          treePatch,\n          href\n        )\n\n        // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n        // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n        if (newTree === null) {\n          newTree = applyRouterStatePatchToTree(\n            // TODO-APP: remove ''\n            flightSegmentPathWithLeadingEmpty,\n            treeAtTimeOfPrefetch,\n            treePatch,\n            href\n          )\n        }\n\n        if (newTree !== null) {\n          if (\n            // This is just a paranoid check. When a route is PPRed, the server\n            // will send back a static response that's rendered from\n            // the root. If for some reason it doesn't, we fall back to the\n            // non-PPR implementation.\n            // TODO: We should get rid of the else branch and do all navigations\n            // via startPPRNavigation. The current structure is just\n            // an incremental step.\n            seedData &&\n            isRootRender &&\n            postponed\n          ) {\n            const task = startPPRNavigation(\n              navigatedAt,\n              currentCache,\n              currentTree,\n              treePatch,\n              seedData,\n              head,\n              isHeadPartial,\n              false,\n              scrollableSegments\n            )\n\n            if (task !== null) {\n              if (task.route === null) {\n                // Detected a change to the root layout. Perform an full-\n                // page navigation.\n                return handleExternalUrl(state, mutable, href, pendingPush)\n              }\n              // Use the tree computed by startPPRNavigation instead\n              // of the one computed by applyRouterStatePatchToTree.\n              // TODO: We should remove applyRouterStatePatchToTree\n              // from the PPR path entirely.\n              const patchedRouterState: FlightRouterState = task.route\n              newTree = patchedRouterState\n\n              const newCache = task.node\n              if (newCache !== null) {\n                // We've created a new Cache Node tree that contains a prefetched\n                // version of the next page. This can be rendered instantly.\n                mutable.cache = newCache\n              }\n              const dynamicRequestTree = task.dynamicRequestTree\n              if (dynamicRequestTree !== null) {\n                // The prefetched tree has dynamic holes in it. We initiate a\n                // dynamic request to fill them in.\n                //\n                // Do not block on the result. We'll immediately render the Cache\n                // Node tree and suspend on the dynamic parts. When the request\n                // comes in, we'll fill in missing data and ping React to\n                // re-render. Unlike the lazy fetching model in the non-PPR\n                // implementation, this is modeled as a single React update +\n                // streaming, rather than multiple top-level updates. (However,\n                // even in the new model, we'll still need to sometimes update the\n                // root multiple times per navigation, like if the server sends us\n                // a different response than we expected. For now, we revert back\n                // to the lazy fetching mechanism in that case.)\n                const dynamicRequest = fetchServerResponse(url, {\n                  flightRouterState: dynamicRequestTree,\n                  nextUrl: state.nextUrl,\n                })\n\n                listenForDynamicRequest(task, dynamicRequest)\n                // We store the dynamic request on the `lazyData` property of the CacheNode\n                // because we're not going to await the dynamic request here. Since we're not blocking\n                // on the dynamic request, `layout-router` will\n                // task.node.lazyData = dynamicRequest\n              } else {\n                // The prefetched tree does not contain dynamic holes — it's\n                // fully static. We can skip the dynamic request.\n              }\n            } else {\n              // Nothing changed, so reuse the old cache.\n              // TODO: What if the head changed but not any of the segment data?\n              // Is that possible? If so, we should clone the whole tree and\n              // update the head.\n              newTree = treePatch\n            }\n          } else {\n            // The static response does not include any dynamic holes, so\n            // there's no need to do a second request.\n            // TODO: As an incremental step this just reverts back to the\n            // non-PPR implementation. We can simplify this branch further,\n            // given that PPR prefetches are always static and return the whole\n            // tree. Or in the meantime we could factor it out into a\n            // separate function.\n\n            if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n              return handleExternalUrl(state, mutable, href, pendingPush)\n            }\n\n            const cache: CacheNode = createEmptyCacheNode()\n            let applied = false\n\n            if (\n              prefetchValues.status === PrefetchCacheEntryStatus.stale &&\n              !isFirstRead\n            ) {\n              // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n              // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n              // while copying over the `loading` for the segment that contains the page data.\n              // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n\n              // We skip this branch if only the hash fragment has changed, as we don't want to trigger a lazy fetch in that case\n              applied = triggerLazyFetchForLeafSegments(\n                cache,\n                currentCache,\n                flightSegmentPath,\n                treePatch\n              )\n              // since we re-used the stale cache's loading state & refreshed the data,\n              // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n              prefetchValues.lastUsedTime = navigatedAt\n            } else {\n              applied = applyFlightData(\n                navigatedAt,\n                currentCache,\n                cache,\n                normalizedFlightData,\n                prefetchValues\n              )\n            }\n\n            const hardNavigate = shouldHardNavigate(\n              // TODO-APP: remove ''\n              flightSegmentPathWithLeadingEmpty,\n              currentTree\n            )\n\n            if (hardNavigate) {\n              // Copy rsc for the root node of the cache.\n              cache.rsc = currentCache.rsc\n              cache.prefetchRsc = currentCache.prefetchRsc\n\n              invalidateCacheBelowFlightSegmentPath(\n                cache,\n                currentCache,\n                flightSegmentPath\n              )\n              // Ensure the existing cache value is used when the cache was not invalidated.\n              mutable.cache = cache\n            } else if (applied) {\n              mutable.cache = cache\n              // If we applied the cache, we update the \"current cache\" value so any other\n              // segments in the FlightDataPath will be able to reference the updated cache.\n              currentCache = cache\n            }\n\n            for (const subSegment of generateSegmentsFromPatch(treePatch)) {\n              const scrollableSegmentPath = [\n                ...flightSegmentPath,\n                ...subSegment,\n              ]\n              // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n              if (\n                scrollableSegmentPath[scrollableSegmentPath.length - 1] !==\n                DEFAULT_SEGMENT_KEY\n              ) {\n                scrollableSegments.push(scrollableSegmentPath)\n              }\n            }\n          }\n\n          currentTree = newTree\n        }\n      }\n\n      mutable.patchedTree = currentTree\n      mutable.canonicalUrl = updatedCanonicalUrl\n      mutable.scrollableSegments = scrollableSegments\n      mutable.hashFragment = hash\n      mutable.shouldScroll = shouldScroll\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n"], "names": ["fetchServerResponse", "createHrefFromUrl", "invalidateCacheBelowFlightSegmentPath", "applyRouterStatePatchToTree", "shouldHardNavigate", "isNavigatingToNewRootLayout", "PrefetchCacheEntryStatus", "handleMutable", "applyFlightData", "prefetchQueue", "createEmptyCacheNode", "DEFAULT_SEGMENT_KEY", "listenForDynamicRequest", "startPPRNavigation", "getOrCreatePrefetchCacheEntry", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "clearCacheNodeDataForSegmentPath", "handleAliasedPrefetchEntry", "navigate", "navigateUsingSegmentCache", "NavigationResultTag", "handleExternalUrl", "state", "mutable", "url", "pendingPush", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "triggerLazyFetchForLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "appliedPatch", "rsc", "prefetchRsc", "loading", "Map", "segmentPathsToFill", "map", "segmentPaths", "handleNavigationResult", "result", "tag", "MPA", "newUrl", "data", "NoOp", "newCanonicalUrl", "oldUrl", "URL", "onlyHashChange", "pathname", "search", "hash", "shouldScroll", "hashFragment", "Success", "cache", "cacheNode", "patchedTree", "flightRouterState", "Async", "then", "asyncResult", "navigateReducer", "action", "isExternalUrl", "navigateType", "allowAliasing", "href", "prefetchCache", "preserveCustomHistoryState", "toString", "document", "getElementById", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "tree", "nextUrl", "prefetchValues", "treeAtTimeOfPrefetch", "bump", "flightData", "canonicalUrlOverride", "postponed", "navigatedAt", "Date", "now", "isFirstRead", "lastUsedTime", "aliased", "updatedCanonicalUrl", "split", "currentTree", "normalizedFlightData", "pathToSegment", "seedData", "head", "isHeadPartial", "isRootRender", "flightSegmentPathWithLeadingEmpty", "newTree", "task", "route", "patchedRouterState", "node", "dynamicRequestTree", "dynamicRequest", "applied", "status", "stale", "hardNavigate", "subSegment", "scrollableSegmentPath"], "mappings": "AAKA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,qCAAqC,QAAQ,+CAA8C;AACpG,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SACEC,wBAAwB,QAKnB,0BAAyB;AAChC,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,mBAAmB,QAAQ,iCAAgC;AACpE,SAASC,uBAAuB,EAAEC,kBAAkB,QAAQ,qBAAoB;AAChF,SACEC,6BAA6B,EAC7BC,kBAAkB,QACb,0BAAyB;AAChC,SAASC,gCAAgC,QAAQ,4CAA2C;AAC5F,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SACEC,YAAYC,yBAAyB,EACrCC,mBAAmB,QAEd,sBAAqB;AAE5B,OAAO,SAASC,kBACdC,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,aAAa,GAAG;IACxBH,QAAQI,YAAY,GAAGH;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQK,kBAAkB,GAAGC;IAE7B,OAAOtB,cAAce,OAAOC;AAC9B;AAEA,SAASO,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,gCACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B;IAE5B,IAAIC,eAAe;IAEnBJ,SAASK,GAAG,GAAGJ,aAAaI,GAAG;IAC/BL,SAASM,WAAW,GAAGL,aAAaK,WAAW;IAC/CN,SAASO,OAAO,GAAGN,aAAaM,OAAO;IACvCP,SAASV,cAAc,GAAG,IAAIkB,IAAIP,aAAaX,cAAc;IAE7D,MAAMmB,qBAAqBvB,0BAA0BiB,WAAWO,GAAG,CACjE,CAACrB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMsB,gBAAgBF,mBAAoB;QAC7CrC,iCAAiC4B,UAAUC,cAAcU;QAEzDP,eAAe;IACjB;IAEA,OAAOA;AACT;AAEA,SAASQ,uBACPhC,GAAQ,EACRF,KAA2B,EAC3BC,OAAgB,EAChBE,WAAoB,EACpBgC,MAAwB;IAExB,OAAQA,OAAOC,GAAG;QAChB,KAAKtC,oBAAoBuC,GAAG;YAAE;gBAC5B,6BAA6B;gBAC7B,MAAMC,SAASH,OAAOI,IAAI;gBAC1B,OAAOxC,kBAAkBC,OAAOC,SAASqC,QAAQnC;YACnD;QACA,KAAKL,oBAAoB0C,IAAI;YAAE;gBAC7B,uEAAuE;gBACvE,iDAAiD;gBACjD,MAAMC,kBAAkBN,OAAOI,IAAI,CAAClC,YAAY;gBAChDJ,QAAQI,YAAY,GAAGoC;gBAEvB,8DAA8D;gBAC9D,MAAMC,SAAS,IAAIC,IAAI3C,MAAMK,YAAY,EAAEH;gBAC3C,MAAM0C,iBACJ,8DAA8D;gBAC9D,sCAAsC;gBACtC1C,IAAI2C,QAAQ,KAAKH,OAAOG,QAAQ,IAChC3C,IAAI4C,MAAM,KAAKJ,OAAOI,MAAM,IAC5B5C,IAAI6C,IAAI,KAAKL,OAAOK,IAAI;gBAC1B,IAAIH,gBAAgB;oBAClB,gDAAgD;oBAChD3C,QAAQ2C,cAAc,GAAG;oBACzB3C,QAAQ+C,YAAY,GAAGb,OAAOI,IAAI,CAACS,YAAY;oBAC/C/C,QAAQgD,YAAY,GAAG/C,IAAI6C,IAAI;oBAC/B,mEAAmE;oBACnE,kEAAkE;oBAClE9C,QAAQK,kBAAkB,GAAG,EAAE;gBACjC;gBAEA,OAAOrB,cAAce,OAAOC;YAC9B;QACA,KAAKH,oBAAoBoD,OAAO;YAAE;gBAChC,yBAAyB;gBACzBjD,QAAQkD,KAAK,GAAGhB,OAAOI,IAAI,CAACa,SAAS;gBACrCnD,QAAQoD,WAAW,GAAGlB,OAAOI,IAAI,CAACe,iBAAiB;gBACnDrD,QAAQI,YAAY,GAAG8B,OAAOI,IAAI,CAAClC,YAAY;gBAC/CJ,QAAQK,kBAAkB,GAAG6B,OAAOI,IAAI,CAACjC,kBAAkB;gBAC3DL,QAAQ+C,YAAY,GAAGb,OAAOI,IAAI,CAACS,YAAY;gBAC/C/C,QAAQgD,YAAY,GAAGd,OAAOI,IAAI,CAACQ,IAAI;gBACvC,OAAO9D,cAAce,OAAOC;YAC9B;QACA,KAAKH,oBAAoByD,KAAK;YAAE;gBAC9B,OAAOpB,OAAOI,IAAI,CAACiB,IAAI,CACrB,CAACC,cACCvB,uBAAuBhC,KAAKF,OAAOC,SAASE,aAAasD,cAC3D,sDAAsD;gBACtD,sEAAsE;gBACtE,oCAAoC;gBACpC;oBACE,OAAOzD;gBACT;YAEJ;QACA;YAAS;gBACPmC;gBACA,OAAOnC;YACT;IACF;AACF;AAEA,OAAO,SAAS0D,gBACd1D,KAA2B,EAC3B2D,MAAsB;IAEtB,MAAM,EAAEzD,GAAG,EAAE0D,aAAa,EAAEC,YAAY,EAAEb,YAAY,EAAEc,aAAa,EAAE,GACrEH;IACF,MAAM1D,UAAmB,CAAC;IAC1B,MAAM,EAAE8C,IAAI,EAAE,GAAG7C;IACjB,MAAM6D,OAAOpF,kBAAkBuB;IAC/B,MAAMC,cAAc0D,iBAAiB;IACrC,wFAAwF;IACxFpE,mBAAmBO,MAAMgE,aAAa;IAEtC/D,QAAQgE,0BAA0B,GAAG;IACrChE,QAAQE,WAAW,GAAGA;IAEtB,IAAIyD,eAAe;QACjB,OAAO7D,kBAAkBC,OAAOC,SAASC,IAAIgE,QAAQ,IAAI/D;IAC3D;IAEA,mEAAmE;IACnE,wCAAwC;IACxC,IAAIgE,SAASC,cAAc,CAAC,yBAAyB;QACnD,OAAOrE,kBAAkBC,OAAOC,SAAS8D,MAAM5D;IACjD;IAEA,IAAIkE,QAAQC,GAAG,CAACC,2BAA2B,EAAE;QAC3C,kDAAkD;QAClD,EAAE;QACF,qEAAqE;QACrE,yEAAyE;QACzE,EAAE;QACF,wEAAwE;QACxE,mEAAmE;QACnE,iBAAiB;QACjB,MAAMpC,SAAStC,0BACbK,KACAF,MAAMmD,KAAK,EACXnD,MAAMwE,IAAI,EACVxE,MAAMyE,OAAO,EACbzB;QAEF,OAAOd,uBAAuBhC,KAAKF,OAAOC,SAASE,aAAagC;IAClE;IAEA,MAAMuC,iBAAiBlF,8BAA8B;QACnDU;QACAuE,SAASzE,MAAMyE,OAAO;QACtBD,MAAMxE,MAAMwE,IAAI;QAChBR,eAAehE,MAAMgE,aAAa;QAClCF;IACF;IACA,MAAM,EAAEa,oBAAoB,EAAEpC,IAAI,EAAE,GAAGmC;IAEvCvF,cAAcyF,IAAI,CAACrC;IAEnB,OAAOA,KAAKiB,IAAI,CACd;YAAC,EAAEqB,UAAU,EAAExE,cAAcyE,oBAAoB,EAAEC,SAAS,EAAE;QAC5D,MAAMC,cAAcC,KAAKC,GAAG;QAE5B,IAAIC,cAAc;QAClB,iCAAiC;QACjC,IAAI,CAACT,eAAeU,YAAY,EAAE;YAChC,gGAAgG;YAChGV,eAAeU,YAAY,GAAGJ;YAC9BG,cAAc;QAChB;QAEA,IAAIT,eAAeW,OAAO,EAAE;YAC1B,MAAMlD,SAASxC,2BACbqF,aACAhF,OACA6E,YACA3E,KACAD;YAGF,+FAA+F;YAC/F,yGAAyG;YACzG,2CAA2C;YAC3C,IAAIkC,WAAW,OAAO;gBACpB,OAAOuB,gBAAgB1D,OAAO;oBAAE,GAAG2D,MAAM;oBAAEG,eAAe;gBAAM;YAClE;YAEA,OAAO3B;QACT;QAEA,4DAA4D;QAC5D,IAAI,OAAO0C,eAAe,UAAU;YAClC,OAAO9E,kBAAkBC,OAAOC,SAAS4E,YAAY1E;QACvD;QAEA,MAAMmF,sBAAsBR,uBACxBnG,kBAAkBmG,wBAClBf;QAEJ,MAAMnB,iBACJ,CAAC,CAACG,QACF/C,MAAMK,YAAY,CAACkF,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,KACjCD,oBAAoBC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;QAExC,2FAA2F;QAC3F,mFAAmF;QACnF,IAAI3C,gBAAgB;YAClB3C,QAAQ2C,cAAc,GAAG;YACzB3C,QAAQI,YAAY,GAAGiF;YACvBrF,QAAQ+C,YAAY,GAAGA;YACvB/C,QAAQgD,YAAY,GAAGF;YACvB9C,QAAQK,kBAAkB,GAAG,EAAE;YAC/B,OAAOrB,cAAce,OAAOC;QAC9B;QAEA,IAAIuF,cAAcxF,MAAMwE,IAAI;QAC5B,IAAIjD,eAAevB,MAAMmD,KAAK;QAC9B,IAAI7C,qBAA0C,EAAE;QAChD,KAAK,MAAMmF,wBAAwBZ,WAAY;YAC7C,MAAM,EACJa,eAAelE,iBAAiB,EAChCmE,QAAQ,EACRC,IAAI,EACJC,aAAa,EACbC,YAAY,EACb,GAAGL;YACJ,IAAIhE,YAAYgE,qBAAqBjB,IAAI;YAEzC,sBAAsB;YACtB,MAAMuB,oCAAoC;gBAAC;mBAAOvE;aAAkB;YAEpE,wEAAwE;YACxE,IAAIwE,UAAUnH,4BACZ,sBAAsB;YACtBkH,mCACAP,aACA/D,WACAsC;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAIiC,YAAY,MAAM;gBACpBA,UAAUnH,4BACR,sBAAsB;gBACtBkH,mCACApB,sBACAlD,WACAsC;YAEJ;YAEA,IAAIiC,YAAY,MAAM;gBACpB,IACE,mEAAmE;gBACnE,wDAAwD;gBACxD,+DAA+D;gBAC/D,0BAA0B;gBAC1B,oEAAoE;gBACpE,wDAAwD;gBACxD,uBAAuB;gBACvBL,YACAG,gBACAf,WACA;oBACA,MAAMkB,OAAO1G,mBACXyF,aACAzD,cACAiE,aACA/D,WACAkE,UACAC,MACAC,eACA,OACAvF;oBAGF,IAAI2F,SAAS,MAAM;wBACjB,IAAIA,KAAKC,KAAK,KAAK,MAAM;4BACvB,yDAAyD;4BACzD,mBAAmB;4BACnB,OAAOnG,kBAAkBC,OAAOC,SAAS8D,MAAM5D;wBACjD;wBACA,sDAAsD;wBACtD,sDAAsD;wBACtD,qDAAqD;wBACrD,8BAA8B;wBAC9B,MAAMgG,qBAAwCF,KAAKC,KAAK;wBACxDF,UAAUG;wBAEV,MAAM7E,WAAW2E,KAAKG,IAAI;wBAC1B,IAAI9E,aAAa,MAAM;4BACrB,iEAAiE;4BACjE,4DAA4D;4BAC5DrB,QAAQkD,KAAK,GAAG7B;wBAClB;wBACA,MAAM+E,qBAAqBJ,KAAKI,kBAAkB;wBAClD,IAAIA,uBAAuB,MAAM;4BAC/B,6DAA6D;4BAC7D,mCAAmC;4BACnC,EAAE;4BACF,iEAAiE;4BACjE,+DAA+D;4BAC/D,yDAAyD;4BACzD,2DAA2D;4BAC3D,6DAA6D;4BAC7D,+DAA+D;4BAC/D,kEAAkE;4BAClE,kEAAkE;4BAClE,iEAAiE;4BACjE,gDAAgD;4BAChD,MAAMC,iBAAiB5H,oBAAoBwB,KAAK;gCAC9CoD,mBAAmB+C;gCACnB5B,SAASzE,MAAMyE,OAAO;4BACxB;4BAEAnF,wBAAwB2G,MAAMK;wBAC9B,2EAA2E;wBAC3E,sFAAsF;wBACtF,+CAA+C;wBAC/C,sCAAsC;wBACxC,OAAO;wBACL,4DAA4D;wBAC5D,iDAAiD;wBACnD;oBACF,OAAO;wBACL,2CAA2C;wBAC3C,kEAAkE;wBAClE,8DAA8D;wBAC9D,mBAAmB;wBACnBN,UAAUvE;oBACZ;gBACF,OAAO;oBACL,6DAA6D;oBAC7D,0CAA0C;oBAC1C,6DAA6D;oBAC7D,+DAA+D;oBAC/D,mEAAmE;oBACnE,yDAAyD;oBACzD,qBAAqB;oBAErB,IAAI1C,4BAA4ByG,aAAaQ,UAAU;wBACrD,OAAOjG,kBAAkBC,OAAOC,SAAS8D,MAAM5D;oBACjD;oBAEA,MAAMgD,QAAmB/D;oBACzB,IAAImH,UAAU;oBAEd,IACE7B,eAAe8B,MAAM,KAAKxH,yBAAyByH,KAAK,IACxD,CAACtB,aACD;wBACA,yJAAyJ;wBACzJ,uHAAuH;wBACvH,gFAAgF;wBAChF,0FAA0F;wBAE1F,mHAAmH;wBACnHoB,UAAUlF,gCACR8B,OACA5B,cACAC,mBACAC;wBAEF,yEAAyE;wBACzE,mFAAmF;wBACnFiD,eAAeU,YAAY,GAAGJ;oBAChC,OAAO;wBACLuB,UAAUrH,gBACR8F,aACAzD,cACA4B,OACAsC,sBACAf;oBAEJ;oBAEA,MAAMgC,eAAe5H,mBACnB,sBAAsB;oBACtBiH,mCACAP;oBAGF,IAAIkB,cAAc;wBAChB,2CAA2C;wBAC3CvD,MAAMxB,GAAG,GAAGJ,aAAaI,GAAG;wBAC5BwB,MAAMvB,WAAW,GAAGL,aAAaK,WAAW;wBAE5ChD,sCACEuE,OACA5B,cACAC;wBAEF,8EAA8E;wBAC9EvB,QAAQkD,KAAK,GAAGA;oBAClB,OAAO,IAAIoD,SAAS;wBAClBtG,QAAQkD,KAAK,GAAGA;wBAChB,4EAA4E;wBAC5E,8EAA8E;wBAC9E5B,eAAe4B;oBACjB;oBAEA,KAAK,MAAMwD,cAAcnG,0BAA0BiB,WAAY;wBAC7D,MAAMmF,wBAAwB;+BACzBpF;+BACAmF;yBACJ;wBACD,kFAAkF;wBAClF,IACEC,qBAAqB,CAACA,sBAAsB7F,MAAM,GAAG,EAAE,KACvD1B,qBACA;4BACAiB,mBAAmBc,IAAI,CAACwF;wBAC1B;oBACF;gBACF;gBAEApB,cAAcQ;YAChB;QACF;QAEA/F,QAAQoD,WAAW,GAAGmC;QACtBvF,QAAQI,YAAY,GAAGiF;QACvBrF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQgD,YAAY,GAAGF;QACvB9C,QAAQ+C,YAAY,GAAGA;QAEvB,OAAO/D,cAAce,OAAOC;IAC9B,GACA,IAAMD;AAEV"}