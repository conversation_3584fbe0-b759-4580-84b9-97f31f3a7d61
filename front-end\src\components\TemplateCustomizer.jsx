import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  TextField,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { updateTemplate, createTemplate } from '../services/templateService';

// Predefined color palettes
const colorPalettes = [
  // Row 1
  ['#e67e22', '#f39c12', '#d81b60', '#8e24aa', '#3f51b5', '#00bcd4'],
  // Row 2
  ['#f1c40f', '#ff9800', '#e91e63', '#9c27b0', '#5677fc', '#009688'],
];

const TemplateCustomizer = ({ open, onClose, template, onSave }) => {
  const theme = useTheme();
  const [selectedColor, setSelectedColor] = useState(template?.color || '#3f51b5');
  const [templateName, setTemplateName] = useState(template?.name || 'Nouveau template');
  const [customColor, setCustomColor] = useState('');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [templateType, setTemplateType] = useState(template?.type || 'Facture');

  useEffect(() => {
    if (template) {
      setSelectedColor(template.color || '#3f51b5');
      setTemplateName(template.name || 'Nouveau template');
      setTemplateType(template.type || 'Facture');
    }
  }, [template]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
  };

  const handleCustomColorChange = (e) => {
    setCustomColor(e.target.value);
  };

  const handleApplyCustomColor = () => {
    if (customColor && /^#([0-9A-F]{3}){1,2}$/i.test(customColor)) {
      setSelectedColor(customColor);
    }
  };

  const handleTypeChange = (type) => {
    setTemplateType(type);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const templateData = {
        name: templateName,
        type: templateType.toLowerCase(),
        layout: 'standard',
        isActive: true
      };

      console.log('Données du template à enregistrer:', templateData);
      let result;

      // Check if we have a valid MongoDB ID (24 hex characters)
      const isValidMongoId = template?._id && /^[0-9a-fA-F]{24}$/.test(template._id);
      const templateId = isValidMongoId ? template._id : (template?.id || null);

      if (templateId) {
        // Update existing template
        console.log(`Mise à jour du template existant avec ID: ${templateId}`);
        try {
          result = await updateTemplate(templateId, templateData);
        } catch (err) {
          console.error('Erreur lors de la mise à jour du template:', err);
          // Si l'ID n'est pas valide, créer un nouveau template
          if (err.message && err.message.includes('ID de template invalide')) {
            console.log('ID invalide, création d\'un nouveau template à la place');
            result = await createTemplate(templateData);
          } else {
            throw err;
          }
        }
      } else {
        // Create new template
        console.log('Création d\'un nouveau template');
        result = await createTemplate(templateData);
      }

      console.log('Template enregistré avec succès:', result);

      if (onSave) {
        // Pass the complete template data to the parent component
        onSave({
          ...result,
          name: templateName,
          type: templateType,
          color: selectedColor,
          secondaryColor: secondaryColor,
          font: font,
          layout: layout,
          thumbnail: `/templates/thumbnails/template-${templateType.toLowerCase()}-standard.svg`
        });
      }

      onClose();
    } catch (err) {
      console.error('Error saving template:', err);
      // Afficher un message d'erreur plus détaillé
      const errorMessage = err.message || 'Erreur lors de l\'enregistrement du template';
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        bgcolor: theme.palette.background.default,
        borderBottom: `1px solid ${theme.palette.divider}`
      }}>
        <Typography variant="h6">Style de vos documents</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Grid container>
          {/* Left panel - Customization options */}
          <Grid item xs={12} md={4} sx={{
            p: 3,
            borderRight: `1px solid ${theme.palette.divider}`,
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Couleur
            </Typography>

            <Box sx={{ mb: 3 }}>
              {colorPalettes.map((row, rowIndex) => (
                <Box
                  key={rowIndex}
                  sx={{
                    display: 'flex',
                    gap: 1,
                    mb: 1
                  }}
                >
                  {row.map((color) => (
                    <Box
                      component={motion.div}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      key={color}
                      onClick={() => handleColorSelect(color)}
                      sx={{
                        width: 40,
                        height: 40,
                        bgcolor: color,
                        borderRadius: 1,
                        cursor: 'pointer',
                        border: selectedColor === color ? `3px solid ${theme.palette.primary.main}` : 'none',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
                        }
                      }}
                    />
                  ))}
                </Box>
              ))}
            </Box>

            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 3
            }}>
              <TextField
                label="Couleur personnalisée"
                value={customColor}
                onChange={handleCustomColorChange}
                placeholder="#RRGGBB"
                size="small"
                fullWidth
                InputProps={{
                  startAdornment: (
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '4px',
                        bgcolor: customColor || '#fff',
                        border: '1px solid #ddd',
                        mr: 1
                      }}
                    />
                  )
                }}
              />
              <Button
                variant="outlined"
                size="small"
                onClick={handleApplyCustomColor}
              >
                Appliquer
              </Button>
            </Box>

            <Box sx={{
              p: 2,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
              borderRadius: 2,
              mb: 3
            }}>
              <Typography variant="subtitle2" gutterBottom>
                Nom du template
              </Typography>
              <TextField
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                fullWidth
                size="small"
                variant="outlined"
                sx={{ mb: 2 }}
              />

              <Typography variant="subtitle2" gutterBottom>
                Type de document
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant={templateType === 'Facture' ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => handleTypeChange('Facture')}
                  sx={{
                    flex: 1,
                    bgcolor: templateType === 'Facture' ? selectedColor : 'transparent',
                    borderColor: selectedColor,
                    color: templateType === 'Facture' ? '#fff' : selectedColor,
                    '&:hover': {
                      bgcolor: templateType === 'Facture' ? selectedColor : 'transparent',
                      filter: templateType === 'Facture' ? 'brightness(0.9)' : 'none',
                      borderColor: selectedColor,
                      opacity: 0.9
                    }
                  }}
                >
                  Facture
                </Button>
                <Button
                  variant={templateType === 'Devis' ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => handleTypeChange('Devis')}
                  sx={{
                    flex: 1,
                    bgcolor: templateType === 'Devis' ? selectedColor : 'transparent',
                    borderColor: selectedColor,
                    color: templateType === 'Devis' ? '#fff' : selectedColor,
                    '&:hover': {
                      bgcolor: templateType === 'Devis' ? selectedColor : 'transparent',
                      filter: templateType === 'Devis' ? 'brightness(0.9)' : 'none',
                      borderColor: selectedColor,
                      opacity: 0.9
                    }
                  }}
                >
                  Devis
                </Button>
                <Button
                  variant={templateType === 'Both' ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => handleTypeChange('Both')}
                  sx={{
                    flex: 1,
                    bgcolor: templateType === 'Both' ? selectedColor : 'transparent',
                    borderColor: selectedColor,
                    color: templateType === 'Both' ? '#fff' : selectedColor,
                    '&:hover': {
                      bgcolor: templateType === 'Both' ? selectedColor : 'transparent',
                      filter: templateType === 'Both' ? 'brightness(0.9)' : 'none',
                      borderColor: selectedColor,
                      opacity: 0.9
                    }
                  }}
                >
                  Les deux
                </Button>
              </Box>
            </Box>

            <Box sx={{ mt: 'auto' }}>
              <Button
                variant="contained"
                fullWidth
                startIcon={<SaveIcon />}
                onClick={handleSave}
                sx={{
                  bgcolor: selectedColor,
                  '&:hover': {
                    bgcolor: selectedColor,
                    filter: 'brightness(0.9)'
                  }
                }}
              >
                Enregistrer
              </Button>
            </Box>
          </Grid>

          {/* Right panel - Preview */}
          <Grid item xs={12} md={8} sx={{ p: 3 }}>
            <Box sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography variant="subtitle1" gutterBottom sx={{ alignSelf: 'flex-start', mb: 2 }}>
                {template?.type === 'Devis' ? 'Devis' : 'Facture'}
              </Typography>

              {/* Document Preview */}
              <Paper
                elevation={3}
                sx={{
                  width: '100%',
                  maxWidth: 600,
                  height: 500,
                  overflow: 'hidden',
                  position: 'relative'
                }}
              >
                {/* Header with selected color */}
                <Box sx={{
                  bgcolor: selectedColor,
                  height: '80px',
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  px: 3,
                  color: '#fff'
                }}>
                  <Typography variant="h6">Votre Entreprise</Typography>
                  <Typography variant="h6">
                    {template?.type === 'Devis' ? 'DEVIS' : 'FACTURE'}
                  </Typography>
                </Box>

                {/* Document content */}
                <Box sx={{ p: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">ÉMETTEUR</Typography>
                      <Typography variant="body1">Votre Entreprise</Typography>
                      <Typography variant="body2">123 Rue Exemple</Typography>
                      <Typography variant="body2">75000 Paris</Typography>
                      <Typography variant="body2"><EMAIL></Typography>
                    </Grid>
                    <Grid item xs={6} sx={{ textAlign: 'right' }}>
                      <Typography variant="body2" color="textSecondary">DESTINATAIRE</Typography>
                      <Typography variant="body1">Client Exemple</Typography>
                      <Typography variant="body2">456 Avenue Client</Typography>
                      <Typography variant="body2">69000 Lyon</Typography>
                      <Typography variant="body2"><EMAIL></Typography>
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  {/* Table header with selected color */}
                  <Box sx={{
                    bgcolor: selectedColor,
                    p: 1,
                    borderRadius: '4px 4px 0 0',
                    color: '#fff',
                    display: 'flex',
                    mb: 1
                  }}>
                    <Box sx={{ flex: 3 }}>
                      <Typography variant="body2" fontWeight="bold">Description</Typography>
                    </Box>
                    <Box sx={{ flex: 1, textAlign: 'center' }}>
                      <Typography variant="body2" fontWeight="bold">Quantité</Typography>
                    </Box>
                    <Box sx={{ flex: 1, textAlign: 'right' }}>
                      <Typography variant="body2" fontWeight="bold">Prix HT</Typography>
                    </Box>
                    <Box sx={{ flex: 1, textAlign: 'right' }}>
                      <Typography variant="body2" fontWeight="bold">Total HT</Typography>
                    </Box>
                  </Box>

                  {/* Table rows */}
                  {[1, 2, 3].map((item) => (
                    <Box key={item} sx={{
                      display: 'flex',
                      p: 1,
                      borderBottom: `1px solid ${theme.palette.divider}`
                    }}>
                      <Box sx={{ flex: 3 }}>
                        <Typography variant="body2">Produit/Service {item}</Typography>
                      </Box>
                      <Box sx={{ flex: 1, textAlign: 'center' }}>
                        <Typography variant="body2">1</Typography>
                      </Box>
                      <Box sx={{ flex: 1, textAlign: 'right' }}>
                        <Typography variant="body2">{(item * 100).toFixed(2)} €</Typography>
                      </Box>
                      <Box sx={{ flex: 1, textAlign: 'right' }}>
                        <Typography variant="body2">{(item * 100).toFixed(2)} €</Typography>
                      </Box>
                    </Box>
                  ))}

                  {/* Totals */}
                  <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                    <Box sx={{ display: 'flex', width: '200px', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Total HT:</Typography>
                      <Typography variant="body2">600.00 €</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', width: '200px', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">TVA (20%):</Typography>
                      <Typography variant="body2">120.00 €</Typography>
                    </Box>
                    <Box sx={{
                      display: 'flex',
                      width: '200px',
                      justifyContent: 'space-between',
                      p: 1,
                      bgcolor: selectedColor,
                      color: '#fff',
                      borderRadius: 1
                    }}>
                      <Typography variant="body1" fontWeight="bold">Total TTC:</Typography>
                      <Typography variant="body1" fontWeight="bold">720.00 €</Typography>
                    </Box>
                  </Box>
                </Box>
              </Paper>

              <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                <Button
                  variant="outlined"
                  startIcon={<VisibilityIcon />}
                  onClick={() => {
                    // Ouvrir l'aperçu complet dans une nouvelle fenêtre ou modal
                    console.log('Aperçu complet du template', { templateName, templateType, selectedColor });
                    // Vous pouvez ajouter ici une fonction pour ouvrir un aperçu plus grand
                    window.open(`/preview?type=${templateType}&color=${encodeURIComponent(selectedColor)}&name=${encodeURIComponent(templateName)}`, '_blank');
                  }}
                >
                  Aperçu complet
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => {
                    // Ouvrir l'interface de personnalisation avancée
                    console.log('Personnalisation avancée du template', { templateName, templateType, selectedColor });
                    // Vous pouvez ajouter ici une fonction pour ouvrir un éditeur plus avancé
                    const templateData = {
                      name: templateName,
                      type: templateType.toLowerCase(),
                      layout: 'standard'
                    };
                    // Stocker temporairement les données du template en cours d'édition
                    localStorage.setItem('editingTemplate', JSON.stringify(templateData));
                    window.open(`/template-editor?id=${template?._id || template?.id || 'new'}`, '_blank');
                  }}
                >
                  Personnaliser
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{
        p: 2,
        bgcolor: theme.palette.background.default,
        borderTop: `1px solid ${theme.palette.divider}`,
        flexDirection: 'column',
        alignItems: 'stretch'
      }}>
        {error && (
          <Box sx={{ mb: 2, p: 1, bgcolor: 'error.light', color: 'error.dark', borderRadius: 1 }}>
            <Typography variant="body2">{error}</Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button onClick={onClose} disabled={saving}>Annuler</Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={saving}
            startIcon={saving ? <CircularProgress size={20} color="inherit" /> : null}
            sx={{
              bgcolor: selectedColor,
              '&:hover': {
                bgcolor: selectedColor,
                filter: 'brightness(0.9)'
              }
            }}
          >
            {saving ? 'Enregistrement...' : 'Continuer'}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateCustomizer;
