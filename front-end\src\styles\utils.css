/* ==========================================================================
   Advanced Utility Styles
   ========================================================================== */

/* Custom Scrollbars */
.custom-scrollbar {
  scrollbar-width: thin;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Gradient Text */
.gradient-text-primary {
  background: linear-gradient(90deg, #3a6ea5, #4f83b9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-secondary {
  background: linear-gradient(90deg, #ff6b6b, #ff8c8c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Avatar Customization */
.avatar-bordered {
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .avatar-bordered {
  border-color: #1e2430;
}

/* Card Enhancements */
.card-hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.06);
}

.dark .card-hover-effect:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
}

/* Text Truncation */
.truncate-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Blur Effects */
.backdrop-blur {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Glass Effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(30, 36, 48, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Custom Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

.slide-left {
  animation: slideLeft 0.3s ease-in-out;
}

.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Print Optimizations */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    color: #000;
    background: #fff;
  }
  
  a {
    text-decoration: none !important;
    color: #000 !important;
  }
  
  .page-break {
    page-break-before: always;
  }
}

/* Status Colors */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.75rem;
}

.status-paid {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.status-pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.status-draft {
  background-color: rgba(160, 174, 192, 0.1);
  color: #a0aec0;
}

.status-late {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.dark .status-paid {
  background-color: rgba(46, 204, 113, 0.2);
}

.dark .status-pending {
  background-color: rgba(243, 156, 18, 0.2);
}

.dark .status-draft {
  background-color: rgba(160, 174, 192, 0.2);
}

.dark .status-late {
  background-color: rgba(231, 76, 60, 0.2);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .hide-xs {
    display: none !important;
  }
}

@media (min-width: 641px) and (max-width: 960px) {
  .hide-sm {
    display: none !important;
  }
}

@media (min-width: 961px) and (max-width: 1280px) {
  .hide-md {
    display: none !important;
  }
}

@media (min-width: 1281px) {
  .hide-lg {
    display: none !important;
  }
}