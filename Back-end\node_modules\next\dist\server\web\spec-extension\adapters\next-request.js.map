{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/next-request.ts"], "sourcesContent": ["import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n"], "names": ["NextRequestAdapter", "ResponseAborted", "ResponseAbortedName", "createAbortController", "signalFromNodeResponse", "Error", "name", "response", "controller", "AbortController", "once", "writableFinished", "abort", "errored", "destroyed", "AbortSignal", "signal", "fromBaseNextRequest", "request", "process", "env", "NEXT_RUNTIME", "isWebNextRequest", "fromWebNextRequest", "isNodeNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "getRequestMeta", "NextRequest", "headers", "fromNodeOutgoingHttpHeaders", "duplex", "aborted"], "mappings": ";;;;;;;;;;;;;;;;;;IAuDaA,kBAAkB;eAAlBA;;IA5CAC,eAAe;eAAfA;;IADAC,mBAAmB;eAAnBA;;IAYGC,qBAAqB;eAArBA;;IAuBAC,sBAAsB;eAAtBA;;;6BAxCe;uBACa;yBAChB;yBACwB;AAE7C,MAAMF,sBAAsB;AAC5B,MAAMD,wBAAwBI;;QAA9B,qBACWC,OAAOJ;;AACzB;AASO,SAASC,sBAAsBI,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIX;IACvB;IAEA,OAAOO;AACT;AAUO,SAASJ,uBAAuBG,QAAkB;IACvD,MAAM,EAAEM,OAAO,EAAEC,SAAS,EAAE,GAAGP;IAC/B,IAAIM,WAAWC,WAAW;QACxB,OAAOC,YAAYH,KAAK,CAACC,WAAW,IAAIZ;IAC1C;IAEA,MAAM,EAAEe,MAAM,EAAE,GAAGb,sBAAsBI;IACzC,OAAOS;AACT;AAEO,MAAMhB;IACX,OAAciB,oBACZC,OAAwB,EACxBF,MAAmB,EACN;QACb,IACE,qEAAqE;QACrE,6DAA6D;QAC7DG,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAACJ,UACjB;YACA,OAAOlB,mBAAmBuB,kBAAkB,CAACL;QAC/C,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BG,IAAAA,0BAAiB,EAACN,UAClB;YACA,OAAOlB,mBAAmByB,mBAAmB,CAACP,SAASF;QACzD,OAAO;YACL,MAAM,qBAAoD,CAApD,IAAIX,MAAM,4CAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D;IACF;IAEA,OAAcoB,oBACZP,OAAwB,EACxBF,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIU,OAAwB;QAC5B,IAAIR,QAAQS,MAAM,KAAK,SAAST,QAAQS,MAAM,KAAK,UAAUT,QAAQQ,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAOR,QAAQQ,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIV,QAAQU,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIZ,QAAQU,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,OAAOC,IAAAA,2BAAc,EAACd,SAAS;YACrC,IAAI,CAACa,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIZ,QAAQU,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIZ,QAAQU,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,IAAIE,oBAAW,CAACL,KAAK;YAC1BD,QAAQT,QAAQS,MAAM;YACtBO,SAASC,IAAAA,kCAA2B,EAACjB,QAAQgB,OAAO;YACpDE,QAAQ;YACRpB;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOqB,OAAO,GACd,CAAC,IACD;gBACEX;YACF,CAAC;QACP;IACF;IAEA,OAAcH,mBAAmBL,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIQ,OAA8B;QAClC,IAAIR,QAAQS,MAAM,KAAK,SAAST,QAAQS,MAAM,KAAK,QAAQ;YACzDD,OAAOR,QAAQQ,IAAI;QACrB;QAEA,OAAO,IAAIO,oBAAW,CAACf,QAAQU,GAAG,EAAE;YAClCD,QAAQT,QAAQS,MAAM;YACtBO,SAASC,IAAAA,kCAA2B,EAACjB,QAAQgB,OAAO;YACpDE,QAAQ;YACRpB,QAAQE,QAAQA,OAAO,CAACF,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIE,QAAQA,OAAO,CAACF,MAAM,CAACqB,OAAO,GAC9B,CAAC,IACD;gBACEX;YACF,CAAC;QACP;IACF;AACF"}