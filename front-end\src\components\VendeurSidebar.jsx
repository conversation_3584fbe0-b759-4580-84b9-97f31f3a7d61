import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  useTheme,
  styled,
  IconButton,
  Tooltip,
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ReceiptIcon from '@mui/icons-material/Receipt';
import DescriptionIcon from '@mui/icons-material/Description';
import PeopleIcon from '@mui/icons-material/People';
import InventoryIcon from '@mui/icons-material/Inventory';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import MenuIcon from '@mui/icons-material/Menu';
import PaymentIcon from '@mui/icons-material/Payment';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import BusinessIcon from '@mui/icons-material/Business';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import { useMediaQuery } from '@mui/material';

// Styled components
const drawerWidth = 260;
const miniDrawerWidth = 70;

const StyledDrawer = styled(Drawer)(({ theme, mini }) => ({
  width: mini ? miniDrawerWidth : drawerWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  overflowX: 'hidden',
  overflowY: 'auto', // Enable scrolling
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  '& .MuiDrawer-paper': {
    width: mini ? miniDrawerWidth : drawerWidth,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    borderRight: 'none',
    boxShadow: theme.palette.mode === 'dark' ? 'none' : '0 4px 20px rgba(0, 0, 0, 0.05)',
    overflowX: 'hidden',
    overflowY: 'auto', // Enable scrolling
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    // Custom scrollbar styling
    '&::-webkit-scrollbar': {
      width: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: 'transparent',
    },
    '&::-webkit-scrollbar-thumb': {
      background: theme.palette.mode === 'dark'
        ? 'rgba(255, 255, 255, 0.2)'
        : 'rgba(0, 0, 0, 0.2)',
      borderRadius: '3px',
      transition: 'background 0.2s ease',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: theme.palette.mode === 'dark'
        ? 'rgba(255, 255, 255, 0.3)'
        : 'rgba(0, 0, 0, 0.3)',
    },
    scrollbarWidth: 'thin',
    scrollbarColor: theme.palette.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.2) transparent'
      : 'rgba(0, 0, 0, 0.2) transparent',
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(1.8, 2),
  minHeight: '60px',
  borderBottom: `1px solid ${theme.palette.divider}`,
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(to right, rgba(58, 110, 165, 0.1), rgba(42, 80, 128, 0.05))'
    : 'linear-gradient(to right, rgba(58, 110, 165, 0.05), rgba(42, 80, 128, 0.02))',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  fontWeight: 700,
  textTransform: 'uppercase',
  letterSpacing: '0.8px',
  color: theme.palette.mode === 'dark'
    ? theme.palette.primary.light
    : theme.palette.primary.dark,
  padding: theme.spacing(2.5, 2.5, 1),
  opacity: 0.85,
  marginTop: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: theme.spacing(2.5),
    width: '30px',
    height: '2px',
    backgroundColor: theme.palette.primary.main,
    opacity: 0.5,
    borderRadius: '2px',
  }
}));

// Utiliser isActive au lieu de active pour éviter les avertissements React
const StyledListItemButton = styled(ListItemButton)(({ theme, isActive }) => ({
  borderRadius: 12,
  margin: theme.spacing(0.5, 1.5),
  padding: theme.spacing(1.2, 2),
  backgroundColor: isActive ?
    (theme.palette.mode === 'dark'
      ? `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.18)`
      : `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.1)`)
    : 'transparent',
  transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: isActive ?
    (theme.palette.mode === 'dark'
      ? '0 2px 8px rgba(0, 0, 0, 0.15)'
      : '0 2px 8px rgba(58, 110, 165, 0.08)')
    : 'none',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: isActive ? '4px' : '0px',
    backgroundColor: theme.palette.primary.main,
    borderRadius: '4px',
    transition: 'width 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  '&:hover': {
    backgroundColor: isActive
      ? (theme.palette.mode === 'dark'
        ? `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.25)`
        : `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.15)`)
      : theme.palette.action.hover,
    transform: 'translateX(3px)',
    boxShadow: isActive ?
      (theme.palette.mode === 'dark'
        ? '0 4px 12px rgba(0, 0, 0, 0.2)'
        : '0 4px 12px rgba(58, 110, 165, 0.12)')
      : 'none',
    '&::before': {
      width: '4px',
    },
  },
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme, isActive }) => ({
  minWidth: 40,
  color: isActive ? theme.palette.primary.main : theme.palette.text.secondary,
  '& .MuiSvgIcon-root': {
    fontSize: '1.3rem',
    transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
    transform: isActive ? 'scale(1.15)' : 'scale(1)',
    filter: isActive ? `drop-shadow(0 2px 4px ${theme.palette.mode === 'dark'
      ? 'rgba(0, 0, 0, 0.3)'
      : 'rgba(58, 110, 165, 0.2)'})` : 'none',
  }
}));

const StyledListItemText = styled(ListItemText)(({ theme, isActive }) => ({
  '& .MuiTypography-root': {
    fontWeight: isActive ? 600 : 500,
    fontSize: '0.95rem',
    color: isActive ? theme.palette.text.primary : theme.palette.text.secondary,
    transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
    letterSpacing: '0.01em',
    position: 'relative',
    display: 'inline-block',
  },
}));

const VendeurSidebar = () => {
  const theme = useTheme();
  const location = useLocation();
  const [mini, setMini] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const isActive = (path) => {
    return location.pathname === path;
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMiniDrawerToggle = () => {
    setMini(!mini);
  };

  const drawer = (
    <div>
      <LogoContainer>
        <IconButton
          onClick={handleMiniDrawerToggle}
          sx={{
            mr: 1.5,
            color: theme.palette.primary.main,
            backgroundColor: theme.palette.mode === 'dark'
              ? 'rgba(58, 110, 165, 0.1)'
              : 'rgba(58, 110, 165, 0.05)',
            border: `1px solid ${theme.palette.mode === 'dark'
              ? 'rgba(58, 110, 165, 0.2)'
              : 'rgba(58, 110, 165, 0.1)'}`,
            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(58, 110, 165, 0.2)'
                : 'rgba(58, 110, 165, 0.1)',
              transform: 'translateY(-1px)',
              boxShadow: '0 3px 8px rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          {mini ? <MenuIcon /> : <ChevronLeftIcon />}
        </IconButton>
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            color: "#fff",
            width: 36,
            height: 36,
            boxShadow: "0 4px 12px rgba(58, 110, 165, 0.25)",
            mr: 1.5,
            border: '1.5px solid white',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'scale(1.05)',
              boxShadow: "0 6px 16px rgba(58, 110, 165, 0.35)",
            },
            display: mini ? 'none' : 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #3a6ea5, #4f83b9)',
          }}
        >
          <BusinessIcon sx={{
            fontSize: '1.2rem',
            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15))'
          }} />
        </Avatar>
        {!mini && (
          <Box>
            <Typography variant="subtitle1" component="div" sx={{
              fontWeight: 700,
              fontSize: '0.95rem',
              color: theme.palette.primary.main,
              letterSpacing: '0.01em',
              position: 'relative',
              display: 'inline-block',
              mb: 0.1,
            }}>
              Gestion Vendeur
            </Typography>
            <Typography variant="caption" sx={{
              color: theme.palette.text.secondary,
              fontSize: '0.7rem',
              display: 'block',
              mt: 0,
              fontWeight: 500,
              letterSpacing: '0.01em',
              opacity: 0.75,
            }}>
              Pilotage & Facturation
            </Typography>
          </Box>
        )}
      </LogoContainer>

      <Box>
        <List component="nav" sx={{ p: mini ? 1 : 2 }}>
          {!mini && <SectionTitle>Principal</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Tableau de bord" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/analytics"
                isActive={isActive("/vendeur/analytics")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3,
                  background: 'linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(162, 155, 254, 0.1) 100%)',
                  borderLeft: '4px solid var(--primary-color)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, rgba(108, 92, 231, 0.2) 0%, rgba(162, 155, 254, 0.2) 100%)',
                  }
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/analytics")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <DashboardIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Tableau de bord" isActive={isActive("/vendeur/analytics")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && <SectionTitle>Documents</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Factures" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/factures"
                isActive={isActive("/vendeur/factures")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/factures")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <ReceiptIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Factures" isActive={isActive("/vendeur/factures")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Devis" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/devis"
                isActive={isActive("/vendeur/devis")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/devis")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <DescriptionIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Devis" isActive={isActive("/vendeur/devis")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Bons de Livraison" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/bon-livraisons"
                isActive={isActive("/vendeur/bon-livraisons")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/bon-livraisons")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <LocalShippingIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Bons de Livraison" isActive={isActive("/vendeur/bon-livraisons")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && <SectionTitle>Gestion</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Clients" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/clients"
                isActive={isActive("/vendeur/clients")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/clients")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <PeopleIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Clients" isActive={isActive("/vendeur/clients")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Produits" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/produits"
                isActive={isActive("/vendeur/produits")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/produits")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <InventoryIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Produits" isActive={isActive("/vendeur/produits")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Paiements" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/paiements"
                isActive={isActive("/vendeur/paiements")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/paiements")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <PaymentIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Paiements" isActive={isActive("/vendeur/paiements")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && <SectionTitle>Compte</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Mon Profil" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/vendeur/profile"
                isActive={isActive("/vendeur/profile")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  isActive={isActive("/vendeur/profile")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <AccountCircleIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Mon Profil" isActive={isActive("/vendeur/profile")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>
        </List>
      </Box>
    </div>
  );

  return (
    <>
      {isMobile ? (
        <>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              position: "fixed",
              top: 12,
              left: 12,
              zIndex: 1300,
              backgroundColor: theme.palette.background.paper,
              boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
              display: { md: "none" },
            }}
          >
            <MenuIcon />
          </IconButton>
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: "block", md: "none" },
              "& .MuiDrawer-paper": {
                width: drawerWidth,
                backgroundColor: theme.palette.background.paper,
                color: theme.palette.text.primary,
                overflowY: 'auto', // Enable scrolling on mobile too
                // Custom scrollbar styling for mobile
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                  background: 'transparent',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.2)'
                    : 'rgba(0, 0, 0, 0.2)',
                  borderRadius: '3px',
                },
                scrollbarWidth: 'thin',
                scrollbarColor: theme.palette.mode === 'dark'
                  ? 'rgba(255, 255, 255, 0.2) transparent'
                  : 'rgba(0, 0, 0, 0.2) transparent',
              },
            }}
          >
            {drawer}
          </Drawer>
        </>
      ) : (
        <StyledDrawer variant="permanent" mini={mini}>
          {drawer}
        </StyledDrawer>
      )}
    </>
  )
}

export default VendeurSidebar;
