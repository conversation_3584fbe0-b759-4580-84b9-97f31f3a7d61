import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Grid,
  Divider,
  useTheme,
  Tooltip,
  Card,
  CardContent,
  InputAdornment,
  Avatar,
  Stack,
  Badge,
  LinearProgress,
  alpha,
  useMediaQuery,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Search as SearchIcon,
  AccountBalance as BankIcon,
  CreditCard as CardIcon,
  Money as CashIcon,
  CheckCircle as CheckCircleIcon,
  PendingActions as PendingIcon,
  Cancel as CancelIcon,
  FilterList as FilterListIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  AttachMoney as MoneyIcon,
  ReceiptLong as ReceiptLongIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
}  from '@mui/icons-material';
import { motion } from 'framer-motion';
import factureService from '../services/factureService';
import paiementService from '../services/paiementService';
import { formatCurrency, formatDate, formatStatut } from '../utils/formatters';

// Animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Fonctions utilitaires pour les statuts et modes de paiement
const getStatusColor = (status) => {
  switch (status) {
    case 'PAID':
      return 'success';
    case 'SENT':
      return 'warning';
    case 'DRAFT':
      return 'info';
    case 'CANCELED':
      return 'error';
    default:
      return 'default';
  }
};

const getStatusIcon = (status) => {
  switch (status) {
    case 'PAID':
      return <CheckCircleIcon fontSize="small" />;
    case 'SENT':
      return <PendingIcon fontSize="small" />;
    case 'DRAFT':
      return <InfoIcon fontSize="small" />;
    case 'CANCELED':
      return <CancelIcon fontSize="small" />;
    default:
      return null;
  }
};

const getPaymentModeIcon = (mode) => {
  switch (mode) {
    case 'BANK_TRANSFER':
      return <BankIcon fontSize="small" />;
    case 'CHECK':
      return <ReceiptIcon fontSize="small" />;
    case 'CASH':
      return <CashIcon fontSize="small" />;
    default:
      return <PaymentIcon fontSize="small" />;
  }
};

const getPaymentModeLabel = (mode) => {
  switch (mode) {
    case 'BANK_TRANSFER':
      return 'Virement bancaire';
    case 'CHECK':
      return 'Chèque';
    case 'CASH':
      return 'Espèces';
    default:
      return mode;
  }
};

const EntreprisePaiements = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(true);
  const [factures, setFactures] = useState([]);
  const [filteredFactures, setFilteredFactures] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modeFilter, setModeFilter] = useState('all');
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [selectedFacture, setSelectedFacture] = useState(null);
  const [paymentData, setPaymentData] = useState({
    montant: 0,
    modePaiement: 'BANK_TRANSFER',
    reference: '',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Statistiques
  const [stats, setStats] = useState({
    totalFactures: 0,
    totalMontant: 0,
    facturesPaid: 0,
    facturesPending: 0,
    montantPaid: 0,
    montantPending: 0,
    recentPayments: []
  });

  // Fonction pour calculer les statistiques
  const calculateStats = (facturesData) => {
    const totalFactures = facturesData.length;
    const facturesPaid = facturesData.filter(f => f.statut === 'PAID').length;
    const facturesPending = facturesData.filter(f => f.statut === 'SENT').length;

    const totalMontant = facturesData.reduce((sum, f) => sum + f.total, 0);
    const montantPaid = facturesData
      .filter(f => f.statut === 'PAID')
      .reduce((sum, f) => sum + f.total, 0);
    const montantPending = facturesData
      .filter(f => f.statut === 'SENT')
      .reduce((sum, f) => sum + f.total, 0);

    // Récupérer les 5 derniers paiements (factures payées)
    const recentPayments = facturesData
      .filter(f => f.statut === 'PAID')
      .sort((a, b) => new Date(b.dateEmission) - new Date(a.dateEmission))
      .slice(0, 5);

    setStats({
      totalFactures,
      totalMontant,
      facturesPaid,
      facturesPending,
      montantPaid,
      montantPending,
      recentPayments
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching data for enterprise user:', currentUser);

        // Récupérer les factures et les paiements en parallèle
        const [facturesData, paiementsData] = await Promise.all([
          factureService.getFactures(),
          paiementService.getPaiements()
        ]);

        console.log('Fetched invoices:', facturesData.length);
        console.log('Fetched payments:', paiementsData.length);

        // Afficher les statuts des factures récupérées pour le débogage
        const statusCounts = {};
        facturesData.forEach(facture => {
          statusCounts[facture.statut] = (statusCounts[facture.statut] || 0) + 1;
        });
        console.log('Invoice status counts:', statusCounts);

        // Afficher les détails des factures avec le statut "SENT"
        const sentInvoices = facturesData.filter(f => f.statut === 'SENT');
        console.log(`Found ${sentInvoices.length} invoices with status SENT:`, sentInvoices);

        // Pas besoin de filtrer côté client car l'API renvoie déjà les factures filtrées
        setFactures(facturesData);
        setFilteredFactures(facturesData);

        // Calculer les statistiques
        calculateStats(facturesData);

        // Mettre à jour les factures payées avec les informations de paiement
        if (paiementsData.length > 0) {
          // Créer un mapping des paiements par ID de facture
          const paiementsByFactureId = {};
          paiementsData.forEach(paiement => {
            if (paiement.factureId && paiement.factureId._id) {
              paiementsByFactureId[paiement.factureId._id] = paiement;
            }
          });

          // Mettre à jour les factures avec les informations de paiement
          const updatedFactures = facturesData.map(facture => {
            const paiement = paiementsByFactureId[facture._id];
            if (paiement && facture.statut === 'PAID') {
              return {
                ...facture,
                modePaiement: paiement.modePaiement,
                datePaiement: paiement.datePaiement,
                reference: paiement.reference
              };
            }
            return facture;
          });

          setFactures(updatedFactures);
          setFilteredFactures(updatedFactures);
          calculateStats(updatedFactures);
        }

        setLoading(false);
      } catch (error) {
        console.error('Erreur lors de la récupération des données:', error);
        setLoading(false);
      }
    };

    if (currentUser) {
      fetchData();
    }
  }, [currentUser]);

  useEffect(() => {
    filterFactures();
  }, [factures, searchTerm, statusFilter, modeFilter]);

  const filterFactures = () => {
    let filtered = [...factures];

    console.log("Filtering factures, total count:", factures.length);

    // Compter les factures par statut avant filtrage
    const statusCountsBefore = {};
    factures.forEach(facture => {
      statusCountsBefore[facture.statut] = (statusCountsBefore[facture.statut] || 0) + 1;
    });
    console.log("Status counts before filtering:", statusCountsBefore);

    // Filtrer par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(facture =>
        facture.numero.toLowerCase().includes(searchTerm.toLowerCase())
      );
      console.log(`After search term filter "${searchTerm}":`, filtered.length);
    }

    // Filtrer par statut
    if (statusFilter !== 'all') {
      console.log(`Filtering by status: ${statusFilter}`);
      filtered = filtered.filter(facture => facture.statut === statusFilter);
      console.log(`After status filter:`, filtered.length);
    }

    // Filtrer par mode de paiement (pour les factures payées)
    if (modeFilter !== 'all' && statusFilter === 'PAID') {
      filtered = filtered.filter(facture =>
        facture.modePaiement === modeFilter
      );
      console.log(`After payment mode filter "${modeFilter}":`, filtered.length);
    }

    // Compter les factures par statut après filtrage
    const statusCountsAfter = {};
    filtered.forEach(facture => {
      statusCountsAfter[facture.statut] = (statusCountsAfter[facture.statut] || 0) + 1;
    });
    console.log("Status counts after filtering:", statusCountsAfter);

    setFilteredFactures(filtered);
  };

  const handleViewFacture = (id) => {
    navigate(`/entreprise/factures/${id}`);
  };

  const handleOpenPaymentDialog = (facture) => {
    setSelectedFacture(facture);
    setPaymentData({
      montant: facture.total,
      modePaiement: 'BANK_TRANSFER',
      reference: '',
    });
    setOpenPaymentDialog(true);
  };

  const handleClosePaymentDialog = () => {
    setOpenPaymentDialog(false);
    setSelectedFacture(null);
    setError('');
  };

  const handlePaymentChange = (e) => {
    const { name, value } = e.target;
    setPaymentData(prev => ({
      ...prev,
      [name]: name === 'montant' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSubmitPayment = async () => {
    try {
      if (!selectedFacture) return;

      if (paymentData.montant <= 0) {
        setError('Le montant doit être supérieur à 0');
        return;
      }

      // Envoyer les données de paiement à l'API
      await paiementService.payerFacture(selectedFacture._id, {
        factureId: selectedFacture._id,
        ...paymentData,
        datePaiement: new Date()
      });

      // La mise à jour du statut de la facture est gérée par le backend

      // Rafraîchir les factures avec le nouveau statut et le mode de paiement
      const updatedFactures = factures.map(f =>
        f._id === selectedFacture._id ? {
          ...f,
          statut: 'PAID',
          modePaiement: paymentData.modePaiement
        } : f
      );

      setFactures(updatedFactures);

      // Recalculer les statistiques
      calculateStats(updatedFactures);

      setSuccess(`Paiement de ${formatCurrency(paymentData.montant)} enregistré avec succès.`);
      handleClosePaymentDialog();

      // Effacer le message de succès après 5 secondes
      setTimeout(() => {
        setSuccess('');
      }, 5000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du paiement:', error);
      setError('Une erreur est survenue lors de l\'enregistrement du paiement.');
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* En-tête avec design amélioré */}
      <Card
        elevation={3}
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          borderRadius: 2,
          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
          position: 'relative',
          overflow: 'hidden',
          borderLeft: '4px solid',
          borderColor: 'primary.main'
        }}
      >
        <CardContent sx={{ p: 3, position: 'relative', zIndex: 2 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}>
            <Box>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 0.5,
                  letterSpacing: 0.7
                }}
              >
                GESTION DES PAIEMENTS
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600
                }}
              >
                Consultez et effectuez vos paiements pour les factures en attente.
              </Typography>
            </Box>
            <Stack direction="row" spacing={1}>
              <Tooltip title="Télécharger l'historique">
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<DownloadIcon />}
                  sx={{ borderRadius: 2 }}
                >
                  Exporter
                </Button>
              </Tooltip>
            </Stack>
          </Box>
        </CardContent>
      </Card>

      {/* Messages de succès/erreur */}
      {success && (
        <Alert
          severity="success"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 2px 10px rgba(0,0,0,0.08)'
          }}
          onClose={() => setSuccess('')}
        >
          {success}
        </Alert>
      )}

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            component={motion.div}
            variants={itemVariants}
            elevation={2}
            sx={{
              borderRadius: 2,
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Total des factures
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalFactures}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: 'primary.main' }}>
                  <ReceiptLongIcon />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Montant total:
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {formatCurrency(stats.totalMontant)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            component={motion.div}
            variants={itemVariants}
            elevation={2}
            sx={{
              borderRadius: 2,
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Factures payées
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {stats.facturesPaid}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: 'success.main' }}>
                  <CheckCircleIcon />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Montant payé:
                </Typography>
                <Typography variant="body1" fontWeight="medium" color="success.main">
                  {formatCurrency(stats.montantPaid)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            component={motion.div}
            variants={itemVariants}
            elevation={2}
            sx={{
              borderRadius: 2,
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Factures en attente
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {stats.facturesPending}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: 'warning.main' }}>
                  <PendingIcon />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Montant à payer:
                </Typography>
                <Typography variant="body1" fontWeight="medium" color="warning.main">
                  {formatCurrency(stats.montantPending)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            component={motion.div}
            variants={itemVariants}
            elevation={2}
            sx={{
              borderRadius: 2,
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
              }
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Taux de paiement
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    {stats.totalFactures ? Math.round((stats.facturesPaid / stats.totalFactures) * 100) : 0}%
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: 'info.main' }}>
                  <MoneyIcon />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={stats.totalFactures ? (stats.facturesPaid / stats.totalFactures) * 100 : 0}
                  color="info"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filtres améliorés */}
      <Paper
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: 'white',
          boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
        }} />

        <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
          <FilterListIcon sx={{ mr: 1 }} fontSize="small" />
          Filtrer les factures
        </Typography>

        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              placeholder="Rechercher par numéro de facture..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  transition: 'box-shadow 0.3s',
                  '&:hover': {
                    boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.1)'
                  },
                  '&.Mui-focused': {
                    boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)'
                  }
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="small" variant="outlined">
              <InputLabel id="status-filter-label">Statut</InputLabel>
              <Select
                labelId="status-filter-label"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Statut"
                sx={{
                  borderRadius: 2,
                  '& .MuiOutlinedInput-notchedOutline': {
                    transition: 'border-color 0.3s'
                  }
                }}
              >
                <MenuItem value="all">Tous les statuts</MenuItem>
                <MenuItem value="DRAFT">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <InfoIcon fontSize="small" color="info" sx={{ mr: 1 }} />
                    Brouillon
                  </Box>
                </MenuItem>
                <MenuItem value="SENT">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PendingIcon fontSize="small" color="warning" sx={{ mr: 1 }} />
                    En attente
                  </Box>
                </MenuItem>
                <MenuItem value="PAID">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CheckCircleIcon fontSize="small" color="success" sx={{ mr: 1 }} />
                    Payée
                  </Box>
                </MenuItem>
                <MenuItem value="CANCELED">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CancelIcon fontSize="small" color="error" sx={{ mr: 1 }} />
                    Annulée
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="small" variant="outlined">
              <InputLabel id="mode-filter-label">Mode de paiement</InputLabel>
              <Select
                labelId="mode-filter-label"
                value={modeFilter}
                onChange={(e) => setModeFilter(e.target.value)}
                label="Mode de paiement"
                sx={{
                  borderRadius: 2,
                  '& .MuiOutlinedInput-notchedOutline': {
                    transition: 'border-color 0.3s'
                  }
                }}
              >
                <MenuItem value="all">Tous les modes</MenuItem>
                <MenuItem value="BANK_TRANSFER">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BankIcon fontSize="small" sx={{ mr: 1 }} />
                    Virement bancaire
                  </Box>
                </MenuItem>
                <MenuItem value="CHECK">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ReceiptIcon fontSize="small" sx={{ mr: 1 }} />
                    Chèque
                  </Box>
                </MenuItem>
                <MenuItem value="CASH">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CashIcon fontSize="small" sx={{ mr: 1 }} />
                    Espèces
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Tableau des factures amélioré */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          mb: 4
        }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid rgba(0,0,0,0.08)' }}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
            <ReceiptLongIcon sx={{ mr: 1 }} fontSize="small" />
            Liste des factures
          </Typography>
          <Box>
            <Chip
              label={`${filteredFactures.length} facture${filteredFactures.length > 1 ? 's' : ''}`}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ borderRadius: 1 }}
            />
          </Box>
        </Box>

        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow sx={{
                '& .MuiTableCell-head': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                  fontWeight: 'bold'
                }
              }}>
                <TableCell>Numéro de facture</TableCell>
                <TableCell>Date d'émission</TableCell>
                <TableCell>Montant</TableCell>
                <TableCell>Statut</TableCell>
                <TableCell>Mode de paiement</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredFactures.length > 0 ? (
                filteredFactures.map((facture) => (
                  <TableRow
                    key={facture._id}
                    hover
                    sx={{
                      transition: 'background-color 0.2s',
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.03),
                        cursor: 'pointer'
                      }
                    }}
                  >
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {facture.numero}
                      </Typography>
                    </TableCell>
                    <TableCell>{formatDate(facture.dateEmission)}</TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(facture.total)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(facture.statut)}
                        label={formatStatut(facture.statut)}
                        color={getStatusColor(facture.statut)}
                        size="small"
                        sx={{
                          fontWeight: 500,
                          borderRadius: '4px',
                          '& .MuiChip-icon': {
                            fontSize: '0.875rem',
                            marginLeft: '4px'
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      {facture.statut === 'PAID' ? (
                        <Chip
                          icon={getPaymentModeIcon(facture.modePaiement || 'BANK_TRANSFER')}
                          label={getPaymentModeLabel(facture.modePaiement || 'BANK_TRANSFER')}
                          size="small"
                          variant="outlined"
                          sx={{
                            borderRadius: '4px',
                            '& .MuiChip-icon': {
                              fontSize: '0.875rem',
                              marginLeft: '4px'
                            }
                          }}
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary">-</Typography>
                      )}
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Tooltip title="Voir la facture">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleViewFacture(facture._id)}
                            sx={{
                              backgroundColor: alpha(theme.palette.primary.main, 0.1),
                              mr: 1,
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                              }
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        {facture.statut === 'SENT' && (
                          <Tooltip title="Effectuer un paiement">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleOpenPaymentDialog(facture)}
                              sx={{
                                backgroundColor: alpha(theme.palette.success.main, 0.1),
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.success.main, 0.2),
                                }
                              }}
                            >
                              <PaymentIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
                      <ReceiptLongIcon sx={{ fontSize: 60, color: alpha(theme.palette.text.secondary, 0.2), mb: 2 }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        Aucune facture trouvée
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Modifiez vos critères de recherche ou consultez toutes les factures.
                      </Typography>

                      {statusFilter === 'SENT' && (
                        <Alert
                          severity="info"
                          sx={{
                            mt: 2,
                            maxWidth: 500,
                            borderRadius: 2,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                          }}
                        >
                          <Typography variant="subtitle2" fontWeight="bold">
                            Aucune facture avec le statut "Envoyée" n'a été trouvée
                          </Typography>
                          <Typography variant="body2">
                            Vérifiez que vous avez bien des factures avec le statut "Envoyée" dans votre système.
                            Si vous venez de créer une facture, assurez-vous qu'elle a bien le statut "SENT" et non "DRAFT".
                          </Typography>
                        </Alert>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Dialogue de paiement amélioré */}
      <Dialog
        open={openPaymentDialog}
        onClose={handleClosePaymentDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }
        }}
      >
        <Box sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: 2,
          px: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <PaymentIcon />
          <Typography variant="h6" component="div">
            Effectuer un paiement
          </Typography>
        </Box>

        <DialogContent sx={{ px: 3, py: 4 }}>
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                boxShadow: '0 2px 8px rgba(211, 47, 47, 0.2)'
              }}
            >
              {error}
            </Alert>
          )}

          <Box sx={{ mb: 3 }}>
            <Card
              elevation={0}
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
              }}
            >
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Facture #{selectedFacture?.numero}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Date d'émission
                  </Typography>
                  <Typography variant="body1">
                    {selectedFacture && formatDate(selectedFacture.dateEmission)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Montant total
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {selectedFacture && formatCurrency(selectedFacture.total)}
                  </Typography>
                </Grid>
              </Grid>
            </Card>
          </Box>

          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Détails du paiement
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                name="montant"
                label="Montant"
                type="number"
                value={paymentData.montant}
                onChange={handlePaymentChange}
                fullWidth
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <MoneyIcon fontSize="small" color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">DT</InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    transition: 'box-shadow 0.3s',
                    '&:hover': {
                      boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.1)'
                    },
                    '&.Mui-focused': {
                      boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)'
                    }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel id="payment-mode-label">Mode de paiement</InputLabel>
                <Select
                  labelId="payment-mode-label"
                  name="modePaiement"
                  value={paymentData.modePaiement}
                  onChange={handlePaymentChange}
                  label="Mode de paiement"
                  sx={{
                    borderRadius: 2,
                    '& .MuiOutlinedInput-notchedOutline': {
                      transition: 'border-color 0.3s'
                    }
                  }}
                >
                  <MenuItem value="BANK_TRANSFER">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <BankIcon fontSize="small" sx={{ mr: 1 }} />
                      Virement bancaire
                    </Box>
                  </MenuItem>
                  <MenuItem value="CHECK">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ReceiptIcon fontSize="small" sx={{ mr: 1 }} />
                      Chèque
                    </Box>
                  </MenuItem>
                  <MenuItem value="CASH">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CashIcon fontSize="small" sx={{ mr: 1 }} />
                      Espèces
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                name="reference"
                label="Référence du paiement"
                value={paymentData.reference}
                onChange={handlePaymentChange}
                fullWidth
                placeholder="Ex: Numéro de transaction, référence du virement..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <ReceiptLongIcon fontSize="small" color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    transition: 'box-shadow 0.3s',
                    '&:hover': {
                      boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.1)'
                    },
                    '&.Mui-focused': {
                      boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)'
                    }
                  }
                }}
              />
            </Grid>
          </Grid>

          <Box sx={{ mt: 4 }}>
            <Card
              elevation={0}
              sx={{
                bgcolor: alpha(theme.palette.info.main, 0.05),
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`
              }}
            >
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon fontSize="small" color="info" sx={{ mr: 1 }} />
                Informations de paiement
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Bénéficiaire
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    Nom de l'entreprise
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Banque
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    Banque Exemple
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    IBAN
                  </Typography>
                  <Typography variant="body2" fontWeight="medium" sx={{ wordBreak: 'break-all' }}>
                    FR76 1234 5678 9012 3456 7890 123
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    BIC
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    ABCDEFGHIJK
                  </Typography>
                </Grid>
              </Grid>

              <Box sx={{ mt: 2, p: 1.5, bgcolor: alpha(theme.palette.warning.main, 0.1), borderRadius: 1 }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                  <WarningIcon fontSize="small" color="warning" sx={{ mr: 1 }} />
                  Veuillez indiquer le numéro de facture <strong>{selectedFacture?.numero}</strong> dans le libellé du virement.
                </Typography>
              </Box>
            </Card>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, bgcolor: alpha(theme.palette.background.default, 0.5) }}>
          <Button
            onClick={handleClosePaymentDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3
            }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmitPayment}
            startIcon={<PaymentIcon />}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3,
              boxShadow: '0 4px 10px rgba(25, 118, 210, 0.25)'
            }}
          >
            Confirmer le paiement
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EntreprisePaiements;
