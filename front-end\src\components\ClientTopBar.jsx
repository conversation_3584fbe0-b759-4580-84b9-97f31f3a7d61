import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  styled,
  useTheme,
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LogoutIcon from '@mui/icons-material/Logout';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import ApartmentIcon from '@mui/icons-material/Apartment';

// Styled components
const ActionButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.text.secondary,
  backgroundColor: theme.palette.background.paper,
  borderRadius: '50%',
  padding: theme.spacing(1),
  marginLeft: theme.spacing(1),
  boxShadow: '0 2px 5px rgba(0,0,0,0.05)',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const ClientTopBar = ({ title, darkMode, toggleDarkMode, user }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
    handleMenuClose();
  };

  const handleProfileClick = () => {
    // Check if user is an entreprise and navigate accordingly
    const isEntreprise = user && user.role === 'ENTREPRISE';
    navigate(isEntreprise ? "/entreprise/profile" : "/client/profile");
    handleMenuClose();
  };

  const getUserInitials = () => {
    if (!user || !user.nom) return "U";
    return user.nom.charAt(0).toUpperCase();
  };

  return (
    <AppBar
      position="sticky"
      elevation={0}
      sx={{
        backgroundColor: theme.palette.background.paper,
        color: theme.palette.text.primary,
        borderBottom: `1px solid ${theme.palette.divider}`,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {user && user.role === 'ENTREPRISE' && !title && (
            <Avatar
              sx={{
                bgcolor: theme.palette.primary.main,
                color: "#fff",
                width: 32,
                height: 32,
                mr: 1.5,
                boxShadow: "0 2px 8px rgba(58, 110, 165, 0.2)",
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <ApartmentIcon sx={{ fontSize: '1.2rem' }} />
            </Avatar>
          )}
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {title || (user && user.role === 'ENTREPRISE' ? "Gestion Entreprise" :
                      (user && user.role === 'CLIENT' ? "Espace Client" : "Portail"))}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>


          <Tooltip title="Mode sombre">
            <ActionButton onClick={toggleDarkMode} size="medium">
              {darkMode ? <Brightness7Icon fontSize="small" /> : <Brightness4Icon fontSize="small" />}
            </ActionButton>
          </Tooltip>

          <Tooltip title="Profil">
            <ActionButton
              onClick={handleProfileMenuOpen}
              size="medium"
              sx={{ ml: 1 }}
            >
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  backgroundColor: theme.palette.primary.main,
                  color: '#fff',
                  fontSize: '0.9rem',
                  fontWeight: 600,
                }}
              >
                {getUserInitials()}
              </Avatar>
            </ActionButton>
          </Tooltip>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              elevation: 3,
              sx: {
                minWidth: 200,
                borderRadius: 2,
                mt: 1.5,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              },
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={handleProfileClick} sx={{ py: 1.5 }}>
              <ListItemIcon>
                <AccountCircleIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Mon profil" />
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ py: 1.5 }}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Déconnexion" />
            </MenuItem>
          </Menu>


        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default ClientTopBar;
