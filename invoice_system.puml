@startuml InvoiceSystemClassDiagram
!theme plain

' Enumerations
enum UserRole {
    ADMIN
    COMPANY_MANAGER
    SELLER
    CLIENT
}

enum InvoiceStatus {
    DRAFT
    SENT
    PAID
    CANCELED
    ACCEPTED
    REJECTED
    PARTIALLY_PAID
}

enum QuoteStatus {
    DRAFT
    WAITING_APPROVAL
    APPROVED_INTERNAL
    SENT
    PENDING
    ACCEPTED
    REJECTED
    EXPIRED
}

enum PaymentMethod {
    CHECK
    BANK_TRANSFER
    CASH
}

enum PaymentStatus {
    PENDING
    COMPLETED
    FAILED
    REFUNDED
}

enum SubscriptionStatus {
    ACTIVE
    SUSPENDED
    EXPIRED
    CANCELED
}

enum SubscriptionDuration {
    TEN_MINUTES
    ONE_DAY
    SEVEN_DAYS
    THREE_MONTHS
    SIX_MONTHS
    ONE_YEAR
}

enum ProductCategory {
    SERVICE
    PHYSICAL_PRODUCT
    SOFTWARE
    SUBSCRIPTION
    TRAINING
    UNCATEGORIZED
}

enum DeliveryStatus {
    IN_PREPARATION
    IN_PROGRESS
    DELIVERED
    PARTIALLY_DELIVERED
    FAILED
    CANCELED
    RETURNED
}

' Main Entities
class User {
    - firstName: String
    - lastName: String
    - email: String
    - password: String
    - role: UserRole
    - phone: String
    - address: String
    - contact: String
    - nationalId: String
    - profileImage: String
    - creationDate: Date
    - createdBy: ObjectId
    - activeSubscription: Boolean
    - subscriptionId: ObjectId
    - companyId: ObjectId
    - resetOTP: String
    - resetOTPExpires: Date
    - companyName: String
    - companyAddress: String
    - companyPhone: String
    - taxNumber: String
    --
    + createUser(userData: User): Boolean
    + updateUser(userData: User): Boolean
    + getUser(id: String): User
    + deleteUser(id: String): Boolean
    + authenticate(email: String, password: String): Boolean
    + resetPassword(email: String): Boolean
    + updateProfile(profileData: Object): Boolean
    + createCompanyManagerAccount(data: CompanyManager): Boolean
    + assignSubscription(managerId: String, subscriptionId: String): Boolean
    + reactivateAccount(userId: String): Boolean
    + checkSubscriptionStatus(): List<CompanyManager>
    + generateReport(type: String, parameters: Object): Report
    + configureSystem(settings: Object): Boolean
    + createQuote(): Boolean
    + createInvoice(): Boolean
    + manageClients(): Boolean
    + recordPayment(): Boolean
    + generateReports(): Boolean
}

class Company {
    - name: String
    - legalName: String
    - taxNumber: String
    - vatNumber: String
    - address: String
    - country: String
    - phone: String
    - email: String
    - website: String
    - contactName: String
    - contactEmail: String
    - contactPhone: String
    - logo: String
    - companyManagerId: ObjectId
    - createdBy: ObjectId
    - createdAt: Date
    --
    + createCompany(companyData: Company): Boolean
    + updateCompany(companyData: Company): Boolean
    + getCompanyByManager(managerId: String): Company
    + uploadLogo(logoFile: File): Boolean
    + manageTeam(): Boolean
    + viewAnalytics(): Object
    + manageSubscription(): Boolean
}

class Client {
    - name: String
    - address: String
    - email: String
    - contact: String
    - phone: String
    - nationalId: String
    - logo: String
    - sellerId: ObjectId
    - companyManagers: ObjectId[]
    - createdAt: Date
    - updatedAt: Date
    --
    + createClient(clientData: Client): Boolean
    + updateClient(clientData: Client): Boolean
    + getClient(id: String): Client
    + deleteClient(id: String): Boolean
    + assignToSeller(sellerId: String): Boolean
    + assignToCompanyManager(managerId: String): Boolean
    + viewInvoices(): Invoice[]
    + viewQuotes(): Quote[]
    + makePayment(paymentData: Payment): Boolean
    + downloadDocument(documentId: String): File
}

class Product {
    - name: String
    - description: String
    - detailedDescription: String
    - price: Number
    - category: ProductCategory
    - unit: String
    - stockManagement: Boolean
    - stockQuantity: Number
    - alertThreshold: Number
    - imageUrl: String
    - statistics: Object
    - companyManagerId: ObjectId
    - createdAt: Date
    - updatedAt: Date
    --
    + createProduct(productData: Product): Boolean
    + updateProduct(productData: Product): Boolean
    + updatePrice(newPrice: Number): Boolean
    + updateStock(newQuantity: Number): Boolean
    + checkStockLevel(): Boolean
    + calculateSalesStats(): Object
}

class Invoice {
    - number: String
    - clientId: ObjectId
    - sellerId: ObjectId
    - companyManagerId: ObjectId
    - issueDate: Date
    - lines: LineSchema[]
    - notes: String
    - status: InvoiceStatus
    - total: Number
    - totalExcludingTax: Number
    - taxAmount: Number
    - taxRate: Number
    - quoteId: ObjectId
    - stockAdjusted: Boolean
    - createdAt: Date
    --
    + createInvoice(invoiceData: Invoice): Boolean
    + updateInvoice(invoiceData: Invoice): Boolean
    + calculateTax(): Number
    + calculateTotal(): Number
    + generatePDF(): String
    + sendByEmail(email: String): Boolean
    + convertFromQuote(quoteId: String): Boolean
    + adjustStock(): Boolean
}

class Quote {
    - number: String
    - clientId: ObjectId
    - sellerId: ObjectId
    - companyManagerId: ObjectId
    - creationDate: Date
    - validityDate: Date
    - lines: LineSchema[]
    - notes: String
    - status: QuoteStatus
    - total: Number
    - totalExcludingTax: Number
    - totalIncludingTax: Number
    - taxRate: Number
    - managerValidation: Object
    - clientResponse: Object
    - isClientRequest: Boolean
    - createdAt: Date
    --
    + createQuote(quoteData: Quote): Boolean
    + updateQuote(quoteData: Quote): Boolean
    + calculateTax(): Number
    + calculateTotal(): Number
    + generatePDF(): String
    + sendByEmail(email: String): Boolean
    + convertToInvoice(): Invoice
    + approveByManager(): Boolean
    + rejectByManager(reason: String): Boolean
}

class Payment {
    - amount: Number
    - paymentMethod: PaymentMethod
    - paymentDate: Date
    - invoiceId: ObjectId
    - reference: String
    - notes: String
    - status: PaymentStatus
    - createdBy: ObjectId
    - createdAt: Date
    --
    + createPayment(paymentData: Payment): Boolean
    + updatePayment(paymentData: Payment): Boolean
    + recordPayment(): Boolean
    + validatePayment(): Boolean
    + generateReceipt(): String
}

class Subscription {
    - companyId: ObjectId
    - companyManagerId: ObjectId
    - startDate: Date
    - endDate: Date
    - duration: SubscriptionDuration
    - status: SubscriptionStatus
    - enabledFeatures: Object
    - notes: String
    - createdAt: Date
    --
    + createSubscription(subscriptionData: Subscription): Boolean
    + updateSubscription(subscriptionData: Subscription): Boolean
    + isActive(): Boolean
    + renewSubscription(duration: SubscriptionDuration): Boolean
    + suspend(): Boolean
    + calculateRemainingDays(): Number
    + checkExpiration(): Boolean
}

class BaseTemplate {
    - name: String
    - type: String
    - layout: String
    - isActive: Boolean
    - createdAt: Date
    --
    + createTemplate(templateData: BaseTemplate): Boolean
    + updateTemplate(templateData: BaseTemplate): Boolean
    + activateTemplate(): Boolean
    + deactivateTemplate(): Boolean
}

class CompanyTemplate {
    - companyId: ObjectId
    - invoiceTemplate: Object
    - quoteTemplate: Object
    - createdAt: Date
    - updatedAt: Date
    --
    + createCustomization(customizationData: CompanyTemplate): Boolean
    + updateCustomization(customizationData: CompanyTemplate): Boolean
    + applyTemplate(templateId: String, type: String): Boolean
    + uploadLogo(logoFile: File): Boolean
}

class Parameters {
    - defaultCurrency: String
    - defaultTaxRate: Number
    - emailSignature: String
    - invoicePrefix: String
    - quotePrefix: String
    - paymentDelay: Number
    - createdAt: Date
    - updatedAt: Date
    --
    + updateParameters(parameterData: Parameters): Boolean
    + getSystemParameters(): Parameters
    + resetToDefaults(): Boolean
}

class DeliveryPerson {
    - firstName: String
    - lastName: String
    - phone: String
    - email: String
    - nationalId: String
    - address: String
    - vehicle: Object
    - status: String
    - available: Boolean
    - statistics: Object
    - companyManagerId: ObjectId
    - notes: String
    - creationDate: Date
    - updateDate: Date
    --
    + createDeliveryPerson(deliveryPersonData: DeliveryPerson): Boolean
    + updateDeliveryPerson(deliveryPersonData: DeliveryPerson): Boolean
    + updateAvailability(available: Boolean): Boolean
    + updateStatistics(deliveryResult: String): Boolean
    + calculateSuccessRate(): Number
}

class DeliveryNote {
    - number: String
    - clientId: ObjectId
    - deliveryPersonId: ObjectId
    - companyManagerId: ObjectId
    - deliveryDate: Date
    - departureTime: String
    - arrivalTime: String
    - lines: DeliveryLineSchema[]
    - status: DeliveryStatus
    - deliveryAddress: Object
    - notes: String
    - creationDate: Date
    --
    + createDeliveryNote(deliveryData: DeliveryNote): Boolean
    + updateDeliveryNote(deliveryData: DeliveryNote): Boolean
    + assignDeliveryPerson(deliveryPersonId: String): Boolean
    + updateStatus(newStatus: DeliveryStatus): Boolean
    + generatePDF(): String
    + sendByEmail(email: String): Boolean
    + calculateDeliveryDuration(): Number
}

class Report {
    - name: String
    - type: String
    - generatedBy: ObjectId
    - data: Object
    - format: String
    - createdAt: Date
    --
    + generateReport(reportData: Report): Boolean
    + exportReport(format: String): File
    + scheduleReport(schedule: Object): Boolean
}

' Relationships with Cardinalities

' User relationships
User ||--|| Company : "1:1 (COMPANY_MANAGER role)"
User ||--o{ User : "1:N (creates/manages)"
User ||--o| Subscription : "1:0..1 (has)"
User ||--o{ Report : "1:N (generates)"

' Company relationships
Company ||--o{ Client : "1:N (manages)"
Company ||--o{ Product : "1:N (owns)"
Company ||--o{ Invoice : "1:N (generates)"
Company ||--o{ Quote : "1:N (creates)"
Company ||--o{ DeliveryPerson : "1:N (manages)"
Company ||--o{ DeliveryNote : "1:N (creates)"
Company ||--|| CompanyTemplate : "1:1 (customizes)"

' Client relationships
Client ||--o{ Invoice : "1:N (receives)"
Client ||--o{ Quote : "1:N (receives)"
Client }o--o{ User : "N:M (managed by sellers/managers)"

' Business document relationships
Quote ||--o| Invoice : "1:0..1 (converts to)"
Invoice ||--o{ Payment : "1:N (receives)"

' Product relationships
Product }o--|| ProductCategory : "N:1 (categorized)"

' Delivery relationships
DeliveryPerson ||--o{ DeliveryNote : "1:N (assigned to)"
Client ||--o{ DeliveryNote : "1:N (receives)"

' Template relationships
BaseTemplate ||--o{ CompanyTemplate : "1:N (customized by)"

' Subscription relationships
Subscription }o--|| SubscriptionStatus : "N:1"
Subscription }o--|| SubscriptionDuration : "N:1"

' Enum relationships
User }o--|| UserRole : "N:1"
Invoice }o--|| InvoiceStatus : "N:1"
Quote }o--|| QuoteStatus : "N:1"
Payment }o--|| PaymentMethod : "N:1"
Payment }o--|| PaymentStatus : "N:1"
DeliveryNote }o--|| DeliveryStatus : "N:1"

@enduml
