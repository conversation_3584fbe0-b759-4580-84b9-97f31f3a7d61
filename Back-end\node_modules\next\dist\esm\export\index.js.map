{"version": 3, "sources": ["../../src/export/index.ts"], "sourcesContent": ["import type {\n  ExportAppResult,\n  ExportAppOptions,\n  WorkerRenderOptsPartial,\n} from './types'\nimport { createStaticWorker, type PrerenderManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\n\nimport { bold, yellow } from '../lib/picocolors'\nimport findUp from 'next/dist/compiled/find-up'\nimport { existsSync, promises as fs } from 'fs'\n\nimport '../server/require-hook'\n\nimport { dirname, join, resolve, sep, relative } from 'path'\nimport { formatAmpMessages } from '../build/output/index'\nimport type { AmpPageStatus } from '../build/output/index'\nimport * as Log from '../build/output/log'\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n  SSG_FALLBACK_EXPORT_ERROR,\n} from '../lib/constants'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport {\n  BUILD_ID_FILE,\n  CLIENT_PUBLIC_FILES_PATH,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  NEXT_FONT_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_EXPORT,\n  PRERENDER_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_REFERENCE_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  ROUTES_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport loadConfig from '../server/config'\nimport type { ExportPathMap } from '../server/config-shared'\nimport { eventCliSession } from '../telemetry/events'\nimport { hasNextSupport } from '../server/ci-info'\nimport { Telemetry } from '../telemetry/storage'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { loadEnvConfig } from '@next/env'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { getPagePath } from '../server/require'\nimport type { Span } from '../trace'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport isError from '../lib/is-error'\nimport { formatManifest } from '../build/manifests/formatter/format-manifest'\nimport { TurborepoAccessTraceResult } from '../build/turborepo-access-trace'\nimport { createProgress } from '../build/progress'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ActionManifest } from '../build/webpack/plugins/flight-client-entry-plugin'\nimport { extractInfoFromServerReferenceId } from '../shared/lib/server-reference-info'\nimport { convertSegmentPathToStaticExportFilename } from '../shared/lib/segment-cache/segment-value-encoding'\n\nexport class ExportError extends Error {\n  code = 'NEXT_EXPORT_ERROR'\n}\n\nasync function exportAppImpl(\n  dir: string,\n  options: Readonly<ExportAppOptions>,\n  span: Span\n): Promise<ExportAppResult | null> {\n  dir = resolve(dir)\n\n  // attempt to load global env values so they are available in next.config.js\n  span.traceChild('load-dotenv').traceFn(() => loadEnvConfig(dir, false, Log))\n\n  const { enabledDirectories } = options\n\n  const nextConfig =\n    options.nextConfig ||\n    (await span\n      .traceChild('load-next-config')\n      .traceAsyncFn(() => loadConfig(PHASE_EXPORT, dir)))\n\n  const distDir = join(dir, nextConfig.distDir)\n  const telemetry = options.buildExport ? null : new Telemetry({ distDir })\n\n  if (telemetry) {\n    telemetry.record(\n      eventCliSession(distDir, nextConfig, {\n        webpackVersion: null,\n        cliCommand: 'export',\n        isSrcDir: null,\n        hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n        isCustomServer: null,\n        turboFlag: false,\n        pagesDir: null,\n        appDir: null,\n      })\n    )\n  }\n\n  const subFolders = nextConfig.trailingSlash && !options.buildExport\n\n  if (!options.silent && !options.buildExport) {\n    Log.info(`using build directory: ${distDir}`)\n  }\n\n  const buildIdFile = join(distDir, BUILD_ID_FILE)\n\n  if (!existsSync(buildIdFile)) {\n    throw new ExportError(\n      `Could not find a production build in the '${distDir}' directory. Try building your app with 'next build' before starting the static export. https://nextjs.org/docs/messages/next-export-no-build-id`\n    )\n  }\n\n  const customRoutes = ['rewrites', 'redirects', 'headers'].filter(\n    (config) => typeof nextConfig[config] === 'function'\n  )\n\n  if (!hasNextSupport && !options.buildExport && customRoutes.length > 0) {\n    Log.warn(\n      `rewrites, redirects, and headers are not applied when exporting your application, detected (${customRoutes.join(\n        ', '\n      )}). See more info here: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  const buildId = await fs.readFile(buildIdFile, 'utf8')\n\n  const pagesManifest =\n    !options.pages &&\n    (require(join(distDir, SERVER_DIRECTORY, PAGES_MANIFEST)) as PagesManifest)\n\n  let prerenderManifest: DeepReadonly<PrerenderManifest> | undefined\n  try {\n    prerenderManifest = require(join(distDir, PRERENDER_MANIFEST))\n  } catch {}\n\n  let appRoutePathManifest: Record<string, string> | undefined\n  try {\n    appRoutePathManifest = require(join(distDir, APP_PATH_ROUTES_MANIFEST))\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'MODULE_NOT_FOUND')\n    ) {\n      // the manifest doesn't exist which will happen when using\n      // \"pages\" dir instead of \"app\" dir.\n      appRoutePathManifest = undefined\n    } else {\n      // the manifest is malformed (invalid json)\n      throw err\n    }\n  }\n\n  const excludedPrerenderRoutes = new Set<string>()\n  const pages = options.pages || Object.keys(pagesManifest)\n  const defaultPathMap: ExportPathMap = {}\n\n  let hasApiRoutes = false\n  for (const page of pages) {\n    // _document and _app are not real pages\n    // _error is exported as 404.html later on\n    // API Routes are Node.js functions\n\n    if (isAPIRoute(page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    if (page === '/_document' || page === '/_app' || page === '/_error') {\n      continue\n    }\n\n    // iSSG pages that are dynamic should not export templated version by\n    // default. In most cases, this would never work. There is no server that\n    // could run `getStaticProps`. If users make their page work lazily, they\n    // can manually add it to the `exportPathMap`.\n    if (prerenderManifest?.dynamicRoutes[page]) {\n      excludedPrerenderRoutes.add(page)\n      continue\n    }\n\n    defaultPathMap[page] = { page }\n  }\n\n  const mapAppRouteToPage = new Map<string, string>()\n  if (!options.buildExport && appRoutePathManifest) {\n    for (const [pageName, routePath] of Object.entries(appRoutePathManifest)) {\n      mapAppRouteToPage.set(routePath, pageName)\n      if (\n        isAppPageRoute(pageName) &&\n        !prerenderManifest?.routes[routePath] &&\n        !prerenderManifest?.dynamicRoutes[routePath]\n      ) {\n        defaultPathMap[routePath] = {\n          page: pageName,\n          _isAppDir: true,\n        }\n      }\n    }\n  }\n\n  // Initialize the output directory\n  const outDir = options.outdir\n\n  if (outDir === join(dir, 'public')) {\n    throw new ExportError(\n      `The 'public' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-public`\n    )\n  }\n\n  if (outDir === join(dir, 'static')) {\n    throw new ExportError(\n      `The 'static' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-static`\n    )\n  }\n\n  await fs.rm(outDir, { recursive: true, force: true })\n  await fs.mkdir(join(outDir, '_next', buildId), { recursive: true })\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: false,\n    }),\n    'utf8'\n  )\n\n  // Copy static directory\n  if (!options.buildExport && existsSync(join(dir, 'static'))) {\n    if (!options.silent) {\n      Log.info('Copying \"static\" directory')\n    }\n    await span\n      .traceChild('copy-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(join(dir, 'static'), join(outDir, 'static'))\n      )\n  }\n\n  // Copy .next/static directory\n  if (\n    !options.buildExport &&\n    existsSync(join(distDir, CLIENT_STATIC_FILES_PATH))\n  ) {\n    if (!options.silent) {\n      Log.info('Copying \"static build\" directory')\n    }\n    await span\n      .traceChild('copy-next-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(\n          join(distDir, CLIENT_STATIC_FILES_PATH),\n          join(outDir, '_next', CLIENT_STATIC_FILES_PATH)\n        )\n      )\n  }\n\n  // Get the exportPathMap from the config file\n  if (typeof nextConfig.exportPathMap !== 'function') {\n    nextConfig.exportPathMap = async (defaultMap) => {\n      return defaultMap\n    }\n  }\n\n  const {\n    i18n,\n    images: { loader = 'default', unoptimized },\n  } = nextConfig\n\n  if (i18n && !options.buildExport) {\n    throw new ExportError(\n      `i18n support is not compatible with next export. See here for more info on deploying: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  if (!options.buildExport) {\n    const { isNextImageImported } = await span\n      .traceChild('is-next-image-imported')\n      .traceAsyncFn(() =>\n        fs\n          .readFile(join(distDir, EXPORT_MARKER), 'utf8')\n          .then((text) => JSON.parse(text))\n          .catch(() => ({}))\n      )\n\n    if (\n      isNextImageImported &&\n      loader === 'default' &&\n      !unoptimized &&\n      !hasNextSupport\n    ) {\n      throw new ExportError(\n        `Image Optimization using the default loader is not compatible with export.\n  Possible solutions:\n    - Use \\`next start\\` to run a server, which includes the Image Optimization API.\n    - Configure \\`images.unoptimized = true\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n  }\n\n  let serverActionsManifest: ActionManifest | undefined\n  if (enabledDirectories.app) {\n    serverActionsManifest = require(\n      join(distDir, SERVER_DIRECTORY, SERVER_REFERENCE_MANIFEST + '.json')\n    ) as ActionManifest\n\n    if (nextConfig.output === 'export') {\n      const routesManifest = require(join(distDir, ROUTES_MANIFEST))\n\n      // We already prevent rewrites earlier in the process, however Next.js will insert rewrites\n      // for interception routes so we need to check for that here.\n      if (routesManifest?.rewrites?.beforeFiles?.length > 0) {\n        const hasInterceptionRouteRewrite =\n          routesManifest.rewrites.beforeFiles.some(isInterceptionRouteRewrite)\n\n        if (hasInterceptionRouteRewrite) {\n          throw new ExportError(\n            `Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n          )\n        }\n      }\n\n      const actionIds = [\n        ...Object.keys(serverActionsManifest.node),\n        ...Object.keys(serverActionsManifest.edge),\n      ]\n\n      if (\n        actionIds.some(\n          (actionId) =>\n            extractInfoFromServerReferenceId(actionId).type === 'server-action'\n        )\n      ) {\n        throw new ExportError(\n          `Server Actions are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n        )\n      }\n    }\n  }\n\n  // Start the rendering process\n  const renderOpts: WorkerRenderOptsPartial = {\n    previewProps: prerenderManifest?.preview,\n    nextExport: true,\n    assetPrefix: nextConfig.assetPrefix.replace(/\\/$/, ''),\n    distDir,\n    dev: false,\n    basePath: nextConfig.basePath,\n    trailingSlash: nextConfig.trailingSlash,\n    canonicalBase: nextConfig.amp?.canonicalBase || '',\n    ampSkipValidation: nextConfig.experimental.amp?.skipValidation || false,\n    ampOptimizerConfig: nextConfig.experimental.amp?.optimizer || undefined,\n    locales: i18n?.locales,\n    locale: i18n?.defaultLocale,\n    defaultLocale: i18n?.defaultLocale,\n    domainLocales: i18n?.domains,\n    disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n    // Exported pages do not currently support dynamic HTML.\n    supportsDynamicResponse: false,\n    crossOrigin: nextConfig.crossOrigin,\n    optimizeCss: nextConfig.experimental.optimizeCss,\n    nextConfigOutput: nextConfig.output,\n    nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n    largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n    serverActions: nextConfig.experimental.serverActions,\n    serverComponents: enabledDirectories.app,\n    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n    nextFontManifest: require(\n      join(distDir, 'server', `${NEXT_FONT_MANIFEST}.json`)\n    ),\n    images: nextConfig.images,\n    ...(enabledDirectories.app\n      ? {\n          serverActionsManifest,\n        }\n      : {}),\n    strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n    deploymentId: nextConfig.deploymentId,\n    htmlLimitedBots: nextConfig.htmlLimitedBots.source,\n    experimental: {\n      clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      expireTime: nextConfig.expireTime,\n      dynamicIO: nextConfig.experimental.dynamicIO ?? false,\n      clientSegmentCache:\n        nextConfig.experimental.clientSegmentCache === 'client-only'\n          ? 'client-only'\n          : Boolean(nextConfig.experimental.clientSegmentCache),\n      dynamicOnHover: nextConfig.experimental.dynamicOnHover ?? false,\n      inlineCss: nextConfig.experimental.inlineCss ?? false,\n      authInterrupts: !!nextConfig.experimental.authInterrupts,\n    },\n    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n  }\n\n  const { publicRuntimeConfig } = nextConfig\n\n  if (Object.keys(publicRuntimeConfig).length > 0) {\n    renderOpts.runtimeConfig = publicRuntimeConfig\n  }\n\n  // We need this for server rendering the Link component.\n  ;(globalThis as any).__NEXT_DATA__ = {\n    nextExport: true,\n  }\n\n  const exportPathMap = await span\n    .traceChild('run-export-path-map')\n    .traceAsyncFn(async () => {\n      const exportMap = await nextConfig.exportPathMap(defaultPathMap, {\n        dev: false,\n        dir,\n        outDir,\n        distDir,\n        buildId,\n      })\n      return exportMap\n    })\n\n  // only add missing 404 page when `buildExport` is false\n  if (!options.buildExport) {\n    // only add missing /404 if not specified in `exportPathMap`\n    if (!exportPathMap['/404']) {\n      exportPathMap['/404'] = { page: '/_error' }\n    }\n\n    /**\n     * exports 404.html for backwards compat\n     * E.g. GitHub Pages, GitLab Pages, Cloudflare Pages, Netlify\n     */\n    if (!exportPathMap['/404.html']) {\n      // alias /404.html to /404 to be compatible with custom 404 / _error page\n      exportPathMap['/404.html'] = exportPathMap['/404']\n    }\n  }\n\n  // make sure to prevent duplicates\n  const exportPaths = [\n    ...new Set(\n      Object.keys(exportPathMap).map((path) =>\n        denormalizePagePath(normalizePagePath(path))\n      )\n    ),\n  ]\n\n  const filteredPaths = exportPaths.filter(\n    (route) =>\n      exportPathMap[route]._isAppDir ||\n      // Remove API routes\n      !isAPIRoute(exportPathMap[route].page)\n  )\n\n  if (filteredPaths.length !== exportPaths.length) {\n    hasApiRoutes = true\n  }\n\n  if (filteredPaths.length === 0) {\n    return null\n  }\n\n  if (prerenderManifest && !options.buildExport) {\n    const fallbackEnabledPages = new Set()\n\n    for (const path of Object.keys(exportPathMap)) {\n      const page = exportPathMap[path].page\n      const prerenderInfo = prerenderManifest.dynamicRoutes[page]\n\n      if (prerenderInfo && prerenderInfo.fallback !== false) {\n        fallbackEnabledPages.add(page)\n      }\n    }\n\n    if (fallbackEnabledPages.size > 0) {\n      throw new ExportError(\n        `Found pages with \\`fallback\\` enabled:\\n${[\n          ...fallbackEnabledPages,\n        ].join('\\n')}\\n${SSG_FALLBACK_EXPORT_ERROR}\\n`\n      )\n    }\n  }\n  let hasMiddleware = false\n\n  if (!options.buildExport) {\n    try {\n      const middlewareManifest = require(\n        join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      ) as MiddlewareManifest\n\n      const functionsConfigManifest = require(\n        join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST)\n      )\n\n      hasMiddleware =\n        Object.keys(middlewareManifest.middleware).length > 0 ||\n        Boolean(functionsConfigManifest.functions?.['/_middleware'])\n    } catch {}\n\n    // Warn if the user defines a path for an API page\n    if (hasApiRoutes || hasMiddleware) {\n      if (nextConfig.output === 'export') {\n        Log.warn(\n          yellow(\n            `Statically exporting a Next.js application via \\`next export\\` disables API routes and middleware.`\n          ) +\n            `\\n` +\n            yellow(\n              `This command is meant for static-only hosts, and is` +\n                ' ' +\n                bold(`not necessary to make your application static.`)\n            ) +\n            `\\n` +\n            yellow(\n              `Pages in your application without server-side data dependencies will be automatically statically exported by \\`next build\\`, including pages powered by \\`getStaticProps\\`.`\n            ) +\n            `\\n` +\n            yellow(\n              `Learn more: https://nextjs.org/docs/messages/api-routes-static-export`\n            )\n        )\n      }\n    }\n  }\n\n  const pagesDataDir = options.buildExport\n    ? outDir\n    : join(outDir, '_next/data', buildId)\n\n  const ampValidations: AmpPageStatus = {}\n\n  const publicDir = join(dir, CLIENT_PUBLIC_FILES_PATH)\n  // Copy public directory\n  if (!options.buildExport && existsSync(publicDir)) {\n    if (!options.silent) {\n      Log.info('Copying \"public\" directory')\n    }\n    await span.traceChild('copy-public-directory').traceAsyncFn(() =>\n      recursiveCopy(publicDir, outDir, {\n        filter(path) {\n          // Exclude paths used by pages\n          return !exportPathMap[path]\n        },\n      })\n    )\n  }\n\n  const failedExportAttemptsByPage: Map<string, boolean> = new Map()\n\n  // Chunk filtered pages into smaller groups, and call the export worker on each group.\n  // We've set a default minimum of 25 pages per chunk to ensure that even setups\n  // with only a few static pages can leverage a shared incremental cache, however this\n  // value can be configured.\n  const minChunkSize =\n    nextConfig.experimental.staticGenerationMinPagesPerWorker ?? 25\n  // Calculate the number of workers needed to ensure each chunk has at least minChunkSize pages\n  const numWorkers = Math.min(\n    options.numWorkers,\n    Math.ceil(filteredPaths.length / minChunkSize)\n  )\n  // Calculate the chunk size based on the number of workers\n  const chunkSize = Math.ceil(filteredPaths.length / numWorkers)\n  const chunks = Array.from({ length: numWorkers }, (_, i) =>\n    filteredPaths.slice(i * chunkSize, (i + 1) * chunkSize)\n  )\n  // Distribute remaining pages\n  const remainingPages = filteredPaths.slice(numWorkers * chunkSize)\n  remainingPages.forEach((page, index) => {\n    chunks[index % chunks.length].push(page)\n  })\n\n  const progress = createProgress(\n    filteredPaths.length,\n    options.statusMessage || 'Exporting'\n  )\n\n  const worker = createStaticWorker(nextConfig, progress)\n\n  const results = (\n    await Promise.all(\n      chunks.map((paths) =>\n        worker.exportPages({\n          buildId,\n          paths,\n          exportPathMap,\n          parentSpanId: span.getId(),\n          pagesDataDir,\n          renderOpts,\n          options,\n          dir,\n          distDir,\n          outDir,\n          nextConfig,\n          cacheHandler: nextConfig.cacheHandler,\n          cacheMaxMemorySize: nextConfig.cacheMaxMemorySize,\n          fetchCache: true,\n          fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n        })\n      )\n    )\n  ).flat()\n\n  let hadValidationError = false\n\n  const collector: ExportAppResult = {\n    byPath: new Map(),\n    byPage: new Map(),\n    ssgNotFoundPaths: new Set(),\n    turborepoAccessTraceResults: new Map(),\n  }\n\n  for (const { result, path, pageKey } of results) {\n    if (!result) continue\n    if ('error' in result) {\n      failedExportAttemptsByPage.set(pageKey, true)\n      continue\n    }\n\n    const { page } = exportPathMap[path]\n\n    if (result.turborepoAccessTraceResult) {\n      collector.turborepoAccessTraceResults?.set(\n        path,\n        TurborepoAccessTraceResult.fromSerialized(\n          result.turborepoAccessTraceResult\n        )\n      )\n    }\n\n    // Capture any amp validations.\n    if (result.ampValidations) {\n      for (const validation of result.ampValidations) {\n        ampValidations[validation.page] = validation.result\n        hadValidationError ||= validation.result.errors.length > 0\n      }\n    }\n\n    if (options.buildExport) {\n      // Update path info by path.\n      const info = collector.byPath.get(path) ?? {}\n      if (result.cacheControl) {\n        info.cacheControl = result.cacheControl\n      }\n      if (typeof result.metadata !== 'undefined') {\n        info.metadata = result.metadata\n      }\n\n      if (typeof result.hasEmptyPrelude !== 'undefined') {\n        info.hasEmptyPrelude = result.hasEmptyPrelude\n      }\n\n      if (typeof result.hasPostponed !== 'undefined') {\n        info.hasPostponed = result.hasPostponed\n      }\n\n      if (typeof result.fetchMetrics !== 'undefined') {\n        info.fetchMetrics = result.fetchMetrics\n      }\n\n      collector.byPath.set(path, info)\n\n      // Update not found.\n      if (result.ssgNotFound === true) {\n        collector.ssgNotFoundPaths.add(path)\n      }\n\n      // Update durations.\n      const durations = collector.byPage.get(page) ?? {\n        durationsByPath: new Map<string, number>(),\n      }\n      durations.durationsByPath.set(path, result.duration)\n      collector.byPage.set(page, durations)\n    }\n  }\n\n  // Export mode provide static outputs that are not compatible with PPR mode.\n  if (!options.buildExport && nextConfig.experimental.ppr) {\n    // TODO: add message\n    throw new Error('Invariant: PPR cannot be enabled in export mode')\n  }\n\n  // copy prerendered routes to outDir\n  if (!options.buildExport && prerenderManifest) {\n    await Promise.all(\n      Object.keys(prerenderManifest.routes).map(async (unnormalizedRoute) => {\n        const { srcRoute } = prerenderManifest!.routes[unnormalizedRoute]\n        const appPageName = mapAppRouteToPage.get(srcRoute || '')\n        const pageName = appPageName || srcRoute || unnormalizedRoute\n        const isAppPath = Boolean(appPageName)\n        const isAppRouteHandler = appPageName && isAppRouteRoute(appPageName)\n\n        // returning notFound: true from getStaticProps will not\n        // output html/json files during the build\n        if (prerenderManifest!.notFoundRoutes.includes(unnormalizedRoute)) {\n          return\n        }\n        // TODO: This rewrites /index/foo to /index/index/foo. Investigate and\n        // fix. I presume this was because normalizePagePath was designed for\n        // some other use case and then reused here for static exports without\n        // realizing the implications.\n        const route = normalizePagePath(unnormalizedRoute)\n\n        const pagePath = getPagePath(pageName, distDir, undefined, isAppPath)\n        const distPagesDir = join(\n          pagePath,\n          // strip leading / and then recurse number of nested dirs\n          // to place from base folder\n          pageName\n            .slice(1)\n            .split('/')\n            .map(() => '..')\n            .join('/')\n        )\n\n        const orig = join(distPagesDir, route)\n        const handlerSrc = `${orig}.body`\n        const handlerDest = join(outDir, route)\n\n        if (isAppRouteHandler && existsSync(handlerSrc)) {\n          await fs.mkdir(dirname(handlerDest), { recursive: true })\n          await fs.copyFile(handlerSrc, handlerDest)\n          return\n        }\n\n        const htmlDest = join(\n          outDir,\n          `${route}${\n            subFolders && route !== '/index' ? `${sep}index` : ''\n          }.html`\n        )\n        const ampHtmlDest = join(\n          outDir,\n          `${route}.amp${subFolders ? `${sep}index` : ''}.html`\n        )\n        const jsonDest = isAppPath\n          ? join(\n              outDir,\n              `${route}${\n                subFolders && route !== '/index' ? `${sep}index` : ''\n              }.txt`\n            )\n          : join(pagesDataDir, `${route}.json`)\n\n        await fs.mkdir(dirname(htmlDest), { recursive: true })\n        await fs.mkdir(dirname(jsonDest), { recursive: true })\n\n        const htmlSrc = `${orig}.html`\n        const jsonSrc = `${orig}${isAppPath ? RSC_SUFFIX : '.json'}`\n\n        await fs.copyFile(htmlSrc, htmlDest)\n        await fs.copyFile(jsonSrc, jsonDest)\n\n        if (existsSync(`${orig}.amp.html`)) {\n          await fs.mkdir(dirname(ampHtmlDest), { recursive: true })\n          await fs.copyFile(`${orig}.amp.html`, ampHtmlDest)\n        }\n\n        const segmentsDir = `${orig}${RSC_SEGMENTS_DIR_SUFFIX}`\n        if (isAppPath && existsSync(segmentsDir)) {\n          // Output a data file for each of this page's segments\n          //\n          // These files are requested by the client router's internal\n          // prefetcher, not the user directly. So we don't need to account for\n          // things like trailing slash handling.\n          //\n          // To keep the protocol simple, we can use the non-normalized route\n          // path instead of the normalized one (which, among other things,\n          // rewrites `/` to `/index`).\n          const segmentsDirDest = join(outDir, unnormalizedRoute)\n          const segmentPaths = await collectSegmentPaths(segmentsDir)\n          await Promise.all(\n            segmentPaths.map(async (segmentFileSrc) => {\n              const segmentPath =\n                '/' + segmentFileSrc.slice(0, -RSC_SEGMENT_SUFFIX.length)\n              const segmentFilename =\n                convertSegmentPathToStaticExportFilename(segmentPath)\n              const segmentFileDest = join(segmentsDirDest, segmentFilename)\n              await fs.mkdir(dirname(segmentFileDest), { recursive: true })\n              await fs.copyFile(\n                join(segmentsDir, segmentFileSrc),\n                segmentFileDest\n              )\n            })\n          )\n        }\n      })\n    )\n  }\n\n  if (Object.keys(ampValidations).length) {\n    console.log(formatAmpMessages(ampValidations))\n  }\n  if (hadValidationError) {\n    throw new ExportError(\n      `AMP Validation caused the export to fail. https://nextjs.org/docs/messages/amp-export-validation`\n    )\n  }\n\n  if (failedExportAttemptsByPage.size > 0) {\n    const failedPages = Array.from(failedExportAttemptsByPage.keys())\n    throw new ExportError(\n      `Export encountered errors on following paths:\\n\\t${failedPages\n        .sort()\n        .join('\\n\\t')}`\n    )\n  }\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: true,\n    }),\n    'utf8'\n  )\n\n  if (telemetry) {\n    await telemetry.flush()\n  }\n\n  await worker.end()\n\n  return collector\n}\n\nasync function collectSegmentPaths(segmentsDirectory: string) {\n  const results: Array<string> = []\n  await collectSegmentPathsImpl(segmentsDirectory, segmentsDirectory, results)\n  return results\n}\n\nasync function collectSegmentPathsImpl(\n  segmentsDirectory: string,\n  directory: string,\n  results: Array<string>\n) {\n  const segmentFiles = await fs.readdir(directory, {\n    withFileTypes: true,\n  })\n  await Promise.all(\n    segmentFiles.map(async (segmentFile) => {\n      if (segmentFile.isDirectory()) {\n        await collectSegmentPathsImpl(\n          segmentsDirectory,\n          join(directory, segmentFile.name),\n          results\n        )\n        return\n      }\n      if (!segmentFile.name.endsWith(RSC_SEGMENT_SUFFIX)) {\n        return\n      }\n      results.push(\n        relative(segmentsDirectory, join(directory, segmentFile.name))\n      )\n    })\n  )\n}\n\nexport default async function exportApp(\n  dir: string,\n  options: ExportAppOptions,\n  span: Span\n): Promise<ExportAppResult | null> {\n  const nextExportSpan = span.traceChild('next-export')\n\n  return nextExportSpan.traceAsyncFn(async () => {\n    return await exportAppImpl(dir, options, nextExportSpan)\n  })\n}\n"], "names": ["createStaticWorker", "bold", "yellow", "findUp", "existsSync", "promises", "fs", "dirname", "join", "resolve", "sep", "relative", "formatAmpMessages", "Log", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SUFFIX", "SSG_FALLBACK_EXPORT_ERROR", "recursiveCopy", "BUILD_ID_FILE", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_STATIC_FILES_PATH", "EXPORT_DETAIL", "EXPORT_MARKER", "NEXT_FONT_MANIFEST", "MIDDLEWARE_MANIFEST", "PAGES_MANIFEST", "PHASE_EXPORT", "PRERENDER_MANIFEST", "SERVER_DIRECTORY", "SERVER_REFERENCE_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "ROUTES_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "loadConfig", "eventCliSession", "hasNextSupport", "Telemetry", "normalizePagePath", "denormalizePagePath", "loadEnvConfig", "isAPIRoute", "getPagePath", "isAppRouteRoute", "isAppPageRoute", "isError", "formatManifest", "TurborepoAccessTraceResult", "createProgress", "isInterceptionRouteRewrite", "extractInfoFromServerReferenceId", "convertSegmentPathToStaticExportFilename", "ExportError", "Error", "code", "exportAppImpl", "dir", "options", "span", "nextConfig", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "enabledDirectories", "traceAsyncFn", "distDir", "telemetry", "buildExport", "record", "webpackVersion", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "cwd", "isCustomServer", "turboFlag", "pagesDir", "appDir", "subFolders", "trailingSlash", "silent", "info", "buildIdFile", "customRoutes", "filter", "config", "length", "warn", "buildId", "readFile", "pagesManifest", "pages", "require", "prerenderManifest", "appRoutePathManifest", "err", "undefined", "excludedPrerenderRoutes", "Set", "Object", "keys", "defaultPathMap", "hasApiRoutes", "page", "dynamicRoutes", "add", "mapAppRouteToPage", "Map", "pageName", "routePath", "entries", "set", "routes", "_isAppDir", "outDir", "outdir", "rm", "recursive", "force", "mkdir", "writeFile", "version", "outDirectory", "success", "exportPathMap", "defaultMap", "i18n", "images", "loader", "unoptimized", "isNextImageImported", "then", "text", "JSON", "parse", "catch", "serverActionsManifest", "app", "output", "routesManifest", "rewrites", "beforeFiles", "hasInterceptionRouteRewrite", "some", "actionIds", "node", "edge", "actionId", "type", "renderOpts", "previewProps", "preview", "nextExport", "assetPrefix", "replace", "dev", "basePath", "canonicalBase", "amp", "ampSkipValidation", "experimental", "skipValidation", "ampOptimizerConfig", "optimizer", "locales", "locale", "defaultLocale", "domainLocales", "domains", "disableOptimizedLoading", "supportsDynamicResponse", "crossOrigin", "optimizeCss", "nextConfigOutput", "nextScriptWorkers", "largePageDataBytes", "serverActions", "serverComponents", "cacheLifeProfiles", "cacheLife", "nextFontManifest", "strictNextHead", "deploymentId", "htmlLimitedBots", "source", "clientTraceMetadata", "expireTime", "dynamicIO", "clientSegmentCache", "Boolean", "dynamicOnHover", "inlineCss", "authInterrupts", "reactMaxHeadersLength", "publicRuntimeConfig", "runtimeConfig", "globalThis", "__NEXT_DATA__", "exportMap", "exportPaths", "map", "path", "filteredPaths", "route", "fallbackEnabledPages", "prerenderInfo", "fallback", "size", "hasMiddleware", "functionsConfigManifest", "middlewareManifest", "middleware", "functions", "pagesDataDir", "ampValidations", "publicDir", "failedExportAttemptsByPage", "minChunkSize", "staticGenerationMinPagesPerWorker", "numWorkers", "Math", "min", "ceil", "chunkSize", "chunks", "Array", "from", "_", "i", "slice", "remainingPages", "for<PERSON>ach", "index", "push", "progress", "statusMessage", "worker", "results", "Promise", "all", "paths", "exportPages", "parentSpanId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCache", "fetchCacheKeyPrefix", "flat", "hadValidationError", "collector", "by<PERSON><PERSON>", "byPage", "ssgNotFoundPaths", "turborepoAccessTraceResults", "result", "page<PERSON><PERSON>", "turborepoAccessTraceResult", "fromSerialized", "validation", "errors", "get", "cacheControl", "metadata", "hasEmptyPrelude", "hasPostponed", "fetchMetrics", "ssgNotFound", "durations", "durationsByPath", "duration", "ppr", "unnormalizedRoute", "srcRoute", "appPageName", "isAppPath", "isAppRouteHandler", "notFoundRoutes", "includes", "pagePath", "distPagesDir", "split", "orig", "handlerSrc", "handlerDest", "copyFile", "htmlDest", "ampHtmlDest", "jsonDest", "htmlSrc", "jsonSrc", "segmentsDir", "segmentsDirDest", "segmentPaths", "collectSegmentPaths", "segmentFileSrc", "segmentPath", "segmentFilename", "segmentFileDest", "console", "log", "failedPages", "sort", "flush", "end", "segmentsDirectory", "collectSegmentPathsImpl", "directory", "segmentFiles", "readdir", "withFileTypes", "segmentFile", "isDirectory", "name", "endsWith", "exportApp", "nextExportSpan"], "mappings": "AAKA,SAASA,kBAAkB,QAAgC,WAAU;AAGrE,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAE/C,OAAO,yBAAwB;AAE/B,SAASC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,OAAM;AAC5D,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,YAAYC,SAAS,sBAAqB;AAC1C,SACEC,kBAAkB,EAClBC,uBAAuB,EACvBC,UAAU,EACVC,yBAAyB,QACpB,mBAAkB;AACzB,SAASC,aAAa,QAAQ,wBAAuB;AACrD,SACEC,aAAa,EACbC,wBAAwB,EACxBC,wBAAwB,EACxBC,aAAa,EACbC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,cAAc,EACdC,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAChBC,yBAAyB,EACzBC,wBAAwB,EACxBC,eAAe,EACfC,yBAAyB,QACpB,0BAAyB;AAChC,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,aAAa,QAAQ,YAAW;AACzC,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,WAAW,QAAQ,oBAAmB;AAG/C,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,cAAc,QAAQ,2BAA0B;AACzD,OAAOC,aAAa,kBAAiB;AACrC,SAASC,cAAc,QAAQ,+CAA8C;AAC7E,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SAASC,cAAc,QAAQ,oBAAmB;AAElD,SAASC,0BAA0B,QAAQ,+CAA8C;AAEzF,SAASC,gCAAgC,QAAQ,sCAAqC;AACtF,SAASC,wCAAwC,QAAQ,qDAAoD;AAE7G,OAAO,MAAMC,oBAAoBC;;QAA1B,qBACLC,OAAO;;AACT;AAEA,eAAeC,cACbC,GAAW,EACXC,OAAmC,EACnCC,IAAU;QA8ROC,iBACIA,8BACCA;IA9RtBH,MAAM/C,QAAQ+C;IAEd,4EAA4E;IAC5EE,KAAKE,UAAU,CAAC,eAAeC,OAAO,CAAC,IAAMrB,cAAcgB,KAAK,OAAO3C;IAEvE,MAAM,EAAEiD,kBAAkB,EAAE,GAAGL;IAE/B,MAAME,aACJF,QAAQE,UAAU,IACjB,MAAMD,KACJE,UAAU,CAAC,oBACXG,YAAY,CAAC,IAAM7B,WAAWP,cAAc6B;IAEjD,MAAMQ,UAAUxD,KAAKgD,KAAKG,WAAWK,OAAO;IAC5C,MAAMC,YAAYR,QAAQS,WAAW,GAAG,OAAO,IAAI7B,UAAU;QAAE2B;IAAQ;IAEvE,IAAIC,WAAW;QACbA,UAAUE,MAAM,CACdhC,gBAAgB6B,SAASL,YAAY;YACnCS,gBAAgB;YAChBC,YAAY;YACZC,UAAU;YACVC,YAAY,CAAC,CAAE,MAAMpE,OAAO,YAAY;gBAAEqE,KAAKhB;YAAI;YACnDiB,gBAAgB;YAChBC,WAAW;YACXC,UAAU;YACVC,QAAQ;QACV;IAEJ;IAEA,MAAMC,aAAalB,WAAWmB,aAAa,IAAI,CAACrB,QAAQS,WAAW;IAEnE,IAAI,CAACT,QAAQsB,MAAM,IAAI,CAACtB,QAAQS,WAAW,EAAE;QAC3CrD,IAAImE,IAAI,CAAC,CAAC,uBAAuB,EAAEhB,SAAS;IAC9C;IAEA,MAAMiB,cAAczE,KAAKwD,SAAS7C;IAElC,IAAI,CAACf,WAAW6E,cAAc;QAC5B,MAAM,qBAEL,CAFK,IAAI7B,YACR,CAAC,0CAA0C,EAAEY,QAAQ,gJAAgJ,CAAC,GADlM,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMkB,eAAe;QAAC;QAAY;QAAa;KAAU,CAACC,MAAM,CAC9D,CAACC,SAAW,OAAOzB,UAAU,CAACyB,OAAO,KAAK;IAG5C,IAAI,CAAChD,kBAAkB,CAACqB,QAAQS,WAAW,IAAIgB,aAAaG,MAAM,GAAG,GAAG;QACtExE,IAAIyE,IAAI,CACN,CAAC,4FAA4F,EAAEJ,aAAa1E,IAAI,CAC9G,MACA,+EAA+E,CAAC;IAEtF;IAEA,MAAM+E,UAAU,MAAMjF,GAAGkF,QAAQ,CAACP,aAAa;IAE/C,MAAMQ,gBACJ,CAAChC,QAAQiC,KAAK,IACbC,QAAQnF,KAAKwD,SAASnC,kBAAkBH;IAE3C,IAAIkE;IACJ,IAAI;QACFA,oBAAoBD,QAAQnF,KAAKwD,SAASpC;IAC5C,EAAE,OAAM,CAAC;IAET,IAAIiE;IACJ,IAAI;QACFA,uBAAuBF,QAAQnF,KAAKwD,SAASjC;IAC/C,EAAE,OAAO+D,KAAK;QACZ,IACEjD,QAAQiD,QACPA,CAAAA,IAAIxC,IAAI,KAAK,YAAYwC,IAAIxC,IAAI,KAAK,kBAAiB,GACxD;YACA,0DAA0D;YAC1D,oCAAoC;YACpCuC,uBAAuBE;QACzB,OAAO;YACL,2CAA2C;YAC3C,MAAMD;QACR;IACF;IAEA,MAAME,0BAA0B,IAAIC;IACpC,MAAMP,QAAQjC,QAAQiC,KAAK,IAAIQ,OAAOC,IAAI,CAACV;IAC3C,MAAMW,iBAAgC,CAAC;IAEvC,IAAIC,eAAe;IACnB,KAAK,MAAMC,QAAQZ,MAAO;QACxB,wCAAwC;QACxC,0CAA0C;QAC1C,mCAAmC;QAEnC,IAAIjD,WAAW6D,OAAO;YACpBD,eAAe;YACf;QACF;QAEA,IAAIC,SAAS,gBAAgBA,SAAS,WAAWA,SAAS,WAAW;YACnE;QACF;QAEA,qEAAqE;QACrE,yEAAyE;QACzE,yEAAyE;QACzE,8CAA8C;QAC9C,IAAIV,qCAAAA,kBAAmBW,aAAa,CAACD,KAAK,EAAE;YAC1CN,wBAAwBQ,GAAG,CAACF;YAC5B;QACF;QAEAF,cAAc,CAACE,KAAK,GAAG;YAAEA;QAAK;IAChC;IAEA,MAAMG,oBAAoB,IAAIC;IAC9B,IAAI,CAACjD,QAAQS,WAAW,IAAI2B,sBAAsB;QAChD,KAAK,MAAM,CAACc,UAAUC,UAAU,IAAIV,OAAOW,OAAO,CAAChB,sBAAuB;YACxEY,kBAAkBK,GAAG,CAACF,WAAWD;YACjC,IACE/D,eAAe+D,aACf,EAACf,qCAAAA,kBAAmBmB,MAAM,CAACH,UAAU,KACrC,EAAChB,qCAAAA,kBAAmBW,aAAa,CAACK,UAAU,GAC5C;gBACAR,cAAc,CAACQ,UAAU,GAAG;oBAC1BN,MAAMK;oBACNK,WAAW;gBACb;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,MAAMC,SAASxD,QAAQyD,MAAM;IAE7B,IAAID,WAAWzG,KAAKgD,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIJ,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI6D,WAAWzG,KAAKgD,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIJ,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM9C,GAAG6G,EAAE,CAACF,QAAQ;QAAEG,WAAW;QAAMC,OAAO;IAAK;IACnD,MAAM/G,GAAGgH,KAAK,CAAC9G,KAAKyG,QAAQ,SAAS1B,UAAU;QAAE6B,WAAW;IAAK;IAEjE,MAAM9G,GAAGiH,SAAS,CAChB/G,KAAKwD,SAAS1C,gBACdwB,eAAe;QACb0E,SAAS;QACTC,cAAcR;QACdS,SAAS;IACX,IACA;IAGF,wBAAwB;IACxB,IAAI,CAACjE,QAAQS,WAAW,IAAI9D,WAAWI,KAAKgD,KAAK,YAAY;QAC3D,IAAI,CAACC,QAAQsB,MAAM,EAAE;YACnBlE,IAAImE,IAAI,CAAC;QACX;QACA,MAAMtB,KACHE,UAAU,CAAC,yBACXG,YAAY,CAAC,IACZ7C,cAAcV,KAAKgD,KAAK,WAAWhD,KAAKyG,QAAQ;IAEtD;IAEA,8BAA8B;IAC9B,IACE,CAACxD,QAAQS,WAAW,IACpB9D,WAAWI,KAAKwD,SAAS3C,4BACzB;QACA,IAAI,CAACoC,QAAQsB,MAAM,EAAE;YACnBlE,IAAImE,IAAI,CAAC;QACX;QACA,MAAMtB,KACHE,UAAU,CAAC,8BACXG,YAAY,CAAC,IACZ7C,cACEV,KAAKwD,SAAS3C,2BACdb,KAAKyG,QAAQ,SAAS5F;IAG9B;IAEA,6CAA6C;IAC7C,IAAI,OAAOsC,WAAWgE,aAAa,KAAK,YAAY;QAClDhE,WAAWgE,aAAa,GAAG,OAAOC;YAChC,OAAOA;QACT;IACF;IAEA,MAAM,EACJC,IAAI,EACJC,QAAQ,EAAEC,SAAS,SAAS,EAAEC,WAAW,EAAE,EAC5C,GAAGrE;IAEJ,IAAIkE,QAAQ,CAACpE,QAAQS,WAAW,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAId,YACR,CAAC,8IAA8I,CAAC,GAD5I,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,CAACK,QAAQS,WAAW,EAAE;QACxB,MAAM,EAAE+D,mBAAmB,EAAE,GAAG,MAAMvE,KACnCE,UAAU,CAAC,0BACXG,YAAY,CAAC,IACZzD,GACGkF,QAAQ,CAAChF,KAAKwD,SAASzC,gBAAgB,QACvC2G,IAAI,CAAC,CAACC,OAASC,KAAKC,KAAK,CAACF,OAC1BG,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA;QAGrB,IACEL,uBACAF,WAAW,aACX,CAACC,eACD,CAAC5F,gBACD;YACA,MAAM,qBAML,CANK,IAAIgB,YACR,CAAC;;;;8DAIqD,CAAC,GALnD,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;IACF;IAEA,IAAImF;IACJ,IAAIzE,mBAAmB0E,GAAG,EAAE;QAC1BD,wBAAwB5C,QACtBnF,KAAKwD,SAASnC,kBAAkBC,4BAA4B;QAG9D,IAAI6B,WAAW8E,MAAM,KAAK,UAAU;gBAK9BC,sCAAAA;YAJJ,MAAMA,iBAAiB/C,QAAQnF,KAAKwD,SAAShC;YAE7C,2FAA2F;YAC3F,6DAA6D;YAC7D,IAAI0G,CAAAA,mCAAAA,2BAAAA,eAAgBC,QAAQ,sBAAxBD,uCAAAA,yBAA0BE,WAAW,qBAArCF,qCAAuCrD,MAAM,IAAG,GAAG;gBACrD,MAAMwD,8BACJH,eAAeC,QAAQ,CAACC,WAAW,CAACE,IAAI,CAAC7F;gBAE3C,IAAI4F,6BAA6B;oBAC/B,MAAM,qBAEL,CAFK,IAAIzF,YACR,CAAC,yKAAyK,CAAC,GADvK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAM2F,YAAY;mBACb7C,OAAOC,IAAI,CAACoC,sBAAsBS,IAAI;mBACtC9C,OAAOC,IAAI,CAACoC,sBAAsBU,IAAI;aAC1C;YAED,IACEF,UAAUD,IAAI,CACZ,CAACI,WACChG,iCAAiCgG,UAAUC,IAAI,KAAK,kBAExD;gBACA,MAAM,qBAEL,CAFK,IAAI/F,YACR,CAAC,oKAAoK,CAAC,GADlK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAMgG,aAAsC;QAC1CC,YAAY,EAAEzD,qCAAAA,kBAAmB0D,OAAO;QACxCC,YAAY;QACZC,aAAa7F,WAAW6F,WAAW,CAACC,OAAO,CAAC,OAAO;QACnDzF;QACA0F,KAAK;QACLC,UAAUhG,WAAWgG,QAAQ;QAC7B7E,eAAenB,WAAWmB,aAAa;QACvC8E,eAAejG,EAAAA,kBAAAA,WAAWkG,GAAG,qBAAdlG,gBAAgBiG,aAAa,KAAI;QAChDE,mBAAmBnG,EAAAA,+BAAAA,WAAWoG,YAAY,CAACF,GAAG,qBAA3BlG,6BAA6BqG,cAAc,KAAI;QAClEC,oBAAoBtG,EAAAA,gCAAAA,WAAWoG,YAAY,CAACF,GAAG,qBAA3BlG,8BAA6BuG,SAAS,KAAInE;QAC9DoE,OAAO,EAAEtC,wBAAAA,KAAMsC,OAAO;QACtBC,MAAM,EAAEvC,wBAAAA,KAAMwC,aAAa;QAC3BA,aAAa,EAAExC,wBAAAA,KAAMwC,aAAa;QAClCC,aAAa,EAAEzC,wBAAAA,KAAM0C,OAAO;QAC5BC,yBAAyB7G,WAAWoG,YAAY,CAACS,uBAAuB;QACxE,wDAAwD;QACxDC,yBAAyB;QACzBC,aAAa/G,WAAW+G,WAAW;QACnCC,aAAahH,WAAWoG,YAAY,CAACY,WAAW;QAChDC,kBAAkBjH,WAAW8E,MAAM;QACnCoC,mBAAmBlH,WAAWoG,YAAY,CAACc,iBAAiB;QAC5DC,oBAAoBnH,WAAWoG,YAAY,CAACe,kBAAkB;QAC9DC,eAAepH,WAAWoG,YAAY,CAACgB,aAAa;QACpDC,kBAAkBlH,mBAAmB0E,GAAG;QACxCyC,mBAAmBtH,WAAWoG,YAAY,CAACmB,SAAS;QACpDC,kBAAkBxF,QAChBnF,KAAKwD,SAAS,UAAU,GAAGxC,mBAAmB,KAAK,CAAC;QAEtDsG,QAAQnE,WAAWmE,MAAM;QACzB,GAAIhE,mBAAmB0E,GAAG,GACtB;YACED;QACF,IACA,CAAC,CAAC;QACN6C,gBAAgBzH,WAAWoG,YAAY,CAACqB,cAAc,IAAI;QAC1DC,cAAc1H,WAAW0H,YAAY;QACrCC,iBAAiB3H,WAAW2H,eAAe,CAACC,MAAM;QAClDxB,cAAc;YACZyB,qBAAqB7H,WAAWoG,YAAY,CAACyB,mBAAmB;YAChEC,YAAY9H,WAAW8H,UAAU;YACjCC,WAAW/H,WAAWoG,YAAY,CAAC2B,SAAS,IAAI;YAChDC,oBACEhI,WAAWoG,YAAY,CAAC4B,kBAAkB,KAAK,gBAC3C,gBACAC,QAAQjI,WAAWoG,YAAY,CAAC4B,kBAAkB;YACxDE,gBAAgBlI,WAAWoG,YAAY,CAAC8B,cAAc,IAAI;YAC1DC,WAAWnI,WAAWoG,YAAY,CAAC+B,SAAS,IAAI;YAChDC,gBAAgB,CAAC,CAACpI,WAAWoG,YAAY,CAACgC,cAAc;QAC1D;QACAC,uBAAuBrI,WAAWqI,qBAAqB;IACzD;IAEA,MAAM,EAAEC,mBAAmB,EAAE,GAAGtI;IAEhC,IAAIuC,OAAOC,IAAI,CAAC8F,qBAAqB5G,MAAM,GAAG,GAAG;QAC/C+D,WAAW8C,aAAa,GAAGD;IAC7B;IAEA,wDAAwD;;IACtDE,WAAmBC,aAAa,GAAG;QACnC7C,YAAY;IACd;IAEA,MAAM5B,gBAAgB,MAAMjE,KACzBE,UAAU,CAAC,uBACXG,YAAY,CAAC;QACZ,MAAMsI,YAAY,MAAM1I,WAAWgE,aAAa,CAACvB,gBAAgB;YAC/DsD,KAAK;YACLlG;YACAyD;YACAjD;YACAuB;QACF;QACA,OAAO8G;IACT;IAEF,wDAAwD;IACxD,IAAI,CAAC5I,QAAQS,WAAW,EAAE;QACxB,4DAA4D;QAC5D,IAAI,CAACyD,aAAa,CAAC,OAAO,EAAE;YAC1BA,aAAa,CAAC,OAAO,GAAG;gBAAErB,MAAM;YAAU;QAC5C;QAEA;;;KAGC,GACD,IAAI,CAACqB,aAAa,CAAC,YAAY,EAAE;YAC/B,yEAAyE;YACzEA,aAAa,CAAC,YAAY,GAAGA,aAAa,CAAC,OAAO;QACpD;IACF;IAEA,kCAAkC;IAClC,MAAM2E,cAAc;WACf,IAAIrG,IACLC,OAAOC,IAAI,CAACwB,eAAe4E,GAAG,CAAC,CAACC,OAC9BjK,oBAAoBD,kBAAkBkK;KAG3C;IAED,MAAMC,gBAAgBH,YAAYnH,MAAM,CACtC,CAACuH,QACC/E,aAAa,CAAC+E,MAAM,CAAC1F,SAAS,IAC9B,oBAAoB;QACpB,CAACvE,WAAWkF,aAAa,CAAC+E,MAAM,CAACpG,IAAI;IAGzC,IAAImG,cAAcpH,MAAM,KAAKiH,YAAYjH,MAAM,EAAE;QAC/CgB,eAAe;IACjB;IAEA,IAAIoG,cAAcpH,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,IAAIO,qBAAqB,CAACnC,QAAQS,WAAW,EAAE;QAC7C,MAAMyI,uBAAuB,IAAI1G;QAEjC,KAAK,MAAMuG,QAAQtG,OAAOC,IAAI,CAACwB,eAAgB;YAC7C,MAAMrB,OAAOqB,aAAa,CAAC6E,KAAK,CAAClG,IAAI;YACrC,MAAMsG,gBAAgBhH,kBAAkBW,aAAa,CAACD,KAAK;YAE3D,IAAIsG,iBAAiBA,cAAcC,QAAQ,KAAK,OAAO;gBACrDF,qBAAqBnG,GAAG,CAACF;YAC3B;QACF;QAEA,IAAIqG,qBAAqBG,IAAI,GAAG,GAAG;YACjC,MAAM,qBAIL,CAJK,IAAI1J,YACR,CAAC,wCAAwC,EAAE;mBACtCuJ;aACJ,CAACnM,IAAI,CAAC,MAAM,EAAE,EAAES,0BAA0B,EAAE,CAAC,GAH1C,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IACA,IAAI8L,gBAAgB;IAEpB,IAAI,CAACtJ,QAAQS,WAAW,EAAE;QACxB,IAAI;gBAWQ8I;YAVV,MAAMC,qBAAqBtH,QACzBnF,KAAKwD,SAASnC,kBAAkBJ;YAGlC,MAAMuL,0BAA0BrH,QAC9BnF,KAAKwD,SAASnC,kBAAkBI;YAGlC8K,gBACE7G,OAAOC,IAAI,CAAC8G,mBAAmBC,UAAU,EAAE7H,MAAM,GAAG,KACpDuG,SAAQoB,qCAAAA,wBAAwBG,SAAS,qBAAjCH,kCAAmC,CAAC,eAAe;QAC/D,EAAE,OAAM,CAAC;QAET,kDAAkD;QAClD,IAAI3G,gBAAgB0G,eAAe;YACjC,IAAIpJ,WAAW8E,MAAM,KAAK,UAAU;gBAClC5H,IAAIyE,IAAI,CACNpF,OACE,CAAC,kGAAkG,CAAC,IAEpG,CAAC,EAAE,CAAC,GACJA,OACE,CAAC,mDAAmD,CAAC,GACnD,MACAD,KAAK,CAAC,8CAA8C,CAAC,KAEzD,CAAC,EAAE,CAAC,GACJC,OACE,CAAC,2KAA2K,CAAC,IAE/K,CAAC,EAAE,CAAC,GACJA,OACE,CAAC,qEAAqE,CAAC;YAG/E;QACF;IACF;IAEA,MAAMkN,eAAe3J,QAAQS,WAAW,GACpC+C,SACAzG,KAAKyG,QAAQ,cAAc1B;IAE/B,MAAM8H,iBAAgC,CAAC;IAEvC,MAAMC,YAAY9M,KAAKgD,KAAKpC;IAC5B,wBAAwB;IACxB,IAAI,CAACqC,QAAQS,WAAW,IAAI9D,WAAWkN,YAAY;QACjD,IAAI,CAAC7J,QAAQsB,MAAM,EAAE;YACnBlE,IAAImE,IAAI,CAAC;QACX;QACA,MAAMtB,KAAKE,UAAU,CAAC,yBAAyBG,YAAY,CAAC,IAC1D7C,cAAcoM,WAAWrG,QAAQ;gBAC/B9B,QAAOqH,IAAI;oBACT,8BAA8B;oBAC9B,OAAO,CAAC7E,aAAa,CAAC6E,KAAK;gBAC7B;YACF;IAEJ;IAEA,MAAMe,6BAAmD,IAAI7G;IAE7D,sFAAsF;IACtF,+EAA+E;IAC/E,qFAAqF;IACrF,2BAA2B;IAC3B,MAAM8G,eACJ7J,WAAWoG,YAAY,CAAC0D,iCAAiC,IAAI;IAC/D,8FAA8F;IAC9F,MAAMC,aAAaC,KAAKC,GAAG,CACzBnK,QAAQiK,UAAU,EAClBC,KAAKE,IAAI,CAACpB,cAAcpH,MAAM,GAAGmI;IAEnC,0DAA0D;IAC1D,MAAMM,YAAYH,KAAKE,IAAI,CAACpB,cAAcpH,MAAM,GAAGqI;IACnD,MAAMK,SAASC,MAAMC,IAAI,CAAC;QAAE5I,QAAQqI;IAAW,GAAG,CAACQ,GAAGC,IACpD1B,cAAc2B,KAAK,CAACD,IAAIL,WAAW,AAACK,CAAAA,IAAI,CAAA,IAAKL;IAE/C,6BAA6B;IAC7B,MAAMO,iBAAiB5B,cAAc2B,KAAK,CAACV,aAAaI;IACxDO,eAAeC,OAAO,CAAC,CAAChI,MAAMiI;QAC5BR,MAAM,CAACQ,QAAQR,OAAO1I,MAAM,CAAC,CAACmJ,IAAI,CAAClI;IACrC;IAEA,MAAMmI,WAAWzL,eACfyJ,cAAcpH,MAAM,EACpB5B,QAAQiL,aAAa,IAAI;IAG3B,MAAMC,SAAS3O,mBAAmB2D,YAAY8K;IAE9C,MAAMG,UAAU,AACd,CAAA,MAAMC,QAAQC,GAAG,CACff,OAAOxB,GAAG,CAAC,CAACwC,QACVJ,OAAOK,WAAW,CAAC;YACjBzJ;YACAwJ;YACApH;YACAsH,cAAcvL,KAAKwL,KAAK;YACxB9B;YACAhE;YACA3F;YACAD;YACAQ;YACAiD;YACAtD;YACAwL,cAAcxL,WAAWwL,YAAY;YACrCC,oBAAoBzL,WAAWyL,kBAAkB;YACjDC,YAAY;YACZC,qBAAqB3L,WAAWoG,YAAY,CAACuF,mBAAmB;QAClE,IAEJ,EACAC,IAAI;IAEN,IAAIC,qBAAqB;IAEzB,MAAMC,YAA6B;QACjCC,QAAQ,IAAIhJ;QACZiJ,QAAQ,IAAIjJ;QACZkJ,kBAAkB,IAAI3J;QACtB4J,6BAA6B,IAAInJ;IACnC;IAEA,KAAK,MAAM,EAAEoJ,MAAM,EAAEtD,IAAI,EAAEuD,OAAO,EAAE,IAAInB,QAAS;QAC/C,IAAI,CAACkB,QAAQ;QACb,IAAI,WAAWA,QAAQ;YACrBvC,2BAA2BzG,GAAG,CAACiJ,SAAS;YACxC;QACF;QAEA,MAAM,EAAEzJ,IAAI,EAAE,GAAGqB,aAAa,CAAC6E,KAAK;QAEpC,IAAIsD,OAAOE,0BAA0B,EAAE;gBACrCP;aAAAA,yCAAAA,UAAUI,2BAA2B,qBAArCJ,uCAAuC3I,GAAG,CACxC0F,MACAzJ,2BAA2BkN,cAAc,CACvCH,OAAOE,0BAA0B;QAGvC;QAEA,+BAA+B;QAC/B,IAAIF,OAAOzC,cAAc,EAAE;YACzB,KAAK,MAAM6C,cAAcJ,OAAOzC,cAAc,CAAE;gBAC9CA,cAAc,CAAC6C,WAAW5J,IAAI,CAAC,GAAG4J,WAAWJ,MAAM;gBACnDN,uBAAuBU,WAAWJ,MAAM,CAACK,MAAM,CAAC9K,MAAM,GAAG;YAC3D;QACF;QAEA,IAAI5B,QAAQS,WAAW,EAAE;YACvB,4BAA4B;YAC5B,MAAMc,OAAOyK,UAAUC,MAAM,CAACU,GAAG,CAAC5D,SAAS,CAAC;YAC5C,IAAIsD,OAAOO,YAAY,EAAE;gBACvBrL,KAAKqL,YAAY,GAAGP,OAAOO,YAAY;YACzC;YACA,IAAI,OAAOP,OAAOQ,QAAQ,KAAK,aAAa;gBAC1CtL,KAAKsL,QAAQ,GAAGR,OAAOQ,QAAQ;YACjC;YAEA,IAAI,OAAOR,OAAOS,eAAe,KAAK,aAAa;gBACjDvL,KAAKuL,eAAe,GAAGT,OAAOS,eAAe;YAC/C;YAEA,IAAI,OAAOT,OAAOU,YAAY,KAAK,aAAa;gBAC9CxL,KAAKwL,YAAY,GAAGV,OAAOU,YAAY;YACzC;YAEA,IAAI,OAAOV,OAAOW,YAAY,KAAK,aAAa;gBAC9CzL,KAAKyL,YAAY,GAAGX,OAAOW,YAAY;YACzC;YAEAhB,UAAUC,MAAM,CAAC5I,GAAG,CAAC0F,MAAMxH;YAE3B,oBAAoB;YACpB,IAAI8K,OAAOY,WAAW,KAAK,MAAM;gBAC/BjB,UAAUG,gBAAgB,CAACpJ,GAAG,CAACgG;YACjC;YAEA,oBAAoB;YACpB,MAAMmE,YAAYlB,UAAUE,MAAM,CAACS,GAAG,CAAC9J,SAAS;gBAC9CsK,iBAAiB,IAAIlK;YACvB;YACAiK,UAAUC,eAAe,CAAC9J,GAAG,CAAC0F,MAAMsD,OAAOe,QAAQ;YACnDpB,UAAUE,MAAM,CAAC7I,GAAG,CAACR,MAAMqK;QAC7B;IACF;IAEA,4EAA4E;IAC5E,IAAI,CAAClN,QAAQS,WAAW,IAAIP,WAAWoG,YAAY,CAAC+G,GAAG,EAAE;QACvD,oBAAoB;QACpB,MAAM,qBAA4D,CAA5D,IAAIzN,MAAM,oDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA2D;IACnE;IAEA,oCAAoC;IACpC,IAAI,CAACI,QAAQS,WAAW,IAAI0B,mBAAmB;QAC7C,MAAMiJ,QAAQC,GAAG,CACf5I,OAAOC,IAAI,CAACP,kBAAkBmB,MAAM,EAAEwF,GAAG,CAAC,OAAOwE;YAC/C,MAAM,EAAEC,QAAQ,EAAE,GAAGpL,kBAAmBmB,MAAM,CAACgK,kBAAkB;YACjE,MAAME,cAAcxK,kBAAkB2J,GAAG,CAACY,YAAY;YACtD,MAAMrK,WAAWsK,eAAeD,YAAYD;YAC5C,MAAMG,YAAYtF,QAAQqF;YAC1B,MAAME,oBAAoBF,eAAetO,gBAAgBsO;YAEzD,wDAAwD;YACxD,0CAA0C;YAC1C,IAAIrL,kBAAmBwL,cAAc,CAACC,QAAQ,CAACN,oBAAoB;gBACjE;YACF;YACA,sEAAsE;YACtE,qEAAqE;YACrE,sEAAsE;YACtE,8BAA8B;YAC9B,MAAMrE,QAAQpK,kBAAkByO;YAEhC,MAAMO,WAAW5O,YAAYiE,UAAU3C,SAAS+B,WAAWmL;YAC3D,MAAMK,eAAe/Q,KACnB8Q,UACA,yDAAyD;YACzD,4BAA4B;YAC5B3K,SACGyH,KAAK,CAAC,GACNoD,KAAK,CAAC,KACNjF,GAAG,CAAC,IAAM,MACV/L,IAAI,CAAC;YAGV,MAAMiR,OAAOjR,KAAK+Q,cAAc7E;YAChC,MAAMgF,aAAa,GAAGD,KAAK,KAAK,CAAC;YACjC,MAAME,cAAcnR,KAAKyG,QAAQyF;YAEjC,IAAIyE,qBAAqB/Q,WAAWsR,aAAa;gBAC/C,MAAMpR,GAAGgH,KAAK,CAAC/G,QAAQoR,cAAc;oBAAEvK,WAAW;gBAAK;gBACvD,MAAM9G,GAAGsR,QAAQ,CAACF,YAAYC;gBAC9B;YACF;YAEA,MAAME,WAAWrR,KACfyG,QACA,GAAGyF,QACD7H,cAAc6H,UAAU,WAAW,GAAGhM,IAAI,KAAK,CAAC,GAAG,GACpD,KAAK,CAAC;YAET,MAAMoR,cAActR,KAClByG,QACA,GAAGyF,MAAM,IAAI,EAAE7H,aAAa,GAAGnE,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YAEvD,MAAMqR,WAAWb,YACb1Q,KACEyG,QACA,GAAGyF,QACD7H,cAAc6H,UAAU,WAAW,GAAGhM,IAAI,KAAK,CAAC,GAAG,GACpD,IAAI,CAAC,IAERF,KAAK4M,cAAc,GAAGV,MAAM,KAAK,CAAC;YAEtC,MAAMpM,GAAGgH,KAAK,CAAC/G,QAAQsR,WAAW;gBAAEzK,WAAW;YAAK;YACpD,MAAM9G,GAAGgH,KAAK,CAAC/G,QAAQwR,WAAW;gBAAE3K,WAAW;YAAK;YAEpD,MAAM4K,UAAU,GAAGP,KAAK,KAAK,CAAC;YAC9B,MAAMQ,UAAU,GAAGR,OAAOP,YAAYlQ,aAAa,SAAS;YAE5D,MAAMV,GAAGsR,QAAQ,CAACI,SAASH;YAC3B,MAAMvR,GAAGsR,QAAQ,CAACK,SAASF;YAE3B,IAAI3R,WAAW,GAAGqR,KAAK,SAAS,CAAC,GAAG;gBAClC,MAAMnR,GAAGgH,KAAK,CAAC/G,QAAQuR,cAAc;oBAAE1K,WAAW;gBAAK;gBACvD,MAAM9G,GAAGsR,QAAQ,CAAC,GAAGH,KAAK,SAAS,CAAC,EAAEK;YACxC;YAEA,MAAMI,cAAc,GAAGT,OAAO1Q,yBAAyB;YACvD,IAAImQ,aAAa9Q,WAAW8R,cAAc;gBACxC,sDAAsD;gBACtD,EAAE;gBACF,4DAA4D;gBAC5D,qEAAqE;gBACrE,uCAAuC;gBACvC,EAAE;gBACF,mEAAmE;gBACnE,iEAAiE;gBACjE,6BAA6B;gBAC7B,MAAMC,kBAAkB3R,KAAKyG,QAAQ8J;gBACrC,MAAMqB,eAAe,MAAMC,oBAAoBH;gBAC/C,MAAMrD,QAAQC,GAAG,CACfsD,aAAa7F,GAAG,CAAC,OAAO+F;oBACtB,MAAMC,cACJ,MAAMD,eAAelE,KAAK,CAAC,GAAG,CAACtN,mBAAmBuE,MAAM;oBAC1D,MAAMmN,kBACJrP,yCAAyCoP;oBAC3C,MAAME,kBAAkBjS,KAAK2R,iBAAiBK;oBAC9C,MAAMlS,GAAGgH,KAAK,CAAC/G,QAAQkS,kBAAkB;wBAAErL,WAAW;oBAAK;oBAC3D,MAAM9G,GAAGsR,QAAQ,CACfpR,KAAK0R,aAAaI,iBAClBG;gBAEJ;YAEJ;QACF;IAEJ;IAEA,IAAIvM,OAAOC,IAAI,CAACkH,gBAAgBhI,MAAM,EAAE;QACtCqN,QAAQC,GAAG,CAAC/R,kBAAkByM;IAChC;IACA,IAAImC,oBAAoB;QACtB,MAAM,qBAEL,CAFK,IAAIpM,YACR,CAAC,gGAAgG,CAAC,GAD9F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAImK,2BAA2BT,IAAI,GAAG,GAAG;QACvC,MAAM8F,cAAc5E,MAAMC,IAAI,CAACV,2BAA2BpH,IAAI;QAC9D,MAAM,qBAIL,CAJK,IAAI/C,YACR,CAAC,iDAAiD,EAAEwP,YACjDC,IAAI,GACJrS,IAAI,CAAC,SAAS,GAHb,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,MAAMF,GAAGiH,SAAS,CAChB/G,KAAKwD,SAAS1C,gBACdwB,eAAe;QACb0E,SAAS;QACTC,cAAcR;QACdS,SAAS;IACX,IACA;IAGF,IAAIzD,WAAW;QACb,MAAMA,UAAU6O,KAAK;IACvB;IAEA,MAAMnE,OAAOoE,GAAG;IAEhB,OAAOtD;AACT;AAEA,eAAe4C,oBAAoBW,iBAAyB;IAC1D,MAAMpE,UAAyB,EAAE;IACjC,MAAMqE,wBAAwBD,mBAAmBA,mBAAmBpE;IACpE,OAAOA;AACT;AAEA,eAAeqE,wBACbD,iBAAyB,EACzBE,SAAiB,EACjBtE,OAAsB;IAEtB,MAAMuE,eAAe,MAAM7S,GAAG8S,OAAO,CAACF,WAAW;QAC/CG,eAAe;IACjB;IACA,MAAMxE,QAAQC,GAAG,CACfqE,aAAa5G,GAAG,CAAC,OAAO+G;QACtB,IAAIA,YAAYC,WAAW,IAAI;YAC7B,MAAMN,wBACJD,mBACAxS,KAAK0S,WAAWI,YAAYE,IAAI,GAChC5E;YAEF;QACF;QACA,IAAI,CAAC0E,YAAYE,IAAI,CAACC,QAAQ,CAAC3S,qBAAqB;YAClD;QACF;QACA8N,QAAQJ,IAAI,CACV7N,SAASqS,mBAAmBxS,KAAK0S,WAAWI,YAAYE,IAAI;IAEhE;AAEJ;AAEA,eAAe,eAAeE,UAC5BlQ,GAAW,EACXC,OAAyB,EACzBC,IAAU;IAEV,MAAMiQ,iBAAiBjQ,KAAKE,UAAU,CAAC;IAEvC,OAAO+P,eAAe5P,YAAY,CAAC;QACjC,OAAO,MAAMR,cAAcC,KAAKC,SAASkQ;IAC3C;AACF"}