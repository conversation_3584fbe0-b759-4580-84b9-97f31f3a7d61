const express = require('express');
const router = express.Router();
const Template = require('../models/TemplateModel');

// Get all templates
router.get('/templates', async (req, res) => {
    try {
        const type = req.query.type;
        let templates;

        if (type) {
            templates = await Template.find({ type });
        } else {
            templates = await Template.find();
        }

        res.status(200).json(templates);
    } catch (error) {
        console.error('Error fetching templates:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des templates' });
    }
});

// Get a single template by ID
router.get('/templates/:id', async (req, res) => {
    try {
        const id = req.params.id;
        console.log('Getting template with ID:', id);

        let template = null;

        // Vérifier si l'ID est un ID MongoDB valide (24 caractères hexadécimaux)
        const isValidMongoId = id.match(/^[0-9a-fA-F]{24}$/);

        if (isValidMongoId) {
            // Si c'est un ID MongoDB valide, utiliser findById
            template = await Template.findById(id);
        } else {
            // Sinon, essayer de trouver le template par un autre champ (comme un ID numérique)
            console.log('ID is not a valid MongoDB ID, trying to find by other means');

            // Essayer de trouver par un ID numérique si c'est un nombre
            if (!isNaN(Number(id))) {
                // Vous pourriez avoir un champ 'legacyId' ou similaire dans votre modèle
                // Ici, nous allons simplement retourner une erreur pour indiquer que l'ID n'est pas valide
                return res.status(400).json({ error: 'ID de template invalide' });
            } else {
                return res.status(400).json({ error: 'ID de template invalide' });
            }
        }

        if (!template) {
            console.log('Template not found with ID:', id);
            return res.status(404).json({ error: 'Template non trouvé' });
        }

        console.log('Template found:', template);
        res.status(200).json(template);
    } catch (error) {
        console.error('Error fetching template:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération du template' });
    }
});

// Create a new template
router.post('/templates', async (req, res) => {
    try {
        console.log('Creating template with data:', req.body);
        const newTemplate = new Template(req.body);
        await newTemplate.save();
        res.status(201).json(newTemplate);
    } catch (error) {
        console.error('Error creating template:', error);
        res.status(500).json({
            error: 'Erreur lors de la création du template',
            details: error.message,
            stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined
        });
    }
});

// Update a template
router.put('/templates/:id', async (req, res) => {
    try {
        console.log('Updating template with data:', req.body);
        console.log('Template ID:', req.params.id);

        let template = null;
        const id = req.params.id;

        // Vérifier si l'ID est un ID MongoDB valide (24 caractères hexadécimaux)
        const isValidMongoId = id.match(/^[0-9a-fA-F]{24}$/);

        if (isValidMongoId) {
            // Si c'est un ID MongoDB valide, utiliser findByIdAndUpdate
            template = await Template.findByIdAndUpdate(
                id,
                req.body,
                { new: true, runValidators: true }
            );
        } else {
            // Sinon, essayer de trouver le template par un autre champ (comme un ID numérique)
            // Cela suppose que vous avez un champ 'legacyId' ou similaire dans votre modèle
            // ou que vous utilisez l'ID MongoDB comme une chaîne
            console.log('ID is not a valid MongoDB ID, trying to find by other means');

            // Essayer de trouver par un ID numérique si c'est un nombre
            if (!isNaN(Number(id))) {
                // Créer un nouveau template avec les données fournies
                console.log('Creating new template with provided data');
                const newTemplate = new Template(req.body);
                template = await newTemplate.save();
            } else {
                return res.status(400).json({ error: 'ID de template invalide' });
            }
        }

        if (!template) {
            console.log('Template not found with ID:', id);
            return res.status(404).json({ error: 'Template non trouvé' });
        }

        console.log('Template updated successfully:', template);
        res.status(200).json(template);
    } catch (error) {
        console.error('Error updating template:', error);
        res.status(500).json({
            error: 'Erreur lors de la mise à jour du template',
            details: error.message,
            stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined
        });
    }
});

// Delete a template
router.delete('/templates/:id', async (req, res) => {
    try {
        const template = await Template.findByIdAndDelete(req.params.id);
        if (!template) return res.status(404).json({ error: 'Template non trouvé' });
        res.status(204).send();
    } catch (error) {
        console.error('Error deleting template:', error.message);
        res.status(500).json({ error: 'Erreur lors de la suppression du template' });
    }
});

// Set a template as default
router.post('/templates/:id/set-default', async (req, res) => {
    try {
        const template = await Template.findById(req.params.id);
        if (!template) return res.status(404).json({ error: 'Template non trouvé' });

        // Récupérer le type du template actuel
        const { type } = template;

        // Retirer l'attribut default des autres templates du même type
        await Template.updateMany(
            { type, _id: { $ne: template._id }, isDefault: true },
            { $set: { isDefault: false } }
        );

        // Définir ce template comme défaut
        template.isDefault = true;
        await template.save();

        res.status(200).json({ message: 'Template défini comme défaut avec succès' });
    } catch (error) {
        console.error('Error setting default template:', error.message);
        res.status(500).json({ error: 'Erreur lors de la définition du template par défaut' });
    }
});

module.exports = router;