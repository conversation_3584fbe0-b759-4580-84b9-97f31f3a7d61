import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress
} from '@mui/material';

const EmailDialog = ({ open, onClose, onSend, documentType, documentNumber, recipientEmail }) => {
  const [to, setTo] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (open) {
      // Initialize form when dialog opens
      setTo(recipientEmail || '');
      setSubject(`${documentType === 'facture' ? 'Facture' : 'Devis'} ${documentNumber}`);
      setMessage(`Veuillez trouver ci-joint ${documentType === 'facture' ? 'la facture' : 'le devis'} ${documentNumber}.`);
      setError('');
      setLoading(false);
    }
  }, [open, documentType, documentNumber, recipientEmail]);

  const handleSend = async () => {
    // Validate email
    if (!to) {
      setError('L\'adresse email du destinataire est requise');
      return;
    }

    // Check if there are multiple emails
    const emails = to.split(',').map(email => email.trim()).filter(email => email);

    // Validate each email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    for (const email of emails) {
      if (!emailRegex.test(email)) {
        setError(`L'adresse email "${email}" n'est pas valide`);
        return;
      }
    }

    setLoading(true);
    setError('');

    try {
      await onSend({ to, subject, message });
      onClose();
    } catch (error) {
      setError(`Erreur lors de l'envoi de l'email: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Envoyer {documentType === 'facture' ? 'la facture' : 'le devis'} par email
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <TextField
            label="Destinataire(s)"
            fullWidth
            margin="normal"
            value={to}
            onChange={(e) => setTo(e.target.value)}
            placeholder="<EMAIL>, <EMAIL>"
            helperText="Pour plusieurs destinataires, séparez les adresses par des virgules"
            required
          />
          <TextField
            label="Sujet"
            fullWidth
            margin="normal"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
          <TextField
            label="Message"
            fullWidth
            margin="normal"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            multiline
            rows={4}
          />
          {error && (
            <Typography color="error" variant="body2" sx={{ mt: 2 }}>
              {error}
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Annuler
        </Button>
        <Button
          onClick={handleSend}
          variant="contained"
          color="primary"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          Envoyer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmailDialog;
