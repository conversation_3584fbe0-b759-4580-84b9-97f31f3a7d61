{"version": 3, "sources": ["../../../src/client/components/not-found-error.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function NotFound() {\n  return (\n    <HTTPAccessErrorFallback\n      status={404}\n      message=\"This page could not be found.\"\n    />\n  )\n}\n"], "names": ["HTTPAccessErrorFallback", "NotFound", "status", "message"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,wCAAuC;AAE/E,eAAe,SAASC;IACtB,qBACE,KAACD;QACCE,QAAQ;QACRC,SAAQ;;AAGd"}