import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  Divider,
  Button,
  Chip,
  CircularProgress,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import factureService from '../services/factureService';
import devisService from '../services/devisService';
import DocumentPreview from '../components/DocumentPreview';
import { formatCurrency, formatDate, formatStatut } from '../utils/formatters';
import { getCompanyData } from '../services/entrepriseService';

const EntrepriseDocumentView = () => {
  const { id } = useParams();
  const type = window.location.pathname.includes('/devis/') ? 'devis' : 'facture';
  const navigate = useNavigate();
  // Nous n'utilisons pas useAuth pour le moment
  useAuth();
  const [loading, setLoading] = useState(true);
  const [document, setDocument] = useState(null);
  const [companyData, setCompanyData] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState(null);

  // États pour les dialogues de confirmation
  const [openAcceptDialog, setOpenAcceptDialog] = useState(false);
  const [openRejectDialog, setOpenRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // État pour les notifications
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check for valid id
        if (!id || id === 'undefined') {
          setError('ID de document invalide ou manquant.');
          setLoading(false);
          return;
        }

        // Récupérer les données de l'entreprise pour l'aperçu
        try {
          const response = await getCompanyData();
          if (response && response.data) {
            setCompanyData(response.data);
          }
        } catch (err) {
          console.error("Erreur lors de la récupération des données de l'entreprise:", err);
          // Continuer même si les données de l'entreprise ne sont pas disponibles
        }

        // Récupérer le document (devis ou facture)
        let documentData;
        if (type === 'devis') {
          documentData = await devisService.getDevisById(id);
        } else if (type === 'facture') {
          documentData = await factureService.getFactureById(id);
        } else {
          throw new Error('Type de document non reconnu');
        }

        setDocument(documentData);
        setLoading(false);
      } catch (error) {
        console.error(`Erreur lors de la récupération du ${type}:`, error);
        setError(`Impossible de charger le ${type}. ${error.message}`);
        setLoading(false);
      }
    };

    fetchData();
  }, [type, id]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleBack = () => {
    navigate(`/entreprise/${type === 'devis' ? 'devis' : 'factures'}`);
  };

  // Ouvrir le dialogue de confirmation pour accepter un devis
  const handleOpenAcceptDialog = () => {
    setOpenAcceptDialog(true);
  };

  // Fermer le dialogue de confirmation pour accepter un devis
  const handleCloseAcceptDialog = () => {
    setOpenAcceptDialog(false);
  };

  // Ouvrir le dialogue de confirmation pour refuser un devis
  const handleOpenRejectDialog = () => {
    setOpenRejectDialog(true);
  };

  // Fermer le dialogue de confirmation pour refuser un devis
  const handleCloseRejectDialog = () => {
    setOpenRejectDialog(false);
    setRejectReason('');
  };

  // Accepter un devis après confirmation
  const handleAcceptDevis = async () => {
    try {
      setLoading(true);
      setOpenAcceptDialog(false);
      setError(null); // Réinitialiser les erreurs précédentes

      console.log(`Tentative d'acceptation du devis avec ID: ${id}`);

      // Vérifier que l'ID est valide
      if (!id) {
        throw new Error('ID du devis non valide');
      }

      // Appeler le service pour accepter le devis
      const result = await devisService.acceptDevis(id);
      console.log('Résultat de l\'acceptation:', result);

      // Attendre un peu avant de recharger le devis (pour laisser le temps à la BD de se mettre à jour)
      setTimeout(async () => {
        try {
          // Recharger le devis pour mettre à jour son statut
          const updatedDevis = await devisService.getDevisById(id);
          console.log('Devis mis à jour:', updatedDevis);
          setDocument(updatedDevis);

          // Afficher une notification de succès
          setNotification({
            open: true,
            message: 'Le devis a été accepté avec succès',
            severity: 'success'
          });

          setLoading(false);
        } catch (reloadError) {
          console.error('Erreur lors du rechargement du devis:', reloadError);

          // Essayer de recharger la page complète si le rechargement du devis échoue
          try {
            const documentData = await devisService.getDevisById(id);
            if (documentData) {
              setDocument(documentData);
              setNotification({
                open: true,
                message: 'Le devis a été accepté avec succès',
                severity: 'success'
              });
            } else {
              setError(`Le devis a été accepté mais impossible de recharger les détails.`);
            }
          } catch (secondError) {
            setError(`Le devis a été accepté mais impossible de recharger les détails. ${reloadError.message}`);
          }

          setLoading(false);
        }
      }, 2000); // Augmenter le délai à 2 secondes
    } catch (error) {
      console.error('Erreur lors de l\'acceptation du devis:', error);

      // Message d'erreur plus convivial
      let errorMessage = 'Impossible d\'accepter le devis.';

      if (error.message && error.message.includes('Network Error')) {
        errorMessage += ' Problème de connexion au serveur. Veuillez vérifier votre connexion et réessayer.';
      } else if (error.customMessage) {
        errorMessage += ` ${error.customMessage}`;
      } else if (error.message) {
        errorMessage += ` ${error.message}`;
      }

      setError(errorMessage);
      setLoading(false);

      // Afficher une notification d'erreur
      setNotification({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    }
  };

  // Refuser un devis après confirmation
  const handleRejectDevis = async () => {
    try {
      setLoading(true);
      setOpenRejectDialog(false);
      setError(null); // Réinitialiser les erreurs précédentes

      console.log(`Tentative de refus du devis avec ID: ${id}, raison: ${rejectReason}`);

      // Vérifier que l'ID est valide
      if (!id) {
        throw new Error('ID du devis non valide');
      }

      // Appeler le service pour refuser le devis
      const result = await devisService.rejectDevis(id, rejectReason);
      console.log('Résultat du refus:', result);

      // Attendre un peu avant de recharger le devis (pour laisser le temps à la BD de se mettre à jour)
      setTimeout(async () => {
        try {
          // Recharger le devis pour mettre à jour son statut
          const updatedDevis = await devisService.getDevisById(id);
          console.log('Devis mis à jour après refus:', updatedDevis);
          setDocument(updatedDevis);

          // Afficher une notification de succès
          setNotification({
            open: true,
            message: 'Le devis a été refusé',
            severity: 'info'
          });

          setLoading(false);
          setRejectReason('');
        } catch (reloadError) {
          console.error('Erreur lors du rechargement du devis:', reloadError);

          // Essayer de recharger la page complète si le rechargement du devis échoue
          try {
            const documentData = await devisService.getDevisById(id);
            if (documentData) {
              setDocument(documentData);
              setNotification({
                open: true,
                message: 'Le devis a été refusé',
                severity: 'info'
              });
            } else {
              setError(`Le devis a été refusé mais impossible de recharger les détails.`);
            }
          } catch (secondError) {
            setError(`Le devis a été refusé mais impossible de recharger les détails. ${reloadError.message}`);
          }

          setLoading(false);
          setRejectReason('');
        }
      }, 2000); // Augmenter le délai à 2 secondes
    } catch (error) {
      console.error('Erreur lors du refus du devis:', error);

      // Message d'erreur plus convivial
      let errorMessage = 'Impossible de refuser le devis.';

      if (error.message && error.message.includes('Network Error')) {
        errorMessage += ' Problème de connexion au serveur. Veuillez vérifier votre connexion et réessayer.';
      } else if (error.customMessage) {
        errorMessage += ` ${error.customMessage}`;
      } else if (error.message) {
        errorMessage += ` ${error.message}`;
      }

      setError(errorMessage);
      setLoading(false);

      // Afficher une notification d'erreur
      setNotification({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    }
  };

  // Fermer la notification
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  const handlePayFacture = () => {
    navigate(`/entreprise/paiements/new/${id}`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error" variant="h6">{error}</Typography>
        <Button variant="outlined" onClick={handleBack} sx={{ mt: 2 }}>
          Retour
        </Button>
      </Box>
    );
  }

  if (!document) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">Document non trouvé</Typography>
        <Button variant="outlined" onClick={handleBack} sx={{ mt: 2 }}>
          Retour
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBack} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
            {type === 'devis' ? 'Détails du devis' : 'Détails de la facture'}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {type === 'devis' &&
           !['ACCEPTED', 'REJECTED', 'ACCEPTÉ', 'REFUSÉ'].includes(document.statut) && (
            <>
              <Button
                variant="outlined"
                color="error"
                startIcon={<CancelIcon />}
                onClick={handleOpenRejectDialog}
              >
                Refuser
              </Button>
              <Button
                variant="contained"
                color="success"
                startIcon={<CheckCircleIcon />}
                onClick={handleOpenAcceptDialog}
              >
                Accepter
              </Button>
            </>
          )}
          {type === 'facture' && document.statut !== 'PAID' && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<PaymentIcon />}
              onClick={handlePayFacture}
            >
              Payer
            </Button>
          )}
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Informations" />
          <Tab label="Aperçu" />
        </Tabs>
      </Paper>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Informations générales
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {type === 'devis' ? 'Numéro de devis' : 'Numéro de facture'}
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {type === 'devis' ? document.numéro : document.numero}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date d'émission
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {formatDate(type === 'devis' ? document.dateCréation : document.dateEmission)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {type === 'devis' ? 'Date de validité' : 'Date d\'échéance'}
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {formatDate(type === 'devis' ? document.dateValidite : document.dateEcheance)}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Statut
                  </Typography>
                  <Chip
                    label={formatStatut(document.statut)}
                    color={
                      ['ACCEPTED', 'ACCEPTÉ', 'PAID', 'PAYÉ'].includes(document.statut) ? 'success' :
                      ['REJECTED', 'REFUSÉ'].includes(document.statut) ? 'error' :
                      ['SENT', 'ENVOYÉ'].includes(document.statut) ? 'primary' : 'warning'
                    }
                    sx={{ mt: 1 }}
                  />
                </Grid>
              </Grid>
            </Paper>

            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Produits et services
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Description</TableCell>
                      <TableCell align="right">Quantité</TableCell>
                      <TableCell align="right">Prix unitaire HT</TableCell>
                      <TableCell align="right">Total HT</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {document.lignes && document.lignes.length > 0 ? (
                      document.lignes.map((ligne, index) => (
                        <TableRow key={index}>
                          <TableCell>{ligne.description}</TableCell>
                          <TableCell align="right">{ligne.quantite}</TableCell>
                          <TableCell align="right">{formatCurrency(ligne.prixUnitaire)}</TableCell>
                          <TableCell align="right">{formatCurrency(ligne.montantHT)}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} align="center">
                          Aucun produit ou service
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Grid container spacing={1} sx={{ maxWidth: 300 }}>
                  <Grid item xs={6}>
                    <Typography variant="body1">Total HT:</Typography>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1">{formatCurrency(document.montantHT || 0)}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body1">TVA ({document.tauxTVA}%):</Typography>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1">
                      {formatCurrency((document.montantHT || 0) * (document.tauxTVA / 100))}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>Total TTC:</Typography>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      {formatCurrency(document.montantTTC || document.total || 0)}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Paper>

            {document.notes && (
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Notes
                </Typography>
                <Divider sx={{ mb: 3 }} />
                <Typography variant="body1">{document.notes}</Typography>
              </Paper>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Émetteur
              </Typography>
              <Divider sx={{ mb: 3 }} />

              {companyData && (
                <>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {companyData.nomEntreprise}
                  </Typography>
                  <Typography variant="body2">
                    {companyData.adresseEntreprise}<br />
                    {companyData.codePostal} {companyData.ville}<br />
                    {companyData.pays}
                  </Typography>

                  {companyData.email && (
                    <Typography variant="body2" sx={{ mt: 2 }}>
                      Email: {companyData.email}
                    </Typography>
                  )}

                  {companyData.telephone && (
                    <Typography variant="body2">
                      Téléphone: {companyData.telephone}
                    </Typography>
                  )}
                </>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <DocumentPreview
          type={type}
          companyData={companyData}
          document={document}
        />
      )}

      {/* Dialogue de confirmation pour accepter un devis */}
      <Dialog
        open={openAcceptDialog}
        onClose={handleCloseAcceptDialog}
        aria-labelledby="accept-dialog-title"
        aria-describedby="accept-dialog-description"
      >
        <DialogTitle id="accept-dialog-title">
          Confirmer l'acceptation du devis
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="accept-dialog-description">
            Êtes-vous sûr de vouloir accepter ce devis ?
            Une fois accepté, le vendeur pourra le convertir en facture.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAcceptDialog} color="primary">
            Annuler
          </Button>
          <Button onClick={handleAcceptDevis} color="success" variant="contained" autoFocus>
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation pour refuser un devis */}
      <Dialog
        open={openRejectDialog}
        onClose={handleCloseRejectDialog}
        aria-labelledby="reject-dialog-title"
        aria-describedby="reject-dialog-description"
      >
        <DialogTitle id="reject-dialog-title">
          Confirmer le refus du devis
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="reject-dialog-description">
            Êtes-vous sûr de vouloir refuser ce devis ?
            Veuillez indiquer la raison de votre refus (optionnel).
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="reason"
            label="Raison du refus"
            type="text"
            fullWidth
            multiline
            rows={4}
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRejectDialog} color="primary">
            Annuler
          </Button>
          <Button onClick={handleRejectDevis} color="error" variant="contained">
            Refuser le devis
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EntrepriseDocumentView;
