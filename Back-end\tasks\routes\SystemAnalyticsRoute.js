/**
 * System Analytics Routes
 *
 * This file contains routes for system-level analytics:
 * - System metrics (users, documents, storage)
 * - System health (uptime, performance, errors)
 */

const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { verifyToken, isAdmin } = require('../middleware/authMiddleware');
const os = require('os');

// Import models
const User = require('../models/UserModel');
const Facture = require('../models/FactureModel');
const Devis = require('../models/DevisModel');
const Client = require('../models/ClientModel');
const Produit = require('../models/ProduitModel');
const Paiement = require('../models/PaiementModel');

// Helper function to get date range based on period
const getDateRange = (period, targetDate = new Date()) => {
  const now = targetDate;
  let startDate, endDate;

  switch (period) {
    case 'daily':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case 'weekly':
      const day = now.getDay();
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day);
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - day));
      break;
    case 'monthly':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      break;
    case 'quarterly':
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      endDate = new Date(now.getFullYear(), (quarter + 1) * 3, 0);
      break;
    case 'yearly':
      startDate = new Date(now.getFullYear(), 0, 1);
      endDate = new Date(now.getFullYear() + 1, 0, 0);
      break;
    case 'all-time':
    default:
      startDate = new Date(2000, 0, 1);
      endDate = new Date(now.getFullYear() + 1, 0, 0);
  }

  return { startDate, endDate };
};

/**
 * @route GET /api/analytics/system-metrics
 * @desc Get system metrics (users, documents, storage)
 * @access Private (Admin only)
 */
router.get('/analytics/system-metrics', verifyToken, isAdmin, async (req, res) => {
  try {
    const { period = 'all-time' } = req.query;
    const { startDate, endDate } = getDateRange(period);

    // Get user metrics
    const totalUsers = await User.countDocuments();
    const newUsers = await User.countDocuments({
      dateCreation: { $gte: startDate, $lte: endDate }
    });

    // Get user distribution by role
    const usersByRole = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    // Format user distribution for frontend
    const userDistribution = usersByRole.map(item => ({
      name: item._id === 'ADMIN' ? 'Administrateurs' :
            item._id === 'ENTREPRISE' ? 'Entreprises' : 'Vendeurs',
      value: item.count,
      color: item._id === 'ADMIN' ? '#FF9800' :
             item._id === 'ENTREPRISE' ? '#2196F3' : '#4CAF50'
    }));

    // Get document metrics
    const totalInvoices = await Facture.countDocuments();
    const totalQuotes = await Devis.countDocuments();
    const totalClients = await Client.countDocuments();
    const totalProducts = await Produit.countDocuments();
    const totalPayments = await Paiement.countDocuments();

    // Get new documents in the period
    const newInvoices = await Facture.countDocuments({
      createdAt: { $gte: startDate, $lte: endDate }
    });
    const newQuotes = await Devis.countDocuments({
      dateCréation: { $gte: startDate, $lte: endDate }
    });
    const newClients = await Client.countDocuments({
      createdAt: { $gte: startDate, $lte: endDate }
    });

    // Calculate total revenue
    const revenueData = await Paiement.aggregate([
      {
        $match: {
          datePaiement: { $gte: startDate, $lte: endDate },
          statut: 'COMPLETED'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$montant' }
        }
      }
    ]);
    const totalRevenue = revenueData.length > 0 ? revenueData[0].total : 0;

    // Document distribution for visualization
    const documentDistribution = [
      { name: 'Factures', value: totalInvoices, color: '#4CAF50' },
      { name: 'Devis', value: totalQuotes, color: '#2196F3' },
      { name: 'Clients', value: totalClients, color: '#FF9800' },
      { name: 'Produits', value: totalProducts, color: '#9C27B0' }
    ];

    // Prepare response
    const metrics = {
      users: {
        total: totalUsers,
        new: newUsers,
        distribution: userDistribution
      },
      documents: {
        invoices: { total: totalInvoices, new: newInvoices },
        quotes: { total: totalQuotes, new: newQuotes },
        clients: { total: totalClients, new: newClients },
        products: { total: totalProducts },
        payments: { total: totalPayments },
        distribution: documentDistribution
      },
      revenue: {
        total: totalRevenue
      },
      period: {
        name: period,
        startDate,
        endDate
      }
    };

    res.status(200).json(metrics);
  } catch (error) {
    console.error('Error fetching system metrics:', error);
    res.status(500).json({
      error: 'Error fetching system metrics',
      message: error.message
    });
  }
});

/**
 * @route GET /api/analytics/system-health
 * @desc Get system health information (uptime, memory usage, etc.)
 * @access Private (Admin only)
 */
router.get('/analytics/system-health', verifyToken, isAdmin, async (req, res) => {
  try {
    // Get system uptime
    const uptimeSeconds = process.uptime();
    const uptimeDays = Math.floor(uptimeSeconds / 86400);
    const uptimeHours = Math.floor((uptimeSeconds % 86400) / 3600);
    const uptimeMinutes = Math.floor((uptimeSeconds % 3600) / 60);

    // Format uptime
    const uptime = {
      days: uptimeDays,
      hours: uptimeHours,
      minutes: uptimeMinutes,
      seconds: Math.floor(uptimeSeconds % 60),
      formatted: `${uptimeDays}d ${uptimeHours}h ${uptimeMinutes}m`
    };

    // Get memory usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = Math.round((usedMemory / totalMemory) * 100);

    // Format memory usage
    const memory = {
      total: Math.round(totalMemory / (1024 * 1024 * 1024) * 100) / 100, // GB
      free: Math.round(freeMemory / (1024 * 1024 * 1024) * 100) / 100, // GB
      used: Math.round(usedMemory / (1024 * 1024 * 1024) * 100) / 100, // GB
      usagePercent: memoryUsagePercent
    };

    // Get CPU info
    const cpus = os.cpus();
    const cpuCount = cpus.length;
    const cpuModel = cpus[0].model;
    const cpuSpeed = cpus[0].speed;

    // Get MongoDB connection status
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // Prepare response
    const health = {
      status: 'healthy', // Default status
      uptime,
      memory,
      cpu: {
        count: cpuCount,
        model: cpuModel,
        speed: cpuSpeed
      },
      database: {
        status: dbStatus,
        name: mongoose.connection.name || 'facturesapp'
      },
      os: {
        type: os.type(),
        platform: os.platform(),
        release: os.release()
      }
    };

    res.status(200).json(health);
  } catch (error) {
    console.error('Error fetching system health:', error);
    res.status(500).json({
      error: 'Error fetching system health',
      message: error.message
    });
  }
});

module.exports = router;
