{"version": 3, "sources": ["../../../src/lib/memory/gc-observer.ts"], "sourcesContent": ["import { PerformanceObserver } from 'perf_hooks'\nimport { warn } from '../../build/output/log'\nimport { bold } from '../picocolors'\n\nconst LONG_RUNNING_GC_THRESHOLD_MS = 15\n\nconst gcEvents: PerformanceEntry[] = []\nconst obs = new PerformanceObserver((list) => {\n  const entry = list.getEntries()[0]\n  gcEvents.push(entry)\n\n  if (entry.duration > LONG_RUNNING_GC_THRESHOLD_MS) {\n    warn(bold(`Long running GC detected: ${entry.duration.toFixed(2)}ms`))\n  }\n})\n\n/**\n * Starts recording garbage collection events in the process and warn on long\n * running GCs. To disable, call `stopObservingGc`.\n */\nexport function startObservingGc() {\n  obs.observe({ entryTypes: ['gc'] })\n}\n\nexport function stopObservingGc() {\n  obs.disconnect()\n}\n\n/**\n * Returns all recorded garbage collection events. This function will only\n * return information from when `startObservingGc` was enabled and before\n * `stopObservingGc` was called.\n */\nexport function getGcEvents() {\n  return gcEvents\n}\n"], "names": ["getGcEvents", "startObservingGc", "stopObservingGc", "LONG_RUNNING_GC_THRESHOLD_MS", "gcEvents", "obs", "PerformanceObserver", "list", "entry", "getEntries", "push", "duration", "warn", "bold", "toFixed", "observe", "entryTypes", "disconnect"], "mappings": ";;;;;;;;;;;;;;;;IAiCgBA,WAAW;eAAXA;;IAbAC,gBAAgB;eAAhBA;;IAIAC,eAAe;eAAfA;;;4BAxBoB;qBACf;4BACA;AAErB,MAAMC,+BAA+B;AAErC,MAAMC,WAA+B,EAAE;AACvC,MAAMC,MAAM,IAAIC,+BAAmB,CAAC,CAACC;IACnC,MAAMC,QAAQD,KAAKE,UAAU,EAAE,CAAC,EAAE;IAClCL,SAASM,IAAI,CAACF;IAEd,IAAIA,MAAMG,QAAQ,GAAGR,8BAA8B;QACjDS,IAAAA,SAAI,EAACC,IAAAA,gBAAI,EAAC,CAAC,0BAA0B,EAAEL,MAAMG,QAAQ,CAACG,OAAO,CAAC,GAAG,EAAE,CAAC;IACtE;AACF;AAMO,SAASb;IACdI,IAAIU,OAAO,CAAC;QAAEC,YAAY;YAAC;SAAK;IAAC;AACnC;AAEO,SAASd;IACdG,IAAIY,UAAU;AAChB;AAOO,SAASjB;IACd,OAAOI;AACT"}