import { useState } from "react"
import { Link, useLocation } from "react-router-dom"
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Divider,
  IconButton,
  Typography,
  Avatar,
  useTheme,
  useMediaQuery,
  Tooltip,
  Badge,
} from "@mui/material"
import {
  Dashboard as DashboardIcon,
  Receipt as ReceiptIcon,
  People as PeopleIcon,
  Inventory as InventoryIcon,
  Settings as SettingsIcon,
  Business as BusinessIcon,
  ExpandLess,
  ExpandMore,
  Menu as MenuIcon,
  DesignServices as TemplateIcon,
  ChevronRight as ChevronRightIcon,
  ChevronLeft as ChevronLeftIcon,
} from "@mui/icons-material"
import { styled } from "@mui/material/styles"

const drawerWidth = 260
const miniDrawerWidth = 70

const LogoContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(3),
  height: "72px",
  backgroundColor: theme.palette.mode === "dark"
    ? "rgba(30, 36, 48, 0.9)"
    : "#ffffff",
  borderBottom: `1px solid ${theme.palette.mode === "dark"
    ? "rgba(255, 255, 255, 0.05)"
    : "rgba(0, 0, 0, 0.05)"}`,
  color: theme.palette.text.primary,
  position: "sticky",
  top: 0,
  zIndex: 10,
}))

const StyledDrawer = styled(Drawer)(({ theme, open, mini }) => ({
  width: mini ? miniDrawerWidth : drawerWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  overflowX: 'hidden',
  overflowY: 'hidden',
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  "& .MuiDrawer-paper": {
    width: mini ? miniDrawerWidth : drawerWidth,
    boxSizing: "border-box",
    backgroundColor: theme.palette.mode === "dark"
      ? theme.palette.background.paper
      : "#ffffff",
    color: theme.palette.text.primary,
    borderRight: 'none',
    boxShadow: theme.palette.mode === "dark"
      ? "none"
      : "0 4px 20px rgba(0, 0, 0, 0.03)",
    overflowX: 'hidden',
    overflowY: 'hidden',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
}))

const StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  margin: "4px 10px",
  padding: "10px 16px",
  borderRadius: "12px",
  color: active
    ? theme.palette.primary.main
    : theme.palette.text.primary,
  backgroundColor: active
    ? theme.palette.mode === "dark"
      ? "rgba(58, 110, 165, 0.15)"
      : "rgba(58, 110, 165, 0.08)"
    : "transparent",
  transition: "all 0.2s ease",
  "&:hover": {
    backgroundColor: theme.palette.mode === "dark"
      ? "rgba(255, 255, 255, 0.05)"
      : "rgba(0, 0, 0, 0.03)",
    transform: "translateX(5px)",
  },
}))

const StyledListItemIcon = styled(ListItemIcon)(({ theme, active }) => ({
  color: active
    ? theme.palette.primary.main
    : theme.palette.text.secondary,
  minWidth: "36px",
  transition: "color 0.2s ease",
}))

const StyledListItemText = styled(ListItemText)(({ theme, active }) => ({
  "& .MuiTypography-root": {
    fontSize: "0.95rem",
    fontWeight: active ? 600 : 500,
    letterSpacing: "0.01em",
  },
}))

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: "0.75rem",
  fontWeight: 700,
  textTransform: "uppercase",
  letterSpacing: "0.07em",
  color: theme.palette.text.secondary,
  padding: theme.spacing(0, 3, 1),
  marginTop: theme.spacing(2),
}))

const Sidebar = ({ darkMode }) => {
  const location = useLocation()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down("md"))
  const [mini, setMini] = useState(false)
  const [open, setOpen] = useState(true)
  const [documentsOpen, setDocumentsOpen] = useState(true)
  const [mobileOpen, setMobileOpen] = useState(false)

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMiniDrawerToggle = () => {
    setMini(!mini)
  }

  const handleDocumentsClick = () => {
    setDocumentsOpen(!documentsOpen)
  }

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`)
  }

  const drawer = (
    <>
      <LogoContainer>
        <IconButton
          onClick={handleMiniDrawerToggle}
          sx={{
            mr: 1,
            color: theme.palette.primary.main,
          }}
        >
          {mini ? <MenuIcon /> : <ChevronLeftIcon />}
        </IconButton>
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            color: "#fff",
            width: 38,
            height: 38,
            boxShadow: "0 3px 10px rgba(58, 110, 165, 0.2)",
            mr: 2,
            display: mini ? 'none' : 'flex'
          }}
        >
          A
        </Avatar>
        {!mini && (
          <Typography variant="h6" component="div" sx={{
            flexGrow: 1,
            fontWeight: 700,
            background: darkMode
              ? "linear-gradient(90deg, #4f83b9, #3a6ea5)"
              : "linear-gradient(90deg, #3a6ea5, #2a5080)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}>
            Abby Pro
          </Typography>
        )}
      </LogoContainer>

      <Box sx={{
        overflowY: "hidden",
        height: "calc(100vh - 72px)",
      }}>
        <List component="nav" sx={{ p: mini ? 1 : 2 }}>
          {!mini && <SectionTitle>Principal</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Analyses" : ""} placement="right">
              <StyledListItemButton component={Link} to="/" active={isActive("/")} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <DashboardIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Analyses" active={isActive("/")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Facturation" : ""} placement="right">
              <StyledListItemButton onClick={handleDocumentsClick} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/factures") || isActive("/devis")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <ReceiptIcon />
                </StyledListItemIcon>
                {!mini && (
                  <>
                    <StyledListItemText
                      primary="Facturation"
                      active={isActive("/factures") || isActive("/devis")}
                    />
                    {documentsOpen ? <ExpandLess /> : <ExpandMore />}
                  </>
                )}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <Collapse in={documentsOpen && !mini} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              <ListItem disablePadding sx={{ mb: 0.5 }}>
                <StyledListItemButton
                  component={Link}
                  to="/factures"
                  active={isActive("/factures")}
                  sx={{
                    pl: 4,
                    ml: 3,
                    borderLeft: `2px solid ${isActive("/factures")
                      ? theme.palette.primary.main
                      : "transparent"}`,
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                  }}
                >
                  <StyledListItemIcon active={isActive("/factures")}>
                    <ChevronRightIcon fontSize="small" />
                  </StyledListItemIcon>
                  <StyledListItemText primary="Factures" active={isActive("/factures")} />
                  <Badge
                    badgeContent={4}
                    color="secondary"
                    sx={{ ml: 1 }}
                    size="small"
                  />
                </StyledListItemButton>
              </ListItem>

              <ListItem disablePadding>
                <StyledListItemButton
                  component={Link}
                  to="/devis"
                  active={isActive("/devis")}
                  sx={{
                    pl: 4,
                    ml: 3,
                    borderLeft: `2px solid ${isActive("/devis")
                      ? theme.palette.primary.main
                      : "transparent"}`,
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                  }}
                >
                  <StyledListItemIcon active={isActive("/devis")}>
                    <ChevronRightIcon fontSize="small" />
                  </StyledListItemIcon>
                  <StyledListItemText primary="Devis" active={isActive("/devis")} />
                  <Badge
                    badgeContent={2}
                    color="info"
                    sx={{ ml: 1 }}
                    size="small"
                  />
                </StyledListItemButton>
              </ListItem>
            </List>
          </Collapse>

          {mini && documentsOpen && (
            <>
              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title="Factures" placement="right">
                  <StyledListItemButton
                    component={Link}
                    to="/factures"
                    active={isActive("/factures")}
                    sx={{
                      justifyContent: 'center',
                      px: 2
                    }}
                  >
                    <StyledListItemIcon active={isActive("/factures")} sx={{ minWidth: 0 }}>
                      <ReceiptIcon fontSize="small" />
                    </StyledListItemIcon>
                    <Badge
                      badgeContent={4}
                      color="secondary"
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8
                      }}
                      size="small"
                    />
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>

              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title="Devis" placement="right">
                  <StyledListItemButton
                    component={Link}
                    to="/devis"
                    active={isActive("/devis")}
                    sx={{
                      justifyContent: 'center',
                      px: 2
                    }}
                  >
                    <StyledListItemIcon active={isActive("/devis")} sx={{ minWidth: 0 }}>
                      <ReceiptIcon fontSize="small" />
                    </StyledListItemIcon>
                    <Badge
                      badgeContent={2}
                      color="info"
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8
                      }}
                      size="small"
                    />
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>
            </>
          )}

          {!mini && <SectionTitle sx={{ mt: 3 }}>Gestion</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Clients" : ""} placement="right">
              <StyledListItemButton component={Link} to="/clients" active={isActive("/clients")} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/clients")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <PeopleIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Clients" active={isActive("/clients")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Produits" : ""} placement="right">
              <StyledListItemButton component={Link} to="/produits" active={isActive("/produits")} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/produits")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <InventoryIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Produits" active={isActive("/produits")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && <SectionTitle sx={{ mt: 3 }}>Personnalisation</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Templates" : ""} placement="right">
              <StyledListItemButton component={Link} to="/templates" active={isActive("/templates")} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/templates")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <TemplateIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Templates" active={isActive("/templates")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Entreprise" : ""} placement="right">
              <StyledListItemButton component={Link} to="/entreprise" active={isActive("/entreprise")} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/entreprise")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <BusinessIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Entreprise" active={isActive("/entreprise")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && <Divider sx={{ my: 2, opacity: 0.6 }} />}

          <ListItem disablePadding>
            <Tooltip title={mini ? "Paramètres" : ""} placement="right">
              <StyledListItemButton component={Link} to="/parametres" active={isActive("/parametres")} sx={{
                justifyContent: mini ? 'center' : 'flex-start',
                px: mini ? 2 : 3
              }}>
                <StyledListItemIcon active={isActive("/parametres")} sx={{ minWidth: mini ? 0 : 36 }}>
                  <SettingsIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Paramètres" active={isActive("/parametres")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>
        </List>

        {!mini && (
          <Box sx={{ p: 3, mt: 'auto' }}>
            <Box
              sx={{
                p: 2,
                borderRadius: 3,
                bgcolor: theme.palette.mode === "dark"
                  ? "rgba(58, 110, 165, 0.12)"
                  : "rgba(58, 110, 165, 0.06)",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                textAlign: "center",
                mt: 2
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                Besoin d'aide?
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Contactez notre support pour toute question
              </Typography>
              <IconButton
                size="small"
                sx={{
                  bgcolor: theme.palette.primary.main,
                  color: "white",
                  '&:hover': {
                    bgcolor: theme.palette.primary.dark,
                  },
                  mb: 1
                }}
              >
                <ChevronRightIcon />
              </IconButton>
            </Box>
          </Box>
        )}
      </Box>
    </>
  )

  return (
    <>
      {isMobile ? (
        <>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              position: "fixed",
              top: 12,
              left: 12,
              zIndex: 1300,
              backgroundColor: theme.palette.background.paper,
              boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
              display: { md: "none" },
            }}
          >
            <MenuIcon />
          </IconButton>
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: "block", md: "none" },
              "& .MuiDrawer-paper": {
                width: drawerWidth,
                backgroundColor: theme.palette.background.paper,
                color: theme.palette.text.primary,
                overflowY: 'hidden',
              },
            }}
          >
            {drawer}
          </Drawer>
        </>
      ) : (
        <StyledDrawer variant="permanent" open={open} mini={mini}>
          {drawer}
        </StyledDrawer>
      )}
    </>
  )
}

export default Sidebar