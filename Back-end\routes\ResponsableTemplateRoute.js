const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const ResponsableTemplate = require('../models/ResponsableTemplateModel');
const BaseTemplate = require('../models/BaseTemplateModel');
const { verifyToken } = require('../middleware/authMiddleware');

// Configure multer for logo uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/logos/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'logo-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|gif|svg/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Seuls les fichiers image sont autorisés'));
        }
    }
});

// Get responsable template settings
router.get('/responsable-templates', verifyToken, async (req, res) => {
    try {
        let entrepriseId;

        // Determine entrepriseId based on user role
        if (req.userRole === 'RESPONSABLE') {
            entrepriseId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // For vendeurs, we need to get their entrepriseId from the user record
            const User = require('../models/UserModel');
            const user = await User.findById(req.userId);
            entrepriseId = user?.entrepriseId;
        } else {
            return res.status(403).json({ error: 'Accès refusé' });
        }

        let responsableTemplate = await ResponsableTemplate.findOne({ entrepriseId })
            .populate('factureTemplate.baseTemplateId')
            .populate('devisTemplate.baseTemplateId');

        if (!responsableTemplate) {
            // Create default template settings if none exist
            const defaultFactureTemplate = await BaseTemplate.findOne({ type: 'facture', layout: 'standard' });
            const defaultDevisTemplate = await BaseTemplate.findOne({ type: 'devis', layout: 'standard' });

            if (!defaultFactureTemplate || !defaultDevisTemplate) {
                return res.status(404).json({ error: 'Templates de base par défaut non trouvés' });
            }

            responsableTemplate = new ResponsableTemplate({
                entrepriseId,
                factureTemplate: {
                    baseTemplateId: defaultFactureTemplate._id,
                    color: '#f57c00'
                },
                devisTemplate: {
                    baseTemplateId: defaultDevisTemplate._id,
                    color: '#f57c00'
                }
            });

            await responsableTemplate.save();
            await responsableTemplate.populate('factureTemplate.baseTemplateId devisTemplate.baseTemplateId');
        }

        res.status(200).json(responsableTemplate);
    } catch (error) {
        console.error('Error fetching responsable template:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des paramètres de template' });
    }
});

// Update responsable template settings
router.put('/responsable-templates', verifyToken, upload.fields([
    { name: 'factureLogo', maxCount: 1 },
    { name: 'devisLogo', maxCount: 1 }
]), async (req, res) => {
    try {
        let entrepriseId;

        // Determine entrepriseId based on user role
        if (req.userRole === 'RESPONSABLE') {
            entrepriseId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // For vendeurs, we need to get their entrepriseId from the user record
            const User = require('../models/UserModel');
            const user = await User.findById(req.userId);
            entrepriseId = user?.entrepriseId;
        } else {
            return res.status(403).json({ error: 'Accès refusé' });
        }

        const updateData = JSON.parse(req.body.templateData || '{}');

        let responsableTemplate = await ResponsableTemplate.findOne({ entrepriseId });

        if (!responsableTemplate) {
            return res.status(404).json({ error: 'Paramètres de template non trouvés' });
        }

        // Update facture template settings
        if (updateData.factureTemplate) {
            if (updateData.factureTemplate.baseTemplateId) {
                responsableTemplate.factureTemplate.baseTemplateId = updateData.factureTemplate.baseTemplateId;
            }
            if (updateData.factureTemplate.color) {
                responsableTemplate.factureTemplate.color = updateData.factureTemplate.color;
            }
        }

        // Update devis template settings
        if (updateData.devisTemplate) {
            if (updateData.devisTemplate.baseTemplateId) {
                responsableTemplate.devisTemplate.baseTemplateId = updateData.devisTemplate.baseTemplateId;
            }
            if (updateData.devisTemplate.color) {
                responsableTemplate.devisTemplate.color = updateData.devisTemplate.color;
            }
        }



        // Handle logo uploads
        if (req.files) {
            if (req.files.factureLogo && req.files.factureLogo[0]) {
                responsableTemplate.factureTemplate.logo = req.files.factureLogo[0].path;
            }
            if (req.files.devisLogo && req.files.devisLogo[0]) {
                responsableTemplate.devisTemplate.logo = req.files.devisLogo[0].path;
            }
        }

        await responsableTemplate.save();
        await responsableTemplate.populate('factureTemplate.baseTemplateId devisTemplate.baseTemplateId');

        res.status(200).json(responsableTemplate);
    } catch (error) {
        console.error('Error updating responsable template:', error);
        res.status(500).json({ error: 'Erreur lors de la mise à jour des paramètres de template' });
    }
});

// Get template settings for a specific document type
router.get('/responsable-templates/:type', verifyToken, async (req, res) => {
    try {
        const { type } = req.params;

        if (!['facture', 'devis'].includes(type)) {
            return res.status(400).json({ error: 'Type de document invalide' });
        }

        let entrepriseId;

        // Determine entrepriseId based on user role
        if (req.userRole === 'RESPONSABLE') {
            entrepriseId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // For vendeurs, we need to get their entrepriseId from the user record
            const User = require('../models/UserModel');
            const user = await User.findById(req.userId);
            entrepriseId = user?.entrepriseId;
        } else if (req.userRole === 'CLIENT') {
            // For clients, we need to find the enterprise through their vendeur
            // This requires additional logic based on your client-vendeur relationship
            return res.status(403).json({ error: 'Accès client non implémenté' });
        } else {
            return res.status(403).json({ error: 'Accès refusé' });
        }

        const responsableTemplate = await ResponsableTemplate.findOne({ entrepriseId })
            .populate(`${type}Template.baseTemplateId`);

        if (!responsableTemplate) {
            return res.status(404).json({ error: 'Paramètres de template non trouvés' });
        }

        const templateSettings = type === 'facture'
            ? responsableTemplate.factureTemplate
            : responsableTemplate.devisTemplate;

        res.status(200).json({
            ...templateSettings.toObject()
        });
    } catch (error) {
        console.error('Error fetching template settings:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des paramètres de template' });
    }
});

module.exports = router;
