{"version": 3, "sources": ["../../../../src/server/route-modules/app-route/module.ts"], "sourcesContent": ["import type { NextConfig } from '../../config-shared'\nimport type { AppRouteRouteDefinition } from '../../route-definitions/app-route-route-definition'\nimport type { AppSegmentConfig } from '../../../build/segment-config/app/app-segment-config'\nimport type { NextRequest } from '../../web/spec-extension/request'\nimport type { PrerenderManifest } from '../../../build'\nimport type { NextURL } from '../../web/next-url'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\nimport type { WorkUnitStore } from '../../app-render/work-unit-async-storage.external'\n\nimport {\n  RouteModule,\n  type RouteModuleHandleContext,\n  type RouteModuleOptions,\n} from '../route-module'\nimport { createRequestStoreForAPI } from '../../async-storage/request-store'\nimport {\n  createWorkStore,\n  type WorkStoreContext,\n} from '../../async-storage/work-store'\nimport { type HTTP_METHOD, HTTP_METHODS, isHTTPMethod } from '../../web/http'\nimport { getImplicitTags, type ImplicitTags } from '../../lib/implicit-tags'\nimport { patchFetch } from '../../lib/patch-fetch'\nimport { getTracer } from '../../lib/trace/tracer'\nimport { AppRouteRouteHandlersSpan } from '../../lib/trace/constants'\nimport { getPathnameFromAbsolutePath } from './helpers/get-pathname-from-absolute-path'\nimport * as Log from '../../../build/output/log'\nimport { autoImplementMethods } from './helpers/auto-implement-methods'\nimport {\n  appendMutableCookies,\n  type ReadonlyRequestCookies,\n} from '../../web/spec-extension/adapters/request-cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\nimport { RequestCookiesAdapter } from '../../web/spec-extension/adapters/request-cookies'\nimport { parsedUrlQueryToParams } from './helpers/parsed-url-query-to-params'\nimport { printDebugThrownValueForProspectiveRender } from '../../app-render/prospective-render-utils'\n\nimport * as serverHooks from '../../../client/components/hooks-server-context'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type RequestStore,\n  type PrerenderStore,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  actionAsyncStorage,\n  type ActionStore,\n} from '../../app-render/action-async-storage.external'\nimport * as sharedModules from './shared-modules'\nimport { getIsPossibleServerAction } from '../../lib/server-action-request-meta'\nimport { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies'\nimport { cleanURL } from './helpers/clean-url'\nimport { StaticGenBailoutError } from '../../../client/components/static-generation-bailout'\nimport { isStaticGenEnabled } from './helpers/is-static-gen-enabled'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  createDynamicTrackingState,\n  getFirstDynamicReason,\n} from '../../app-render/dynamic-rendering'\nimport { ReflectAdapter } from '../../web/spec-extension/adapters/reflect'\nimport type { RenderOptsPartial } from '../../app-render/types'\nimport { CacheSignal } from '../../app-render/cache-signal'\nimport { scheduleImmediate } from '../../../lib/scheduler'\nimport { createServerParamsForRoute } from '../../request/params'\nimport type { AppSegment } from '../../../build/segment-config/app/app-segments'\nimport {\n  getRedirectStatusCodeFromError,\n  getURLFromRedirectError,\n} from '../../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectError,\n} from '../../../client/components/redirect-error'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../../client/components/http-access-fallback/http-access-fallback'\nimport { RedirectStatusCode } from '../../../client/components/redirect-status-code'\nimport { INFINITE_CACHE } from '../../../lib/constants'\nimport { executeRevalidates } from '../../revalidation-utils'\n\nexport class WrappedNextRouterError {\n  constructor(\n    public readonly error: RedirectError,\n    public readonly headers?: Headers\n  ) {}\n}\n\n/**\n * The AppRouteModule is the type of the module exported by the bundled App\n * Route module.\n */\nexport type AppRouteModule = typeof import('../../../build/templates/app-route')\n\nexport type AppRouteSharedContext = {\n  buildId: string\n}\n\n/**\n * AppRouteRouteHandlerContext is the context that is passed to the route\n * handler for app routes.\n */\nexport interface AppRouteRouteHandlerContext extends RouteModuleHandleContext {\n  renderOpts: WorkStoreContext['renderOpts'] &\n    Pick<RenderOptsPartial, 'onInstrumentationRequestError'> &\n    CollectedCacheInfo\n  prerenderManifest: DeepReadonly<PrerenderManifest>\n  sharedContext: AppRouteSharedContext\n}\n\ntype CollectedCacheInfo = {\n  collectedTags?: string\n  collectedRevalidate?: number\n  collectedExpire?: number\n  collectedStale?: number\n}\n\n/**\n * AppRouteHandlerFnContext is the context that is passed to the handler as the\n * second argument.\n */\ntype AppRouteHandlerFnContext = {\n  params?: Promise<Record<string, string | string[] | undefined>>\n}\n\n/**\n * Handler function for app routes. If a non-Response value is returned, an error\n * will be thrown.\n */\nexport type AppRouteHandlerFn = (\n  /**\n   * Incoming request object.\n   */\n  req: NextRequest,\n  /**\n   * Context properties on the request (including the parameters if this was a\n   * dynamic route).\n   */\n  ctx: AppRouteHandlerFnContext\n) => unknown\n\n/**\n * AppRouteHandlers describes the handlers for app routes that is provided by\n * the userland module.\n */\nexport type AppRouteHandlers = {\n  [method in HTTP_METHOD]?: AppRouteHandlerFn\n}\n\n/**\n * AppRouteUserlandModule is the userland module that is provided for app\n * routes. This contains all the user generated code.\n */\nexport type AppRouteUserlandModule = AppRouteHandlers &\n  Pick<\n    AppSegmentConfig,\n    'dynamic' | 'revalidate' | 'dynamicParams' | 'fetchCache'\n  > &\n  Pick<AppSegment, 'generateStaticParams'>\n\n/**\n * AppRouteRouteModuleOptions is the options that are passed to the app route\n * module from the bundled code.\n */\nexport interface AppRouteRouteModuleOptions\n  extends RouteModuleOptions<AppRouteRouteDefinition, AppRouteUserlandModule> {\n  readonly resolvedPagePath: string\n  readonly nextConfigOutput: NextConfig['output']\n}\n\n/**\n * AppRouteRouteHandler is the handler for app routes.\n */\nexport class AppRouteRouteModule extends RouteModule<\n  AppRouteRouteDefinition,\n  AppRouteUserlandModule\n> {\n  /**\n   * A reference to the request async storage.\n   */\n  public readonly workUnitAsyncStorage = workUnitAsyncStorage\n\n  /**\n   * A reference to the static generation async storage.\n   */\n  public readonly workAsyncStorage = workAsyncStorage\n\n  /**\n   * An interface to call server hooks which interact with the underlying\n   * storage.\n   */\n  public readonly serverHooks = serverHooks\n\n  public static readonly sharedModules = sharedModules\n\n  /**\n   * A reference to the mutation related async storage, such as mutations of\n   * cookies.\n   */\n  public readonly actionAsyncStorage = actionAsyncStorage\n\n  public readonly resolvedPagePath: string\n  public readonly nextConfigOutput: NextConfig['output'] | undefined\n\n  private readonly methods: Record<HTTP_METHOD, AppRouteHandlerFn>\n  private readonly hasNonStaticMethods: boolean\n  private readonly dynamic: AppRouteUserlandModule['dynamic']\n\n  constructor({\n    userland,\n    definition,\n    resolvedPagePath,\n    nextConfigOutput,\n  }: AppRouteRouteModuleOptions) {\n    super({ userland, definition })\n\n    this.resolvedPagePath = resolvedPagePath\n    this.nextConfigOutput = nextConfigOutput\n\n    // Automatically implement some methods if they aren't implemented by the\n    // userland module.\n    this.methods = autoImplementMethods(userland)\n\n    // Get the non-static methods for this route.\n    this.hasNonStaticMethods = hasNonStaticMethods(userland)\n\n    // Get the dynamic property from the userland module.\n    this.dynamic = this.userland.dynamic\n    if (this.nextConfigOutput === 'export') {\n      if (this.dynamic === 'force-dynamic') {\n        throw new Error(\n          `export const dynamic = \"force-dynamic\" on page \"${definition.pathname}\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else if (!isStaticGenEnabled(this.userland) && this.userland['GET']) {\n        throw new Error(\n          `export const dynamic = \"force-static\"/export const revalidate not configured on route \"${definition.pathname}\" with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else {\n        this.dynamic = 'error'\n      }\n    }\n\n    // We only warn in development after here, so return if we're not in\n    // development.\n    if (process.env.NODE_ENV === 'development') {\n      // Print error in development if the exported handlers are in lowercase, only\n      // uppercase handlers are supported.\n      const lowercased = HTTP_METHODS.map((method) => method.toLowerCase())\n      for (const method of lowercased) {\n        if (method in this.userland) {\n          Log.error(\n            `Detected lowercase method '${method}' in '${\n              this.resolvedPagePath\n            }'. Export the uppercase '${method.toUpperCase()}' method name to fix this error.`\n          )\n        }\n      }\n\n      // Print error if the module exports a default handler, they must use named\n      // exports for each HTTP method.\n      if ('default' in this.userland) {\n        Log.error(\n          `Detected default export in '${this.resolvedPagePath}'. Export a named export for each HTTP method instead.`\n        )\n      }\n\n      // If there is no methods exported by this module, then return a not found\n      // response.\n      if (!HTTP_METHODS.some((method) => method in this.userland)) {\n        Log.error(\n          `No HTTP methods exported in '${this.resolvedPagePath}'. Export a named export for each HTTP method.`\n        )\n      }\n    }\n  }\n\n  /**\n   * Resolves the handler function for the given method.\n   *\n   * @param method the requested method\n   * @returns the handler function for the given method\n   */\n  private resolve(method: string): AppRouteHandlerFn {\n    // Ensure that the requested method is a valid method (to prevent RCE's).\n    if (!isHTTPMethod(method)) return () => new Response(null, { status: 400 })\n\n    // Return the handler.\n    return this.methods[method]\n  }\n\n  private async do(\n    handler: AppRouteHandlerFn,\n    actionStore: ActionStore,\n    workStore: WorkStore,\n    // @TODO refactor to not take this argument but instead construct the RequestStore\n    // inside this function. Right now we get passed a RequestStore even when\n    // we're going to do a prerender. We should probably just split do up into prexecute and execute\n    requestStore: RequestStore,\n    implicitTags: ImplicitTags,\n    request: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ) {\n    const isStaticGeneration = workStore.isStaticGeneration\n    const dynamicIOEnabled = !!context.renderOpts.experimental?.dynamicIO\n\n    // Patch the global fetch.\n    patchFetch({\n      workAsyncStorage: this.workAsyncStorage,\n      workUnitAsyncStorage: this.workUnitAsyncStorage,\n    })\n\n    const handlerContext: AppRouteHandlerFnContext = {\n      params: context.params\n        ? createServerParamsForRoute(\n            parsedUrlQueryToParams(context.params),\n            workStore\n          )\n        : undefined,\n    }\n\n    const resolvePendingRevalidations = () => {\n      context.renderOpts.pendingWaitUntil = executeRevalidates(\n        workStore\n      ).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log(\n            'pending revalidates promise finished for:',\n            requestStore.url\n          )\n        }\n      })\n    }\n\n    let prerenderStore: null | PrerenderStore = null\n\n    let res: unknown\n    try {\n      if (isStaticGeneration) {\n        const userlandRevalidate = this.userland.revalidate\n        const defaultRevalidate: number =\n          // If the static generation store does not have a revalidate value\n          // set, then we should set it the revalidate value from the userland\n          // module or default to false.\n          userlandRevalidate === false || userlandRevalidate === undefined\n            ? INFINITE_CACHE\n            : userlandRevalidate\n\n        if (dynamicIOEnabled) {\n          /**\n           * When we are attempting to statically prerender the GET handler of a route.ts module\n           * and dynamicIO is on we follow a similar pattern to rendering.\n           *\n           * We first run the handler letting caches fill. If something synchronously dynamic occurs\n           * during this prospective render then we can infer it will happen on every render and we\n           * just bail out of prerendering.\n           *\n           * Next we run the handler again and we check if we get a result back in a microtask.\n           * Next.js expects the return value to be a Response or a Thenable that resolves to a Response.\n           * Unfortunately Response's do not allow for accessing the response body synchronously or in\n           * a microtask so we need to allow one more task to unwrap the response body. This is a slightly\n           * different semantic than what we have when we render and it means that certain tasks can still\n           * execute before a prerender completes such as a carefully timed setImmediate.\n           *\n           * Functionally though IO should still take longer than the time it takes to unwrap the response body\n           * so our heuristic of excluding any IO should be preserved.\n           */\n          const prospectiveController = new AbortController()\n          let prospectiveRenderIsDynamic = false\n          const cacheSignal = new CacheSignal()\n          let dynamicTracking = createDynamicTrackingState(undefined)\n\n          const prospectiveRoutePrerenderStore: PrerenderStore =\n            (prerenderStore = {\n              type: 'prerender',\n              phase: 'action',\n              // This replicates prior behavior where rootParams is empty in routes\n              // TODO we need to make this have the proper rootParams for this route\n              rootParams: {},\n              implicitTags,\n              renderSignal: prospectiveController.signal,\n              controller: prospectiveController,\n              cacheSignal,\n              // During prospective render we don't use a controller\n              // because we need to let all caches fill.\n              dynamicTracking,\n              revalidate: defaultRevalidate,\n              expire: INFINITE_CACHE,\n              stale: INFINITE_CACHE,\n              tags: [...implicitTags.tags],\n              prerenderResumeDataCache: null,\n              hmrRefreshHash: undefined,\n            })\n\n          let prospectiveResult\n          try {\n            prospectiveResult = this.workUnitAsyncStorage.run(\n              prospectiveRoutePrerenderStore,\n              handler,\n              request,\n              handlerContext\n            )\n          } catch (err) {\n            if (prospectiveController.signal.aborted) {\n              // the route handler called an API which is always dynamic\n              // there is no need to try again\n              prospectiveRenderIsDynamic = true\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          }\n          if (\n            typeof prospectiveResult === 'object' &&\n            prospectiveResult !== null &&\n            typeof (prospectiveResult as any).then === 'function'\n          ) {\n            // The handler returned a Thenable. We'll listen for rejections to determine\n            // if the route is erroring for dynamic reasons.\n            ;(prospectiveResult as any as Promise<unknown>).then(\n              () => {},\n              (err) => {\n                if (prospectiveController.signal.aborted) {\n                  // the route handler called an API which is always dynamic\n                  // there is no need to try again\n                  prospectiveRenderIsDynamic = true\n                } else if (process.env.NEXT_DEBUG_BUILD) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              }\n            )\n          }\n          await cacheSignal.cacheReady()\n\n          if (prospectiveRenderIsDynamic) {\n            // the route handler called an API which is always dynamic\n            // there is no need to try again\n            const dynamicReason = getFirstDynamicReason(dynamicTracking)\n            if (dynamicReason) {\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            } else {\n              console.error(\n                'Expected Next.js to keep track of reason for opting out of static rendering but one was not found. This is a bug in Next.js'\n              )\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used a dynamic API. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            }\n          }\n\n          // TODO start passing this controller to the route handler. We should expose\n          // it so the handler to abort inflight requests and other operations if we abort\n          // the prerender.\n          const finalController = new AbortController()\n          dynamicTracking = createDynamicTrackingState(undefined)\n\n          const finalRoutePrerenderStore: PrerenderStore = (prerenderStore = {\n            type: 'prerender',\n            phase: 'action',\n            rootParams: {},\n            implicitTags,\n            renderSignal: finalController.signal,\n            controller: finalController,\n            cacheSignal: null,\n            dynamicTracking,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n            prerenderResumeDataCache: null,\n            hmrRefreshHash: undefined,\n          })\n\n          let responseHandled = false\n          res = await new Promise((resolve, reject) => {\n            scheduleImmediate(async () => {\n              try {\n                const result = await (this.workUnitAsyncStorage.run(\n                  finalRoutePrerenderStore,\n                  handler,\n                  request,\n                  handlerContext\n                ) as Promise<Response>)\n                if (responseHandled) {\n                  // we already rejected in the followup task\n                  return\n                } else if (!(result instanceof Response)) {\n                  // This is going to error but we let that happen below\n                  resolve(result)\n                  return\n                }\n\n                responseHandled = true\n\n                let bodyHandled = false\n                result.arrayBuffer().then((body) => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n\n                    resolve(\n                      new Response(body, {\n                        headers: result.headers,\n                        status: result.status,\n                        statusText: result.statusText,\n                      })\n                    )\n                  }\n                }, reject)\n                scheduleImmediate(() => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n                    finalController.abort()\n                    reject(createDynamicIOError(workStore.route))\n                  }\n                })\n              } catch (err) {\n                reject(err)\n              }\n            })\n            scheduleImmediate(() => {\n              if (!responseHandled) {\n                responseHandled = true\n                finalController.abort()\n                reject(createDynamicIOError(workStore.route))\n              }\n            })\n          })\n          if (finalController.signal.aborted) {\n            // We aborted from within the execution\n            throw createDynamicIOError(workStore.route)\n          } else {\n            // We didn't abort during the execution. We can abort now as a matter of semantics\n            // though at the moment nothing actually consumes this signal so it won't halt any\n            // inflight work.\n            finalController.abort()\n          }\n        } else {\n          prerenderStore = {\n            type: 'prerender-legacy',\n            phase: 'action',\n            rootParams: {},\n            implicitTags,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n          }\n\n          res = await workUnitAsyncStorage.run(\n            prerenderStore,\n            handler,\n            request,\n            handlerContext\n          )\n        }\n      } else {\n        res = await workUnitAsyncStorage.run(\n          requestStore,\n          handler,\n          request,\n          handlerContext\n        )\n      }\n    } catch (err) {\n      if (isRedirectError(err)) {\n        const url = getURLFromRedirectError(err)\n        if (!url) {\n          throw new Error('Invariant: Unexpected redirect url format')\n        }\n\n        // We need to capture any headers that should be sent on\n        // the response.\n        const headers = new Headers({ Location: url })\n\n        // Let's append any cookies that were added by the\n        // cookie API.\n        // TODO leaving the gate here b/c it indicates that we might not actually want to do this\n        // on every `do` call. During prerender there should be no mutableCookies because\n        if (requestStore.type === 'request') {\n          appendMutableCookies(headers, requestStore.mutableCookies)\n        }\n\n        resolvePendingRevalidations()\n\n        // Return the redirect response.\n        return new Response(null, {\n          // If we're in an action, we want to use a 303 redirect as we don't\n          // want the POST request to follow the redirect, as it could result in\n          // erroneous re-submissions.\n          status: actionStore.isAction\n            ? RedirectStatusCode.SeeOther\n            : getRedirectStatusCodeFromError(err),\n          headers,\n        })\n      } else if (isHTTPAccessFallbackError(err)) {\n        const httpStatus = getAccessFallbackHTTPStatus(err)\n        return new Response(null, { status: httpStatus })\n      }\n\n      throw err\n    }\n\n    // Validate that the response is a valid response object.\n    if (!(res instanceof Response)) {\n      throw new Error(\n        `No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \\`Response\\` or a \\`NextResponse\\` in all branches of your handler.`\n      )\n    }\n\n    context.renderOpts.fetchMetrics = workStore.fetchMetrics\n\n    resolvePendingRevalidations()\n\n    if (prerenderStore) {\n      context.renderOpts.collectedTags = prerenderStore.tags?.join(',')\n      context.renderOpts.collectedRevalidate = prerenderStore.revalidate\n      context.renderOpts.collectedExpire = prerenderStore.expire\n      context.renderOpts.collectedStale = prerenderStore.stale\n    }\n\n    // It's possible cookies were set in the handler, so we need\n    // to merge the modified cookies and the returned response\n    // here.\n    const headers = new Headers(res.headers)\n    if (\n      requestStore.type === 'request' &&\n      appendMutableCookies(headers, requestStore.mutableCookies)\n    ) {\n      return new Response(res.body, {\n        status: res.status,\n        statusText: res.statusText,\n        headers,\n      })\n    }\n\n    return res\n  }\n\n  public async handle(\n    req: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ): Promise<Response> {\n    // Get the handler function for the given method.\n    const handler = this.resolve(req.method)\n\n    // Get the context for the static generation.\n    const staticGenerationContext: WorkStoreContext = {\n      // App Routes don't support unknown route params.\n      fallbackRouteParams: null,\n      page: this.definition.page,\n      renderOpts: context.renderOpts,\n      buildId: context.sharedContext.buildId,\n      previouslyRevalidatedTags: [],\n    }\n\n    // Add the fetchCache option to the renderOpts.\n    staticGenerationContext.renderOpts.fetchCache = this.userland.fetchCache\n\n    const actionStore: ActionStore = {\n      isAppRoute: true,\n      isAction: getIsPossibleServerAction(req),\n    }\n\n    const implicitTags = await getImplicitTags(\n      this.definition.page,\n      req.nextUrl,\n      // App Routes don't support unknown route params.\n      null\n    )\n\n    const requestStore = createRequestStoreForAPI(\n      req,\n      req.nextUrl,\n      implicitTags,\n      undefined,\n      context.prerenderManifest.preview\n    )\n\n    const workStore = createWorkStore(staticGenerationContext)\n\n    // Run the handler with the request AsyncLocalStorage to inject the helper\n    // support. We set this to `unknown` because the type is not known until\n    // runtime when we do a instanceof check below.\n    const response: unknown = await this.actionAsyncStorage.run(\n      actionStore,\n      () =>\n        this.workUnitAsyncStorage.run(requestStore, () =>\n          this.workAsyncStorage.run(workStore, async () => {\n            // Check to see if we should bail out of static generation based on\n            // having non-static methods.\n            if (this.hasNonStaticMethods) {\n              if (workStore.isStaticGeneration) {\n                const err = new DynamicServerError(\n                  'Route is configured with methods that cannot be statically generated.'\n                )\n                workStore.dynamicUsageDescription = err.message\n                workStore.dynamicUsageStack = err.stack\n                throw err\n              }\n            }\n\n            // We assume we can pass the original request through however we may end up\n            // proxying it in certain circumstances based on execution type and configuration\n            let request = req\n\n            // Update the static generation store based on the dynamic property.\n            switch (this.dynamic) {\n              case 'force-dynamic': {\n                // Routes of generated paths should be dynamic\n                workStore.forceDynamic = true\n                break\n              }\n              case 'force-static':\n                // The dynamic property is set to force-static, so we should\n                // force the page to be static.\n                workStore.forceStatic = true\n                // We also Proxy the request to replace dynamic data on the request\n                // with empty stubs to allow for safely executing as static\n                request = new Proxy(req, forceStaticRequestHandlers)\n                break\n              case 'error':\n                // The dynamic property is set to error, so we should throw an\n                // error if the page is being statically generated.\n                workStore.dynamicShouldError = true\n                if (workStore.isStaticGeneration)\n                  request = new Proxy(req, requireStaticRequestHandlers)\n                break\n              default:\n                // We proxy `NextRequest` to track dynamic access, and potentially bail out of static generation\n                request = proxyNextRequest(req, workStore)\n            }\n\n            // TODO: propagate this pathname from route matcher\n            const route = getPathnameFromAbsolutePath(this.resolvedPagePath)\n\n            const tracer = getTracer()\n\n            // Update the root span attribute for the route.\n            tracer.setRootSpanAttribute('next.route', route)\n\n            return tracer.trace(\n              AppRouteRouteHandlersSpan.runHandler,\n              {\n                spanName: `executing api route (app) ${route}`,\n                attributes: {\n                  'next.route': route,\n                },\n              },\n              async () =>\n                this.do(\n                  handler,\n                  actionStore,\n                  workStore,\n                  requestStore,\n                  implicitTags,\n                  request,\n                  context\n                )\n            )\n          })\n        )\n    )\n\n    // If the handler did't return a valid response, then return the internal\n    // error response.\n    if (!(response instanceof Response)) {\n      // TODO: validate the correct handling behavior, maybe log something?\n      return new Response(null, { status: 500 })\n    }\n\n    if (response.headers.has('x-middleware-rewrite')) {\n      throw new Error(\n        'NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue.'\n      )\n    }\n\n    if (response.headers.get('x-middleware-next') === '1') {\n      // TODO: move this error into the `NextResponse.next()` function.\n      throw new Error(\n        'NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler'\n      )\n    }\n\n    return response\n  }\n}\n\nexport default AppRouteRouteModule\n\n/**\n * Gets all the method names for handlers that are not considered static.\n *\n * @param handlers the handlers from the userland module\n * @returns the method names that are not considered static or false if all\n *          methods are static\n */\nexport function hasNonStaticMethods(handlers: AppRouteHandlers): boolean {\n  if (\n    // Order these by how common they are to be used\n    handlers.POST ||\n    handlers.PUT ||\n    handlers.DELETE ||\n    handlers.PATCH ||\n    handlers.OPTIONS\n  ) {\n    return true\n  }\n  return false\n}\n\n// These symbols will be used to stash cached values on Proxied requests without requiring\n// additional closures or storage such as WeakMaps.\nconst nextURLSymbol = Symbol('nextUrl')\nconst requestCloneSymbol = Symbol('clone')\nconst urlCloneSymbol = Symbol('clone')\nconst searchParamsSymbol = Symbol('searchParams')\nconst hrefSymbol = Symbol('href')\nconst toStringSymbol = Symbol('toString')\nconst headersSymbol = Symbol('headers')\nconst cookiesSymbol = Symbol('cookies')\n\ntype RequestSymbolTarget = {\n  [headersSymbol]?: Headers\n  [cookiesSymbol]?: RequestCookies | ReadonlyRequestCookies\n  [nextURLSymbol]?: NextURL\n  [requestCloneSymbol]?: () => NextRequest\n}\n\ntype UrlSymbolTarget = {\n  [searchParamsSymbol]?: URLSearchParams\n  [hrefSymbol]?: string\n  [toStringSymbol]?: () => string\n  [urlCloneSymbol]?: () => NextURL\n}\n\n/**\n * The general technique with these proxy handlers is to prioritize keeping them static\n * by stashing computed values on the Proxy itself. This is safe because the Proxy is\n * inaccessible to the consumer since all operations are forwarded\n */\nconst forceStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'headers':\n        return (\n          target[headersSymbol] ||\n          (target[headersSymbol] = HeadersAdapter.seal(new Headers({})))\n        )\n      case 'cookies':\n        return (\n          target[cookiesSymbol] ||\n          (target[cookiesSymbol] = RequestCookiesAdapter.seal(\n            new RequestCookies(new Headers({}))\n          ))\n        )\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            forceStaticNextUrlHandlers\n          ))\n        )\n      case 'url':\n        // we don't need to separately cache this we can just read the nextUrl\n        // and return the href since we know it will have been stripped of any\n        // dynamic parts. We access via the receiver to trigger the get trap\n        return receiver.nextUrl.href\n      case 'geo':\n      case 'ip':\n        return undefined\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              forceStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst forceStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      // URL properties\n      case 'search':\n        return ''\n      case 'searchParams':\n        return (\n          target[searchParamsSymbol] ||\n          (target[searchParamsSymbol] = new URLSearchParams())\n        )\n      case 'href':\n        return (\n          target[hrefSymbol] ||\n          (target[hrefSymbol] = cleanURL(target.href).href)\n        )\n      case 'toJSON':\n      case 'toString':\n        return (\n          target[toStringSymbol] ||\n          (target[toStringSymbol] = () => receiver.href)\n        )\n\n      // NextUrl properties\n      case 'url':\n        // Currently nextURL does not expose url but our Docs indicate that it is an available property\n        // I am forcing this to undefined here to avoid accidentally exposing a dynamic value later if\n        // the underlying nextURL ends up adding this property\n        return undefined\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), forceStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction proxyNextRequest(request: NextRequest, workStore: WorkStore) {\n  const nextUrlHandlers = {\n    get(\n      target: NextURL & UrlSymbolTarget,\n      prop: string | symbol,\n      receiver: any\n    ): unknown {\n      switch (prop) {\n        case 'search':\n        case 'searchParams':\n        case 'url':\n        case 'href':\n        case 'toJSON':\n        case 'toString':\n        case 'origin': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `nextUrl.${prop}`)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'clone':\n          return (\n            target[urlCloneSymbol] ||\n            (target[urlCloneSymbol] = () =>\n              new Proxy(target.clone(), nextUrlHandlers))\n          )\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  }\n\n  const nextRequestHandlers = {\n    get(\n      target: NextRequest & RequestSymbolTarget,\n      prop: string | symbol\n    ): unknown {\n      switch (prop) {\n        case 'nextUrl':\n          return (\n            target[nextURLSymbol] ||\n            (target[nextURLSymbol] = new Proxy(target.nextUrl, nextUrlHandlers))\n          )\n        case 'headers':\n        case 'cookies':\n        case 'url':\n        case 'body':\n        case 'blob':\n        case 'json':\n        case 'text':\n        case 'arrayBuffer':\n        case 'formData': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `request.${prop}`)\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n        }\n        case 'clone':\n          return (\n            target[requestCloneSymbol] ||\n            (target[requestCloneSymbol] = () =>\n              new Proxy(\n                // This is vaguely unsafe but it's required since NextRequest does not implement\n                // clone. The reason we might expect this to work in this context is the Proxy will\n                // respond with static-amenable values anyway somewhat restoring the interface.\n                // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                // sophisticated to adequately represent themselves in all contexts. A better approach is\n                // to probably embed the static generation logic into the class itself removing the need\n                // for any kind of proxying\n                target.clone() as NextRequest,\n                nextRequestHandlers\n              ))\n          )\n        default:\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n      }\n    },\n    // We don't need to proxy set because all the properties we proxy are ready only\n    // and will be ignored\n  }\n\n  return new Proxy(request, nextRequestHandlers)\n}\n\nconst requireStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            requireStaticNextUrlHandlers\n          ))\n        )\n      case 'headers':\n      case 'cookies':\n      case 'url':\n      case 'body':\n      case 'blob':\n      case 'json':\n      case 'text':\n      case 'arrayBuffer':\n      case 'formData':\n        throw new StaticGenBailoutError(\n          `Route ${target.nextUrl.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`request.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              requireStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst requireStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'search':\n      case 'searchParams':\n      case 'url':\n      case 'href':\n      case 'toJSON':\n      case 'toString':\n      case 'origin':\n        throw new StaticGenBailoutError(\n          `Route ${target.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`nextUrl.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), requireStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction createDynamicIOError(route: string) {\n  return new DynamicServerError(\n    `Route ${route} couldn't be rendered statically because it used IO that was not cached. See more info here: https://nextjs.org/docs/messages/dynamic-io`\n  )\n}\n\nexport function trackDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | WorkUnitStore,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n      )\n    } else if (workUnitStore.type === 'unstable-cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n      )\n    }\n  }\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender') {\n      // dynamicIO Prerender\n      const error = new Error(\n        `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-request`\n      )\n      abortAndThrowOnSynchronousRequestDataAccess(\n        store.route,\n        expression,\n        error,\n        workUnitStore\n      )\n    } else if (workUnitStore.type === 'prerender-ppr') {\n      // PPR Prerender\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      // legacy Prerender\n      workUnitStore.revalidate = 0\n\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n"], "names": ["AppRouteRouteModule", "WrappedNextRouterError", "hasNonStaticMethods", "trackDynamic", "constructor", "error", "headers", "RouteModule", "sharedModules", "userland", "definition", "resolvedPagePath", "nextConfigOutput", "workUnitAsyncStorage", "workAsyncStorage", "serverHooks", "actionAsyncStorage", "methods", "autoImplementMethods", "dynamic", "Error", "pathname", "isStaticGenEnabled", "process", "env", "NODE_ENV", "lowercased", "HTTP_METHODS", "map", "method", "toLowerCase", "Log", "toUpperCase", "some", "resolve", "isHTTPMethod", "Response", "status", "do", "handler", "actionStore", "workStore", "requestStore", "implicitTags", "request", "context", "isStaticGeneration", "dynamicIOEnabled", "renderOpts", "experimental", "dynamicIO", "patchFetch", "handlerContext", "params", "createServerParamsForRoute", "parsedUrlQueryToParams", "undefined", "resolvePendingRevalidations", "pendingWaitUntil", "executeRevalidates", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "url", "prerenderStore", "res", "userlandRevalidate", "revalidate", "defaultRevalidate", "INFINITE_CACHE", "prospectiveController", "AbortController", "prospectiveRenderIsDynamic", "cacheSignal", "CacheSignal", "dynamicTracking", "createDynamicTrackingState", "prospectiveRoutePrerenderStore", "type", "phase", "rootParams", "renderSignal", "signal", "controller", "expire", "stale", "tags", "prerenderResumeDataCache", "hmrRefreshHash", "prospectiveResult", "run", "err", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "route", "then", "cacheReady", "dynamicReason", "getFirstDynamicReason", "DynamicServerError", "finalController", "finalRoutePrerenderStore", "responseHandled", "Promise", "reject", "scheduleImmediate", "result", "bodyHandled", "arrayBuffer", "body", "statusText", "abort", "createDynamicIOError", "isRedirectError", "getURLFromRedirectError", "Headers", "Location", "appendMutableCookies", "mutableCookies", "isAction", "RedirectStatusCode", "<PERSON><PERSON><PERSON>", "getRedirectStatusCodeFromError", "isHTTPAccessFallbackError", "httpStatus", "getAccessFallbackHTTPStatus", "fetchMetrics", "collectedTags", "join", "collectedRevalidate", "collectedExpire", "collectedStale", "handle", "req", "staticGenerationContext", "fallbackRouteParams", "page", "buildId", "sharedContext", "previouslyRevalidatedTags", "fetchCache", "isAppRoute", "getIsPossibleServerAction", "getImplicitTags", "nextUrl", "createRequestStoreForAPI", "prerenderManifest", "preview", "createWorkStore", "response", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceDynamic", "forceStatic", "Proxy", "forceStaticRequestHandlers", "dynamicShouldError", "requireStaticRequestHandlers", "proxyNextRequest", "getPathnameFromAbsolutePath", "tracer", "getTracer", "setRootSpanAttribute", "trace", "AppRouteRouteHandlersSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "has", "get", "handlers", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "nextURLSymbol", "Symbol", "requestCloneSymbol", "urlCloneSymbol", "searchParamsSymbol", "hrefSymbol", "toStringSymbol", "headersSymbol", "cookiesSymbol", "target", "prop", "receiver", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "RequestCookiesAdapter", "RequestCookies", "forceStaticNextUrlHandlers", "href", "clone", "ReflectAdapter", "URLSearchParams", "cleanURL", "nextUrlHandlers", "workUnitStore", "getStore", "nextRequestHandlers", "requireStaticNextUrlHandlers", "StaticGenBailoutError", "store", "expression", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "usedDynamic"], "mappings": ";;;;;;;;;;;;;;;;;;IAkLaA,mBAAmB;eAAnBA;;IA5FAC,sBAAsB;eAAtBA;;IAwsBb,OAAkC;eAAlC;;IASgBC,mBAAmB;eAAnBA;;IAkUAC,YAAY;eAAZA;;;6BA5lCT;8BACkC;2BAIlC;sBACsD;8BACV;4BACxB;wBACD;2BACgB;6CACE;6DACvB;sCACgB;gCAI9B;yBACwB;wCAEQ;wCACmB;4EAE7B;0CAMtB;8CAKA;4CAIA;uEACwB;yCACW;yBACX;0BACN;yCACa;oCACH;kCAM5B;yBACwB;6BAEH;2BACM;wBACS;0BAKpC;+BAIA;oCAIA;oCAC4B;4BACJ;mCACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,MAAMF;IACXG,YACE,AAAgBC,KAAoB,EACpC,AAAgBC,OAAiB,CACjC;aAFgBD,QAAAA;aACAC,UAAAA;IACf;AACL;AAuFO,MAAMN,4BAA4BO,wBAAW;qBAoB3BC,gBAAgBA;IAevCJ,YAAY,EACVK,QAAQ,EACRC,UAAU,EACVC,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEH;YAAUC;QAAW,IArC/B;;GAEC,QACeG,uBAAuBA,kDAAoB,EAE3D;;GAEC,QACeC,mBAAmBA,0CAAgB,EAEnD;;;GAGC,QACeC,cAAcA,qBAI9B;;;GAGC,QACeC,qBAAqBA,8CAAkB;QAiBrD,IAAI,CAACL,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACK,OAAO,GAAGC,IAAAA,0CAAoB,EAACT;QAEpC,6CAA6C;QAC7C,IAAI,CAACP,mBAAmB,GAAGA,oBAAoBO;QAE/C,qDAAqD;QACrD,IAAI,CAACU,OAAO,GAAG,IAAI,CAACV,QAAQ,CAACU,OAAO;QACpC,IAAI,IAAI,CAACP,gBAAgB,KAAK,UAAU;YACtC,IAAI,IAAI,CAACO,OAAO,KAAK,iBAAiB;gBACpC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gDAAgD,EAAEV,WAAWW,QAAQ,CAAC,wHAAwH,CAAC,GAD5L,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAI,CAACC,IAAAA,sCAAkB,EAAC,IAAI,CAACb,QAAQ,KAAK,IAAI,CAACA,QAAQ,CAAC,MAAM,EAAE;gBACrE,MAAM,qBAEL,CAFK,IAAIW,MACR,CAAC,uFAAuF,EAAEV,WAAWW,QAAQ,CAAC,yGAAyG,CAAC,GADpN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO;gBACL,IAAI,CAACF,OAAO,GAAG;YACjB;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAaC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUH,WAAY;gBAC/B,IAAIG,UAAU,IAAI,CAACpB,QAAQ,EAAE;oBAC3BsB,KAAI1B,KAAK,CACP,CAAC,2BAA2B,EAAEwB,OAAO,MAAM,EACzC,IAAI,CAAClB,gBAAgB,CACtB,yBAAyB,EAAEkB,OAAOG,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAACvB,QAAQ,EAAE;gBAC9BsB,KAAI1B,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACM,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAACgB,kBAAY,CAACM,IAAI,CAAC,CAACJ,SAAWA,UAAU,IAAI,CAACpB,QAAQ,GAAG;gBAC3DsB,KAAI1B,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACM,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQuB,QAAQL,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAACM,IAAAA,kBAAY,EAACN,SAAS,OAAO,IAAM,IAAIO,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAEzE,sBAAsB;QACtB,OAAO,IAAI,CAACpB,OAAO,CAACY,OAAO;IAC7B;IAEA,MAAcS,GACZC,OAA0B,EAC1BC,WAAwB,EACxBC,SAAoB,EACpB,kFAAkF;IAClF,yEAAyE;IACzE,gGAAgG;IAChGC,YAA0B,EAC1BC,YAA0B,EAC1BC,OAAoB,EACpBC,OAAoC,EACpC;YAE2BA;QAD3B,MAAMC,qBAAqBL,UAAUK,kBAAkB;QACvD,MAAMC,mBAAmB,CAAC,GAACF,mCAAAA,QAAQG,UAAU,CAACC,YAAY,qBAA/BJ,iCAAiCK,SAAS;QAErE,0BAA0B;QAC1BC,IAAAA,sBAAU,EAAC;YACTrC,kBAAkB,IAAI,CAACA,gBAAgB;YACvCD,sBAAsB,IAAI,CAACA,oBAAoB;QACjD;QAEA,MAAMuC,iBAA2C;YAC/CC,QAAQR,QAAQQ,MAAM,GAClBC,IAAAA,kCAA0B,EACxBC,IAAAA,8CAAsB,EAACV,QAAQQ,MAAM,GACrCZ,aAEFe;QACN;QAEA,MAAMC,8BAA8B;YAClCZ,QAAQG,UAAU,CAACU,gBAAgB,GAAGC,IAAAA,qCAAkB,EACtDlB,WACAmB,OAAO,CAAC;gBACR,IAAIrC,QAAQC,GAAG,CAACqC,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CACT,6CACArB,aAAasB,GAAG;gBAEpB;YACF;QACF;QAEA,IAAIC,iBAAwC;QAE5C,IAAIC;QACJ,IAAI;YACF,IAAIpB,oBAAoB;gBACtB,MAAMqB,qBAAqB,IAAI,CAAC1D,QAAQ,CAAC2D,UAAU;gBACnD,MAAMC,oBACJ,kEAAkE;gBAClE,oEAAoE;gBACpE,8BAA8B;gBAC9BF,uBAAuB,SAASA,uBAAuBX,YACnDc,0BAAc,GACdH;gBAEN,IAAIpB,kBAAkB;oBACpB;;;;;;;;;;;;;;;;;WAiBC,GACD,MAAMwB,wBAAwB,IAAIC;oBAClC,IAAIC,6BAA6B;oBACjC,MAAMC,cAAc,IAAIC,wBAAW;oBACnC,IAAIC,kBAAkBC,IAAAA,4CAA0B,EAACrB;oBAEjD,MAAMsB,iCACHb,iBAAiB;wBAChBc,MAAM;wBACNC,OAAO;wBACP,qEAAqE;wBACrE,sEAAsE;wBACtEC,YAAY,CAAC;wBACbtC;wBACAuC,cAAcX,sBAAsBY,MAAM;wBAC1CC,YAAYb;wBACZG;wBACA,sDAAsD;wBACtD,0CAA0C;wBAC1CE;wBACAR,YAAYC;wBACZgB,QAAQf,0BAAc;wBACtBgB,OAAOhB,0BAAc;wBACrBiB,MAAM;+BAAI5C,aAAa4C,IAAI;yBAAC;wBAC5BC,0BAA0B;wBAC1BC,gBAAgBjC;oBAClB;oBAEF,IAAIkC;oBACJ,IAAI;wBACFA,oBAAoB,IAAI,CAAC7E,oBAAoB,CAAC8E,GAAG,CAC/Cb,gCACAvC,SACAK,SACAQ;oBAEJ,EAAE,OAAOwC,KAAK;wBACZ,IAAIrB,sBAAsBY,MAAM,CAACU,OAAO,EAAE;4BACxC,0DAA0D;4BAC1D,gCAAgC;4BAChCpB,6BAA6B;wBAC/B,OAAO,IACLlD,QAAQC,GAAG,CAACsE,gBAAgB,IAC5BvE,QAAQC,GAAG,CAACuE,sBAAsB,EAClC;4BACAC,IAAAA,iEAAyC,EAACJ,KAAKnD,UAAUwD,KAAK;wBAChE;oBACF;oBACA,IACE,OAAOP,sBAAsB,YAC7BA,sBAAsB,QACtB,OAAO,AAACA,kBAA0BQ,IAAI,KAAK,YAC3C;wBACA,4EAA4E;wBAC5E,gDAAgD;;wBAC9CR,kBAA8CQ,IAAI,CAClD,KAAO,GACP,CAACN;4BACC,IAAIrB,sBAAsBY,MAAM,CAACU,OAAO,EAAE;gCACxC,0DAA0D;gCAC1D,gCAAgC;gCAChCpB,6BAA6B;4BAC/B,OAAO,IAAIlD,QAAQC,GAAG,CAACsE,gBAAgB,EAAE;gCACvCE,IAAAA,iEAAyC,EACvCJ,KACAnD,UAAUwD,KAAK;4BAEnB;wBACF;oBAEJ;oBACA,MAAMvB,YAAYyB,UAAU;oBAE5B,IAAI1B,4BAA4B;wBAC9B,0DAA0D;wBAC1D,gCAAgC;wBAChC,MAAM2B,gBAAgBC,IAAAA,uCAAqB,EAACzB;wBAC5C,IAAIwB,eAAe;4BACjB,MAAM,qBAEL,CAFK,IAAIE,sCAAkB,CAC1B,CAAC,MAAM,EAAE7D,UAAUwD,KAAK,CAAC,mDAAmD,EAAEG,cAAc,6EAA6E,CAAC,GADtK,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF,OAAO;4BACLtC,QAAQzD,KAAK,CACX;4BAEF,MAAM,qBAEL,CAFK,IAAIiG,sCAAkB,CAC1B,CAAC,MAAM,EAAE7D,UAAUwD,KAAK,CAAC,yIAAyI,CAAC,GAD/J,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;oBACF;oBAEA,4EAA4E;oBAC5E,gFAAgF;oBAChF,iBAAiB;oBACjB,MAAMM,kBAAkB,IAAI/B;oBAC5BI,kBAAkBC,IAAAA,4CAA0B,EAACrB;oBAE7C,MAAMgD,2BAA4CvC,iBAAiB;wBACjEc,MAAM;wBACNC,OAAO;wBACPC,YAAY,CAAC;wBACbtC;wBACAuC,cAAcqB,gBAAgBpB,MAAM;wBACpCC,YAAYmB;wBACZ7B,aAAa;wBACbE;wBACAR,YAAYC;wBACZgB,QAAQf,0BAAc;wBACtBgB,OAAOhB,0BAAc;wBACrBiB,MAAM;+BAAI5C,aAAa4C,IAAI;yBAAC;wBAC5BC,0BAA0B;wBAC1BC,gBAAgBjC;oBAClB;oBAEA,IAAIiD,kBAAkB;oBACtBvC,MAAM,MAAM,IAAIwC,QAAQ,CAACxE,SAASyE;wBAChCC,IAAAA,4BAAiB,EAAC;4BAChB,IAAI;gCACF,MAAMC,SAAS,MAAO,IAAI,CAAChG,oBAAoB,CAAC8E,GAAG,CACjDa,0BACAjE,SACAK,SACAQ;gCAEF,IAAIqD,iBAAiB;oCACnB,2CAA2C;oCAC3C;gCACF,OAAO,IAAI,CAAEI,CAAAA,kBAAkBzE,QAAO,GAAI;oCACxC,sDAAsD;oCACtDF,QAAQ2E;oCACR;gCACF;gCAEAJ,kBAAkB;gCAElB,IAAIK,cAAc;gCAClBD,OAAOE,WAAW,GAAGb,IAAI,CAAC,CAACc;oCACzB,IAAI,CAACF,aAAa;wCAChBA,cAAc;wCAEd5E,QACE,IAAIE,SAAS4E,MAAM;4CACjB1G,SAASuG,OAAOvG,OAAO;4CACvB+B,QAAQwE,OAAOxE,MAAM;4CACrB4E,YAAYJ,OAAOI,UAAU;wCAC/B;oCAEJ;gCACF,GAAGN;gCACHC,IAAAA,4BAAiB,EAAC;oCAChB,IAAI,CAACE,aAAa;wCAChBA,cAAc;wCACdP,gBAAgBW,KAAK;wCACrBP,OAAOQ,qBAAqB1E,UAAUwD,KAAK;oCAC7C;gCACF;4BACF,EAAE,OAAOL,KAAK;gCACZe,OAAOf;4BACT;wBACF;wBACAgB,IAAAA,4BAAiB,EAAC;4BAChB,IAAI,CAACH,iBAAiB;gCACpBA,kBAAkB;gCAClBF,gBAAgBW,KAAK;gCACrBP,OAAOQ,qBAAqB1E,UAAUwD,KAAK;4BAC7C;wBACF;oBACF;oBACA,IAAIM,gBAAgBpB,MAAM,CAACU,OAAO,EAAE;wBAClC,uCAAuC;wBACvC,MAAMsB,qBAAqB1E,UAAUwD,KAAK;oBAC5C,OAAO;wBACL,kFAAkF;wBAClF,kFAAkF;wBAClF,iBAAiB;wBACjBM,gBAAgBW,KAAK;oBACvB;gBACF,OAAO;oBACLjD,iBAAiB;wBACfc,MAAM;wBACNC,OAAO;wBACPC,YAAY,CAAC;wBACbtC;wBACAyB,YAAYC;wBACZgB,QAAQf,0BAAc;wBACtBgB,OAAOhB,0BAAc;wBACrBiB,MAAM;+BAAI5C,aAAa4C,IAAI;yBAAC;oBAC9B;oBAEArB,MAAM,MAAMrD,kDAAoB,CAAC8E,GAAG,CAClC1B,gBACA1B,SACAK,SACAQ;gBAEJ;YACF,OAAO;gBACLc,MAAM,MAAMrD,kDAAoB,CAAC8E,GAAG,CAClCjD,cACAH,SACAK,SACAQ;YAEJ;QACF,EAAE,OAAOwC,KAAK;YACZ,IAAIwB,IAAAA,8BAAe,EAACxB,MAAM;gBACxB,MAAM5B,MAAMqD,IAAAA,iCAAuB,EAACzB;gBACpC,IAAI,CAAC5B,KAAK;oBACR,MAAM,qBAAsD,CAAtD,IAAI5C,MAAM,8CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAqD;gBAC7D;gBAEA,wDAAwD;gBACxD,gBAAgB;gBAChB,MAAMd,UAAU,IAAIgH,QAAQ;oBAAEC,UAAUvD;gBAAI;gBAE5C,kDAAkD;gBAClD,cAAc;gBACd,yFAAyF;gBACzF,iFAAiF;gBACjF,IAAItB,aAAaqC,IAAI,KAAK,WAAW;oBACnCyC,IAAAA,oCAAoB,EAAClH,SAASoC,aAAa+E,cAAc;gBAC3D;gBAEAhE;gBAEA,gCAAgC;gBAChC,OAAO,IAAIrB,SAAS,MAAM;oBACxB,mEAAmE;oBACnE,sEAAsE;oBACtE,4BAA4B;oBAC5BC,QAAQG,YAAYkF,QAAQ,GACxBC,sCAAkB,CAACC,QAAQ,GAC3BC,IAAAA,wCAA8B,EAACjC;oBACnCtF;gBACF;YACF,OAAO,IAAIwH,IAAAA,6CAAyB,EAAClC,MAAM;gBACzC,MAAMmC,aAAaC,IAAAA,+CAA2B,EAACpC;gBAC/C,OAAO,IAAIxD,SAAS,MAAM;oBAAEC,QAAQ0F;gBAAW;YACjD;YAEA,MAAMnC;QACR;QAEA,yDAAyD;QACzD,IAAI,CAAE1B,CAAAA,eAAe9B,QAAO,GAAI;YAC9B,MAAM,qBAEL,CAFK,IAAIhB,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACT,gBAAgB,CAAC,0FAA0F,CAAC,GAD5J,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAkC,QAAQG,UAAU,CAACiF,YAAY,GAAGxF,UAAUwF,YAAY;QAExDxE;QAEA,IAAIQ,gBAAgB;gBACiBA;YAAnCpB,QAAQG,UAAU,CAACkF,aAAa,IAAGjE,uBAAAA,eAAesB,IAAI,qBAAnBtB,qBAAqBkE,IAAI,CAAC;YAC7DtF,QAAQG,UAAU,CAACoF,mBAAmB,GAAGnE,eAAeG,UAAU;YAClEvB,QAAQG,UAAU,CAACqF,eAAe,GAAGpE,eAAeoB,MAAM;YAC1DxC,QAAQG,UAAU,CAACsF,cAAc,GAAGrE,eAAeqB,KAAK;QAC1D;QAEA,4DAA4D;QAC5D,0DAA0D;QAC1D,QAAQ;QACR,MAAMhF,UAAU,IAAIgH,QAAQpD,IAAI5D,OAAO;QACvC,IACEoC,aAAaqC,IAAI,KAAK,aACtByC,IAAAA,oCAAoB,EAAClH,SAASoC,aAAa+E,cAAc,GACzD;YACA,OAAO,IAAIrF,SAAS8B,IAAI8C,IAAI,EAAE;gBAC5B3E,QAAQ6B,IAAI7B,MAAM;gBAClB4E,YAAY/C,IAAI+C,UAAU;gBAC1B3G;YACF;QACF;QAEA,OAAO4D;IACT;IAEA,MAAaqE,OACXC,GAAgB,EAChB3F,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMN,UAAU,IAAI,CAACL,OAAO,CAACsG,IAAI3G,MAAM;QAEvC,6CAA6C;QAC7C,MAAM4G,0BAA4C;YAChD,iDAAiD;YACjDC,qBAAqB;YACrBC,MAAM,IAAI,CAACjI,UAAU,CAACiI,IAAI;YAC1B3F,YAAYH,QAAQG,UAAU;YAC9B4F,SAAS/F,QAAQgG,aAAa,CAACD,OAAO;YACtCE,2BAA2B,EAAE;QAC/B;QAEA,+CAA+C;QAC/CL,wBAAwBzF,UAAU,CAAC+F,UAAU,GAAG,IAAI,CAACtI,QAAQ,CAACsI,UAAU;QAExE,MAAMvG,cAA2B;YAC/BwG,YAAY;YACZtB,UAAUuB,IAAAA,kDAAyB,EAACT;QACtC;QAEA,MAAM7F,eAAe,MAAMuG,IAAAA,6BAAe,EACxC,IAAI,CAACxI,UAAU,CAACiI,IAAI,EACpBH,IAAIW,OAAO,EACX,iDAAiD;QACjD;QAGF,MAAMzG,eAAe0G,IAAAA,sCAAwB,EAC3CZ,KACAA,IAAIW,OAAO,EACXxG,cACAa,WACAX,QAAQwG,iBAAiB,CAACC,OAAO;QAGnC,MAAM7G,YAAY8G,IAAAA,0BAAe,EAACd;QAElC,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMe,WAAoB,MAAM,IAAI,CAACxI,kBAAkB,CAAC2E,GAAG,CACzDnD,aACA,IACE,IAAI,CAAC3B,oBAAoB,CAAC8E,GAAG,CAACjD,cAAc,IAC1C,IAAI,CAAC5B,gBAAgB,CAAC6E,GAAG,CAAClD,WAAW;oBACnC,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAACvC,mBAAmB,EAAE;wBAC5B,IAAIuC,UAAUK,kBAAkB,EAAE;4BAChC,MAAM8C,MAAM,qBAEX,CAFW,IAAIU,sCAAkB,CAChC,0EADU,qBAAA;uCAAA;4CAAA;8CAAA;4BAEZ;4BACA7D,UAAUgH,uBAAuB,GAAG7D,IAAI8D,OAAO;4BAC/CjH,UAAUkH,iBAAiB,GAAG/D,IAAIgE,KAAK;4BACvC,MAAMhE;wBACR;oBACF;oBAEA,2EAA2E;oBAC3E,iFAAiF;oBACjF,IAAIhD,UAAU4F;oBAEd,oEAAoE;oBACpE,OAAQ,IAAI,CAACrH,OAAO;wBAClB,KAAK;4BAAiB;gCACpB,8CAA8C;gCAC9CsB,UAAUoH,YAAY,GAAG;gCACzB;4BACF;wBACA,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BpH,UAAUqH,WAAW,GAAG;4BACxB,mEAAmE;4BACnE,2DAA2D;4BAC3DlH,UAAU,IAAImH,MAAMvB,KAAKwB;4BACzB;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnDvH,UAAUwH,kBAAkB,GAAG;4BAC/B,IAAIxH,UAAUK,kBAAkB,EAC9BF,UAAU,IAAImH,MAAMvB,KAAK0B;4BAC3B;wBACF;4BACE,gGAAgG;4BAChGtH,UAAUuH,iBAAiB3B,KAAK/F;oBACpC;oBAEA,mDAAmD;oBACnD,MAAMwD,QAAQmE,IAAAA,wDAA2B,EAAC,IAAI,CAACzJ,gBAAgB;oBAE/D,MAAM0J,SAASC,IAAAA,iBAAS;oBAExB,gDAAgD;oBAChDD,OAAOE,oBAAoB,CAAC,cAActE;oBAE1C,OAAOoE,OAAOG,KAAK,CACjBC,oCAAyB,CAACC,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAE1E,OAAO;wBAC9C2E,YAAY;4BACV,cAAc3E;wBAChB;oBACF,GACA,UACE,IAAI,CAAC3D,EAAE,CACLC,SACAC,aACAC,WACAC,cACAC,cACAC,SACAC;gBAGR;QAIN,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAE2G,CAAAA,oBAAoBpH,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAO,IAAIA,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,IAAImH,SAASlJ,OAAO,CAACuK,GAAG,CAAC,yBAAyB;YAChD,MAAM,qBAEL,CAFK,IAAIzJ,MACR,uIADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIoI,SAASlJ,OAAO,CAACwK,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,qBAEL,CAFK,IAAI1J,MACR,iLADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOoI;IACT;AACF;MAEA,WAAexJ;AASR,SAASE,oBAAoB6K,QAA0B;IAC5D,IACE,gDAAgD;IAChDA,SAASC,IAAI,IACbD,SAASE,GAAG,IACZF,SAASG,MAAM,IACfH,SAASI,KAAK,IACdJ,SAASK,OAAO,EAChB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,0FAA0F;AAC1F,mDAAmD;AACnD,MAAMC,gBAAgBC,OAAO;AAC7B,MAAMC,qBAAqBD,OAAO;AAClC,MAAME,iBAAiBF,OAAO;AAC9B,MAAMG,qBAAqBH,OAAO;AAClC,MAAMI,aAAaJ,OAAO;AAC1B,MAAMK,iBAAiBL,OAAO;AAC9B,MAAMM,gBAAgBN,OAAO;AAC7B,MAAMO,gBAAgBP,OAAO;AAgB7B;;;;CAIC,GACD,MAAMtB,6BAA6B;IACjCc,KACEgB,MAAyC,EACzCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;gBACH,OACED,MAAM,CAACF,cAAc,IACpBE,CAAAA,MAAM,CAACF,cAAc,GAAGK,uBAAc,CAACC,IAAI,CAAC,IAAI5E,QAAQ,CAAC,GAAE;YAEhE,KAAK;gBACH,OACEwE,MAAM,CAACD,cAAc,IACpBC,CAAAA,MAAM,CAACD,cAAc,GAAGM,qCAAqB,CAACD,IAAI,CACjD,IAAIE,uBAAc,CAAC,IAAI9E,QAAQ,CAAC,IAClC;YAEJ,KAAK;gBACH,OACEwE,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAItB,MAC3B+B,OAAO3C,OAAO,EACdkD,2BACF;YAEJ,KAAK;gBACH,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,OAAOL,SAAS7C,OAAO,CAACmD,IAAI;YAC9B,KAAK;YACL,KAAK;gBACH,OAAO9I;YACT,KAAK;gBACH,OACEsI,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIxB,MACF,gFAAgF;oBAChF,mFAAmF;oBACnF,+EAA+E;oBAC/E,sFAAsF;oBACtF,yFAAyF;oBACzF,wFAAwF;oBACxF,2BAA2B;oBAC3B+B,OAAOS,KAAK,IACZvC,2BACF;YAEN;gBACE,OAAOwC,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AAGF;AAEA,MAAMK,6BAA6B;IACjCvB,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,iBAAiB;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OACED,MAAM,CAACL,mBAAmB,IACzBK,CAAAA,MAAM,CAACL,mBAAmB,GAAG,IAAIgB,iBAAgB;YAEtD,KAAK;gBACH,OACEX,MAAM,CAACJ,WAAW,IACjBI,CAAAA,MAAM,CAACJ,WAAW,GAAGgB,IAAAA,kBAAQ,EAACZ,OAAOQ,IAAI,EAAEA,IAAI,AAAD;YAEnD,KAAK;YACL,KAAK;gBACH,OACER,MAAM,CAACH,eAAe,IACrBG,CAAAA,MAAM,CAACH,eAAe,GAAG,IAAMK,SAASM,IAAI,AAAD;YAGhD,qBAAqB;YACrB,KAAK;gBACH,+FAA+F;gBAC/F,8FAA8F;gBAC9F,sDAAsD;gBACtD,OAAO9I;YACT,KAAK;gBACH,OACEsI,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAIzB,MAAM+B,OAAOS,KAAK,IAAIF,2BAA0B;YAE1D;gBACE,OAAOG,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AACF;AAEA,SAAS7B,iBAAiBvH,OAAoB,EAAEH,SAAoB;IAClE,MAAMkK,kBAAkB;QACtB7B,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;YAEb,OAAQD;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAU;wBACb,MAAMa,gBAAgB/L,kDAAoB,CAACgM,QAAQ;wBACnD1M,aAAasC,WAAWmK,eAAe,CAAC,QAAQ,EAAEb,MAAM;wBACxD,OAAOS,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBACH,OACEF,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAIzB,MAAM+B,OAAOS,KAAK,IAAII,gBAAe;gBAE/C;oBACE,OAAOH,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;YAC5C;QACF;IACF;IAEA,MAAMc,sBAAsB;QAC1BhC,KACEgB,MAAyC,EACzCC,IAAqB;YAErB,OAAQA;gBACN,KAAK;oBACH,OACED,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAItB,MAAM+B,OAAO3C,OAAO,EAAEwD,gBAAe;gBAEtE,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAY;wBACf,MAAMC,gBAAgB/L,kDAAoB,CAACgM,QAAQ;wBACnD1M,aAAasC,WAAWmK,eAAe,CAAC,QAAQ,EAAEb,MAAM;wBACxD,gFAAgF;wBAChF,wFAAwF;wBACxF,uBAAuB;wBACvB,OAAOS,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMD;oBAC1C;gBACA,KAAK;oBACH,OACEA,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIxB,MACF,gFAAgF;wBAChF,mFAAmF;wBACnF,+EAA+E;wBAC/E,sFAAsF;wBACtF,yFAAyF;wBACzF,wFAAwF;wBACxF,2BAA2B;wBAC3B+B,OAAOS,KAAK,IACZO,oBACF;gBAEN;oBACE,gFAAgF;oBAChF,wFAAwF;oBACxF,uBAAuB;oBACvB,OAAON,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMD;YAC5C;QACF;IAGF;IAEA,OAAO,IAAI/B,MAAMnH,SAASkK;AAC5B;AAEA,MAAM5C,+BAA+B;IACnCY,KACEgB,MAAyC,EACzCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;gBACH,OACED,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAItB,MAC3B+B,OAAO3C,OAAO,EACd4D,6BACF;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAElB,OAAO3C,OAAO,CAAC9H,QAAQ,CAAC,sFAAsF,EAAE0K,KAAK,GAAG,CAAC,GAD9H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OACED,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIxB,MACF,gFAAgF;oBAChF,mFAAmF;oBACnF,+EAA+E;oBAC/E,sFAAsF;oBACtF,yFAAyF;oBACzF,wFAAwF;oBACxF,2BAA2B;oBAC3B+B,OAAOS,KAAK,IACZrC,6BACF;YAEN;gBACE,OAAOsC,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AAGF;AAEA,MAAMe,+BAA+B;IACnCjC,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIiB,8CAAqB,CAC7B,CAAC,MAAM,EAAElB,OAAOzK,QAAQ,CAAC,sFAAsF,EAAE0K,KAAK,GAAG,CAAC,GADtH,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OACED,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAIzB,MAAM+B,OAAOS,KAAK,IAAIQ,6BAA4B;YAE5D;gBACE,OAAOP,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AACF;AAEA,SAAS7E,qBAAqBlB,KAAa;IACzC,OAAO,qBAEN,CAFM,IAAIK,sCAAkB,CAC3B,CAAC,MAAM,EAAEL,MAAM,wIAAwI,CAAC,GADnJ,qBAAA;eAAA;oBAAA;sBAAA;IAEP;AACF;AAEO,SAAS9F,aACd8M,KAAgB,EAChBL,aAAwC,EACxCM,UAAkB;IAElB,IAAIN,eAAe;QACjB,IAAIA,cAAc7H,IAAI,KAAK,SAAS;YAClC,MAAM,qBAEL,CAFK,IAAI3D,MACR,CAAC,MAAM,EAAE6L,MAAMhH,KAAK,CAAC,OAAO,EAAEiH,WAAW,gJAAgJ,EAAEA,WAAW,qKAAqK,CAAC,GADxW,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIN,cAAc7H,IAAI,KAAK,kBAAkB;YAClD,MAAM,qBAEL,CAFK,IAAI3D,MACR,CAAC,MAAM,EAAE6L,MAAMhH,KAAK,CAAC,OAAO,EAAEiH,WAAW,iLAAiL,EAAEA,WAAW,6KAA6K,CAAC,GADjZ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAID,MAAMhD,kBAAkB,EAAE;QAC5B,MAAM,qBAEL,CAFK,IAAI+C,8CAAqB,CAC7B,CAAC,MAAM,EAAEC,MAAMhH,KAAK,CAAC,8EAA8E,EAAEiH,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIN,eAAe;QACjB,IAAIA,cAAc7H,IAAI,KAAK,aAAa;YACtC,sBAAsB;YACtB,MAAM1E,QAAQ,qBAEb,CAFa,IAAIe,MAChB,CAAC,MAAM,EAAE6L,MAAMhH,KAAK,CAAC,MAAM,EAAEiH,WAAW,+HAA+H,CAAC,GAD5J,qBAAA;uBAAA;4BAAA;8BAAA;YAEd;YACAC,IAAAA,6DAA2C,EACzCF,MAAMhH,KAAK,EACXiH,YACA7M,OACAuM;QAEJ,OAAO,IAAIA,cAAc7H,IAAI,KAAK,iBAAiB;YACjD,gBAAgB;YAChBqI,IAAAA,sCAAoB,EAClBH,MAAMhH,KAAK,EACXiH,YACAN,cAAchI,eAAe;QAEjC,OAAO,IAAIgI,cAAc7H,IAAI,KAAK,oBAAoB;YACpD,mBAAmB;YACnB6H,cAAcxI,UAAU,GAAG;YAE3B,MAAMwB,MAAM,qBAEX,CAFW,IAAIU,sCAAkB,CAChC,CAAC,MAAM,EAAE2G,MAAMhH,KAAK,CAAC,mDAAmD,EAAEiH,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAD,MAAMxD,uBAAuB,GAAGyD;YAChCD,MAAMtD,iBAAiB,GAAG/D,IAAIgE,KAAK;YAEnC,MAAMhE;QACR,OAAO,IACLrE,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBmL,iBACAA,cAAc7H,IAAI,KAAK,WACvB;YACA6H,cAAcS,WAAW,GAAG;QAC9B;IACF;AACF"}