const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const moment = require('moment');

// Import models
const User = require('../models/UserModel');
const Facture = require('../models/FactureModel');
const Client = require('../models/ClientModel');

// Import middleware
const { verifyToken, isAdmin } = require('../middleware/authMiddleware');

// Helper function to get date range
const getDateRange = (period, targetDate = new Date()) => {
  const endDate = new Date(targetDate);
  let startDate = new Date(targetDate);

  switch (period) {
    case 'weekly':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'monthly':
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case 'quarterly':
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    case 'yearly':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case 'all-time':
      startDate = new Date(2018, 0, 1); // Default to 2018
      endDate.setFullYear(2025, 11, 31); // Default to end of 2025
      break;
    default:
      startDate.setMonth(endDate.getMonth() - 1); // Default to monthly
  }

  return { startDate, endDate };
};

// GET /user-metrics/by-role - Get user counts by role
router.get('/user-metrics/by-role', verifyToken, isAdmin, async (req, res) => {
  try {
    // Aggregate users by role
    const usersByRole = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          role: '$_id',
          count: 1,
          _id: 0
        }
      }
    ]);

    // Format the response
    const formattedData = usersByRole.map(item => {
      let name, color;

      switch (item.role) {
        case 'VENDEUR':
          name = 'Vendeurs';
          color = '#4CAF50'; // Green
          break;
        case 'RESPONSABLE': // Changed from 'ENTREPRISE' to 'RESPONSABLE'
          name = 'Entreprises';
          color = '#2196F3'; // Blue
          break;
        case 'ADMIN':
          name = 'Administrateurs';
          color = '#FF9800'; // Orange
          break;
        default:
          name = item.role;
          color = '#9E9E9E'; // Grey
      }

      return {
        name,
        value: item.count,
        color
      };
    });

    // Filter out ADMIN role for the chart (optional)
    const chartData = formattedData.filter(item => item.name !== 'Administrateurs');

    // Calculate total users
    const totalUsers = chartData.reduce((sum, item) => sum + item.value, 0);

    res.status(200).json({
      chartData,
      totalUsers
    });
  } catch (error) {
    console.error('Error fetching users by role:', error);
    res.status(500).json({
      error: 'Error fetching users by role',
      message: error.message
    });
  }
});

// GET /user-metrics/top-vendors - Get top performing vendors
router.get('/user-metrics/top-vendors', verifyToken, async (req, res) => {
  try {
    const { limit = 10, period = 'all-time' } = req.query;

    // Get date range for the specified period
    const { startDate, endDate } = getDateRange(period);

    // Get all vendors
    const vendors = await User.find({ role: 'VENDEUR' }).select('_id nom prenom email');

    // For each vendor, calculate their performance metrics
    const vendorPerformance = await Promise.all(
      vendors.map(async (vendor) => {
        // Count invoices created by this vendor in the specified period
        const invoiceCount = await Facture.countDocuments({
          vendeurId: vendor._id,
          dateEmission: { $gte: startDate, $lte: endDate }
        });

        // Calculate total revenue from invoices
        const invoices = await Facture.find({
          vendeurId: vendor._id,
          dateEmission: { $gte: startDate, $lte: endDate }
        });

        const revenue = invoices.reduce((total, invoice) => {
          return total + (invoice.montantTotal || 0);
        }, 0);

        // Count clients associated with this vendor
        const clientCount = await Client.countDocuments({
          vendeurId: vendor._id,
          createdAt: { $lte: endDate }
        });

        // Format name properly, handling undefined or null prenom
        const vendorName = vendor.prenom ? `${vendor.prenom} ${vendor.nom}` : vendor.nom;

        return {
          id: vendor._id,
          name: vendorName,
          email: vendor.email,
          revenue: revenue,
          invoices: invoiceCount,
          clients: clientCount
        };
      })
    );

    // Sort by revenue (descending)
    const sortedVendors = vendorPerformance
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, parseInt(limit));

    res.status(200).json(sortedVendors);
  } catch (error) {
    console.error('Error fetching top vendors:', error);
    res.status(500).json({
      error: 'Error fetching top vendors',
      message: error.message
    });
  }
});

// GET /user-metrics/client-growth - Get client growth data
router.get('/user-metrics/client-growth', verifyToken, isAdmin, async (req, res) => {
  try {
    const { months = 12 } = req.query;

    // Get current date
    const currentDate = new Date();

    // Create an array of month names and initialize data structure
    const monthNames = [];
    const growthData = [];

    // Calculate client growth for each month
    for (let i = months - 1; i >= 0; i--) {
      const targetDate = new Date(currentDate);
      targetDate.setMonth(currentDate.getMonth() - i);

      const monthStart = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
      const monthEnd = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);

      // Get month name in French
      const monthName = moment(targetDate).locale('fr').format('MMM');
      monthNames.push(monthName);

      // Count new clients for this month
      const newClientsCount = await Client.countDocuments({
        createdAt: { $gte: monthStart, $lte: monthEnd }
      });

      // Count total clients up to this month
      const totalClientsCount = await Client.countDocuments({
        createdAt: { $lte: monthEnd }
      });

      growthData.push({
        month: monthName,
        newClients: newClientsCount,
        totalClients: totalClientsCount
      });
    }

    res.status(200).json(growthData);
  } catch (error) {
    console.error('Error fetching client growth:', error);
    res.status(500).json({
      error: 'Error fetching client growth',
      message: error.message
    });
  }
});

// GET /user-metrics/admin-stats - Get admin dashboard stats
router.get('/user-metrics/admin-stats', verifyToken, isAdmin, async (req, res) => {
  try {
    // Get active users count (excluding ADMIN users)
    const activeUsers = await User.countDocuments({
      role: { $in: ['VENDEUR', 'RESPONSABLE'] } // Changed from 'ENTREPRISE' to 'RESPONSABLE'
    });

    // Count users by role
    const vendeurCount = await User.countDocuments({ role: 'VENDEUR' });
    const entrepriseCount = await User.countDocuments({ role: 'RESPONSABLE' }); // Changed from 'ENTREPRISE' to 'RESPONSABLE'
    const adminCount = await User.countDocuments({ role: 'ADMIN' });

    // Count recent connections (users created or modified in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentConnections = await User.countDocuments({
      $or: [
        { dateCreation: { $gte: thirtyDaysAgo } },
        { lastLogin: { $gte: thirtyDaysAgo } } // If you have a lastLogin field
      ],
      role: { $in: ['VENDEUR', 'RESPONSABLE'] } // Changed from 'ENTREPRISE' to 'RESPONSABLE'
    });

    // Calculate conversion rate (quotes to invoices)
    let conversionRate = 0;
    let conversionTrend = 0;

    try {
      const Facture = require('../models/FactureModel');
      const Devis = require('../models/DevisModel');

      // Current period (last 3 months)
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      // Previous period (3-6 months ago)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      // Count quotes and converted invoices for current period
      const currentPeriodQuotes = await Devis.countDocuments({
        dateEmission: { $gte: threeMonthsAgo }
      });

      const currentPeriodConvertedQuotes = await Devis.countDocuments({
        dateEmission: { $gte: threeMonthsAgo },
        statut: 'ACCEPTED' // Assuming 'ACCEPTED' status means converted
      });

      // Count quotes and converted invoices for previous period
      const previousPeriodQuotes = await Devis.countDocuments({
        dateEmission: { $gte: sixMonthsAgo, $lt: threeMonthsAgo }
      });

      const previousPeriodConvertedQuotes = await Devis.countDocuments({
        dateEmission: { $gte: sixMonthsAgo, $lt: threeMonthsAgo },
        statut: 'ACCEPTED'
      });

      // Calculate conversion rates
      const currentRate = currentPeriodQuotes > 0
        ? (currentPeriodConvertedQuotes / currentPeriodQuotes) * 100
        : 0;

      const previousRate = previousPeriodQuotes > 0
        ? (previousPeriodConvertedQuotes / previousPeriodQuotes) * 100
        : 0;

      // Calculate trend (percentage change)
      conversionTrend = previousRate > 0
        ? ((currentRate - previousRate) / previousRate) * 100
        : 0;

      conversionRate = Math.round(currentRate);
      conversionTrend = Math.round(conversionTrend);
    } catch (error) {
      console.log('Error calculating conversion rate:', error.message);
      // If there's an error, we'll keep the default values
    }

    // Get clients count
    const clients = await Client.countDocuments();

    // Get new clients (created within the last 30 days)
    const newClients = await Client.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Calculate client distribution by vendor
    let clientDistribution = [];

    try {
      // Get all vendors
      const vendors = await User.find({ role: 'VENDEUR' }).select('_id nom');

      // For each vendor, count their clients
      const vendorClientCounts = await Promise.all(
        vendors.map(async (vendor) => {
          // Count clients associated with this vendor
          // This assumes there's a vendeurId field in the Client model
          // Adjust the query based on your actual data model
          const clientCount = await Client.countDocuments({ vendeurId: vendor._id });

          return {
            id: vendor._id,
            name: vendor.nom,
            clientCount
          };
        })
      );

      // Sort by client count (descending)
      clientDistribution = vendorClientCounts.sort((a, b) => b.clientCount - a.clientCount);

      // Calculate total clients for percentage
      const totalClients = clientDistribution.reduce((sum, vendor) => sum + vendor.clientCount, 0);

      // Add percentage to each vendor
      clientDistribution = clientDistribution.map(vendor => ({
        ...vendor,
        percentage: totalClients > 0 ? Math.round((vendor.clientCount / totalClients) * 100) : 0
      }));

    } catch (error) {
      console.log('Error calculating client distribution:', error.message);
      // If there's an error, we'll keep the default empty array
    }

    // Prepare response
    const stats = {
      activeUsers,
      recentConnections,
      conversionRate,
      conversionTrend,
      clients,
      newClients,
      clientDistribution,
      usersByRole: {
        admin: adminCount,
        vendeur: vendeurCount,
        entreprise: entrepriseCount
      }
    };

    res.status(200).json(stats);
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      error: 'Error fetching admin stats',
      message: error.message
    });
  }
});

module.exports = router;
