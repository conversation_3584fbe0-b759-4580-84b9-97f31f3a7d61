{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/parse-stack.ts"], "sourcesContent": ["import { parse } from 'next/dist/compiled/stacktrace-parser'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  getHydrationErrorStackInfo,\n  isReactHydrationErrorMessage,\n} from '../../is-hydration-error'\n\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/\n\nexport function parseStack(stack: string | undefined): StackFrame[] {\n  if (!stack) return []\n  const messageAndStack = stack.replace(/^Error: /, '')\n  if (isReactHydrationErrorMessage(messageAndStack)) {\n    const { stack: parsedStack } = getHydrationErrorStackInfo(messageAndStack)\n    if (parsedStack) {\n      stack = parsedStack\n    }\n  }\n\n  // throw away eval information that stacktrace-parser doesn't support\n  // adapted from https://github.com/stacktracejs/error-stack-parser/blob/9f33c224b5d7b607755eb277f9d51fcdb7287e24/error-stack-parser.js#L59C33-L59C62\n  stack = stack\n    .split('\\n')\n    .map((line) => {\n      if (line.includes('(eval ')) {\n        line = line\n          .replace(/eval code/g, 'eval')\n          .replace(/\\(eval at [^()]* \\(/, '(file://')\n          .replace(/\\),.*$/g, ')')\n      }\n\n      return line\n    })\n    .join('\\n')\n\n  const frames = parse(stack)\n  return frames.map((frame) => {\n    try {\n      const url = new URL(frame.file!)\n      const res = regexNextStatic.exec(url.pathname)\n      if (res) {\n        const distDir = process.env.__NEXT_DIST_DIR\n          ?.replace(/\\\\/g, '/')\n          ?.replace(/\\/$/, '')\n        if (distDir) {\n          frame.file = 'file://' + distDir.concat(res.pop()!) + url.search\n        }\n      }\n    } catch {}\n    return frame\n  })\n}\n"], "names": ["parseStack", "regexNextStatic", "stack", "messageAndStack", "replace", "isReactHydrationErrorMessage", "parsedStack", "getHydrationErrorStackInfo", "split", "map", "line", "includes", "join", "frames", "parse", "frame", "url", "URL", "file", "res", "exec", "pathname", "process", "distDir", "env", "__NEXT_DIST_DIR", "concat", "pop", "search"], "mappings": ";;;;+BASgBA;;;eAAAA;;;kCATM;kCAKf;AAEP,MAAMC,kBAAkB;AAEjB,SAASD,WAAWE,KAAyB;IAClD,IAAI,CAACA,OAAO,OAAO,EAAE;IACrB,MAAMC,kBAAkBD,MAAME,OAAO,CAAC,YAAY;IAClD,IAAIC,IAAAA,8CAA4B,EAACF,kBAAkB;QACjD,MAAM,EAAED,OAAOI,WAAW,EAAE,GAAGC,IAAAA,4CAA0B,EAACJ;QAC1D,IAAIG,aAAa;YACfJ,QAAQI;QACV;IACF;IAEA,qEAAqE;IACrE,oJAAoJ;IACpJJ,QAAQA,MACLM,KAAK,CAAC,MACNC,GAAG,CAAC,CAACC;QACJ,IAAIA,KAAKC,QAAQ,CAAC,WAAW;YAC3BD,OAAOA,KACJN,OAAO,CAAC,cAAc,QACtBA,OAAO,CAAC,uBAAuB,YAC/BA,OAAO,CAAC,WAAW;QACxB;QAEA,OAAOM;IACT,GACCE,IAAI,CAAC;IAER,MAAMC,SAASC,IAAAA,uBAAK,EAACZ;IACrB,OAAOW,OAAOJ,GAAG,CAAC,CAACM;QACjB,IAAI;YACF,MAAMC,MAAM,IAAIC,IAAIF,MAAMG,IAAI;YAC9B,MAAMC,MAAMlB,gBAAgBmB,IAAI,CAACJ,IAAIK,QAAQ;YAC7C,IAAIF,KAAK;oBACSG,sCAAAA;gBAAhB,MAAMC,WAAUD,+BAAAA,QAAQE,GAAG,CAACC,eAAe,sBAA3BH,uCAAAA,6BACZlB,OAAO,CAAC,OAAO,yBADHkB,qCAEZlB,OAAO,CAAC,OAAO;gBACnB,IAAImB,SAAS;oBACXR,MAAMG,IAAI,GAAG,YAAYK,QAAQG,MAAM,CAACP,IAAIQ,GAAG,MAAOX,IAAIY,MAAM;gBAClE;YACF;QACF,EAAE,UAAM,CAAC;QACT,OAAOb;IACT;AACF"}