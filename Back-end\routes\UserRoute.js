const express = require('express');
const router = express.Router();
const User = require('../models/UserModel');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const entrepriseService = require('../services/entrepriseService');

// JWT Secret (should be in environment variables in production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and check if user is admin
const verifyAdminToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      if (decoded.role !== 'ADMIN') {
        return res.status(200).json({
          redirect: true,
          message: 'Redirection vers votre tableau de bord',
          role: decoded.role
        });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Middleware to verify JWT token for any authenticated user
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Create a user
router.post('/users', async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const userData = req.body;

        // Check if email already exists
        const existingUser = await User.findOne({ email: userData.email });
        if (existingUser) {
            return res.status(400).json({ error: 'Cet email est déjà utilisé' });
        }

        // Hash password if provided
        if (userData.motDePasse) {
            const salt = await bcrypt.genSalt(10);
            userData.motDePasse = await bcrypt.hash(userData.motDePasse, salt);
        }

        // Clean up responsables array - ensure it's a valid array with no null values
        if (userData.responsables) {
            if (Array.isArray(userData.responsables)) {
                // Filter out null or undefined values
                userData.responsables = userData.responsables.filter(id => id);
            } else if (userData.responsables) {
                // If it's a single non-null value, convert to array
                userData.responsables = [userData.responsables];
            } else {
                // If it's null or undefined, set to empty array
                userData.responsables = [];
            }
        } else {
            userData.responsables = [];
        }

        console.log('Creating user with cleaned data:', userData);

        // Create and save the new user
        const newUser = new User(userData);
        const savedUser = await newUser.save();

        // Si c'est un responsable d'entreprise, créer une entrée dans la collection entreprises
        if (savedUser.role === 'RESPONSABLE') {
            try {
                console.log('Création d\'une entreprise pour le responsable:', savedUser._id);
                const entreprise = await entrepriseService.createOrUpdateEntreprise(userData, savedUser._id);
                console.log('Entreprise créée avec succès:', entreprise._id);
            } catch (entrepriseError) {
                console.error('Erreur lors de la création de l\'entreprise:', entrepriseError);
                // On continue même si la création d'entreprise échoue
            }
        }

        // Return the created user without password
        const userResponse = savedUser.toObject();
        delete userResponse.motDePasse;

        res.status(201).json(userResponse);
    } catch (error) {
        console.error('Error adding user:', error.message);
        res.status(500).json({ error: 'Erreur lors de l\'ajout de l\'utilisateur', details: error.message });
    }
});

// Get all users
router.get('/users', verifyToken, async (req, res) => {
    try {
        console.log('Getting all users. Requested by:', req.userId, 'with role:', req.userRole);

        // Get all users
        const users = await User.find().select('-motDePasse');

        console.log(`Found ${users.length} users`);

        // Return all users
        res.status(200).json(users);
    } catch (error) {
        console.error('Error fetching users:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des utilisateurs' });
    }
});

// Get all responsables (public route for signup)
router.get('/public/responsables', async (req, res) => {
    try {
        console.log('Getting all responsables for public access (signup)');

        // Get all users with role RESPONSABLE
        const responsables = await User.find({ role: 'RESPONSABLE' }).select('-motDePasse');

        console.log(`Found ${responsables.length} responsables`);

        // Return responsables with minimal information
        const simplifiedResponsables = responsables.map(responsable => ({
            _id: responsable._id,
            nom: responsable.nom,
            nomEntreprise: responsable.nomEntreprise || responsable.nom
        }));

        res.status(200).json(simplifiedResponsables);
    } catch (error) {
        console.error('Error fetching public responsables:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération des responsables d\'entreprise' });
    }
});

// Get a single user by ID (without password)
router.get('/users/:id', verifyToken, async (req, res) => {
    try {
        console.log('Getting user by ID:', req.params.id, 'Requested by:', req.userId, 'with role:', req.userRole);

        const user = await User.findById(req.params.id).select('-motDePasse');
        if (!user) return res.status(404).json({ error: 'Utilisateur non trouvé' });

        console.log('Found user:', user.nom, user._id);
        res.status(200).json(user);
    } catch (error) {
        console.error('Error fetching user:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération de l\'utilisateur' });
    }
});

// Get a single user by ID with password (admin only)
router.get('/users/:id/with-password', verifyAdminToken, async (req, res) => {
    try {
        const user = await User.findById(req.params.id);
        if (!user) return res.status(404).json({ error: 'Utilisateur non trouvé' });
        console.log('User with password requested by admin');
        res.status(200).json(user);
    } catch (error) {
        console.error('Error fetching user with password:', error.message);
        res.status(500).json({ error: 'Erreur lors de la récupération de l\'utilisateur' });
    }
});

// Update a user
router.put('/users/:id', verifyToken, async (req, res) => {
    try {
        console.log('Updating user:', req.params.id, 'Requested by:', req.userId, 'with role:', req.userRole);

        const userData = req.body;
        console.log('Update data:', userData);

        // Check if email is being changed and if it already exists
        if (userData.email) {
            const existingUser = await User.findOne({
                email: userData.email,
                _id: { $ne: req.params.id }
            });

            if (existingUser) {
                return res.status(400).json({ error: 'Cet email est déjà utilisé par un autre utilisateur' });
            }
        }

        // Hash password if provided
        if (userData.motDePasse) {
            const salt = await bcrypt.genSalt(10);
            userData.motDePasse = await bcrypt.hash(userData.motDePasse, salt);
        }

        // Clean up responsables array - ensure it's a valid array with no null values
        if (userData.responsables) {
            if (Array.isArray(userData.responsables)) {
                // Filter out null or undefined values
                userData.responsables = userData.responsables.filter(id => id);
            } else if (userData.responsables) {
                // If it's a single non-null value, convert to array
                userData.responsables = [userData.responsables];
            } else {
                // If it's null or undefined, set to empty array
                userData.responsables = [];
            }
        } else {
            userData.responsables = [];
        }

        console.log(`Updating user ${req.params.id} with cleaned data:`, userData);

        // Update user
        const user = await User.findByIdAndUpdate(
            req.params.id,
            userData,
            { new: true, runValidators: true }
        );

        if (!user) {
            return res.status(404).json({ error: 'Utilisateur non trouvé' });
        }

        console.log('User updated successfully:', user.nom, user._id);

        // Si c'est un responsable d'entreprise, mettre à jour l'entreprise associée
        if (user.role === 'RESPONSABLE') {
            try {
                console.log('Mise à jour de l\'entreprise pour le responsable:', user._id);
                const entreprise = await entrepriseService.createOrUpdateEntreprise(userData, user._id);
                console.log('Entreprise mise à jour avec succès:', entreprise._id);
            } catch (entrepriseError) {
                console.error('Erreur lors de la mise à jour de l\'entreprise:', entrepriseError);
                // On continue même si la mise à jour d'entreprise échoue
            }
        }

        // Return the updated user without password
        const userResponse = user.toObject();
        delete userResponse.motDePasse;

        res.status(200).json(userResponse);
    } catch (error) {
        console.error('Error updating user:', error.message);
        res.status(500).json({
            error: 'Erreur lors de la mise à jour de l\'utilisateur',
            details: error.message
        });
    }
});

// Delete a user
router.delete('/users/:id', verifyToken, async (req, res) => {
    try {
        console.log('Deleting user:', req.params.id, 'Requested by:', req.userId, 'with role:', req.userRole);

        const user = await User.findByIdAndDelete(req.params.id);
        if (!user) return res.status(404).json({ error: 'Utilisateur non trouvé' });

        console.log('User deleted successfully:', user.nom, user._id);
        res.status(204).send();
    } catch (error) {
        console.error('Error deleting user:', error.message);
        res.status(500).json({ error: 'Erreur lors de la suppression de l\'utilisateur' });
    }
});

module.exports = router;