{"version": 3, "sources": ["../../../src/server/lib/server-action-request-meta.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { NextRequest } from '../web/exports'\nimport { ACTION_HEADER } from '../../client/components/app-router-headers'\n\nexport function getServerActionRequestMetadata(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): {\n  actionId: string | null\n  isURLEncodedAction: boolean\n  isMultipartAction: boolean\n  isFetchAction: boolean\n  isPossibleServerAction: boolean\n} {\n  let actionId: string | null\n  let contentType: string | null\n\n  if (req.headers instanceof Headers) {\n    actionId = req.headers.get(ACTION_HEADER.toLowerCase()) ?? null\n    contentType = req.headers.get('content-type')\n  } else {\n    actionId = (req.headers[ACTION_HEADER.toLowerCase()] as string) ?? null\n    contentType = req.headers['content-type'] ?? null\n  }\n\n  const isURLEncodedAction = Boolean(\n    req.method === 'POST' && contentType === 'application/x-www-form-urlencoded'\n  )\n  const isMultipartAction = Boolean(\n    req.method === 'POST' && contentType?.startsWith('multipart/form-data')\n  )\n  const isFetchAction = Boolean(\n    actionId !== undefined &&\n      typeof actionId === 'string' &&\n      req.method === 'POST'\n  )\n\n  const isPossibleServerAction = Boolean(\n    isFetchAction || isURLEncodedAction || isMultipartAction\n  )\n\n  return {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  }\n}\n\nexport function getIsPossibleServerAction(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): boolean {\n  return getServerActionRequestMetadata(req).isPossibleServerAction\n}\n"], "names": ["getIsPossibleServerAction", "getServerActionRequestMetadata", "req", "actionId", "contentType", "headers", "Headers", "get", "ACTION_HEADER", "toLowerCase", "isURLEncodedAction", "Boolean", "method", "isMultipartAction", "startsWith", "isFetchAction", "undefined", "isPossibleServerAction"], "mappings": ";;;;;;;;;;;;;;;IAkDgBA,yBAAyB;eAAzBA;;IA7CAC,8BAA8B;eAA9BA;;;kCAFc;AAEvB,SAASA,+BACdC,GAAoD;IAQpD,IAAIC;IACJ,IAAIC;IAEJ,IAAIF,IAAIG,OAAO,YAAYC,SAAS;QAClCH,WAAWD,IAAIG,OAAO,CAACE,GAAG,CAACC,+BAAa,CAACC,WAAW,OAAO;QAC3DL,cAAcF,IAAIG,OAAO,CAACE,GAAG,CAAC;IAChC,OAAO;QACLJ,WAAW,AAACD,IAAIG,OAAO,CAACG,+BAAa,CAACC,WAAW,GAAG,IAAe;QACnEL,cAAcF,IAAIG,OAAO,CAAC,eAAe,IAAI;IAC/C;IAEA,MAAMK,qBAAqBC,QACzBT,IAAIU,MAAM,KAAK,UAAUR,gBAAgB;IAE3C,MAAMS,oBAAoBF,QACxBT,IAAIU,MAAM,KAAK,WAAUR,+BAAAA,YAAaU,UAAU,CAAC;IAEnD,MAAMC,gBAAgBJ,QACpBR,aAAaa,aACX,OAAOb,aAAa,YACpBD,IAAIU,MAAM,KAAK;IAGnB,MAAMK,yBAAyBN,QAC7BI,iBAAiBL,sBAAsBG;IAGzC,OAAO;QACLV;QACAO;QACAG;QACAE;QACAE;IACF;AACF;AAEO,SAASjB,0BACdE,GAAoD;IAEpD,OAAOD,+BAA+BC,KAAKe,sBAAsB;AACnE"}