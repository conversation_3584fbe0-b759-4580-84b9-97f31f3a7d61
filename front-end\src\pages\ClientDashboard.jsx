import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Paper,
  Avatar,
  IconButton,
  Tooltip,
  CircularProgress,
  useTheme,
  alpha,
  Container,
  ButtonGroup,
  Snackbar,
  Alert,
  Stack
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Payment as PaymentIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  PictureAsPdf as PdfIcon,
  Download as DownloadIcon,
  ArrowForward as ArrowForwardIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  Refresh as RefreshIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  <PERSON><PERSON><PERSON> as ShowChartIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  FileDownload as FileDownloadIcon
} from '@mui/icons-material';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts';
import { useAuth } from '../contexts/AuthContext';
import factureService from '../services/factureService';
import devisService from '../services/devisService';
import paiementService from '../services/paiementService';
import { formatDate, formatCurrency, formatStatut, normalizeStatus } from '../utils/formatters';
import SimpleDateFilter from '../components/SimpleDateFilter';
import PowerBIExport from '../components/PowerBIExport';

// Données vides par défaut pour les graphiques
const emptyPaymentData = [
  { month: 'Jan', montant: 0 },
  { month: 'Fév', montant: 0 },
  { month: 'Mar', montant: 0 },
  { month: 'Avr', montant: 0 },
  { month: 'Mai', montant: 0 },
  { month: 'Juin', montant: 0 },
  { month: 'Juil', montant: 0 },
  { month: 'Août', montant: 0 },
  { month: 'Sep', montant: 0 },
  { month: 'Oct', montant: 0 },
  { month: 'Nov', montant: 0 },
  { month: 'Déc', montant: 0 },
];

// Couleurs pour les graphiques
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A569BD', '#5DADE2'];

const ClientDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [factures, setFactures] = useState([]);
  const [devis, setDevis] = useState([]);
  const [paiements, setPaiements] = useState([]);
  const [dateRange, setDateRange] = useState('all');
  const [customDateRange, setCustomDateRange] = useState(null);
  const [chartType, setChartType] = useState('area');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [paymentData, setPaymentData] = useState(emptyPaymentData);
  const [stats, setStats] = useState({
    totalFactures: 0,
    totalDevis: 0,
    facturesPaid: 0,
    facturesPending: 0,
    devisAccepted: 0,
    devisPending: 0,
    totalMontant: 0,
    montantChange: 0
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 12
      }
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const fetchData = useCallback(async (period = 'all', customRange = null) => {
    try {
      setLoading(true);
      setRefreshing(true);

      // Récupérer les factures du client
      const facturesResponse = await factureService.getFactures();
      console.log('Factures récupérées du serveur:', facturesResponse);
      console.log('ID client connecté:', currentUser.clientId);

      console.log('Type de currentUser.clientId (dashboard):', typeof currentUser.clientId);
      console.log('Valeur de currentUser.clientId (dashboard):', currentUser.clientId);
      console.log('Email de l\'utilisateur connecté (dashboard):', currentUser.email);

      const facturesData = facturesResponse.filter(facture => {
        if (!facture.clientId) {
          console.log('Facture sans clientId ignorée (dashboard)');
          return false;
        }

        // Afficher plus d'informations sur la facture et son clientId
        console.log('Structure de facture.clientId (dashboard):', facture.clientId);

        // Vérifier si clientId est un objet avec _id ou directement un ID
        let factureClientId;
        let factureClientEmail = '';

        if (typeof facture.clientId === 'object' && facture.clientId !== null) {
          factureClientId = facture.clientId._id ? facture.clientId._id.toString() : '';
          factureClientEmail = facture.clientId.email || '';
          console.log('factureClientId est un objet avec _id (dashboard):', factureClientId);
          console.log('Email du client de la facture (dashboard):', factureClientEmail);
        } else {
          factureClientId = facture.clientId.toString();
          console.log('factureClientId est une chaîne ou un ID (dashboard):', factureClientId);
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
          userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
          console.log('userClientId est un objet avec _id (dashboard):', userClientId);
        } else {
          userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
          console.log('userClientId est une chaîne ou un ID (dashboard):', userClientId);
        }

        console.log('Comparaison ID (dashboard):', factureClientId, '===', userClientId, factureClientId === userClientId);
        console.log('Comparaison Email (dashboard):', factureClientEmail, '===', currentUser.email, factureClientEmail === currentUser.email);

        // Vérifier si l'ID correspond OU si l'email correspond
        const matchById = factureClientId === userClientId;
        const matchByEmail = factureClientEmail && currentUser.email && factureClientEmail.toLowerCase() === currentUser.email.toLowerCase();

        const match = matchById || matchByEmail;
        console.log('Résultat de la comparaison (ID ou Email) (dashboard):', match);

        return match;
      });

      console.log('Factures filtrées pour le client:', facturesData);

      // Récupérer les devis du client
      const devisResponse = await devisService.getDevis();
      console.log('Devis récupérés du serveur:', devisResponse);

      const devisData = devisResponse.filter(devis => {
        if (!devis.clientId) {
          console.log('Devis sans clientId ignoré (dashboard)');
          return false;
        }

        // Afficher plus d'informations sur le devis et son clientId
        console.log('Structure de devis.clientId (dashboard):', devis.clientId);

        // Vérifier si clientId est un objet avec _id ou directement un ID
        let devisClientId;
        let devisClientEmail = '';

        if (typeof devis.clientId === 'object' && devis.clientId !== null) {
          devisClientId = devis.clientId._id ? devis.clientId._id.toString() : '';
          devisClientEmail = devis.clientId.email || '';
          console.log('devisClientId est un objet avec _id (dashboard):', devisClientId);
          console.log('Email du client du devis (dashboard):', devisClientEmail);
        } else {
          devisClientId = devis.clientId.toString();
          console.log('devisClientId est une chaîne ou un ID (dashboard):', devisClientId);
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
          userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
          console.log('userClientId est un objet avec _id (dashboard):', userClientId);
        } else {
          userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
          console.log('userClientId est une chaîne ou un ID (dashboard):', userClientId);
        }

        console.log('Comparaison ID (dashboard):', devisClientId, '===', userClientId, devisClientId === userClientId);
        console.log('Comparaison Email (dashboard):', devisClientEmail, '===', currentUser.email, devisClientEmail === currentUser.email);

        // Vérifier si l'ID correspond OU si l'email correspond
        const matchById = devisClientId === userClientId;
        const matchByEmail = devisClientEmail && currentUser.email && devisClientEmail.toLowerCase() === currentUser.email.toLowerCase();

        const match = matchById || matchByEmail;
        console.log('Résultat de la comparaison (ID ou Email) (dashboard):', match);

        return match;
      });

      console.log('Devis filtrés pour le client:', devisData);

      // Récupérer les paiements du client
      const paiementsResponse = await paiementService.getPaiements();
      console.log('Paiements récupérés du serveur:', paiementsResponse);

      const paiementsData = paiementsResponse.filter(paiement => {
        if (!paiement.factureId || !paiement.factureId.clientId) {
          console.log('Paiement sans factureId ou sans clientId ignoré (dashboard)');
          return false;
        }

        // Afficher plus d'informations sur le paiement et son clientId
        console.log('Structure de paiement.factureId.clientId (dashboard):', paiement.factureId.clientId);

        // Vérifier si clientId est un objet avec _id ou directement un ID
        let paiementClientId;
        let paiementClientEmail = '';

        if (typeof paiement.factureId.clientId === 'object' && paiement.factureId.clientId !== null) {
          paiementClientId = paiement.factureId.clientId._id ? paiement.factureId.clientId._id.toString() : '';
          paiementClientEmail = paiement.factureId.clientId.email || '';
          console.log('paiementClientId est un objet avec _id (dashboard):', paiementClientId);
          console.log('Email du client du paiement (dashboard):', paiementClientEmail);
        } else {
          paiementClientId = paiement.factureId.clientId.toString();
          console.log('paiementClientId est une chaîne ou un ID (dashboard):', paiementClientId);
        }

        let userClientId;
        if (typeof currentUser.clientId === 'object' && currentUser.clientId !== null) {
          userClientId = currentUser.clientId._id ? currentUser.clientId._id.toString() : '';
          console.log('userClientId est un objet avec _id (dashboard):', userClientId);
        } else {
          userClientId = currentUser.clientId ? currentUser.clientId.toString() : '';
          console.log('userClientId est une chaîne ou un ID (dashboard):', userClientId);
        }

        console.log('Comparaison ID (dashboard):', paiementClientId, '===', userClientId, paiementClientId === userClientId);
        console.log('Comparaison Email (dashboard):', paiementClientEmail, '===', currentUser.email, paiementClientEmail === currentUser.email);

        // Vérifier si l'ID correspond OU si l'email correspond
        const matchById = paiementClientId === userClientId;
        const matchByEmail = paiementClientEmail && currentUser.email && paiementClientEmail.toLowerCase() === currentUser.email.toLowerCase();

        const match = matchById || matchByEmail;
        console.log('Résultat de la comparaison (ID ou Email) (dashboard):', match);

        return match;
      });

      console.log('Paiements filtrés pour le client:', paiementsData);

      // Filtrer par période si nécessaire
      let filteredFactures = facturesData;
      let filteredDevis = devisData;
      let filteredPaiements = paiementsData;

      if (period !== 'all') {
        const now = new Date();
        let startDate;

        if (period === 'month') {
          startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        } else if (period === 'quarter') {
          startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
        } else if (period === 'year') {
          startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        } else if (period === 'custom' && customRange) {
          // Pour les dates personnalisées, utiliser les dates de début et de fin du customRange
          if (customRange.startDate && customRange.endDate) {
            startDate = new Date(customRange.startDate);
            const endDate = new Date(customRange.endDate);

            // S'assurer que les dates de fin incluent toute la journée
            endDate.setHours(23, 59, 59, 999);

            console.log('Filtre personnalisé - Date de début:', startDate);
            console.log('Filtre personnalisé - Date de fin:', endDate);

            filteredFactures = facturesData.filter(f => {
              const date = new Date(f.dateEmission);
              return date >= startDate && date <= endDate;
            });

            filteredDevis = devisData.filter(d => {
              const date = new Date(d.dateCréation || d.dateCreation);
              return date >= startDate && date <= endDate;
            });

            filteredPaiements = paiementsData.filter(p => {
              const date = new Date(p.datePaiement);
              return date >= startDate && date <= endDate;
            });
          }
        }

        if (period !== 'custom') {
          // Pour les périodes prédéfinies, filtrer à partir de la date de début jusqu'à maintenant
          const now = new Date();
          now.setHours(23, 59, 59, 999);

          filteredFactures = facturesData.filter(f => {
            const date = new Date(f.dateEmission);
            return date >= startDate && date <= now;
          });

          filteredDevis = devisData.filter(d => {
            const date = new Date(d.dateCréation || d.dateCreation);
            return date >= startDate && date <= now;
          });

          filteredPaiements = paiementsData.filter(p => {
            const date = new Date(p.datePaiement);
            return date >= startDate && date <= now;
          });
        }
      }

      setFactures(filteredFactures);
      setDevis(filteredDevis);
      setPaiements(filteredPaiements);

      // Calculer les statistiques
      const totalMontant = filteredPaiements.reduce((sum, paiement) => sum + paiement.montant, 0);

      // Calculer le changement de montant par rapport à la période précédente
      let montantChange = 0;
      if (period !== 'all' && period !== 'custom') {
        const now = new Date();
        let previousStartDate, previousEndDate;

        if (period === 'month') {
          previousStartDate = new Date(now.getFullYear(), now.getMonth() - 2, now.getDate());
          previousEndDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate() - 1);
        } else if (period === 'quarter') {
          previousStartDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
          previousEndDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate() - 1);
        } else if (period === 'year') {
          previousStartDate = new Date(now.getFullYear() - 2, now.getMonth(), now.getDate());
          previousEndDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate() - 1);
        }

        const previousPeriodPaiements = paiementsData.filter(p => {
          const date = new Date(p.datePaiement);
          return date >= previousStartDate && date <= previousEndDate;
        });

        const previousTotalMontant = previousPeriodPaiements.reduce((sum, paiement) => sum + paiement.montant, 0);

        if (previousTotalMontant > 0) {
          montantChange = ((totalMontant - previousTotalMontant) / previousTotalMontant) * 100;
        }
      }

      // Utiliser normalizeStatus pour normaliser les statuts lors du filtrage
      setStats({
        totalFactures: filteredFactures.length,
        totalDevis: filteredDevis.length,
        facturesPaid: filteredFactures.filter(f => normalizeStatus(f.statut) === 'PAID').length,
        facturesPending: filteredFactures.filter(f => {
          const normalizedStatus = normalizeStatus(f.statut);
          return normalizedStatus !== 'PAID' && normalizedStatus !== 'CANCELED';
        }).length,
        devisAccepted: filteredDevis.filter(d => normalizeStatus(d.statut) === 'ACCEPTED').length,
        devisPending: filteredDevis.filter(d => {
          const normalizedStatus = normalizeStatus(d.statut);
          return normalizedStatus === 'PENDING' || normalizedStatus === 'SENT';
        }).length,
        totalMontant: totalMontant,
        montantChange: montantChange
      });

      // Préparer les données pour le graphique des paiements
      const paymentsByMonth = {};

      // Initialiser tous les mois à 0
      emptyPaymentData.forEach(item => {
        paymentsByMonth[item.month] = 0;
      });

      // Remplir avec les données réelles
      filteredPaiements.forEach(paiement => {
        const date = new Date(paiement.datePaiement);
        const monthIndex = date.getMonth();
        const monthNames = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
        const monthName = monthNames[monthIndex];

        paymentsByMonth[monthName] = (paymentsByMonth[monthName] || 0) + paiement.montant;
      });

      // Convertir en format pour le graphique
      const formattedPaymentData = Object.keys(paymentsByMonth).map(month => ({
        month,
        montant: paymentsByMonth[month]
      }));

      // Trier les mois dans l'ordre chronologique
      const monthOrder = {
        'Jan': 0, 'Fév': 1, 'Mar': 2, 'Avr': 3, 'Mai': 4, 'Juin': 5,
        'Juil': 6, 'Août': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Déc': 11
      };

      formattedPaymentData.sort((a, b) => monthOrder[a.month] - monthOrder[b.month]);

      setPaymentData(formattedPaymentData);

      setLoading(false);
      setRefreshing(false);

      // Afficher une notification de succès si c'est un rafraîchissement manuel
      if (refreshing) {
        setNotification({
          open: true,
          message: 'Données actualisées avec succès',
          severity: 'success'
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setLoading(false);
      setRefreshing(false);
      setNotification({
        open: true,
        message: 'Erreur lors du chargement des données',
        severity: 'error'
      });
    }
  }, [currentUser]); // Suppression de la dépendance refreshing pour éviter les boucles infinies

  useEffect(() => {
    fetchData(dateRange, customDateRange);
  }, [fetchData, dateRange, customDateRange]);

  const handleDateRangeChange = (rangeId, dateRange) => {
    console.log('handleDateRangeChange appelé avec:', rangeId, dateRange);

    // Map the range ID to period format
    let newRange;
    switch(rangeId) {
      case 'thisMonth':
        newRange = 'month';
        break;
      case 'thisQuarter':
        newRange = 'quarter';
        break;
      case 'thisYear':
        newRange = 'year';
        break;
      case 'allTime':
        newRange = 'all';
        break;
      case 'custom':
        newRange = 'custom';
        break;
      default:
        newRange = 'all';
    }

    console.log('Nouvelle période sélectionnée:', newRange);
    console.log('Dates personnalisées:', dateRange);

    setDateRange(newRange);
    setCustomDateRange(dateRange);

    // Forcer l'actualisation des données avec les nouveaux filtres
    setTimeout(() => {
      fetchData(newRange, dateRange);
    }, 100);
  };

  const handleViewFacture = (id) => {
    navigate(`/client/factures/${id}`);
  };

  const handleViewDevis = (id) => {
    navigate(`/client/devis/${id}`);
  };

  const handleDownloadPdf = async (id, type) => {
    try {
      if (type === 'facture') {
        await factureService.generatePdf(id);
      } else {
        await devisService.generatePdf(id);
      }
    } catch (error) {
      console.error(`Erreur lors du téléchargement du ${type}:`, error);
    }
  };

  const handleAcceptDevis = async (id) => {
    try {
      console.log(`Acceptation du devis ${id} depuis le dashboard client`);
      await devisService.acceptDevis(id);
      // Rafraîchir les données
      fetchData(dateRange, customDateRange);
      // Afficher une notification de succès
      setNotification({
        open: true,
        message: 'Devis accepté avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de l\'acceptation du devis:', error);
      setNotification({
        open: true,
        message: `Erreur lors de l'acceptation du devis: ${error.message}`,
        severity: 'error'
      });
    }
  };

  const handleRejectDevis = async (id) => {
    try {
      console.log(`Refus du devis ${id} depuis le dashboard client`);
      await devisService.rejectDevis(id);
      // Rafraîchir les données
      fetchData(dateRange, customDateRange);
      // Afficher une notification de succès
      setNotification({
        open: true,
        message: 'Devis refusé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors du refus du devis:', error);
      setNotification({
        open: true,
        message: `Erreur lors du refus du devis: ${error.message}`,
        severity: 'error'
      });
    }
  };

  const handleRefresh = () => {
    console.log('Actualisation manuelle déclenchée');
    console.log('Période actuelle:', dateRange);
    console.log('Dates personnalisées:', customDateRange);

    // Définir refreshing à true pour afficher l'indicateur de chargement
    setRefreshing(true);

    // Forcer l'actualisation des données
    setTimeout(() => {
      fetchData(dateRange, customDateRange);

      // Afficher une notification de succès
      setNotification({
        open: true,
        message: 'Données actualisées avec succès',
        severity: 'success'
      });

      // Réinitialiser l'état de refreshing après un court délai
      setTimeout(() => {
        setRefreshing(false);
      }, 500);
    }, 100);
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  // Composant KPI Card
  const KpiCard = ({ title, value, subtitle, icon, color, trend, trendValue }) => {
    const isTrendPositive = trendValue > 0;
    const trendIcon = isTrendPositive ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />;
    const trendColor = isTrendPositive ? 'success' : 'error';

    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <Card
          elevation={0}
          sx={{
            height: '100%',
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette[color].light, 0.2)} 0%, ${alpha(theme.palette[color].main, 0.05)} 100%)`,
            border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette[color].main, 0.03)} 100%)`,
              zIndex: 0
            }
          }}
        >
          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  backgroundColor: alpha(theme.palette[color].main, 0.15),
                  color: theme.palette[color].main,
                  mr: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette[color].main, 0.2)}`
                }}
              >
                {icon}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="500" color="text.primary">
                  {title}
                </Typography>
                {trend && trendValue !== 0 && (
                  <Chip
                    icon={trendIcon}
                    label={`${Math.abs(trendValue).toFixed(1)}%`}
                    size="small"
                    color={trendColor}
                    sx={{
                      height: 24,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      mt: 0.5
                    }}
                  />
                )}
              </Box>
            </Box>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                color: theme.palette[color].dark,
                textShadow: `0 2px 4px ${alpha(theme.palette[color].main, 0.2)}`
              }}
            >
              {value}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                opacity: 0.8,
                maxWidth: '90%'
              }}
            >
              {subtitle}
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header Section with Title and Actions */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', md: 'center' },
          mb: 4
        }}
        component={motion.div}
        variants={itemVariants}
      >
        <Box sx={{ mb: { xs: 2, md: 0 } }}>
          <Typography
            variant="h4"
            component="h1"
            fontWeight="bold"
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1
            }}
          >
            Tableau de Bord Client
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Suivez vos factures, devis et paiements
          </Typography>
        </Box>

        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          sx={{ mt: { xs: 2, md: 0 }, width: { xs: '100%', sm: 'auto' } }}
        >
          <SimpleDateFilter
            onDateRangeChange={handleDateRangeChange}
            onRefresh={handleRefresh}
            initialRange={dateRange}
            showRefreshButton={false}
          />

          <Button
            variant="contained"
            color="primary"
            onClick={handleRefresh}
            startIcon={refreshing ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
            disabled={refreshing}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
              minWidth: 130
            }}
          >
            {refreshing ? 'Actualisation...' : 'Actualiser'}
          </Button>
        </Stack>
      </Box>

      {/* Dashboard Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* KPI Cards */}
        <Grid
          container
          spacing={3}
          sx={{ mb: 4 }}
        >
          <Grid item xs={12} sm={6} md={4}>
            <KpiCard
              title="Montant payé"
              value={formatCurrency(stats.totalMontant)}
              subtitle={`${stats.facturesPaid} facture${stats.facturesPaid !== 1 ? 's' : ''} payée${stats.facturesPaid !== 1 ? 's' : ''}`}
              icon={<MoneyIcon />}
              color="success"
              trend={true}
              trendValue={stats.montantChange}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <KpiCard
              title="Factures"
              value={stats.totalFactures}
              subtitle={`${stats.facturesPaid} payée${stats.facturesPaid !== 1 ? 's' : ''}, ${stats.facturesPending} en attente`}
              icon={<ReceiptIcon />}
              color="primary"
              trend={false}
              trendValue={0}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <KpiCard
              title="Devis"
              value={stats.totalDevis}
              subtitle={`${stats.devisAccepted} accepté${stats.devisAccepted !== 1 ? 's' : ''}, ${stats.devisPending} en attente`}
              icon={<DescriptionIcon />}
              color="info"
              trend={false}
              trendValue={0}
            />
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid container spacing={3} component={motion.div} variants={itemVariants}>
          {/* Derniers documents */}
          <Grid item xs={12} md={6}>
            <Card
              elevation={1}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                height: '100%',
                position: 'relative'
              }}
            >
              <CardHeader
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        width: 36,
                        height: 36,
                        mr: 1.5
                      }}
                    >
                      <ReceiptIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="h6" fontWeight="600">
                      Dernières factures
                    </Typography>
                  </Box>
                }
                action={
                  <Button
                    endIcon={<ArrowForwardIcon />}
                    onClick={() => navigate('/client/factures')}
                    size="small"
                    sx={{
                      borderRadius: 2,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1)
                      }
                    }}
                  >
                    Voir toutes
                  </Button>
                }
                sx={{
                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  p: 2
                }}
              />
              <List sx={{ p: 0 }}>
                {factures.length > 0 ? (
                  factures.slice(0, 5).map((facture) => (
                    <React.Fragment key={facture._id}>
                      <ListItem
                        sx={{
                          py: 1.5,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.03)
                          }
                        }}
                        secondaryAction={
                          <Box sx={{ display: 'flex' }}>
                            <Tooltip title="Voir détails">
                              <IconButton
                                size="small"
                                onClick={() => handleViewFacture(facture._id)}
                                sx={{
                                  color: theme.palette.primary.main,
                                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                  mr: 1,
                                  '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.2) }
                                }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Télécharger PDF">
                              <IconButton
                                size="small"
                                onClick={() => handleDownloadPdf(facture._id, 'facture')}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                  '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                                }}
                              >
                                <PdfIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      >
                        <ListItemIcon>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main
                            }}
                          >
                            <ReceiptIcon fontSize="small" />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body1" fontWeight="medium">
                              {facture.numero}
                            </Typography>
                          }
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                              <Typography variant="body2" color="text.secondary">
                                {formatDate(facture.dateEmission)} - {formatCurrency(facture.total)}
                              </Typography>
                              <Chip
                                size="small"
                                label={formatStatut(facture.statut)}
                                color={facture.statut === 'PAID' ? 'success' : 'warning'}
                                sx={{ borderRadius: 1, height: 22 }}
                              />
                            </Box>
                          }
                        />
                      </ListItem>
                      <Divider component="li" />
                    </React.Fragment>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText
                      primary={
                        <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                          Aucune facture disponible
                        </Typography>
                      }
                    />
                  </ListItem>
                )}
              </List>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card
              elevation={1}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                height: '100%',
                position: 'relative'
              }}
            >
              <CardHeader
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.info.main, 0.1),
                        color: theme.palette.info.main,
                        width: 36,
                        height: 36,
                        mr: 1.5
                      }}
                    >
                      <DescriptionIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="h6" fontWeight="600">
                      Derniers devis
                    </Typography>
                  </Box>
                }
                action={
                  <Button
                    endIcon={<ArrowForwardIcon />}
                    onClick={() => navigate('/client/devis')}
                    size="small"
                    sx={{
                      borderRadius: 2,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.info.main, 0.1)
                      }
                    }}
                  >
                    Voir tous
                  </Button>
                }
                sx={{
                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  p: 2
                }}
              />
              <List sx={{ p: 0 }}>
                {devis.length > 0 ? (
                  devis.slice(0, 5).map((devis) => (
                    <React.Fragment key={devis._id}>
                      <ListItem
                        sx={{
                          py: 1.5,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.info.main, 0.03)
                          }
                        }}
                        secondaryAction={
                          <Box sx={{ display: 'flex' }}>
                            {(devis.statut === 'PENDING' || devis.statut === 'SENT' || devis.statut === 'ENVOYÉ') && (
                              <>
                                <Tooltip title="Accepter">
                                  <IconButton
                                    size="small"
                                    color="success"
                                    onClick={() => handleAcceptDevis(devis._id)}
                                    sx={{
                                      backgroundColor: alpha(theme.palette.success.main, 0.1),
                                      mr: 1,
                                      '&:hover': { backgroundColor: alpha(theme.palette.success.main, 0.2) }
                                    }}
                                  >
                                    <CheckCircleIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Refuser">
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => handleRejectDevis(devis._id)}
                                    sx={{
                                      backgroundColor: alpha(theme.palette.error.main, 0.1),
                                      mr: 1,
                                      '&:hover': { backgroundColor: alpha(theme.palette.error.main, 0.2) }
                                    }}
                                  >
                                    <CancelIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </>
                            )}
                            <Tooltip title="Voir détails">
                              <IconButton
                                size="small"
                                onClick={() => handleViewDevis(devis._id)}
                                sx={{
                                  color: theme.palette.info.main,
                                  backgroundColor: alpha(theme.palette.info.main, 0.1),
                                  mr: 1,
                                  '&:hover': { backgroundColor: alpha(theme.palette.info.main, 0.2) }
                                }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Télécharger PDF">
                              <IconButton
                                size="small"
                                onClick={() => handleDownloadPdf(devis._id, 'devis')}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                  '&:hover': { backgroundColor: alpha(theme.palette.text.secondary, 0.2) }
                                }}
                              >
                                <PdfIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      >
                        <ListItemIcon>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.info.main, 0.1),
                              color: theme.palette.info.main
                            }}
                          >
                            <DescriptionIcon fontSize="small" />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body1" fontWeight="medium">
                              {devis.numéro}
                            </Typography>
                          }
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                              <Typography variant="body2" color="text.secondary">
                                {formatDate(devis.dateCréation)} - {formatCurrency(devis.total)}
                              </Typography>
                              <Chip
                                size="small"
                                label={formatStatut(devis.statut)}
                                color={
                                  devis.statut === 'ACCEPTED' || devis.statut === 'ACCEPTÉ' ? 'success' :
                                  devis.statut === 'REJECTED' || devis.statut === 'REFUSÉ' ? 'error' : 'warning'
                                }
                                sx={{ borderRadius: 1, height: 22 }}
                              />
                            </Box>
                          }
                        />
                      </ListItem>
                      <Divider component="li" />
                    </React.Fragment>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText
                      primary={
                        <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                          Aucun devis disponible
                        </Typography>
                      }
                    />
                  </ListItem>
                )}
              </List>
            </Card>
          </Grid>

          {/* Export Section */}
          <Grid item xs={12} component={motion.div} variants={itemVariants} sx={{ mt: 3 }}>
            <Card
              elevation={1}
              sx={{
                borderRadius: 2,
                overflow: 'hidden'
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h6" fontWeight="600">
                    Exporter les données
                  </Typography>
                }
                sx={{
                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  p: 2
                }}
              />
              <CardContent>
                <PowerBIExport />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </motion.div>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ClientDashboard;
