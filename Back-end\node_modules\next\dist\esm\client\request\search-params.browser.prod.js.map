{"version": 3, "sources": ["../../../src/client/request/search-params.browser.prod.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  return promise\n}\n"], "names": ["wellKnownProperties", "CachedSearchParams", "WeakMap", "makeUntrackedExoticSearchParams", "underlyingSearchParams", "cachedSearchParams", "get", "promise", "Promise", "resolve", "set", "Object", "keys", "for<PERSON>ach", "prop", "has"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,uCAAsC;AAG1E,MAAMC,qBAAqB,IAAIC;AAE/B,OAAO,SAASC,gCACdC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAChCH,mBAAmBS,GAAG,CAACN,wBAAwBG;IAE/CI,OAAOC,IAAI,CAACR,wBAAwBS,OAAO,CAAC,CAACC;QAC3C,IAAId,oBAAoBe,GAAG,CAACD,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHP,OAAe,CAACO,KAAK,GAAGV,sBAAsB,CAACU,KAAK;QACxD;IACF;IAEA,OAAOP;AACT"}