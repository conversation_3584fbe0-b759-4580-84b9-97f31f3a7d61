import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  Card,
  useTheme,
  InputAdornment,
  IconButton,
  CircularProgress
} from '@mui/material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import logo from '../assets/benyounes_logo.png';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import LoginIcon from '@mui/icons-material/Login';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';

const AdminLogin = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    motDePasse: '',
    role: 'ADMIN',
  });
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Simulate loading for demo purposes
    const timer = setTimeout(() => {
      // Any initialization code can go here
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Admin login form submitted with data:', formData);
    setError('');
    setLoading(true);

    try {
      const response = await login(formData.email, formData.motDePasse, formData.role);
      console.log('Admin login response:', response);

      // Verify if the user is actually an admin
      if (response.user && response.user.role === 'ADMIN') {
        // Add a small delay for better UX
        setTimeout(() => {
          navigate('/admin/analytics');
          setLoading(false);
        }, 1000);
      } else {
        setError('Vous n\'avez pas les droits d\'administrateur.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Admin login error:', err);
      setError(err.response?.data?.error || 'Erreur lors de la connexion');
      setLoading(false);
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      component={motion.div}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      sx={{
        display: 'flex',
        minHeight: '100vh',
        backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient 15s ease infinite',
        alignItems: 'center',
        justifyContent: 'center',
        padding: { xs: 2, md: 4 },
        '@keyframes gradient': {
          '0%': {
            backgroundPosition: '0% 50%'
          },
          '50%': {
            backgroundPosition: '100% 50%'
          },
          '100%': {
            backgroundPosition: '0% 50%'
          },
        },
      }}
    >
      <Card
        elevation={6}
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          width: '100%',
          maxWidth: '1000px',
          borderRadius: 4,
          overflow: 'hidden',
          background: '#fff',
        }}
      >
        {/* Left Side - Brand Section */}
        <Box
          component={motion.div}
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.45 },
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, #1a237e 100%)`,
            backgroundSize: '200% 200%',
            animation: 'gradientBg 10s ease infinite',
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 4,
            position: 'relative',
            overflow: 'hidden',
            '@keyframes gradientBg': {
              '0%': {
                backgroundPosition: '0% 50%'
              },
              '50%': {
                backgroundPosition: '100% 50%'
              },
              '100%': {
                backgroundPosition: '0% 50%'
              },
            },
          }}
        >
          {/* Background Pattern */}
          <Box sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            opacity: 0.1,
            backgroundImage: `
              radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
              radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 40%)
            `,
            zIndex: 0,
          }} />

          <Box sx={{
            textAlign: 'center',
            position: 'relative',
            zIndex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}>
            <Box
              component={motion.img}
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              src={logo}
              alt="BENYOUNES WEB Logo"
              sx={{
                width: '200px',
                mb: 3,
                filter: 'brightness(1.05)',
                animation: 'float 6s ease-in-out infinite',
                '@keyframes float': {
                  '0%': {
                    transform: 'translateY(0px)'
                  },
                  '50%': {
                    transform: 'translateY(-10px)'
                  },
                  '100%': {
                    transform: 'translateY(0px)'
                  },
                },
              }}
            />

            <Typography
              component={motion.h1}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.5 }}
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 2,
                letterSpacing: '0.5px',
                textShadow: '0 2px 10px rgba(0,0,0,0.1)',
              }}
            >
              Administration
            </Typography>

            <Typography
              component={motion.p}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.5 }}
              variant="body1"
              sx={{
                mb: 3,
                opacity: 0.9,
                maxWidth: '350px',
                textAlign: 'center',
                lineHeight: 1.6,
              }}
            >
              Connectez-vous à votre espace administrateur pour gérer l'application.
            </Typography>

            <Box sx={{ mt: 3, display: { xs: 'none', md: 'block' }}}>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                © {new Date().getFullYear()} BENYOUNES WEB - Tous droits réservés
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Right Side - Form */}
        <Box
          component={motion.div}
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.55 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            padding: { xs: 3, sm: 4, md: 5 },
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Box sx={{ maxWidth: '450px', width: '100%', mx: 'auto', position: 'relative' }}>
            <Box sx={{
              position: 'absolute',
              top: 0,
              right: 0
            }}>
              <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={() => navigate('/login')}
                sx={{
                  borderRadius: 2,
                  padding: '6px 12px',
                  fontSize: '0.8rem',
                  fontWeight: 600,
                  textTransform: 'none',
                  '&:hover': {
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    transform: 'translateY(-1px)'
                  }
                }}
              >
                Retour
              </Button>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, pt: 5 }}>
              <AdminPanelSettingsIcon sx={{ fontSize: 32, color: theme.palette.primary.main, mr: 1 }} />
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                }}
              >
                Connexion Admin
              </Typography>
            </Box>

            <Typography
              variant="body2"
              sx={{
                mb: 4,
                color: theme.palette.text.secondary,
              }}
            >
              Entrez vos identifiants administrateur pour accéder au panneau d'administration
            </Typography>

            {error && (
              <Alert
                severity="error"
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                {error}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <TextField
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: theme.palette.background.default,
                    transition: 'all 0.3s ease',
                    '&:hover, &.Mui-focused': {
                      backgroundColor: '#fff',
                      boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                    }
                  },
                }}
              />

              <TextField
                label="Mot de passe"
                name="motDePasse"
                type={showPassword ? 'text' : 'password'}
                value={formData.motDePasse}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                        aria-label={showPassword ? 'hide password' : 'show password'}
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                sx={{
                  mb: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: theme.palette.background.default,
                    transition: 'all 0.3s ease',
                    '&:hover, &.Mui-focused': {
                      backgroundColor: '#fff',
                      boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                    }
                  },
                }}
              />

              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={loading}
                disableElevation
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                sx={{
                  mt: 2,
                  mb: 3,
                  backgroundColor: theme.palette.primary.main,
                  color: '#fff',
                  borderRadius: 2,
                  padding: '12px',
                  fontWeight: 600,
                  textTransform: 'none',
                  fontSize: '1rem',
                  transition: 'all 0.3s',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: '-100%',
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                    transition: 'all 0.5s',
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                    boxShadow: '0 4px 12px rgba(63, 81, 255, 0.25)',
                    transform: 'translateY(-2px)',
                    '&::after': {
                      left: '100%',
                    }
                  },
                }}
              >
                {loading ? 'Connexion en cours...' : 'Se connecter'}
              </Button>
            </form>

            <Typography
              variant="body2"
              sx={{
                mt: 3,
                textAlign: 'center',
                color: theme.palette.text.secondary,
              }}
            >
              Accès réservé aux administrateurs
            </Typography>
          </Box>
        </Box>
      </Card>
    </Box>
  );
};

export default AdminLogin;
