# Updated UML Use Case Diagrams - Invoice/Quote Management System

## System Overview
The system has been updated with new features including <PERSON> (Delivery Notes), <PERSON><PERSON>ur (Delivery Person) management, enhanced subscription management, and improved authentication.

## User Roles
- **ADMIN**: System administrator
- **RESPONSABLE**: Enterprise manager (manages vendeurs and enterprise settings)
- **VENDEUR**: Salesperson (creates invoices, quotes, manages clients/products)
- **CLIENT**: Customer (views documents, makes payments)

---

## 1. CLIENT Use Cases

### Primary Actor: Client

### Use Cases:
1. **S'authentifier**
   - Login with email or CIN (Carte d'Identité Nationale)
   - Password reset via OTP
   - Automatic role detection

2. **Consulter tableau de bord**
   - View dashboard with KPIs
   - Total amount paid
   - Invoice statistics (paid/pending)
   - Quote statistics (accepted/pending)

3. **Gérer les documents**
   - **Consulter factures**
     - View invoice list with filters
     - View invoice details
     - Download PDF
     - Print invoice
   - **Consulter devis**
     - View quote list
     - View quote details
     - Accept/refuse quotes
     - Download PDF
     - Print quote
   - **Consulter bons de livraison**
     - View delivery notes
     - Track delivery status
     - View delivery details

4. **Effectuer paiements**
   - **Payer par virement bancaire**
   - **Payer par chèque**
   - **Payer par espèces**
   - View payment history
   - Download payment receipts

5. **Demander devis**
   - Submit quote requests
   - Specify product requirements
   - Add custom notes

6. **Gérer profil**
   - Update personal information
   - Change password
   - Update contact details

### Extensions:
- **<<include>>** S'authentifier (for all authenticated actions)
- **<<extend>>** Notifications for payment confirmations
- **<<extend>>** Email notifications for new documents

---

## 2. VENDEUR Use Cases

### Primary Actor: Vendeur

### Use Cases:
1. **S'authentifier**
   - Login with email/password
   - Role-based dashboard redirection

2. **Gérer tableau de bord**
   - View analytics and KPIs
   - Revenue statistics
   - Client growth metrics
   - Product performance

3. **Gérer les documents**
   - **Gérer les factures**
     - Create invoice
     - Modify invoice
     - Send invoice (email)
     - Download PDF
     - Print invoice
     - Convert quote to invoice
   - **Gérer les devis**
     - Create quote
     - Modify quote
     - Send quote (email)
     - Download PDF
     - Print quote
     - Convert to invoice
   - **Gérer bons de livraison**
     - Create delivery note
     - Assign delivery person
     - Track delivery status
     - Update delivery information
     - Send delivery confirmation

4. **Gérer les clients**
   - Add client
   - Modify client
   - Delete client
   - View client details
   - Client CIN management

5. **Gérer les produits**
   - Add product
   - Modify product
   - Delete product
   - Manage stock
   - Track product sales
   - Upload product images

6. **Gérer les livreurs**
   - View assigned delivery persons
   - Check availability
   - Assign deliveries
   - Track performance

7. **Gérer son profil**
   - Update profile information
   - Change password
   - View account settings

### Extensions:
- **<<include>>** S'authentifier (for all actions)
- **<<extend>>** Email notifications
- **<<extend>>** PDF generation
- **<<extend>>** Stock updates when products sold

---

## 3. RESPONSABLE D'ENTREPRISE Use Cases

### Primary Actor: Responsable d'Entreprise

### Use Cases:
1. **S'authentifier**
   - Login with email/password
   - Enterprise account validation

2. **Gérer tableau de bord**
   - View enterprise analytics
   - Team performance metrics
   - Financial overview
   - Subscription status

3. **Gérer l'équipe**
   - **Gérer les vendeurs**
     - Add vendeur
     - Modify vendeur
     - Delete vendeur
     - Assign permissions
   - **Gérer les livreurs**
     - Add delivery person
     - Modify delivery person
     - Set availability
     - Track performance
     - Manage delivery zones

4. **Gérer les documents**
   - View all team documents
   - Approve/validate quotes
   - Monitor document status
   - Access team reports

5. **Gérer l'entreprise**
   - **Configurer informations entreprise**
     - Company details
     - Logo upload
     - Contact information
   - **Personnaliser templates**
     - Choose base template (Standard/Moderne)
     - Customize colors
     - Add company branding
   - **Gérer paramètres**
     - Document prefixes
     - Payment modes
     - Business settings

6. **Gérer abonnement**
   - View subscription status
   - Monitor usage limits
   - Request subscription renewal
   - View billing history
   - Upgrade/downgrade plans

7. **Gérer son profil**
   - Update personal information
   - Change password
   - Account settings

### Extensions:
- **<<include>>** S'authentifier (for all actions)
- **<<extend>>** Subscription expiry notifications
- **<<extend>>** Team activity monitoring
- **<<extend>>** Automatic account suspension on expiry

---

## 4. ADMIN Use Cases

### Primary Actor: Admin

### Use Cases:
1. **S'authentifier**
   - Admin login with special credentials
   - System access validation

2. **Gérer tableau de bord**
   - System-wide analytics
   - User statistics
   - Revenue metrics
   - System health monitoring

3. **Gérer les utilisateurs**
   - **Gérer les entreprises**
     - Create enterprise accounts
     - Modify enterprise details
     - Suspend/activate accounts
     - View enterprise statistics
   - **Gérer les vendeurs**
     - View all vendeurs
     - Modify vendeur accounts
     - Reset passwords
   - **Gérer les clients**
     - System-wide client management
     - Resolve client issues

4. **Gérer les templates**
   - **Créer templates de base**
     - Standard template
     - Moderne template
   - **Modifier templates**
   - **Configurer options**
     - Available customizations
     - Template settings

5. **Gérer les abonnements**
   - **Créer plans d'abonnement**
   - **Modifier tarifs**
   - **Gérer renouvellements**
   - **Traiter demandes**
   - **Suspendre comptes expirés**
   - Monitor subscription metrics

6. **Gérer paramètres système**
   - **Configurer paramètres globaux**
     - Currency settings (DT only)
     - System preferences
     - Email configurations
   - **Gérer sécurité**
     - User permissions
     - System access controls

7. **Gérer son profil**
   - Update admin information
   - Change password
   - System preferences

### Extensions:
- **<<include>>** S'authentifier (for all actions)
- **<<extend>>** System monitoring
- **<<extend>>** Automated subscription management
- **<<extend>>** Backup and maintenance

---

## Key System Changes from Original Diagrams:

### Added Features:
1. **Bon de Livraison** - Complete delivery note management
2. **Livreur Management** - Delivery person system
3. **CIN Authentication** - Clients can login with ID number
4. **Enhanced Subscription Management** - Real-time monitoring
5. **Template Customization** - Standard/Moderne with branding
6. **Role-based Permissions** - Enhanced security

### Removed Features:
1. **Remember Me** option from login
2. **Multiple Currency** support (only Tunisian Dinar)
3. **Payment Delay** fields
4. **Unused Admin Parameters** (emailSender, autoBackup, etc.)

### Updated Workflows:
1. **Document Management** - Enhanced with delivery notes
2. **Authentication** - Simplified with automatic role detection
3. **Enterprise Management** - Consolidated under RESPONSABLE role
4. **Payment Processing** - Streamlined payment modes
5. **Template System** - Professional template management

## Technical Implementation Notes:
- MongoDB collections optimized for role-based access
- Real-time subscription monitoring
- Automated PDF generation for all document types
- Email integration for document delivery
- Stock management with automatic updates
- Performance tracking for delivery personnel
