import React, { useState, useEffect } from 'react';
import { Box, Typo<PERSON>, TextField, Button, Paper, Alert, CircularProgress, Grid, Tabs, Tab, InputAdornment, Switch, FormControlLabel, IconButton } from '@mui/material';
import { ColorPicker, createColor } from 'material-ui-color';
import entrepriseService from '../services/entrepriseService';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const EntrepriseProfile = () => {
  const [tabValue, setTabValue] = useState(0);
  const [profile, setProfile] = useState({
    nomCommercial: '',
    raisonSociale: '',
    numéroFiscal: '',
    numéroTVA: '',
    formeJuridique: '',
    capitalSocial: '',
    adresse: '',
    codePostal: '',
    ville: '',
    pays: 'France',
    telephone: '',
    email: '',
    siteWeb: '',
    nomContact: '',
    emailContact: '',
    telephoneContact: '',
    prefixeFacture: 'FACT-',
    prefixeDevis: 'DEV-',
    prochainNumeroFacture: 1,
    prochainNumeroDevis: 1,
    tauxTVA: 20,
    delaiPaiement: 30,
    mentionsLegales: '',
    nomBanque: '',
    titulaireCompte: '',
    iban: '',
    bicSwift: '',
    logo: '',
    couleurPrincipale: '#1976D2',
    couleurSecondaire: '#F5F5F5',
    signature: '',
    signatureElectronique: false,
    paiementEnLigne: false,
    rappelsAutomatiques: false,
    numerotationChronologique: false,
  });
  const [logoFile, setLogoFile] = useState(null);
  const [signatureFile, setSignatureFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState('');
  const [signaturePreview, setSignaturePreview] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [formValid, setFormValid] = useState(false); // Track form validity

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        const data = await entrepriseService.getCompanyProfile();
        if (data.message) {
          setSuccess('Aucun profil existant. Veuillez créer un nouveau profil.');
        } else {
          setProfile({
            nomCommercial: data.nom || '',
            raisonSociale: data.raisonSociale || '',
            numéroFiscal: data.numéroFiscal || '',
            numéroTVA: data.numéroTVA || '',
            formeJuridique: data.formeJuridique || '',
            capitalSocial: data.capitalSocial || '',
            adresse: data.adresse || '',
            codePostal: data.codePostal || '',
            ville: data.ville || '',
            pays: data.pays || 'France',
            telephone: data.telephone || '',
            email: data.email || '',
            siteWeb: data.siteWeb || '',
            nomContact: data.nomContact || '',
            emailContact: data.emailContact || '',
            telephoneContact: data.telephoneContact || '',
            prefixeFacture: data.prefixeFacture || 'FACT-',
            prefixeDevis: data.prefixeDevis || 'DEV-',
            prochainNumeroFacture: data.prochainNumeroFacture || 1,
            prochainNumeroDevis: data.prochainNumeroDevis || 1,
            tauxTVA: data.tauxTVA || 20,
            delaiPaiement: data.delaiPaiement || 30,
            mentionsLegales: data.mentionsLegales || '',
            nomBanque: data.nomBanque || '',
            titulaireCompte: data.titulaireCompte || '',
            iban: data.iban || '',
            bicSwift: data.bicSwift || '',
            logo: data.logo || '',
            couleurPrincipale: data.couleurPrincipale || '#1976D2',
            couleurSecondaire: data.couleurSecondaire || '#F5F5F5',
            signature: data.signature || '',
            signatureElectronique: data.signatureElectronique || false,
            paiementEnLigne: data.paiementEnLigne || false,
            rappelsAutomatiques: data.rappelsAutomatiques || false,
            numerotationChronologique: data.numerotationChronologique || false,
          });
          setLogoPreview(data.logo || '');
          setSignaturePreview(data.signature || '');
        }
      } catch (err) {
        setError('Erreur lors de la récupération du profil.');
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, []);

  // Validate form whenever profile changes
  useEffect(() => {
    const isValid =
      profile.nomCommercial &&
      /^\d{14}$/.test(profile.numéroFiscal) &&
      profile.adresse &&
      profile.codePostal &&
      profile.ville;
    setFormValid(isValid);
  }, [profile]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfile((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setProfile((prev) => ({ ...prev, [name]: checked }));
  };

  const handleColorChange = (name) => (color) => {
    const hexColor = `#${color.hex}`;
    setProfile((prev) => ({ ...prev, [name]: hexColor }));
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (file) {
      if (type === 'logo') {
        setLogoFile(file);
        setLogoPreview(URL.createObjectURL(file));
      } else if (type === 'signature') {
        setSignatureFile(file);
        setSignaturePreview(URL.createObjectURL(file));
      }
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validation
    if (!profile.nomCommercial) {
      setError('Le nom commercial est requis.');
      setLoading(false);
      return;
    }
    if (!/^\d{14}$/.test(profile.numéroFiscal)) {
      setError('Le numéro SIRET doit contenir 14 chiffres.');
      setLoading(false);
      return;
    }
    if (!profile.adresse) {
      setError("L'adresse est requise.");
      setLoading(false);
      return;
    }
    if (!profile.codePostal) {
      setError('Le code postal est requis.');
      setLoading(false);
      return;
    }
    if (!profile.ville) {
      setError('La ville est requise.');
      setLoading(false);
      return;
    }

    try {
      const profileData = {
        nom: profile.nomCommercial,
        raisonSociale: profile.raisonSociale,
        numéroFiscal: profile.numéroFiscal,
        numéroTVA: profile.numéroTVA,
        formeJuridique: profile.formeJuridique,
        capitalSocial: profile.capitalSocial,
        adresse: profile.adresse,
        codePostal: profile.codePostal,
        ville: profile.ville,
        pays: profile.pays,
        telephone: profile.telephone,
        email: profile.email,
        siteWeb: profile.siteWeb,
        nomContact: profile.nomContact,
        emailContact: profile.emailContact,
        telephoneContact: profile.telephoneContact,
        prefixeFacture: profile.prefixeFacture,
        prefixeDevis: profile.prefixeDevis,
        prochainNumeroFacture: profile.prochainNumeroFacture,
        prochainNumeroDevis: profile.prochainNumeroDevis,
        tauxTVA: profile.tauxTVA,
        delaiPaiement: profile.delaiPaiement,
        mentionsLegales: profile.mentionsLegales,
        nomBanque: profile.nomBanque,
        titulaireCompte: profile.titulaireCompte,
        iban: profile.iban,
        bicSwift: profile.bicSwift,
        couleurPrincipale: profile.couleurPrincipale,
        couleurSecondaire: profile.couleurSecondaire,
        signatureElectronique: profile.signatureElectronique,
        paiementEnLigne: profile.paiementEnLigne,
        rappelsAutomatiques: profile.rappelsAutomatiques,
        numerotationChronologique: profile.numerotationChronologique,
      };

      const response = await entrepriseService.saveCompanyProfile(profileData, logoFile, signatureFile);
      setProfile({
        nomCommercial: response.nom || '',
        raisonSociale: response.raisonSociale || '',
        numéroFiscal: response.numéroFiscal || '',
        numéroTVA: response.numéroTVA || '',
        formeJuridique: response.formeJuridique || '',
        capitalSocial: response.capitalSocial || '',
        adresse: response.adresse || '',
        codePostal: response.codePostal || '',
        ville: response.ville || '',
        pays: response.pays || 'France',
        telephone: response.telephone || '',
        email: response.email || '',
        siteWeb: response.siteWeb || '',
        nomContact: response.nomContact || '',
        emailContact: response.emailContact || '',
        telephoneContact: response.telephoneContact || '',
        prefixeFacture: response.prefixeFacture || 'FACT-',
        prefixeDevis: response.prefixeDevis || 'DEV-',
        prochainNumeroFacture: response.prochainNumeroFacture || 1,
        prochainNumeroDevis: response.prochainNumeroDevis || 1,
        tauxTVA: response.tauxTVA || 20,
        delaiPaiement: response.delaiPaiement || 30,
        mentionsLegales: response.mentionsLegales || '',
        nomBanque: response.nomBanque || '',
        titulaireCompte: response.titulaireCompte || '',
        iban: response.iban || '',
        bicSwift: response.bicSwift || '',
        logo: response.logo || '',
        couleurPrincipale: response.couleurPrincipale || '#1976D2',
        couleurSecondaire: response.couleurSecondaire || '#F5F5F5',
        signature: response.signature || '',
        signatureElectronique: response.signatureElectronique || false,
        paiementEnLigne: response.paiementEnLigne || false,
        rappelsAutomatiques: response.rappelsAutomatiques || false,
        numerotationChronologique: response.numerotationChronologique || false,
      });
      setLogoPreview(response.logo || '');
      setSignaturePreview(response.signature || '');
      setSuccess('Profil enregistré avec succès !');
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Erreur lors de la sauvegarde des informations.';
      const errorDetails = err.response?.data?.details || '';
      setError(errorDetails ? `${errorMessage}: ${errorDetails}` : errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h5" sx={{ mb: 3 }}>Profil de l'Entreprise</Typography>
      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="Informations générales" />
        <Tab label="Coordonnées" />
        <Tab label="Facturation" />
        <Tab label="Banque" />
        <Tab label="Identité visuelle" />
        <Tab label="Options avancées" />
      </Tabs>

      {tabValue === 0 && (
        <Paper sx={{ p: 3, borderRadius: 2 }}>
          {loading && <CircularProgress sx={{ display: 'block', mx: 'auto', mb: 2 }} />}
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}
          <Typography variant="h6" sx={{ mb: 2 }}>Informations générales</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Nom commercial *"
                name="nomCommercial"
                value={profile.nomCommercial}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Raison sociale"
                name="raisonSociale"
                value={profile.raisonSociale}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Numéro SIRET *"
                name="numéroFiscal"
                value={profile.numéroFiscal}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
                error={profile.numéroFiscal && !/^\d{14}$/.test(profile.numéroFiscal)}
                helperText={
                  profile.numéroFiscal && !/^\d{14}$/.test(profile.numéroFiscal)
                    ? 'Doit contenir 14 chiffres sans espaces'
                    : profile.numéroFiscal.length === 14 ? '14 chiffres sans espaces' : ''
                }
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Numéro de TVA"
                name="numéroTVA"
                value={profile.numéroTVA}
                onChange={handleChange}
                fullWidth
                margin="normal"
                helperText="Format: FR12345678901"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Forme juridique"
                name="formeJuridique"
                value={profile.formeJuridique}
                onChange={handleChange}
                fullWidth
                margin="normal"
                helperText="Ex: SARL, SAS, Auto-entrepreneur..."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Capital social"
                name="capitalSocial"
                value={profile.capitalSocial}
                onChange={handleChange}
                fullWidth
                margin="normal"
                InputProps={{
                  endAdornment: <InputAdornment position="end">€</InputAdornment>,
                }}
              />
            </Grid>
          </Grid>
        </Paper>
      )}

      {tabValue === 1 && (
        <Paper sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Coordonnées</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Adresse *"
                name="adresse"
                value={profile.adresse}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Code postal *"
                name="codePostal"
                value={profile.codePostal}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Ville *"
                name="ville"
                value={profile.ville}
                onChange={handleChange}
                fullWidth
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Pays"
                name="pays"
                value={profile.pays}
                onChange={handleChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Téléphone"
                name="telephone"
                value={profile.telephone}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Email"
                name="email"
                value={profile.email}
                onChange={handleChange}
                fullWidth
                margin="normal"
                type="email"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Site web"
                name="siteWeb"
                value={profile.siteWeb}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>Contact principal</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Nom du contact"
                name="nomContact"
                value={profile.nomContact}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Email du contact"
                name="emailContact"
                value={profile.emailContact}
                onChange={handleChange}
                fullWidth
                margin="normal"
                type="email"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Téléphone du contact"
                name="telephoneContact"
                value={profile.telephoneContact}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
          </Grid>
        </Paper>
      )}

      {tabValue === 2 && (
        <Paper sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Facturation</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Préfixe des factures"
                name="prefixeFacture"
                value={profile.prefixeFacture}
                onChange={handleChange}
                fullWidth
                margin="normal"
                helperText="Ex: FACT-, F-, etc."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Préfixe des devis"
                name="prefixeDevis"
                value={profile.prefixeDevis}
                onChange={handleChange}
                fullWidth
                margin="normal"
                helperText="Ex: DEV-, D-, etc."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Prochain numéro de facture"
                name="prochainNumeroFacture"
                value={profile.prochainNumeroFacture}
                onChange={handleChange}
                fullWidth
                margin="normal"
                type="number"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Prochain numéro de devis"
                name="prochainNumeroDevis"
                value={profile.prochainNumeroDevis}
                onChange={handleChange}
                fullWidth
                margin="normal"
                type="number"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Taux de TVA par défaut (%)"
                name="tauxTVA"
                value={profile.tauxTVA}
                onChange={handleChange}
                fullWidth
                margin="normal"
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Délai de paiement (jours)"
                name="delaiPaiement"
                value={profile.delaiPaiement}
                onChange={handleChange}
                fullWidth
                margin="normal"
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">jours</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Mentions légales"
                name="mentionsLegales"
                value={profile.mentionsLegales}
                onChange={handleChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
                helperText="Entreprise dispensée d'immatriculation au registre du commerce et des sociétés (RCS) et au répertoire des métiers (RM)"
              />
            </Grid>
          </Grid>
          <Alert severity="info" sx={{ mt: 2 }}>
            Ces mentions apparaîtront sur vos factures et devis
          </Alert>
        </Paper>
      )}

      {tabValue === 3 && (
        <Paper sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Coordonnées bancaires</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Nom de la banque"
                name="nomBanque"
                value={profile.nomBanque}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Titulaire du compte"
                name="titulaireCompte"
                value={profile.titulaireCompte}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="IBAN"
                name="iban"
                value={profile.iban}
                onChange={handleChange}
                fullWidth
                margin="normal"
                helperText="Format: FR76 1234 5678 9123 4567 8912 345"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="BIC / SWIFT"
                name="bicSwift"
                value={profile.bicSwift}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
          </Grid>
          <Alert severity="info" sx={{ mt: 2 }}>
            Ces informations sont importantes pour permettre à vos clients d'effectuer des virements bancaires.
          </Alert>
        </Paper>
      )}

      {tabValue === 4 && (
        <Paper sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Identité visuelle</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>Logo de l'entreprise</Typography>
              {logoPreview && (
                <Box sx={{ mb: 2 }}>
                  <img src={logoPreview} alt="Logo Preview" style={{ maxWidth: '200px', maxHeight: '200px' }} />
                </Box>
              )}
              <Button
                variant="outlined"
                component="label"
                startIcon={<CloudUploadIcon />}
                sx={{ mb: 1 }}
              >
                Télécharger un logo
                <input
                  type="file"
                  hidden
                  accept="image/png,image/jpeg"
                  onChange={(e) => handleFileChange(e, 'logo')}
                />
              </Button>
              <Typography variant="caption" display="block" sx={{ mb: 2 }}>
                Format recommandé: PNG ou JPG, max 2MB
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>Couleur principale</Typography>
              <ColorPicker
                value={createColor(profile.couleurPrincipale)}
                onChange={handleColorChange('couleurPrincipale')}
              />
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Cette couleur sera utilisée pour les éléments principaux de vos documents
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>Couleur secondaire</Typography>
              <ColorPicker
                value={createColor(profile.couleurSecondaire)}
                onChange={handleColorChange('couleurSecondaire')}
              />
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Cette couleur sera utilisée pour les arrière-plans et éléments secondaires
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>Signature</Typography>
              {signaturePreview && (
                <Box sx={{ mb: 2 }}>
                  <img src={signaturePreview} alt="Signature Preview" style={{ maxWidth: '200px', maxHeight: '200px' }} />
                </Box>
              )}
              <Button
                variant="outlined"
                component="label"
                startIcon={<CloudUploadIcon />}
                sx={{ mb: 1 }}
              >
                Télécharger une signature
                <input
                  type="file"
                  hidden
                  accept="image/png,image/jpeg"
                  onChange={(e) => handleFileChange(e, 'signature')}
                />
              </Button>
              <Typography variant="caption" display="block" sx={{ mb: 2 }}>
                Format recommandé: PNG avec fond transparent
              </Typography>
            </Grid>
          </Grid>
        </Paper>
      )}

      {tabValue === 5 && (
        <Paper sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Options avancées</Typography>
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={profile.signatureElectronique}
                  onChange={handleSwitchChange}
                  name="signatureElectronique"
                />
              }
              label="Activer la signature électronique"
              labelPlacement="end"
            />
            <Typography variant="caption" display="block" sx={{ mb: 2, ml: 4 }}>
              Permet à vos clients de signer électroniquement les devis
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={profile.paiementEnLigne}
                  onChange={handleSwitchChange}
                  name="paiementEnLigne"
                />
              }
              label="Activer le paiement en ligne"
              labelPlacement="end"
            />
            <Typography variant="caption" display="block" sx={{ mb: 2, ml: 4 }}>
              Permet à vos clients de payer directement en ligne
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={profile.rappelsAutomatiques}
                  onChange={handleSwitchChange}
                  name="rappelsAutomatiques"
                />
              }
              label="Activer les rappels automatiques"
              labelPlacement="end"
            />
            <Typography variant="caption" display="block" sx={{ mb: 2, ml: 4 }}>
              Envoie des rappels automatiques pour les factures impayées
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={profile.numerotationChronologique}
                  onChange={handleSwitchChange}
                  name="numerotationChronologique"
                />
              }
              label="Activer la numérotation chronologique"
              labelPlacement="end"
            />
            <Typography variant="caption" display="block" sx={{ mb: 2, ml: 4 }}>
              Génère automatiquement des numéros séquentiels pour vos documents
            </Typography>
          </Box>
          <Alert severity="info" sx={{ mt: 2 }}>
            Certains options peuvent nécessiter une configuration supplémentaire dans les paramètres avancés.
          </Alert>
        </Paper>
      )}

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={loading || !formValid}
        >
          Enregistrer les modifications
        </Button>
      </Box>
    </Box>
  );
};

export default EntrepriseProfile;