{"version": 3, "sources": ["../../../src/server/lib/format-hostname.ts"], "sourcesContent": ["import { isIPv6 } from './is-ipv6'\n\n/**\n * Formats a hostname so that it is a valid host that can be fetched by wrapping\n * IPv6 hosts with brackets.\n * @param hostname\n * @returns\n */\nexport function formatHostname(hostname: string): string {\n  return isIPv6(hostname) ? `[${hostname}]` : hostname\n}\n"], "names": ["formatHostname", "hostname", "isIPv6"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;wBARO;AAQhB,SAASA,eAAeC,QAAgB;IAC7C,OAAOC,IAAAA,cAAM,EAACD,YAAY,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,GAAGA;AAC9C"}