const express = require('express');
const router = express.Router();
const Livreur = require('../models/LivreurModel');
const User = require('../models/UserModel');
const { verifyToken, isResponsable, isVendeur } = require('../middleware/authMiddleware');

// Middleware pour vérifier que l'utilisateur peut accéder aux livreurs
const canAccessLivreurs = [verifyToken, (req, res, next) => {
    if (req.userRole === 'RESPONSABLE' || req.userRole === 'VENDEUR' || req.userRole === 'ADMIN') {
        next();
    } else {
        return res.status(403).json({ message: 'Accès non autorisé' });
    }
}];

// Middleware pour vérifier que seul le responsable peut modifier les livreurs
const canModifyLivreurs = [verifyToken, (req, res, next) => {
    if (req.userRole === 'RESPONSABLE' || req.userRole === 'ADMIN') {
        next();
    } else {
        return res.status(403).json({ message: 'Seuls les responsables peuvent modifier les livreurs' });
    }
}];

// GET /api/livreurs/disponibles - Récupérer les livreurs disponibles (DOIT ÊTRE AVANT /livreurs/:id)
router.get('/livreurs/disponibles', canAccessLivreurs, async (req, res) => {
    try {
        let query = { statut: 'ACTIF', disponible: true };

        // Si l'utilisateur est un responsable, ne montrer que ses livreurs
        if (req.userRole === 'RESPONSABLE') {
            query.responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // Pour les vendeurs, trouver le responsable associé
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                const responsableIds = vendeur.responsables.map(r => r._id);
                query.responsableId = { $in: responsableIds };
            } else {
                return res.status(200).json([]);
            }
        }

        const livreurs = await Livreur.find(query)
            .select('nom prenom telephone vehicule.type')
            .sort({ nom: 1, prenom: 1 });

        console.log(`✅ Livreurs disponibles trouvés: ${livreurs.length}`);
        res.status(200).json(livreurs);
    } catch (error) {
        console.error('❌ Erreur lors de la récupération des livreurs disponibles:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération des livreurs disponibles' });
    }
});

// GET /api/livreurs - Récupérer tous les livreurs
router.get('/livreurs', canAccessLivreurs, async (req, res) => {
    try {
        let query = {};

        // Si l'utilisateur est un responsable, ne montrer que ses livreurs
        if (req.userRole === 'RESPONSABLE') {
            query.responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // Pour les vendeurs, trouver le responsable associé
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                const responsableIds = vendeur.responsables.map(r => r._id);
                query.responsableId = { $in: responsableIds };
            } else {
                return res.status(200).json([]);
            }
        }

        const { statut, disponible, search } = req.query;

        // Filtres optionnels
        if (statut) {
            query.statut = statut;
        }
        if (disponible !== undefined) {
            query.disponible = disponible === 'true';
        }
        if (search) {
            query.$or = [
                { nom: { $regex: search, $options: 'i' } },
                { prenom: { $regex: search, $options: 'i' } },
                { telephone: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } }
            ];
        }

        const livreurs = await Livreur.find(query)
            .populate('responsableId', 'nom prenom email')
            .sort({ dateCreation: -1 });

        res.status(200).json(livreurs);
    } catch (error) {
        console.error('Erreur lors de la récupération des livreurs:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération des livreurs' });
    }
});

// GET /api/livreurs/:id - Récupérer un livreur par ID
router.get('/livreurs/:id', canAccessLivreurs, async (req, res) => {
    try {
        const livreur = await Livreur.findById(req.params.id)
            .populate('responsableId', 'nom prenom email');

        if (!livreur) {
            return res.status(404).json({ message: 'Livreur non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && livreur.responsableId._id.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce livreur' });
        }

        res.status(200).json(livreur);
    } catch (error) {
        console.error('Erreur lors de la récupération du livreur:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération du livreur' });
    }
});

// POST /api/livreurs - Créer un nouveau livreur
router.post('/livreurs', canModifyLivreurs, async (req, res) => {
    try {
        const {
            nom,
            prenom,
            telephone,
            email,
            cin,
            adresse,
            vehicule,
            notes
        } = req.body;

        // Validation des champs obligatoires
        if (!nom || !prenom || !telephone) {
            return res.status(400).json({
                message: 'Les champs nom, prénom et téléphone sont obligatoires'
            });
        }

        // Vérifier si le téléphone existe déjà pour ce responsable
        const existingLivreur = await Livreur.findOne({
            telephone,
            responsableId: req.userRole === 'RESPONSABLE' ? req.userId : req.body.responsableId
        });

        if (existingLivreur) {
            return res.status(400).json({
                message: 'Un livreur avec ce numéro de téléphone existe déjà'
            });
        }

        const nouveauLivreur = new Livreur({
            nom,
            prenom,
            telephone,
            email,
            cin,
            adresse,
            vehicule,
            notes,
            responsableId: req.userRole === 'RESPONSABLE' ? req.userId : req.body.responsableId
        });

        const livreurSauvegarde = await nouveauLivreur.save();
        await livreurSauvegarde.populate('responsableId', 'nom prenom email');

        res.status(201).json({
            message: 'Livreur créé avec succès',
            livreur: livreurSauvegarde
        });
    } catch (error) {
        console.error('Erreur lors de la création du livreur:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                message: 'Données invalides',
                errors: Object.values(error.errors).map(e => e.message)
            });
        }
        res.status(500).json({ message: 'Erreur serveur lors de la création du livreur' });
    }
});

// PUT /api/livreurs/:id - Mettre à jour un livreur
router.put('/livreurs/:id', canModifyLivreurs, async (req, res) => {
    try {
        const livreur = await Livreur.findById(req.params.id);

        if (!livreur) {
            return res.status(404).json({ message: 'Livreur non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && livreur.responsableId.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce livreur' });
        }

        const {
            nom,
            prenom,
            telephone,
            email,
            cin,
            adresse,
            vehicule,
            statut,
            disponible,
            notes
        } = req.body;

        // Vérifier si le nouveau téléphone existe déjà (sauf pour ce livreur)
        if (telephone && telephone !== livreur.telephone) {
            const existingLivreur = await Livreur.findOne({
                telephone,
                responsableId: livreur.responsableId,
                _id: { $ne: req.params.id }
            });

            if (existingLivreur) {
                return res.status(400).json({
                    message: 'Un autre livreur avec ce numéro de téléphone existe déjà'
                });
            }
        }

        // Mettre à jour les champs
        if (nom) livreur.nom = nom;
        if (prenom) livreur.prenom = prenom;
        if (telephone) livreur.telephone = telephone;
        if (email !== undefined) livreur.email = email;
        if (cin !== undefined) livreur.cin = cin;
        if (adresse !== undefined) livreur.adresse = adresse;
        if (vehicule) livreur.vehicule = { ...livreur.vehicule, ...vehicule };
        if (statut) livreur.statut = statut;
        if (disponible !== undefined) livreur.disponible = disponible;
        if (notes !== undefined) livreur.notes = notes;

        const livreurMisAJour = await livreur.save();
        await livreurMisAJour.populate('responsableId', 'nom prenom email');

        res.status(200).json({
            message: 'Livreur mis à jour avec succès',
            livreur: livreurMisAJour
        });
    } catch (error) {
        console.error('Erreur lors de la mise à jour du livreur:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                message: 'Données invalides',
                errors: Object.values(error.errors).map(e => e.message)
            });
        }
        res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du livreur' });
    }
});

// DELETE /api/livreurs/:id - Supprimer un livreur
router.delete('/livreurs/:id', canModifyLivreurs, async (req, res) => {
    try {
        const livreur = await Livreur.findById(req.params.id);

        if (!livreur) {
            return res.status(404).json({ message: 'Livreur non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && livreur.responsableId.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce livreur' });
        }

        // Vérifier s'il y a des bons de livraison associés
        const BonLivraison = require('../models/BonLivraisonModel');
        const bonLivraisonsCount = await BonLivraison.countDocuments({ livreurId: req.params.id });

        if (bonLivraisonsCount > 0) {
            return res.status(400).json({
                message: `Impossible de supprimer ce livreur car il est associé à ${bonLivraisonsCount} bon(s) de livraison`
            });
        }

        await Livreur.findByIdAndDelete(req.params.id);

        res.status(200).json({ message: 'Livreur supprimé avec succès' });
    } catch (error) {
        console.error('Erreur lors de la suppression du livreur:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la suppression du livreur' });
    }
});

module.exports = router;
