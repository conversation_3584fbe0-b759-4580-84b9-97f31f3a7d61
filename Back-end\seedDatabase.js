const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// Import models
const User = require('./models/UserModel');
const Client = require('./models/ClientModel');
const Produit = require('./models/ProduitModel');
const Facture = require('./models/FactureModel');
const Devis = require('./models/DevisModel');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://127.0.0.1:27017/test');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    console.log('💡 Make sure MongoDB is running. Try starting it with: mongod');
    process.exit(1);
  }
};

// Clear existing data
const clearDatabase = async () => {
  try {
    await User.deleteMany({});
    await Client.deleteMany({});
    await Produit.deleteMany({});
    await Facture.deleteMany({});
    await Devis.deleteMany({});
    console.log('🧹 Database cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing database:', error.message);
  }
};

// Seed Users with different roles
const seedUsers = async () => {
  console.log('👥 Creating users...');
  const hashedPassword = await bcrypt.hash('112233', 10);

  const users = [
    // Admin
    {
      nom: 'Admin',
      prenom: 'System',
      email: '<EMAIL>',
      motDePasse: await bcrypt.hash('Admin123', 10),
      role: 'ADMIN',
      dateCreation: new Date('2024-01-01')
    },

    // Responsables d'entreprise
    {
      nom: 'Ben Ahmed',
      prenom: 'Mohamed',
      email: '<EMAIL>',
      motDePasse: hashedPassword,
      role: 'RESPONSABLE',
      adresse: '15 Avenue Habib Bourguiba, Tunis 1001',
      telephone: '+216 71 123 456',
      contact: 'Mohamed Ben Ahmed',
      abonnementActif: true,
      dateCreation: new Date('2024-01-15')
    },
    {
      nom: 'Trabelsi',
      prenom: 'Fatma',
      email: '<EMAIL>',
      motDePasse: hashedPassword,
      role: 'RESPONSABLE',
      adresse: '28 Rue de la République, Sfax 3000',
      telephone: '+216 74 987 654',
      contact: 'Fatma Trabelsi',
      abonnementActif: true,
      dateCreation: new Date('2024-02-01')
    },

    // Vendeurs
    {
      nom: 'Khelifi',
      prenom: 'Ahmed',
      email: '<EMAIL>',
      motDePasse: hashedPassword,
      role: 'VENDEUR',
      adresse: '12 Rue Ibn Khaldoun, Tunis 1002',
      telephone: '+216 98 123 789',
      contact: 'Ahmed Khelifi',
      dateCreation: new Date('2024-01-20')
    },
    {
      nom: 'Mansouri',
      prenom: 'Leila',
      email: '<EMAIL>',
      motDePasse: hashedPassword,
      role: 'VENDEUR',
      adresse: '45 Avenue Hedi Chaker, Sfax 3001',
      telephone: '+216 97 456 123',
      contact: 'Leila Mansouri',
      dateCreation: new Date('2024-02-05')
    },

    // Clients
    {
      nom: 'Bouazizi',
      prenom: 'Karim',
      email: '<EMAIL>',
      motDePasse: hashedPassword,
      role: 'CLIENT',
      adresse: '67 Rue de Marseille, Tunis 1003',
      telephone: '+216 22 789 456',
      contact: 'Karim Bouazizi',
      cin: '12345678',
      dateCreation: new Date('2024-02-10')
    },
    {
      nom: 'Gharbi',
      prenom: 'Amina',
      email: '<EMAIL>',
      motDePasse: hashedPassword,
      role: 'CLIENT',
      adresse: '89 Avenue de la Liberté, Sousse 4000',
      telephone: '+216 23 654 321',
      contact: 'Amina Gharbi',
      cin: '87654321',
      dateCreation: new Date('2024-02-15')
    }
  ];

  const createdUsers = await User.insertMany(users);

  // Update user relationships after creation
  const responsable1 = createdUsers.find(u => u.email === '<EMAIL>');
  const responsable2 = createdUsers.find(u => u.email === '<EMAIL>');
  const vendeur1 = createdUsers.find(u => u.email === '<EMAIL>');
  const vendeur2 = createdUsers.find(u => u.email === '<EMAIL>');

  // Assign vendeurs to responsables
  await User.findByIdAndUpdate(vendeur1._id, {
    responsables: [responsable1._id],
    createdBy: responsable1._id
  });
  await User.findByIdAndUpdate(vendeur2._id, {
    responsables: [responsable2._id],
    createdBy: responsable2._id
  });

  console.log(`✅ Created ${createdUsers.length} users with relationships`);
  return createdUsers;
};

// Seed Clients (Companies)
const seedClients = async (users) => {
  console.log('🏢 Creating client companies...');

  const clients = [
    {
      nom: 'TechCorp Solutions',
      adresse: '123 Avenue Mohamed V, Tunis 1001',
      email: '<EMAIL>',
      contact: 'Directeur Général',
      telephone: '+216 71 234 567',
      cin: 'A1234567',
      actif: true,
      createdAt: new Date('2024-01-20'),
      updatedAt: new Date('2024-01-20')
    },
    {
      nom: 'Digital Innovations SARL',
      adresse: '456 Rue de la République, Sfax 3000',
      email: '<EMAIL>',
      contact: 'Chef de Projet',
      telephone: '+216 74 345 678',
      cin: 'B2345678',
      actif: true,
      createdAt: new Date('2024-02-01'),
      updatedAt: new Date('2024-02-01')
    },
    {
      nom: 'StartUp Hub',
      adresse: '789 Boulevard 14 Janvier, Sousse 4000',
      email: '<EMAIL>',
      contact: 'Responsable Commercial',
      telephone: '+216 73 456 789',
      cin: 'C3456789',
      actif: true,
      createdAt: new Date('2024-02-10'),
      updatedAt: new Date('2024-02-10')
    },
    {
      nom: 'E-Commerce Plus',
      adresse: '321 Avenue Habib Thameur, Monastir 5000',
      email: '<EMAIL>',
      contact: 'Service Client',
      telephone: '+216 73 567 890',
      cin: 'D4567890',
      actif: true,
      createdAt: new Date('2024-02-15'),
      updatedAt: new Date('2024-02-15')
    },
    {
      nom: 'Consulting Pro',
      adresse: '654 Rue Ibn Sina, Ariana 2080',
      email: '<EMAIL>',
      contact: 'Consultant Senior',
      telephone: '+216 71 678 901',
      cin: 'E5678901',
      actif: false,
      createdAt: new Date('2024-01-25'),
      updatedAt: new Date('2024-03-01')
    }
  ];

  const createdClients = await Client.insertMany(clients);
  console.log(`✅ Created ${createdClients.length} client companies`);
  return createdClients;
};

// Seed Products with different categories
const seedProducts = async () => {
  console.log('📦 Creating products...');

  const products = [
    {
      nom: 'Site Web Vitrine',
      description: 'Création de site web vitrine responsive',
      descriptionDetaille: 'Site web professionnel avec design moderne, responsive, optimisé SEO, hébergement inclus pour 1 an',
      prix: 1200.00,
      category: 'Service',
      unite: 'projet',
      gestionStock: false,
      quantiteStock: 0,
      seuilAlerte: 0,
      statistiques: {
        nombreVentes: 15,
        chiffreAffaires: 18000.00
      },
      dateCreation: new Date('2024-01-10')
    },
    {
      nom: 'Application Mobile',
      description: 'Développement d\'application mobile native',
      descriptionDetaille: 'Application mobile iOS/Android avec interface utilisateur moderne, API backend, tests et déploiement',
      prix: 3500.00,
      category: 'Service',
      unite: 'projet',
      gestionStock: false,
      quantiteStock: 0,
      seuilAlerte: 0,
      statistiques: {
        nombreVentes: 8,
        chiffreAffaires: 28000.00
      },
      dateCreation: new Date('2024-01-12')
    },
    {
      nom: 'Ordinateur Portable Dell',
      description: 'Dell Latitude 5520 - Intel i7',
      descriptionDetaille: 'Dell Latitude 5520, Intel Core i7-1165G7, 16GB RAM, 512GB SSD, Windows 11 Pro',
      prix: 2800.00,
      category: 'Produit physique',
      unite: 'unité',
      gestionStock: true,
      quantiteStock: 25,
      seuilAlerte: 5,
      statistiques: {
        nombreVentes: 12,
        chiffreAffaires: 33600.00
      },
      dateCreation: new Date('2024-01-15')
    }
  ];

  const createdProducts = await Produit.insertMany(products);
  console.log(`✅ Created ${createdProducts.length} products`);
  return createdProducts;
};

// Seed Devis (Quotes) with different statuses
const seedDevis = async (users, clients, products) => {
  console.log('📋 Creating quotes (devis)...');

  const responsable1 = users.find(u => u.email === '<EMAIL>');
  const responsable2 = users.find(u => u.email === '<EMAIL>');
  const vendeur1 = users.find(u => u.email === '<EMAIL>');
  const vendeur2 = users.find(u => u.email === '<EMAIL>');

  const devis = [
    {
      numéro: 'DEV-2024-001',
      clientId: clients[0]._id,
      vendeurId: vendeur1._id,
      responsableId: responsable1._id,
      dateCréation: new Date('2024-03-01'),
      dateValidite: new Date('2024-04-01'),
      lignes: [
        {
          produit: products[0]._id,
          description: 'Site Web Vitrine - Package Standard',
          quantite: 1,
          prixUnitaire: 1200.00,
          montantHT: 1200.00,
          montantTTC: 1440.00
        }
      ],
      notes: 'Devis pour création de site web vitrine avec design personnalisé',
      statut: 'SENT',
      validationResponsable: {
        statut: 'APPROVED',
        date: new Date('2024-03-02'),
        commentaires: 'Approuvé - client prioritaire',
        validéPar: responsable1._id
      },
      total: 1440.00,
      montantHT: 1200.00,
      montantTTC: 1440.00,
      tauxTVA: 20,
      estDemandeClient: false
    },
    {
      numéro: 'DEV-2024-002',
      clientId: clients[1]._id,
      vendeurId: vendeur2._id,
      responsableId: responsable2._id,
      dateCréation: new Date('2024-03-05'),
      dateValidite: new Date('2024-04-05'),
      lignes: [
        {
          produit: products[1]._id,
          description: 'Application Mobile - iOS & Android',
          quantite: 1,
          prixUnitaire: 3500.00,
          montantHT: 3500.00,
          montantTTC: 4200.00
        }
      ],
      notes: 'Application mobile avec fonctionnalités avancées',
      statut: 'ACCEPTED',
      validationResponsable: {
        statut: 'APPROVED',
        date: new Date('2024-03-06'),
        commentaires: 'Projet stratégique approuvé',
        validéPar: responsable2._id
      },
      reponseClient: {
        date: new Date('2024-03-10'),
        commentaires: 'Devis accepté, nous souhaitons commencer rapidement'
      },
      total: 4200.00,
      montantHT: 3500.00,
      montantTTC: 4200.00,
      tauxTVA: 20,
      estDemandeClient: true
    },
    {
      numéro: 'DEV-2024-003',
      clientId: clients[2]._id,
      vendeurId: vendeur1._id,
      responsableId: responsable1._id,
      dateCréation: new Date('2024-03-08'),
      dateValidite: new Date('2024-04-08'),
      lignes: [
        {
          produit: products[2]._id,
          description: 'Ordinateurs portables pour équipe',
          quantite: 5,
          prixUnitaire: 2800.00,
          montantHT: 14000.00,
          montantTTC: 16800.00
        }
      ],
      notes: 'Commande groupée pour équipement bureau',
      statut: 'DRAFT',
      validationResponsable: {
        statut: 'PENDING',
        date: null,
        commentaires: null,
        validéPar: null
      },
      total: 16800.00,
      montantHT: 14000.00,
      montantTTC: 16800.00,
      tauxTVA: 20,
      estDemandeClient: false
    },
    {
      numéro: 'DEV-2024-004',
      clientId: clients[3]._id,
      vendeurId: vendeur2._id,
      responsableId: responsable2._id,
      dateCréation: new Date('2024-03-12'),
      dateValidite: new Date('2024-04-12'),
      lignes: [
        {
          produit: products[0]._id,
          description: 'Site Web E-commerce',
          quantite: 1,
          prixUnitaire: 2500.00,
          montantHT: 2500.00,
          montantTTC: 3000.00
        }
      ],
      notes: 'Site e-commerce avec paiement en ligne',
      statut: 'REJECTED',
      validationResponsable: {
        statut: 'APPROVED',
        date: new Date('2024-03-13'),
        commentaires: 'Approuvé sous conditions',
        validéPar: responsable2._id
      },
      reponseClient: {
        date: new Date('2024-03-15'),
        commentaires: 'Budget trop élevé pour le moment'
      },
      total: 3000.00,
      montantHT: 2500.00,
      montantTTC: 3000.00,
      tauxTVA: 20,
      estDemandeClient: true
    }
  ];

  const createdDevis = await Devis.insertMany(devis);
  console.log(`✅ Created ${createdDevis.length} quotes with different statuses`);
  return createdDevis;
};

// Seed Factures (Invoices) with different statuses
const seedFactures = async (users, clients, products, devis) => {
  console.log('🧾 Creating invoices (factures)...');

  const responsable1 = users.find(u => u.email === '<EMAIL>');
  const responsable2 = users.find(u => u.email === '<EMAIL>');
  const vendeur1 = users.find(u => u.email === '<EMAIL>');
  const vendeur2 = users.find(u => u.email === '<EMAIL>');

  const factures = [
    {
      numero: 'FACT-2024-001',
      clientId: clients[0]._id,
      vendeurId: vendeur1._id,
      responsableId: responsable1._id,
      dateEmission: new Date('2024-03-15'),
      lignes: [
        {
          produit: products[0]._id,
          description: 'Site Web Vitrine - Livré',
          quantite: 1,
          prixUnitaire: 1200.00,
          montantHT: 1200.00,
          montantTTC: 1440.00,
          stockAjuste: false
        }
      ],
      notes: 'Facture pour site web vitrine terminé',
      statut: 'PAID',
      paiement: {
        datePaiement: new Date('2024-03-20'),
        modePaiement: 'VIREMENT_BANCAIRE',
        reference: 'VIR-2024-001',
        montantPaye: 1440.00
      },
      devisId: devis[0]._id,
      dateAcceptation: new Date('2024-03-16'),
      total: 1440.00,
      totalHT: 1200.00,
      montantTVA: 240.00,
      tauxTVA: 20,
      stockAjuste: false,
      createdAt: new Date('2024-03-15')
    },
    {
      numero: 'FACT-2024-002',
      clientId: clients[1]._id,
      vendeurId: vendeur2._id,
      responsableId: responsable2._id,
      dateEmission: new Date('2024-03-18'),
      lignes: [
        {
          produit: products[1]._id,
          description: 'Application Mobile - Phase 1',
          quantite: 1,
          prixUnitaire: 1750.00,
          montantHT: 1750.00,
          montantTTC: 2100.00,
          stockAjuste: false
        }
      ],
      notes: 'Facture partielle - Phase 1 du développement',
      statut: 'SENT',
      devisId: devis[1]._id,
      total: 2100.00,
      totalHT: 1750.00,
      montantTVA: 350.00,
      tauxTVA: 20,
      stockAjuste: false,
      createdAt: new Date('2024-03-18')
    },
    {
      numero: 'FACT-2024-003',
      clientId: clients[2]._id,
      vendeurId: vendeur1._id,
      responsableId: responsable1._id,
      dateEmission: new Date('2024-03-22'),
      lignes: [
        {
          produit: products[2]._id,
          description: 'Ordinateurs portables Dell - Commande partielle',
          quantite: 3,
          prixUnitaire: 2800.00,
          montantHT: 8400.00,
          montantTTC: 10080.00,
          stockAjuste: true
        }
      ],
      notes: 'Livraison partielle - 3 ordinateurs sur 5',
      statut: 'ACCEPTED',
      reponseClient: {
        date: new Date('2024-03-23'),
        commentaires: 'Facture acceptée, en attente de livraison du reste',
        motifRefus: null
      },
      dateAcceptation: new Date('2024-03-23'),
      total: 10080.00,
      totalHT: 8400.00,
      montantTVA: 1680.00,
      tauxTVA: 20,
      stockAjuste: true,
      createdAt: new Date('2024-03-22')
    },
    {
      numero: 'FACT-2024-004',
      clientId: clients[3]._id,
      vendeurId: vendeur2._id,
      responsableId: responsable2._id,
      dateEmission: new Date('2024-03-25'),
      lignes: [
        {
          produit: products[0]._id,
          description: 'Consultation technique',
          quantite: 4,
          prixUnitaire: 150.00,
          montantHT: 600.00,
          montantTTC: 720.00,
          stockAjuste: false
        }
      ],
      notes: 'Heures de consultation technique',
      statut: 'DRAFT',
      total: 720.00,
      totalHT: 600.00,
      montantTVA: 120.00,
      tauxTVA: 20,
      stockAjuste: false,
      createdAt: new Date('2024-03-25')
    },
    {
      numero: 'FACT-2024-005',
      clientId: clients[4]._id,
      vendeurId: vendeur1._id,
      responsableId: responsable1._id,
      dateEmission: new Date('2024-03-28'),
      lignes: [
        {
          produit: products[1]._id,
          description: 'Audit technique système',
          quantite: 1,
          prixUnitaire: 800.00,
          montantHT: 800.00,
          montantTTC: 960.00,
          stockAjuste: false
        }
      ],
      notes: 'Audit complet du système informatique',
      statut: 'REJECTED',
      reponseClient: {
        date: new Date('2024-03-30'),
        commentaires: 'Service non conforme aux attentes',
        motifRefus: 'Qualité insuffisante'
      },
      dateRefus: new Date('2024-03-30'),
      total: 960.00,
      totalHT: 800.00,
      montantTVA: 160.00,
      tauxTVA: 20,
      stockAjuste: false,
      createdAt: new Date('2024-03-28')
    },
    {
      numero: 'FACT-2024-006',
      clientId: clients[1]._id,
      vendeurId: vendeur2._id,
      responsableId: responsable2._id,
      dateEmission: new Date('2024-04-01'),
      lignes: [
        {
          produit: products[2]._id,
          description: 'Ordinateur portable - Remplacement',
          quantite: 1,
          prixUnitaire: 2800.00,
          montantHT: 2800.00,
          montantTTC: 3360.00,
          stockAjuste: true
        }
      ],
      notes: 'Remplacement suite à défaillance',
      statut: 'PARTIALLY_PAID',
      paiement: {
        datePaiement: new Date('2024-04-05'),
        modePaiement: 'CHEQUE',
        reference: 'CHQ-2024-002',
        montantPaye: 1680.00
      },
      total: 3360.00,
      totalHT: 2800.00,
      montantTVA: 560.00,
      tauxTVA: 20,
      stockAjuste: true,
      createdAt: new Date('2024-04-01')
    }
  ];

  const createdFactures = await Facture.insertMany(factures);
  console.log(`✅ Created ${createdFactures.length} invoices with different statuses`);
  return createdFactures;
};

// Main seeding function
const seedDatabase = async () => {
  console.log('🚀 Starting database seeding...\n');

  try {
    // Connect to database
    await connectDB();

    // Clear existing data
    await clearDatabase();

    // Seed data in order (respecting dependencies)
    console.log('\n📊 Creating sample data...\n');

    const users = await seedUsers();
    const clients = await seedClients(users);
    const products = await seedProducts();
    const devis = await seedDevis(users, clients, products);
    const factures = await seedFactures(users, clients, products, devis);

    // Display summary
    console.log('\n🎉 Database seeding completed successfully!\n');
    console.log('📈 Summary:');
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   🏢 Clients: ${clients.length}`);
    console.log(`   📦 Products: ${products.length}`);
    console.log(`   📋 Quotes: ${devis.length}`);
    console.log(`   🧾 Invoices: ${factures.length}`);

    console.log('\n🔑 Test Accounts:');
    console.log('   Admin: <EMAIL> / Admin123');
    console.log('   Responsable 1: <EMAIL> / 112233');
    console.log('   Responsable 2: <EMAIL> / 112233');
    console.log('   Vendeur 1: <EMAIL> / 112233');
    console.log('   Vendeur 2: <EMAIL> / 112233');

    console.log('\n📊 Status Distribution:');
    console.log('   Devis: DRAFT(1), SENT(1), ACCEPTED(1), REJECTED(1)');
    console.log('   Factures: DRAFT(1), SENT(1), ACCEPTED(1), PAID(1), REJECTED(1), PARTIALLY_PAID(1)');

    console.log('\n💰 Financial Summary:');
    const totalQuotes = devis.reduce((sum, d) => sum + d.total, 0);
    const totalInvoices = factures.reduce((sum, f) => sum + f.total, 0);
    console.log(`   Total Quotes Value: ${totalQuotes.toLocaleString()} DT`);
    console.log(`   Total Invoices Value: ${totalInvoices.toLocaleString()} DT`);

    console.log('\n✨ Ready to test! Start your server and explore the data.\n');

  } catch (error) {
    console.error('❌ Error seeding database:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
