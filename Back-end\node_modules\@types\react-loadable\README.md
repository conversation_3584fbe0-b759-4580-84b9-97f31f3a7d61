# Installation
> `npm install --save @types/react-loadable`

# Summary
This package contains type definitions for react-loadable (https://github.com/thejameskyle/react-loadable#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-loadable.

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react), [@types/webpack](https://npmjs.com/package/@types/webpack)

# Credits
These definitions were written by [<PERSON>](https://github.com/Jessid<PERSON>), [Oden S.](https://github.com/odensc), [<PERSON>](https://github.com/ianks), [<PERSON><PERSON>](https://github.com/tlaziuk), and [<PERSON>](https://github.com/iMobs).
