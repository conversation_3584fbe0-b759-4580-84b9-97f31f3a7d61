import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Avatar,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Check as CheckIcon,
  Block as BlockIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Close as CloseIcon,
  DateRange as DateRangeIcon,
  Info as InfoIcon,
  Business as BusinessIcon,
  People as PeopleIcon,
  PhotoCamera as PhotoCameraIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import userService from '../services/userService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.3
    }
  }
};

const EquipeManagement = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [vendeurs, setVendeurs] = useState([]);
  const [filteredVendeurs, setFilteredVendeurs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedVendeur, setSelectedVendeur] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);

  // Log current user for debugging
  useEffect(() => {
    console.log('Current user in EquipeManagement:', currentUser);

    // If currentUser exists but doesn't have _id, try to get it from id
    if (currentUser && !currentUser._id && currentUser.id) {
      console.log('Using id instead of _id for currentUser');
      currentUser._id = currentUser.id;
    }
  }, [currentUser]);

  // Fetch vendeurs on component mount
  useEffect(() => {
    fetchVendeurs();
  }, []);

  useEffect(() => {
    filterVendeurs();
  }, [vendeurs, searchTerm, activeFilter]);

  const fetchVendeurs = async () => {
    try {
      setLoading(true);

      // Check if currentUser exists and has an _id or id
      const entrepriseId = currentUser?._id || currentUser?.id;

      if (!currentUser || !entrepriseId) {
        console.error('Current user or user ID is missing:', currentUser);
        setVendeurs([]);
        setFilteredVendeurs([]);
        setSnackbar({
          open: true,
          message: 'Impossible de récupérer les vendeurs: ID d\'entreprise manquant',
          severity: 'error'
        });
        return;
      }

      console.log('Fetching vendeurs for entreprise ID:', entrepriseId);

      // Get all vendeurs associated with the current entreprise
      const data = await userService.getVendeursByEntreprise(entrepriseId);

      if (Array.isArray(data)) {
        // Sort vendeurs by name
        const sortedData = [...data].sort((a, b) =>
          a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' })
        );

        console.log('Found vendeurs:', sortedData.length);
        sortedData.forEach(v => console.log(`- ${v.nom} (${v._id})`));

        setVendeurs(sortedData);
        setFilteredVendeurs(sortedData);
      } else {
        // Handle case where API returns unexpected data
        console.error('Unexpected data format from API:', data);
        setVendeurs([]);
        setFilteredVendeurs([]);
        setSnackbar({
          open: true,
          message: 'Format de données inattendu lors de la récupération des vendeurs',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error fetching vendeurs:', error);
      setVendeurs([]);
      setFilteredVendeurs([]);

      let errorMessage = 'Erreur lors de la récupération des vendeurs';

      // Try to extract the most useful error message
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.details) {
        errorMessage = error.response.data.details;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const filterVendeurs = () => {
    let filtered = [...vendeurs];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(vendeur =>
        vendeur.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vendeur.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply active filter
    if (activeFilter !== 'all') {
      const isActive = activeFilter === 'active';
      filtered = filtered.filter(vendeur => vendeur.actif === isActive);
    }

    setFilteredVendeurs(filtered);
  };

  // Handle search
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle active filter
  const handleActiveFilterChange = (event) => {
    setActiveFilter(event.target.value);
  };

  // Handle menu open
  const handleMenuOpen = (event, vendeur) => {
    setAnchorEl(event.currentTarget);
    setSelectedVendeur(vendeur);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle view details
  const handleViewDetails = () => {
    setDetailsDialogOpen(true);
    handleMenuClose();
  };

  // Handle edit vendeur
  const handleEditVendeur = () => {
    setFormDialogOpen(true);
    handleMenuClose();
  };

  // Handle delete dialog open
  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
  };

  // Handle details dialog close
  const handleDetailsDialogClose = () => {
    setDetailsDialogOpen(false);
  };

  // Handle form dialog close
  const handleFormDialogClose = () => {
    setFormDialogOpen(false);
    setSelectedVendeur(null);
    setAvatarFile(null);
    setAvatarPreview(null);
  };

  // Handle add vendeur
  const handleAddVendeur = () => {
    setSelectedVendeur(null);
    setFormDialogOpen(true);
    setAvatarFile(null);
    setAvatarPreview(null);
  };

  // Handle avatar change
  const handleAvatarChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save vendeur
  const handleSaveVendeur = async (vendeurData) => {
    try {
      setLoading(true);

      // Validate required fields
      if (!vendeurData.nom || !vendeurData.email) {
        setSnackbar({
          open: true,
          message: 'Veuillez remplir tous les champs obligatoires',
          severity: 'error'
        });
        setLoading(false);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(vendeurData.email)) {
        setSnackbar({
          open: true,
          message: 'Format d\'email invalide',
          severity: 'error'
        });
        setLoading(false);
        return;
      }

      // Validate password for new vendeur
      if (!selectedVendeur && (!vendeurData.motDePasse || vendeurData.motDePasse.length < 6)) {
        setSnackbar({
          open: true,
          message: 'Le mot de passe doit contenir au moins 6 caractères',
          severity: 'error'
        });
        setLoading(false);
        return;
      }

      let response;

      if (selectedVendeur) {
        // Get entreprise ID from either _id or id field
        const entrepriseId = currentUser?._id || currentUser?.id;

        // Update existing vendeur
        response = await userService.updateUser(selectedVendeur._id, {
          ...vendeurData,
          role: 'VENDEUR',
          // Keep existing entreprises and add current one if not already there
          entreprises: selectedVendeur.entreprises &&
                      selectedVendeur.entreprises.includes(entrepriseId) ?
                      selectedVendeur.entreprises :
                      [...(selectedVendeur.entreprises || []), entrepriseId],
          // Keep existing responsables and add current one if not already there
          responsables: selectedVendeur.responsables &&
                      selectedVendeur.responsables.includes(entrepriseId) ?
                      selectedVendeur.responsables :
                      [...(selectedVendeur.responsables || []), entrepriseId]
        });

        if (response && response._id) {
          // Upload avatar if provided
          if (avatarFile) {
            const formData = new FormData();
            formData.append('avatar', avatarFile);
            await userService.uploadProfileImage(response._id, formData);
          }

          setSnackbar({
            open: true,
            message: 'Vendeur mis à jour avec succès',
            severity: 'success'
          });
        } else {
          throw new Error('Erreur lors de la mise à jour du vendeur');
        }
      } else {
        // Get entreprise ID from either _id or id field
        const entrepriseId = currentUser?._id || currentUser?.id;

        console.log('Creating new vendeur with entreprise ID:', entrepriseId);
        response = await userService.createUser({
          ...vendeurData,
          role: 'VENDEUR',
          entreprises: entrepriseId ? [entrepriseId] : [], // Associate with current entreprise
          responsables: entrepriseId ? [entrepriseId] : [], // Set responsables to the current responsable ID
          dateCreation: new Date().toISOString()
        });

        if (response && response._id) {
          // Upload avatar if provided
          if (avatarFile) {
            const formData = new FormData();
            formData.append('avatar', avatarFile);
            await userService.uploadProfileImage(response._id, formData);
          }

          setSnackbar({
            open: true,
            message: 'Vendeur créé avec succès',
            severity: 'success'
          });
        } else {
          throw new Error('Erreur lors de la création du vendeur');
        }
      }

      await fetchVendeurs();
      handleFormDialogClose();
    } catch (error) {
      console.error('Error saving vendeur:', error);
      let errorMessage = 'Erreur lors de l\'enregistrement du vendeur';

      // Try to extract the most useful error message
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.details) {
        errorMessage = error.response.data.details;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle delete vendeur
  const handleDeleteVendeur = async () => {
    try {
      if (selectedVendeur) {
        await userService.deleteUser(selectedVendeur._id);
        setSnackbar({
          open: true,
          message: 'Vendeur supprimé avec succès',
          severity: 'success'
        });
        fetchVendeurs();
      }
      handleDeleteDialogClose();
    } catch (error) {
      console.error('Error deleting vendeur:', error);
      let errorMessage = 'Erreur lors de la suppression du vendeur';

      // Try to extract the most useful error message
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.details) {
        errorMessage = error.response.data.details;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Helper function to get initials from name
  const getInitials = (name) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header */}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          p: 2,
          borderRadius: 2,
          background: 'white',
          position: 'relative',
          overflow: 'hidden',
          borderLeft: '4px solid',
          borderColor: 'primary.main'
        }}
        component={motion.div}
        variants={itemVariants}
      >
        <CardContent sx={{ p: 1, position: 'relative', zIndex: 2 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2
          }}>
            <Box>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 0.5,
                  letterSpacing: 0.7
                }}
              >
                GESTION DE L'ÉQUIPE
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600
                }}
              >
                Gérez les vendeurs associés à votre entreprise pour leur permettre d'émettre des factures et devis
              </Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddVendeur}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: '0 4px 10px rgba(0,0,0,0.15)',
                fontWeight: 'bold',
                letterSpacing: 0.5,
                textTransform: 'none',
                fontSize: '0.95rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 6px 15px rgba(0,0,0,0.2)',
                  transform: 'translateY(-2px)',
                }
              }}
            >
              Ajouter un vendeur
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={3}
        sx={{
          mb: 3,
          borderRadius: 2,
          overflow: 'hidden',
          background: 'linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95)), linear-gradient(to right, #f5f7fa, #e4e7eb)',
          boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
          border: '1px solid rgba(0,0,0,0.05)',
        }}
      >
        <CardContent sx={{ p: 2 }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2,
            justifyContent: 'space-between'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>
              <TextField
                placeholder="Rechercher un vendeur..."
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={handleSearchChange}
                sx={{
                  flexGrow: 1,
                  minWidth: 200,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 0 8px rgba(0,0,0,0.1)'
                    },
                    '&.Mui-focused': {
                      boxShadow: '0 0 8px rgba(0,0,0,0.1)'
                    }
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" color="primary" />
                    </InputAdornment>
                  ),
                }}
              />
              <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
                <InputLabel id="active-filter-label">État</InputLabel>
                <Select
                  labelId="active-filter-label"
                  value={activeFilter}
                  onChange={handleActiveFilterChange}
                  label="État"
                  sx={{
                    borderRadius: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 0 8px rgba(0,0,0,0.1)'
                    },
                    '&.Mui-focused': {
                      boxShadow: '0 0 8px rgba(0,0,0,0.1)'
                    }
                  }}
                >
                  <MenuItem value="all">Tous</MenuItem>
                  <MenuItem value="active">Actifs</MenuItem>
                  <MenuItem value="inactive">Inactifs</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Box>
              <Tooltip title="Rafraîchir les données" arrow placement="top">
                <IconButton
                  onClick={fetchVendeurs}
                  color="primary"
                  sx={{
                    bgcolor: 'primary.light',
                    color: 'white',
                    '&:hover': {
                      bgcolor: 'primary.main',
                      transform: 'rotate(180deg)',
                      transition: 'transform 0.5s ease'
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Vendeurs Table */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={3}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          background: 'linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95)), linear-gradient(to right, #f5f7fa, #e4e7eb)',
          boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
          border: '1px solid rgba(0,0,0,0.05)',
          animation: 'fadeIn 0.5s ease-out',
          animationDelay: '0.3s',
        }}
      >
        <TableContainer sx={{ overflow: 'auto' }}>
          <Table>
            <TableHead>
              <TableRow sx={{
                background: 'white',
                borderBottom: '2px solid',
                borderColor: 'primary.main',
              }}>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PeopleIcon fontSize="small" color="primary" />
                    <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>VENDEUR</Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EmailIcon fontSize="small" color="primary" />
                    <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>EMAIL</Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PhoneIcon fontSize="small" color="primary" />
                    <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>TÉLÉPHONE</Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DateRangeIcon fontSize="small" color="primary" />
                    <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>DATE D'AJOUT</Typography>
                  </Box>
                </TableCell>

                <TableCell align="right" sx={{ fontWeight: 'bold', color: 'text.primary', py: 2 }}>
                  <Typography variant="subtitle1" sx={{ letterSpacing: 0.5, color: 'primary.main' }}>ACTIONS</Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from(new Array(5)).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          bgcolor: '#f0f0f0',
                          mr: 2
                        }} />
                        <Box sx={{ height: 20, width: '70%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '90%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '60%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '40%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>

                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Box sx={{ height: 30, width: 30, bgcolor: '#f0f0f0', borderRadius: '50%' }} />
                        <Box sx={{ height: 30, width: 30, bgcolor: '#f0f0f0', borderRadius: '50%' }} />
                        <Box sx={{ height: 30, width: 30, bgcolor: '#f0f0f0', borderRadius: '50%' }} />
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredVendeurs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 5 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                      <PeopleIcon sx={{ fontSize: 60, color: 'text.disabled' }} />
                      <Typography variant="h6" color="text.secondary">
                        Aucun vendeur trouvé
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 500, textAlign: 'center', mb: 2 }}>
                        Modifiez vos critères de recherche ou cliquez sur "Ajouter un vendeur" pour créer un nouveau membre d'équipe
                      </Typography>
                      <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleAddVendeur}
                        sx={{ mt: 1 }}
                      >
                        Ajouter un vendeur
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                filteredVendeurs.map((vendeur, index) => (
                  <TableRow
                    key={vendeur._id}
                    hover
                    sx={{
                      backgroundColor: index % 2 === 0 ? 'white' : 'rgba(0, 0, 0, 0.02)',
                      transition: 'background-color 0.2s ease',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      }
                    }}
                  >
                    <TableCell sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={vendeur.profileImage ? `http://localhost:5000${vendeur.profileImage}` : ''}
                          sx={{
                            bgcolor: vendeur.actif ? 'primary.main' : 'text.disabled',
                            color: '#fff',
                            width: 40,
                            height: 40,
                            mr: 2,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                          }}
                        >
                          {!vendeur.profileImage && getInitials(vendeur.nom)}
                        </Avatar>
                        <Box>
                          <Typography
                            variant="body1"
                            fontWeight="medium"
                            sx={{
                              color: 'primary.dark'
                            }}
                          >
                            {vendeur.nom}
                          </Typography>
                          {vendeur.role && (
                            <Typography variant="caption" color="text.secondary">
                              Vendeur
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{vendeur.email}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{vendeur.telephone || '—'}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{new Date(vendeur.dateCreation).toLocaleDateString()}</Typography>
                    </TableCell>

                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Tooltip title="Voir détails" arrow placement="top">
                          <IconButton
                            onClick={() => { setSelectedVendeur(vendeur); handleViewDetails(); }}
                            size="small"
                            sx={{
                              bgcolor: 'info.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'info.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Modifier" arrow placement="top">
                          <IconButton
                            onClick={() => { setSelectedVendeur(vendeur); handleEditVendeur(); }}
                            size="small"
                            sx={{
                              bgcolor: 'primary.light',
                              color: 'white',
                              '&:hover': {
                                bgcolor: 'primary.main',
                                transform: 'translateY(-2px)',
                                boxShadow: 2
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Plus d'options" arrow placement="top">
                          <IconButton
                            onClick={(event) => handleMenuOpen(event, vendeur)}
                            size="small"
                            sx={{
                              bgcolor: 'action.hover',
                              '&:hover': {
                                bgcolor: 'action.selected',
                                transform: 'translateY(-2px)',
                                boxShadow: 1
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <MoreVertIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            overflow: 'visible',
            mt: 1.5,
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          }
        }}
      >
        <MenuItem onClick={handleViewDetails} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" color="info" />
          </ListItemIcon>
          <Typography variant="body2">Voir les détails</Typography>
        </MenuItem>
        <MenuItem onClick={handleEditVendeur} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <EditIcon fontSize="small" color="primary" />
          </ListItemIcon>
          <Typography variant="body2">Modifier</Typography>
        </MenuItem>
        <Divider sx={{ my: 1 }} />
        <MenuItem onClick={handleDeleteDialogOpen} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <Typography variant="body2" color="error.main" fontWeight="medium">Supprimer</Typography>
        </MenuItem>
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{
            width: '100%',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            borderRadius: 2
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleDetailsDialogClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          elevation: 5,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{
          pb: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}>
          <Typography variant="h6" fontWeight="bold" color="primary.main">
            Détails du vendeur
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleDetailsDialogClose}
            size="small"
            sx={{
              color: 'text.secondary',
              bgcolor: 'action.hover',
              '&:hover': {
                bgcolor: 'action.selected',
              }
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          {selectedVendeur && (
            <Box>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 4,
                pb: 3,
                borderBottom: '1px dashed',
                borderColor: 'divider'
              }}>
                <Avatar
                  src={selectedVendeur.profileImage ? `http://localhost:5000${selectedVendeur.profileImage}` : ''}
                  sx={{
                    bgcolor: selectedVendeur.actif ? 'primary.main' : 'text.disabled',
                    color: '#fff',
                    width: 80,
                    height: 80,
                    mr: 3,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                  }}
                >
                  {!selectedVendeur.profileImage && getInitials(selectedVendeur.nom)}
                </Avatar>
                <Box>
                  <Typography variant="h5" fontWeight="bold" color="primary.dark" gutterBottom>
                    {selectedVendeur.nom}
                  </Typography>
                  <Chip
                    label={selectedVendeur.actif ? 'Actif' : 'Inactif'}
                    color={selectedVendeur.actif ? 'success' : 'default'}
                    size="small"
                    icon={selectedVendeur.actif ? <CheckIcon fontSize="small" /> : <BlockIcon fontSize="small" />}
                    sx={{
                      borderRadius: 1,
                      fontWeight: 'medium',
                      boxShadow: selectedVendeur.actif ? '0 2px 5px rgba(76, 175, 80, 0.2)' : 'none'
                    }}
                  />
                </Box>
              </Box>

              <Typography variant="subtitle1" color="primary.main" fontWeight="bold" gutterBottom>
                Coordonnées
              </Typography>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.light', width: 36, height: 36, mr: 2 }}>
                    <EmailIcon fontSize="small" />
                  </Avatar>
                  <Box>
                    <Typography variant="caption" color="text.secondary">Email</Typography>
                    <Typography variant="body1" fontWeight="medium">{selectedVendeur.email}</Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'primary.light', width: 36, height: 36, mr: 2 }}>
                    <PhoneIcon fontSize="small" />
                  </Avatar>
                  <Box>
                    <Typography variant="caption" color="text.secondary">Téléphone</Typography>
                    <Typography variant="body1" fontWeight="medium">{selectedVendeur.telephone || 'Non renseigné'}</Typography>
                  </Box>
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Typography variant="subtitle1" color="primary.main" fontWeight="bold" gutterBottom>
                Statistiques
              </Typography>
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6} sm={4}>
                  <Card elevation={1} sx={{ p: 2, borderRadius: 2, bgcolor: 'background.paper' }}>
                    <Typography variant="caption" color="text.secondary">Factures</Typography>
                    <Typography variant="h6" fontWeight="bold" color="primary.dark">
                      {selectedVendeur.factures || 0}
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Card elevation={1} sx={{ p: 2, borderRadius: 2, bgcolor: 'background.paper' }}>
                    <Typography variant="caption" color="text.secondary">Devis</Typography>
                    <Typography variant="h6" fontWeight="bold" color="primary.dark">
                      {selectedVendeur.devis || 0}
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Card elevation={1} sx={{ p: 2, borderRadius: 2, bgcolor: 'background.paper' }}>
                    <Typography variant="caption" color="text.secondary">Date d'ajout</Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {new Date(selectedVendeur.dateCreation).toLocaleDateString()}
                    </Typography>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button
            onClick={handleDetailsDialogClose}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Fermer
          </Button>
          <Button
            variant="contained"
            startIcon={<EditIcon />}
            onClick={() => {
              handleDetailsDialogClose();
              handleEditVendeur();
            }}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
            }}
          >
            Modifier
          </Button>
        </DialogActions>
      </Dialog>

      {/* Form Dialog */}
      <Dialog
        open={formDialogOpen}
        onClose={handleFormDialogClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          elevation: 5,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{
          pb: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}>
          <Typography variant="h6" fontWeight="bold" color="primary.main">
            {selectedVendeur ? 'Modifier le vendeur' : 'Ajouter un vendeur'}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleFormDialogClose}
            size="small"
            sx={{
              color: 'text.secondary',
              bgcolor: 'action.hover',
              '&:hover': {
                bgcolor: 'action.selected',
              }
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box component="form" id="vendeur-form" sx={{ mt: 1 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} display="flex" justifyContent="center" mb={2}>
                <Box sx={{ position: 'relative', mb: 2 }}>
                  <Avatar
                    src={avatarPreview || (selectedVendeur?.profileImage ? `http://localhost:5000${selectedVendeur.profileImage}` : '')}
                    alt={selectedVendeur?.nom || 'Nouveau vendeur'}
                    sx={{
                      width: 120,
                      height: 120,
                      bgcolor: 'primary.main',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                    }}
                  >
                    {!avatarPreview && !selectedVendeur?.profileImage && getInitials(selectedVendeur?.nom || 'NV')}
                  </Avatar>
                  <Box sx={{ position: 'absolute', bottom: 0, right: 0 }}>
                    <input
                      accept="image/*"
                      style={{ display: 'none' }}
                      id="avatar-upload"
                      type="file"
                      onChange={handleAvatarChange}
                    />
                    <label htmlFor="avatar-upload">
                      <IconButton
                        component="span"
                        sx={{
                          bgcolor: 'primary.main',
                          color: 'white',
                          '&:hover': {
                            bgcolor: 'primary.dark'
                          }
                        }}
                      >
                        <PhotoCameraIcon fontSize="small" />
                      </IconButton>
                    </label>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="Nom complet"
                  fullWidth
                  required
                  defaultValue={selectedVendeur?.nom || ''}
                  name="nom"
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Email"
                  fullWidth
                  required
                  type="email"
                  defaultValue={selectedVendeur?.email || ''}
                  name="email"
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Téléphone"
                  fullWidth
                  defaultValue={selectedVendeur?.telephone || ''}
                  name="telephone"
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }
                  }}
                />
              </Grid>
              {!selectedVendeur && (
                <Grid item xs={12}>
                  <TextField
                    label="Mot de passe"
                    fullWidth
                    required
                    type="password"
                    name="motDePasse"
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                        },
                      }
                    }}
                  />
                </Grid>
              )}
              <Grid item xs={12}>
                <TextField
                  label="Adresse"
                  fullWidth
                  multiline
                  rows={2}
                  defaultValue={selectedVendeur?.adresse || ''}
                  name="adresse"
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      defaultChecked={selectedVendeur ? selectedVendeur.actif : true}
                      name="actif"
                      color="success"
                    />
                  }
                  label={
                    <Typography variant="body2" fontWeight="medium">
                      Compte actif
                    </Typography>
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button
            onClick={handleFormDialogClose}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            type="submit"
            form="vendeur-form"
            onClick={(e) => {
              e.preventDefault();
              // Get form data
              const form = document.getElementById('vendeur-form');
              const formData = new FormData(form);
              const vendeurData = {
                nom: formData.get('nom'),
                email: formData.get('email'),
                telephone: formData.get('telephone'),
                adresse: formData.get('adresse'),
                actif: formData.has('actif'),
                role: 'VENDEUR'
              };

              // Add password for new vendeur
              if (!selectedVendeur) {
                vendeurData.motDePasse = formData.get('motDePasse');
              }

              handleSaveVendeur(vendeurData);
            }}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
            }}
          >
            {selectedVendeur ? 'Mettre à jour' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          elevation: 5,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{
          pb: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          color: 'error.main'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DeleteIcon color="error" />
            <Typography variant="h6" fontWeight="bold">
              Confirmer la suppression
            </Typography>
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleDeleteDialogClose}
            size="small"
            sx={{
              color: 'text.secondary',
              bgcolor: 'action.hover',
              '&:hover': {
                bgcolor: 'action.selected',
              }
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Cette action est irréversible et supprimera définitivement ce vendeur.
          </Alert>
          <Typography variant="body1" sx={{ mb: 1 }}>
            Êtes-vous sûr de vouloir supprimer le vendeur <strong>{selectedVendeur?.nom}</strong> ?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Toutes les données associées à ce vendeur seront conservées, mais il ne pourra plus se connecter à l'application.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={handleDeleteDialogClose}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={handleDeleteVendeur}
            startIcon={<DeleteIcon />}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 10px rgba(244, 67, 54, 0.2)',
            }}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EquipeManagement;
