(()=>{var e={16:function(e){(function(t){"use strict";var r,s=20,n=1,i=1e6,o=1e6,u=-7,c=21,f="[big.js] ",a=f+"Invalid ",l=a+"decimal places",p=a+"rounding mode",h=f+"Division by zero",g={},d=void 0,_=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function _Big_(){function Big(e){var t=this;if(!(t instanceof Big))return e===d?_Big_():new Big(e);if(e instanceof Big){t.s=e.s;t.e=e.e;t.c=e.c.slice()}else{parse(t,e)}t.constructor=Big}Big.prototype=g;Big.DP=s;Big.RM=n;Big.NE=u;Big.PE=c;Big.version="5.2.2";return Big}function parse(e,t){var r,s,n;if(t===0&&1/t<0)t="-0";else if(!_.test(t+=""))throw Error(a+"number");e.s=t.charAt(0)=="-"?(t=t.slice(1),-1):1;if((r=t.indexOf("."))>-1)t=t.replace(".","");if((s=t.search(/e/i))>0){if(r<0)r=s;r+=+t.slice(s+1);t=t.substring(0,s)}else if(r<0){r=t.length}n=t.length;for(s=0;s<n&&t.charAt(s)=="0";)++s;if(s==n){e.c=[e.e=0]}else{for(;n>0&&t.charAt(--n)=="0";);e.e=r-s-1;e.c=[];for(r=0;s<=n;)e.c[r++]=+t.charAt(s++)}return e}function round(e,t,r,s){var n=e.c,i=e.e+t+1;if(i<n.length){if(r===1){s=n[i]>=5}else if(r===2){s=n[i]>5||n[i]==5&&(s||i<0||n[i+1]!==d||n[i-1]&1)}else if(r===3){s=s||!!n[0]}else{s=false;if(r!==0)throw Error(p)}if(i<1){n.length=1;if(s){e.e=-t;n[0]=1}else{n[0]=e.e=0}}else{n.length=i--;if(s){for(;++n[i]>9;){n[i]=0;if(!i--){++e.e;n.unshift(1)}}}for(i=n.length;!n[--i];)n.pop()}}else if(r<0||r>3||r!==~~r){throw Error(p)}return e}function stringify(e,t,r,s){var n,o,u=e.constructor,c=!e.c[0];if(r!==d){if(r!==~~r||r<(t==3)||r>i){throw Error(t==3?a+"precision":l)}e=new u(e);r=s-e.e;if(e.c.length>++s)round(e,r,u.RM);if(t==2)s=e.e+r+1;for(;e.c.length<s;)e.c.push(0)}n=e.e;o=e.c.join("");r=o.length;if(t!=2&&(t==1||t==3&&s<=n||n<=u.NE||n>=u.PE)){o=o.charAt(0)+(r>1?"."+o.slice(1):"")+(n<0?"e":"e+")+n}else if(n<0){for(;++n;)o="0"+o;o="0."+o}else if(n>0){if(++n>r)for(n-=r;n--;)o+="0";else if(n<r)o=o.slice(0,n)+"."+o.slice(n)}else if(r>1){o=o.charAt(0)+"."+o.slice(1)}return e.s<0&&(!c||t==4)?"-"+o:o}g.abs=function(){var e=new this.constructor(this);e.s=1;return e};g.cmp=function(e){var t,r=this,s=r.c,n=(e=new r.constructor(e)).c,i=r.s,o=e.s,u=r.e,c=e.e;if(!s[0]||!n[0])return!s[0]?!n[0]?0:-o:i;if(i!=o)return i;t=i<0;if(u!=c)return u>c^t?1:-1;o=(u=s.length)<(c=n.length)?u:c;for(i=-1;++i<o;){if(s[i]!=n[i])return s[i]>n[i]^t?1:-1}return u==c?0:u>c^t?1:-1};g.div=function(e){var t=this,r=t.constructor,s=t.c,n=(e=new r(e)).c,o=t.s==e.s?1:-1,u=r.DP;if(u!==~~u||u<0||u>i)throw Error(l);if(!n[0])throw Error(h);if(!s[0])return new r(o*0);var c,f,a,p,g,_=n.slice(),b=c=n.length,w=s.length,m=s.slice(0,c),q=m.length,x=e,v=x.c=[],y=0,R=u+(x.e=t.e-e.e)+1;x.s=o;o=R<0?0:R;_.unshift(0);for(;q++<c;)m.push(0);do{for(a=0;a<10;a++){if(c!=(q=m.length)){p=c>q?1:-1}else{for(g=-1,p=0;++g<c;){if(n[g]!=m[g]){p=n[g]>m[g]?1:-1;break}}}if(p<0){for(f=q==c?n:_;q;){if(m[--q]<f[q]){g=q;for(;g&&!m[--g];)m[g]=9;--m[g];m[q]+=10}m[q]-=f[q]}for(;!m[0];)m.shift()}else{break}}v[y++]=p?a:++a;if(m[0]&&p)m[q]=s[b]||0;else m=[s[b]]}while((b++<w||m[0]!==d)&&o--);if(!v[0]&&y!=1){v.shift();x.e--}if(y>R)round(x,u,r.RM,m[0]!==d);return x};g.eq=function(e){return!this.cmp(e)};g.gt=function(e){return this.cmp(e)>0};g.gte=function(e){return this.cmp(e)>-1};g.lt=function(e){return this.cmp(e)<0};g.lte=function(e){return this.cmp(e)<1};g.minus=g.sub=function(e){var t,r,s,n,i=this,o=i.constructor,u=i.s,c=(e=new o(e)).s;if(u!=c){e.s=-c;return i.plus(e)}var f=i.c.slice(),a=i.e,l=e.c,p=e.e;if(!f[0]||!l[0]){return l[0]?(e.s=-c,e):new o(f[0]?i:0)}if(u=a-p){if(n=u<0){u=-u;s=f}else{p=a;s=l}s.reverse();for(c=u;c--;)s.push(0);s.reverse()}else{r=((n=f.length<l.length)?f:l).length;for(u=c=0;c<r;c++){if(f[c]!=l[c]){n=f[c]<l[c];break}}}if(n){s=f;f=l;l=s;e.s=-e.s}if((c=(r=l.length)-(t=f.length))>0)for(;c--;)f[t++]=0;for(c=t;r>u;){if(f[--r]<l[r]){for(t=r;t&&!f[--t];)f[t]=9;--f[t];f[r]+=10}f[r]-=l[r]}for(;f[--c]===0;)f.pop();for(;f[0]===0;){f.shift();--p}if(!f[0]){e.s=1;f=[p=0]}e.c=f;e.e=p;return e};g.mod=function(e){var t,r=this,s=r.constructor,n=r.s,i=(e=new s(e)).s;if(!e.c[0])throw Error(h);r.s=e.s=1;t=e.cmp(r)==1;r.s=n;e.s=i;if(t)return new s(r);n=s.DP;i=s.RM;s.DP=s.RM=0;r=r.div(e);s.DP=n;s.RM=i;return this.minus(r.times(e))};g.plus=g.add=function(e){var t,r=this,s=r.constructor,n=r.s,i=(e=new s(e)).s;if(n!=i){e.s=-i;return r.minus(e)}var o=r.e,u=r.c,c=e.e,f=e.c;if(!u[0]||!f[0])return f[0]?e:new s(u[0]?r:n*0);u=u.slice();if(n=o-c){if(n>0){c=o;t=f}else{n=-n;t=u}t.reverse();for(;n--;)t.push(0);t.reverse()}if(u.length-f.length<0){t=f;f=u;u=t}n=f.length;for(i=0;n;u[n]%=10)i=(u[--n]=u[n]+f[n]+i)/10|0;if(i){u.unshift(i);++c}for(n=u.length;u[--n]===0;)u.pop();e.c=u;e.e=c;return e};g.pow=function(e){var t=this,r=new t.constructor(1),s=r,n=e<0;if(e!==~~e||e<-o||e>o)throw Error(a+"exponent");if(n)e=-e;for(;;){if(e&1)s=s.times(t);e>>=1;if(!e)break;t=t.times(t)}return n?r.div(s):s};g.round=function(e,t){var r=this.constructor;if(e===d)e=0;else if(e!==~~e||e<-i||e>i)throw Error(l);return round(new r(this),e,t===d?r.RM:t)};g.sqrt=function(){var e,t,r,s=this,n=s.constructor,i=s.s,o=s.e,u=new n(.5);if(!s.c[0])return new n(s);if(i<0)throw Error(f+"No square root");i=Math.sqrt(s+"");if(i===0||i===1/0){t=s.c.join("");if(!(t.length+o&1))t+="0";i=Math.sqrt(t);o=((o+1)/2|0)-(o<0||o&1);e=new n((i==1/0?"1e":(i=i.toExponential()).slice(0,i.indexOf("e")+1))+o)}else{e=new n(i)}o=e.e+(n.DP+=4);do{r=e;e=u.times(r.plus(s.div(r)))}while(r.c.slice(0,o).join("")!==e.c.slice(0,o).join(""));return round(e,n.DP-=4,n.RM)};g.times=g.mul=function(e){var t,r=this,s=r.constructor,n=r.c,i=(e=new s(e)).c,o=n.length,u=i.length,c=r.e,f=e.e;e.s=r.s==e.s?1:-1;if(!n[0]||!i[0])return new s(e.s*0);e.e=c+f;if(o<u){t=n;n=i;i=t;f=o;o=u;u=f}for(t=new Array(f=o+u);f--;)t[f]=0;for(c=u;c--;){u=0;for(f=o+c;f>c;){u=t[f]+i[c]*n[f-c-1]+u;t[f--]=u%10;u=u/10|0}t[f]=(t[f]+u)%10}if(u)++e.e;else t.shift();for(c=t.length;!t[--c];)t.pop();e.c=t;return e};g.toExponential=function(e){return stringify(this,1,e,e)};g.toFixed=function(e){return stringify(this,2,e,this.e+e)};g.toPrecision=function(e){return stringify(this,3,e,e-1)};g.toString=function(){return stringify(this)};g.valueOf=g.toJSON=function(){return stringify(this,4)};r=_Big_();r["default"]=r.Big=r;if(typeof define==="function"&&define.amd){define((function(){return r}))}else if(true&&e.exports){e.exports=r}else{t.Big=r}})(this)},74:e=>{e.exports=["🀄️","🃏","🅰️","🅱️","🅾️","🅿️","🆎","🆑","🆒","🆓","🆔","🆕","🆖","🆗","🆘","🆙","🆚","🇦🇨","🇦🇩","🇦🇪","🇦🇫","🇦🇬","🇦🇮","🇦🇱","🇦🇲","🇦🇴","🇦🇶","🇦🇷","🇦🇸","🇦🇹","🇦🇺","🇦🇼","🇦🇽","🇦🇿","🇦","🇧🇦","🇧🇧","🇧🇩","🇧🇪","🇧🇫","🇧🇬","🇧🇭","🇧🇮","🇧🇯","🇧🇱","🇧🇲","🇧🇳","🇧🇴","🇧🇶","🇧🇷","🇧🇸","🇧🇹","🇧🇻","🇧🇼","🇧🇾","🇧🇿","🇧","🇨🇦","🇨🇨","🇨🇩","🇨🇫","🇨🇬","🇨🇭","🇨🇮","🇨🇰","🇨🇱","🇨🇲","🇨🇳","🇨🇴","🇨🇵","🇨🇷","🇨🇺","🇨🇻","🇨🇼","🇨🇽","🇨🇾","🇨🇿","🇨","🇩🇪","🇩🇬","🇩🇯","🇩🇰","🇩🇲","🇩🇴","🇩🇿","🇩","🇪🇦","🇪🇨","🇪🇪","🇪🇬","🇪🇭","🇪🇷","🇪🇸","🇪🇹","🇪🇺","🇪","🇫🇮","🇫🇯","🇫🇰","🇫🇲","🇫🇴","🇫🇷","🇫","🇬🇦","🇬🇧","🇬🇩","🇬🇪","🇬🇫","🇬🇬","🇬🇭","🇬🇮","🇬🇱","🇬🇲","🇬🇳","🇬🇵","🇬🇶","🇬🇷","🇬🇸","🇬🇹","🇬🇺","🇬🇼","🇬🇾","🇬","🇭🇰","🇭🇲","🇭🇳","🇭🇷","🇭🇹","🇭🇺","🇭","🇮🇨","🇮🇩","🇮🇪","🇮🇱","🇮🇲","🇮🇳","🇮🇴","🇮🇶","🇮🇷","🇮🇸","🇮🇹","🇮","🇯🇪","🇯🇲","🇯🇴","🇯🇵","🇯","🇰🇪","🇰🇬","🇰🇭","🇰🇮","🇰🇲","🇰🇳","🇰🇵","🇰🇷","🇰🇼","🇰🇾","🇰🇿","🇰","🇱🇦","🇱🇧","🇱🇨","🇱🇮","🇱🇰","🇱🇷","🇱🇸","🇱🇹","🇱🇺","🇱🇻","🇱🇾","🇱","🇲🇦","🇲🇨","🇲🇩","🇲🇪","🇲🇫","🇲🇬","🇲🇭","🇲🇰","🇲🇱","🇲🇲","🇲🇳","🇲🇴","🇲🇵","🇲🇶","🇲🇷","🇲🇸","🇲🇹","🇲🇺","🇲🇻","🇲🇼","🇲🇽","🇲🇾","🇲🇿","🇲","🇳🇦","🇳🇨","🇳🇪","🇳🇫","🇳🇬","🇳🇮","🇳🇱","🇳🇴","🇳🇵","🇳🇷","🇳🇺","🇳🇿","🇳","🇴🇲","🇴","🇵🇦","🇵🇪","🇵🇫","🇵🇬","🇵🇭","🇵🇰","🇵🇱","🇵🇲","🇵🇳","🇵🇷","🇵🇸","🇵🇹","🇵🇼","🇵🇾","🇵","🇶🇦","🇶","🇷🇪","🇷🇴","🇷🇸","🇷🇺","🇷🇼","🇷","🇸🇦","🇸🇧","🇸🇨","🇸🇩","🇸🇪","🇸🇬","🇸🇭","🇸🇮","🇸🇯","🇸🇰","🇸🇱","🇸🇲","🇸🇳","🇸🇴","🇸🇷","🇸🇸","🇸🇹","🇸🇻","🇸🇽","🇸🇾","🇸🇿","🇸","🇹🇦","🇹🇨","🇹🇩","🇹🇫","🇹🇬","🇹🇭","🇹🇯","🇹🇰","🇹🇱","🇹🇲","🇹🇳","🇹🇴","🇹🇷","🇹🇹","🇹🇻","🇹🇼","🇹🇿","🇹","🇺🇦","🇺🇬","🇺🇲","🇺🇳","🇺🇸","🇺🇾","🇺🇿","🇺","🇻🇦","🇻🇨","🇻🇪","🇻🇬","🇻🇮","🇻🇳","🇻🇺","🇻","🇼🇫","🇼🇸","🇼","🇽🇰","🇽","🇾🇪","🇾🇹","🇾","🇿🇦","🇿🇲","🇿🇼","🇿","🈁","🈂️","🈚️","🈯️","🈲","🈳","🈴","🈵","🈶","🈷️","🈸","🈹","🈺","🉐","🉑","🌀","🌁","🌂","🌃","🌄","🌅","🌆","🌇","🌈","🌉","🌊","🌋","🌌","🌍","🌎","🌏","🌐","🌑","🌒","🌓","🌔","🌕","🌖","🌗","🌘","🌙","🌚","🌛","🌜","🌝","🌞","🌟","🌠","🌡️","🌤️","🌥️","🌦️","🌧️","🌨️","🌩️","🌪️","🌫️","🌬️","🌭","🌮","🌯","🌰","🌱","🌲","🌳","🌴","🌵","🌶️","🌷","🌸","🌹","🌺","🌻","🌼","🌽","🌾","🌿","🍀","🍁","🍂","🍃","🍄","🍅","🍆","🍇","🍈","🍉","🍊","🍋","🍌","🍍","🍎","🍏","🍐","🍑","🍒","🍓","🍔","🍕","🍖","🍗","🍘","🍙","🍚","🍛","🍜","🍝","🍞","🍟","🍠","🍡","🍢","🍣","🍤","🍥","🍦","🍧","🍨","🍩","🍪","🍫","🍬","🍭","🍮","🍯","🍰","🍱","🍲","🍳","🍴","🍵","🍶","🍷","🍸","🍹","🍺","🍻","🍼","🍽️","🍾","🍿","🎀","🎁","🎂","🎃","🎄","🎅🏻","🎅🏼","🎅🏽","🎅🏾","🎅🏿","🎅","🎆","🎇","🎈","🎉","🎊","🎋","🎌","🎍","🎎","🎏","🎐","🎑","🎒","🎓","🎖️","🎗️","🎙️","🎚️","🎛️","🎞️","🎟️","🎠","🎡","🎢","🎣","🎤","🎥","🎦","🎧","🎨","🎩","🎪","🎫","🎬","🎭","🎮","🎯","🎰","🎱","🎲","🎳","🎴","🎵","🎶","🎷","🎸","🎹","🎺","🎻","🎼","🎽","🎾","🎿","🏀","🏁","🏂🏻","🏂🏼","🏂🏽","🏂🏾","🏂🏿","🏂","🏃🏻‍♀️","🏃🏻‍♂️","🏃🏻","🏃🏼‍♀️","🏃🏼‍♂️","🏃🏼","🏃🏽‍♀️","🏃🏽‍♂️","🏃🏽","🏃🏾‍♀️","🏃🏾‍♂️","🏃🏾","🏃🏿‍♀️","🏃🏿‍♂️","🏃🏿","🏃‍♀️","🏃‍♂️","🏃","🏄🏻‍♀️","🏄🏻‍♂️","🏄🏻","🏄🏼‍♀️","🏄🏼‍♂️","🏄🏼","🏄🏽‍♀️","🏄🏽‍♂️","🏄🏽","🏄🏾‍♀️","🏄🏾‍♂️","🏄🏾","🏄🏿‍♀️","🏄🏿‍♂️","🏄🏿","🏄‍♀️","🏄‍♂️","🏄","🏅","🏆","🏇🏻","🏇🏼","🏇🏽","🏇🏾","🏇🏿","🏇","🏈","🏉","🏊🏻‍♀️","🏊🏻‍♂️","🏊🏻","🏊🏼‍♀️","🏊🏼‍♂️","🏊🏼","🏊🏽‍♀️","🏊🏽‍♂️","🏊🏽","🏊🏾‍♀️","🏊🏾‍♂️","🏊🏾","🏊🏿‍♀️","🏊🏿‍♂️","🏊🏿","🏊‍♀️","🏊‍♂️","🏊","🏋🏻‍♀️","🏋🏻‍♂️","🏋🏻","🏋🏼‍♀️","🏋🏼‍♂️","🏋🏼","🏋🏽‍♀️","🏋🏽‍♂️","🏋🏽","🏋🏾‍♀️","🏋🏾‍♂️","🏋🏾","🏋🏿‍♀️","🏋🏿‍♂️","🏋🏿","🏋️‍♀️","🏋️‍♂️","🏋️","🏌🏻‍♀️","🏌🏻‍♂️","🏌🏻","🏌🏼‍♀️","🏌🏼‍♂️","🏌🏼","🏌🏽‍♀️","🏌🏽‍♂️","🏌🏽","🏌🏾‍♀️","🏌🏾‍♂️","🏌🏾","🏌🏿‍♀️","🏌🏿‍♂️","🏌🏿","🏌️‍♀️","🏌️‍♂️","🏌️","🏍️","🏎️","🏏","🏐","🏑","🏒","🏓","🏔️","🏕️","🏖️","🏗️","🏘️","🏙️","🏚️","🏛️","🏜️","🏝️","🏞️","🏟️","🏠","🏡","🏢","🏣","🏤","🏥","🏦","🏧","🏨","🏩","🏪","🏫","🏬","🏭","🏮","🏯","🏰","🏳️‍🌈","🏳️","🏴‍☠️","🏴󠁧󠁢󠁥󠁮󠁧󠁿","🏴󠁧󠁢󠁳󠁣󠁴󠁿","🏴󠁧󠁢󠁷󠁬󠁳󠁿","🏴","🏵️","🏷️","🏸","🏹","🏺","🏻","🏼","🏽","🏾","🏿","🐀","🐁","🐂","🐃","🐄","🐅","🐆","🐇","🐈","🐉","🐊","🐋","🐌","🐍","🐎","🐏","🐐","🐑","🐒","🐓","🐔","🐕‍🦺","🐕","🐖","🐗","🐘","🐙","🐚","🐛","🐜","🐝","🐞","🐟","🐠","🐡","🐢","🐣","🐤","🐥","🐦","🐧","🐨","🐩","🐪","🐫","🐬","🐭","🐮","🐯","🐰","🐱","🐲","🐳","🐴","🐵","🐶","🐷","🐸","🐹","🐺","🐻","🐼","🐽","🐾","🐿️","👀","👁‍🗨","👁️","👂🏻","👂🏼","👂🏽","👂🏾","👂🏿","👂","👃🏻","👃🏼","👃🏽","👃🏾","👃🏿","👃","👄","👅","👆🏻","👆🏼","👆🏽","👆🏾","👆🏿","👆","👇🏻","👇🏼","👇🏽","👇🏾","👇🏿","👇","👈🏻","👈🏼","👈🏽","👈🏾","👈🏿","👈","👉🏻","👉🏼","👉🏽","👉🏾","👉🏿","👉","👊🏻","👊🏼","👊🏽","👊🏾","👊🏿","👊","👋🏻","👋🏼","👋🏽","👋🏾","👋🏿","👋","👌🏻","👌🏼","👌🏽","👌🏾","👌🏿","👌","👍🏻","👍🏼","👍🏽","👍🏾","👍🏿","👍","👎🏻","👎🏼","👎🏽","👎🏾","👎🏿","👎","👏🏻","👏🏼","👏🏽","👏🏾","👏🏿","👏","👐🏻","👐🏼","👐🏽","👐🏾","👐🏿","👐","👑","👒","👓","👔","👕","👖","👗","👘","👙","👚","👛","👜","👝","👞","👟","👠","👡","👢","👣","👤","👥","👦🏻","👦🏼","👦🏽","👦🏾","👦🏿","👦","👧🏻","👧🏼","👧🏽","👧🏾","👧🏿","👧","👨🏻‍🌾","👨🏻‍🍳","👨🏻‍🎓","👨🏻‍🎤","👨🏻‍🎨","👨🏻‍🏫","👨🏻‍🏭","👨🏻‍💻","👨🏻‍💼","👨🏻‍🔧","👨🏻‍🔬","👨🏻‍🚀","👨🏻‍🚒","👨🏻‍🦯","👨🏻‍🦰","👨🏻‍🦱","👨🏻‍🦲","👨🏻‍🦳","👨🏻‍🦼","👨🏻‍🦽","👨🏻‍⚕️","👨🏻‍⚖️","👨🏻‍✈️","👨🏻","👨🏼‍🌾","👨🏼‍🍳","👨🏼‍🎓","👨🏼‍🎤","👨🏼‍🎨","👨🏼‍🏫","👨🏼‍🏭","👨🏼‍💻","👨🏼‍💼","👨🏼‍🔧","👨🏼‍🔬","👨🏼‍🚀","👨🏼‍🚒","👨🏼‍🤝‍👨🏻","👨🏼‍🦯","👨🏼‍🦰","👨🏼‍🦱","👨🏼‍🦲","👨🏼‍🦳","👨🏼‍🦼","👨🏼‍🦽","👨🏼‍⚕️","👨🏼‍⚖️","👨🏼‍✈️","👨🏼","👨🏽‍🌾","👨🏽‍🍳","👨🏽‍🎓","👨🏽‍🎤","👨🏽‍🎨","👨🏽‍🏫","👨🏽‍🏭","👨🏽‍💻","👨🏽‍💼","👨🏽‍🔧","👨🏽‍🔬","👨🏽‍🚀","👨🏽‍🚒","👨🏽‍🤝‍👨🏻","👨🏽‍🤝‍👨🏼","👨🏽‍🦯","👨🏽‍🦰","👨🏽‍🦱","👨🏽‍🦲","👨🏽‍🦳","👨🏽‍🦼","👨🏽‍🦽","👨🏽‍⚕️","👨🏽‍⚖️","👨🏽‍✈️","👨🏽","👨🏾‍🌾","👨🏾‍🍳","👨🏾‍🎓","👨🏾‍🎤","👨🏾‍🎨","👨🏾‍🏫","👨🏾‍🏭","👨🏾‍💻","👨🏾‍💼","👨🏾‍🔧","👨🏾‍🔬","👨🏾‍🚀","👨🏾‍🚒","👨🏾‍🤝‍👨🏻","👨🏾‍🤝‍👨🏼","👨🏾‍🤝‍👨🏽","👨🏾‍🦯","👨🏾‍🦰","👨🏾‍🦱","👨🏾‍🦲","👨🏾‍🦳","👨🏾‍🦼","👨🏾‍🦽","👨🏾‍⚕️","👨🏾‍⚖️","👨🏾‍✈️","👨🏾","👨🏿‍🌾","👨🏿‍🍳","👨🏿‍🎓","👨🏿‍🎤","👨🏿‍🎨","👨🏿‍🏫","👨🏿‍🏭","👨🏿‍💻","👨🏿‍💼","👨🏿‍🔧","👨🏿‍🔬","👨🏿‍🚀","👨🏿‍🚒","👨🏿‍🤝‍👨🏻","👨🏿‍🤝‍👨🏼","👨🏿‍🤝‍👨🏽","👨🏿‍🤝‍👨🏾","👨🏿‍🦯","👨🏿‍🦰","👨🏿‍🦱","👨🏿‍🦲","👨🏿‍🦳","👨🏿‍🦼","👨🏿‍🦽","👨🏿‍⚕️","👨🏿‍⚖️","👨🏿‍✈️","👨🏿","👨‍🌾","👨‍🍳","👨‍🎓","👨‍🎤","👨‍🎨","👨‍🏫","👨‍🏭","👨‍👦‍👦","👨‍👦","👨‍👧‍👦","👨‍👧‍👧","👨‍👧","👨‍👨‍👦‍👦","👨‍👨‍👦","👨‍👨‍👧‍👦","👨‍👨‍👧‍👧","👨‍👨‍👧","👨‍👩‍👦‍👦","👨‍👩‍👦","👨‍👩‍👧‍👦","👨‍👩‍👧‍👧","👨‍👩‍👧","👨‍💻","👨‍💼","👨‍🔧","👨‍🔬","👨‍🚀","👨‍🚒","👨‍🦯","👨‍🦰","👨‍🦱","👨‍🦲","👨‍🦳","👨‍🦼","👨‍🦽","👨‍⚕️","👨‍⚖️","👨‍✈️","👨‍❤️‍👨","👨‍❤️‍💋‍👨","👨","👩🏻‍🌾","👩🏻‍🍳","👩🏻‍🎓","👩🏻‍🎤","👩🏻‍🎨","👩🏻‍🏫","👩🏻‍🏭","👩🏻‍💻","👩🏻‍💼","👩🏻‍🔧","👩🏻‍🔬","👩🏻‍🚀","👩🏻‍🚒","👩🏻‍🤝‍👨🏼","👩🏻‍🤝‍👨🏽","👩🏻‍🤝‍👨🏾","👩🏻‍🤝‍👨🏿","👩🏻‍🦯","👩🏻‍🦰","👩🏻‍🦱","👩🏻‍🦲","👩🏻‍🦳","👩🏻‍🦼","👩🏻‍🦽","👩🏻‍⚕️","👩🏻‍⚖️","👩🏻‍✈️","👩🏻","👩🏼‍🌾","👩🏼‍🍳","👩🏼‍🎓","👩🏼‍🎤","👩🏼‍🎨","👩🏼‍🏫","👩🏼‍🏭","👩🏼‍💻","👩🏼‍💼","👩🏼‍🔧","👩🏼‍🔬","👩🏼‍🚀","👩🏼‍🚒","👩🏼‍🤝‍👨🏻","👩🏼‍🤝‍👨🏽","👩🏼‍🤝‍👨🏾","👩🏼‍🤝‍👨🏿","👩🏼‍🤝‍👩🏻","👩🏼‍🦯","👩🏼‍🦰","👩🏼‍🦱","👩🏼‍🦲","👩🏼‍🦳","👩🏼‍🦼","👩🏼‍🦽","👩🏼‍⚕️","👩🏼‍⚖️","👩🏼‍✈️","👩🏼","👩🏽‍🌾","👩🏽‍🍳","👩🏽‍🎓","👩🏽‍🎤","👩🏽‍🎨","👩🏽‍🏫","👩🏽‍🏭","👩🏽‍💻","👩🏽‍💼","👩🏽‍🔧","👩🏽‍🔬","👩🏽‍🚀","👩🏽‍🚒","👩🏽‍🤝‍👨🏻","👩🏽‍🤝‍👨🏼","👩🏽‍🤝‍👨🏾","👩🏽‍🤝‍👨🏿","👩🏽‍🤝‍👩🏻","👩🏽‍🤝‍👩🏼","👩🏽‍🦯","👩🏽‍🦰","👩🏽‍🦱","👩🏽‍🦲","👩🏽‍🦳","👩🏽‍🦼","👩🏽‍🦽","👩🏽‍⚕️","👩🏽‍⚖️","👩🏽‍✈️","👩🏽","👩🏾‍🌾","👩🏾‍🍳","👩🏾‍🎓","👩🏾‍🎤","👩🏾‍🎨","👩🏾‍🏫","👩🏾‍🏭","👩🏾‍💻","👩🏾‍💼","👩🏾‍🔧","👩🏾‍🔬","👩🏾‍🚀","👩🏾‍🚒","👩🏾‍🤝‍👨🏻","👩🏾‍🤝‍👨🏼","👩🏾‍🤝‍👨🏽","👩🏾‍🤝‍👨🏿","👩🏾‍🤝‍👩🏻","👩🏾‍🤝‍👩🏼","👩🏾‍🤝‍👩🏽","👩🏾‍🦯","👩🏾‍🦰","👩🏾‍🦱","👩🏾‍🦲","👩🏾‍🦳","👩🏾‍🦼","👩🏾‍🦽","👩🏾‍⚕️","👩🏾‍⚖️","👩🏾‍✈️","👩🏾","👩🏿‍🌾","👩🏿‍🍳","👩🏿‍🎓","👩🏿‍🎤","👩🏿‍🎨","👩🏿‍🏫","👩🏿‍🏭","👩🏿‍💻","👩🏿‍💼","👩🏿‍🔧","👩🏿‍🔬","👩🏿‍🚀","👩🏿‍🚒","👩🏿‍🤝‍👨🏻","👩🏿‍🤝‍👨🏼","👩🏿‍🤝‍👨🏽","👩🏿‍🤝‍👨🏾","👩🏿‍🤝‍👩🏻","👩🏿‍🤝‍👩🏼","👩🏿‍🤝‍👩🏽","👩🏿‍🤝‍👩🏾","👩🏿‍🦯","👩🏿‍🦰","👩🏿‍🦱","👩🏿‍🦲","👩🏿‍🦳","👩🏿‍🦼","👩🏿‍🦽","👩🏿‍⚕️","👩🏿‍⚖️","👩🏿‍✈️","👩🏿","👩‍🌾","👩‍🍳","👩‍🎓","👩‍🎤","👩‍🎨","👩‍🏫","👩‍🏭","👩‍👦‍👦","👩‍👦","👩‍👧‍👦","👩‍👧‍👧","👩‍👧","👩‍👩‍👦‍👦","👩‍👩‍👦","👩‍👩‍👧‍👦","👩‍👩‍👧‍👧","👩‍👩‍👧","👩‍💻","👩‍💼","👩‍🔧","👩‍🔬","👩‍🚀","👩‍🚒","👩‍🦯","👩‍🦰","👩‍🦱","👩‍🦲","👩‍🦳","👩‍🦼","👩‍🦽","👩‍⚕️","👩‍⚖️","👩‍✈️","👩‍❤️‍👨","👩‍❤️‍👩","👩‍❤️‍💋‍👨","👩‍❤️‍💋‍👩","👩","👪","👫🏻","👫🏼","👫🏽","👫🏾","👫🏿","👫","👬🏻","👬🏼","👬🏽","👬🏾","👬🏿","👬","👭🏻","👭🏼","👭🏽","👭🏾","👭🏿","👭","👮🏻‍♀️","👮🏻‍♂️","👮🏻","👮🏼‍♀️","👮🏼‍♂️","👮🏼","👮🏽‍♀️","👮🏽‍♂️","👮🏽","👮🏾‍♀️","👮🏾‍♂️","👮🏾","👮🏿‍♀️","👮🏿‍♂️","👮🏿","👮‍♀️","👮‍♂️","👮","👯‍♀️","👯‍♂️","👯","👰🏻","👰🏼","👰🏽","👰🏾","👰🏿","👰","👱🏻‍♀️","👱🏻‍♂️","👱🏻","👱🏼‍♀️","👱🏼‍♂️","👱🏼","👱🏽‍♀️","👱🏽‍♂️","👱🏽","👱🏾‍♀️","👱🏾‍♂️","👱🏾","👱🏿‍♀️","👱🏿‍♂️","👱🏿","👱‍♀️","👱‍♂️","👱","👲🏻","👲🏼","👲🏽","👲🏾","👲🏿","👲","👳🏻‍♀️","👳🏻‍♂️","👳🏻","👳🏼‍♀️","👳🏼‍♂️","👳🏼","👳🏽‍♀️","👳🏽‍♂️","👳🏽","👳🏾‍♀️","👳🏾‍♂️","👳🏾","👳🏿‍♀️","👳🏿‍♂️","👳🏿","👳‍♀️","👳‍♂️","👳","👴🏻","👴🏼","👴🏽","👴🏾","👴🏿","👴","👵🏻","👵🏼","👵🏽","👵🏾","👵🏿","👵","👶🏻","👶🏼","👶🏽","👶🏾","👶🏿","👶","👷🏻‍♀️","👷🏻‍♂️","👷🏻","👷🏼‍♀️","👷🏼‍♂️","👷🏼","👷🏽‍♀️","👷🏽‍♂️","👷🏽","👷🏾‍♀️","👷🏾‍♂️","👷🏾","👷🏿‍♀️","👷🏿‍♂️","👷🏿","👷‍♀️","👷‍♂️","👷","👸🏻","👸🏼","👸🏽","👸🏾","👸🏿","👸","👹","👺","👻","👼🏻","👼🏼","👼🏽","👼🏾","👼🏿","👼","👽","👾","👿","💀","💁🏻‍♀️","💁🏻‍♂️","💁🏻","💁🏼‍♀️","💁🏼‍♂️","💁🏼","💁🏽‍♀️","💁🏽‍♂️","💁🏽","💁🏾‍♀️","💁🏾‍♂️","💁🏾","💁🏿‍♀️","💁🏿‍♂️","💁🏿","💁‍♀️","💁‍♂️","💁","💂🏻‍♀️","💂🏻‍♂️","💂🏻","💂🏼‍♀️","💂🏼‍♂️","💂🏼","💂🏽‍♀️","💂🏽‍♂️","💂🏽","💂🏾‍♀️","💂🏾‍♂️","💂🏾","💂🏿‍♀️","💂🏿‍♂️","💂🏿","💂‍♀️","💂‍♂️","💂","💃🏻","💃🏼","💃🏽","💃🏾","💃🏿","💃","💄","💅🏻","💅🏼","💅🏽","💅🏾","💅🏿","💅","💆🏻‍♀️","💆🏻‍♂️","💆🏻","💆🏼‍♀️","💆🏼‍♂️","💆🏼","💆🏽‍♀️","💆🏽‍♂️","💆🏽","💆🏾‍♀️","💆🏾‍♂️","💆🏾","💆🏿‍♀️","💆🏿‍♂️","💆🏿","💆‍♀️","💆‍♂️","💆","💇🏻‍♀️","💇🏻‍♂️","💇🏻","💇🏼‍♀️","💇🏼‍♂️","💇🏼","💇🏽‍♀️","💇🏽‍♂️","💇🏽","💇🏾‍♀️","💇🏾‍♂️","💇🏾","💇🏿‍♀️","💇🏿‍♂️","💇🏿","💇‍♀️","💇‍♂️","💇","💈","💉","💊","💋","💌","💍","💎","💏","💐","💑","💒","💓","💔","💕","💖","💗","💘","💙","💚","💛","💜","💝","💞","💟","💠","💡","💢","💣","💤","💥","💦","💧","💨","💩","💪🏻","💪🏼","💪🏽","💪🏾","💪🏿","💪","💫","💬","💭","💮","💯","💰","💱","💲","💳","💴","💵","💶","💷","💸","💹","💺","💻","💼","💽","💾","💿","📀","📁","📂","📃","📄","📅","📆","📇","📈","📉","📊","📋","📌","📍","📎","📏","📐","📑","📒","📓","📔","📕","📖","📗","📘","📙","📚","📛","📜","📝","📞","📟","📠","📡","📢","📣","📤","📥","📦","📧","📨","📩","📪","📫","📬","📭","📮","📯","📰","📱","📲","📳","📴","📵","📶","📷","📸","📹","📺","📻","📼","📽️","📿","🔀","🔁","🔂","🔃","🔄","🔅","🔆","🔇","🔈","🔉","🔊","🔋","🔌","🔍","🔎","🔏","🔐","🔑","🔒","🔓","🔔","🔕","🔖","🔗","🔘","🔙","🔚","🔛","🔜","🔝","🔞","🔟","🔠","🔡","🔢","🔣","🔤","🔥","🔦","🔧","🔨","🔩","🔪","🔫","🔬","🔭","🔮","🔯","🔰","🔱","🔲","🔳","🔴","🔵","🔶","🔷","🔸","🔹","🔺","🔻","🔼","🔽","🕉️","🕊️","🕋","🕌","🕍","🕎","🕐","🕑","🕒","🕓","🕔","🕕","🕖","🕗","🕘","🕙","🕚","🕛","🕜","🕝","🕞","🕟","🕠","🕡","🕢","🕣","🕤","🕥","🕦","🕧","🕯️","🕰️","🕳️","🕴🏻‍♀️","🕴🏻‍♂️","🕴🏻","🕴🏼‍♀️","🕴🏼‍♂️","🕴🏼","🕴🏽‍♀️","🕴🏽‍♂️","🕴🏽","🕴🏾‍♀️","🕴🏾‍♂️","🕴🏾","🕴🏿‍♀️","🕴🏿‍♂️","🕴🏿","🕴️‍♀️","🕴️‍♂️","🕴️","🕵🏻‍♀️","🕵🏻‍♂️","🕵🏻","🕵🏼‍♀️","🕵🏼‍♂️","🕵🏼","🕵🏽‍♀️","🕵🏽‍♂️","🕵🏽","🕵🏾‍♀️","🕵🏾‍♂️","🕵🏾","🕵🏿‍♀️","🕵🏿‍♂️","🕵🏿","🕵️‍♀️","🕵️‍♂️","🕵️","🕶️","🕷️","🕸️","🕹️","🕺🏻","🕺🏼","🕺🏽","🕺🏾","🕺🏿","🕺","🖇️","🖊️","🖋️","🖌️","🖍️","🖐🏻","🖐🏼","🖐🏽","🖐🏾","🖐🏿","🖐️","🖕🏻","🖕🏼","🖕🏽","🖕🏾","🖕🏿","🖕","🖖🏻","🖖🏼","🖖🏽","🖖🏾","🖖🏿","🖖","🖤","🖥️","🖨️","🖱️","🖲️","🖼️","🗂️","🗃️","🗄️","🗑️","🗒️","🗓️","🗜️","🗝️","🗞️","🗡️","🗣️","🗨️","🗯️","🗳️","🗺️","🗻","🗼","🗽","🗾","🗿","😀","😁","😂","😃","😄","😅","😆","😇","😈","😉","😊","😋","😌","😍","😎","😏","😐","😑","😒","😓","😔","😕","😖","😗","😘","😙","😚","😛","😜","😝","😞","😟","😠","😡","😢","😣","😤","😥","😦","😧","😨","😩","😪","😫","😬","😭","😮","😯","😰","😱","😲","😳","😴","😵","😶","😷","😸","😹","😺","😻","😼","😽","😾","😿","🙀","🙁","🙂","🙃","🙄","🙅🏻‍♀️","🙅🏻‍♂️","🙅🏻","🙅🏼‍♀️","🙅🏼‍♂️","🙅🏼","🙅🏽‍♀️","🙅🏽‍♂️","🙅🏽","🙅🏾‍♀️","🙅🏾‍♂️","🙅🏾","🙅🏿‍♀️","🙅🏿‍♂️","🙅🏿","🙅‍♀️","🙅‍♂️","🙅","🙆🏻‍♀️","🙆🏻‍♂️","🙆🏻","🙆🏼‍♀️","🙆🏼‍♂️","🙆🏼","🙆🏽‍♀️","🙆🏽‍♂️","🙆🏽","🙆🏾‍♀️","🙆🏾‍♂️","🙆🏾","🙆🏿‍♀️","🙆🏿‍♂️","🙆🏿","🙆‍♀️","🙆‍♂️","🙆","🙇🏻‍♀️","🙇🏻‍♂️","🙇🏻","🙇🏼‍♀️","🙇🏼‍♂️","🙇🏼","🙇🏽‍♀️","🙇🏽‍♂️","🙇🏽","🙇🏾‍♀️","🙇🏾‍♂️","🙇🏾","🙇🏿‍♀️","🙇🏿‍♂️","🙇🏿","🙇‍♀️","🙇‍♂️","🙇","🙈","🙉","🙊","🙋🏻‍♀️","🙋🏻‍♂️","🙋🏻","🙋🏼‍♀️","🙋🏼‍♂️","🙋🏼","🙋🏽‍♀️","🙋🏽‍♂️","🙋🏽","🙋🏾‍♀️","🙋🏾‍♂️","🙋🏾","🙋🏿‍♀️","🙋🏿‍♂️","🙋🏿","🙋‍♀️","🙋‍♂️","🙋","🙌🏻","🙌🏼","🙌🏽","🙌🏾","🙌🏿","🙌","🙍🏻‍♀️","🙍🏻‍♂️","🙍🏻","🙍🏼‍♀️","🙍🏼‍♂️","🙍🏼","🙍🏽‍♀️","🙍🏽‍♂️","🙍🏽","🙍🏾‍♀️","🙍🏾‍♂️","🙍🏾","🙍🏿‍♀️","🙍🏿‍♂️","🙍🏿","🙍‍♀️","🙍‍♂️","🙍","🙎🏻‍♀️","🙎🏻‍♂️","🙎🏻","🙎🏼‍♀️","🙎🏼‍♂️","🙎🏼","🙎🏽‍♀️","🙎🏽‍♂️","🙎🏽","🙎🏾‍♀️","🙎🏾‍♂️","🙎🏾","🙎🏿‍♀️","🙎🏿‍♂️","🙎🏿","🙎‍♀️","🙎‍♂️","🙎","🙏🏻","🙏🏼","🙏🏽","🙏🏾","🙏🏿","🙏","🚀","🚁","🚂","🚃","🚄","🚅","🚆","🚇","🚈","🚉","🚊","🚋","🚌","🚍","🚎","🚏","🚐","🚑","🚒","🚓","🚔","🚕","🚖","🚗","🚘","🚙","🚚","🚛","🚜","🚝","🚞","🚟","🚠","🚡","🚢","🚣🏻‍♀️","🚣🏻‍♂️","🚣🏻","🚣🏼‍♀️","🚣🏼‍♂️","🚣🏼","🚣🏽‍♀️","🚣🏽‍♂️","🚣🏽","🚣🏾‍♀️","🚣🏾‍♂️","🚣🏾","🚣🏿‍♀️","🚣🏿‍♂️","🚣🏿","🚣‍♀️","🚣‍♂️","🚣","🚤","🚥","🚦","🚧","🚨","🚩","🚪","🚫","🚬","🚭","🚮","🚯","🚰","🚱","🚲","🚳","🚴🏻‍♀️","🚴🏻‍♂️","🚴🏻","🚴🏼‍♀️","🚴🏼‍♂️","🚴🏼","🚴🏽‍♀️","🚴🏽‍♂️","🚴🏽","🚴🏾‍♀️","🚴🏾‍♂️","🚴🏾","🚴🏿‍♀️","🚴🏿‍♂️","🚴🏿","🚴‍♀️","🚴‍♂️","🚴","🚵🏻‍♀️","🚵🏻‍♂️","🚵🏻","🚵🏼‍♀️","🚵🏼‍♂️","🚵🏼","🚵🏽‍♀️","🚵🏽‍♂️","🚵🏽","🚵🏾‍♀️","🚵🏾‍♂️","🚵🏾","🚵🏿‍♀️","🚵🏿‍♂️","🚵🏿","🚵‍♀️","🚵‍♂️","🚵","🚶🏻‍♀️","🚶🏻‍♂️","🚶🏻","🚶🏼‍♀️","🚶🏼‍♂️","🚶🏼","🚶🏽‍♀️","🚶🏽‍♂️","🚶🏽","🚶🏾‍♀️","🚶🏾‍♂️","🚶🏾","🚶🏿‍♀️","🚶🏿‍♂️","🚶🏿","🚶‍♀️","🚶‍♂️","🚶","🚷","🚸","🚹","🚺","🚻","🚼","🚽","🚾","🚿","🛀🏻","🛀🏼","🛀🏽","🛀🏾","🛀🏿","🛀","🛁","🛂","🛃","🛄","🛅","🛋️","🛌🏻","🛌🏼","🛌🏽","🛌🏾","🛌🏿","🛌","🛍️","🛎️","🛏️","🛐","🛑","🛒","🛕","🛠️","🛡️","🛢️","🛣️","🛤️","🛥️","🛩️","🛫","🛬","🛰️","🛳️","🛴","🛵","🛶","🛷","🛸","🛹","🛺","🟠","🟡","🟢","🟣","🟤","🟥","🟦","🟧","🟨","🟩","🟪","🟫","🤍","🤎","🤏🏻","🤏🏼","🤏🏽","🤏🏾","🤏🏿","🤏","🤐","🤑","🤒","🤓","🤔","🤕","🤖","🤗","🤘🏻","🤘🏼","🤘🏽","🤘🏾","🤘🏿","🤘","🤙🏻","🤙🏼","🤙🏽","🤙🏾","🤙🏿","🤙","🤚🏻","🤚🏼","🤚🏽","🤚🏾","🤚🏿","🤚","🤛🏻","🤛🏼","🤛🏽","🤛🏾","🤛🏿","🤛","🤜🏻","🤜🏼","🤜🏽","🤜🏾","🤜🏿","🤜","🤝","🤞🏻","🤞🏼","🤞🏽","🤞🏾","🤞🏿","🤞","🤟🏻","🤟🏼","🤟🏽","🤟🏾","🤟🏿","🤟","🤠","🤡","🤢","🤣","🤤","🤥","🤦🏻‍♀️","🤦🏻‍♂️","🤦🏻","🤦🏼‍♀️","🤦🏼‍♂️","🤦🏼","🤦🏽‍♀️","🤦🏽‍♂️","🤦🏽","🤦🏾‍♀️","🤦🏾‍♂️","🤦🏾","🤦🏿‍♀️","🤦🏿‍♂️","🤦🏿","🤦‍♀️","🤦‍♂️","🤦","🤧","🤨","🤩","🤪","🤫","🤬","🤭","🤮","🤯","🤰🏻","🤰🏼","🤰🏽","🤰🏾","🤰🏿","🤰","🤱🏻","🤱🏼","🤱🏽","🤱🏾","🤱🏿","🤱","🤲🏻","🤲🏼","🤲🏽","🤲🏾","🤲🏿","🤲","🤳🏻","🤳🏼","🤳🏽","🤳🏾","🤳🏿","🤳","🤴🏻","🤴🏼","🤴🏽","🤴🏾","🤴🏿","🤴","🤵🏻‍♀️","🤵🏻‍♂️","🤵🏻","🤵🏼‍♀️","🤵🏼‍♂️","🤵🏼","🤵🏽‍♀️","🤵🏽‍♂️","🤵🏽","🤵🏾‍♀️","🤵🏾‍♂️","🤵🏾","🤵🏿‍♀️","🤵🏿‍♂️","🤵🏿","🤵‍♀️","🤵‍♂️","🤵","🤶🏻","🤶🏼","🤶🏽","🤶🏾","🤶🏿","🤶","🤷🏻‍♀️","🤷🏻‍♂️","🤷🏻","🤷🏼‍♀️","🤷🏼‍♂️","🤷🏼","🤷🏽‍♀️","🤷🏽‍♂️","🤷🏽","🤷🏾‍♀️","🤷🏾‍♂️","🤷🏾","🤷🏿‍♀️","🤷🏿‍♂️","🤷🏿","🤷‍♀️","🤷‍♂️","🤷","🤸🏻‍♀️","🤸🏻‍♂️","🤸🏻","🤸🏼‍♀️","🤸🏼‍♂️","🤸🏼","🤸🏽‍♀️","🤸🏽‍♂️","🤸🏽","🤸🏾‍♀️","🤸🏾‍♂️","🤸🏾","🤸🏿‍♀️","🤸🏿‍♂️","🤸🏿","🤸‍♀️","🤸‍♂️","🤸","🤹🏻‍♀️","🤹🏻‍♂️","🤹🏻","🤹🏼‍♀️","🤹🏼‍♂️","🤹🏼","🤹🏽‍♀️","🤹🏽‍♂️","🤹🏽","🤹🏾‍♀️","🤹🏾‍♂️","🤹🏾","🤹🏿‍♀️","🤹🏿‍♂️","🤹🏿","🤹‍♀️","🤹‍♂️","🤹","🤺","🤼‍♀️","🤼‍♂️","🤼","🤽🏻‍♀️","🤽🏻‍♂️","🤽🏻","🤽🏼‍♀️","🤽🏼‍♂️","🤽🏼","🤽🏽‍♀️","🤽🏽‍♂️","🤽🏽","🤽🏾‍♀️","🤽🏾‍♂️","🤽🏾","🤽🏿‍♀️","🤽🏿‍♂️","🤽🏿","🤽‍♀️","🤽‍♂️","🤽","🤾🏻‍♀️","🤾🏻‍♂️","🤾🏻","🤾🏼‍♀️","🤾🏼‍♂️","🤾🏼","🤾🏽‍♀️","🤾🏽‍♂️","🤾🏽","🤾🏾‍♀️","🤾🏾‍♂️","🤾🏾","🤾🏿‍♀️","🤾🏿‍♂️","🤾🏿","🤾‍♀️","🤾‍♂️","🤾","🤿","🥀","🥁","🥂","🥃","🥄","🥅","🥇","🥈","🥉","🥊","🥋","🥌","🥍","🥎","🥏","🥐","🥑","🥒","🥓","🥔","🥕","🥖","🥗","🥘","🥙","🥚","🥛","🥜","🥝","🥞","🥟","🥠","🥡","🥢","🥣","🥤","🥥","🥦","🥧","🥨","🥩","🥪","🥫","🥬","🥭","🥮","🥯","🥰","🥱","🥳","🥴","🥵","🥶","🥺","🥻","🥼","🥽","🥾","🥿","🦀","🦁","🦂","🦃","🦄","🦅","🦆","🦇","🦈","🦉","🦊","🦋","🦌","🦍","🦎","🦏","🦐","🦑","🦒","🦓","🦔","🦕","🦖","🦗","🦘","🦙","🦚","🦛","🦜","🦝","🦞","🦟","🦠","🦡","🦢","🦥","🦦","🦧","🦨","🦩","🦪","🦮","🦯","🦰","🦱","🦲","🦳","🦴","🦵🏻","🦵🏼","🦵🏽","🦵🏾","🦵🏿","🦵","🦶🏻","🦶🏼","🦶🏽","🦶🏾","🦶🏿","🦶","🦷","🦸🏻‍♀️","🦸🏻‍♂️","🦸🏻","🦸🏼‍♀️","🦸🏼‍♂️","🦸🏼","🦸🏽‍♀️","🦸🏽‍♂️","🦸🏽","🦸🏾‍♀️","🦸🏾‍♂️","🦸🏾","🦸🏿‍♀️","🦸🏿‍♂️","🦸🏿","🦸‍♀️","🦸‍♂️","🦸","🦹🏻‍♀️","🦹🏻‍♂️","🦹🏻","🦹🏼‍♀️","🦹🏼‍♂️","🦹🏼","🦹🏽‍♀️","🦹🏽‍♂️","🦹🏽","🦹🏾‍♀️","🦹🏾‍♂️","🦹🏾","🦹🏿‍♀️","🦹🏿‍♂️","🦹🏿","🦹‍♀️","🦹‍♂️","🦹","🦺","🦻🏻","🦻🏼","🦻🏽","🦻🏾","🦻🏿","🦻","🦼","🦽","🦾","🦿","🧀","🧁","🧂","🧃","🧄","🧅","🧆","🧇","🧈","🧉","🧊","🧍🏻‍♀️","🧍🏻‍♂️","🧍🏻","🧍🏼‍♀️","🧍🏼‍♂️","🧍🏼","🧍🏽‍♀️","🧍🏽‍♂️","🧍🏽","🧍🏾‍♀️","🧍🏾‍♂️","🧍🏾","🧍🏿‍♀️","🧍🏿‍♂️","🧍🏿","🧍‍♀️","🧍‍♂️","🧍","🧎🏻‍♀️","🧎🏻‍♂️","🧎🏻","🧎🏼‍♀️","🧎🏼‍♂️","🧎🏼","🧎🏽‍♀️","🧎🏽‍♂️","🧎🏽","🧎🏾‍♀️","🧎🏾‍♂️","🧎🏾","🧎🏿‍♀️","🧎🏿‍♂️","🧎🏿","🧎‍♀️","🧎‍♂️","🧎","🧏🏻‍♀️","🧏🏻‍♂️","🧏🏻","🧏🏼‍♀️","🧏🏼‍♂️","🧏🏼","🧏🏽‍♀️","🧏🏽‍♂️","🧏🏽","🧏🏾‍♀️","🧏🏾‍♂️","🧏🏾","🧏🏿‍♀️","🧏🏿‍♂️","🧏🏿","🧏‍♀️","🧏‍♂️","🧏","🧐","🧑🏻‍🤝‍🧑🏻","🧑🏻","🧑🏼‍🤝‍🧑🏻","🧑🏼‍🤝‍🧑🏼","🧑🏼","🧑🏽‍🤝‍🧑🏻","🧑🏽‍🤝‍🧑🏼","🧑🏽‍🤝‍🧑🏽","🧑🏽","🧑🏾‍🤝‍🧑🏻","🧑🏾‍🤝‍🧑🏼","🧑🏾‍🤝‍🧑🏽","🧑🏾‍🤝‍🧑🏾","🧑🏾","🧑🏿‍🤝‍🧑🏻","🧑🏿‍🤝‍🧑🏼","🧑🏿‍🤝‍🧑🏽","🧑🏿‍🤝‍🧑🏾","🧑🏿‍🤝‍🧑🏿","🧑🏿","🧑‍🤝‍🧑","🧑","🧒🏻","🧒🏼","🧒🏽","🧒🏾","🧒🏿","🧒","🧓🏻","🧓🏼","🧓🏽","🧓🏾","🧓🏿","🧓","🧔🏻","🧔🏼","🧔🏽","🧔🏾","🧔🏿","🧔","🧕🏻","🧕🏼","🧕🏽","🧕🏾","🧕🏿","🧕","🧖🏻‍♀️","🧖🏻‍♂️","🧖🏻","🧖🏼‍♀️","🧖🏼‍♂️","🧖🏼","🧖🏽‍♀️","🧖🏽‍♂️","🧖🏽","🧖🏾‍♀️","🧖🏾‍♂️","🧖🏾","🧖🏿‍♀️","🧖🏿‍♂️","🧖🏿","🧖‍♀️","🧖‍♂️","🧖","🧗🏻‍♀️","🧗🏻‍♂️","🧗🏻","🧗🏼‍♀️","🧗🏼‍♂️","🧗🏼","🧗🏽‍♀️","🧗🏽‍♂️","🧗🏽","🧗🏾‍♀️","🧗🏾‍♂️","🧗🏾","🧗🏿‍♀️","🧗🏿‍♂️","🧗🏿","🧗‍♀️","🧗‍♂️","🧗","🧘🏻‍♀️","🧘🏻‍♂️","🧘🏻","🧘🏼‍♀️","🧘🏼‍♂️","🧘🏼","🧘🏽‍♀️","🧘🏽‍♂️","🧘🏽","🧘🏾‍♀️","🧘🏾‍♂️","🧘🏾","🧘🏿‍♀️","🧘🏿‍♂️","🧘🏿","🧘‍♀️","🧘‍♂️","🧘","🧙🏻‍♀️","🧙🏻‍♂️","🧙🏻","🧙🏼‍♀️","🧙🏼‍♂️","🧙🏼","🧙🏽‍♀️","🧙🏽‍♂️","🧙🏽","🧙🏾‍♀️","🧙🏾‍♂️","🧙🏾","🧙🏿‍♀️","🧙🏿‍♂️","🧙🏿","🧙‍♀️","🧙‍♂️","🧙","🧚🏻‍♀️","🧚🏻‍♂️","🧚🏻","🧚🏼‍♀️","🧚🏼‍♂️","🧚🏼","🧚🏽‍♀️","🧚🏽‍♂️","🧚🏽","🧚🏾‍♀️","🧚🏾‍♂️","🧚🏾","🧚🏿‍♀️","🧚🏿‍♂️","🧚🏿","🧚‍♀️","🧚‍♂️","🧚","🧛🏻‍♀️","🧛🏻‍♂️","🧛🏻","🧛🏼‍♀️","🧛🏼‍♂️","🧛🏼","🧛🏽‍♀️","🧛🏽‍♂️","🧛🏽","🧛🏾‍♀️","🧛🏾‍♂️","🧛🏾","🧛🏿‍♀️","🧛🏿‍♂️","🧛🏿","🧛‍♀️","🧛‍♂️","🧛","🧜🏻‍♀️","🧜🏻‍♂️","🧜🏻","🧜🏼‍♀️","🧜🏼‍♂️","🧜🏼","🧜🏽‍♀️","🧜🏽‍♂️","🧜🏽","🧜🏾‍♀️","🧜🏾‍♂️","🧜🏾","🧜🏿‍♀️","🧜🏿‍♂️","🧜🏿","🧜‍♀️","🧜‍♂️","🧜","🧝🏻‍♀️","🧝🏻‍♂️","🧝🏻","🧝🏼‍♀️","🧝🏼‍♂️","🧝🏼","🧝🏽‍♀️","🧝🏽‍♂️","🧝🏽","🧝🏾‍♀️","🧝🏾‍♂️","🧝🏾","🧝🏿‍♀️","🧝🏿‍♂️","🧝🏿","🧝‍♀️","🧝‍♂️","🧝","🧞‍♀️","🧞‍♂️","🧞","🧟‍♀️","🧟‍♂️","🧟","🧠","🧡","🧢","🧣","🧤","🧥","🧦","🧧","🧨","🧩","🧪","🧫","🧬","🧭","🧮","🧯","🧰","🧱","🧲","🧳","🧴","🧵","🧶","🧷","🧸","🧹","🧺","🧻","🧼","🧽","🧾","🧿","🩰","🩱","🩲","🩳","🩸","🩹","🩺","🪀","🪁","🪂","🪐","🪑","🪒","🪓","🪔","🪕","‼️","⁉️","™️","ℹ️","↔️","↕️","↖️","↗️","↘️","↙️","↩️","↪️","#⃣","⌚️","⌛️","⌨️","⏏️","⏩","⏪","⏫","⏬","⏭️","⏮️","⏯️","⏰","⏱️","⏲️","⏳","⏸️","⏹️","⏺️","Ⓜ️","▪️","▫️","▶️","◀️","◻️","◼️","◽️","◾️","☀️","☁️","☂️","☃️","☄️","☎️","☑️","☔️","☕️","☘️","☝🏻","☝🏼","☝🏽","☝🏾","☝🏿","☝️","☠️","☢️","☣️","☦️","☪️","☮️","☯️","☸️","☹️","☺️","♀️","♂️","♈️","♉️","♊️","♋️","♌️","♍️","♎️","♏️","♐️","♑️","♒️","♓️","♟️","♠️","♣️","♥️","♦️","♨️","♻️","♾","♿️","⚒️","⚓️","⚔️","⚕️","⚖️","⚗️","⚙️","⚛️","⚜️","⚠️","⚡️","⚪️","⚫️","⚰️","⚱️","⚽️","⚾️","⛄️","⛅️","⛈️","⛎","⛏️","⛑️","⛓️","⛔️","⛩️","⛪️","⛰️","⛱️","⛲️","⛳️","⛴️","⛵️","⛷🏻","⛷🏼","⛷🏽","⛷🏾","⛷🏿","⛷️","⛸️","⛹🏻‍♀️","⛹🏻‍♂️","⛹🏻","⛹🏼‍♀️","⛹🏼‍♂️","⛹🏼","⛹🏽‍♀️","⛹🏽‍♂️","⛹🏽","⛹🏾‍♀️","⛹🏾‍♂️","⛹🏾","⛹🏿‍♀️","⛹🏿‍♂️","⛹🏿","⛹️‍♀️","⛹️‍♂️","⛹️","⛺️","⛽️","✂️","✅","✈️","✉️","✊🏻","✊🏼","✊🏽","✊🏾","✊🏿","✊","✋🏻","✋🏼","✋🏽","✋🏾","✋🏿","✋","✌🏻","✌🏼","✌🏽","✌🏾","✌🏿","✌️","✍🏻","✍🏼","✍🏽","✍🏾","✍🏿","✍️","✏️","✒️","✔️","✖️","✝️","✡️","✨","✳️","✴️","❄️","❇️","❌","❎","❓","❔","❕","❗️","❣️","❤️","➕","➖","➗","➡️","➰","➿","⤴️","⤵️","*⃣","⬅️","⬆️","⬇️","⬛️","⬜️","⭐️","⭕️","0⃣","〰️","〽️","1⃣","2⃣","㊗️","㊙️","3⃣","4⃣","5⃣","6⃣","7⃣","8⃣","9⃣","©️","®️",""]},980:e=>{"use strict";function getCurrentRequest(e){if(e.currentRequest){return e.currentRequest}const t=e.loaders.slice(e.loaderIndex).map((e=>e.request)).concat([e.resource]);return t.join("!")}e.exports=getCurrentRequest},5:(e,t,r)=>{"use strict";const s={26:"abcdefghijklmnopqrstuvwxyz",32:"123456789abcdefghjkmnpqrstuvwxyz",36:"0123456789abcdefghijklmnopqrstuvwxyz",49:"abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",52:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",58:"**********************************************************",62:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",64:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"};function encodeBufferToBase(e,t){const n=s[t];if(!n){throw new Error("Unknown encoding base"+t)}const i=e.length;const o=r(16);o.RM=o.DP=0;let u=new o(0);for(let t=i-1;t>=0;t--){u=u.times(256).plus(e[t])}let c="";while(u.gt(0)){c=n[u.mod(t)]+c;u=u.div(t)}o.DP=20;o.RM=1;return c}function getHashDigest(e,t,s,n){t=t||"md4";n=n||9999;const i=r(113).createHash(t);i.update(e);if(s==="base26"||s==="base32"||s==="base36"||s==="base49"||s==="base52"||s==="base58"||s==="base62"||s==="base64"){return encodeBufferToBase(i.digest(),s.substr(4)).substr(0,n)}else{return i.digest(s||"hex").substr(0,n)}}e.exports=getHashDigest},252:(e,t,r)=>{"use strict";const s=r(929);function getOptions(e){const t=e.query;if(typeof t==="string"&&t!==""){return s(e.query)}if(!t||typeof t!=="object"){return{}}return t}e.exports=getOptions},298:e=>{"use strict";function getRemainingRequest(e){if(e.remainingRequest){return e.remainingRequest}const t=e.loaders.slice(e.loaderIndex+1).map((e=>e.request)).concat([e.resource]);return t.join("!")}e.exports=getRemainingRequest},518:(e,t,r)=>{"use strict";const s=r(17);const n=r(74);const i=r(5);const o=/[\uD800-\uDFFF]./;const u=n.filter((e=>o.test(e)));const c={};function encodeStringToEmoji(e,t){if(c[e]){return c[e]}t=t||1;const r=[];do{if(!u.length){throw new Error("Ran out of emoji")}const e=Math.floor(Math.random()*u.length);r.push(u[e]);u.splice(e,1)}while(--t>0);const s=r.join("");c[e]=s;return s}function interpolateName(e,t,r){let n;const o=e.resourceQuery&&e.resourceQuery.length>1;if(typeof t==="function"){n=t(e.resourcePath,o?e.resourceQuery:undefined)}else{n=t||"[hash].[ext]"}const u=r.context;const c=r.content;const f=r.regExp;let a="bin";let l="file";let p="";let h="";let g="";if(e.resourcePath){const t=s.parse(e.resourcePath);let r=e.resourcePath;if(t.ext){a=t.ext.substr(1)}if(t.dir){l=t.name;r=t.dir+s.sep}if(typeof u!=="undefined"){p=s.relative(u,r+"_").replace(/\\/g,"/").replace(/\.\.(\/)?/g,"_$1");p=p.substr(0,p.length-1)}else{p=r.replace(/\\/g,"/").replace(/\.\.(\/)?/g,"_$1")}if(p.length===1){p=""}else if(p.length>1){h=s.basename(p)}}if(e.resourceQuery&&e.resourceQuery.length>1){g=e.resourceQuery;const t=g.indexOf("#");if(t>=0){g=g.substr(0,t)}}let d=n;if(c){d=d.replace(/\[(?:([^:\]]+):)?(?:hash|contenthash)(?::([a-z]+\d*))?(?::(\d+))?\]/gi,((e,t,r,s)=>i(c,t,r,parseInt(s,10)))).replace(/\[emoji(?::(\d+))?\]/gi,((e,t)=>encodeStringToEmoji(c,parseInt(t,10))))}d=d.replace(/\[ext\]/gi,(()=>a)).replace(/\[name\]/gi,(()=>l)).replace(/\[path\]/gi,(()=>p)).replace(/\[folder\]/gi,(()=>h)).replace(/\[query\]/gi,(()=>g));if(f&&e.resourcePath){const t=e.resourcePath.match(new RegExp(f));t&&t.forEach(((e,t)=>{d=d.replace(new RegExp("\\["+t+"\\]","ig"),e)}))}if(typeof e.options==="object"&&typeof e.options.customInterpolateName==="function"){d=e.options.customInterpolateName.call(e,d,t,r)}return d}e.exports=interpolateName},598:(e,t,r)=>{"use strict";const s=r(17);function isUrlRequest(e,t){if(/^[a-z][a-z0-9+.-]*:/i.test(e)&&!s.win32.isAbsolute(e)){return false}if(/^\/\//.test(e)){return false}if(/^[{}[\]#*;,'§$%&(=?`´^°<>]/.test(e)){return false}if((t===undefined||t===false)&&/^\//.test(e)){return false}return true}e.exports=isUrlRequest},929:(e,t,r)=>{"use strict";const s=r(310);const n={null:null,true:true,false:false};function parseQuery(e){if(e.substr(0,1)!=="?"){throw new Error("A valid query string passed to parseQuery should begin with '?'")}e=e.substr(1);if(!e){return{}}if(e.substr(0,1)==="{"&&e.substr(-1)==="}"){return s.parse(e)}const t=e.split(/[,&]/g);const r={};t.forEach((e=>{const t=e.indexOf("=");if(t>=0){let s=e.substr(0,t);let i=decodeURIComponent(e.substr(t+1));if(n.hasOwnProperty(i)){i=n[i]}if(s.substr(-2)==="[]"){s=decodeURIComponent(s.substr(0,s.length-2));if(!Array.isArray(r[s])){r[s]=[]}r[s].push(i)}else{s=decodeURIComponent(s);r[s]=i}}else{if(e.substr(0,1)==="-"){r[decodeURIComponent(e.substr(1))]=false}else if(e.substr(0,1)==="+"){r[decodeURIComponent(e.substr(1))]=true}else{r[decodeURIComponent(e)]=true}}}));return r}e.exports=parseQuery},894:e=>{"use strict";function parseString(e){try{if(e[0]==='"'){return JSON.parse(e)}if(e[0]==="'"&&e.substr(e.length-1)==="'"){return parseString(e.replace(/\\.|"/g,(e=>e==='"'?'\\"':e)).replace(/^'|'$/g,'"'))}return JSON.parse('"'+e+'"')}catch(t){return e}}e.exports=parseString},516:(e,t,r)=>{"use strict";const s=r(17);const n=/^\.\.?[/\\]/;function isAbsolutePath(e){return s.posix.isAbsolute(e)||s.win32.isAbsolute(e)}function isRelativePath(e){return n.test(e)}function stringifyRequest(e,t){const r=t.split("!");const n=e.context||e.options&&e.options.context;return JSON.stringify(r.map((e=>{const t=e.match(/^(.*?)(\?.*)/);const r=t?t[2]:"";let i=t?t[1]:e;if(isAbsolutePath(i)&&n){i=s.relative(n,i);if(isAbsolutePath(i)){return i+r}if(isRelativePath(i)===false){i="./"+i}}return i.replace(/\\/g,"/")+r})).join("!"))}e.exports=stringifyRequest},795:e=>{"use strict";const t=/^[A-Z]:[/\\]|^\\\\/i;function urlToRequest(e,r){if(e===""){return""}const s=/^[^?]*~/;let n;if(t.test(e)){n=e}else if(r!==undefined&&r!==false&&/^\//.test(e)){switch(typeof r){case"string":if(s.test(r)){n=r.replace(/([^~/])$/,"$1/")+e.slice(1)}else{n=r+e}break;case"boolean":n=e;break;default:throw new Error("Unexpected parameters to loader-utils 'urlToRequest': url = "+e+", root = "+r+".")}}else if(/^\.\.?\//.test(e)){n=e}else{n="./"+e}if(s.test(n)){n=n.replace(s,"")}return n}e.exports=urlToRequest},113:e=>{"use strict";e.exports=require("crypto")},310:e=>{"use strict";e.exports=require("next/dist/compiled/json5")},17:e=>{"use strict";e.exports=require("path")}};var t={};function __nccwpck_require__(r){var s=t[r];if(s!==undefined){return s.exports}var n=t[r]={exports:{}};var i=true;try{e[r].call(n.exports,n,n.exports,__nccwpck_require__);i=false}finally{if(i)delete t[r]}return n.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r={};(()=>{"use strict";var e=r;const t=__nccwpck_require__(252);const s=__nccwpck_require__(929);const n=__nccwpck_require__(516);const i=__nccwpck_require__(298);const o=__nccwpck_require__(980);const u=__nccwpck_require__(598);const c=__nccwpck_require__(795);const f=__nccwpck_require__(894);const a=__nccwpck_require__(5);const l=__nccwpck_require__(518);e.getOptions=t;e.parseQuery=s;e.stringifyRequest=n;e.getRemainingRequest=i;e.getCurrentRequest=o;e.isUrlRequest=u;e.urlToRequest=c;e.parseString=f;e.getHashDigest=a;e.interpolateName=l})();module.exports=r})();