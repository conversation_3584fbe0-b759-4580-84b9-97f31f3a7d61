import { api } from './api';

/**
 * Récupère les paramètres de template pour un type spécifique
 * @param {string} type - Type de document (facture/devis)
 * @returns {Promise<Object>} Paramètres de template
 */
export const getTemplateSettings = async (type = 'facture') => {
  try {
    // Vérifier que le type est valide
    const validType = ['facture', 'devis'].includes(type) ? type : 'facture';
    console.log(`Récupération des paramètres de template pour: ${validType}`);

    const response = await api.get(`/template-settings?type=${validType}`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la récupération des paramètres de template (${type}):`, error);
    // Si le endpoint n'existe pas encore, retourner des valeurs par défaut
    if (error.response && error.response.status === 404) {
      return {
        color: '#e91e63',
        logo: null,
        type: type
      };
    }
    throw error;
  }
};

/**
 * Met à jour les paramètres de template pour un type spécifique
 * @param {FormData} formData - Données du template (couleur, logo et type)
 * @returns {Promise<Object>} Paramètres mis à jour
 */
export const updateTemplateSettings = async (formData) => {
  try {
    // Récupérer le type depuis formData
    const type = formData.get('type') || 'facture';
    console.log(`Mise à jour des paramètres de template pour: ${type}`);

    const response = await api.post('/template-settings', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    console.log(`Paramètres de template mis à jour pour ${type}:`, response.data);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour des paramètres de template:', error);
    throw error;
  }
};

/**
 * Récupère tous les templates de documents
 * @param {string} type - Optionnel: filtre par type (Facture/Devis)
 * @returns {Promise<Array>} Liste des templates
 */
export const getAllTemplates = async (type = null) => {
  try {
    const url = type ? `/templates?type=${type}` : '/templates';
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des templates:', error);
    throw error;
  }
};

/**
 * Récupère un template spécifique
 * @param {string|number} id - Identifiant du template
 * @returns {Promise<Object>} Template trouvé
 */
export const getTemplate = async (id) => {
  try {
    if (!id) {
      throw new Error('ID de template invalide');
    }

    // Vérifier si l'ID est un ID MongoDB valide (24 caractères hexadécimaux)
    const isValidMongoId = typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id);

    // Si ce n'est pas un ID MongoDB valide et que c'est un nombre, convertir en chaîne
    const formattedId = isValidMongoId ? id : String(id);

    const response = await api.get(`/templates/${formattedId}`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la récupération du template ${id}:`, error);
    throw error;
  }
};

/**
 * Crée un nouveau template
 * @param {Object} template - Données du template à créer
 * @returns {Promise<Object>} Template créé
 */
export const createTemplate = async (template) => {
  try {
    const response = await api.post('/templates', template);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la création du template:', error);
    throw error;
  }
};

/**
 * Met à jour un template existant
 * @param {string|number} id - Identifiant du template
 * @param {Object} template - Données à mettre à jour
 * @returns {Promise<Object>} Template mis à jour
 */
export const updateTemplate = async (id, template) => {
  try {
    // Vérifier que l'ID est valide
    if (!id) {
      throw new Error('ID de template invalide');
    }

    // Vérifier si l'ID est un ID MongoDB valide (24 caractères hexadécimaux)
    const isValidMongoId = typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id);

    // Si ce n'est pas un ID MongoDB valide et que c'est un nombre, convertir en chaîne
    const formattedId = isValidMongoId ? id : String(id);

    console.log(`Mise à jour du template ${formattedId} avec les données:`, template);
    const response = await api.put(`/templates/${formattedId}`, template);
    console.log('Réponse de mise à jour:', response.data);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du template ${id}:`, error);
    // Créer un message d'erreur plus convivial
    const errorMessage = error.customMessage ||
                        (error.response?.data?.error) ||
                        error.message ||
                        'Erreur lors de la mise à jour du template';
    const enhancedError = new Error(errorMessage);
    enhancedError.originalError = error;
    throw enhancedError;
  }
};

/**
 * Supprime un template
 * @param {string|number} id - Identifiant du template
 * @returns {Promise<void>}
 */
export const deleteTemplate = async (id) => {
  try {
    await api.delete(`/templates/${id}`);
  } catch (error) {
    console.error(`Erreur lors de la suppression du template ${id}:`, error);
    throw error;
  }
};

/**
 * Définit un template comme template par défaut
 * @param {string|number} id - Identifiant du template
 * @param {string} type - Type du template (Facture/Devis)
 * @returns {Promise<Object>} Résultat de l'opération
 */
export const setDefaultTemplate = async (id, type) => {
  try {
    const response = await api.post(`/templates/${id}/set-default`, { type });
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la définition du template par défaut ${id}:`, error);
    throw error;
  }
};

/**
 * Récupère le template par défaut pour un type donné
 * @param {string} type - Type de document (Facture/Devis)
 * @returns {Promise<Object>} Template par défaut
 */
export const getDefaultTemplate = async (type) => {
  try {
    // Récupérer tous les templates du type spécifié
    const templates = await getAllTemplates(type);

    // Chercher d'abord un template par défaut du type spécifique
    let defaultTemplate = templates.find(t => t.type === type && t.isDefault);

    // Si aucun template par défaut n'est trouvé, chercher un template de type 'Both'
    if (!defaultTemplate) {
      const bothTemplates = await getAllTemplates('Both');
      defaultTemplate = bothTemplates.find(t => t.isDefault);
    }

    // Si toujours aucun template par défaut, prendre le premier template du type spécifié
    if (!defaultTemplate && templates.length > 0) {
      defaultTemplate = templates[0];
    }

    return defaultTemplate || null;
  } catch (error) {
    console.error(`Erreur lors de la récupération du template par défaut pour ${type}:`, error);
    throw error;
  }
};
