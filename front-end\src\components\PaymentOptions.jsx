import React, { useState } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  Divider,
  Button,
  Alert
} from '@mui/material';
import { Save as SaveIcon } from '@mui/icons-material';

const PaymentOptions = ({ factureId, onSave }) => {
  const [paymentOptions, setPaymentOptions] = useState({
    mode: 'BANK_TRANSFER',
    delai: 30,
    rappelAutomatique: true,
    rappelJours: [7, 3, 1],
    instructions: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setPaymentOptions(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setPaymentOptions(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(paymentOptions);
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Options de paiement
        </Typography>

        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="payment-mode-label">Mode de paiement</InputLabel>
              <Select
                labelId="payment-mode-label"
                name="mode"
                value={paymentOptions.mode}
                onChange={handleChange}
                label="Mode de paiement"
              >
                <MenuItem value="BANK_TRANSFER">Virement bancaire</MenuItem>
                <MenuItem value="CHECK">Chèque</MenuItem>
                <MenuItem value="CASH">Espèces</MenuItem>
              </Select>
            </FormControl>

            <TextField
              name="delai"
              label="Délai de paiement (jours)"
              type="number"
              value={paymentOptions.delai}
              onChange={handleChange}
              fullWidth
              margin="normal"
              InputProps={{ inputProps: { min: 0, step: 1 } }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={paymentOptions.rappelAutomatique}
                  onChange={handleSwitchChange}
                  name="rappelAutomatique"
                  color="primary"
                />
              }
              label="Activer les rappels automatiques"
              sx={{ mt: 2 }}
            />

            {paymentOptions.rappelAutomatique && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Envoyer des rappels avant échéance (jours) :
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {paymentOptions.rappelJours.map((jour, index) => (
                    <TextField
                      key={index}
                      size="small"
                      type="number"
                      value={jour}
                      onChange={(e) => {
                        const newJours = [...paymentOptions.rappelJours];
                        newJours[index] = parseInt(e.target.value);
                        setPaymentOptions(prev => ({
                          ...prev,
                          rappelJours: newJours
                        }));
                      }}
                      InputProps={{ inputProps: { min: 1, step: 1 } }}
                      sx={{ width: 70 }}
                    />
                  ))}
                </Box>
              </Box>
            )}
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              name="instructions"
              label="Instructions de paiement personnalisées"
              multiline
              rows={4}
              value={paymentOptions.instructions}
              onChange={handleChange}
              fullWidth
              margin="normal"
              placeholder="Ex: Merci d'indiquer le numéro de facture dans le libellé du virement."
            />

            <Alert severity="info" sx={{ mt: 2 }}>
              Les informations bancaires (IBAN, BIC) sont automatiquement ajoutées à la facture selon les paramètres de votre entreprise.
            </Alert>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={handleSave}
          >
            Enregistrer les options de paiement
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PaymentOptions;