const mongoose = require('mongoose');

const abonnementSchema = new mongoose.Schema({
  // Relations
  entrepriseId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'entrepriseModel',
    required: true
  },
  entrepriseModel: {
    type: String,
    enum: ['Client', 'ResponsableEntreprise'],
    default: 'ResponsableEntreprise'
  },
  responsableId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'users',
    required: true
  },

  // Période d'abonnement
  dateDebut: {
    type: Date,
    required: true,
    default: Date.now
  },
  dateFin: {
    type: Date,
    required: true
  },
  duree: {
    type: String,
    enum: ['10_MIN', '1_JOUR', '7_JOURS', '3_MOIS', '6_MOIS', '1_AN'],
    required: true
  },

  // Statut
  statut: {
    type: String,
    enum: ['ACTIF', 'SUSPENDU', 'EXPIRE', 'ANNULE'],
    required: true,
    default: 'ACTIF'
  },

  // Fonctionnalités
  fonctionnalitesActivees: {
    nombreUtilisateursMax: { type: Number, default: 10 },
    stockageDocumentsGo: { type: Number, default: 20 },
    modulesActifs: [{ type: String }]
  },

  // Notes
  notes: {
    type: String
  }
}, { timestamps: true });

// Méthode pour vérifier si l'abonnement est actif
abonnementSchema.methods.isActif = function() {
  return this.statut === 'ACTIF' && this.dateFin > new Date();
};

// Méthode pour calculer les jours restants
abonnementSchema.methods.joursRestants = function() {
  const aujourdhui = new Date();
  const fin = new Date(this.dateFin);
  const diffTime = Math.abs(fin - aujourdhui);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// Définir les fonctionnalités par défaut
abonnementSchema.pre('save', function(next) {
  if (this.isNew) {
    this.fonctionnalitesActivees = {
      nombreUtilisateursMax: 10,
      stockageDocumentsGo: 20,
      modulesActifs: ['devis', 'factures', 'clients', 'produits', 'statistiques']
    };
  }
  next();
});

const Abonnement = mongoose.model('Abonnement', abonnementSchema);

module.exports = Abonnement;
