import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  ButtonGroup,
  FormControl,
  Select,
  MenuItem,
  Tab,
  Tabs,
  useTheme,
  Snackbar,
  Alert,
  IconButton,
  Tooltip,
  Avatar,
  Chip,
  Stack,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts';
import {
  People as PeopleIcon,
  Receipt as ReceiptIcon,
  AttachMoney as MoneyIcon,
  Refresh as RefreshIcon,
  CalendarToday as CalendarIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  <PERSON><PERSON><PERSON> as ShowChartIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  Search as SearchIcon,
  FileDownload as FileDownloadIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import analyticsService from '../services/analyticsService';
import SimpleDateFilter from '../components/SimpleDateFilter';
import { exportToCSV } from '../utils/csvExporter';
import PowerBIExport from '../components/PowerBIExport';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { fr } from 'date-fns/locale';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
};

const cardVariants = {
  hidden: { scale: 0.95, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  },
  hover: {
    scale: 1.02,
    boxShadow: "0px 8px 25px rgba(0, 0, 0, 0.1)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

// Données vides par défaut pour les graphiques
const emptyRevenueData = [
  { month: 'Jan', revenue: 0, growth: 0 },
  { month: 'Fév', revenue: 0, growth: 0 },
  { month: 'Mar', revenue: 0, growth: 0 },
  { month: 'Avr', revenue: 0, growth: 0 },
  { month: 'Mai', revenue: 0, growth: 0 },
  { month: 'Juin', revenue: 0, growth: 0 },
  { month: 'Juil', revenue: 0, growth: 0 },
  { month: 'Août', revenue: 0, growth: 0 },
  { month: 'Sep', revenue: 0, growth: 0 },
  { month: 'Oct', revenue: 0, growth: 0 },
  { month: 'Nov', revenue: 0, growth: 0 },
  { month: 'Déc', revenue: 0, growth: 0 },
];

const EntrepriseBIDashboard = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('all-time');
  const [tabValue, setTabValue] = useState(0);
  const [chartType, setChartType] = useState('area');
  const [revenueData, setRevenueData] = useState([
    { month: 'Jan', revenue: 0, growth: 0 },
    { month: 'Fév', revenue: 0, growth: 0 },
    { month: 'Mar', revenue: 0, growth: 0 },
    { month: 'Avr', revenue: 0, growth: 0 },
    { month: 'Mai', revenue: 0, growth: 0 },
    { month: 'Juin', revenue: 0, growth: 0 },
    { month: 'Juil', revenue: 0, growth: 0 },
    { month: 'Août', revenue: 0, growth: 0 },
    { month: 'Sep', revenue: 0, growth: 0 },
    { month: 'Oct', revenue: 0, growth: 0 },
    { month: 'Nov', revenue: 0, growth: 0 },
    { month: 'Déc', revenue: 0, growth: 0 },
  ]);
  const [refreshing, setRefreshing] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  // Les états pour l'exportation ont été supprimés car nous utilisons maintenant le composant PowerBIExport

  // États pour stocker les données du tableau de bord
  const [dashboardData, setDashboardData] = useState({
    revenue: 0,
    clients: { total: 0, new: 0 },
    invoices: { total: 0 },
    quotes: { total: 0 }
  });

  // Les états pour les graphiques circulaires et les factures récentes ont été supprimés

  // État pour la mise en cache des données
  const [dataCache, setDataCache] = useState({});

  // Fonction pour vérifier si les données en cache sont valides
  const isCacheValid = useCallback((cacheKey) => {
    if (!dataCache[cacheKey]) return false;

    const cacheTime = dataCache[cacheKey].timestamp;
    const now = new Date().getTime();
    const cacheValidityPeriod = 5 * 60 * 1000; // 5 minutes en millisecondes

    return (now - cacheTime) < cacheValidityPeriod;
  }, [dataCache]);

  // Function to fetch all dashboard data
  const fetchAllData = useCallback(async () => {
    try {
      console.log('EntrepriseBIDashboard - fetchAllData called with period:', period);
      setRefreshing(true);

      // Vérifier si les données sont en cache
      const cacheKey = `dashboard_${period}`;
      if (isCacheValid(cacheKey)) {
        console.log('EntrepriseBIDashboard - Using cached data');
        const cachedData = dataCache[cacheKey].data;

        setDashboardData(cachedData.dashboardData);
        setRevenueData(cachedData.revenueData);

        setRefreshing(false);
        return;
      }

      // Si les données ne sont pas en cache, les récupérer depuis l'API
      console.log('EntrepriseBIDashboard - Fetching fresh data');

      // Utiliser Promise.all pour exécuter toutes les requêtes en parallèle
      const [
        dashboardResponse,
        revenueResponse
      ] = await Promise.all([
        // Fetch dashboard data
        analyticsService.getDashboardData(period)
          .catch(err => {
            console.error('Error fetching dashboard data:', err);
            return null;
          }),

        // Fetch revenue data
        analyticsService.getRevenueData(period)
          .catch(err => {
            console.error('Error fetching revenue data:', err);
            return null;
          })
      ]);

      console.log('EntrepriseBIDashboard - All data fetched successfully');

      // Mettre à jour les données de revenus avec des valeurs vides si aucune donnée n'est disponible
      setRevenueData(revenueResponse || {
        data: emptyRevenueData
      });

      // Mettre à jour les données du tableau de bord
      if (dashboardResponse) {
        setDashboardData({
          revenue: dashboardResponse.revenue.total || 0,
          clients: {
            total: dashboardResponse.clients.total || 0,
            new: dashboardResponse.clients.new || 0
          },
          invoices: {
            total: dashboardResponse.invoices.total || 0,
            paid: dashboardResponse.invoices.byStatus.paid || 0,
            pending: dashboardResponse.invoices.byStatus.sent || 0
          },
          quotes: {
            total: dashboardResponse.quotes.total || 0,
            accepted: dashboardResponse.quotes.byStatus.accepted || 0
          }
        });
      }

      // Les sections pour les graphiques circulaires et les factures récentes ont été supprimées

      // Si aucune donnée n'est disponible, afficher une notification
      if (!dashboardResponse && !revenueResponse) {
        setNotification({
          open: true,
          message: 'Aucune donnée disponible. La base de données est vide.',
          severity: 'info'
        });
      }

      // Les sections pour les graphiques circulaires ont été supprimées

      const dashboardDataFormatted = dashboardResponse
        ? {
            revenue: dashboardResponse.revenue.total || 0,
            clients: {
              total: dashboardResponse.clients.total || 0,
              new: dashboardResponse.clients.new || 0
            },
            invoices: {
              total: dashboardResponse.invoices.total || 0,
              paid: dashboardResponse.invoices.byStatus.paid || 0,
              pending: dashboardResponse.invoices.byStatus.sent || 0
            },
            quotes: {
              total: dashboardResponse.quotes.total || 0,
              accepted: dashboardResponse.quotes.byStatus.accepted || 0
            }
          }
        : dashboardData;

      const revenueDataFormatted = revenueResponse || { data: revenueData };

      // Mettre à jour le cache
      setDataCache(prevCache => ({
        ...prevCache,
        [cacheKey]: {
          timestamp: new Date().getTime(),
          data: {
            dashboardData: dashboardDataFormatted,
            revenueData: revenueDataFormatted
          }
        }
      }));
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setNotification({
        open: true,
        message: 'Erreur lors de la récupération des données',
        severity: 'error'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [period, dataCache, dashboardData, revenueData, isCacheValid]);

  // Initial data fetch
  useEffect(() => {
    // Utiliser la période actuellement sélectionnée pour le chargement initial
    fetchAllData();
  }, [fetchAllData]);

  // Fonction pour gérer le changement d'onglet
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Fonction pour gérer le changement de type de graphique
  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  // Les fonctions pour gérer le menu ont été supprimées

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAllData().finally(() => {
      setRefreshing(false);
      setNotification({
        open: true,
        message: 'Données actualisées avec succès',
        severity: 'success'
      });
    });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // Les fonctions de gestion du dialogue d'exportation ont été supprimées car nous utilisons maintenant le composant PowerBIExport

  // La fonction d'exportation a été supprimée car nous utilisons maintenant le composant PowerBIExport

  // KPI Card Component
  const KpiCard = ({ title, value, subtitle, icon, color, trend, trendValue }) => {
    const isTrendPositive = trendValue > 0;
    const trendIcon = isTrendPositive ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />;
    const trendColor = isTrendPositive ? 'success' : 'error';

    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
      >
        <Card
          elevation={0}
          sx={{
            height: '100%',
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette[color].light, 0.2)} 0%, ${alpha(theme.palette[color].main, 0.05)} 100%)`,
            border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette[color].main, 0.03)} 100%)`,
              zIndex: 0
            }
          }}
        >
          <CardContent sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  backgroundColor: alpha(theme.palette[color].main, 0.15),
                  color: theme.palette[color].main,
                  mr: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette[color].main, 0.2)}`
                }}
              >
                {icon}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="500" color="text.primary">
                  {title}
                </Typography>
                {trend && (
                  <Chip
                    icon={trendIcon}
                    label={`${Math.abs(trendValue).toFixed(1)}%`}
                    size="small"
                    color={trendColor}
                    sx={{
                      height: 24,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      mt: 0.5
                    }}
                  />
                )}
              </Box>
            </Box>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                color: theme.palette[color].dark,
                textShadow: `0 2px 4px ${alpha(theme.palette[color].main, 0.2)}`
              }}
            >
              {value}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                opacity: 0.8,
                maxWidth: '90%'
              }}
            >
              {subtitle}
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Handle date range change
  const handleDateRangeChange = (rangeId, _) => {
    // Map the range ID to period format
    let newPeriod;
    switch(rangeId) {
      case 'today':
      case 'yesterday':
        newPeriod = 'daily';
        break;
      case 'thisWeek':
      case 'lastWeek':
        newPeriod = 'weekly';
        break;
      case 'thisMonth':
      case 'lastMonth':
      case 'january':
      case 'february':
      case 'march':
      case 'april':
      case 'may':
      case 'june':
      case 'july':
      case 'august':
      case 'september':
      case 'october':
      case 'november':
      case 'december':
        newPeriod = 'monthly';
        break;
      case 'thisQuarter':
      case 'q1':
      case 'q2':
      case 'q3':
      case 'q4':
        newPeriod = 'quarterly';
        break;
      case 'thisYear':
      case 'lastYear':
      case '2023':
      case '2024':
      case '2025':
        newPeriod = 'yearly';
        break;
      case 'allTime':
        newPeriod = 'all-time';
        break;
      case 'custom':
        newPeriod = 'custom';
        break;
      default:
        newPeriod = 'monthly';
    }

    // Mettre à jour la période et déclencher le chargement des données
    setPeriod(newPeriod);

    // Utiliser setTimeout pour s'assurer que la mise à jour de l'état est prise en compte
    setTimeout(() => {
      setLoading(true);
      fetchAllData();
    }, 0);
  };

  // Le dialogue d'exportation a été supprimé car nous utilisons maintenant le composant PowerBIExport





  // Couleurs pour les graphiques circulaires
  const CHART_COLORS = {
    paid: theme.palette.success.main,
    pending: theme.palette.warning.main,
    draft: theme.palette.grey[400],
    canceled: theme.palette.error.main,
    invoices: theme.palette.primary.main,
    quotes: theme.palette.secondary.main
  };

  // Données pour le graphique circulaire des statuts de factures
  const invoiceStatusData = [
    { name: 'Payées', value: dashboardData.invoices.paid || 0, color: CHART_COLORS.paid },
    { name: 'En attente', value: dashboardData.invoices.pending || 0, color: CHART_COLORS.pending },
    { name: 'Brouillon', value: dashboardData.invoices.total - (dashboardData.invoices.paid + dashboardData.invoices.pending) || 0, color: CHART_COLORS.draft }
  ].filter(item => item.value > 0);

  // S'assurer qu'il y a au moins un élément dans le tableau
  if (invoiceStatusData.length === 0) {
    invoiceStatusData.push({ name: 'Aucune facture', value: 1, color: theme.palette.grey[300] });
  }

  // Données pour le graphique circulaire de répartition des documents
  const documentTypeData = [
    { name: 'Factures', value: dashboardData.invoices.total || 0, color: CHART_COLORS.invoices },
    { name: 'Devis', value: dashboardData.quotes.total || 0, color: CHART_COLORS.quotes }
  ].filter(item => item.value > 0);

  // S'assurer qu'il y a au moins un élément dans le tableau
  if (documentTypeData.length === 0) {
    documentTypeData.push({ name: 'Aucun document', value: 1, color: theme.palette.grey[300] });
  }

  // Skip loading state and render the dashboard immediately
  // This removes the loading spinner and message

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 2, sm: 3 },
        background: `linear-gradient(180deg, ${alpha(theme.palette.background.default, 0.6)} 0%, ${theme.palette.background.default} 100%)`,
        minHeight: '100vh'
      }}
    >
      <Container maxWidth="xl">
        {/* Header */}
        <Box
          component={motion.div}
          variants={itemVariants}
          sx={{
            mb: 4,
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', md: 'center' }
          }}
        >
          <Box>
            <Typography
              variant="h3"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}
            >
              Tableau de Bord Entreprise
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
              Analysez les performances globales de votre entreprise
            </Typography>
          </Box>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mt: { xs: 2, md: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            <TextField
              placeholder="Rechercher..."
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                sx: { borderRadius: 2 }
              }}
              sx={{ width: { xs: '100%', sm: 220 } }}
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
                minWidth: 130
              }}
            >
              Actualiser
            </Button>

            <SimpleDateFilter
              onDateRangeChange={handleDateRangeChange}
              onRefresh={handleRefresh}
              initialRange="allTime"
              showRefreshButton={false}
            />

            {/* Le bouton d'exportation a été supprimé car nous utilisons maintenant le composant PowerBIExport */}
          </Stack>
        </Box>

        {/* Dashboard Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* KPI Cards */}
          <Grid
            container
            spacing={3}
            sx={{ mb: 4 }}
            component={motion.div}
            variants={itemVariants}
          >
            <Grid item xs={12} sm={6} md={4}>
              <KpiCard
                title="Chiffre d'affaires"
                value={`${new Intl.NumberFormat('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(dashboardData.revenue)} DT`}
                subtitle={`${dashboardData.invoices.paid || 0} factures payées`}
                icon={<MoneyIcon />}
                color="primary"
                trend={false}
                trendValue={0}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <KpiCard
                title="Clients"
                value={dashboardData.clients.total.toString()}
                subtitle={dashboardData.clients.total <= 1 ? "client" : "clients"}
                icon={<PeopleIcon />}
                color="secondary"
                trend={false}
                trendValue={0}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <KpiCard
                title="Factures et Devis"
                value={(dashboardData.invoices.total + dashboardData.quotes.total).toString()}
                subtitle={`${dashboardData.invoices.total} factures • ${dashboardData.quotes.total} devis`}
                icon={<ReceiptIcon />}
                color="info"
                trend={false}
                trendValue={0}
              />
            </Grid>
          </Grid>

          {/* Main Content */}
          <Grid
            container
            spacing={3}
            component={motion.div}
            variants={itemVariants}
          >
            {/* Left Column - Charts */}
            <Grid item xs={12} lg={8}>
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    mb: 3,
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h5" fontWeight="600">
                        Évolution du chiffre d'affaires
                      </Typography>
                    }
                    action={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ButtonGroup
                          variant="outlined"
                          size="small"
                          aria-label="chart type"
                          sx={{ mr: 2, borderRadius: 2, overflow: 'hidden' }}
                        >
                          <Tooltip title="Graphique en aires">
                            <Button
                              onClick={() => handleChartTypeChange('area')}
                              color={chartType === 'area' ? 'primary' : 'inherit'}
                              sx={{
                                borderRadius: '8px 0 0 8px',
                                backgroundColor: chartType === 'area' ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                              }}
                            >
                              <ShowChartIcon fontSize="small" />
                            </Button>
                          </Tooltip>
                          <Tooltip title="Graphique en barres">
                            <Button
                              onClick={() => handleChartTypeChange('bar')}
                              color={chartType === 'bar' ? 'primary' : 'inherit'}
                              sx={{
                                borderRadius: '0 8px 8px 0',
                                backgroundColor: chartType === 'bar' ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                              }}
                            >
                              <BarChartIcon fontSize="small" />
                            </Button>
                          </Tooltip>
                        </ButtonGroup>

                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          borderRadius: 2,
                          px: 2,
                          py: 0.5
                        }}>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              color: theme.palette.primary.main,
                              fontWeight: 600
                            }}
                          >
                            Revenus
                          </Typography>
                        </Box>
                      </Box>
                    }
                    sx={{
                      px: 3,
                      pt: 3,
                      pb: 2,
                      '& .MuiCardHeader-action': {
                        m: 0,
                        alignSelf: 'center'
                      }
                    }}
                  />
                  <Divider />
                  <CardContent sx={{ height: 400, p: 3 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      {chartType === 'area' ? (
                        <AreaChart data={revenueData}>
                          <defs>
                            <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8} />
                              <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0} />
                            </linearGradient>
                          </defs>
                          <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.2)} vertical={false} />
                          <XAxis
                            dataKey="month"
                            stroke={theme.palette.text.secondary}
                            fontSize={12}
                            tickLine={false}
                            axisLine={{ stroke: theme.palette.divider }}
                            padding={{ left: 10, right: 10 }}
                          />
                          <YAxis
                            stroke={theme.palette.text.secondary}
                            fontSize={12}
                            tickLine={false}
                            axisLine={{ stroke: theme.palette.divider }}
                            tickFormatter={(value) =>
                              `${new Intl.NumberFormat('fr-FR', { notation: 'compact', compactDisplay: 'short' }).format(value)} DT`
                            }
                          />
                          <RechartsTooltip
                            formatter={(value) =>
                              new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'TND' }).format(value)
                            }
                            labelFormatter={(label) => `Période: ${label}`}
                            contentStyle={{
                              backgroundColor: theme.palette.background.paper,
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: 8,
                              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                              padding: '12px 16px'
                            }}
                            cursor={{ stroke: theme.palette.primary.main, strokeWidth: 1, strokeDasharray: '5 5' }}
                          />
                          <Area
                            type="monotone"
                            dataKey="revenue"
                            stroke={theme.palette.primary.main}
                            strokeWidth={3}
                            fillOpacity={1}
                            fill="url(#colorRevenue)"
                            activeDot={{
                              r: 8,
                              fill: theme.palette.primary.main,
                              strokeWidth: 3,
                              stroke: theme.palette.background.paper
                            }}
                            name="Revenus"
                          />
                          <Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <Box
                                    sx={{
                                      backgroundColor: 'white',
                                      p: 2,
                                      border: `1px solid ${theme.palette.divider}`,
                                      borderRadius: 2,
                                      boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                                    }}
                                  >
                                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                      Période: {payload[0].payload.month}
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: theme.palette.primary.main, fontWeight: 'bold' }}>
                                      Revenus : {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'TND' }).format(payload[0].value)}
                                    </Typography>
                                  </Box>
                                );
                              }
                              return null;
                            }}
                          />
                        </AreaChart>
                      ) : (
                        <BarChart data={revenueData}>
                          <defs>
                            <linearGradient id="barRevenue" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor={theme.palette.primary.main} stopOpacity={1} />
                              <stop offset="100%" stopColor={theme.palette.primary.light} stopOpacity={0.8} />
                            </linearGradient>
                          </defs>
                          <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.2)} vertical={false} />
                          <XAxis
                            dataKey="month"
                            stroke={theme.palette.text.secondary}
                            fontSize={12}
                            tickLine={false}
                            axisLine={{ stroke: theme.palette.divider }}
                            padding={{ left: 10, right: 10 }}
                          />
                          <YAxis
                            stroke={theme.palette.text.secondary}
                            fontSize={12}
                            tickLine={false}
                            axisLine={{ stroke: theme.palette.divider }}
                            tickFormatter={(value) =>
                              `${new Intl.NumberFormat('fr-FR', { notation: 'compact', compactDisplay: 'short' }).format(value)} DT`
                            }
                            domain={['auto', 'auto']}
                          />
                          <Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <Box
                                    sx={{
                                      backgroundColor: 'white',
                                      p: 2,
                                      border: `1px solid ${theme.palette.divider}`,
                                      borderRadius: 2,
                                      boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                                    }}
                                  >
                                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                      Période: {payload[0].payload.month}
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: theme.palette.primary.main, fontWeight: 'bold' }}>
                                      Revenus : {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'TND' }).format(payload[0].value)}
                                    </Typography>
                                  </Box>
                                );
                              }
                              return null;
                            }}
                          />
                          <Bar
                            dataKey="revenue"
                            fill="url(#barRevenue)"
                            radius={[6, 6, 0, 0]}
                            name="Revenus"
                            barSize={36}
                          />
                        </BarChart>
                      )}
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Graphiques circulaires */}
              <Grid container spacing={3}>
                {/* Statut des factures */}
                <Grid item xs={12} md={6}>
                  <motion.div variants={cardVariants}>
                    <Card
                      elevation={0}
                      sx={{
                        borderRadius: 3,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                        overflow: 'hidden'
                      }}
                    >
                      <CardHeader
                        title={
                          <Typography variant="h6" fontWeight="600">
                            Statut des factures
                          </Typography>
                        }
                        sx={{ px: 3, pt: 3, pb: 2 }}
                      />
                      <Divider />
                      <CardContent sx={{ height: 300, p: 3 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={invoiceStatusData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={90}
                              paddingAngle={3}
                              dataKey="value"
                              label={false}
                              labelLine={false}
                              animationDuration={1000}
                              animationBegin={200}
                            >
                              {invoiceStatusData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Legend
                              verticalAlign="bottom"
                              height={36}
                              formatter={(value, entry) => (
                                <span style={{ color: theme.palette.text.primary, fontWeight: 500 }}>
                                  {value} ({(entry.payload.value / invoiceStatusData.reduce((sum, item) => sum + item.value, 0) * 100).toFixed(0)}%)
                                </span>
                              )}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                {/* Répartition des documents */}
                <Grid item xs={12} md={6}>
                  <motion.div variants={cardVariants}>
                    <Card
                      elevation={0}
                      sx={{
                        borderRadius: 3,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                        overflow: 'hidden'
                      }}
                    >
                      <CardHeader
                        title={
                          <Typography variant="h6" fontWeight="600">
                            Répartition des documents
                          </Typography>
                        }
                        sx={{ px: 3, pt: 3, pb: 2 }}
                      />
                      <Divider />
                      <CardContent sx={{ height: 300, p: 3 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={documentTypeData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={90}
                              paddingAngle={3}
                              dataKey="value"
                              label={false}
                              labelLine={false}
                              animationDuration={1000}
                              animationBegin={200}
                            >
                              {documentTypeData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Legend
                              verticalAlign="bottom"
                              height={36}
                              formatter={(value, entry) => (
                                <span style={{ color: theme.palette.text.primary, fontWeight: 500 }}>
                                  {value} ({(entry.payload.value / documentTypeData.reduce((sum, item) => sum + item.value, 0) * 100).toFixed(0)}%)
                                </span>
                              )}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>
            </Grid>

            {/* Colonne de droite - Statistiques */}
            <Grid item xs={12} lg={4}>
              {/* Power BI Export Component */}
              <motion.div variants={cardVariants}>
                <Card
                  elevation={0}
                  sx={{
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    overflow: 'hidden',
                    background: `linear-gradient(135deg, ${alpha(theme.palette.info.light, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`
                  }}
                >
                  <CardHeader
                    title={
                      <Typography variant="h6" fontWeight="600">
                        Exporter les données pour Power BI
                      </Typography>
                    }
                    subheader="Exportez vos données au format CSV pour les importer dans Power BI"
                    sx={{ px: 3, pt: 3, pb: 2 }}
                  />
                  <Divider />
                  <CardContent sx={{ p: 3 }}>
                    <PowerBIExport />
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Notifications */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
            {notification.message}
          </Alert>
        </Snackbar>

        {/* Le dialogue d'exportation et le menu pour "Suivi des paiements clients" ont été supprimés */}
      </Container>
    </Box>
  );
}

export default EntrepriseBIDashboard;