import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Avatar,
  Chip,
  Box,
  IconButton,
  Tooltip,
  Grid,
  useTheme,
} from '@mui/material';
import {
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { formatDate } from '../utils/formatters';

const ClientCard = ({ client, onView, onEdit, onDelete }) => {
  const theme = useTheme();

  // Handle null client
  if (!client) {
    return null;
  }

  // Get category styling
  const getCategoryStyle = (category) => {
    switch (category) {
      case 'VIP':
        return { label: 'VIP', color: 'error' };
      case 'Premium':
        return { label: 'Premium', color: 'warning' };
      default:
        return { label: 'Standard', color: 'info' };
    }
  };

  const categoryStyle = getCategoryStyle(client?.categorie);

  return (
    <Card
      elevation={2}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease',
        borderRadius: 2,
        overflow: 'hidden',
        border: '1px solid',
        borderColor: 'divider',
        position: 'relative',
        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: '0 12px 28px rgba(0,0,0,0.15)',
          borderColor: 'primary.light',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          backgroundColor: categoryStyle.color === 'error' ? theme.palette.error.main :
                          categoryStyle.color === 'warning' ? theme.palette.warning.main :
                          theme.palette.info.main,
          transition: 'height 0.3s ease',
        },
        '&:hover::before': {
          height: '6px',
        }
      }}
    >
      <CardContent sx={{ flexGrow: 1, p: 0 }}>
        {/* Header */}
        <Box sx={{
          p: 2,
          pb: 1.5,
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider',
          bgcolor: 'rgba(0,0,0,0.01)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {client?.logo ? (
              <Avatar
                src={client.logo}
                alt={client.nom || ''}
                sx={{
                  width: 48,
                  height: 48,
                  mr: 2,
                  border: '2px solid white',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              />
            ) : (
              <Avatar
                sx={{
                  bgcolor: theme.palette.primary.main,
                  width: 48,
                  height: 48,
                  mr: 2,
                  border: '2px solid white',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                {client?.nom?.charAt(0)?.toUpperCase() || '?'}
              </Avatar>
            )}
            <Box>
              <Typography
                variant="h6"
                component="div"
                noWrap
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.dark',
                  fontSize: '1.1rem'
                }}
              >
                {client?.nom || 'Client sans nom'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                <Chip
                  label={categoryStyle?.label || 'Standard'}
                  color={categoryStyle?.color || 'info'}
                  size="small"
                  sx={{
                    height: 20,
                    fontSize: '0.7rem',
                    fontWeight: 'bold',
                    borderRadius: 1
                  }}
                />
                <Chip
                  label={client?.actif ? 'Actif' : 'Inactif'}
                  color={client?.actif ? 'success' : 'default'}
                  size="small"
                  variant="outlined"
                  sx={{
                    height: 20,
                    fontSize: '0.7rem',
                    borderRadius: 1
                  }}
                />
              </Box>
            </Box>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ p: 2 }}>
          <Grid container spacing={1} sx={{ mb: 2 }}>
            {client?.secteurActivite && (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <BusinessIcon fontSize="small" sx={{ color: theme.palette.primary.light }} />
                  <Typography variant="body2" color="text.secondary" noWrap>
                    {client.secteurActivite}
                  </Typography>
                </Box>
              </Grid>
            )}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <EmailIcon fontSize="small" sx={{ color: theme.palette.primary.light }} />
                <Typography variant="body2" noWrap>{client?.email || 'Non spécifié'}</Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PhoneIcon fontSize="small" sx={{ color: theme.palette.primary.light }} />
                <Typography variant="body2" noWrap>{client?.contact || 'Non spécifié'}</Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Stats */}
          {client?.stats && (
            <Box sx={{
              bgcolor: 'rgba(0,0,0,0.02)',
              p: 1.5,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              mt: 2
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                  <MoneyIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.success.main }} />
                  Total facturé:
                </Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  {client.stats?.montantTotal?.toLocaleString() || '0'} €
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">
                  Factures:
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {client.stats?.totalFactures || 0}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      </CardContent>

      <CardActions sx={{
        justifyContent: 'space-between',
        px: 2,
        py: 1.5,
        borderTop: '1px solid',
        borderColor: 'divider',
        bgcolor: 'rgba(0,0,0,0.01)'
      }}>
        <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
          Client depuis: {formatDate(client?.createdAt) || 'Date inconnue'}
        </Typography>
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="Voir détails" arrow placement="top">
            <IconButton
              size="small"
              onClick={() => onView && onView(client)}
              sx={{
                bgcolor: 'info.light',
                color: 'white',
                '&:hover': {
                  bgcolor: 'info.main',
                  transform: 'translateY(-2px)',
                  boxShadow: 2
                },
                transition: 'all 0.2s ease'
              }}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Modifier" arrow placement="top">
            <IconButton
              size="small"
              onClick={() => onEdit && onEdit(client)}
              sx={{
                bgcolor: 'primary.light',
                color: 'white',
                '&:hover': {
                  bgcolor: 'primary.main',
                  transform: 'translateY(-2px)',
                  boxShadow: 2
                },
                transition: 'all 0.2s ease'
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Supprimer" arrow placement="top">
            <IconButton
              size="small"
              onClick={() => onDelete && onDelete(client)}
              sx={{
                bgcolor: 'error.light',
                color: 'white',
                '&:hover': {
                  bgcolor: 'error.main',
                  transform: 'translateY(-2px)',
                  boxShadow: 2
                },
                transition: 'all 0.2s ease'
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </CardActions>
    </Card>
  );
};

export default ClientCard;
