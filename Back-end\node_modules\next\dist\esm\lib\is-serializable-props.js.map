{"version": 3, "sources": ["../../src/lib/is-serializable-props.ts"], "sourcesContent": ["import {\n  isPlainObject,\n  getObjectClassLabel,\n} from '../shared/lib/is-plain-object'\n\nconst regexpPlainIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport class SerializableError extends Error {\n  constructor(page: string, method: string, path: string, message: string) {\n    super(\n      path\n        ? `Error serializing \\`${path}\\` returned from \\`${method}\\` in \"${page}\".\\nReason: ${message}`\n        : `Error serializing props returned from \\`${method}\\` in \"${page}\".\\nReason: ${message}`\n    )\n  }\n}\n\nexport function isSerializableProps(\n  page: string,\n  method: string,\n  input: any\n): true {\n  if (!isPlainObject(input)) {\n    throw new SerializableError(\n      page,\n      method,\n      '',\n      `Props must be returned as a plain object from ${method}: \\`{ props: { ... } }\\` (received: \\`${getObjectClassLabel(\n        input\n      )}\\`).`\n    )\n  }\n\n  function visit(visited: Map<any, string>, value: any, path: string) {\n    if (visited.has(value)) {\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `Circular references cannot be expressed in JSON (references: \\`${\n          visited.get(value) || '(self)'\n        }\\`).`\n      )\n    }\n\n    visited.set(value, path)\n  }\n\n  function isSerializable(\n    refs: Map<any, string>,\n    value: any,\n    path: string\n  ): true {\n    const type = typeof value\n    if (\n      // `null` can be serialized, but not `undefined`.\n      value === null ||\n      // n.b. `bigint`, `function`, `symbol`, and `undefined` cannot be\n      // serialized.\n      //\n      // `object` is special-cased below, as it may represent `null`, an Array,\n      // a plain object, a class, et al.\n      type === 'boolean' ||\n      type === 'number' ||\n      type === 'string'\n    ) {\n      return true\n    }\n\n    if (type === 'undefined') {\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        '`undefined` cannot be serialized as JSON. Please use `null` or omit this value.'\n      )\n    }\n\n    if (isPlainObject(value)) {\n      visit(refs, value, path)\n\n      if (\n        Object.entries(value).every(([key, nestedValue]) => {\n          const nextPath = regexpPlainIdentifier.test(key)\n            ? `${path}.${key}`\n            : `${path}[${JSON.stringify(key)}]`\n\n          const newRefs = new Map(refs)\n          return (\n            isSerializable(newRefs, key, nextPath) &&\n            isSerializable(newRefs, nestedValue, nextPath)\n          )\n        })\n      ) {\n        return true\n      }\n\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `invariant: Unknown error encountered in Object.`\n      )\n    }\n\n    if (Array.isArray(value)) {\n      visit(refs, value, path)\n\n      if (\n        value.every((nestedValue, index) => {\n          const newRefs = new Map(refs)\n          return isSerializable(newRefs, nestedValue, `${path}[${index}]`)\n        })\n      ) {\n        return true\n      }\n\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `invariant: Unknown error encountered in Array.`\n      )\n    }\n\n    // None of these can be expressed as JSON:\n    // const type: \"bigint\" | \"symbol\" | \"object\" | \"function\"\n    throw new SerializableError(\n      page,\n      method,\n      path,\n      '`' +\n        type +\n        '`' +\n        (type === 'object'\n          ? ` (\"${Object.prototype.toString.call(value)}\")`\n          : '') +\n        ' cannot be serialized as JSON. Please only return JSON serializable data types.'\n    )\n  }\n\n  return isSerializable(new Map(), input, '')\n}\n"], "names": ["isPlainObject", "getObjectClassLabel", "regexpPlainIdentifier", "SerializableError", "Error", "constructor", "page", "method", "path", "message", "isSerializableProps", "input", "visit", "visited", "value", "has", "get", "set", "isSerializable", "refs", "type", "Object", "entries", "every", "key", "nestedV<PERSON>ue", "nextPath", "test", "JSON", "stringify", "newRefs", "Map", "Array", "isArray", "index", "prototype", "toString", "call"], "mappings": "AAAA,SACEA,aAAa,EACbC,mBAAmB,QACd,gCAA+B;AAEtC,MAAMC,wBAAwB;AAE9B,OAAO,MAAMC,0BAA0BC;IACrCC,YAAYC,IAAY,EAAEC,MAAc,EAAEC,IAAY,EAAEC,OAAe,CAAE;QACvE,KAAK,CACHD,OACI,CAAC,oBAAoB,EAAEA,KAAK,mBAAmB,EAAED,OAAO,OAAO,EAAED,KAAK,YAAY,EAAEG,SAAS,GAC7F,CAAC,wCAAwC,EAAEF,OAAO,OAAO,EAAED,KAAK,YAAY,EAAEG,SAAS;IAE/F;AACF;AAEA,OAAO,SAASC,oBACdJ,IAAY,EACZC,MAAc,EACdI,KAAU;IAEV,IAAI,CAACX,cAAcW,QAAQ;QACzB,MAAM,qBAOL,CAPK,IAAIR,kBACRG,MACAC,QACA,IACA,CAAC,8CAA8C,EAAEA,OAAO,sCAAsC,EAAEN,oBAC9FU,OACA,IAAI,CAAC,GANH,qBAAA;mBAAA;wBAAA;0BAAA;QAON;IACF;IAEA,SAASC,MAAMC,OAAyB,EAAEC,KAAU,EAAEN,IAAY;QAChE,IAAIK,QAAQE,GAAG,CAACD,QAAQ;YACtB,MAAM,qBAOL,CAPK,IAAIX,kBACRG,MACAC,QACAC,MACA,CAAC,+DAA+D,EAC9DK,QAAQG,GAAG,CAACF,UAAU,SACvB,IAAI,CAAC,GANF,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QAEAD,QAAQI,GAAG,CAACH,OAAON;IACrB;IAEA,SAASU,eACPC,IAAsB,EACtBL,KAAU,EACVN,IAAY;QAEZ,MAAMY,OAAO,OAAON;QACpB,IACE,iDAAiD;QACjDA,UAAU,QACV,iEAAiE;QACjE,cAAc;QACd,EAAE;QACF,yEAAyE;QACzE,kCAAkC;QAClCM,SAAS,aACTA,SAAS,YACTA,SAAS,UACT;YACA,OAAO;QACT;QAEA,IAAIA,SAAS,aAAa;YACxB,MAAM,qBAKL,CALK,IAAIjB,kBACRG,MACAC,QACAC,MACA,oFAJI,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,IAAIR,cAAcc,QAAQ;YACxBF,MAAMO,MAAML,OAAON;YAEnB,IACEa,OAAOC,OAAO,CAACR,OAAOS,KAAK,CAAC,CAAC,CAACC,KAAKC,YAAY;gBAC7C,MAAMC,WAAWxB,sBAAsByB,IAAI,CAACH,OACxC,GAAGhB,KAAK,CAAC,EAAEgB,KAAK,GAChB,GAAGhB,KAAK,CAAC,EAAEoB,KAAKC,SAAS,CAACL,KAAK,CAAC,CAAC;gBAErC,MAAMM,UAAU,IAAIC,IAAIZ;gBACxB,OACED,eAAeY,SAASN,KAAKE,aAC7BR,eAAeY,SAASL,aAAaC;YAEzC,IACA;gBACA,OAAO;YACT;YAEA,MAAM,qBAKL,CALK,IAAIvB,kBACRG,MACAC,QACAC,MACA,CAAC,+CAA+C,CAAC,GAJ7C,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,IAAIwB,MAAMC,OAAO,CAACnB,QAAQ;YACxBF,MAAMO,MAAML,OAAON;YAEnB,IACEM,MAAMS,KAAK,CAAC,CAACE,aAAaS;gBACxB,MAAMJ,UAAU,IAAIC,IAAIZ;gBACxB,OAAOD,eAAeY,SAASL,aAAa,GAAGjB,KAAK,CAAC,EAAE0B,MAAM,CAAC,CAAC;YACjE,IACA;gBACA,OAAO;YACT;YAEA,MAAM,qBAKL,CALK,IAAI/B,kBACRG,MACAC,QACAC,MACA,CAAC,8CAA8C,CAAC,GAJ5C,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,0CAA0C;QAC1C,0DAA0D;QAC1D,MAAM,qBAWL,CAXK,IAAIL,kBACRG,MACAC,QACAC,MACA,MACEY,OACA,MACCA,CAAAA,SAAS,WACN,CAAC,GAAG,EAAEC,OAAOc,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACvB,OAAO,EAAE,CAAC,GAC/C,EAAC,IACL,oFAVE,qBAAA;mBAAA;wBAAA;0BAAA;QAWN;IACF;IAEA,OAAOI,eAAe,IAAIa,OAAOpB,OAAO;AAC1C"}