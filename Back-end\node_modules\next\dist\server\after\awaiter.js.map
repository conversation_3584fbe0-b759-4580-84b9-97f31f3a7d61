{"version": 3, "sources": ["../../../src/server/after/awaiter.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\n\n/**\n * Provides a `waitUntil` implementation which gathers promises to be awaited later (via {@link AwaiterMulti.awaiting}).\n * Unlike a simple `Promise.all`, {@link AwaiterMulti} works recursively --\n * if a promise passed to {@link AwaiterMulti.waitUntil} calls `waitUntil` again,\n * that second promise will also be awaited.\n */\nexport class AwaiterMulti {\n  private promises: Set<Promise<unknown>> = new Set()\n  private onError: (error: unknown) => void\n\n  constructor({ onError }: { onError?: (error: unknown) => void } = {}) {\n    this.onError = onError ?? console.error\n  }\n\n  public waitUntil = (promise: Promise<unknown>): void => {\n    // if a promise settles before we await it, we should drop it --\n    // storing them indefinitely could result in a memory leak.\n    const cleanup = () => {\n      this.promises.delete(promise)\n    }\n\n    promise.then(cleanup, (err) => {\n      cleanup()\n      this.onError(err)\n    })\n\n    this.promises.add(promise)\n  }\n\n  public async awaiting(): Promise<void> {\n    while (this.promises.size > 0) {\n      const promises = Array.from(this.promises)\n      this.promises.clear()\n      await Promise.allSettled(promises)\n    }\n  }\n}\n\n/**\n * Like {@link AwaiterMulti}, but can only be awaited once.\n * If {@link AwaiterOnce.waitUntil} is called after that, it will throw.\n */\nexport class AwaiterOnce {\n  private awaiter: AwaiterMulti\n  private done: boolean = false\n  private pending: Promise<void> | undefined\n\n  constructor(options: { onError?: (error: unknown) => void } = {}) {\n    this.awaiter = new AwaiterMulti(options)\n  }\n\n  public waitUntil = (promise: Promise<unknown>): void => {\n    if (this.done) {\n      throw new InvariantError(\n        'Cannot call waitUntil() on an AwaiterOnce that was already awaited'\n      )\n    }\n    return this.awaiter.waitUntil(promise)\n  }\n\n  public async awaiting(): Promise<void> {\n    if (!this.pending) {\n      this.pending = this.awaiter.awaiting().finally(() => {\n        this.done = true\n      })\n    }\n    return this.pending\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Awaiter<PERSON>nce", "constructor", "onError", "promises", "Set", "waitUntil", "promise", "cleanup", "delete", "then", "err", "add", "console", "error", "awaiting", "size", "Array", "from", "clear", "Promise", "allSettled", "options", "done", "InvariantError", "awaiter", "pending", "finally"], "mappings": ";;;;;;;;;;;;;;;IAQaA,YAAY;eAAZA;;IAoCAC,WAAW;eAAXA;;;gCA5CkB;AAQxB,MAAMD;IAIXE,YAAY,EAAEC,OAAO,EAA0C,GAAG,CAAC,CAAC,CAAE;aAH9DC,WAAkC,IAAIC;aAOvCC,YAAY,CAACC;YAClB,gEAAgE;YAChE,2DAA2D;YAC3D,MAAMC,UAAU;gBACd,IAAI,CAACJ,QAAQ,CAACK,MAAM,CAACF;YACvB;YAEAA,QAAQG,IAAI,CAACF,SAAS,CAACG;gBACrBH;gBACA,IAAI,CAACL,OAAO,CAACQ;YACf;YAEA,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAACL;QACpB;QAhBE,IAAI,CAACJ,OAAO,GAAGA,WAAWU,QAAQC,KAAK;IACzC;IAiBA,MAAaC,WAA0B;QACrC,MAAO,IAAI,CAACX,QAAQ,CAACY,IAAI,GAAG,EAAG;YAC7B,MAAMZ,WAAWa,MAAMC,IAAI,CAAC,IAAI,CAACd,QAAQ;YACzC,IAAI,CAACA,QAAQ,CAACe,KAAK;YACnB,MAAMC,QAAQC,UAAU,CAACjB;QAC3B;IACF;AACF;AAMO,MAAMH;IAKXC,YAAYoB,UAAkD,CAAC,CAAC,CAAE;aAH1DC,OAAgB;aAOjBjB,YAAY,CAACC;YAClB,IAAI,IAAI,CAACgB,IAAI,EAAE;gBACb,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,uEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,OAAO,IAAI,CAACC,OAAO,CAACnB,SAAS,CAACC;QAChC;QAVE,IAAI,CAACkB,OAAO,GAAG,IAAIzB,aAAasB;IAClC;IAWA,MAAaP,WAA0B;QACrC,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE;YACjB,IAAI,CAACA,OAAO,GAAG,IAAI,CAACD,OAAO,CAACV,QAAQ,GAAGY,OAAO,CAAC;gBAC7C,IAAI,CAACJ,IAAI,GAAG;YACd;QACF;QACA,OAAO,IAAI,CAACG,OAAO;IACrB;AACF"}