@startuml Global_Sequence_Diagram
title Global Business Process - Invoice and Quote Management System

actor Client
actor "Responsable d'Entreprise" as Responsable
participant "Livreur" as Livreur
actor Vendeur
actor Admin
participant "Authentication System" as Auth
participant "Document Management" as DocMgmt
participant "Payment System" as Payment
participant "Email Service" as Email
participant Database

== System Initialization ==
Admin -> Auth: authenticate()
Auth -> Database: validate_credentials()
Database -> Auth: return_admin_session()
Auth -> Admin: login_success()

Admin -> DocMgmt: configure_templates()
DocMgmt -> Database: save_templates()

Admin -> Database: manage_subscriptions()
Admin -> Database: manage_enterprise_accounts()

== Enterprise Setup ==
Responsable -> Auth: register_enterprise()
Auth -> Database: create_enterprise_account()
Database -> Auth: return_enterprise_id()
Auth -> Responsable: registration_success()

Responsable -> Auth: authenticate()
Auth -> Database: validate_enterprise_credentials()
Database -> Auth: return_responsable_session()
Auth -> Responsable: login_success()

Responsable -> DocMgmt: configure_enterprise_settings()
DocMgmt -> Database: save_enterprise_config()

Responsable -> Database: add_vendeur_accounts()

== Vendeur Operations ==
Vendeur -> Auth: authenticate()
Auth -> Database: validate_vendeur_credentials()
Database -> Auth: return_vendeur_session()
Auth -> Vendeur: login_success()

Vendeur -> DocMgmt: manage_clients()
DocMgmt -> Database: save_client_data()

Vendeur -> DocMgmt: manage_products()
DocMgmt -> Database: save_product_data()

== Quote Management ==
Vendeur -> DocMgmt: create_quote()
DocMgmt -> Database: save_quote(status: "Brouillon")
Database -> DocMgmt: return_quote_id()

Vendeur -> DocMgmt: send_quote()
DocMgmt -> Database: update_quote_status("Envoye")
DocMgmt -> Email: send_quote_email()
Email -> Client: deliver_quote()

Client -> Auth: authenticate()
Auth -> Database: validate_client_credentials()
Database -> Auth: return_client_session()
Auth -> Client: login_success()

Client -> DocMgmt: view_quote()
DocMgmt -> Database: retrieve_quote()
Database -> DocMgmt: return_quote_data()
DocMgmt -> Client: display_quote()

Client -> DocMgmt: accept_quote()
DocMgmt -> Database: update_quote_status("Accepte")
DocMgmt -> Vendeur: notify_quote_accepted()

== Invoice Creation ==
Vendeur -> DocMgmt: convert_quote_to_invoice()
DocMgmt -> Database: create_invoice_from_quote()
Database -> DocMgmt: return_invoice_id()

Vendeur -> DocMgmt: send_invoice()
DocMgmt -> Database: update_invoice_status("Envoye")
DocMgmt -> Email: send_invoice_email()
Email -> Client: deliver_invoice()

== Delivery Management ==
Vendeur -> DocMgmt: create_delivery_note()
DocMgmt -> Database: save_delivery_note()
DocMgmt -> Livreur: assign_delivery()

note over Livreur: Livreur handles physical delivery

Livreur -> DocMgmt: confirm_delivery()
DocMgmt -> Database: update_delivery_status("Livre")
DocMgmt -> Vendeur: notify_delivery_completed()

== Payment Processing ==
Client -> DocMgmt: view_invoice()
DocMgmt -> Database: retrieve_invoice()
Database -> DocMgmt: return_invoice_data()
DocMgmt -> Client: display_invoice()

Client -> Payment: initiate_payment()
Payment -> Database: process_payment()

alt Bank Transfer
    Payment -> Client: provide_bank_details()
    Client -> Payment: confirm_bank_transfer()
else Check Payment
    Payment -> Client: provide_check_instructions()
    Client -> Payment: confirm_check_payment()
else Cash Payment
    Payment -> Client: confirm_cash_payment()
end

Payment -> Database: update_payment_status()
Payment -> DocMgmt: notify_payment_completed()
DocMgmt -> Vendeur: notify_payment_received()
DocMgmt -> Email: send_payment_confirmation()
Email -> Client: deliver_confirmation()

== Document Operations ==
Vendeur -> DocMgmt: generate_pdf()
DocMgmt -> Database: retrieve_document_data()
Database -> DocMgmt: return_formatted_data()
DocMgmt -> Vendeur: return_pdf_document()

Vendeur -> DocMgmt: print_document()
DocMgmt -> Vendeur: send_to_printer()

== System Monitoring ==
Admin -> Database: monitor_subscriptions()
Database -> Admin: return_subscription_status()

Admin -> Auth: manage_user_accounts()
Auth -> Database: update_user_permissions()

Admin -> DocMgmt: view_system_analytics()
DocMgmt -> Database: generate_reports()
Database -> DocMgmt: return_analytics_data()
DocMgmt -> Admin: display_dashboard()

@enduml
