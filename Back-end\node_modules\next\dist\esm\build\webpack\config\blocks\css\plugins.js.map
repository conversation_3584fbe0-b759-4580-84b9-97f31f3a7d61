{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/plugins.ts"], "sourcesContent": ["import { bold, red, underline, yellow } from '../../../../../lib/picocolors'\nimport { findConfig } from '../../../../../lib/find-config'\n\ntype CssPluginCollection_Array = (string | [string, boolean | object])[]\n\ntype CssPluginCollection_Object = { [key: string]: object | boolean }\n\ntype CssPluginCollection =\n  | CssPluginCollection_Array\n  | CssPluginCollection_Object\n\ntype CssPluginShape = [string, object | boolean | string]\n\nconst genericErrorText = 'Malformed PostCSS Configuration'\n\nfunction getError_NullConfig(pluginName: string) {\n  return `${red(\n    bold('Error')\n  )}: Your PostCSS configuration for '${pluginName}' cannot have ${bold(\n    'null'\n  )} configuration.\\nTo disable '${pluginName}', pass ${bold(\n    'false'\n  )}, otherwise, pass ${bold('true')} or a configuration object.`\n}\n\nfunction isIgnoredPlugin(pluginPath: string): boolean {\n  const ignoredRegex =\n    /(?:^|[\\\\/])(postcss-modules-values|postcss-modules-scope|postcss-modules-extract-imports|postcss-modules-local-by-default|postcss-modules)(?:[\\\\/]|$)/i\n  const match = ignoredRegex.exec(pluginPath)\n  if (match == null) {\n    return false\n  }\n\n  const plugin = match.pop()!\n  console.warn(\n    `${yellow(bold('Warning'))}: Please remove the ${underline(\n      plugin\n    )} plugin from your PostCSS configuration. ` +\n      `This plugin is automatically configured by Next.js.\\n` +\n      'Read more: https://nextjs.org/docs/messages/postcss-ignored-plugin'\n  )\n  return true\n}\n\nconst createLazyPostCssPlugin = (\n  fn: () => import('postcss').AcceptedPlugin\n): import('postcss').AcceptedPlugin => {\n  let result: any = undefined\n  const plugin = (...args: any[]) => {\n    if (result === undefined) result = fn() as any\n    if (result.postcss === true) {\n      return result(...args)\n    } else if (result.postcss) {\n      return result.postcss\n    }\n    return result\n  }\n  plugin.postcss = true\n  return plugin\n}\n\nasync function loadPlugin(\n  dir: string,\n  pluginName: string,\n  options: boolean | object | string\n): Promise<import('postcss').AcceptedPlugin | false> {\n  if (options === false || isIgnoredPlugin(pluginName)) {\n    return false\n  }\n\n  if (options == null) {\n    console.error(getError_NullConfig(pluginName))\n    throw new Error(genericErrorText)\n  }\n\n  const pluginPath = require.resolve(pluginName, { paths: [dir] })\n  if (isIgnoredPlugin(pluginPath)) {\n    return false\n  } else if (options === true) {\n    return createLazyPostCssPlugin(() => require(pluginPath))\n  } else {\n    if (typeof options === 'object' && Object.keys(options).length === 0) {\n      return createLazyPostCssPlugin(() => require(pluginPath))\n    }\n    return createLazyPostCssPlugin(() => require(pluginPath)(options))\n  }\n}\n\nfunction getDefaultPlugins(\n  supportedBrowsers: string[] | undefined,\n  disablePostcssPresetEnv: boolean\n): any[] {\n  return [\n    require.resolve('next/dist/compiled/postcss-flexbugs-fixes'),\n    disablePostcssPresetEnv\n      ? false\n      : [\n          require.resolve('next/dist/compiled/postcss-preset-env'),\n          {\n            browsers: supportedBrowsers ?? ['defaults'],\n            autoprefixer: {\n              // Disable legacy flexbox support\n              flexbox: 'no-2009',\n            },\n            // Enable CSS features that have shipped to the\n            // web platform, i.e. in 2+ browsers unflagged.\n            stage: 3,\n            features: {\n              'custom-properties': false,\n            },\n          },\n        ],\n  ].filter(Boolean)\n}\n\nexport async function getPostCssPlugins(\n  dir: string,\n  supportedBrowsers: string[] | undefined,\n  disablePostcssPresetEnv: boolean = false,\n  useLightningcss: boolean = false\n): Promise<import('postcss').AcceptedPlugin[]> {\n  let config = await findConfig<{ plugins: CssPluginCollection }>(\n    dir,\n    'postcss'\n  )\n\n  if (config == null) {\n    config = {\n      plugins: useLightningcss\n        ? []\n        : getDefaultPlugins(supportedBrowsers, disablePostcssPresetEnv),\n    }\n  }\n\n  if (typeof config === 'function') {\n    throw new Error(\n      `Your custom PostCSS configuration may not export a function. Please export a plain object instead.\\n` +\n        'Read more: https://nextjs.org/docs/messages/postcss-function'\n    )\n  }\n\n  // Warn user about configuration keys which are not respected\n  const invalidKey = Object.keys(config).find((key) => key !== 'plugins')\n  if (invalidKey) {\n    console.warn(\n      `${yellow(\n        bold('Warning')\n      )}: Your PostCSS configuration defines a field which is not supported (\\`${invalidKey}\\`). ` +\n        `Please remove this configuration value.`\n    )\n  }\n\n  // Enforce the user provided plugins if the configuration file is present\n  let plugins = config.plugins\n  if (plugins == null || typeof plugins !== 'object') {\n    throw new Error(\n      `Your custom PostCSS configuration must export a \\`plugins\\` key.`\n    )\n  }\n\n  if (!Array.isArray(plugins)) {\n    // Capture variable so TypeScript is happy\n    const pc = plugins\n\n    plugins = Object.keys(plugins).reduce((acc, curr) => {\n      const p = pc[curr]\n      if (typeof p === 'undefined') {\n        console.error(getError_NullConfig(curr))\n        throw new Error(genericErrorText)\n      }\n\n      acc.push([curr, p])\n      return acc\n    }, [] as CssPluginCollection_Array)\n  }\n\n  const parsed: CssPluginShape[] = []\n  plugins.forEach((plugin) => {\n    if (plugin == null) {\n      console.warn(\n        `${yellow(bold('Warning'))}: A ${bold(\n          'null'\n        )} PostCSS plugin was provided. This entry will be ignored.`\n      )\n    } else if (typeof plugin === 'string') {\n      parsed.push([plugin, true])\n    } else if (Array.isArray(plugin)) {\n      const pluginName = plugin[0]\n      const pluginConfig = plugin[1]\n      if (\n        typeof pluginName === 'string' &&\n        (typeof pluginConfig === 'boolean' ||\n          typeof pluginConfig === 'object' ||\n          typeof pluginConfig === 'string')\n      ) {\n        parsed.push([pluginName, pluginConfig])\n      } else {\n        if (typeof pluginName !== 'string') {\n          console.error(\n            `${red(\n              bold('Error')\n            )}: A PostCSS Plugin must be provided as a ${bold(\n              'string'\n            )}. Instead, we got: '${pluginName}'.\\n` +\n              'Read more: https://nextjs.org/docs/messages/postcss-shape'\n          )\n        } else {\n          console.error(\n            `${red(\n              bold('Error')\n            )}: A PostCSS Plugin was passed as an array but did not provide its configuration ('${pluginName}').\\n` +\n              'Read more: https://nextjs.org/docs/messages/postcss-shape'\n          )\n        }\n        throw new Error(genericErrorText)\n      }\n    } else if (typeof plugin === 'function') {\n      console.error(\n        `${red(\n          bold('Error')\n        )}: A PostCSS Plugin was passed as a function using require(), but it must be provided as a ${bold(\n          'string'\n        )}.\\nRead more: https://nextjs.org/docs/messages/postcss-shape`\n      )\n      throw new Error(genericErrorText)\n    } else {\n      console.error(\n        `${red(\n          bold('Error')\n        )}: An unknown PostCSS plugin was provided (${plugin}).\\n` +\n          'Read more: https://nextjs.org/docs/messages/postcss-shape'\n      )\n      throw new Error(genericErrorText)\n    }\n  })\n\n  const resolved = await Promise.all(\n    parsed.map((p) => loadPlugin(dir, p[0], p[1]))\n  )\n  const filtered: import('postcss').AcceptedPlugin[] = resolved.filter(\n    Boolean\n  ) as import('postcss').AcceptedPlugin[]\n\n  return filtered\n}\n"], "names": ["bold", "red", "underline", "yellow", "findConfig", "genericErrorText", "getError_NullConfig", "pluginName", "isIgnoredPlugin", "pluginPath", "ignoredRegex", "match", "exec", "plugin", "pop", "console", "warn", "createLazyPostCssPlugin", "fn", "result", "undefined", "args", "postcss", "loadPlugin", "dir", "options", "error", "Error", "require", "resolve", "paths", "Object", "keys", "length", "getDefaultPlugins", "supportedBrowsers", "disablePostcssPresetEnv", "browsers", "autoprefixer", "flexbox", "stage", "features", "filter", "Boolean", "getPostCssPlugins", "useLightningcss", "config", "plugins", "<PERSON><PERSON><PERSON>", "find", "key", "Array", "isArray", "pc", "reduce", "acc", "curr", "p", "push", "parsed", "for<PERSON>ach", "pluginConfig", "resolved", "Promise", "all", "map", "filtered"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gCAA+B;AAC5E,SAASC,UAAU,QAAQ,iCAAgC;AAY3D,MAAMC,mBAAmB;AAEzB,SAASC,oBAAoBC,UAAkB;IAC7C,OAAO,GAAGN,IACRD,KAAK,UACL,kCAAkC,EAAEO,WAAW,cAAc,EAAEP,KAC/D,QACA,6BAA6B,EAAEO,WAAW,QAAQ,EAAEP,KACpD,SACA,kBAAkB,EAAEA,KAAK,QAAQ,2BAA2B,CAAC;AACjE;AAEA,SAASQ,gBAAgBC,UAAkB;IACzC,MAAMC,eACJ;IACF,MAAMC,QAAQD,aAAaE,IAAI,CAACH;IAChC,IAAIE,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,MAAME,SAASF,MAAMG,GAAG;IACxBC,QAAQC,IAAI,CACV,GAAGb,OAAOH,KAAK,YAAY,oBAAoB,EAAEE,UAC/CW,QACA,yCAAyC,CAAC,GAC1C,CAAC,qDAAqD,CAAC,GACvD;IAEJ,OAAO;AACT;AAEA,MAAMI,0BAA0B,CAC9BC;IAEA,IAAIC,SAAcC;IAClB,MAAMP,SAAS,CAAC,GAAGQ;QACjB,IAAIF,WAAWC,WAAWD,SAASD;QACnC,IAAIC,OAAOG,OAAO,KAAK,MAAM;YAC3B,OAAOH,UAAUE;QACnB,OAAO,IAAIF,OAAOG,OAAO,EAAE;YACzB,OAAOH,OAAOG,OAAO;QACvB;QACA,OAAOH;IACT;IACAN,OAAOS,OAAO,GAAG;IACjB,OAAOT;AACT;AAEA,eAAeU,WACbC,GAAW,EACXjB,UAAkB,EAClBkB,OAAkC;IAElC,IAAIA,YAAY,SAASjB,gBAAgBD,aAAa;QACpD,OAAO;IACT;IAEA,IAAIkB,WAAW,MAAM;QACnBV,QAAQW,KAAK,CAACpB,oBAAoBC;QAClC,MAAM,qBAA2B,CAA3B,IAAIoB,MAAMtB,mBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0B;IAClC;IAEA,MAAMI,aAAamB,QAAQC,OAAO,CAACtB,YAAY;QAAEuB,OAAO;YAACN;SAAI;IAAC;IAC9D,IAAIhB,gBAAgBC,aAAa;QAC/B,OAAO;IACT,OAAO,IAAIgB,YAAY,MAAM;QAC3B,OAAOR,wBAAwB,IAAMW,QAAQnB;IAC/C,OAAO;QACL,IAAI,OAAOgB,YAAY,YAAYM,OAAOC,IAAI,CAACP,SAASQ,MAAM,KAAK,GAAG;YACpE,OAAOhB,wBAAwB,IAAMW,QAAQnB;QAC/C;QACA,OAAOQ,wBAAwB,IAAMW,QAAQnB,YAAYgB;IAC3D;AACF;AAEA,SAASS,kBACPC,iBAAuC,EACvCC,uBAAgC;IAEhC,OAAO;QACLR,QAAQC,OAAO,CAAC;QAChBO,0BACI,QACA;YACER,QAAQC,OAAO,CAAC;YAChB;gBACEQ,UAAUF,qBAAqB;oBAAC;iBAAW;gBAC3CG,cAAc;oBACZ,iCAAiC;oBACjCC,SAAS;gBACX;gBACA,+CAA+C;gBAC/C,+CAA+C;gBAC/CC,OAAO;gBACPC,UAAU;oBACR,qBAAqB;gBACvB;YACF;SACD;KACN,CAACC,MAAM,CAACC;AACX;AAEA,OAAO,eAAeC,kBACpBpB,GAAW,EACXW,iBAAuC,EACvCC,0BAAmC,KAAK,EACxCS,kBAA2B,KAAK;IAEhC,IAAIC,SAAS,MAAM1C,WACjBoB,KACA;IAGF,IAAIsB,UAAU,MAAM;QAClBA,SAAS;YACPC,SAASF,kBACL,EAAE,GACFX,kBAAkBC,mBAAmBC;QAC3C;IACF;IAEA,IAAI,OAAOU,WAAW,YAAY;QAChC,MAAM,qBAGL,CAHK,IAAInB,MACR,CAAC,oGAAoG,CAAC,GACpG,iEAFE,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,6DAA6D;IAC7D,MAAMqB,aAAajB,OAAOC,IAAI,CAACc,QAAQG,IAAI,CAAC,CAACC,MAAQA,QAAQ;IAC7D,IAAIF,YAAY;QACdjC,QAAQC,IAAI,CACV,GAAGb,OACDH,KAAK,YACL,uEAAuE,EAAEgD,WAAW,KAAK,CAAC,GAC1F,CAAC,uCAAuC,CAAC;IAE/C;IAEA,yEAAyE;IACzE,IAAID,UAAUD,OAAOC,OAAO;IAC5B,IAAIA,WAAW,QAAQ,OAAOA,YAAY,UAAU;QAClD,MAAM,qBAEL,CAFK,IAAIpB,MACR,CAAC,gEAAgE,CAAC,GAD9D,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,CAACwB,MAAMC,OAAO,CAACL,UAAU;QAC3B,0CAA0C;QAC1C,MAAMM,KAAKN;QAEXA,UAAUhB,OAAOC,IAAI,CAACe,SAASO,MAAM,CAAC,CAACC,KAAKC;YAC1C,MAAMC,IAAIJ,EAAE,CAACG,KAAK;YAClB,IAAI,OAAOC,MAAM,aAAa;gBAC5B1C,QAAQW,KAAK,CAACpB,oBAAoBkD;gBAClC,MAAM,qBAA2B,CAA3B,IAAI7B,MAAMtB,mBAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0B;YAClC;YAEAkD,IAAIG,IAAI,CAAC;gBAACF;gBAAMC;aAAE;YAClB,OAAOF;QACT,GAAG,EAAE;IACP;IAEA,MAAMI,SAA2B,EAAE;IACnCZ,QAAQa,OAAO,CAAC,CAAC/C;QACf,IAAIA,UAAU,MAAM;YAClBE,QAAQC,IAAI,CACV,GAAGb,OAAOH,KAAK,YAAY,IAAI,EAAEA,KAC/B,QACA,yDAAyD,CAAC;QAEhE,OAAO,IAAI,OAAOa,WAAW,UAAU;YACrC8C,OAAOD,IAAI,CAAC;gBAAC7C;gBAAQ;aAAK;QAC5B,OAAO,IAAIsC,MAAMC,OAAO,CAACvC,SAAS;YAChC,MAAMN,aAAaM,MAAM,CAAC,EAAE;YAC5B,MAAMgD,eAAehD,MAAM,CAAC,EAAE;YAC9B,IACE,OAAON,eAAe,YACrB,CAAA,OAAOsD,iBAAiB,aACvB,OAAOA,iBAAiB,YACxB,OAAOA,iBAAiB,QAAO,GACjC;gBACAF,OAAOD,IAAI,CAAC;oBAACnD;oBAAYsD;iBAAa;YACxC,OAAO;gBACL,IAAI,OAAOtD,eAAe,UAAU;oBAClCQ,QAAQW,KAAK,CACX,GAAGzB,IACDD,KAAK,UACL,yCAAyC,EAAEA,KAC3C,UACA,oBAAoB,EAAEO,WAAW,IAAI,CAAC,GACtC;gBAEN,OAAO;oBACLQ,QAAQW,KAAK,CACX,GAAGzB,IACDD,KAAK,UACL,kFAAkF,EAAEO,WAAW,KAAK,CAAC,GACrG;gBAEN;gBACA,MAAM,qBAA2B,CAA3B,IAAIoB,MAAMtB,mBAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0B;YAClC;QACF,OAAO,IAAI,OAAOQ,WAAW,YAAY;YACvCE,QAAQW,KAAK,CACX,GAAGzB,IACDD,KAAK,UACL,0FAA0F,EAAEA,KAC5F,UACA,4DAA4D,CAAC;YAEjE,MAAM,qBAA2B,CAA3B,IAAI2B,MAAMtB,mBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0B;QAClC,OAAO;YACLU,QAAQW,KAAK,CACX,GAAGzB,IACDD,KAAK,UACL,0CAA0C,EAAEa,OAAO,IAAI,CAAC,GACxD;YAEJ,MAAM,qBAA2B,CAA3B,IAAIc,MAAMtB,mBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0B;QAClC;IACF;IAEA,MAAMyD,WAAW,MAAMC,QAAQC,GAAG,CAChCL,OAAOM,GAAG,CAAC,CAACR,IAAMlC,WAAWC,KAAKiC,CAAC,CAAC,EAAE,EAAEA,CAAC,CAAC,EAAE;IAE9C,MAAMS,WAA+CJ,SAASpB,MAAM,CAClEC;IAGF,OAAOuB;AACT"}