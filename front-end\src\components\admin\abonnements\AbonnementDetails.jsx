import React, { useState, useEffect } from 'react';
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Typography,
  useTheme,
  Alert,
  Snackbar
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import abonnementService from '../../../services/abonnementService';
import { Edit as EditIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';

const AbonnementDetails = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { id } = useParams();
  const [abonnement, setAbonnement] = useState(null);
  const [loading, setLoading] = useState(true);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('error');

  useEffect(() => {
    const fetchAbonnement = async () => {
      try {
        setLoading(true);
        const data = await abonnementService.getAbonnementById(id);
        setAbonnement(data);
      } catch (error) {
        setAlertMessage(error.message || `Erreur lors de la récupération de l'abonnement ${id}`);
        setAlertSeverity('error');
        setAlertOpen(true);
        setTimeout(() => {
          navigate('/admin/abonnements');
        }, 2000);
      } finally {
        setLoading(false);
      }
    };

    fetchAbonnement();
  }, [id, navigate]);

  const handleEdit = () => {
    navigate(`/admin/abonnements/edit/${id}`);
  };

  const handleBack = () => {
    navigate('/admin/abonnements');
  };

  // Formater la date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  // Déterminer la couleur du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIF':
        return theme.palette.success.main;
      case 'SUSPENDU':
        return theme.palette.warning.main;
      case 'EXPIRE':
      case 'ANNULE':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };



  // Traduire la durée en français
  const translateDuree = (duree) => {
    switch (duree) {
      case '3_MOIS':
        return '3 mois';
      case '6_MOIS':
        return '6 mois';
      case '1_AN':
      case 'ANNEE':
        return '1 an';
      default:
        return duree;
    }
  };



  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!abonnement) {
    return (
      <Container maxWidth="md">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Typography variant="h5" color="error">
            Abonnement non trouvé
          </Typography>
          <Button
            variant="contained"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            sx={{ mt: 2 }}
          >
            Retour à la liste
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleBack}
              sx={{ mb: 2 }}
            >
              Retour
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={handleEdit}
            >
              Modifier
            </Button>
          </Grid>
        </Grid>

        <Typography variant="h4" component="h1" gutterBottom>
          Détails de l'abonnement
        </Typography>

        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h5">
                    {abonnement.entrepriseId?.nom || 'Entreprise inconnue'}
                  </Typography>
                  <Chip
                    label={abonnement.statut}
                    sx={{
                      backgroundColor: getStatusColor(abonnement.statut),
                      color: '#fff',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>
                <Typography variant="subtitle1" color="text.secondary">
                  Responsable: {abonnement.responsableId?.prenom} {abonnement.responsableId?.nom}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Divider />
              </Grid>



              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Durée
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {translateDuree(abonnement.duree)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date de début
                </Typography>
                <Typography variant="body1">
                  {formatDate(abonnement.dateDebut)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date de fin
                </Typography>
                <Typography variant="body1">
                  {formatDate(abonnement.dateFin)}
                </Typography>
              </Grid>



              <Grid item xs={12}>
                <Divider />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6">
                  Fonctionnalités activées
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Nombre maximum d'utilisateurs
                </Typography>
                <Typography variant="body1">
                  {abonnement.fonctionnalitesActivees?.nombreUtilisateursMax || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Stockage documents
                </Typography>
                <Typography variant="body1">
                  {abonnement.fonctionnalitesActivees?.stockageDocumentsGo || 'N/A'} Go
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Modules actifs
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {abonnement.fonctionnalitesActivees?.modulesActifs?.map((module, index) => (
                    <Chip
                      key={index}
                      label={module}
                      color="primary"
                      variant="outlined"
                      size="small"
                    />
                  ))}
                </Box>
              </Grid>

              {abonnement.notes && (
                <>
                  <Grid item xs={12}>
                    <Divider />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Notes
                    </Typography>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-line', mt: 1 }}>
                      {abonnement.notes}
                    </Typography>
                  </Grid>
                </>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Box>

      {/* Snackbar pour les alertes */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={() => setAlertOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setAlertOpen(false)}
          severity={alertSeverity}
          sx={{ width: '100%' }}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default AbonnementDetails;
