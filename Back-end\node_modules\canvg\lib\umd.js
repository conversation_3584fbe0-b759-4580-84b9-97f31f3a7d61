!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).canvg={})}(this,(function(t){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function n(t){var e={exports:{}};return t(e,e.exports),e.exports}var i,o,a=function(t){return t&&t.Math==Math&&t},u=a("object"==typeof globalThis&&globalThis)||a("object"==typeof window&&window)||a("object"==typeof self&&self)||a("object"==typeof e&&e)||function(){return this}()||Function("return this")(),s=function(t,e){try{Object.defineProperty(u,t,{value:e,configurable:!0,writable:!0})}catch(r){u[t]=e}return e},c="__core-js_shared__",l=u[c]||s(c,{}),f=n((function(t){(t.exports=function(t,e){return l[t]||(l[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.18.2",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),h=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},p=function(t){return Object(h(t))},v={}.hasOwnProperty,y=Object.hasOwn||function(t,e){return v.call(p(t),e)},d=0,g=Math.random(),m=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++d+g).toString(36)},x=function(t){return"function"==typeof t},b=function(t){return x(t)?t:void 0},w=function(t,e){return arguments.length<2?b(u[t]):u[t]&&u[t][e]},S=w("navigator","userAgent")||"",O=u.process,k=u.Deno,T=O&&O.versions||k&&k.version,A=T&&T.v8;A?o=(i=A.split("."))[0]<4?1:i[0]+i[1]:S&&(!(i=S.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=S.match(/Chrome\/(\d+)/))&&(o=i[1]);var R=o&&+o,P=function(t){try{return!!t()}catch(t){return!0}},E=!!Object.getOwnPropertySymbols&&!P((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&R&&R<41})),C=E&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,M=f("wks"),N=u.Symbol,_=C?N:N&&N.withoutSetter||m,I=function(t){return y(M,t)&&(E||"string"==typeof M[t])||(E&&y(N,t)?M[t]=N[t]:M[t]=_("Symbol."+t)),M[t]},V={};V[I("toStringTag")]="z";var L="[object z]"===String(V),B=!P((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),j=function(t){return"object"==typeof t?null!==t:x(t)},D=u.document,F=j(D)&&j(D.createElement),z=function(t){return F?D.createElement(t):{}},U=!B&&!P((function(){return 7!=Object.defineProperty(z("div"),"a",{get:function(){return 7}}).a})),H=function(t){if(j(t))return t;throw TypeError(String(t)+" is not an object")},X=C?function(t){return"symbol"==typeof t}:function(t){var e=w("Symbol");return x(e)&&Object(t)instanceof e},Y=function(t){try{return String(t)}catch(t){return"Object"}},G=function(t){if(x(t))return t;throw TypeError(Y(t)+" is not a function")},W=function(t,e){var r=t[e];return null==r?void 0:G(r)},q=I("toPrimitive"),$=function(t,e){if(!j(t)||X(t))return t;var r,n=W(t,q);if(n){if(void 0===e&&(e="default"),r=n.call(t,e),!j(r)||X(r))return r;throw TypeError("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&x(r=t.toString)&&!j(n=r.call(t)))return n;if(x(r=t.valueOf)&&!j(n=r.call(t)))return n;if("string"!==e&&x(r=t.toString)&&!j(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")}(t,e)},Q=function(t){var e=$(t,"string");return X(e)?e:String(e)},Z=Object.defineProperty,K={f:B?Z:function(t,e,r){if(H(t),e=Q(e),H(r),U)try{return Z(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},J=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},tt=B?function(t,e,r){return K.f(t,e,J(1,r))}:function(t,e,r){return t[e]=r,t},et=Function.toString;x(l.inspectSource)||(l.inspectSource=function(t){return et.call(t)});var rt,nt,it,ot=l.inspectSource,at=u.WeakMap,ut=x(at)&&/native code/.test(ot(at)),st=f("keys"),ct=function(t){return st[t]||(st[t]=m(t))},lt={},ft="Object already initialized",ht=u.WeakMap;if(ut||l.state){var pt=l.state||(l.state=new ht),vt=pt.get,yt=pt.has,dt=pt.set;rt=function(t,e){if(yt.call(pt,t))throw new TypeError(ft);return e.facade=t,dt.call(pt,t,e),e},nt=function(t){return vt.call(pt,t)||{}},it=function(t){return yt.call(pt,t)}}else{var gt=ct("state");lt[gt]=!0,rt=function(t,e){if(y(t,gt))throw new TypeError(ft);return e.facade=t,tt(t,gt,e),e},nt=function(t){return y(t,gt)?t[gt]:{}},it=function(t){return y(t,gt)}}var mt={set:rt,get:nt,has:it,enforce:function(t){return it(t)?nt(t):rt(t,{})},getterFor:function(t){return function(e){var r;if(!j(e)||(r=nt(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},xt=Function.prototype,bt=B&&Object.getOwnPropertyDescriptor,wt=y(xt,"name"),St={EXISTS:wt,PROPER:wt&&"something"===function(){}.name,CONFIGURABLE:wt&&(!B||B&&bt(xt,"name").configurable)},Ot=n((function(t){var e=St.CONFIGURABLE,r=mt.get,n=mt.enforce,i=String(String).split("String");(t.exports=function(t,r,o,a){var c,l=!!a&&!!a.unsafe,f=!!a&&!!a.enumerable,h=!!a&&!!a.noTargetGet,p=a&&void 0!==a.name?a.name:r;x(o)&&("Symbol("===String(p).slice(0,7)&&(p="["+String(p).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!y(o,"name")||e&&o.name!==p)&&tt(o,"name",p),(c=n(o)).source||(c.source=i.join("string"==typeof p?p:""))),t!==u?(l?!h&&t[r]&&(f=!0):delete t[r],f?t[r]=o:tt(t,r,o)):f?t[r]=o:s(r,o)})(Function.prototype,"toString",(function(){return x(this)&&r(this).source||ot(this)}))})),kt={}.toString,Tt=function(t){return kt.call(t).slice(8,-1)},At=I("toStringTag"),Rt="Arguments"==Tt(function(){return arguments}()),Pt=L?Tt:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),At))?r:Rt?Tt(e):"Object"==(n=Tt(e))&&x(e.callee)?"Arguments":n},Et=L?{}.toString:function(){return"[object "+Pt(this)+"]"};L||Ot(Object.prototype,"toString",Et,{unsafe:!0});var Ct={}.propertyIsEnumerable,Mt=Object.getOwnPropertyDescriptor,Nt={f:Mt&&!Ct.call({1:2},1)?function(t){var e=Mt(this,t);return!!e&&e.enumerable}:Ct},_t="".split,It=P((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==Tt(t)?_t.call(t,""):Object(t)}:Object,Vt=function(t){return It(h(t))},Lt=Object.getOwnPropertyDescriptor,Bt={f:B?Lt:function(t,e){if(t=Vt(t),e=Q(e),U)try{return Lt(t,e)}catch(t){}if(y(t,e))return J(!Nt.f.call(t,e),t[e])}},jt=Math.ceil,Dt=Math.floor,Ft=function(t){var e=+t;return e!=e||0===e?0:(e>0?Dt:jt)(e)},zt=Math.max,Ut=Math.min,Ht=function(t,e){var r=Ft(t);return r<0?zt(r+e,0):Ut(r,e)},Xt=Math.min,Yt=function(t){return t>0?Xt(Ft(t),9007199254740991):0},Gt=function(t){return Yt(t.length)},Wt=function(t){return function(e,r,n){var i,o=Vt(e),a=Gt(o),u=Ht(n,a);if(t&&r!=r){for(;a>u;)if((i=o[u++])!=i)return!0}else for(;a>u;u++)if((t||u in o)&&o[u]===r)return t||u||0;return!t&&-1}},qt={includes:Wt(!0),indexOf:Wt(!1)},$t=qt.indexOf,Qt=function(t,e){var r,n=Vt(t),i=0,o=[];for(r in n)!y(lt,r)&&y(n,r)&&o.push(r);for(;e.length>i;)y(n,r=e[i++])&&(~$t(o,r)||o.push(r));return o},Zt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Kt=Zt.concat("length","prototype"),Jt={f:Object.getOwnPropertyNames||function(t){return Qt(t,Kt)}},te={f:Object.getOwnPropertySymbols},ee=w("Reflect","ownKeys")||function(t){var e=Jt.f(H(t)),r=te.f;return r?e.concat(r(t)):e},re=function(t,e){for(var r=ee(e),n=K.f,i=Bt.f,o=0;o<r.length;o++){var a=r[o];y(t,a)||n(t,a,i(e,a))}},ne=/#|\.prototype\./,ie=function(t,e){var r=ae[oe(t)];return r==se||r!=ue&&(x(e)?P(e):!!e)},oe=ie.normalize=function(t){return String(t).replace(ne,".").toLowerCase()},ae=ie.data={},ue=ie.NATIVE="N",se=ie.POLYFILL="P",ce=ie,le=Bt.f,fe=function(t,e){var r,n,i,o,a,c=t.target,l=t.global,f=t.stat;if(r=l?u:f?u[c]||s(c,{}):(u[c]||{}).prototype)for(n in e){if(o=e[n],i=t.noTargetGet?(a=le(r,n))&&a.value:r[n],!ce(l?n:c+(f?".":"#")+n,t.forced)&&void 0!==i){if(typeof o==typeof i)continue;re(o,i)}(t.sham||i&&i.sham)&&tt(o,"sham",!0),Ot(r,n,o,t)}},he=u.Promise,pe=function(t,e,r){for(var n in e)Ot(t,n,e[n],r);return t},ve=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return H(r),function(t){if("object"==typeof t||x(t))return t;throw TypeError("Can't set "+String(t)+" as a prototype")}(n),e?t.call(r,n):r.__proto__=n,r}}():void 0),ye=K.f,de=I("toStringTag"),ge=function(t,e,r){t&&!y(t=r?t:t.prototype,de)&&ye(t,de,{configurable:!0,value:e})},me=I("species"),xe=function(t){var e=w(t),r=K.f;B&&e&&!e[me]&&r(e,me,{configurable:!0,get:function(){return this}})},be=function(t,e,r){if(t instanceof e)return t;throw TypeError("Incorrect "+(r?r+" ":"")+"invocation")},we={},Se=I("iterator"),Oe=Array.prototype,ke=function(t){return void 0!==t&&(we.Array===t||Oe[Se]===t)},Te=function(t,e,r){if(G(t),void 0===e)return t;switch(r){case 0:return function(){return t.call(e)};case 1:return function(r){return t.call(e,r)};case 2:return function(r,n){return t.call(e,r,n)};case 3:return function(r,n,i){return t.call(e,r,n,i)}}return function(){return t.apply(e,arguments)}},Ae=I("iterator"),Re=function(t){if(null!=t)return W(t,Ae)||W(t,"@@iterator")||we[Pt(t)]},Pe=function(t,e){var r=arguments.length<2?Re(t):e;if(G(r))return H(r.call(t));throw TypeError(String(t)+" is not iterable")},Ee=function(t,e,r){var n,i;H(t);try{if(!(n=W(t,"return"))){if("throw"===e)throw r;return r}n=n.call(t)}catch(t){i=!0,n=t}if("throw"===e)throw r;if(i)throw n;return H(n),r},Ce=function(t,e){this.stopped=t,this.result=e},Me=function(t,e,r){var n,i,o,a,u,s,c,l=r&&r.that,f=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),v=Te(e,l,1+f+p),y=function(t){return n&&Ee(n,"normal",t),new Ce(!0,t)},d=function(t){return f?(H(t),p?v(t[0],t[1],y):v(t[0],t[1])):p?v(t,y):v(t)};if(h)n=t;else{if(!(i=Re(t)))throw TypeError(String(t)+" is not iterable");if(ke(i)){for(o=0,a=Gt(t);a>o;o++)if((u=d(t[o]))&&u instanceof Ce)return u;return new Ce(!1)}n=Pe(t,i)}for(s=n.next;!(c=s.call(n)).done;){try{u=d(c.value)}catch(t){Ee(n,"throw",t)}if("object"==typeof u&&u&&u instanceof Ce)return u}return new Ce(!1)},Ne=I("iterator"),_e=!1;try{var Ie=0,Ve={next:function(){return{done:!!Ie++}},return:function(){_e=!0}};Ve[Ne]=function(){return this},Array.from(Ve,(function(){throw 2}))}catch(t){}var Le,Be,je,De,Fe=function(t,e){if(!e&&!_e)return!1;var r=!1;try{var n={};n[Ne]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r},ze=[],Ue=w("Reflect","construct"),He=/^\s*(?:class|function)\b/,Xe=He.exec,Ye=!He.exec((function(){})),Ge=function(t){if(!x(t))return!1;try{return Ue(Object,ze,t),!0}catch(t){return!1}},We=!Ue||P((function(){var t;return Ge(Ge.call)||!Ge(Object)||!Ge((function(){t=!0}))||t}))?function(t){if(!x(t))return!1;switch(Pt(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Ye||!!Xe.call(He,ot(t))}:Ge,qe=I("species"),$e=function(t,e){var r,n=H(t).constructor;return void 0===n||null==(r=H(n)[qe])?e:function(t){if(We(t))return t;throw TypeError(Y(t)+" is not a constructor")}(r)},Qe=w("document","documentElement"),Ze=/(?:ipad|iphone|ipod).*applewebkit/i.test(S),Ke="process"==Tt(u.process),Je=u.setImmediate,tr=u.clearImmediate,er=u.process,rr=u.MessageChannel,nr=u.Dispatch,ir=0,or={},ar="onreadystatechange";try{Le=u.location}catch(t){}var ur=function(t){if(or.hasOwnProperty(t)){var e=or[t];delete or[t],e()}},sr=function(t){return function(){ur(t)}},cr=function(t){ur(t.data)},lr=function(t){u.postMessage(String(t),Le.protocol+"//"+Le.host)};Je&&tr||(Je=function(t){for(var e=[],r=arguments.length,n=1;r>n;)e.push(arguments[n++]);return or[++ir]=function(){(x(t)?t:Function(t)).apply(void 0,e)},Be(ir),ir},tr=function(t){delete or[t]},Ke?Be=function(t){er.nextTick(sr(t))}:nr&&nr.now?Be=function(t){nr.now(sr(t))}:rr&&!Ze?(De=(je=new rr).port2,je.port1.onmessage=cr,Be=Te(De.postMessage,De,1)):u.addEventListener&&x(u.postMessage)&&!u.importScripts&&Le&&"file:"!==Le.protocol&&!P(lr)?(Be=lr,u.addEventListener("message",cr,!1)):Be=ar in z("script")?function(t){Qe.appendChild(z("script")).onreadystatechange=function(){Qe.removeChild(this),ur(t)}}:function(t){setTimeout(sr(t),0)});var fr,hr,pr,vr,yr,dr,gr,mr,xr={set:Je,clear:tr},br=/ipad|iphone|ipod/i.test(S)&&void 0!==u.Pebble,wr=/web0s(?!.*chrome)/i.test(S),Sr=Bt.f,Or=xr.set,kr=u.MutationObserver||u.WebKitMutationObserver,Tr=u.document,Ar=u.process,Rr=u.Promise,Pr=Sr(u,"queueMicrotask"),Er=Pr&&Pr.value;Er||(fr=function(){var t,e;for(Ke&&(t=Ar.domain)&&t.exit();hr;){e=hr.fn,hr=hr.next;try{e()}catch(t){throw hr?vr():pr=void 0,t}}pr=void 0,t&&t.enter()},Ze||Ke||wr||!kr||!Tr?!br&&Rr&&Rr.resolve?((gr=Rr.resolve(void 0)).constructor=Rr,mr=gr.then,vr=function(){mr.call(gr,fr)}):vr=Ke?function(){Ar.nextTick(fr)}:function(){Or.call(u,fr)}:(yr=!0,dr=Tr.createTextNode(""),new kr(fr).observe(dr,{characterData:!0}),vr=function(){dr.data=yr=!yr}));var Cr,Mr,Nr,_r,Ir=Er||function(t){var e={fn:t,next:void 0};pr&&(pr.next=e),hr||(hr=e,vr()),pr=e},Vr=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw TypeError("Bad Promise constructor");e=t,r=n})),this.resolve=G(e),this.reject=G(r)},Lr={f:function(t){return new Vr(t)}},Br=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},jr="object"==typeof window,Dr=xr.set,Fr=I("species"),zr="Promise",Ur=mt.get,Hr=mt.set,Xr=mt.getterFor(zr),Yr=he&&he.prototype,Gr=he,Wr=Yr,qr=u.TypeError,$r=u.document,Qr=u.process,Zr=Lr.f,Kr=Zr,Jr=!!($r&&$r.createEvent&&u.dispatchEvent),tn=x(u.PromiseRejectionEvent),en="unhandledrejection",rn=!1,nn=ce(zr,(function(){var t=ot(Gr),e=t!==String(Gr);if(!e&&66===R)return!0;if(R>=51&&/native code/.test(t))return!1;var r=new Gr((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};return(r.constructor={})[Fr]=n,!(rn=r.then((function(){}))instanceof n)||!e&&jr&&!tn})),on=nn||!Fe((function(t){Gr.all(t).catch((function(){}))})),an=function(t){var e;return!(!j(t)||!x(e=t.then))&&e},un=function(t,e){if(!t.notified){t.notified=!0;var r=t.reactions;Ir((function(){for(var n=t.value,i=1==t.state,o=0;r.length>o;){var a,u,s,c=r[o++],l=i?c.ok:c.fail,f=c.resolve,h=c.reject,p=c.domain;try{l?(i||(2===t.rejection&&fn(t),t.rejection=1),!0===l?a=n:(p&&p.enter(),a=l(n),p&&(p.exit(),s=!0)),a===c.promise?h(qr("Promise-chain cycle")):(u=an(a))?u.call(a,f,h):f(a)):h(n)}catch(t){p&&!s&&p.exit(),h(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&cn(t)}))}},sn=function(t,e,r){var n,i;Jr?((n=$r.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),u.dispatchEvent(n)):n={promise:e,reason:r},!tn&&(i=u["on"+t])?i(n):t===en&&function(t,e){var r=u.console;r&&r.error&&(1===arguments.length?r.error(t):r.error(t,e))}("Unhandled promise rejection",r)},cn=function(t){Dr.call(u,(function(){var e,r=t.facade,n=t.value;if(ln(t)&&(e=Br((function(){Ke?Qr.emit("unhandledRejection",n,r):sn(en,r,n)})),t.rejection=Ke||ln(t)?2:1,e.error))throw e.value}))},ln=function(t){return 1!==t.rejection&&!t.parent},fn=function(t){Dr.call(u,(function(){var e=t.facade;Ke?Qr.emit("rejectionHandled",e):sn("rejectionhandled",e,t.value)}))},hn=function(t,e,r){return function(n){t(e,n,r)}},pn=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,un(t,!0))},vn=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw qr("Promise can't be resolved itself");var n=an(e);n?Ir((function(){var r={done:!1};try{n.call(e,hn(vn,r,t),hn(pn,r,t))}catch(e){pn(r,e,t)}})):(t.value=e,t.state=1,un(t,!1))}catch(e){pn({done:!1},e,t)}}};if(nn&&(Wr=(Gr=function(t){be(this,Gr,zr),G(t),Cr.call(this);var e=Ur(this);try{t(hn(vn,e),hn(pn,e))}catch(t){pn(e,t)}}).prototype,(Cr=function(t){Hr(this,{type:zr,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=pe(Wr,{then:function(t,e){var r=Xr(this),n=Zr($e(this,Gr));return n.ok=!x(t)||t,n.fail=x(e)&&e,n.domain=Ke?Qr.domain:void 0,r.parent=!0,r.reactions.push(n),0!=r.state&&un(r,!1),n.promise},catch:function(t){return this.then(void 0,t)}}),Mr=function(){var t=new Cr,e=Ur(t);this.promise=t,this.resolve=hn(vn,e),this.reject=hn(pn,e)},Lr.f=Zr=function(t){return t===Gr||t===Nr?new Mr(t):Kr(t)},x(he)&&Yr!==Object.prototype)){_r=Yr.then,rn||(Ot(Yr,"then",(function(t,e){var r=this;return new Gr((function(t,e){_r.call(r,t,e)})).then(t,e)}),{unsafe:!0}),Ot(Yr,"catch",Wr.catch,{unsafe:!0}));try{delete Yr.constructor}catch(t){}ve&&ve(Yr,Wr)}fe({global:!0,wrap:!0,forced:nn},{Promise:Gr}),ge(Gr,zr,!1),xe(zr),Nr=w(zr),fe({target:zr,stat:!0,forced:nn},{reject:function(t){var e=Zr(this);return e.reject.call(void 0,t),e.promise}}),fe({target:zr,stat:!0,forced:nn},{resolve:function(t){return function(t,e){if(H(t),j(e)&&e.constructor===t)return e;var r=Lr.f(t);return(0,r.resolve)(e),r.promise}(this,t)}}),fe({target:zr,stat:!0,forced:on},{all:function(t){var e=this,r=Zr(e),n=r.resolve,i=r.reject,o=Br((function(){var r=G(e.resolve),o=[],a=0,u=1;Me(t,(function(t){var s=a++,c=!1;o.push(void 0),u++,r.call(e,t).then((function(t){c||(c=!0,o[s]=t,--u||n(o))}),i)})),--u||n(o)}));return o.error&&i(o.value),r.promise},race:function(t){var e=this,r=Zr(e),n=r.reject,i=Br((function(){var i=G(e.resolve);Me(t,(function(t){i.call(e,t).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}});var yn=Bt.f;fe({target:"Reflect",stat:!0},{deleteProperty:function(t,e){var r=yn(H(t),e);return!(r&&!r.configurable)&&delete t[e]}});var dn=n((function(t){var e=function(t){var e,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var i=e&&e.prototype instanceof d?e:d,o=Object.create(i.prototype),a=new P(n||[]);return o._invoke=function(t,e,r){var n=f;return function(i,o){if(n===p)throw new Error("Generator is already running");if(n===v){if("throw"===i)throw o;return C()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=T(a,r);if(u){if(u===y)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=p;var s=l(t,e,r);if("normal"===s.type){if(n=r.done?v:h,s.arg===y)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n=v,r.method="throw",r.arg=s.arg)}}}(t,r,a),o}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f="suspendedStart",h="suspendedYield",p="executing",v="completed",y={};function d(){}function g(){}function m(){}var x={};s(x,o,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(E([])));w&&w!==r&&n.call(w,o)&&(x=w);var S=m.prototype=d.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(i,o,a,u){var s=l(t[i],t,o);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){c.value=t,a(c)}),(function(t){return r("throw",t,a,u)}))}u(s.arg)}var i;this._invoke=function(t,n){function o(){return new e((function(e,i){r(t,n,e,i)}))}return i=i?i.then(o,o):o()}}function T(t,r){var n=t.iterator[r.method];if(n===e){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=e,T(t,r),"throw"===r.method))return y;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var i=l(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function R(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function E(t){if(t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}return{next:C}}function C(){return{value:e,done:!0}}return g.prototype=m,s(S,"constructor",m),s(m,"constructor",g),g.displayName=s(m,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},t.awrap=function(t){return{__await:t}},O(k.prototype),s(k.prototype,a,(function(){return this})),t.AsyncIterator=k,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new k(c(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,o,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=E,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(R),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return u.type="throw",u.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),R(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;R(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:E(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}})),gn=r(n((function(t){function e(t,e,r,n,i,o,a){try{var u=t[o](a),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,i)}t.exports=function(t){return function(){var r=this,n=arguments;return new Promise((function(i,o){var a=t.apply(r,n);function u(t){e(a,i,o,u,s,"next",t)}function s(t){e(a,i,o,u,s,"throw",t)}u(void 0)}))}},t.exports.default=t.exports,t.exports.__esModule=!0})));var mn=Object.freeze({__proto__:null,offscreen:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.DOMParser,r={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,createCanvas:function(t,e){return new OffscreenCanvas(t,e)},createImage:function(t){return gn(dn.mark((function e(){var r,n,i;return dn.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch(t);case 2:return r=e.sent,e.next=5,r.blob();case 5:return n=e.sent,e.next=8,createImageBitmap(n);case 8:return i=e.sent,e.abrupt("return",i);case 10:case"end":return e.stop()}}),e)})))()}};return"undefined"==typeof DOMParser&&void 0!==e||Reflect.deleteProperty(r,"DOMParser"),r},node:function(t){var e=t.DOMParser,r=t.canvas;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:t.fetch,createCanvas:r.createCanvas,createImage:r.loadImage}}}),xn=Array.isArray||function(t){return"Array"==Tt(t)},bn=I("species"),wn=function(t,e){return new(function(t){var e;return xn(t)&&(e=t.constructor,(We(e)&&(e===Array||xn(e.prototype))||j(e)&&null===(e=e[bn]))&&(e=void 0)),void 0===e?Array:e}(t))(0===e?0:e)},Sn=[].push,On=function(t){var e=1==t,r=2==t,n=3==t,i=4==t,o=6==t,a=7==t,u=5==t||o;return function(s,c,l,f){for(var h,v,y=p(s),d=It(y),g=Te(c,l,3),m=Gt(d),x=0,b=f||wn,w=e?b(s,m):r||a?b(s,0):void 0;m>x;x++)if((u||x in d)&&(v=g(h=d[x],x,y),t))if(e)w[x]=v;else if(v)switch(t){case 3:return!0;case 5:return h;case 6:return x;case 2:Sn.call(w,h)}else switch(t){case 4:return!1;case 7:Sn.call(w,h)}return o?-1:n||i?i:w}},kn={forEach:On(0),map:On(1),filter:On(2),some:On(3),every:On(4),find:On(5),findIndex:On(6),filterReject:On(7)},Tn=I("species"),An=function(t){return R>=51||!P((function(){var e=[];return(e.constructor={})[Tn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Rn=kn.map,Pn=An("map");fe({target:"Array",proto:!0,forced:!Pn},{map:function(t){return Rn(this,t,arguments.length>1?arguments[1]:void 0)}});var En=function(t){if("Symbol"===Pt(t))throw TypeError("Cannot convert a Symbol value to a string");return String(t)},Cn="\t\n\v\f\r                　\u2028\u2029\ufeff",Mn="["+Cn+"]",Nn=RegExp("^"+Mn+Mn+"*"),_n=RegExp(Mn+Mn+"*$"),In=function(t){return function(e){var r=En(h(e));return 1&t&&(r=r.replace(Nn,"")),2&t&&(r=r.replace(_n,"")),r}},Vn={start:In(1),end:In(2),trim:In(3)},Ln=Vn.trim,Bn=u.parseFloat,jn=u.Symbol,Dn=jn&&jn.iterator,Fn=1/Bn(Cn+"-0")!=-1/0||Dn&&!P((function(){Bn(Object(Dn))}))?function(t){var e=Ln(En(t)),r=Bn(e);return 0===r&&"-"==e.charAt(0)?-0:r}:Bn;fe({global:!0,forced:parseFloat!=Fn},{parseFloat:Fn});var zn,Un=function(){var t=H(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Hn=u.RegExp,Xn={UNSUPPORTED_Y:P((function(){var t=Hn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),BROKEN_CARET:P((function(){var t=Hn("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},Yn=Object.keys||function(t){return Qt(t,Zt)},Gn=B?Object.defineProperties:function(t,e){H(t);for(var r,n=Yn(e),i=n.length,o=0;i>o;)K.f(t,r=n[o++],e[r]);return t},Wn=ct("IE_PROTO"),qn=function(){},$n=function(t){return"<script>"+t+"</"+"script>"},Qn=function(t){t.write($n("")),t.close();var e=t.parentWindow.Object;return t=null,e},Zn=function(){try{zn=new ActiveXObject("htmlfile")}catch(t){}var t,e;Zn="undefined"!=typeof document?document.domain&&zn?Qn(zn):((e=z("iframe")).style.display="none",Qe.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write($n("document.F=Object")),t.close(),t.F):Qn(zn);for(var r=Zt.length;r--;)delete Zn.prototype[Zt[r]];return Zn()};lt[Wn]=!0;var Kn,Jn,ti=Object.create||function(t,e){var r;return null!==t?(qn.prototype=H(t),r=new qn,qn.prototype=null,r[Wn]=t):r=Zn(),void 0===e?r:Gn(r,e)},ei=u.RegExp,ri=P((function(){var t=ei(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),ni=u.RegExp,ii=P((function(){var t=ni("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),oi=mt.get,ai=RegExp.prototype.exec,ui=f("native-string-replace",String.prototype.replace),si=ai,ci=(Kn=/a/,Jn=/b*/g,ai.call(Kn,"a"),ai.call(Jn,"a"),0!==Kn.lastIndex||0!==Jn.lastIndex),li=Xn.UNSUPPORTED_Y||Xn.BROKEN_CARET,fi=void 0!==/()??/.exec("")[1];(ci||fi||li||ri||ii)&&(si=function(t){var e,r,n,i,o,a,u,s=this,c=oi(s),l=En(t),f=c.raw;if(f)return f.lastIndex=s.lastIndex,e=si.call(f,l),s.lastIndex=f.lastIndex,e;var h=c.groups,p=li&&s.sticky,v=Un.call(s),y=s.source,d=0,g=l;if(p&&(-1===(v=v.replace("y","")).indexOf("g")&&(v+="g"),g=l.slice(s.lastIndex),s.lastIndex>0&&(!s.multiline||s.multiline&&"\n"!==l.charAt(s.lastIndex-1))&&(y="(?: "+y+")",g=" "+g,d++),r=new RegExp("^(?:"+y+")",v)),fi&&(r=new RegExp("^"+y+"$(?!\\s)",v)),ci&&(n=s.lastIndex),i=ai.call(p?r:s,g),p?i?(i.input=i.input.slice(d),i[0]=i[0].slice(d),i.index=s.lastIndex,s.lastIndex+=i[0].length):s.lastIndex=0:ci&&i&&(s.lastIndex=s.global?i.index+i[0].length:n),fi&&i&&i.length>1&&ui.call(i[0],r,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(i[o]=void 0)})),i&&h)for(i.groups=a=ti(null),o=0;o<h.length;o++)a[(u=h[o])[0]]=i[u[1]];return i});var hi=si;fe({target:"RegExp",proto:!0,forced:/./.exec!==hi},{exec:hi});var pi=I("species"),vi=RegExp.prototype,yi=function(t,e,r,n){var i=I(t),o=!P((function(){var e={};return e[i]=function(){return 7},7!=""[t](e)})),a=o&&!P((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[pi]=function(){return r},r.flags="",r[i]=/./[i]),r.exec=function(){return e=!0,null},r[i](""),!e}));if(!o||!a||r){var u=/./[i],s=e(i,""[t],(function(t,e,r,n,i){var a=e.exec;return a===hi||a===vi.exec?o&&!i?{done:!0,value:u.call(e,r,n)}:{done:!0,value:t.call(r,e,n)}:{done:!1}}));Ot(String.prototype,t,s[0]),Ot(vi,i,s[1])}n&&tt(vi[i],"sham",!0)},di=function(t){return function(e,r){var n,i,o=En(h(e)),a=Ft(r),u=o.length;return a<0||a>=u?t?"":void 0:(n=o.charCodeAt(a))<55296||n>56319||a+1===u||(i=o.charCodeAt(a+1))<56320||i>57343?t?o.charAt(a):n:t?o.slice(a,a+2):i-56320+(n-55296<<10)+65536}},gi={codeAt:di(!1),charAt:di(!0)},mi=gi.charAt,xi=function(t,e,r){return e+(r?mi(t,e).length:1)},bi=function(t,e){var r=t.exec;if(x(r)){var n=r.call(t,e);return null!==n&&H(n),n}if("RegExp"===Tt(t))return hi.call(t,e);throw TypeError("RegExp#exec called on incompatible receiver")};yi("match",(function(t,e,r){return[function(e){var r=h(this),n=null==e?void 0:W(e,t);return n?n.call(e,r):new RegExp(e)[t](En(r))},function(t){var n=H(this),i=En(t),o=r(e,n,i);if(o.done)return o.value;if(!n.global)return bi(n,i);var a=n.unicode;n.lastIndex=0;for(var u,s=[],c=0;null!==(u=bi(n,i));){var l=En(u[0]);s[c]=l,""===l&&(n.lastIndex=xi(i,Yt(n.lastIndex),a)),c++}return 0===c?null:s}]}));var wi=Math.floor,Si="".replace,Oi=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,ki=/\$([$&'`]|\d{1,2})/g,Ti=function(t,e,r,n,i,o){var a=r+t.length,u=n.length,s=ki;return void 0!==i&&(i=p(i),s=Oi),Si.call(o,s,(function(o,s){var c;switch(s.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,r);case"'":return e.slice(a);case"<":c=i[s.slice(1,-1)];break;default:var l=+s;if(0===l)return o;if(l>u){var f=wi(l/10);return 0===f?o:f<=u?void 0===n[f-1]?s.charAt(1):n[f-1]+s.charAt(1):o}c=n[l-1]}return void 0===c?"":c}))},Ai=I("replace"),Ri=Math.max,Pi=Math.min,Ei="$0"==="a".replace(/./,"$0"),Ci=!!/./[Ai]&&""===/./[Ai]("a","$0");yi("replace",(function(t,e,r){var n=Ci?"$":"$0";return[function(t,r){var n=h(this),i=null==t?void 0:W(t,Ai);return i?i.call(t,n,r):e.call(En(n),t,r)},function(t,i){var o=H(this),a=En(t);if("string"==typeof i&&-1===i.indexOf(n)&&-1===i.indexOf("$<")){var u=r(e,o,a,i);if(u.done)return u.value}var s=x(i);s||(i=En(i));var c=o.global;if(c){var l=o.unicode;o.lastIndex=0}for(var f=[];;){var h=bi(o,a);if(null===h)break;if(f.push(h),!c)break;""===En(h[0])&&(o.lastIndex=xi(a,Yt(o.lastIndex),l))}for(var p,v="",y=0,d=0;d<f.length;d++){h=f[d];for(var g=En(h[0]),m=Ri(Pi(Ft(h.index),a.length),0),b=[],w=1;w<h.length;w++)b.push(void 0===(p=h[w])?p:String(p));var S=h.groups;if(s){var O=[g].concat(b,m,a);void 0!==S&&O.push(S);var k=En(i.apply(void 0,O))}else k=Ti(g,a,m,b,S,i);m>=y&&(v+=a.slice(y,m)+k,y=m+g.length)}return v+a.slice(y)}]}),!!P((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!Ei||Ci);var Mi,Ni=I("match"),_i=function(t){var e;return j(t)&&(void 0!==(e=t[Ni])?!!e:"RegExp"==Tt(t))},Ii=function(t){if(_i(t))throw TypeError("The method doesn't accept regular expressions");return t},Vi=I("match"),Li=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[Vi]=!1,"/./"[t](e)}catch(t){}}return!1},Bi=Bt.f,ji="".startsWith,Di=Math.min,Fi=Li("startsWith"),zi=!(Fi||(Mi=Bi(String.prototype,"startsWith"),!Mi||Mi.writable));function Ui(t){return t.replace(/(?!\u3000)\s+/gm," ")}function Hi(t){return t.replace(/^[\n \t]+/,"")}function Xi(t){return t.replace(/[\n \t]+$/,"")}function Yi(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}fe({target:"String",proto:!0,forced:!zi&&!Fi},{startsWith:function(t){var e=En(h(this));Ii(t);var r=Yt(Di(arguments.length>1?arguments[1]:void 0,e.length)),n=En(t);return ji?ji.call(e,n,r):e.slice(r,r+n.length)===n}});var Gi=/^[A-Z-]+$/;function Wi(t){return Gi.test(t)?t.toLowerCase():t}function qi(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function $i(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(function(t,r){return e--&&r?String(Math.round(parseFloat(t))):t}))}var Qi=function(t,e){var r=[][t];return!!r&&P((function(){r.call(null,e||function(){throw 1},1)}))},Zi=[].join,Ki=It!=Object,Ji=Qi("join",",");fe({target:"Array",proto:!0,forced:Ki||!Ji},{join:function(t){return Zi.call(Vt(this),void 0===t?",":t)}});var to=n((function(t){t.exports=function(t){if(Array.isArray(t))return t},t.exports.default=t.exports,t.exports.__esModule=!0})),eo=n((function(t){t.exports=function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(o.push(n.value),!e||o.length!==e);a=!0);}catch(t){u=!0,i=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw i}}return o}},t.exports.default=t.exports,t.exports.__esModule=!0})),ro=n((function(t){t.exports=function(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n},t.exports.default=t.exports,t.exports.__esModule=!0})),no=n((function(t){t.exports=function(t,e){if(t){if("string"==typeof t)return ro(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ro(t,e):void 0}},t.exports.default=t.exports,t.exports.__esModule=!0})),io=n((function(t){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.default=t.exports,t.exports.__esModule=!0})),oo=r(n((function(t){t.exports=function(t,e){return to(t)||eo(t,e)||no(t,e)||io()},t.exports.default=t.exports,t.exports.__esModule=!0}))),ao=/(\[[^\]]+\])/g,uo=/(#[^\s+>~.[:]+)/g,so=/(\.[^\s+>~.[:]+)/g,co=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,lo=/(:[\w-]+\([^)]*\))/gi,fo=/(:[^\s+>~.[:]+)/g,ho=/([^\s+>~.[:]+)/g;function po(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function vo(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),n=0,i=po(r,ao),o=oo(i,2);r=o[0],n=o[1],e[1]+=n;var a=po(r,uo),u=oo(a,2);r=u[0],n=u[1],e[0]+=n;var s=po(r,so),c=oo(s,2);r=c[0],n=c[1],e[1]+=n;var l=po(r,co),f=oo(l,2);r=f[0],n=f[1],e[2]+=n;var h=po(r,lo),p=oo(h,2);r=p[0],n=p[1],e[1]+=n;var v=po(r,fo),y=oo(v,2);r=y[0],n=y[1],e[1]+=n;var d=po(r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),ho),g=oo(d,2);return r=g[0],n=g[1],e[2]+=n,e.join("")}var yo=1e-8;function go(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function mo(t,e){return(t[0]*e[0]+t[1]*e[1])/(go(t)*go(e))}function xo(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(mo(t,e))}function bo(t){return t*t*t}function wo(t){return 3*t*t*(1-t)}function So(t){return 3*t*(1-t)*(1-t)}function Oo(t){return(1-t)*(1-t)*(1-t)}function ko(t){return t*t}function To(t){return 2*t*(1-t)}function Ao(t){return(1-t)*(1-t)}var Ro=r(n((function(t){t.exports=function(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.default=t.exports,t.exports.__esModule=!0}))),Po=r(n((function(t){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},t.exports.default=t.exports,t.exports.__esModule=!0}))),Eo=r(n((function(t){function e(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}t.exports=function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t},t.exports.default=t.exports,t.exports.__esModule=!0}))),Co=function(t,e,r){var n=Q(e);n in t?K.f(t,n,J(0,r)):t[n]=r},Mo=I("isConcatSpreadable"),No=9007199254740991,_o="Maximum allowed index exceeded",Io=R>=51||!P((function(){var t=[];return t[Mo]=!1,t.concat()[0]!==t})),Vo=An("concat"),Lo=function(t){if(!j(t))return!1;var e=t[Mo];return void 0!==e?!!e:xn(t)};fe({target:"Array",proto:!0,forced:!Io||!Vo},{concat:function(t){var e,r,n,i,o,a=p(this),u=wn(a,0),s=0;for(e=-1,n=arguments.length;e<n;e++)if(Lo(o=-1===e?a:arguments[e])){if(s+(i=Gt(o))>No)throw TypeError(_o);for(r=0;r<i;r++,s++)r in o&&Co(u,s,o[r])}else{if(s>=No)throw TypeError(_o);Co(u,s++,o)}return u.length=s,u}});var Bo=kn.every,jo=Qi("every");fe({target:"Array",proto:!0,forced:!jo},{every:function(t){return Bo(this,t,arguments.length>1?arguments[1]:void 0)}});var Do=function(t){return function(e,r,n,i){G(r);var o=p(e),a=It(o),u=Gt(o),s=t?u-1:0,c=t?-1:1;if(n<2)for(;;){if(s in a){i=a[s],s+=c;break}if(s+=c,t?s<0:u<=s)throw TypeError("Reduce of empty array with no initial value")}for(;t?s>=0:u>s;s+=c)s in a&&(i=r(i,a[s],s,o));return i}},Fo={left:Do(!1),right:Do(!0)}.left,zo=Qi("reduce");fe({target:"Array",proto:!0,forced:!zo||!Ke&&R>79&&R<83},{reduce:function(t){return Fo(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}});var Uo=Bt.f,Ho="".endsWith,Xo=Math.min,Yo=Li("endsWith"),Go=!Yo&&!!function(){var t=Uo(String.prototype,"endsWith");return t&&!t.writable}();fe({target:"String",proto:!0,forced:!Go&&!Yo},{endsWith:function(t){var e=En(h(this));Ii(t);var r=arguments.length>1?arguments[1]:void 0,n=e.length,i=void 0===r?n:Xo(Yt(r),n),o=En(t);return Ho?Ho.call(e,o,i):e.slice(i-o.length,i)===o}});var Wo=Xn.UNSUPPORTED_Y,qo=[].push,$o=Math.min,Qo=4294967295;yi("split",(function(t,e,r){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var n=En(h(this)),i=void 0===r?Qo:r>>>0;if(0===i)return[];if(void 0===t)return[n];if(!_i(t))return e.call(n,t,i);for(var o,a,u,s=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),l=0,f=new RegExp(t.source,c+"g");(o=hi.call(f,n))&&!((a=f.lastIndex)>l&&(s.push(n.slice(l,o.index)),o.length>1&&o.index<n.length&&qo.apply(s,o.slice(1)),u=o[0].length,l=a,s.length>=i));)f.lastIndex===o.index&&f.lastIndex++;return l===n.length?!u&&f.test("")||s.push(""):s.push(n.slice(l)),s.length>i?s.slice(0,i):s}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:e.call(this,t,r)}:e,[function(e,r){var i=h(this),o=null==e?void 0:W(e,t);return o?o.call(e,i,r):n.call(En(i),e,r)},function(t,i){var o=H(this),a=En(t),u=r(n,o,a,i,n!==e);if(u.done)return u.value;var s=$e(o,RegExp),c=o.unicode,l=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(Wo?"g":"y"),f=new s(Wo?"^(?:"+o.source+")":o,l),h=void 0===i?Qo:i>>>0;if(0===h)return[];if(0===a.length)return null===bi(f,a)?[a]:[];for(var p=0,v=0,y=[];v<a.length;){f.lastIndex=Wo?0:v;var d,g=bi(f,Wo?a.slice(v):a);if(null===g||(d=$o(Yt(f.lastIndex+(Wo?v:0)),a.length))===p)v=xi(a,v,c);else{if(y.push(a.slice(p,v)),y.length===h)return y;for(var m=1;m<=g.length-1;m++)if(y.push(g[m]),y.length===h)return y;v=p=d}}return y.push(a.slice(p)),y}]}),!!P((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),Wo);var Zo="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Ko(){throw new Error("setTimeout has not been defined")}function Jo(){throw new Error("clearTimeout has not been defined")}var ta=Ko,ea=Jo;function ra(t){if(ta===setTimeout)return setTimeout(t,0);if((ta===Ko||!ta)&&setTimeout)return ta=setTimeout,setTimeout(t,0);try{return ta(t,0)}catch(e){try{return ta.call(null,t,0)}catch(e){return ta.call(this,t,0)}}}"function"==typeof Zo.setTimeout&&(ta=setTimeout),"function"==typeof Zo.clearTimeout&&(ea=clearTimeout);var na,ia=[],oa=!1,aa=-1;function ua(){oa&&na&&(oa=!1,na.length?ia=na.concat(ia):aa=-1,ia.length&&sa())}function sa(){if(!oa){var t=ra(ua);oa=!0;for(var e=ia.length;e;){for(na=ia,ia=[];++aa<e;)na&&na[aa].run();aa=-1,e=ia.length}na=null,oa=!1,function(t){if(ea===clearTimeout)return clearTimeout(t);if((ea===Jo||!ea)&&clearTimeout)return ea=clearTimeout,clearTimeout(t);try{ea(t)}catch(e){try{return ea.call(null,t)}catch(e){return ea.call(this,t)}}}(t)}}function ca(t,e){this.fun=t,this.array=e}ca.prototype.run=function(){this.fun.apply(null,this.array)};function la(){}var fa=la,ha=la,pa=la,va=la,ya=la,da=la,ga=la;var ma=Zo.performance||{},xa=ma.now||ma.mozNow||ma.msNow||ma.oNow||ma.webkitNow||function(){return(new Date).getTime()};var ba=new Date;for(var wa={nextTick:function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];ia.push(new ca(t,e)),1!==ia.length||oa||ra(sa)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:fa,addListener:ha,once:pa,off:va,removeListener:ya,removeAllListeners:da,emit:ga,binding:function(t){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(t){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(t){var e=.001*xa.call(ma),r=Math.floor(e),n=Math.floor(e%1*1e9);return t&&(r-=t[0],(n-=t[1])<0&&(r--,n+=1e9)),[r,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-ba)/1e3}},Sa=n((function(t){(function(){var e,r,n,i,o,a;"undefined"!=typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:null!=wa&&wa.hrtime?(t.exports=function(){return(e()-o)/1e6},r=wa.hrtime,i=(e=function(){var t;return 1e9*(t=r())[0]+t[1]})(),a=1e9*wa.uptime(),o=i-a):Date.now?(t.exports=function(){return Date.now()-n},n=Date.now()):(t.exports=function(){return(new Date).getTime()-n},n=(new Date).getTime())}).call(e)})),Oa="undefined"==typeof window?e:window,ka=["moz","webkit"],Ta="AnimationFrame",Aa=Oa["request"+Ta],Ra=Oa["cancel"+Ta]||Oa["cancelRequest"+Ta],Pa=0;!Aa&&Pa<ka.length;Pa++)Aa=Oa[ka[Pa]+"Request"+Ta],Ra=Oa[ka[Pa]+"Cancel"+Ta]||Oa[ka[Pa]+"CancelRequest"+Ta];if(!Aa||!Ra){var Ea=0,Ca=0,Ma=[];Aa=function(t){if(0===Ma.length){var e=Sa(),r=Math.max(0,16.666666666666668-(e-Ea));Ea=r+e,setTimeout((function(){var t=Ma.slice(0);Ma.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(Ea)}catch(t){setTimeout((function(){throw t}),0)}}),Math.round(r))}return Ma.push({handle:++Ca,callback:t,cancelled:!1}),Ca},Ra=function(t){for(var e=0;e<Ma.length;e++)Ma[e].handle===t&&(Ma[e].cancelled=!0)}}var Na=function(t){return Aa.call(Oa,t)};Na.cancel=function(){Ra.apply(Oa,arguments)},Na.polyfill=function(t){t||(t=Oa),t.requestAnimationFrame=Aa,t.cancelAnimationFrame=Ra};var _a=St.EXISTS,Ia=K.f,Va=Function.prototype,La=Va.toString,Ba=/^\s*function ([^ (]*)/;B&&!_a&&Ia(Va,"name",{configurable:!0,get:function(){try{return La.call(this).match(Ba)[1]}catch(t){return""}}});var ja,Da=St.PROPER,Fa=Vn.trim;fe({target:"String",proto:!0,forced:(ja="trim",P((function(){return!!Cn[ja]()||"​᠎"!=="​᠎"[ja]()||Da&&Cn[ja].name!==ja})))},{trim:function(){return Fa(this)}});var za=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],n=0;n<r.length;n++){var i=r[n].re,o=r[n].process,a=i.exec(t);if(a){var u=o(a);this.r=u[0],this.g=u[1],this.b=u[2],u.length>3&&(this.alpha=u[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,n=0;n<r.length;n++)for(var i=r[n].example,o=0;o<i.length;o++)t[t.length]=i[o];for(var a in e)t[t.length]=a;var u=document.createElement("ul");u.setAttribute("id","rgbcolor-examples");for(n=0;n<t.length;n++)try{var s=document.createElement("li"),c=new RGBColor(t[n]),l=document.createElement("div");l.style.cssText="margin: 3px; border: 1px solid black; background:"+c.toHex()+"; color:"+c.toHex(),l.appendChild(document.createTextNode("test"));var f=document.createTextNode(" "+t[n]+" -> "+c.toRGB()+" -> "+c.toHex());s.appendChild(l),s.appendChild(f),u.appendChild(s)}catch(t){}return u}},Ua=function(){function t(e,r,n){Po(this,t),this.document=e,this.name=r,this.value=n,this.isNormalizedColor=!1}return Eo(t,[{key:"split",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",r=this.document,n=this.name;return Ui(this.getString()).trim().split(e).map((function(e){return new t(r,n,e)}))}},{key:"hasValue",value:function(t){var e=this.value;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}},{key:"isString",value:function(t){var e=this.value,r="string"==typeof e;return r&&t?t.test(e):r}},{key:"isUrlDefinition",value:function(){return this.isString(/^url\(/)}},{key:"isPixels",value:function(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}},{key:"setValue",value:function(t){return this.value=t,this}},{key:"getValue",value:function(t){return void 0===t||this.hasValue()?this.value:t}},{key:"getNumber",value:function(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var e=this.value,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}},{key:"getString",value:function(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}},{key:"getColor",value:function(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=$i(e),this.value=e),e}},{key:"getDpi",value:function(){return 96}},{key:"getRem",value:function(){return this.document.rootEmSize}},{key:"getEm",value:function(){return this.document.emSize}},{key:"getUnits",value:function(){return this.getString().replace(/[0-9.-]/g,"")}},{key:"getPixels",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var r="boolean"==typeof t?[void 0,t]:[t],n=oo(r,2),i=n[0],o=n[1],a=this.document.screen.viewPort;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(a.computeSize("x"),a.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(a.computeSize("x"),a.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*a.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*a.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&o:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*a.computeSize(i);default:var u=this.getNumber();return e&&u<1?u*a.computeSize(i):u}}},{key:"getMilliseconds",value:function(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}},{key:"getRadians",value:function(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}},{key:"getDefinition",value:function(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}},{key:"getFillStyleDefinition",value:function(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var n=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),n.hasValue()&&r.getAttribute("patternTransform",!0).setValue(n.value)}return r.createPattern(this.document.ctx,t,e)}return null}},{key:"getTextBaseline",value:function(){return this.hasValue()?t.textBaselineMapping[this.getString()]:null}},{key:"addOpacity",value:function(e){for(var r=this.getColor(),n=r.length,i=0,o=0;o<n&&(","===r[o]&&i++,3!==i);o++);if(e.hasValue()&&this.isString()&&3!==i){var a=new za(r);a.ok&&(a.alpha=e.getNumber(),r=a.toRGBA())}return new t(this.document,this.name,r)}}],[{key:"empty",value:function(e){return new t(e,"EMPTY","")}}]),t}();Ua.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};var Ha=function(){function t(){Po(this,t),this.viewPorts=[]}return Eo(t,[{key:"clear",value:function(){this.viewPorts=[]}},{key:"setCurrent",value:function(t,e){this.viewPorts.push({width:t,height:e})}},{key:"removeCurrent",value:function(){this.viewPorts.pop()}},{key:"getCurrent",value:function(){var t=this.viewPorts;return t[t.length-1]}},{key:"computeSize",value:function(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}},{key:"width",get:function(){return this.getCurrent().width}},{key:"height",get:function(){return this.getCurrent().height}}]),t}(),Xa=kn.forEach,Ya=Qi("forEach")?[].forEach:function(t){return Xa(this,t,arguments.length>1?arguments[1]:void 0)};fe({target:"Array",proto:!0,forced:[].forEach!=Ya},{forEach:Ya});var Ga={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Wa=z("span").classList,qa=Wa&&Wa.constructor&&Wa.constructor.prototype,$a=qa===Object.prototype?void 0:qa,Qa=function(t){if(t&&t.forEach!==Ya)try{tt(t,"forEach",Ya)}catch(e){t.forEach=Ya}};for(var Za in Ga)Ga[Za]&&Qa(u[Za]&&u[Za].prototype);Qa($a);var Ka=function(){function t(e,r){Po(this,t),this.x=e,this.y=r}return Eo(t,[{key:"angleTo",value:function(t){return Math.atan2(t.y-this.y,t.x-this.x)}},{key:"applyTransform",value:function(t){var e=this.x,r=this.y,n=e*t[0]+r*t[2]+t[4],i=e*t[1]+r*t[3]+t[5];this.x=n,this.y=i}}],[{key:"parse",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=Yi(e),i=oo(n,2),o=i[0],a=void 0===o?r:o,u=i[1],s=void 0===u?r:u;return new t(a,s)}},{key:"parseScale",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=Yi(e),i=oo(n,2),o=i[0],a=void 0===o?r:o,u=i[1],s=void 0===u?a:u;return new t(a,s)}},{key:"parsePath",value:function(e){for(var r=Yi(e),n=r.length,i=[],o=0;o<n;o+=2)i.push(new t(r[o],r[o+1]));return i}}]),t}(),Ja=function(){function t(e){Po(this,t),this.screen=e,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}return Eo(t,[{key:"isWorking",value:function(){return this.working}},{key:"start",value:function(){if(!this.working){var t=this.screen,e=this.onClick,r=this.onMouseMove,n=t.ctx.canvas;n.onclick=e,n.onmousemove=r,this.working=!0}}},{key:"stop",value:function(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}},{key:"hasEvents",value:function(){return this.working&&this.events.length>0}},{key:"runEvents",value:function(){if(this.working){var t=this.screen,e=this.events,r=this.eventElements,n=t.ctx.canvas.style;n&&(n.cursor=""),e.forEach((function(t,e){for(var n=t.run,i=r[e];i;)n(i),i=i.parent})),this.events=[],this.eventElements=[]}}},{key:"checkPath",value:function(t,e){if(this.working&&e){var r=this.events,n=this.eventElements;r.forEach((function(r,i){var o=r.x,a=r.y;!n[i]&&e.isPointInPath&&e.isPointInPath(o,a)&&(n[i]=t)}))}}},{key:"checkBoundingBox",value:function(t,e){if(this.working&&e){var r=this.events,n=this.eventElements;r.forEach((function(r,i){var o=r.x,a=r.y;!n[i]&&e.isPointInBox(o,a)&&(n[i]=t)}))}}},{key:"mapXY",value:function(t,e){for(var r=this.screen,n=r.window,i=r.ctx,o=new Ka(t,e),a=i.canvas;a;)o.x-=a.offsetLeft,o.y-=a.offsetTop,a=a.offsetParent;return n.scrollX&&(o.x+=n.scrollX),n.scrollY&&(o.y+=n.scrollY),o}},{key:"onClick",value:function(t){var e=this.mapXY(t.clientX,t.clientY),r=e.x,n=e.y;this.events.push({type:"onclick",x:r,y:n,run:function(t){t.onClick&&t.onClick()}})}},{key:"onMouseMove",value:function(t){var e=this.mapXY(t.clientX,t.clientY),r=e.x,n=e.y;this.events.push({type:"onmousemove",x:r,y:n,run:function(t){t.onMouseMove&&t.onMouseMove()}})}}]),t}(),tu="undefined"!=typeof window?window:null,eu="undefined"!=typeof fetch?fetch.bind(void 0):null,ru=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.fetch,i=void 0===n?eu:n,o=r.window,a=void 0===o?tu:o;Po(this,t),this.ctx=e,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new Ha,this.mouse=new Ja(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=a,this.fetch=i}return Eo(t,[{key:"wait",value:function(t){this.waits.push(t)}},{key:"ready",value:function(){return this.readyPromise?this.readyPromise:Promise.resolve()}},{key:"isReady",value:function(){if(this.isReadyLock)return!0;var t=this.waits.every((function(t){return t()}));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}},{key:"setDefaults",value:function(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}},{key:"setViewBox",value:function(t){var e=t.document,r=t.ctx,n=t.aspectRatio,i=t.width,o=t.desiredWidth,a=t.height,u=t.desiredHeight,s=t.minX,c=void 0===s?0:s,l=t.minY,f=void 0===l?0:l,h=t.refX,p=t.refY,v=t.clip,y=void 0!==v&&v,d=t.clipX,g=void 0===d?0:d,m=t.clipY,x=void 0===m?0:m,b=Ui(n).replace(/^defer\s/,"").split(" "),w=oo(b,2),S=w[0]||"xMidYMid",O=w[1]||"meet",k=i/o,T=a/u,A=Math.min(k,T),R=Math.max(k,T),P=o,E=u;"meet"===O&&(P*=A,E*=A),"slice"===O&&(P*=R,E*=R);var C=new Ua(e,"refX",h),M=new Ua(e,"refY",p),N=C.hasValue()&&M.hasValue();if(N&&r.translate(-A*C.getPixels("x"),-A*M.getPixels("y")),y){var _=A*g,I=A*x;r.beginPath(),r.moveTo(_,I),r.lineTo(i,I),r.lineTo(i,a),r.lineTo(_,a),r.closePath(),r.clip()}if(!N){var V="meet"===O&&A===T,L="slice"===O&&R===T,B="meet"===O&&A===k,j="slice"===O&&R===k;S.startsWith("xMid")&&(V||L)&&r.translate(i/2-P/2,0),S.endsWith("YMid")&&(B||j)&&r.translate(0,a/2-E/2),S.startsWith("xMax")&&(V||L)&&r.translate(i-P,0),S.endsWith("YMax")&&(B||j)&&r.translate(0,a-E)}switch(!0){case"none"===S:r.scale(k,T);break;case"meet"===O:r.scale(A,A);break;case"slice"===O:r.scale(R,R)}r.translate(-c,-f)}},{key:"start",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.enableRedraw,i=void 0!==n&&n,o=r.ignoreMouse,a=void 0!==o&&o,u=r.ignoreAnimation,s=void 0!==u&&u,c=r.ignoreDimensions,l=void 0!==c&&c,f=r.ignoreClear,h=void 0!==f&&f,p=r.forceRedraw,v=r.scaleWidth,y=r.scaleHeight,d=r.offsetX,g=r.offsetY,m=this.FRAMERATE,x=this.mouse,b=1e3/m;if(this.frameDuration=b,this.readyPromise=new Promise((function(t){e.resolveReady=t})),this.isReady()&&this.render(t,l,h,v,y,d,g),i){var w=Date.now(),S=w,O=0,k=function r(){w=Date.now(),(O=w-S)>=b&&(S=w-O%b,e.shouldUpdate(s,p)&&(e.render(t,l,h,v,y,d,g),x.runEvents())),e.intervalId=Na(r)};a||x.start(),this.intervalId=Na(k)}}},{key:"stop",value:function(){this.intervalId&&(Na.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}},{key:"shouldUpdate",value:function(t,e){if(!t){var r=this.frameDuration,n=this.animations.reduce((function(t,e){return e.update(r)||t}),!1);if(n)return!0}return!("function"!=typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}},{key:"render",value:function(t,e,r,n,i,o,a){var u=this.CLIENT_WIDTH,s=this.CLIENT_HEIGHT,c=this.viewPort,l=this.ctx,f=this.isFirstRender,h=l.canvas;c.clear(),h.width&&h.height?c.setCurrent(h.width,h.height):c.setCurrent(u,s);var p=t.getStyle("width"),v=t.getStyle("height");!e&&(f||"number"!=typeof n&&"number"!=typeof i)&&(p.hasValue()&&(h.width=p.getPixels("x"),h.style&&(h.style.width="".concat(h.width,"px"))),v.hasValue()&&(h.height=v.getPixels("y"),h.style&&(h.style.height="".concat(h.height,"px"))));var y=h.clientWidth||h.width,d=h.clientHeight||h.height;if(e&&p.hasValue()&&v.hasValue()&&(y=p.getPixels("x"),d=v.getPixels("y")),c.setCurrent(y,d),"number"==typeof o&&t.getAttribute("x",!0).setValue(o),"number"==typeof a&&t.getAttribute("y",!0).setValue(a),"number"==typeof n||"number"==typeof i){var g=Yi(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof n){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/n:isNaN(g[2])||(m=g[2]/n)}if("number"==typeof i){var w=t.getStyle("height");w.hasValue()?x=w.getPixels("y")/i:isNaN(g[3])||(x=g[3]/i)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(n),t.getAttribute("height",!0).setValue(i);var S=t.getStyle("transform",!0,!0);S.setValue("".concat(S.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||l.clearRect(0,0,y,d),t.render(l),f&&(this.isFirstRender=!1)}}]),t}();ru.defaultWindow=tu,ru.defaultFetch=eu;var nu=ru.defaultFetch,iu="undefined"!=typeof DOMParser?DOMParser:null,ou=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.fetch,n=void 0===r?nu:r,i=e.DOMParser,o=void 0===i?iu:i;Po(this,t),this.fetch=n,this.DOMParser=o}var e,r;return Eo(t,[{key:"parse",value:(r=gn(dn.mark((function t(e){return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.startsWith("<")){t.next=2;break}return t.abrupt("return",this.parseFromString(e));case 2:return t.abrupt("return",this.load(e));case 3:case"end":return t.stop()}}),t,this)}))),function(t){return r.apply(this,arguments)})},{key:"parseFromString",value:function(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}},{key:"checkDocument",value:function(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}},{key:"load",value:(e=gn(dn.mark((function t(e){var r,n;return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.fetch(e);case 2:return r=t.sent,t.next=5,r.text();case 5:return n=t.sent,t.abrupt("return",this.parseFromString(n));case 7:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})}]),t}(),au=n((function(t){function e(r,n){return t.exports=e=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},t.exports.default=t.exports,t.exports.__esModule=!0,e(r,n)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0})),uu=r(n((function(t){t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&au(t,e)},t.exports.default=t.exports,t.exports.__esModule=!0}))),su=n((function(t){function e(r){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?(t.exports=e=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=e=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),e(r)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0})),cu=n((function(t){t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.default=t.exports,t.exports.__esModule=!0})),lu=r(cu),fu=r(n((function(t){var e=su.default;t.exports=function(t,r){if(r&&("object"===e(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return cu(t)},t.exports.default=t.exports,t.exports.__esModule=!0}))),hu=n((function(t){function e(r){return t.exports=e=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.default=t.exports,t.exports.__esModule=!0,e(r)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0})),pu=r(hu),vu=function(t,e,r,n){try{return n?e(H(r)[0],r[1]):e(r)}catch(e){Ee(t,"throw",e)}},yu=!Fe((function(t){Array.from(t)}));fe({target:"Array",stat:!0,forced:yu},{from:function(t){var e=p(t),r=We(this),n=arguments.length,i=n>1?arguments[1]:void 0,o=void 0!==i;o&&(i=Te(i,n>2?arguments[2]:void 0,2));var a,u,s,c,l,f,h=Re(e),v=0;if(!h||this==Array&&ke(h))for(a=Gt(e),u=r?new this(a):Array(a);a>v;v++)f=o?i(e[v],v):e[v],Co(u,v,f);else for(l=(c=Pe(e,h)).next,u=r?new this:[];!(s=l.call(c)).done;v++)f=o?vu(c,i,[s.value,v],!0):s.value,Co(u,v,f);return u.length=v,u}});var du=I("unscopables"),gu=Array.prototype;null==gu[du]&&K.f(gu,du,{configurable:!0,value:ti(null)});var mu=function(t){gu[du][t]=!0},xu=qt.includes;fe({target:"Array",proto:!0},{includes:function(t){return xu(this,t,arguments.length>1?arguments[1]:void 0)}}),mu("includes");var bu=qt.indexOf,wu=[].indexOf,Su=!!wu&&1/[1].indexOf(1,-0)<0,Ou=Qi("indexOf");fe({target:"Array",proto:!0,forced:Su||!Ou},{indexOf:function(t){return Su?wu.apply(this,arguments)||0:bu(this,t,arguments.length>1?arguments[1]:void 0)}});var ku=kn.some,Tu=Qi("some");fe({target:"Array",proto:!0,forced:!Tu},{some:function(t){return ku(this,t,arguments.length>1?arguments[1]:void 0)}}),fe({target:"String",proto:!0,forced:!Li("includes")},{includes:function(t){return!!~En(h(this)).indexOf(En(Ii(t)),arguments.length>1?arguments[1]:void 0)}});var Au,Ru,Pu,Eu=!P((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Cu=ct("IE_PROTO"),Mu=Object.prototype,Nu=Eu?Object.getPrototypeOf:function(t){var e=p(t);if(y(e,Cu))return e[Cu];var r=e.constructor;return x(r)&&e instanceof r?r.prototype:e instanceof Object?Mu:null},_u=I("iterator"),Iu=!1;[].keys&&("next"in(Pu=[].keys())?(Ru=Nu(Nu(Pu)))!==Object.prototype&&(Au=Ru):Iu=!0),(null==Au||P((function(){var t={};return Au[_u].call(t)!==t})))&&(Au={}),x(Au[_u])||Ot(Au,_u,(function(){return this}));var Vu={IteratorPrototype:Au,BUGGY_SAFARI_ITERATORS:Iu},Lu=Vu.IteratorPrototype,Bu=function(){return this},ju=St.PROPER,Du=St.CONFIGURABLE,Fu=Vu.IteratorPrototype,zu=Vu.BUGGY_SAFARI_ITERATORS,Uu=I("iterator"),Hu="keys",Xu="values",Yu="entries",Gu=function(){return this},Wu=function(t,e,r,n,i,o,a){!function(t,e,r){var n=e+" Iterator";t.prototype=ti(Lu,{next:J(1,r)}),ge(t,n,!1),we[n]=Bu}(r,e,n);var u,s,c,l=function(t){if(t===i&&y)return y;if(!zu&&t in p)return p[t];switch(t){case Hu:case Xu:case Yu:return function(){return new r(this,t)}}return function(){return new r(this)}},f=e+" Iterator",h=!1,p=t.prototype,v=p[Uu]||p["@@iterator"]||i&&p[i],y=!zu&&v||l(i),d="Array"==e&&p.entries||v;if(d&&(u=Nu(d.call(new t)))!==Object.prototype&&u.next&&(Nu(u)!==Fu&&(ve?ve(u,Fu):x(u[Uu])||Ot(u,Uu,Gu)),ge(u,f,!0)),ju&&i==Xu&&v&&v.name!==Xu&&(Du?tt(p,"name",Xu):(h=!0,y=function(){return v.call(this)})),i)if(s={values:l(Xu),keys:o?y:l(Hu),entries:l(Yu)},a)for(c in s)(zu||h||!(c in p))&&Ot(p,c,s[c]);else fe({target:e,proto:!0,forced:zu||h},s);return p[Uu]!==y&&Ot(p,Uu,y,{name:i}),we[e]=y,s},qu=gi.charAt,$u="String Iterator",Qu=mt.set,Zu=mt.getterFor($u);Wu(String,"String",(function(t){Qu(this,{type:$u,string:En(t),index:0})}),(function(){var t,e=Zu(this),r=e.string,n=e.index;return n>=r.length?{value:void 0,done:!0}:(t=qu(r,n),e.index+=t.length,{value:t,done:!1})}));var Ku=n((function(t){t.exports=function(t){if(Array.isArray(t))return ro(t)},t.exports.default=t.exports,t.exports.__esModule=!0})),Ju=n((function(t){t.exports=function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},t.exports.default=t.exports,t.exports.__esModule=!0})),ts=n((function(t){t.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.default=t.exports,t.exports.__esModule=!0})),es=r(n((function(t){t.exports=function(t){return Ku(t)||Ju(t)||no(t)||ts()},t.exports.default=t.exports,t.exports.__esModule=!0}))),rs=function(){function t(e,r){Po(this,t),this.type="translate",this.point=null,this.point=Ka.parse(r)}return Eo(t,[{key:"apply",value:function(t){var e=this.point,r=e.x,n=e.y;t.translate(r||0,n||0)}},{key:"unapply",value:function(t){var e=this.point,r=e.x,n=e.y;t.translate(-1*r||0,-1*n||0)}},{key:"applyToPoint",value:function(t){var e=this.point,r=e.x,n=e.y;t.applyTransform([1,0,0,1,r||0,n||0])}}]),t}(),ns=function(){function t(e,r,n){Po(this,t),this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=Yi(r);this.angle=new Ua(e,"angle",i[0]),this.originX=n[0],this.originY=n[1],this.cx=i[1]||0,this.cy=i[2]||0}return Eo(t,[{key:"apply",value:function(t){var e=this.cx,r=this.cy,n=this.originX,i=this.originY,o=this.angle,a=e+n.getPixels("x"),u=r+i.getPixels("y");t.translate(a,u),t.rotate(o.getRadians()),t.translate(-a,-u)}},{key:"unapply",value:function(t){var e=this.cx,r=this.cy,n=this.originX,i=this.originY,o=this.angle,a=e+n.getPixels("x"),u=r+i.getPixels("y");t.translate(a,u),t.rotate(-1*o.getRadians()),t.translate(-a,-u)}},{key:"applyToPoint",value:function(t){var e=this.cx,r=this.cy,n=this.angle.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}]),t}(),is=function(){function t(e,r,n){Po(this,t),this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=Ka.parseScale(r);0!==i.x&&0!==i.y||(i.x=yo,i.y=yo),this.scale=i,this.originX=n[0],this.originY=n[1]}return Eo(t,[{key:"apply",value:function(t){var e=this.scale,r=e.x,n=e.y,i=this.originX,o=this.originY,a=i.getPixels("x"),u=o.getPixels("y");t.translate(a,u),t.scale(r,n||r),t.translate(-a,-u)}},{key:"unapply",value:function(t){var e=this.scale,r=e.x,n=e.y,i=this.originX,o=this.originY,a=i.getPixels("x"),u=o.getPixels("y");t.translate(a,u),t.scale(1/r,1/n||r),t.translate(-a,-u)}},{key:"applyToPoint",value:function(t){var e=this.scale,r=e.x,n=e.y;t.applyTransform([r||0,0,0,n||0,0,0])}}]),t}(),os=function(){function t(e,r,n){Po(this,t),this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=Yi(r),this.originX=n[0],this.originY=n[1]}return Eo(t,[{key:"apply",value:function(t){var e=this.originX,r=this.originY,n=this.matrix,i=e.getPixels("x"),o=r.getPixels("y");t.translate(i,o),t.transform(n[0],n[1],n[2],n[3],n[4],n[5]),t.translate(-i,-o)}},{key:"unapply",value:function(t){var e=this.originX,r=this.originY,n=this.matrix,i=n[0],o=n[2],a=n[4],u=n[1],s=n[3],c=n[5],l=1/(i*(1*s-0*c)-o*(1*u-0*c)+a*(0*u-0*s)),f=e.getPixels("x"),h=r.getPixels("y");t.translate(f,h),t.transform(l*(1*s-0*c),l*(0*c-1*u),l*(0*a-1*o),l*(1*i-0*a),l*(o*c-a*s),l*(a*u-i*c)),t.translate(-f,-h)}},{key:"applyToPoint",value:function(t){t.applyTransform(this.matrix)}}]),t}();function as(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var us=function(t){uu(r,t);var e=as(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="skew",o.angle=null,o.angle=new Ua(t,"angle",n),o}return r}(os);function ss(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var cs=function(t){uu(r,t);var e=ss(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="skewX",o.matrix=[1,0,Math.tan(o.angle.getRadians()),1,0,0],o}return r}(us);function ls(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var fs=function(t){uu(r,t);var e=ls(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="skewY",o.matrix=[1,Math.tan(o.angle.getRadians()),0,1,0,0],o}return r}(us);var hs=function(){function t(e,r,n){var i=this;Po(this,t),this.document=e,this.transforms=[],function(t){return Ui(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(r).forEach((function(e){if("none"!==e){var r=function(t){var e=t.split("("),r=oo(e,2),n=r[0],i=r[1];return[n.trim(),i.trim().replace(")","")]}(e),o=oo(r,2),a=o[0],u=o[1],s=t.transformTypes[a];void 0!==s&&i.transforms.push(new s(i.document,u,n))}}))}return Eo(t,[{key:"apply",value:function(t){for(var e=this.transforms,r=e.length,n=0;n<r;n++)e[n].apply(t)}},{key:"unapply",value:function(t){for(var e=this.transforms,r=e.length-1;r>=0;r--)e[r].unapply(t)}},{key:"applyToPoint",value:function(t){for(var e=this.transforms,r=e.length,n=0;n<r;n++)e[n].applyToPoint(t)}}],[{key:"fromElement",value:function(e,r){var n=r.getStyle("transform",!1,!0),i=r.getStyle("transform-origin",!1,!0).split(),o=oo(i,2),a=o[0],u=o[1],s=[a,void 0===u?a:u];return n.hasValue()?new t(e,n.getString(),s):null}}]),t}();hs.transformTypes={translate:rs,rotate:ns,scale:is,matrix:os,skewX:cs,skewY:fs};var ps=function(){function t(e,r){var n=this,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(Po(this,t),this.document=e,this.node=r,this.captureTextNodes=i,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],r&&1===r.nodeType){if(Array.from(r.attributes).forEach((function(t){var r=Wi(t.nodeName);n.attributes[r]=new Ua(e,r,t.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var o=this.getAttribute("style").getString().split(";").map((function(t){return t.trim()}));o.forEach((function(t){if(t){var r=t.split(":").map((function(t){return t.trim()})),i=oo(r,2),o=i[0],a=i[1];n.styles[o]=new Ua(e,o,a)}}))}var a=e.definitions,u=this.getAttribute("id");u.hasValue()&&(a[u.getString()]||(a[u.getString()]=this)),Array.from(r.childNodes).forEach((function(t){if(1===t.nodeType)n.addChild(t);else if(i&&(3===t.nodeType||4===t.nodeType)){var r=e.createTextNode(t);r.getText().length>0&&n.addChild(r)}}))}}return Eo(t,[{key:"getAttribute",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var n=new Ua(this.document,t,"");return this.attributes[t]=n,n}return r||Ua.empty(this.document)}},{key:"getHrefAttribute",value:function(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return Ua.empty(this.document)}},{key:"getStyle",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.styles[t];if(n)return n;var i=this.getAttribute(t);if(null!=i&&i.hasValue())return this.styles[t]=i,i;if(!r){var o=this.parent;if(o){var a=o.getStyle(t);if(null!=a&&a.hasValue())return a}}if(e){var u=new Ua(this.document,t,"");return this.styles[t]=u,u}return n||Ua.empty(this.document)}},{key:"render",value:function(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}},{key:"setContext",value:function(t){}},{key:"applyEffects",value:function(t){var e=hs.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var n=r.getDefinition();n&&n.apply(t)}}},{key:"clearContext",value:function(t){}},{key:"renderChildren",value:function(t){this.children.forEach((function(e){e.render(t)}))}},{key:"addChild",value:function(e){var r=e instanceof t?e:this.document.createElement(e);r.parent=this,t.ignoreChildTypes.includes(r.type)||this.children.push(r)}},{key:"matchesSelector",value:function(t){var e,r=this.node;if("function"==typeof r.matches)return r.matches(t);var n=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!n||""===n)&&n.split(" ").some((function(e){return".".concat(e)===t}))}},{key:"addStylesFromStyleDefinition",value:function(){var t=this.document,e=t.styles,r=t.stylesSpecificity;for(var n in e)if(!n.startsWith("@")&&this.matchesSelector(n)){var i=e[n],o=r[n];if(i)for(var a in i){var u=this.stylesSpecificity[a];void 0===u&&(u="000"),o>=u&&(this.styles[a]=i[a],this.stylesSpecificity[a]=o)}}}},{key:"removeStyles",value:function(t,e){return e.reduce((function(e,r){var n=t.getStyle(r);if(!n.hasValue())return e;var i=n.getString();return n.setValue(""),[].concat(es(e),[[r,i]])}),[])}},{key:"restoreStyles",value:function(t,e){e.forEach((function(e){var r=oo(e,2),n=r[0],i=r[1];t.getStyle(n,!0).setValue(i)}))}},{key:"isFirstChild",value:function(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}]),t}();function vs(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}ps.ignoreChildTypes=["title"];var ys=function(t){uu(r,t);var e=vs(r);function r(t,n,i){return Po(this,r),e.call(this,t,n,i)}return r}(ps),ds=[].reverse,gs=[1,2];fe({target:"Array",proto:!0,forced:String(gs)===String(gs.reverse())},{reverse:function(){return xn(this)&&(this.length=this.length),ds.call(this)}});var ms=function(t,e,r){var n,i;return ve&&x(n=e.constructor)&&n!==r&&j(i=n.prototype)&&i!==r.prototype&&ve(t,i),t},xs=1..valueOf,bs=function(t){return xs.call(t)},ws=Jt.f,Ss=Bt.f,Os=K.f,ks=Vn.trim,Ts="Number",As=u.Number,Rs=As.prototype,Ps=function(t){var e=$(t,"number");return"bigint"==typeof e?e:Es(e)},Es=function(t){var e,r,n,i,o,a,u,s,c=$(t,"number");if(X(c))throw TypeError("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(43===(e=(c=ks(c)).charCodeAt(0))||45===e){if(88===(r=c.charCodeAt(2))||120===r)return NaN}else if(48===e){switch(c.charCodeAt(1)){case 66:case 98:n=2,i=49;break;case 79:case 111:n=8,i=55;break;default:return+c}for(a=(o=c.slice(2)).length,u=0;u<a;u++)if((s=o.charCodeAt(u))<48||s>i)return NaN;return parseInt(o,n)}return+c};if(ce(Ts,!As(" 0o1")||!As("0b1")||As("+0x1"))){for(var Cs,Ms=function(t){var e=arguments.length<1?0:As(Ps(t)),r=this;return r instanceof Ms&&P((function(){bs(r)}))?ms(Object(e),r,Ms):e},Ns=B?ws(As):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),_s=0;Ns.length>_s;_s++)y(As,Cs=Ns[_s])&&!y(Ms,Cs)&&Os(Ms,Cs,Ss(As,Cs));Ms.prototype=Rs,Rs.constructor=Ms,Ot(u,Ts,Ms)}var Is=n((function(t){t.exports=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=hu(t)););return t},t.exports.default=t.exports,t.exports.__esModule=!0})),Vs=r(n((function(t){function e(r,n,i){return"undefined"!=typeof Reflect&&Reflect.get?(t.exports=e=Reflect.get,t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=e=function(t,e,r){var n=Is(t,e);if(n){var i=Object.getOwnPropertyDescriptor(n,e);return i.get?i.get.call(r):i.value}},t.exports.default=t.exports,t.exports.__esModule=!0),e(r,n,i||r)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0})));function Ls(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function Bs(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function js(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}var Ds=function(){function t(e,r,n,i,o,a){Po(this,t);var u=a?"string"==typeof a?t.parse(a):a:{};this.fontFamily=o||u.fontFamily,this.fontSize=i||u.fontSize,this.fontStyle=e||u.fontStyle,this.fontWeight=n||u.fontWeight,this.fontVariant=r||u.fontVariant}return Eo(t,[{key:"toString",value:function(){return[Bs(this.fontStyle),this.fontVariant,js(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"==typeof process?t:t.trim().split(",").map(Ls).join(","))].join(" ").trim();var t}}],[{key:"parse",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1?arguments[1]:void 0,n="",i="",o="",a="",u="",s=Ui(e).trim().split(" "),c={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return s.forEach((function(e){switch(!0){case!c.fontStyle&&t.styles.includes(e):"inherit"!==e&&(n=e),c.fontStyle=!0;break;case!c.fontVariant&&t.variants.includes(e):"inherit"!==e&&(i=e),c.fontStyle=!0,c.fontVariant=!0;break;case!c.fontWeight&&t.weights.includes(e):"inherit"!==e&&(o=e),c.fontStyle=!0,c.fontVariant=!0,c.fontWeight=!0;break;case!c.fontSize:if("inherit"!==e){var r=e.split("/"),s=oo(r,1);a=s[0]}c.fontStyle=!0,c.fontVariant=!0,c.fontWeight=!0,c.fontSize=!0;break;default:"inherit"!==e&&(u+=e)}})),new t(n,i,o,a,u,r)}}]),t}();Ds.styles="normal|italic|oblique|inherit",Ds.variants="normal|small-caps|inherit",Ds.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";var Fs=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;Po(this,t),this.x1=e,this.y1=r,this.x2=n,this.y2=i,this.addPoint(e,r),this.addPoint(n,i)}return Eo(t,[{key:"addPoint",value:function(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}},{key:"addX",value:function(t){this.addPoint(t,null)}},{key:"addY",value:function(t){this.addPoint(null,t)}},{key:"addBoundingBox",value:function(t){if(t){var e=t.x1,r=t.y1,n=t.x2,i=t.y2;this.addPoint(e,r),this.addPoint(n,i)}}},{key:"sumCubic",value:function(t,e,r,n,i){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*n+Math.pow(t,3)*i}},{key:"bezierCurveAdd",value:function(t,e,r,n,i){var o=6*e-12*r+6*n,a=-3*e+9*r-9*n+3*i,u=3*r-3*e;if(0!==a){var s=Math.pow(o,2)-4*u*a;if(!(s<0)){var c=(-o+Math.sqrt(s))/(2*a);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,n,i)):this.addY(this.sumCubic(c,e,r,n,i)));var l=(-o-Math.sqrt(s))/(2*a);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,n,i)):this.addY(this.sumCubic(l,e,r,n,i)))}}else{if(0===o)return;var f=-u/o;0<f&&f<1&&(t?this.addX(this.sumCubic(f,e,r,n,i)):this.addY(this.sumCubic(f,e,r,n,i)))}}},{key:"addBezierCurve",value:function(t,e,r,n,i,o,a,u){this.addPoint(t,e),this.addPoint(a,u),this.bezierCurveAdd(!0,t,r,i,a),this.bezierCurveAdd(!1,e,n,o,u)}},{key:"addQuadraticCurve",value:function(t,e,r,n,i,o){var a=t+2/3*(r-t),u=e+2/3*(n-e),s=a+1/3*(i-t),c=u+1/3*(o-e);this.addBezierCurve(t,e,a,s,u,c,i,o)}},{key:"isPointInBox",value:function(t,e){var r=this.x1,n=this.y1,i=this.x2,o=this.y2;return r<=t&&t<=i&&n<=e&&e<=o}},{key:"x",get:function(){return this.x1}},{key:"y",get:function(){return this.y1}},{key:"width",get:function(){return this.x2-this.x1}},{key:"height",get:function(){return this.y2-this.y1}}]),t}();fe({target:"Array",proto:!0},{fill:function(t){for(var e=p(this),r=Gt(e),n=arguments.length,i=Ht(n>1?arguments[1]:void 0,r),o=n>2?arguments[2]:void 0,a=void 0===o?r:Ht(o,r);a>i;)e[i++]=t;return e}}),mu("fill");
/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */
var zs=function(t,e){return(zs=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function Us(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}zs(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function Hs(t,e){var r=t[0],n=t[1];return[r*Math.cos(e)-n*Math.sin(e),r*Math.sin(e)+n*Math.cos(e)]}function Xs(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var Ys=Math.PI;function Gs(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var n=t.rX,i=t.rY,o=t.x,a=t.y;n=Math.abs(t.rX),i=Math.abs(t.rY);var u=Hs([(e-o)/2,(r-a)/2],-t.xRot/180*Ys),s=u[0],c=u[1],l=Math.pow(s,2)/Math.pow(n,2)+Math.pow(c,2)/Math.pow(i,2);1<l&&(n*=Math.sqrt(l),i*=Math.sqrt(l)),t.rX=n,t.rY=i;var f=Math.pow(n,2)*Math.pow(c,2)+Math.pow(i,2)*Math.pow(s,2),h=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(n,2)*Math.pow(i,2)-f)/f)),p=n*c/i*h,v=-i*s/n*h,y=Hs([p,v],t.xRot/180*Ys);t.cX=y[0]+(e+o)/2,t.cY=y[1]+(r+a)/2,t.phi1=Math.atan2((c-v)/i,(s-p)/n),t.phi2=Math.atan2((-c-v)/i,(-s-p)/n),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*Ys),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*Ys),t.phi1*=180/Ys,t.phi2*=180/Ys}function Ws(t,e,r){Xs(t,e,r);var n=t*t+e*e-r*r;if(0>n)return[];if(0===n)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var i=Math.sqrt(n);return[[(t*r+e*i)/(t*t+e*e),(e*r-t*i)/(t*t+e*e)],[(t*r-e*i)/(t*t+e*e),(e*r+t*i)/(t*t+e*e)]]}var qs,$s=Math.PI/180;function Qs(t,e,r){return(1-r)*t+r*e}function Zs(t,e,r,n){return t+Math.cos(n/180*Ys)*e+Math.sin(n/180*Ys)*r}function Ks(t,e,r,n){var i=1e-6,o=e-t,a=r-e,u=3*o+3*(n-r)-6*a,s=6*(a-o),c=3*o;return Math.abs(u)<i?[-c/s]:function(t,e,r){void 0===r&&(r=1e-6);var n=t*t/4-e;if(n<-r)return[];if(n<=r)return[-t/2];var i=Math.sqrt(n);return[-t/2-i,-t/2+i]}(s/u,c/u,i)}function Js(t,e,r,n,i){var o=1-i;return t*(o*o*o)+e*(3*o*o*i)+r*(3*o*i*i)+n*(i*i*i)}!function(t){function e(){return i((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,n=NaN;return i((function(i,o,a){return i.type&oc.SMOOTH_CURVE_TO&&(i.type=oc.CURVE_TO,t=isNaN(t)?o:t,e=isNaN(e)?a:e,i.x1=i.relative?o-t:2*o-t,i.y1=i.relative?a-e:2*a-e),i.type&oc.CURVE_TO?(t=i.relative?o+i.x2:i.x2,e=i.relative?a+i.y2:i.y2):(t=NaN,e=NaN),i.type&oc.SMOOTH_QUAD_TO&&(i.type=oc.QUAD_TO,r=isNaN(r)?o:r,n=isNaN(n)?a:n,i.x1=i.relative?o-r:2*o-r,i.y1=i.relative?a-n:2*a-n),i.type&oc.QUAD_TO?(r=i.relative?o+i.x1:i.x1,n=i.relative?a+i.y1:i.y1):(r=NaN,n=NaN),i}))}function n(){var t=NaN,e=NaN;return i((function(r,n,i){if(r.type&oc.SMOOTH_QUAD_TO&&(r.type=oc.QUAD_TO,t=isNaN(t)?n:t,e=isNaN(e)?i:e,r.x1=r.relative?n-t:2*n-t,r.y1=r.relative?i-e:2*i-e),r.type&oc.QUAD_TO){t=r.relative?n+r.x1:r.x1,e=r.relative?i+r.y1:r.y1;var o=r.x1,a=r.y1;r.type=oc.CURVE_TO,r.x1=((r.relative?0:n)+2*o)/3,r.y1=((r.relative?0:i)+2*a)/3,r.x2=(r.x+2*o)/3,r.y2=(r.y+2*a)/3}else t=NaN,e=NaN;return r}))}function i(t){var e=0,r=0,n=NaN,i=NaN;return function(o){if(isNaN(n)&&!(o.type&oc.MOVE_TO))throw new Error("path must start with moveto");var a=t(o,e,r,n,i);return o.type&oc.CLOSE_PATH&&(e=n,r=i),void 0!==o.x&&(e=o.relative?e+o.x:o.x),void 0!==o.y&&(r=o.relative?r+o.y:o.y),o.type&oc.MOVE_TO&&(n=e,i=r),a}}function o(t,e,r,n,o,a){return Xs(t,e,r,n,o,a),i((function(i,u,s,c){var l=i.x1,f=i.x2,h=i.relative&&!isNaN(c),p=void 0!==i.x?i.x:h?0:u,v=void 0!==i.y?i.y:h?0:s;function y(t){return t*t}i.type&oc.HORIZ_LINE_TO&&0!==e&&(i.type=oc.LINE_TO,i.y=i.relative?0:s),i.type&oc.VERT_LINE_TO&&0!==r&&(i.type=oc.LINE_TO,i.x=i.relative?0:u),void 0!==i.x&&(i.x=i.x*t+v*r+(h?0:o)),void 0!==i.y&&(i.y=p*e+i.y*n+(h?0:a)),void 0!==i.x1&&(i.x1=i.x1*t+i.y1*r+(h?0:o)),void 0!==i.y1&&(i.y1=l*e+i.y1*n+(h?0:a)),void 0!==i.x2&&(i.x2=i.x2*t+i.y2*r+(h?0:o)),void 0!==i.y2&&(i.y2=f*e+i.y2*n+(h?0:a));var d=t*n-e*r;if(void 0!==i.xRot&&(1!==t||0!==e||0!==r||1!==n))if(0===d)delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag,i.type=oc.LINE_TO;else{var g=i.xRot*Math.PI/180,m=Math.sin(g),x=Math.cos(g),b=1/y(i.rX),w=1/y(i.rY),S=y(x)*b+y(m)*w,O=2*m*x*(b-w),k=y(m)*b+y(x)*w,T=S*n*n-O*e*n+k*e*e,A=O*(t*n+e*r)-2*(S*r*n+k*t*e),R=S*r*r-O*t*r+k*t*t,P=(Math.atan2(A,T-R)+Math.PI)%Math.PI/2,E=Math.sin(P),C=Math.cos(P);i.rX=Math.abs(d)/Math.sqrt(T*y(C)+A*E*C+R*y(E)),i.rY=Math.abs(d)/Math.sqrt(T*y(E)-A*E*C+R*y(C)),i.xRot=180*P/Math.PI}return void 0!==i.sweepFlag&&0>d&&(i.sweepFlag=+!i.sweepFlag),i}))}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),Xs(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return i((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),i((function(n,i,o,a,u){if(isNaN(a)&&!(n.type&oc.MOVE_TO))throw new Error("path must start with moveto");return e&&n.type&oc.HORIZ_LINE_TO&&(n.type=oc.LINE_TO,n.y=n.relative?0:o),r&&n.type&oc.VERT_LINE_TO&&(n.type=oc.LINE_TO,n.x=n.relative?0:i),t&&n.type&oc.CLOSE_PATH&&(n.type=oc.LINE_TO,n.x=n.relative?a-i:a,n.y=n.relative?u-o:u),n.type&oc.ARC&&(0===n.rX||0===n.rY)&&(n.type=oc.LINE_TO,delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag),n}))},t.NORMALIZE_ST=r,t.QT_TO_C=n,t.INFO=i,t.SANITIZE=function(t){void 0===t&&(t=0),Xs(t);var e=NaN,r=NaN,n=NaN,o=NaN;return i((function(i,a,u,s,c){var l=Math.abs,f=!1,h=0,p=0;if(i.type&oc.SMOOTH_CURVE_TO&&(h=isNaN(e)?0:a-e,p=isNaN(r)?0:u-r),i.type&(oc.CURVE_TO|oc.SMOOTH_CURVE_TO)?(e=i.relative?a+i.x2:i.x2,r=i.relative?u+i.y2:i.y2):(e=NaN,r=NaN),i.type&oc.SMOOTH_QUAD_TO?(n=isNaN(n)?a:2*a-n,o=isNaN(o)?u:2*u-o):i.type&oc.QUAD_TO?(n=i.relative?a+i.x1:i.x1,o=i.relative?u+i.y1:i.y2):(n=NaN,o=NaN),i.type&oc.LINE_COMMANDS||i.type&oc.ARC&&(0===i.rX||0===i.rY||!i.lArcFlag)||i.type&oc.CURVE_TO||i.type&oc.SMOOTH_CURVE_TO||i.type&oc.QUAD_TO||i.type&oc.SMOOTH_QUAD_TO){var v=void 0===i.x?0:i.relative?i.x:i.x-a,y=void 0===i.y?0:i.relative?i.y:i.y-u;h=isNaN(n)?void 0===i.x1?h:i.relative?i.x:i.x1-a:n-a,p=isNaN(o)?void 0===i.y1?p:i.relative?i.y:i.y1-u:o-u;var d=void 0===i.x2?0:i.relative?i.x:i.x2-a,g=void 0===i.y2?0:i.relative?i.y:i.y2-u;l(v)<=t&&l(y)<=t&&l(h)<=t&&l(p)<=t&&l(d)<=t&&l(g)<=t&&(f=!0)}return i.type&oc.CLOSE_PATH&&l(a-s)<=t&&l(u-c)<=t&&(f=!0),f?[]:i}))},t.MATRIX=o,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),Xs(t,e,r);var n=Math.sin(t),i=Math.cos(t);return o(i,n,-n,i,e-e*i+r*n,r-e*n-r*i)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),Xs(t,e),o(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),Xs(t,e),o(t,0,0,e,0,0)},t.SKEW_X=function(t){return Xs(t),o(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return Xs(t),o(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),Xs(t),o(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),Xs(t),o(1,0,0,-1,0,t)},t.A_TO_C=function(){return i((function(t,e,r){return oc.ARC===t.type?function(t,e,r){var n,i,o,a;t.cX||Gs(t,e,r);for(var u=Math.min(t.phi1,t.phi2),s=Math.max(t.phi1,t.phi2)-u,c=Math.ceil(s/90),l=new Array(c),f=e,h=r,p=0;p<c;p++){var v=Qs(t.phi1,t.phi2,p/c),y=Qs(t.phi1,t.phi2,(p+1)/c),d=y-v,g=4/3*Math.tan(d*$s/4),m=[Math.cos(v*$s)-g*Math.sin(v*$s),Math.sin(v*$s)+g*Math.cos(v*$s)],x=m[0],b=m[1],w=[Math.cos(y*$s),Math.sin(y*$s)],S=w[0],O=w[1],k=[S+g*Math.sin(y*$s),O-g*Math.cos(y*$s)],T=k[0],A=k[1];l[p]={relative:t.relative,type:oc.CURVE_TO};var R=function(e,r){var n=Hs([e*t.rX,r*t.rY],t.xRot),i=n[0],o=n[1];return[t.cX+i,t.cY+o]};n=R(x,b),l[p].x1=n[0],l[p].y1=n[1],i=R(T,A),l[p].x2=i[0],l[p].y2=i[1],o=R(S,O),l[p].x=o[0],l[p].y=o[1],t.relative&&(l[p].x1-=f,l[p].y1-=h,l[p].x2-=f,l[p].y2-=h,l[p].x-=f,l[p].y-=h),f=(a=[l[p].x,l[p].y])[0],h=a[1]}return l}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return i((function(t,e,r){return t.relative&&(e=0,r=0),oc.ARC===t.type&&Gs(t,e,r),t}))},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),o=n(),a=r(),u=i((function(e,r,n){var i=a(o(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function s(t){t>u.maxX&&(u.maxX=t),t<u.minX&&(u.minX=t)}function c(t){t>u.maxY&&(u.maxY=t),t<u.minY&&(u.minY=t)}if(i.type&oc.DRAWING_COMMANDS&&(s(r),c(n)),i.type&oc.HORIZ_LINE_TO&&s(i.x),i.type&oc.VERT_LINE_TO&&c(i.y),i.type&oc.LINE_TO&&(s(i.x),c(i.y)),i.type&oc.CURVE_TO){s(i.x),c(i.y);for(var l=0,f=Ks(r,i.x1,i.x2,i.x);l<f.length;l++)0<(R=f[l])&&1>R&&s(Js(r,i.x1,i.x2,i.x,R));for(var h=0,p=Ks(n,i.y1,i.y2,i.y);h<p.length;h++)0<(R=p[h])&&1>R&&c(Js(n,i.y1,i.y2,i.y,R))}if(i.type&oc.ARC){s(i.x),c(i.y),Gs(i,r,n);for(var v=i.xRot/180*Math.PI,y=Math.cos(v)*i.rX,d=Math.sin(v)*i.rX,g=-Math.sin(v)*i.rY,m=Math.cos(v)*i.rY,x=i.phi1<i.phi2?[i.phi1,i.phi2]:-180>i.phi2?[i.phi2+360,i.phi1+360]:[i.phi2,i.phi1],b=x[0],w=x[1],S=function(t){var e=t[0],r=t[1],n=180*Math.atan2(r,e)/Math.PI;return n<b?n+360:n},O=0,k=Ws(g,-y,0).map(S);O<k.length;O++)(R=k[O])>b&&R<w&&s(Zs(i.cX,y,g,R));for(var T=0,A=Ws(m,-d,0).map(S);T<A.length;T++){var R;(R=A[T])>b&&R<w&&c(Zs(i.cY,d,m,R))}}return e}));return u.minX=1/0,u.maxX=-1/0,u.minY=1/0,u.maxY=-1/0,u}}(qs||(qs={}));var tc,ec=function(){function t(){}return t.prototype.round=function(t){return this.transform(qs.ROUND(t))},t.prototype.toAbs=function(){return this.transform(qs.TO_ABS())},t.prototype.toRel=function(){return this.transform(qs.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(qs.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(qs.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(qs.QT_TO_C())},t.prototype.aToC=function(){return this.transform(qs.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(qs.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(qs.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(qs.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(qs.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,n,i,o){return this.transform(qs.MATRIX(t,e,r,n,i,o))},t.prototype.skewX=function(t){return this.transform(qs.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(qs.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(qs.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(qs.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(qs.ANNOTATE_ARCS())},t}(),rc=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},nc=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},ic=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return Us(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var n=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},i=0;i<t.length;i++){var o=t[i],a=!(this.curCommandType!==oc.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),u=nc(o)&&("0"===this.curNumber&&"0"===o||a);if(!nc(o)||u)if("e"!==o&&"E"!==o)if("-"!==o&&"+"!==o||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==o||this.curNumberHasExp||this.curNumberHasDecimal||a){if(this.curNumber&&-1!==this.curCommandType){var s=Number(this.curNumber);if(isNaN(s))throw new SyntaxError("Invalid number ending at "+i);if(this.curCommandType===oc.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>s)throw new SyntaxError('Expected positive number, got "'+s+'" at index "'+i+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+i+'"');this.curArgs.push(s),this.curArgs.length===ac[this.curCommandType]&&(oc.HORIZ_LINE_TO===this.curCommandType?n({type:oc.HORIZ_LINE_TO,relative:this.curCommandRelative,x:s}):oc.VERT_LINE_TO===this.curCommandType?n({type:oc.VERT_LINE_TO,relative:this.curCommandRelative,y:s}):this.curCommandType===oc.MOVE_TO||this.curCommandType===oc.LINE_TO||this.curCommandType===oc.SMOOTH_QUAD_TO?(n({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),oc.MOVE_TO===this.curCommandType&&(this.curCommandType=oc.LINE_TO)):this.curCommandType===oc.CURVE_TO?n({type:oc.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===oc.SMOOTH_CURVE_TO?n({type:oc.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===oc.QUAD_TO?n({type:oc.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===oc.ARC&&n({type:oc.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!rc(o))if(","===o&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==o&&"-"!==o&&"."!==o)if(u)this.curNumber=o,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+i+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+o+'" at index '+i+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==o&&"Z"!==o)if("h"===o||"H"===o)this.curCommandType=oc.HORIZ_LINE_TO,this.curCommandRelative="h"===o;else if("v"===o||"V"===o)this.curCommandType=oc.VERT_LINE_TO,this.curCommandRelative="v"===o;else if("m"===o||"M"===o)this.curCommandType=oc.MOVE_TO,this.curCommandRelative="m"===o;else if("l"===o||"L"===o)this.curCommandType=oc.LINE_TO,this.curCommandRelative="l"===o;else if("c"===o||"C"===o)this.curCommandType=oc.CURVE_TO,this.curCommandRelative="c"===o;else if("s"===o||"S"===o)this.curCommandType=oc.SMOOTH_CURVE_TO,this.curCommandRelative="s"===o;else if("q"===o||"Q"===o)this.curCommandType=oc.QUAD_TO,this.curCommandRelative="q"===o;else if("t"===o||"T"===o)this.curCommandType=oc.SMOOTH_QUAD_TO,this.curCommandRelative="t"===o;else{if("a"!==o&&"A"!==o)throw new SyntaxError('Unexpected character "'+o+'" at index '+i+".");this.curCommandType=oc.ARC,this.curCommandRelative="a"===o}else e.push({type:oc.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=o,this.curNumberHasDecimal="."===o}else this.curNumber+=o,this.curNumberHasDecimal=!0;else this.curNumber+=o;else this.curNumber+=o,this.curNumberHasExp=!0;else this.curNumber+=o,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var n=0,i=Object.getPrototypeOf(this).parse.call(this,e);n<i.length;n++){var o=i[n],a=t(o);Array.isArray(a)?r.push.apply(r,a):r.push(a)}return r}}})},e}(ec),oc=function(t){function e(r){var n=t.call(this)||this;return n.commands="string"==typeof r?e.parse(r):r,n}return Us(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=qs.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,n=this.commands;r<n.length;r++){var i=t(n[r]);Array.isArray(i)?e.push.apply(e,i):e.push(i)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var n=t[r];if(n.type===oc.CLOSE_PATH)e+="z";else if(n.type===oc.HORIZ_LINE_TO)e+=(n.relative?"h":"H")+n.x;else if(n.type===oc.VERT_LINE_TO)e+=(n.relative?"v":"V")+n.y;else if(n.type===oc.MOVE_TO)e+=(n.relative?"m":"M")+n.x+" "+n.y;else if(n.type===oc.LINE_TO)e+=(n.relative?"l":"L")+n.x+" "+n.y;else if(n.type===oc.CURVE_TO)e+=(n.relative?"c":"C")+n.x1+" "+n.y1+" "+n.x2+" "+n.y2+" "+n.x+" "+n.y;else if(n.type===oc.SMOOTH_CURVE_TO)e+=(n.relative?"s":"S")+n.x2+" "+n.y2+" "+n.x+" "+n.y;else if(n.type===oc.QUAD_TO)e+=(n.relative?"q":"Q")+n.x1+" "+n.y1+" "+n.x+" "+n.y;else if(n.type===oc.SMOOTH_QUAD_TO)e+=(n.relative?"t":"T")+n.x+" "+n.y;else{if(n.type!==oc.ARC)throw new Error('Unexpected command type "'+n.type+'" at index '+r+".");e+=(n.relative?"a":"A")+n.rX+" "+n.rY+" "+n.xRot+" "+ +n.lArcFlag+" "+ +n.sweepFlag+" "+n.x+" "+n.y}}return e}(t)},e.parse=function(t){var e=new ic,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(ec),ac=((tc={})[oc.MOVE_TO]=2,tc[oc.LINE_TO]=2,tc[oc.HORIZ_LINE_TO]=1,tc[oc.VERT_LINE_TO]=1,tc[oc.CLOSE_PATH]=0,tc[oc.QUAD_TO]=4,tc[oc.SMOOTH_QUAD_TO]=2,tc[oc.CURVE_TO]=6,tc[oc.SMOOTH_CURVE_TO]=4,tc[oc.ARC]=7,tc);function uc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var sc=function(t){uu(r,t);var e=uc(r);function r(t){var n;return Po(this,r),(n=e.call(this,t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,""))).control=null,n.start=null,n.current=null,n.command=null,n.commands=n.commands,n.i=-1,n.previousCommand=null,n.points=[],n.angles=[],n}return Eo(r,[{key:"reset",value:function(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new Ka(0,0),this.control=new Ka(0,0),this.current=new Ka(0,0),this.points=[],this.angles=[]}},{key:"isEnd",value:function(){return this.i>=this.commands.length-1}},{key:"next",value:function(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}},{key:"getPoint",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new Ka(this.command[t],this.command[e]);return this.makeAbsolute(r)}},{key:"getAsControlPoint",value:function(t,e){var r=this.getPoint(t,e);return this.control=r,r}},{key:"getAsCurrentPoint",value:function(t,e){var r=this.getPoint(t,e);return this.current=r,r}},{key:"getReflectedControlPoint",value:function(){var t=this.previousCommand.type;if(t!==oc.CURVE_TO&&t!==oc.SMOOTH_CURVE_TO&&t!==oc.QUAD_TO&&t!==oc.SMOOTH_QUAD_TO)return this.current;var e=this.current,r=e.x,n=e.y,i=this.control,o=i.x,a=i.y;return new Ka(2*r-o,2*n-a)}},{key:"makeAbsolute",value:function(t){if(this.command.relative){var e=this.current,r=e.x,n=e.y;t.x+=r,t.y+=n}return t}},{key:"addMarker",value:function(t,e,r){var n=this.points,i=this.angles;r&&i.length>0&&!i[i.length-1]&&(i[i.length-1]=n[n.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}},{key:"addMarkerAngle",value:function(t,e){this.points.push(t),this.angles.push(e)}},{key:"getMarkerPoints",value:function(){return this.points}},{key:"getMarkerAngles",value:function(){for(var t=this.angles,e=t.length,r=0;r<e;r++)if(!t[r])for(var n=r+1;n<e;n++)if(t[n]){t[r]=t[n];break}return t}}]),r}(oc),cc=St.PROPER,lc="toString",fc=RegExp.prototype,hc=fc.toString,pc=P((function(){return"/a/b"!=hc.call({source:"a",flags:"b"})})),vc=cc&&hc.name!=lc;function yc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}(pc||vc)&&Ot(RegExp.prototype,lc,(function(){var t=H(this),e=En(t.source),r=t.flags;return"/"+e+"/"+En(void 0===r&&t instanceof RegExp&&!("flags"in fc)?Un.call(t):r)}),{unsafe:!0});var dc=function(t){uu(r,t);var e=yc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).modifiedEmSizeStack=!1,t}return Eo(r,[{key:"calculateOpacity",value:function(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}},{key:"setContext",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),n=this.getStyle("fill-opacity"),i=this.getStyle("stroke"),o=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var a=r.getFillStyleDefinition(this,n);a&&(t.fillStyle=a)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var u=r.getColor();"inherit"!==u&&(t.fillStyle="none"===u?"rgba(0,0,0,0)":u)}if(n.hasValue()){var s=new Ua(this.document,"fill",t.fillStyle).addOpacity(n).getColor();t.fillStyle=s}if(i.isUrlDefinition()){var c=i.getFillStyleDefinition(this,o);c&&(t.strokeStyle=c)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var l=i.getString();"inherit"!==l&&(t.strokeStyle="none"===l?"rgba(0,0,0,0)":l)}if(o.hasValue()){var f=new Ua(this.document,"stroke",t.strokeStyle).addOpacity(o).getString();t.strokeStyle=f}var h=this.getStyle("stroke-width");if(h.hasValue()){var p=h.getPixels();t.lineWidth=p||yo}var v=this.getStyle("stroke-linecap"),y=this.getStyle("stroke-linejoin"),d=this.getStyle("stroke-miterlimit"),g=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(v.hasValue()&&(t.lineCap=v.getString()),y.hasValue()&&(t.lineJoin=y.getString()),d.hasValue()&&(t.miterLimit=d.getNumber()),g.hasValue()&&"none"!==g.getString()){var x=Yi(g.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),S=this.getStyle("font-style"),O=this.getStyle("font-variant"),k=this.getStyle("font-weight"),T=this.getStyle("font-size"),A=this.getStyle("font-family"),R=new Ds(S.getString(),O.getString(),k.getString(),T.hasValue()?"".concat(T.getPixels(!0),"px"):"",A.getString(),Ds.parse(w.getString(),t.font));S.setValue(R.fontStyle),O.setValue(R.fontVariant),k.setValue(R.fontWeight),T.setValue(R.fontSize),A.setValue(R.fontFamily),t.font=R.toString(),T.isPixels()&&(this.document.emSize=T.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}},{key:"clearContext",value:function(t){Vs(pu(r.prototype),"clearContext",this).call(this,t),this.modifiedEmSizeStack&&this.document.popEmSize()}}]),r}(ps);function gc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var mc=function(t){uu(r,t);var e=gc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="path",o.pathParser=null,o.pathParser=new sc(o.getAttribute("d").getString()),o}return Eo(r,[{key:"path",value:function(t){var e=this.pathParser,r=new Fs;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case sc.MOVE_TO:this.pathM(t,r);break;case sc.LINE_TO:this.pathL(t,r);break;case sc.HORIZ_LINE_TO:this.pathH(t,r);break;case sc.VERT_LINE_TO:this.pathV(t,r);break;case sc.CURVE_TO:this.pathC(t,r);break;case sc.SMOOTH_CURVE_TO:this.pathS(t,r);break;case sc.QUAD_TO:this.pathQ(t,r);break;case sc.SMOOTH_QUAD_TO:this.pathT(t,r);break;case sc.ARC:this.pathA(t,r);break;case sc.CLOSE_PATH:this.pathZ(t,r)}return r}},{key:"getBoundingBox",value:function(t){return this.path()}},{key:"getMarkers",value:function(){var t=this.pathParser,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((function(t,e){return[t,r[e]]}))}},{key:"renderChildren",value:function(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var n=r.length-1,i=this.getStyle("marker-start"),o=this.getStyle("marker-mid"),a=this.getStyle("marker-end");if(i.isUrlDefinition()){var u=i.getDefinition(),s=oo(r[0],2),c=s[0],l=s[1];u.render(t,c,l)}if(o.isUrlDefinition())for(var f=o.getDefinition(),h=1;h<n;h++){var p=oo(r[h],2),v=p[0],y=p[1];f.render(t,v,y)}if(a.isUrlDefinition()){var d=a.getDefinition(),g=oo(r[n],2),m=g[0],x=g[1];d.render(t,m,x)}}}},{key:"pathM",value:function(t,e){var n=this.pathParser,i=r.pathM(n).point,o=i.x,a=i.y;n.addMarker(i),e.addPoint(o,a),t&&t.moveTo(o,a)}},{key:"pathL",value:function(t,e){var n=this.pathParser,i=r.pathL(n),o=i.current,a=i.point,u=a.x,s=a.y;n.addMarker(a,o),e.addPoint(u,s),t&&t.lineTo(u,s)}},{key:"pathH",value:function(t,e){var n=this.pathParser,i=r.pathH(n),o=i.current,a=i.point,u=a.x,s=a.y;n.addMarker(a,o),e.addPoint(u,s),t&&t.lineTo(u,s)}},{key:"pathV",value:function(t,e){var n=this.pathParser,i=r.pathV(n),o=i.current,a=i.point,u=a.x,s=a.y;n.addMarker(a,o),e.addPoint(u,s),t&&t.lineTo(u,s)}},{key:"pathC",value:function(t,e){var n=this.pathParser,i=r.pathC(n),o=i.current,a=i.point,u=i.controlPoint,s=i.currentPoint;n.addMarker(s,u,a),e.addBezierCurve(o.x,o.y,a.x,a.y,u.x,u.y,s.x,s.y),t&&t.bezierCurveTo(a.x,a.y,u.x,u.y,s.x,s.y)}},{key:"pathS",value:function(t,e){var n=this.pathParser,i=r.pathS(n),o=i.current,a=i.point,u=i.controlPoint,s=i.currentPoint;n.addMarker(s,u,a),e.addBezierCurve(o.x,o.y,a.x,a.y,u.x,u.y,s.x,s.y),t&&t.bezierCurveTo(a.x,a.y,u.x,u.y,s.x,s.y)}},{key:"pathQ",value:function(t,e){var n=this.pathParser,i=r.pathQ(n),o=i.current,a=i.controlPoint,u=i.currentPoint;n.addMarker(u,a,a),e.addQuadraticCurve(o.x,o.y,a.x,a.y,u.x,u.y),t&&t.quadraticCurveTo(a.x,a.y,u.x,u.y)}},{key:"pathT",value:function(t,e){var n=this.pathParser,i=r.pathT(n),o=i.current,a=i.controlPoint,u=i.currentPoint;n.addMarker(u,a,a),e.addQuadraticCurve(o.x,o.y,a.x,a.y,u.x,u.y),t&&t.quadraticCurveTo(a.x,a.y,u.x,u.y)}},{key:"pathA",value:function(t,e){var n=this.pathParser,i=r.pathA(n),o=i.currentPoint,a=i.rX,u=i.rY,s=i.sweepFlag,c=i.xAxisRotation,l=i.centp,f=i.a1,h=i.ad,p=1-s?1:-1,v=f+p*(h/2),y=new Ka(l.x+a*Math.cos(v),l.y+u*Math.sin(v));if(n.addMarkerAngle(y,v-p*Math.PI/2),n.addMarkerAngle(o,v-p*Math.PI),e.addPoint(o.x,o.y),t&&!isNaN(f)&&!isNaN(h)){var d=a>u?a:u,g=a>u?1:a/u,m=a>u?u/a:1;t.translate(l.x,l.y),t.rotate(c),t.scale(g,m),t.arc(0,0,d,f,f+h,Boolean(1-s)),t.scale(1/g,1/m),t.rotate(-c),t.translate(-l.x,-l.y)}}},{key:"pathZ",value:function(t,e){r.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}],[{key:"pathM",value:function(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}},{key:"pathL",value:function(t){return{current:t.current,point:t.getAsCurrentPoint()}}},{key:"pathH",value:function(t){var e=t.current,r=t.command,n=new Ka((r.relative?e.x:0)+r.x,e.y);return t.current=n,{current:e,point:n}}},{key:"pathV",value:function(t){var e=t.current,r=t.command,n=new Ka(e.x,(r.relative?e.y:0)+r.y);return t.current=n,{current:e,point:n}}},{key:"pathC",value:function(t){return{current:t.current,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}},{key:"pathS",value:function(t){return{current:t.current,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}},{key:"pathQ",value:function(t){return{current:t.current,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}},{key:"pathT",value:function(t){var e=t.current,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}},{key:"pathA",value:function(t){var e=t.current,r=t.command,n=r.rX,i=r.rY,o=r.xRot,a=r.lArcFlag,u=r.sweepFlag,s=o*(Math.PI/180),c=t.getAsCurrentPoint(),l=new Ka(Math.cos(s)*(e.x-c.x)/2+Math.sin(s)*(e.y-c.y)/2,-Math.sin(s)*(e.x-c.x)/2+Math.cos(s)*(e.y-c.y)/2),f=Math.pow(l.x,2)/Math.pow(n,2)+Math.pow(l.y,2)/Math.pow(i,2);f>1&&(n*=Math.sqrt(f),i*=Math.sqrt(f));var h=(a===u?-1:1)*Math.sqrt((Math.pow(n,2)*Math.pow(i,2)-Math.pow(n,2)*Math.pow(l.y,2)-Math.pow(i,2)*Math.pow(l.x,2))/(Math.pow(n,2)*Math.pow(l.y,2)+Math.pow(i,2)*Math.pow(l.x,2)));isNaN(h)&&(h=0);var p=new Ka(h*n*l.y/i,h*-i*l.x/n),v=new Ka((e.x+c.x)/2+Math.cos(s)*p.x-Math.sin(s)*p.y,(e.y+c.y)/2+Math.sin(s)*p.x+Math.cos(s)*p.y),y=xo([1,0],[(l.x-p.x)/n,(l.y-p.y)/i]),d=[(l.x-p.x)/n,(l.y-p.y)/i],g=[(-l.x-p.x)/n,(-l.y-p.y)/i],m=xo(d,g);return mo(d,g)<=-1&&(m=Math.PI),mo(d,g)>=1&&(m=0),{currentPoint:c,rX:n,rY:i,sweepFlag:u,xAxisRotation:s,centp:v,a1:y,ad:m}}},{key:"pathZ",value:function(t){t.current=t.start}}]),r}(dc);function xc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var bc=function(t){uu(r,t);var e=xc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="glyph",o.horizAdvX=o.getAttribute("horiz-adv-x").getNumber(),o.unicode=o.getAttribute("unicode").getString(),o.arabicForm=o.getAttribute("arabic-form").getString(),o}return r}(mc);function wc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Sc=function(t){uu(r,t);var e=wc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,(this instanceof r?this.constructor:void 0)===r||i)).type="text",o.x=0,o.y=0,o.measureCache=-1,o}return Eo(r,[{key:"setContext",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Vs(pu(r.prototype),"setContext",this).call(this,t,e);var n=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();n&&(t.textBaseline=n)}},{key:"initializeCoordinates",value:function(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}},{key:"getBoundingBox",value:function(t){var e=this;if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var r=null;return this.children.forEach((function(n,i){var o=e.getChildBoundingBox(t,e,e,i);r?r.addBoundingBox(o):r=o})),r}},{key:"getFontSize",value:function(){var t=this.document,e=this.parent,r=Ds.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}},{key:"getTElementBoundingBox",value:function(t){var e=this.getFontSize();return new Fs(this.x,this.y-e,this.x+this.measureText(t),this.y)}},{key:"getGlyph",value:function(t,e,r){var n=e[r],i=null;if(t.isArabic){var o=e.length,a=e[r-1],u=e[r+1],s="isolated";if((0===r||" "===a)&&r<o-1&&" "!==u&&(s="terminal"),r>0&&" "!==a&&r<o-1&&" "!==u&&(s="medial"),r>0&&" "!==a&&(r===o-1||" "===u)&&(s="initial"),void 0!==t.glyphs[n]){var c=t.glyphs[n];i=c instanceof bc?c:c[s]}}else i=t.glyphs[n];return i||(i=t.missingGlyph),i}},{key:"getText",value:function(){return""}},{key:"getTextFromNode",value:function(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),n=r.indexOf(e),i=r.length-1,o=Ui(e.textContent||"");return 0===n&&(o=Hi(o)),n===i&&(o=Xi(o)),o}},{key:"renderChildren",value:function(t){var e=this;if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((function(r,n){e.renderChild(t,e,e,n)}));var r=this.document.screen.mouse;r.isWorking()&&r.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}},{key:"renderTElementChildren",value:function(t){var e=this.document,r=this.parent,n=this.getText(),i=r.getStyle("font-family").getDefinition();if(i)for(var o=i.fontFace.unitsPerEm,a=Ds.parse(e.ctx.font),u=r.getStyle("font-size").getNumber(a.fontSize),s=r.getStyle("font-style").getString(a.fontStyle),c=u/o,l=i.isRTL?n.split("").reverse().join(""):n,f=Yi(r.getAttribute("dx").getString()),h=l.length,p=0;p<h;p++){var v=this.getGlyph(i,l,p);t.translate(this.x,this.y),t.scale(c,-c);var y=t.lineWidth;t.lineWidth=t.lineWidth*o/u,"italic"===s&&t.transform(1,0,.4,1,0,0),v.render(t),"italic"===s&&t.transform(1,0,-.4,1,0,0),t.lineWidth=y,t.scale(1/c,-1/c),t.translate(-this.x,-this.y),this.x+=u*(v.horizAdvX||i.horizAdvX)/o,void 0===f[p]||isNaN(f[p])||(this.x+=f[p])}else{var d=this.x,g=this.y;t.fillStyle&&t.fillText(n,d,g),t.strokeStyle&&t.strokeText(n,d,g)}}},{key:"applyAnchoring",value:function(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}},{key:"adjustChildCoordinatesRecursive",value:function(t){var e=this;this.children.forEach((function(r,n){e.adjustChildCoordinatesRecursiveCore(t,e,e,n)})),this.applyAnchoring()}},{key:"adjustChildCoordinatesRecursiveCore",value:function(t,e,r,n){var i=r.children[n];i.children.length>0?i.children.forEach((function(r,n){e.adjustChildCoordinatesRecursiveCore(t,e,i,n)})):this.adjustChildCoordinates(t,e,r,n)}},{key:"adjustChildCoordinates",value:function(t,e,r,n){var i=r.children[n];if("function"!=typeof i.measureText)return i;t.save(),i.setContext(t,!0);var o=i.getAttribute("x"),a=i.getAttribute("y"),u=i.getAttribute("dx"),s=i.getAttribute("dy"),c=i.getStyle("font-family").getDefinition(),l=Boolean(c)&&c.isRTL;0===n&&(o.hasValue()||o.setValue(i.getInheritedAttribute("x")),a.hasValue()||a.setValue(i.getInheritedAttribute("y")),u.hasValue()||u.setValue(i.getInheritedAttribute("dx")),s.hasValue()||s.setValue(i.getInheritedAttribute("dy")));var f=i.measureText(t);return l&&(e.x-=f),o.hasValue()?(e.applyAnchoring(),i.x=o.getPixels("x"),u.hasValue()&&(i.x+=u.getPixels("x"))):(u.hasValue()&&(e.x+=u.getPixels("x")),i.x=e.x),e.x=i.x,l||(e.x+=f),a.hasValue()?(i.y=a.getPixels("y"),s.hasValue()&&(i.y+=s.getPixels("y"))):(s.hasValue()&&(e.y+=s.getPixels("y")),i.y=e.y),e.y=i.y,e.leafTexts.push(i),e.minX=Math.min(e.minX,i.x,i.x+f),e.maxX=Math.max(e.maxX,i.x,i.x+f),i.clearContext(t),t.restore(),i}},{key:"getChildBoundingBox",value:function(t,e,r,n){var i=r.children[n];if("function"!=typeof i.getBoundingBox)return null;var o=i.getBoundingBox(t);return o?(i.children.forEach((function(r,n){var a=e.getChildBoundingBox(t,e,i,n);o.addBoundingBox(a)})),o):null}},{key:"renderChild",value:function(t,e,r,n){var i=r.children[n];i.render(t),i.children.forEach((function(r,n){e.renderChild(t,e,i,n)}))}},{key:"measureText",value:function(t){var e=this.measureCache;if(~e)return e;var r=this.getText(),n=this.measureTargetText(t,r);return this.measureCache=n,n}},{key:"measureTargetText",value:function(t,e){if(!e.length)return 0;var r=this.parent,n=r.getStyle("font-family").getDefinition();if(n){for(var i=this.getFontSize(),o=n.isRTL?e.split("").reverse().join(""):e,a=Yi(r.getAttribute("dx").getString()),u=o.length,s=0,c=0;c<u;c++){s+=(this.getGlyph(n,o,c).horizAdvX||n.horizAdvX)*i/n.fontFace.unitsPerEm,void 0===a[c]||isNaN(a[c])||(s+=a[c])}return s}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var l=t.measureText(e).width;return this.clearContext(t),t.restore(),l}},{key:"getInheritedAttribute",value:function(t){for(var e=this;e instanceof r&&e.isFirstChild();){var n=e.parent.getAttribute(t);if(n.hasValue(!0))return n.getValue("0");e=e.parent}return null}}]),r}(dc);function Oc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var kc=function(t){uu(r,t);var e=Oc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,(this instanceof r?this.constructor:void 0)===r||i)).type="tspan",o.text=o.children.length>0?"":o.getTextFromNode(),o}return Eo(r,[{key:"getText",value:function(){return this.text}}]),r}(Sc);function Tc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Ac=function(t){uu(r,t);var e=Tc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="textNode",t}return r}(kc);function Rc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Pc=function(t){uu(r,t);var e=Rc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="svg",t.root=!1,t}return Eo(r,[{key:"setContext",value:function(t){var e,n=this.document,i=n.screen,o=n.window,a=t.canvas;if(i.setDefaults(t),a.style&&void 0!==t.font&&o&&void 0!==o.getComputedStyle){t.font=o.getComputedStyle(a).getPropertyValue("font");var u=new Ua(n,"fontSize",Ds.parse(t.font).fontSize);u.hasValue()&&(n.rootEmSize=u.getPixels("y"),n.emSize=n.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var s=i.viewPort,c=s.width,l=s.height;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var f=this.getAttribute("refX"),h=this.getAttribute("refY"),p=this.getAttribute("viewBox"),v=p.hasValue()?Yi(p.getString()):null,y=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),d=0,g=0,m=0,x=0;v&&(d=v[0],g=v[1]),this.root||(c=this.getStyle("width").getPixels("x"),l=this.getStyle("height").getPixels("y"),"marker"===this.type&&(m=d,x=g,d=0,g=0)),i.viewPort.setCurrent(c,l),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),Vs(pu(r.prototype),"setContext",this).call(this,t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),v&&(c=v[2],l=v[3]),n.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:c,height:i.viewPort.height,desiredHeight:l,minX:d,minY:g,refX:f.getValue(),refY:h.getValue(),clip:y,clipX:m,clipY:x}),v&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(c,l))}},{key:"clearContext",value:function(t){Vs(pu(r.prototype),"clearContext",this).call(this,t),this.document.screen.viewPort.removeCurrent()}},{key:"resize",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.getAttribute("width",!0),i=this.getAttribute("height",!0),o=this.getAttribute("viewBox"),a=this.getAttribute("style"),u=n.getNumber(0),s=i.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var c=this.getAttribute("preserveAspectRatio");c.hasValue()&&c.setValue(c.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(n.setValue(t),i.setValue(e),o.hasValue()||o.setValue("0 0 ".concat(u||t," ").concat(s||e)),a.hasValue()){var l=this.getStyle("width"),f=this.getStyle("height");l.hasValue()&&l.setValue("".concat(t,"px")),f.hasValue()&&f.setValue("".concat(e,"px"))}}}]),r}(dc);function Ec(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Cc=function(t){uu(r,t);var e=Ec(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="rect",t}return Eo(r,[{key:"path",value:function(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),n=this.getStyle("width",!1,!0).getPixels("x"),i=this.getStyle("height",!1,!0).getPixels("y"),o=this.getAttribute("rx"),a=this.getAttribute("ry"),u=o.getPixels("x"),s=a.getPixels("y");if(o.hasValue()&&!a.hasValue()&&(s=u),a.hasValue()&&!o.hasValue()&&(u=s),u=Math.min(u,n/2),s=Math.min(s,i/2),t){var c=(Math.sqrt(2)-1)/3*4;t.beginPath(),i>0&&n>0&&(t.moveTo(e+u,r),t.lineTo(e+n-u,r),t.bezierCurveTo(e+n-u+c*u,r,e+n,r+s-c*s,e+n,r+s),t.lineTo(e+n,r+i-s),t.bezierCurveTo(e+n,r+i-s+c*s,e+n-u+c*u,r+i,e+n-u,r+i),t.lineTo(e+u,r+i),t.bezierCurveTo(e+u-c*u,r+i,e,r+i-s+c*s,e,r+i-s),t.lineTo(e,r+s),t.bezierCurveTo(e,r+s-c*s,e+u-c*u,r,e+u,r),t.closePath())}return new Fs(e,r,e+n,r+i)}},{key:"getMarkers",value:function(){return null}}]),r}(mc);function Mc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Nc=function(t){uu(r,t);var e=Mc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="circle",t}return Eo(r,[{key:"path",value:function(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),n=this.getAttribute("r").getPixels();return t&&n>0&&(t.beginPath(),t.arc(e,r,n,0,2*Math.PI,!1),t.closePath()),new Fs(e-n,r-n,e+n,r+n)}},{key:"getMarkers",value:function(){return null}}]),r}(mc);function _c(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Ic=function(t){uu(r,t);var e=_c(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="ellipse",t}return Eo(r,[{key:"path",value:function(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),n=this.getAttribute("ry").getPixels("y"),i=this.getAttribute("cx").getPixels("x"),o=this.getAttribute("cy").getPixels("y");return t&&r>0&&n>0&&(t.beginPath(),t.moveTo(i+r,o),t.bezierCurveTo(i+r,o+e*n,i+e*r,o+n,i,o+n),t.bezierCurveTo(i-e*r,o+n,i-r,o+e*n,i-r,o),t.bezierCurveTo(i-r,o-e*n,i-e*r,o-n,i,o-n),t.bezierCurveTo(i+e*r,o-n,i+r,o-e*n,i+r,o),t.closePath()),new Fs(i-r,o-n,i+r,o+n)}},{key:"getMarkers",value:function(){return null}}]),r}(mc);function Vc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Lc=function(t){uu(r,t);var e=Vc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="line",t}return Eo(r,[{key:"getPoints",value:function(){return[new Ka(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new Ka(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}},{key:"path",value:function(t){var e=this.getPoints(),r=oo(e,2),n=r[0],i=n.x,o=n.y,a=r[1],u=a.x,s=a.y;return t&&(t.beginPath(),t.moveTo(i,o),t.lineTo(u,s)),new Fs(i,o,u,s)}},{key:"getMarkers",value:function(){var t=this.getPoints(),e=oo(t,2),r=e[0],n=e[1],i=r.angleTo(n);return[[r,i],[n,i]]}}]),r}(mc);function Bc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var jc=function(t){uu(r,t);var e=Bc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="polyline",o.points=[],o.points=Ka.parsePath(o.getAttribute("points").getString()),o}return Eo(r,[{key:"path",value:function(t){var e=this.points,r=oo(e,1)[0],n=r.x,i=r.y,o=new Fs(n,i);return t&&(t.beginPath(),t.moveTo(n,i)),e.forEach((function(e){var r=e.x,n=e.y;o.addPoint(r,n),t&&t.lineTo(r,n)})),o}},{key:"getMarkers",value:function(){var t=this.points,e=t.length-1,r=[];return t.forEach((function(n,i){i!==e&&r.push([n,n.angleTo(t[i+1])])})),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}]),r}(mc);function Dc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Fc=function(t){uu(r,t);var e=Dc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="polygon",t}return Eo(r,[{key:"path",value:function(t){var e=Vs(pu(r.prototype),"path",this).call(this,t),n=oo(this.points,1)[0],i=n.x,o=n.y;return t&&(t.lineTo(i,o),t.closePath()),e}}]),r}(jc);function zc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Uc=function(t){uu(r,t);var e=zc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="pattern",t}return Eo(r,[{key:"createPattern",value:function(t,e,r){var n=this.getStyle("width").getPixels("x",!0),i=this.getStyle("height").getPixels("y",!0),o=new Pc(this.document,null);o.attributes.viewBox=new Ua(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.width=new Ua(this.document,"width","".concat(n,"px")),o.attributes.height=new Ua(this.document,"height","".concat(i,"px")),o.attributes.transform=new Ua(this.document,"transform",this.getAttribute("patternTransform").getValue()),o.children=this.children;var a=this.document.createCanvas(n,i),u=a.getContext("2d"),s=this.getAttribute("x"),c=this.getAttribute("y");s.hasValue()&&c.hasValue()&&u.translate(s.getPixels("x",!0),c.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var l=-1;l<=1;l++)for(var f=-1;f<=1;f++)u.save(),o.attributes.x=new Ua(this.document,"x",l*a.width),o.attributes.y=new Ua(this.document,"y",f*a.height),o.render(u),u.restore();return t.createPattern(a,"repeat")}}]),r}(ps);function Hc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Xc=function(t){uu(r,t);var e=Hc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="marker",t}return Eo(r,[{key:"render",value:function(t,e,r){if(e){var n=e.x,i=e.y,o=this.getAttribute("orient").getString("auto"),a=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(n,i),"auto"===o&&t.rotate(r),"strokeWidth"===a&&t.scale(t.lineWidth,t.lineWidth),t.save();var u=new Pc(this.document,null);u.type=this.type,u.attributes.viewBox=new Ua(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new Ua(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new Ua(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new Ua(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new Ua(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new Ua(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new Ua(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new Ua(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(t),t.restore(),"strokeWidth"===a&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===o&&t.rotate(-r),t.translate(-n,-i)}}}]),r}(ps);function Yc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Gc=function(t){uu(r,t);var e=Yc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="defs",t}return Eo(r,[{key:"render",value:function(){}}]),r}(ps);function Wc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var qc=function(t){uu(r,t);var e=Wc(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="g",t}return Eo(r,[{key:"getBoundingBox",value:function(t){var e=new Fs;return this.children.forEach((function(r){e.addBoundingBox(r.getBoundingBox(t))})),e}}]),r}(dc);function $c(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Qc=function(t){uu(r,t);var e=$c(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).attributesToInherit=["gradientUnits"],o.stops=[];var a=lu(o),u=a.stops;return a.children.forEach((function(t){"stop"===t.type&&u.push(t)})),o}return Eo(r,[{key:"getGradientUnits",value:function(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}},{key:"createGradient",value:function(t,e,r){var n=this,i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var o=i.stops,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,o[o.length-1].color);if(o.forEach((function(t){a.addColorStop(t.offset,n.addParentOpacity(r,t.color))})),this.getAttribute("gradientTransform").hasValue()){var u=this.document,s=u.screen,c=s.MAX_VIRTUAL_PIXELS,l=s.viewPort,f=oo(l.viewPorts,1)[0],h=new Cc(u,null);h.attributes.x=new Ua(u,"x",-c/3),h.attributes.y=new Ua(u,"y",-c/3),h.attributes.width=new Ua(u,"width",c),h.attributes.height=new Ua(u,"height",c);var p=new qc(u,null);p.attributes.transform=new Ua(u,"transform",this.getAttribute("gradientTransform").getValue()),p.children=[h];var v=new Pc(u,null);v.attributes.x=new Ua(u,"x",0),v.attributes.y=new Ua(u,"y",0),v.attributes.width=new Ua(u,"width",f.width),v.attributes.height=new Ua(u,"height",f.height),v.children=[p];var y=u.createCanvas(f.width,f.height),d=y.getContext("2d");return d.fillStyle=a,v.render(d),d.createPattern(y,"no-repeat")}return a}},{key:"inheritStopContainer",value:function(t){var e=this;this.attributesToInherit.forEach((function(r){!e.getAttribute(r).hasValue()&&t.getAttribute(r).hasValue()&&e.getAttribute(r,!0).setValue(t.getAttribute(r).getValue())}))}},{key:"addParentOpacity",value:function(t,e){return t.hasValue()?new Ua(this.document,"color",e).addOpacity(t).getColor():e}}]),r}(ps);function Zc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Kc=function(t){uu(r,t);var e=Zc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="linearGradient",o.attributesToInherit.push("x1","y1","x2","y2"),o}return Eo(r,[{key:"getGradient",value:function(t,e){var r="objectBoundingBox"===this.getGradientUnits(),n=r?e.getBoundingBox(t):null;if(r&&!n)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var i=r?n.x+n.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),o=r?n.y+n.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),a=r?n.x+n.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=r?n.y+n.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return i===a&&o===u?null:t.createLinearGradient(i,o,a,u)}}]),r}(Qc);function Jc(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var tl=function(t){uu(r,t);var e=Jc(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="radialGradient",o.attributesToInherit.push("cx","cy","r","fx","fy","fr"),o}return Eo(r,[{key:"getGradient",value:function(t,e){var r="objectBoundingBox"===this.getGradientUnits(),n=e.getBoundingBox(t);if(r&&!n)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var i=r?n.x+n.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),o=r?n.y+n.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),a=i,u=o;this.getAttribute("fx").hasValue()&&(a=r?n.x+n.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=r?n.y+n.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var s=r?(n.width+n.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),c=this.getAttribute("fr").getPixels();return t.createRadialGradient(a,u,c,i,o,s)}}]),r}(Qc);function el(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var rl=function(t){uu(r,t);var e=el(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).type="stop";var a=Math.max(0,Math.min(1,o.getAttribute("offset").getNumber())),u=o.getStyle("stop-opacity"),s=o.getStyle("stop-color",!0);return""===s.getString()&&s.setValue("#000"),u.hasValue()&&(s=s.addOpacity(u)),o.offset=a,o.color=s.getColor(),o}return r}(ps),nl="Array Iterator",il=mt.set,ol=mt.getterFor(nl),al=Wu(Array,"Array",(function(t,e){il(this,{type:nl,target:Vt(t),index:0,kind:e})}),(function(){var t=ol(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values");we.Arguments=we.Array,mu("keys"),mu("values"),mu("entries");var ul=I("iterator"),sl=I("toStringTag"),cl=al.values,ll=function(t,e){if(t){if(t[ul]!==cl)try{tt(t,ul,cl)}catch(e){t[ul]=cl}if(t[sl]||tt(t,sl,e),Ga[e])for(var r in al)if(t[r]!==al[r])try{tt(t,r,al[r])}catch(e){t[r]=al[r]}}};for(var fl in Ga)ll(u[fl]&&u[fl].prototype,fl);function hl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}ll($a,"DOMTokenList");var pl=function(t){uu(r,t);var e=hl(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).type="animate",o.duration=0,o.initialValue=null,o.initialUnits="",o.removed=!1,o.frozen=!1,t.screen.animations.push(lu(o)),o.begin=o.getAttribute("begin").getMilliseconds(),o.maxDuration=o.begin+o.getAttribute("dur").getMilliseconds(),o.from=o.getAttribute("from"),o.to=o.getAttribute("to"),o.values=new Ua(t,"values",null);var a=o.getAttribute("values");return a.hasValue()&&o.values.setValue(a.getString().split(";")),o}return Eo(r,[{key:"getProperty",value:function(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}},{key:"calcValue",value:function(){var t=this.initialUnits,e=this.getProgress(),r=e.progress,n=e.from,i=e.to,o=n.getNumber()+(i.getNumber()-n.getNumber())*r;return"%"===t&&(o*=100),"".concat(o).concat(t)}},{key:"update",value:function(t){var e=this.parent,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var n=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==n||this.frozen){if("remove"===n&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var i=!1;if(this.begin<this.duration){var o=this.calcValue(),a=this.getAttribute("type");if(a.hasValue()){var u=a.getString();o="".concat(u,"(").concat(o,")")}r.setValue(o),i=!0}return i}},{key:"getProgress",value:function(){var t=this.document,e=this.values,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var n=r.progress*(e.getValue().length-1),i=Math.floor(n),o=Math.ceil(n);r.from=new Ua(t,"from",parseFloat(e.getValue()[i])),r.to=new Ua(t,"to",parseFloat(e.getValue()[o])),r.progress=(n-i)/(o-i)}else r.from=this.from,r.to=this.to;return r}}]),r}(ps);function vl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var yl=function(t){uu(r,t);var e=vl(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="animateColor",t}return Eo(r,[{key:"calcValue",value:function(){var t=this.getProgress(),e=t.progress,r=t.from,n=t.to,i=new za(r.getColor()),o=new za(n.getColor());if(i.ok&&o.ok){var a=i.r+(o.r-i.r)*e,u=i.g+(o.g-i.g)*e,s=i.b+(o.b-i.b)*e;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(u),", ").concat(Math.floor(s),")")}return this.getAttribute("from").getColor()}}]),r}(pl);function dl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var gl=function(t){uu(r,t);var e=dl(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="animateTransform",t}return Eo(r,[{key:"calcValue",value:function(){var t=this.getProgress(),e=t.progress,r=t.from,n=t.to,i=Yi(r.getString()),o=Yi(n.getString());return i.map((function(t,r){return t+(o[r]-t)*e})).join(" ")}}]),r}(pl);function ml(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(!t)return;if("string"==typeof t)return xl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xl(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw o}}}}function xl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var wl=function(t){uu(r,t);var e=bl(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).type="font",o.glyphs=Object.create(null),o.horizAdvX=o.getAttribute("horiz-adv-x").getNumber();var a,u=t.definitions,s=ml(lu(o).children);try{for(s.s();!(a=s.n()).done;){var c=a.value;switch(c.type){case"font-face":o.fontFace=c;var l=c.getStyle("font-family");l.hasValue()&&(u[l.getString()]=lu(o));break;case"missing-glyph":o.missingGlyph=c;break;case"glyph":var f=c;f.arabicForm?(o.isRTL=!0,o.isArabic=!0,void 0===o.glyphs[f.unicode]&&(o.glyphs[f.unicode]=Object.create(null)),o.glyphs[f.unicode][f.arabicForm]=f):o.glyphs[f.unicode]=f}}}catch(t){s.e(t)}finally{s.f()}return o}return Eo(r,[{key:"render",value:function(){}}]),r}(ps);function Sl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Ol=function(t){uu(r,t);var e=Sl(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="font-face",o.ascent=o.getAttribute("ascent").getNumber(),o.descent=o.getAttribute("descent").getNumber(),o.unitsPerEm=o.getAttribute("units-per-em").getNumber(),o}return r}(ps);function kl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Tl=function(t){uu(r,t);var e=kl(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="missing-glyph",t.horizAdvX=0,t}return r}(mc);function Al(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Rl=function(t){uu(r,t);var e=Al(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="tref",t}return Eo(r,[{key:"getText",value:function(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}]),r}(Sc);function Pl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var El=function(t){uu(r,t);var e=Pl(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).type="a";var a=n.childNodes,u=a[0],s=a.length>0&&Array.from(a).every((function(t){return 3===t.nodeType}));return o.hasText=s,o.text=s?o.getTextFromNode(u):"",o}return Eo(r,[{key:"getText",value:function(){return this.text}},{key:"renderChildren",value:function(t){if(this.hasText){Vs(pu(r.prototype),"renderChildren",this).call(this,t);var e=this.document,n=this.x,i=this.y,o=e.screen.mouse,a=new Ua(e,"fontSize",Ds.parse(e.ctx.font).fontSize);o.isWorking()&&o.checkBoundingBox(this,new Fs(n,i-a.getPixels("y"),n+this.measureText(t),i))}else if(this.children.length>0){var u=new qc(this.document,null);u.children=this.children,u.parent=this,u.render(t)}}},{key:"onClick",value:function(){var t=this.document.window;t&&t.open(this.getHrefAttribute().getString())}},{key:"onMouseMove",value:function(){this.document.ctx.canvas.style.cursor="pointer"}}]),r}(Sc),Cl=Jt.f,Ml={}.toString,Nl="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],_l={f:function(t){return Nl&&"[object Window]"==Ml.call(t)?function(t){try{return Cl(t)}catch(t){return Nl.slice()}}(t):Cl(Vt(t))}},Il=!P((function(){return Object.isExtensible(Object.preventExtensions({}))})),Vl=n((function(t){var e=K.f,r=!1,n=m("meta"),i=0,o=Object.isExtensible||function(){return!0},a=function(t){e(t,n,{value:{objectID:"O"+i++,weakData:{}}})},u=t.exports={enable:function(){u.enable=function(){},r=!0;var t=Jt.f,e=[].splice,i={};i[n]=1,t(i).length&&(Jt.f=function(r){for(var i=t(r),o=0,a=i.length;o<a;o++)if(i[o]===n){e.call(i,o,1);break}return i},fe({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:_l.f}))},fastKey:function(t,e){if(!j(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!y(t,n)){if(!o(t))return"F";if(!e)return"E";a(t)}return t[n].objectID},getWeakData:function(t,e){if(!y(t,n)){if(!o(t))return!0;if(!e)return!1;a(t)}return t[n].weakData},onFreeze:function(t){return Il&&r&&o(t)&&!y(t,n)&&a(t),t}};lt[n]=!0})),Ll=K.f,Bl=Vl.fastKey,jl=mt.set,Dl=mt.getterFor;function Fl(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(!t)return;if("string"==typeof t)return zl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zl(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw o}}}}function zl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ul(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Hl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ul(Object(r),!0).forEach((function(e){Ro(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ul(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}!function(t,e,r){var n=-1!==t.indexOf("Map"),i=-1!==t.indexOf("Weak"),o=n?"set":"add",a=u[t],s=a&&a.prototype,c=a,l={},f=function(t){var e=s[t];Ot(s,t,"add"==t?function(t){return e.call(this,0===t?0:t),this}:"delete"==t?function(t){return!(i&&!j(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return i&&!j(t)?void 0:e.call(this,0===t?0:t)}:"has"==t?function(t){return!(i&&!j(t))&&e.call(this,0===t?0:t)}:function(t,r){return e.call(this,0===t?0:t,r),this})};if(ce(t,!x(a)||!(i||s.forEach&&!P((function(){(new a).entries().next()})))))c=r.getConstructor(e,t,n,o),Vl.enable();else if(ce(t,!0)){var h=new c,p=h[o](i?{}:-0,1)!=h,v=P((function(){h.has(1)})),y=Fe((function(t){new a(t)})),d=!i&&P((function(){for(var t=new a,e=5;e--;)t[o](e,e);return!t.has(-0)}));y||((c=e((function(e,r){be(e,c,t);var i=ms(new a,e,c);return null!=r&&Me(r,i[o],{that:i,AS_ENTRIES:n}),i}))).prototype=s,s.constructor=c),(v||d)&&(f("delete"),f("has"),n&&f("get")),(d||p)&&f(o),i&&s.clear&&delete s.clear}l[t]=c,fe({global:!0,forced:c!=a},l),ge(c,t),i||r.setStrong(c,t,n)}("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(t,e,r,n){var i=t((function(t,o){be(t,i,e),jl(t,{type:e,index:ti(null),first:void 0,last:void 0,size:0}),B||(t.size=0),null!=o&&Me(o,t[n],{that:t,AS_ENTRIES:r})})),o=Dl(e),a=function(t,e,r){var n,i,a=o(t),s=u(t,e);return s?s.value=r:(a.last=s={index:i=Bl(e,!0),key:e,value:r,previous:n=a.last,next:void 0,removed:!1},a.first||(a.first=s),n&&(n.next=s),B?a.size++:t.size++,"F"!==i&&(a.index[i]=s)),t},u=function(t,e){var r,n=o(t),i=Bl(e);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key==e)return r};return pe(i.prototype,{clear:function(){for(var t=o(this),e=t.index,r=t.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete e[r.index],r=r.next;t.first=t.last=void 0,B?t.size=0:this.size=0},delete:function(t){var e=this,r=o(e),n=u(e,t);if(n){var i=n.next,a=n.previous;delete r.index[n.index],n.removed=!0,a&&(a.next=i),i&&(i.previous=a),r.first==n&&(r.first=i),r.last==n&&(r.last=a),B?r.size--:e.size--}return!!n},forEach:function(t){for(var e,r=o(this),n=Te(t,arguments.length>1?arguments[1]:void 0,3);e=e?e.next:r.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!u(this,t)}}),pe(i.prototype,r?{get:function(t){var e=u(this,t);return e&&e.value},set:function(t,e){return a(this,0===t?0:t,e)}}:{add:function(t){return a(this,t=0===t?0:t,t)}}),B&&Ll(i.prototype,"size",{get:function(){return o(this).size}}),i},setStrong:function(t,e,r){var n=e+" Iterator",i=Dl(e),o=Dl(n);Wu(t,e,(function(t,e){jl(this,{type:n,target:t,state:i(t),kind:e,last:void 0})}),(function(){for(var t=o(this),e=t.kind,r=t.last;r&&r.removed;)r=r.previous;return t.target&&(t.last=r=r?r.next:t.state.first)?"keys"==e?{value:r.key,done:!1}:"values"==e?{value:r.value,done:!1}:{value:[r.key,r.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),r?"entries":"values",!r,!0),xe(e)}});var Yl=function(t){uu(r,t);var e=Xl(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).type="textPath",o.textWidth=0,o.textHeight=0,o.pathLength=-1,o.glyphInfo=null,o.letterSpacingCache=[],o.measuresCache=new Map([["",0]]);var a=o.getHrefAttribute().getDefinition();return o.text=o.getTextFromNode(),o.dataArray=o.parsePathData(a),o}return Eo(r,[{key:"getText",value:function(){return this.text}},{key:"path",value:function(t){var e=this.dataArray;t&&t.beginPath(),e.forEach((function(e){var r=e.type,n=e.points;switch(r){case sc.LINE_TO:t&&t.lineTo(n[0],n[1]);break;case sc.MOVE_TO:t&&t.moveTo(n[0],n[1]);break;case sc.CURVE_TO:t&&t.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case sc.QUAD_TO:t&&t.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case sc.ARC:var i=oo(n,8),o=i[0],a=i[1],u=i[2],s=i[3],c=i[4],l=i[5],f=i[6],h=i[7],p=u>s?u:s,v=u>s?1:u/s,y=u>s?s/u:1;t&&(t.translate(o,a),t.rotate(f),t.scale(v,y),t.arc(0,0,p,c,c+l,Boolean(1-h)),t.scale(1/v,1/y),t.rotate(-f),t.translate(-o,-a));break;case sc.CLOSE_PATH:t&&t.closePath()}}))}},{key:"renderChildren",value:function(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),n=this.glyphInfo,i=t.fillStyle;"underline"===e&&t.beginPath(),n.forEach((function(n,i){var o=n.p0,a=n.p1,u=n.rotation,s=n.text;t.save(),t.translate(o.x,o.y),t.rotate(u),t.fillStyle&&t.fillText(s,0,0),t.strokeStyle&&t.strokeText(s,0,0),t.restore(),"underline"===e&&(0===i&&t.moveTo(o.x,o.y+r/8),t.lineTo(a.x,a.y+r/5))})),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=i,t.stroke(),t.closePath()),t.restore()}},{key:"getLetterSpacingAt",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}},{key:"findSegmentToFitChar",value:function(t,e,r,n,i,o,a,u,s){var c=o,l=this.measureText(t,u);" "===u&&"justify"===e&&r<n&&(l+=(n-r)/i),s>-1&&(c+=this.getLetterSpacingAt(s));var f=this.textHeight/20,h=this.getEquidistantPointOnPath(c,f,0),p=this.getEquidistantPointOnPath(c+l,f,0),v={p0:h,p1:p},y=h&&p?Math.atan2(p.y-h.y,p.x-h.x):0;if(a){var d=Math.cos(Math.PI/2+y)*a,g=Math.cos(-y)*a;v.p0=Hl(Hl({},h),{},{x:h.x+d,y:h.y+g}),v.p1=Hl(Hl({},p),{},{x:p.x+d,y:p.y+g})}return{offset:c+=l,segment:v,rotation:y}}},{key:"measureText",value:function(t,e){var r=this.measuresCache,n=e||this.getText();if(r.has(n))return r.get(n);var i=this.measureTargetText(t,n);return r.set(n,i),i}},{key:"setTextData",value:function(t){var e=this;if(!this.glyphInfo){var r=this.getText(),n=r.split(""),i=r.split(" ").length-1,o=this.parent.getAttribute("dx").split().map((function(t){return t.getPixels("x")})),a=this.parent.getAttribute("dy").getPixels("y"),u=this.parent.getStyle("text-anchor").getString("start"),s=this.getStyle("letter-spacing"),c=this.parent.getStyle("letter-spacing"),l=0;s.hasValue()&&"inherit"!==s.getValue()?s.hasValue()&&"initial"!==s.getValue()&&"unset"!==s.getValue()&&(l=s.getPixels()):l=c.getPixels();var f=[],h=r.length;this.letterSpacingCache=f;for(var p=0;p<h;p++)f.push(void 0!==o[p]?o[p]:l);var v=f.reduce((function(t,e,r){return 0===r?0:t+e||0}),0),y=this.measureText(t),d=Math.max(y+v,0);this.textWidth=y,this.textHeight=this.getFontSize(),this.glyphInfo=[];var g=this.getPathLength(),m=this.getStyle("startOffset").getNumber(0)*g,x=0;"middle"!==u&&"center"!==u||(x=-d/2),"end"!==u&&"right"!==u||(x=-d),x+=m,n.forEach((function(r,o){var s=e.findSegmentToFitChar(t,u,d,g,i,x,a,r,o),c=s.offset,l=s.segment,f=s.rotation;x=c,l.p0&&l.p1&&e.glyphInfo.push({text:n[o],p0:l.p0,p1:l.p1,rotation:f})}))}}},{key:"parsePathData",value:function(t){if(this.pathLength=-1,!t)return[];var e=[],r=t.pathParser;for(r.reset();!r.isEnd();){var n=r.current,i=n?n.x:0,o=n?n.y:0,a=r.next(),u=a.type,s=[];switch(a.type){case sc.MOVE_TO:this.pathM(r,s);break;case sc.LINE_TO:u=this.pathL(r,s);break;case sc.HORIZ_LINE_TO:u=this.pathH(r,s);break;case sc.VERT_LINE_TO:u=this.pathV(r,s);break;case sc.CURVE_TO:this.pathC(r,s);break;case sc.SMOOTH_CURVE_TO:u=this.pathS(r,s);break;case sc.QUAD_TO:this.pathQ(r,s);break;case sc.SMOOTH_QUAD_TO:u=this.pathT(r,s);break;case sc.ARC:s=this.pathA(r);break;case sc.CLOSE_PATH:mc.pathZ(r)}a.type!==sc.CLOSE_PATH?e.push({type:u,points:s,start:{x:i,y:o},pathLength:this.calcLength(i,o,u,s)}):e.push({type:sc.CLOSE_PATH,points:[],pathLength:0})}return e}},{key:"pathM",value:function(t,e){var r=mc.pathM(t).point,n=r.x,i=r.y;e.push(n,i)}},{key:"pathL",value:function(t,e){var r=mc.pathL(t).point,n=r.x,i=r.y;return e.push(n,i),sc.LINE_TO}},{key:"pathH",value:function(t,e){var r=mc.pathH(t).point,n=r.x,i=r.y;return e.push(n,i),sc.LINE_TO}},{key:"pathV",value:function(t,e){var r=mc.pathV(t).point,n=r.x,i=r.y;return e.push(n,i),sc.LINE_TO}},{key:"pathC",value:function(t,e){var r=mc.pathC(t),n=r.point,i=r.controlPoint,o=r.currentPoint;e.push(n.x,n.y,i.x,i.y,o.x,o.y)}},{key:"pathS",value:function(t,e){var r=mc.pathS(t),n=r.point,i=r.controlPoint,o=r.currentPoint;return e.push(n.x,n.y,i.x,i.y,o.x,o.y),sc.CURVE_TO}},{key:"pathQ",value:function(t,e){var r=mc.pathQ(t),n=r.controlPoint,i=r.currentPoint;e.push(n.x,n.y,i.x,i.y)}},{key:"pathT",value:function(t,e){var r=mc.pathT(t),n=r.controlPoint,i=r.currentPoint;return e.push(n.x,n.y,i.x,i.y),sc.QUAD_TO}},{key:"pathA",value:function(t){var e=mc.pathA(t),r=e.rX,n=e.rY,i=e.sweepFlag,o=e.xAxisRotation,a=e.centp,u=e.a1,s=e.ad;return 0===i&&s>0&&(s-=2*Math.PI),1===i&&s<0&&(s+=2*Math.PI),[a.x,a.y,r,n,u,s,o,i]}},{key:"calcLength",value:function(t,e,r,n){var i=0,o=null,a=null,u=0;switch(r){case sc.LINE_TO:return this.getLineLength(t,e,n[0],n[1]);case sc.CURVE_TO:for(i=0,o=this.getPointOnCubicBezier(0,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),u=.01;u<=1;u+=.01)a=this.getPointOnCubicBezier(u,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),i+=this.getLineLength(o.x,o.y,a.x,a.y),o=a;return i;case sc.QUAD_TO:for(i=0,o=this.getPointOnQuadraticBezier(0,t,e,n[0],n[1],n[2],n[3]),u=.01;u<=1;u+=.01)a=this.getPointOnQuadraticBezier(u,t,e,n[0],n[1],n[2],n[3]),i+=this.getLineLength(o.x,o.y,a.x,a.y),o=a;return i;case sc.ARC:i=0;var s=n[4],c=n[5],l=n[4]+c,f=Math.PI/180;if(Math.abs(s-l)<f&&(f=Math.abs(s-l)),o=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],s,0),c<0)for(u=s-f;u>l;u-=f)a=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],u,0),i+=this.getLineLength(o.x,o.y,a.x,a.y),o=a;else for(u=s+f;u<l;u+=f)a=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],u,0),i+=this.getLineLength(o.x,o.y,a.x,a.y),o=a;return a=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],l,0),i+=this.getLineLength(o.x,o.y,a.x,a.y)}return 0}},{key:"getPointOnLine",value:function(t,e,r,n,i){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,u=(i-r)/(n-e+yo),s=Math.sqrt(t*t/(1+u*u));n<e&&(s*=-1);var c=u*s,l=null;if(n===e)l={x:o,y:a+c};else if((a-r)/(o-e+yo)===u)l={x:o+s,y:a+c};else{var f=0,h=0,p=this.getLineLength(e,r,n,i);if(p<yo)return null;var v=(o-e)*(n-e)+(a-r)*(i-r);f=e+(v/=p*p)*(n-e),h=r+v*(i-r);var y=this.getLineLength(o,a,f,h),d=Math.sqrt(t*t-y*y);s=Math.sqrt(d*d/(1+u*u)),n<e&&(s*=-1),l={x:f+s,y:h+(c=u*s)}}return l}},{key:"getPointOnPath",value:function(t){var e=this.getPathLength(),r=0,n=null;if(t<-5e-5||t-5e-5>e)return null;var i,o=Fl(this.dataArray);try{for(o.s();!(i=o.n()).done;){var a=i.value;if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var u=t-r,s=0;switch(a.type){case sc.LINE_TO:n=this.getPointOnLine(u,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case sc.ARC:var c=a.points[4],l=a.points[5],f=a.points[4]+l;if(s=c+u/a.pathLength*l,l<0&&s<f||l>=0&&s>f)break;n=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],s,a.points[6]);break;case sc.CURVE_TO:(s=u/a.pathLength)>1&&(s=1),n=this.getPointOnCubicBezier(s,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case sc.QUAD_TO:(s=u/a.pathLength)>1&&(s=1),n=this.getPointOnQuadraticBezier(s,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(n)return n;break}r+=a.pathLength}}catch(t){o.e(t)}finally{o.f()}return null}},{key:"getLineLength",value:function(t,e,r,n){return Math.sqrt((r-t)*(r-t)+(n-e)*(n-e))}},{key:"getPathLength",value:function(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((function(t,e){return e.pathLength>0?t+e.pathLength:t}),0)),this.pathLength}},{key:"getPointOnCubicBezier",value:function(t,e,r,n,i,o,a,u,s){return{x:u*bo(t)+o*wo(t)+n*So(t)+e*Oo(t),y:s*bo(t)+a*wo(t)+i*So(t)+r*Oo(t)}}},{key:"getPointOnQuadraticBezier",value:function(t,e,r,n,i,o,a){return{x:o*ko(t)+n*To(t)+e*Ao(t),y:a*ko(t)+i*To(t)+r*Ao(t)}}},{key:"getPointOnEllipticalArc",value:function(t,e,r,n,i,o){var a=Math.cos(o),u=Math.sin(o),s=r*Math.cos(i),c=n*Math.sin(i);return{x:t+(s*a-c*u),y:e+(s*u+c*a)}}},{key:"buildEquidistantCache",value:function(t,e){var r=this.getPathLength(),n=e||.25,i=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==i||this.equidistantCache.precision!==n){this.equidistantCache={step:i,precision:n,points:[]};for(var o=0,a=0;a<=r;a+=n){var u=this.getPointOnPath(a),s=this.getPointOnPath(a+n);u&&s&&((o+=this.getLineLength(u.x,u.y,s.x,s.y))>=i&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:a}),o-=i))}}}},{key:"getEquidistantPointOnPath",value:function(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var n=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[n]||null}}]),r}(Sc);function Gl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Wl=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i,ql=function(t){uu(i,t);var e,r,n=Gl(i);function i(t,e,r){var o;Po(this,i),(o=n.call(this,t,e,r)).type="image",o.loaded=!1;var a=o.getHrefAttribute().getString();if(!a)return fu(o);var u=a.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(a);return t.images.push(lu(o)),u?o.loadSvg(a):o.loadImage(a),o.isSvg=u,o}return Eo(i,[{key:"loadImage",value:(r=gn(dn.mark((function t(e){var r;return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.document.createImage(e);case 3:r=t.sent,this.image=r,t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0),console.error('Error while loading image "'.concat(e,'":'),t.t0);case 10:this.loaded=!0;case 11:case"end":return t.stop()}}),t,this,[[0,7]])}))),function(t){return r.apply(this,arguments)})},{key:"loadSvg",value:(e=gn(dn.mark((function t(e){var r,n,i,o;return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(r=Wl.exec(e))){t.next=6;break}n=r[5],"base64"===r[4]?this.image=atob(n):this.image=decodeURIComponent(n),t.next=19;break;case 6:return t.prev=6,t.next=9,this.document.fetch(e);case 9:return i=t.sent,t.next=12,i.text();case 12:o=t.sent,this.image=o,t.next=19;break;case 16:t.prev=16,t.t0=t.catch(6),console.error('Error while loading image "'.concat(e,'":'),t.t0);case 19:this.loaded=!0;case 20:case"end":return t.stop()}}),t,this,[[6,16]])}))),function(t){return e.apply(this,arguments)})},{key:"renderChildren",value:function(t){var e=this.document,r=this.image,n=this.loaded,i=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(n&&r&&a&&u){if(t.save(),t.translate(i,o),this.isSvg){var s=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:a,scaleHeight:u});s.document.documentElement.parent=this,s.render()}else{var c=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a,desiredWidth:c.width,height:u,desiredHeight:c.height}),this.loaded&&(void 0===c.complete||c.complete)&&t.drawImage(c,0,0)}t.restore()}}},{key:"getBoundingBox",value:function(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),n=this.getStyle("height").getPixels("y");return new Fs(t,e,t+r,e+n)}}]),i}(dc);function $l(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Ql=function(t){uu(r,t);var e=$l(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="symbol",t}return Eo(r,[{key:"render",value:function(t){}}]),r}(dc),Zl=function(){function t(e){Po(this,t),this.document=e,this.loaded=!1,e.fonts.push(this)}var e;return Eo(t,[{key:"load",value:(e=gn(dn.mark((function t(e,r){var n,i,o;return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,n=this.document,t.next=4,n.canvg.parser.load(r);case 4:i=t.sent,o=i.getElementsByTagName("font"),Array.from(o).forEach((function(t){var r=n.createElement(t);n.definitions[e]=r})),t.next=12;break;case 9:t.prev=9,t.t0=t.catch(0),console.error('Error while loading font "'.concat(r,'":'),t.t0);case 12:this.loaded=!0;case 13:case"end":return t.stop()}}),t,this,[[0,9]])}))),function(t,r){return e.apply(this,arguments)})}]),t}();function Kl(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Jl=function(t){uu(r,t);var e=Kl(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="style",Ui(Array.from(n.childNodes).map((function(t){return t.textContent})).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach((function(e){var r=e.trim();if(r){var n=r.split("{"),i=n[0].split(","),o=n[1].split(";");i.forEach((function(e){var r=e.trim();if(r){var n=t.styles[r]||{};if(o.forEach((function(e){var r=e.indexOf(":"),i=e.substr(0,r).trim(),o=e.substr(r+1,e.length-r).trim();i&&o&&(n[i]=new Ua(t,i,o))})),t.styles[r]=n,t.stylesSpecificity[r]=vo(r),"@font-face"===r){var i=n["font-family"].getString().replace(/"|'/g,"");n.src.getString().split(",").forEach((function(e){if(e.indexOf('format("svg")')>0){var r=qi(e);r&&new Zl(t).load(i,r)}}))}}}))}})),o}return r}(ps);function tf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}Jl.parseExternalUrl=qi;var ef=function(t){uu(r,t);var e=tf(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="use",t}return Eo(r,[{key:"setContext",value:function(t){Vs(pu(r.prototype),"setContext",this).call(this,t);var e=this.getAttribute("x"),n=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),n.hasValue()&&t.translate(0,n.getPixels("y"))}},{key:"path",value:function(t){var e=this.element;e&&e.path(t)}},{key:"renderChildren",value:function(t){var e=this.document,r=this.element;if(r){var n=r;if("symbol"===r.type&&((n=new Pc(e,null)).attributes.viewBox=new Ua(e,"viewBox",r.getAttribute("viewBox").getString()),n.attributes.preserveAspectRatio=new Ua(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),n.attributes.overflow=new Ua(e,"overflow",r.getAttribute("overflow").getString()),n.children=r.children,r.styles.opacity=new Ua(e,"opacity",this.calculateOpacity())),"svg"===n.type){var i=this.getStyle("width",!1,!0),o=this.getStyle("height",!1,!0);i.hasValue()&&(n.attributes.width=new Ua(e,"width",i.getString())),o.hasValue()&&(n.attributes.height=new Ua(e,"height",o.getString()))}var a=n.parent;n.parent=this,n.render(t),n.parent=a}}},{key:"getBoundingBox",value:function(t){var e=this.element;return e?e.getBoundingBox(t):null}},{key:"elementTransform",value:function(){var t=this.document,e=this.element;return hs.fromElement(t,e)}},{key:"element",get:function(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}]),r}(dc);function rf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}function nf(t,e,r,n,i,o){return t[r*n*4+4*e+o]}function of(t,e,r,n,i,o,a){t[r*n*4+4*e+o]=a}function af(t,e,r){return t[e]*r}function uf(t,e,r,n){return e+Math.cos(t)*r+Math.sin(t)*n}var sf=function(t){uu(r,t);var e=rf(r);function r(t,n,i){var o;Po(this,r),(o=e.call(this,t,n,i)).type="feColorMatrix";var a=Yi(o.getAttribute("values").getString());switch(o.getAttribute("type").getString("matrix")){case"saturate":var u=a[0];a=[.213+.787*u,.715-.715*u,.072-.072*u,0,0,.213-.213*u,.715+.285*u,.072-.072*u,0,0,.213-.213*u,.715-.715*u,.072+.928*u,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var s=a[0]*Math.PI/180;a=[uf(s,.213,.787,-.213),uf(s,.715,-.715,-.715),uf(s,.072,-.072,.928),0,0,uf(s,.213,-.213,.143),uf(s,.715,.285,.14),uf(s,.072,-.072,-.283),0,0,uf(s,.213,-.213,-.787),uf(s,.715,-.715,.715),uf(s,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}return o.matrix=a,o.includeOpacity=o.getAttribute("includeOpacity").hasValue(),o}return Eo(r,[{key:"apply",value:function(t,e,r,n,i){for(var o=this.includeOpacity,a=this.matrix,u=t.getImageData(0,0,n,i),s=0;s<i;s++)for(var c=0;c<n;c++){var l=nf(u.data,c,s,n,0,0),f=nf(u.data,c,s,n,0,1),h=nf(u.data,c,s,n,0,2),p=nf(u.data,c,s,n,0,3),v=af(a,0,l)+af(a,1,f)+af(a,2,h)+af(a,3,p)+af(a,4,1),y=af(a,5,l)+af(a,6,f)+af(a,7,h)+af(a,8,p)+af(a,9,1),d=af(a,10,l)+af(a,11,f)+af(a,12,h)+af(a,13,p)+af(a,14,1),g=af(a,15,l)+af(a,16,f)+af(a,17,h)+af(a,18,p)+af(a,19,1);o&&(v=0,y=0,d=0,g*=p/255),of(u.data,c,s,n,0,0,v),of(u.data,c,s,n,0,1,y),of(u.data,c,s,n,0,2,d),of(u.data,c,s,n,0,3,g)}t.clearRect(0,0,n,i),t.putImageData(u,0,0)}}]),r}(ps);function cf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var lf=function(t){uu(r,t);var e=cf(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="mask",t}return Eo(r,[{key:"apply",value:function(t,e){var n=this.document,i=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!a&&!u){var s=new Fs;this.children.forEach((function(e){s.addBoundingBox(e.getBoundingBox(t))})),i=Math.floor(s.x1),o=Math.floor(s.y1),a=Math.floor(s.width),u=Math.floor(s.height)}var c=this.removeStyles(e,r.ignoreStyles),l=n.createCanvas(i+a,o+u),f=l.getContext("2d");n.screen.setDefaults(f),this.renderChildren(f),new sf(n,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(f,0,0,i+a,o+u);var h=n.createCanvas(i+a,o+u),p=h.getContext("2d");n.screen.setDefaults(p),e.render(p),p.globalCompositeOperation="destination-in",p.fillStyle=f.createPattern(l,"no-repeat"),p.fillRect(0,0,i+a,o+u),t.fillStyle=p.createPattern(h,"no-repeat"),t.fillRect(0,0,i+a,o+u),this.restoreStyles(e,c)}},{key:"render",value:function(t){}}]),r}(ps);lf.ignoreStyles=["mask","transform","clip-path"];var ff=w("Reflect","apply"),hf=Function.apply,pf=!P((function(){ff((function(){}))}));function vf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}fe({target:"Reflect",stat:!0,forced:pf},{apply:function(t,e,r){return G(t),H(r),ff?ff(t,e,r):hf.call(t,e,r)}}),fe({target:"Reflect",stat:!0,sham:!Eu},{getPrototypeOf:function(t){return Nu(H(t))}});var yf=function(){},df=function(t){uu(r,t);var e=vf(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="clipPath",t}return Eo(r,[{key:"apply",value:function(t){var e=this.document,r=Reflect.getPrototypeOf(t),n=t.beginPath,i=t.closePath;r&&(r.beginPath=yf,r.closePath=yf),Reflect.apply(n,t,[]),this.children.forEach((function(n){if(void 0!==n.path){var o=void 0!==n.elementTransform?n.elementTransform():null;o||(o=hs.fromElement(e,n)),o&&o.apply(t),n.path(t),r&&(r.closePath=i),o&&o.unapply(t)}})),Reflect.apply(i,t,[]),t.clip(),r&&(r.beginPath=n,r.closePath=i)}},{key:"render",value:function(t){}}]),r}(ps);function gf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var mf=function(t){uu(r,t);var e=gf(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="filter",t}return Eo(r,[{key:"apply",value:function(t,e){var n=this.document,i=this.children,o=e.getBoundingBox(t);if(o){var a=0,u=0;i.forEach((function(t){var e=t.extraFilterDistance||0;a=Math.max(a,e),u=Math.max(u,e)}));var s=Math.floor(o.width),c=Math.floor(o.height),l=s+2*a,f=c+2*u;if(!(l<1||f<1)){var h=Math.floor(o.x),p=Math.floor(o.y),v=this.removeStyles(e,r.ignoreStyles),y=n.createCanvas(l,f),d=y.getContext("2d");n.screen.setDefaults(d),d.translate(-h+a,-p+u),e.render(d),i.forEach((function(t){"function"==typeof t.apply&&t.apply(d,0,0,l,f)})),t.drawImage(y,0,0,l,f,h-a,p-u,l,f),this.restoreStyles(e,v)}}}},{key:"render",value:function(t){}}]),r}(ps);function xf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}mf.ignoreStyles=["filter","transform","clip-path"];var bf=function(t){uu(r,t);var e=xf(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="feDropShadow",o.addStylesFromStyleDefinition(),o}return Eo(r,[{key:"apply",value:function(t,e,r,n,i){}}]),r}(ps);function wf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Sf=function(t){uu(r,t);var e=wf(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="feMorphology",t}return Eo(r,[{key:"apply",value:function(t,e,r,n,i){}}]),r}(ps);function Of(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var kf=function(t){uu(r,t);var e=Of(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="feComposite",t}return Eo(r,[{key:"apply",value:function(t,e,r,n,i){}}]),r}(ps);function Tf(t){return(Tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Af=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],Rf=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function Pf(t,e,r,n,i,o){if(!(isNaN(o)||o<1)){o|=0;var a=function(t,e,r,n,i){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==Tf(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var o=t.getContext("2d");try{return o.getImageData(e,r,n,i)}catch(t){throw new Error("unable to access image data: "+t)}}(t,e,r,n,i);a=function(t,e,r,n,i,o){for(var a,u=t.data,s=2*o+1,c=n-1,l=i-1,f=o+1,h=f*(f+1)/2,p=new Ef,v=p,y=1;y<s;y++)v=v.next=new Ef,y===f&&(a=v);v.next=p;for(var d=null,g=null,m=0,x=0,b=Af[o],w=Rf[o],S=0;S<i;S++){v=p;for(var O=u[x],k=u[x+1],T=u[x+2],A=u[x+3],R=0;R<f;R++)v.r=O,v.g=k,v.b=T,v.a=A,v=v.next;for(var P=0,E=0,C=0,M=0,N=f*O,_=f*k,I=f*T,V=f*A,L=h*O,B=h*k,j=h*T,D=h*A,F=1;F<f;F++){var z=x+((c<F?c:F)<<2),U=u[z],H=u[z+1],X=u[z+2],Y=u[z+3],G=f-F;L+=(v.r=U)*G,B+=(v.g=H)*G,j+=(v.b=X)*G,D+=(v.a=Y)*G,P+=U,E+=H,C+=X,M+=Y,v=v.next}d=p,g=a;for(var W=0;W<n;W++){var q=D*b>>w;if(u[x+3]=q,0!==q){var $=255/q;u[x]=(L*b>>w)*$,u[x+1]=(B*b>>w)*$,u[x+2]=(j*b>>w)*$}else u[x]=u[x+1]=u[x+2]=0;L-=N,B-=_,j-=I,D-=V,N-=d.r,_-=d.g,I-=d.b,V-=d.a;var Q=W+o+1;Q=m+(Q<c?Q:c)<<2,L+=P+=d.r=u[Q],B+=E+=d.g=u[Q+1],j+=C+=d.b=u[Q+2],D+=M+=d.a=u[Q+3],d=d.next;var Z=g,K=Z.r,J=Z.g,tt=Z.b,et=Z.a;N+=K,_+=J,I+=tt,V+=et,P-=K,E-=J,C-=tt,M-=et,g=g.next,x+=4}m+=n}for(var rt=0;rt<n;rt++){var nt=u[x=rt<<2],it=u[x+1],ot=u[x+2],at=u[x+3],ut=f*nt,st=f*it,ct=f*ot,lt=f*at,ft=h*nt,ht=h*it,pt=h*ot,vt=h*at;v=p;for(var yt=0;yt<f;yt++)v.r=nt,v.g=it,v.b=ot,v.a=at,v=v.next;for(var dt=n,gt=0,mt=0,xt=0,bt=0,wt=1;wt<=o;wt++){x=dt+rt<<2;var St=f-wt;ft+=(v.r=nt=u[x])*St,ht+=(v.g=it=u[x+1])*St,pt+=(v.b=ot=u[x+2])*St,vt+=(v.a=at=u[x+3])*St,bt+=nt,gt+=it,mt+=ot,xt+=at,v=v.next,wt<l&&(dt+=n)}x=rt,d=p,g=a;for(var Ot=0;Ot<i;Ot++){var kt=x<<2;u[kt+3]=at=vt*b>>w,at>0?(at=255/at,u[kt]=(ft*b>>w)*at,u[kt+1]=(ht*b>>w)*at,u[kt+2]=(pt*b>>w)*at):u[kt]=u[kt+1]=u[kt+2]=0,ft-=ut,ht-=st,pt-=ct,vt-=lt,ut-=d.r,st-=d.g,ct-=d.b,lt-=d.a,kt=rt+((kt=Ot+f)<l?kt:l)*n<<2,ft+=bt+=d.r=u[kt],ht+=gt+=d.g=u[kt+1],pt+=mt+=d.b=u[kt+2],vt+=xt+=d.a=u[kt+3],d=d.next,ut+=nt=g.r,st+=it=g.g,ct+=ot=g.b,lt+=at=g.a,bt-=nt,gt-=it,mt-=ot,xt-=at,g=g.next,x+=n}}return t}(a,0,0,n,i,o),t.getContext("2d").putImageData(a,e,r)}}var Ef=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};function Cf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Mf=function(t){uu(r,t);var e=Cf(r);function r(t,n,i){var o;return Po(this,r),(o=e.call(this,t,n,i)).type="feGaussianBlur",o.blurRadius=Math.floor(o.getAttribute("stdDeviation").getNumber()),o.extraFilterDistance=o.blurRadius,o}return Eo(r,[{key:"apply",value:function(t,e,r,n,i){var o=this.document,a=this.blurRadius,u=o.window?o.window.document.body:null,s=t.canvas;s.id=o.getUniqueId(),u&&(s.style.display="none",u.appendChild(s)),Pf(s,e,r,n,i,a),u&&u.removeChild(s)}}]),r}(ps);function Nf(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var _f=function(t){uu(r,t);var e=Nf(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="title",t}return r}(ps);function If(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=pu(t);if(e){var i=pu(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return fu(this,r)}}var Vf=function(t){uu(r,t);var e=If(r);function r(){var t;return Po(this,r),(t=e.apply(this,arguments)).type="desc",t}return r}(ps),Lf={svg:Pc,rect:Cc,circle:Nc,ellipse:Ic,line:Lc,polyline:jc,polygon:Fc,path:mc,pattern:Uc,marker:Xc,defs:Gc,linearGradient:Kc,radialGradient:tl,stop:rl,animate:pl,animateColor:yl,animateTransform:gl,font:wl,"font-face":Ol,"missing-glyph":Tl,glyph:bc,text:Sc,tspan:kc,tref:Rl,a:El,textPath:Yl,image:ql,g:qc,symbol:Ql,style:Jl,use:ef,mask:lf,clipPath:df,filter:mf,feDropShadow:bf,feMorphology:Sf,feComposite:kf,feColorMatrix:sf,feGaussianBlur:Mf,title:_f,desc:Vf};function Bf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jf(){return(jf=gn(dn.mark((function t(e){var r,n,i=arguments;return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=i.length>1&&void 0!==i[1]&&i[1],n=document.createElement("img"),r&&(n.crossOrigin="Anonymous"),t.abrupt("return",new Promise((function(t,r){n.onload=function(){t(n)},n.onerror=function(t,e,n,i,o){r(o)},n.src=e})));case 4:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var Df=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.rootEmSize,i=void 0===n?12:n,o=r.emSize,a=void 0===o?12:o,u=r.createCanvas,s=void 0===u?t.createCanvas:u,c=r.createImage,l=void 0===c?t.createImage:c,f=r.anonymousCrossOrigin;Po(this,t),this.canvg=e,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=e.screen,this.rootEmSize=i,this.emSize=a,this.createCanvas=s,this.createImage=this.bindCreateImage(l,f),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}return Eo(t,[{key:"bindCreateImage",value:function(t,e){return"boolean"==typeof e?function(r,n){return t(r,"boolean"==typeof n?n:e)}:t}},{key:"popEmSize",value:function(){this.emSizeStack.pop()}},{key:"getUniqueId",value:function(){return"canvg".concat(++this.uniqueId)}},{key:"isImagesLoaded",value:function(){return this.images.every((function(t){return t.loaded}))}},{key:"isFontsLoaded",value:function(){return this.fonts.every((function(t){return t.loaded}))}},{key:"createDocumentElement",value:function(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}},{key:"createElement",value:function(e){var r=e.nodeName.replace(/^[^:]+:/,""),n=t.elementTypes[r];return void 0!==n?new n(this,e):new ys(this,e)}},{key:"createTextNode",value:function(t){return new Ac(this,t)}},{key:"setViewBox",value:function(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Bf(Object(r),!0).forEach((function(e){Ro(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Bf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}},{key:"window",get:function(){return this.screen.window}},{key:"fetch",get:function(){return this.screen.fetch}},{key:"ctx",get:function(){return this.screen.ctx}},{key:"emSize",get:function(){var t=this.emSizeStack;return t[t.length-1]},set:function(t){this.emSizeStack.push(t)}}]),t}();function Ff(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function zf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ff(Object(r),!0).forEach((function(e){Ro(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ff(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}Df.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},Df.createImage=function(t){return jf.apply(this,arguments)},Df.elementTypes=Lf;var Uf=function(){function t(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Po(this,t),this.parser=new ou(n),this.screen=new ru(e,n),this.options=n;var i=new Df(this,n),o=i.createDocumentElement(r);this.document=i,this.documentElement=o}var e,r;return Eo(t,[{key:"fork",value:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.from(e,r,zf(zf({},this.options),n))}},{key:"forkString",value:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.fromString(e,r,zf(zf({},this.options),n))}},{key:"ready",value:function(){return this.screen.ready()}},{key:"isReady",value:function(){return this.screen.isReady()}},{key:"render",value:(r=gn(dn.mark((function t(){var e,r=arguments;return dn.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=r.length>0&&void 0!==r[0]?r[0]:{},this.start(zf({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},e)),t.next=4,this.ready();case 4:this.stop();case 5:case"end":return t.stop()}}),t,this)}))),function(){return r.apply(this,arguments)})},{key:"start",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=this.documentElement,r=this.screen,n=this.options;r.start(e,zf(zf({enableRedraw:!0},n),t))}},{key:"stop",value:function(){this.screen.stop()}},{key:"resize",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}],[{key:"from",value:(e=gn(dn.mark((function e(r,n){var i,o,a,u=arguments;return dn.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=u.length>2&&void 0!==u[2]?u[2]:{},o=new ou(i),e.next=4,o.parse(n);case 4:return a=e.sent,e.abrupt("return",new t(r,a,i));case 6:case"end":return e.stop()}}),e)}))),function(t,r){return e.apply(this,arguments)})},{key:"fromString",value:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new ou(n),o=i.parseFromString(r);return new t(e,o,n)}}]),t}();t.AElement=El,t.AnimateColorElement=yl,t.AnimateElement=pl,t.AnimateTransformElement=gl,t.BoundingBox=Fs,t.CB1=bo,t.CB2=wo,t.CB3=So,t.CB4=Oo,t.Canvg=Uf,t.CircleElement=Nc,t.ClipPathElement=df,t.DefsElement=Gc,t.DescElement=Vf,t.Document=Df,t.Element=ps,t.EllipseElement=Ic,t.FeColorMatrixElement=sf,t.FeCompositeElement=kf,t.FeDropShadowElement=bf,t.FeGaussianBlurElement=Mf,t.FeMorphologyElement=Sf,t.FilterElement=mf,t.Font=Ds,t.FontElement=wl,t.FontFaceElement=Ol,t.GElement=qc,t.GlyphElement=bc,t.GradientElement=Qc,t.ImageElement=ql,t.LineElement=Lc,t.LinearGradientElement=Kc,t.MarkerElement=Xc,t.MaskElement=lf,t.Matrix=os,t.MissingGlyphElement=Tl,t.Mouse=Ja,t.PSEUDO_ZERO=yo,t.Parser=ou,t.PathElement=mc,t.PathParser=sc,t.PatternElement=Uc,t.Point=Ka,t.PolygonElement=Fc,t.PolylineElement=jc,t.Property=Ua,t.QB1=ko,t.QB2=To,t.QB3=Ao,t.RadialGradientElement=tl,t.RectElement=Cc,t.RenderedElement=dc,t.Rotate=ns,t.SVGElement=Pc,t.SVGFontLoader=Zl,t.Scale=is,t.Screen=ru,t.Skew=us,t.SkewX=cs,t.SkewY=fs,t.StopElement=rl,t.StyleElement=Jl,t.SymbolElement=Ql,t.TRefElement=Rl,t.TSpanElement=kc,t.TextElement=Sc,t.TextPathElement=Yl,t.TitleElement=_f,t.Transform=hs,t.Translate=rs,t.UnknownElement=ys,t.UseElement=ef,t.ViewPort=Ha,t.compressSpaces=Ui,t.default=Uf,t.getSelectorSpecificity=vo,t.normalizeAttributeName=Wi,t.normalizeColor=$i,t.parseExternalUrl=qi,t.presets=mn,t.toNumbers=Yi,t.trimLeft=Hi,t.trimRight=Xi,t.vectorMagnitude=go,t.vectorsAngle=xo,t.vectorsRatio=mo,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=umd.js.map
