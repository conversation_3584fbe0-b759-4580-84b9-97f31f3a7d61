{"version": 3, "sources": ["../../src/lib/is-edge-runtime.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\nimport { SERVER_RUNTIME } from './constants'\n\nexport function isEdgeRuntime(value?: string): value is ServerRuntime {\n  return (\n    value === SERVER_RUNTIME.experimentalEdge || value === SERVER_RUNTIME.edge\n  )\n}\n"], "names": ["SERVER_RUNTIME", "isEdgeRuntime", "value", "experimentalEdge", "edge"], "mappings": "AACA,SAASA,cAAc,QAAQ,cAAa;AAE5C,OAAO,SAASC,cAAcC,KAAc;IAC1C,OACEA,UAAUF,eAAeG,gBAAgB,IAAID,UAAUF,eAAeI,IAAI;AAE9E"}