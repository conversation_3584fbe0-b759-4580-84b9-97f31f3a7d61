import React from 'react';
import { Chip } from '@mui/material';
import { formatStatut, normalizeStatus } from '../utils/formatters';

const StatutBadge = ({ statut }) => {
  // Normaliser le statut pour la détermination de la couleur
  const normalizedStatus = normalizeStatus(statut);

  const getColor = () => {
    switch (normalizedStatus) {
      case 'PAID':
      case 'ACCEPTED':
        return 'success';
      case 'SENT':
      case 'PENDING':
      case 'WAITING_APPROVAL':
      case 'APPROVED_INTERNAL':
        return 'warning';
      case 'DRAFT':
        return 'info';
      case 'CANCELED':
      case 'REJECTED':
      case 'EXPIRED':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Chip
      label={formatStatut(statut)}
      color={getColor()}
      size="small"
      variant="outlined"
      sx={{ fontWeight: 'medium' }}
    />
  );
};

export default StatutBadge;