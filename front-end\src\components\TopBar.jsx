import React, { useState } from "react";
import {
  AppBar,
  <PERSON><PERSON><PERSON>,
  Typography,
  IconButton,
  Avatar,
  Box,
  Menu,
  MenuItem,
  Tooltip,
  useTheme,
  InputBase,
  alpha,
  Divider,
  styled,
  Button,
  Chip,
  Paper,
  List,
  ListItemText,
  ListSubheader,
} from "@mui/material";
import {
  Search as SearchIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  ArrowDropDown as ArrowDropDownIcon,
  AddCircleOutline as AddCircleOutlineIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.mode === "dark"
    ? alpha(theme.palette.background.paper, 0.9)
    : alpha(theme.palette.background.paper, 0.9),
  backdropFilter: "blur(8px)",
  color: theme.palette.text.primary,
  boxShadow: theme.palette.mode === "dark"
    ? "0 1px 0 rgba(255, 255, 255, 0.05)"
    : "0 1px 3px rgba(0, 0, 0, 0.05)",
  height: 64,
  borderBottom: `1px solid ${theme.palette.mode === "dark"
    ? "rgba(255, 255, 255, 0.05)"
    : "rgba(0, 0, 0, 0.05)"}`,
}));

const SearchBox = styled(Paper)(({ theme }) => ({
  position: "relative",
  borderRadius: theme.shape.borderRadius * 3,
  backgroundColor: theme.palette.mode === "dark"
    ? alpha(theme.palette.background.default, 0.7)
    : alpha(theme.palette.common.white, 0.9),
  border: `1px solid ${theme.palette.mode === "dark"
    ? "rgba(255, 255, 255, 0.05)"
    : "rgba(0, 0, 0, 0.05)"}`,
  boxShadow: theme.palette.mode === "dark"
    ? "none"
    : "0 2px 5px rgba(0, 0, 0, 0.05)",
  padding: theme.spacing(0, 1),
  marginLeft: 0,
  width: "100%",
  [theme.breakpoints.up("sm")]: {
    marginLeft: theme.spacing(1),
    width: "400px",
  },
  display: "flex",
  alignItems: "center",
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 1),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  width: "100%",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1.25, 1, 1.25, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
  },
}));

const ActionButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.mode === "dark"
    ? theme.palette.text.secondary
    : theme.palette.text.primary,
  backgroundColor: theme.palette.mode === "dark"
    ? alpha(theme.palette.background.default, 0.6)
    : alpha(theme.palette.background.paper, 0.8),
  border: `1px solid ${theme.palette.mode === "dark"
    ? "rgba(255, 255, 255, 0.05)"
    : "rgba(0, 0, 0, 0.05)"}`,
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(1),
  marginLeft: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.mode === "dark"
      ? alpha(theme.palette.background.default, 0.8)
      : alpha(theme.palette.background.default, 0.1),
  }
}));



const TopBar = ({ title, darkMode, toggleDarkMode, user }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);

  const [newDocumentAnchorEl, setNewDocumentAnchorEl] = useState(null);

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };



  const handleNewDocumentOpen = (event) => {
    setNewDocumentAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };



  const handleNewDocumentClose = () => {
    setNewDocumentAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
    handleMenuClose();
  };

  const handleProfileClick = () => {
    navigate("/profile");
    handleMenuClose();
  };

  const handleSettingsClick = () => {
    navigate("/parametres");
    handleMenuClose();
  };

  const handleNewInvoice = () => {
    navigate("/factures/new");
    handleNewDocumentClose();
  };

  const handleNewQuote = () => {
    navigate("/devis/new");
    handleNewDocumentClose();
  };

  const handleNewClient = () => {
    navigate("/clients?new=true");
    handleNewDocumentClose();
  };

  const getUserInitials = () => {
    if (!user || !user.name) return "U";
    return user.name.charAt(0).toUpperCase();
  };



  return (
    <StyledAppBar position="sticky">
      <Toolbar sx={{
        minHeight: '64px !important',
        height: 64,
        padding: '0 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{
            display: { xs: "none", sm: "block" },
            fontWeight: 700,
            mr: 2,
            fontSize: '1.125rem',
            letterSpacing: '-0.01em'
          }}
        >
          {title}
        </Typography>

        <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: { xs: 'flex-end', md: 'center' } }}>
          <SearchBox elevation={0}>
            <SearchIconWrapper>
              <SearchIcon sx={{ fontSize: 20, color: theme.palette.text.secondary }} />
            </SearchIconWrapper>
            <StyledInputBase
              placeholder="Rechercher des factures, clients, produits..."
              inputProps={{ "aria-label": "search" }}
            />
            <Chip
              label="Ctrl+K"
              size="small"
              sx={{
                height: 24,
                mr: 1,
                ml: 'auto',
                backgroundColor: theme.palette.mode === "dark"
                  ? alpha(theme.palette.background.default, 0.6)
                  : alpha(theme.palette.background.default, 0.5),
                color: theme.palette.text.secondary,
                fontSize: '0.75rem',
                fontWeight: 500,
                letterSpacing: '0.02em'
              }}
            />
          </SearchBox>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddCircleOutlineIcon />}
            endIcon={<ArrowDropDownIcon />}
            sx={{
              fontWeight: 600,
              borderRadius: 3,
              px: 2,
              display: { xs: 'none', md: 'flex' },
              boxShadow: theme.palette.mode === "dark"
                ? "0 3px 12px rgba(58, 110, 165, 0.3)"
                : "0 4px 14px rgba(58, 110, 165, 0.2)",
              '&:hover': {
                boxShadow: theme.palette.mode === "dark"
                  ? "0 6px 18px rgba(58, 110, 165, 0.4)"
                  : "0 6px 20px rgba(58, 110, 165, 0.25)",
              }
            }}
            onClick={handleNewDocumentOpen}
          >
            Nouveau
          </Button>

          <IconButton
            size="medium"
            sx={{ display: { xs: 'flex', md: 'none' } }}
            onClick={handleNewDocumentOpen}
          >
            <AddCircleOutlineIcon />
          </IconButton>



          <Tooltip title="Mode sombre">
            <ActionButton onClick={toggleDarkMode} size="medium">
              {darkMode ? <Brightness7Icon fontSize="small" /> : <Brightness4Icon fontSize="small" />}
            </ActionButton>
          </Tooltip>

          <ActionButton
            edge="end"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            size="medium"
            sx={{ ml: 1.5 }}
          >
            <Avatar sx={{
              width: 30,
              height: 30,
              bgcolor: theme.palette.primary.main,
              fontSize: 14,
              fontWeight: 700,
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)"
            }}>
              {getUserInitials()}
            </Avatar>
          </ActionButton>
        </Box>
      </Toolbar>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        keepMounted
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 3,
          sx: {
            mt: 1.5,
            borderRadius: 3,
            minWidth: 180,
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: theme.palette.background.paper,
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
      >
        {user && (
          <Box sx={{ px: 2.5, py: 2 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 700, mb: 0.5 }}>
              {user.name}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                mb: 1,
                display: 'flex',
                alignItems: 'center',
                gap: 0.5
              }}
            >
              {user.email}
            </Typography>
            <Chip
              label="Administrateur"
              size="small"
              color="primary"
              sx={{
                height: 24,
                fontWeight: 600,
                fontSize: '0.7rem',
              }}
            />
            <Divider sx={{ my: 1.5 }} />
          </Box>
        )}

        <MenuItem onClick={handleProfileClick} sx={{ py: 1.25, px: 2.5 }}>
          <PersonIcon fontSize="small" sx={{ mr: 1.5, color: theme.palette.primary.main }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Mon profil
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleSettingsClick} sx={{ py: 1.25, px: 2.5 }}>
          <SettingsIcon fontSize="small" sx={{ mr: 1.5, color: theme.palette.text.secondary }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Paramètres
          </Typography>
        </MenuItem>
        <Divider sx={{ my: 1 }} />
        <MenuItem onClick={handleLogout} sx={{ py: 1.25, px: 2.5 }}>
          <LogoutIcon fontSize="small" sx={{ mr: 1.5, color: theme.palette.error.main }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600, color: theme.palette.error.main }}>
            Déconnexion
          </Typography>
        </MenuItem>
      </Menu>



      {/* New Document Menu */}
      <Menu
        anchorEl={newDocumentAnchorEl}
        open={Boolean(newDocumentAnchorEl)}
        onClose={handleNewDocumentClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            mt: 1.5,
            borderRadius: 3,
            minWidth: 180,
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: theme.palette.background.paper,
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
      >
        <List sx={{ py: 1.5 }}>
          <ListSubheader sx={{
            background: 'transparent',
            lineHeight: '24px',
            px: 2.5,
            color: theme.palette.text.secondary,
            fontWeight: 600,
            fontSize: '0.7rem',
            letterSpacing: '0.05em',
            textTransform: 'uppercase'
          }}>
            Créer nouveau
          </ListSubheader>
          <MenuItem onClick={handleNewInvoice} sx={{ py: 1.25, px: 2.5 }}>
            <ListItemText
              primary={
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Nouvelle facture
                </Typography>
              }
            />
          </MenuItem>
          <MenuItem onClick={handleNewQuote} sx={{ py: 1.25, px: 2.5 }}>
            <ListItemText
              primary={
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Nouveau devis
                </Typography>
              }
            />
          </MenuItem>
          <MenuItem onClick={handleNewClient} sx={{ py: 1.25, px: 2.5 }}>
            <ListItemText
              primary={
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Nouveau client
                </Typography>
              }
            />
          </MenuItem>
        </List>
      </Menu>
    </StyledAppBar>
  );
};

export default TopBar;