const express = require('express');
const app = express();
const cors = require('cors');
const path = require('path');
const { router: factureRoutes } = require('./routes/FactureRoute');
const clientRoutes = require('./routes/ClientRoute');
const templateRoutes = require('./routes/TemplateRoute');

const baseTemplateRoutes = require('./routes/BaseTemplateRoute');
const responsableTemplateRoutes = require('./routes/ResponsableTemplateRoute');
const entrepriseRoutes = require('./routes/EntrepriseRoute');
const parametresRoutes = require('./routes/ParametresRoute');
const profileRoutes = require('./routes/ProfileRoute');
const vendeurRoutes = require('./routes/VendeurRoute');
const analyticsRoutes = require('./routes/AnalyticsRoute');
const adminAnalyticsRoutes = require('./routes/AdminAnalyticsRoute');
const systemAnalyticsRoutes = require('./routes/SystemAnalyticsRoute');
const exportRoutes = require('./routes/ExportRoute'); // Export routes for data export
const userMetricsRoutes = require('./routes/UserMetricsRoute');
const abonnementRoutes = require('./routes/AbonnementRoute'); // Routes pour la gestion des abonnements
const responsableEntrepriseRoutes = require('./routes/ResponsableEntrepriseRoute'); // Routes pour les entreprises des responsables
const livreurRoutes = require('./routes/LivreurRoute'); // Routes pour la gestion des livreurs
const bonLivraisonRoutes = require('./routes/BonLivraisonRoute'); // Routes pour la gestion des bons de livraison
const { initSubscriptionChecker } = require('./tasks/subscriptionStatusChecker'); // Subscription status checker

// Connexion à la base de données
require('./connectdb.js');

// Fonction pour initialiser l'administrateur par défaut
const initializeAdmin = async () => {
  try {
    // Importer les modules nécessaires
    const User = require('./models/UserModel');
    const bcrypt = require('bcrypt');

    // Vérifier si l'administrateur existe déjà
    const adminExists = await User.findOne({ email: '<EMAIL>' });

    if (adminExists) {
      console.log('L\'administrateur par défaut existe déjà');
      return;
    }

    // Informations de l'administrateur par défaut
    const defaultAdmin = {
      nom: 'Administrateur',
      prenom: 'Système',
      email: '<EMAIL>',
      motDePasse: 'Admin123',
      role: 'ADMIN',
      nomEntreprise: 'Factures App',
      adresseEntreprise: 'Adresse du système',
      telephoneEntreprise: '0123456789',
      numeroFiscal: 'ADMIN001',
      dateCreation: new Date()
    };

    // Hasher le mot de passe
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(defaultAdmin.motDePasse, salt);

    // Créer le nouvel administrateur
    const newAdmin = new User({
      ...defaultAdmin,
      motDePasse: hashedPassword
    });

    // Sauvegarder l'administrateur dans la base de données
    await newAdmin.save();

    console.log('Administrateur par défaut créé avec succès');
  } catch (error) {
    console.error('Erreur lors de la création de l\'administrateur par défaut:', error);
  }
};

// Initialiser l'administrateur au démarrage du serveur
initializeAdmin();

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5000', 'http://localhost:5173'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Length', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400, // 24 hours in seconds
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true })); // Add this to handle form-data for file uploads

// Serve static files for uploads
app.use('/uploads', express.static('uploads'));

// Routes
app.use('/api/user', require('./routes/UserRoute.js'));
app.use('/api/produit', require('./routes/ProduitRoute.js'));
app.use('/api/devis', require('./routes/DevisRoute.js'));
app.use('/api/client', require('./routes/ClientRoute.js'));
app.use('/api/paiement', require('./routes/PaiementRoute.js'));
app.use('/api/auth', require('./routes/AuthRoute.js'));
app.use('/api', factureRoutes);
app.use('/api', clientRoutes);
app.use('/api', templateRoutes);

app.use('/api', baseTemplateRoutes); // Add the new BaseTemplateRoute
app.use('/api', responsableTemplateRoutes); // Add the new ResponsableTemplateRoute
app.use('/api/entreprise', entrepriseRoutes);
app.use('/api', parametresRoutes); // Add the new ParametresRoute
app.use('/api', profileRoutes); // Add the new ProfileRoute
app.use('/api', vendeurRoutes); // Add the new VendeurRoute
app.use('/api', analyticsRoutes); // Add the Analytics routes
app.use('/api', adminAnalyticsRoutes); // Add the Admin Analytics routes
app.use('/api', systemAnalyticsRoutes); // Add the System Analytics routes
app.use('/api', exportRoutes); // Add the new ExportRoute
app.use('/api', userMetricsRoutes); // Add userMetrics routes
app.use('/api/abonnements', abonnementRoutes); // Add abonnement routes
app.use('/api', responsableEntrepriseRoutes); // Add responsable entreprise routes
app.use('/api', livreurRoutes); // Add livreur routes
app.use('/api', bonLivraisonRoutes); // Add bon de livraison routes

// Catch-all route for debugging
app.use((req, res) => {
  console.log(`Route not found: ${req.method} ${req.url}`);
  res.status(404).json({ message: 'Route not found' });
});

const PORT = process.env.PORT || 5000; // Changed to port 5000 to match frontend configuration

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // Initialize the subscription status checker
  initSubscriptionChecker();
  console.log('Subscription status checker initialized');
});