:root {
    /* <PERSON><PERSON><PERSON> */
    --primary-color: #1976d2;
    --primary-light: #42a5f5;
    --primary-dark: #1565c0;
    --primary-contrast: #ffffff;
  
    --secondary-color: #9c27b0;
    --secondary-light: #ba68c8;
    --secondary-dark: #7b1fa2;
    --secondary-contrast: #ffffff;
  
    --error-color: #d32f2f;
    --error-light: #ef5350;
    --error-dark: #c62828;
    --error-contrast: #ffffff;
  
    --warning-color: #ed6c02;
    --warning-light: #ff9800;
    --warning-dark: #e65100;
    --warning-contrast: #ffffff;
  
    --info-color: #0288d1;
    --info-light: #03a9f4;
    --info-dark: #01579b;
    --info-contrast: #ffffff;
  
    --success-color: #2e7d32;
    --success-light: #4caf50;
    --success-dark: #1b5e20;
    --success-contrast: #ffffff;
  
    /* Arrière-plans */
    --background-default: #f5f5f5;
    --background-paper: #ffffff;
    --background-dark: #212121;
  
    /* Texte */
    --text-primary: rgba(0, 0, 0, 0.87);
    --text-secondary: rgba(0, 0, 0, 0.6);
    --text-disabled: rgba(0, 0, 0, 0.38);
    --text-inverse: rgba(255, 255, 255, 0.87);
  
    /* Bordures */
    --border-color: rgba(0, 0, 0, 0.12);
    --border-radius: 8px;
    --border-radius-lg: 12px;
  
    /* Ombres */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.1);
  
    /* Espacements */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
  
    /* Z-index */
    --z-index-appbar: 1100;
    --z-index-drawer: 1200;
    --z-index-modal: 1300;
    --z-index-snackbar: 1400;
    --z-index-tooltip: 1500;
  
    /* Transitions */
    --transition-short: all 0.2s ease;
    --transition-medium: all 0.3s ease;
    --transition-long: all 0.5s ease;
  
    /* Largeurs */
    --sidebar-width: 240px;
    --max-content-width: 1200px;
    --container-padding: 24px;
  
    /* Hauteurs */
    --header-height: 64px;
    --footer-height: 56px;
  }
  
  /* Media queries variables */
  @custom-media --small-viewport (max-width: 599px);
  @custom-media --medium-viewport (min-width: 600px) and (max-width: 959px);
  @custom-media --large-viewport (min-width: 960px);