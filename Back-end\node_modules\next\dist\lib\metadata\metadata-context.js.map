{"version": 3, "sources": ["../../../src/lib/metadata/metadata-context.tsx"], "sourcesContent": ["import type { AppRenderContext } from '../../server/app-render/app-render'\nimport type { MetadataContext } from './types/resolvers'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport { trackFallbackParamAccessed } from '../../server/app-render/dynamic-rendering'\n\nexport function createMetadataContext(\n  pathname: string,\n  renderOpts: AppRenderContext['renderOpts']\n): MetadataContext {\n  return {\n    pathname,\n    trailingSlash: renderOpts.trailingSlash,\n    isStaticMetadataRouteFile: false,\n  }\n}\n\nexport function createTrackedMetadataContext(\n  pathname: string,\n  renderOpts: AppRenderContext['renderOpts'],\n  workStore: WorkStore | null\n): MetadataContext {\n  return {\n    // Use the regular metadata context, but we trap the pathname access.\n    ...createMetadataContext(pathname, renderOpts),\n\n    // Setup the trap around the pathname access so we can track when the\n    // pathname is accessed while resolving metadata which would indicate it's\n    // being used to resolve a relative URL. If that's the case, we don't want\n    // to provide it, and instead we should error.\n    get pathname() {\n      if (\n        workStore &&\n        workStore.isStaticGeneration &&\n        workStore.fallbackRouteParams &&\n        workStore.fallbackRouteParams.size > 0\n      ) {\n        trackFallbackParamAccessed(workStore, 'metadata relative url resolving')\n      }\n\n      return pathname\n    },\n  }\n}\n"], "names": ["createMetadataContext", "createTrackedMetadataContext", "pathname", "renderOpts", "trailingSlash", "isStaticMetadataRouteFile", "workStore", "isStaticGeneration", "fallbackRouteParams", "size", "trackFallbackParamAccessed"], "mappings": ";;;;;;;;;;;;;;;IAKgBA,qBAAqB;eAArBA;;IAWAC,4BAA4B;eAA5BA;;;kCAb2B;AAEpC,SAASD,sBACdE,QAAgB,EAChBC,UAA0C;IAE1C,OAAO;QACLD;QACAE,eAAeD,WAAWC,aAAa;QACvCC,2BAA2B;IAC7B;AACF;AAEO,SAASJ,6BACdC,QAAgB,EAChBC,UAA0C,EAC1CG,SAA2B;IAE3B,OAAO;QACL,qEAAqE;QACrE,GAAGN,sBAAsBE,UAAUC,WAAW;QAE9C,qEAAqE;QACrE,0EAA0E;QAC1E,0EAA0E;QAC1E,8CAA8C;QAC9C,IAAID,YAAW;YACb,IACEI,aACAA,UAAUC,kBAAkB,IAC5BD,UAAUE,mBAAmB,IAC7BF,UAAUE,mBAAmB,CAACC,IAAI,GAAG,GACrC;gBACAC,IAAAA,4CAA0B,EAACJ,WAAW;YACxC;YAEA,OAAOJ;QACT;IACF;AACF"}