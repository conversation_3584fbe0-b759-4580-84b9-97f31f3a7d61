{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-edge-function-loader.ts"], "sourcesContent": ["import type webpack from 'webpack'\nimport { getModuleBuildInfo } from './get-module-build-info'\nimport { stringifyRequest } from '../stringify-request'\nimport type { MiddlewareConfig } from '../../analysis/get-page-static-info'\n\nexport type EdgeFunctionLoaderOptions = {\n  absolutePagePath: string\n  page: string\n  rootDir: string\n  preferredRegion: string | string[] | undefined\n  middlewareConfig: string\n}\n\nconst nextEdgeFunctionLoader: webpack.LoaderDefinitionFunction<EdgeFunctionLoaderOptions> =\n  function nextEdgeFunctionLoader(this) {\n    const {\n      absolutePagePath,\n      page,\n      rootDir,\n      preferredRegion,\n      middlewareConfig: middlewareConfigBase64,\n    }: EdgeFunctionLoaderOptions = this.getOptions()\n    const stringifiedPagePath = stringifyRequest(this, absolutePagePath)\n    const buildInfo = getModuleBuildInfo(this._module as any)\n    const middlewareConfig: MiddlewareConfig = JSON.parse(\n      Buffer.from(middlewareConfigBase64, 'base64').toString()\n    )\n    buildInfo.route = {\n      page: page || '/',\n      absolutePagePath,\n      preferredRegion,\n      middlewareConfig,\n    }\n    buildInfo.nextEdgeApiFunction = {\n      page: page || '/',\n    }\n    buildInfo.rootDir = rootDir\n\n    return `\n        import 'next/dist/esm/server/web/globals'\n        import { adapter } from 'next/dist/esm/server/web/adapter'\n        import { IncrementalCache } from 'next/dist/esm/server/lib/incremental-cache'\n        import { wrapApiHandler } from 'next/dist/esm/server/api-utils'\n\n        import handler from ${stringifiedPagePath}\n\n        if (typeof handler !== 'function') {\n          throw new Error('The Edge Function \"pages${page}\" must export a \\`default\\` function');\n        }\n\n        export default function nHandler (opts) {\n          return adapter({\n              ...opts,\n              IncrementalCache,\n              page: ${JSON.stringify(page)},\n              handler: wrapApiHandler(${JSON.stringify(page)}, handler),\n          })\n        }\n    `\n  }\n\nexport default nextEdgeFunctionLoader\n"], "names": ["nextEdgeFunctionLoader", "absolutePagePath", "page", "rootDir", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "stringifiedPagePath", "stringifyRequest", "buildInfo", "getModuleBuildInfo", "_module", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "route", "nextEdgeApiFunction", "stringify"], "mappings": ";;;;+BA6DA;;;eAAA;;;oCA5DmC;kCACF;AAWjC,MAAMA,yBACJ,SAASA;IACP,MAAM,EACJC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAA8B,IAAI,CAACC,UAAU;IAC9C,MAAMC,sBAAsBC,IAAAA,kCAAgB,EAAC,IAAI,EAAER;IACnD,MAAMS,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjD,MAAMP,mBAAqCQ,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACV,wBAAwB,UAAUW,QAAQ;IAExDP,UAAUQ,KAAK,GAAG;QAChBhB,MAAMA,QAAQ;QACdD;QACAG;QACAC;IACF;IACAK,UAAUS,mBAAmB,GAAG;QAC9BjB,MAAMA,QAAQ;IAChB;IACAQ,UAAUP,OAAO,GAAGA;IAEpB,OAAO,CAAC;;;;;;4BAMgB,EAAEK,oBAAoB;;;mDAGC,EAAEN,KAAK;;;;;;;oBAOtC,EAAEW,KAAKO,SAAS,CAAClB,MAAM;sCACL,EAAEW,KAAKO,SAAS,CAAClB,MAAM;;;IAGzD,CAAC;AACH;MAEF,WAAeF"}