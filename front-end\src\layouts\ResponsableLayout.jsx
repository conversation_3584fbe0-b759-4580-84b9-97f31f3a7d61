import React from 'react';
import { Box } from '@mui/material';
import ClientSidebar from '../components/ClientSidebar';
import ClientTopBar from '../components/ClientTopBar';
import { useAuth } from '../contexts/AuthContext';
import SubscriptionExpirationAlert from '../components/responsable/abonnement/SubscriptionExpirationAlert';

const ResponsableLayout = ({ children, darkMode, toggleDarkMode }) => {
  const { currentUser } = useAuth();

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: (theme) => theme.palette.background.default
    }}>
      <ClientSidebar darkMode={darkMode} />
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <ClientTopBar
          title="Dashboard Responsable"
          darkMode={darkMode}
          toggleDarkMode={toggleDarkMode}
          user={currentUser}
        />
        <Box sx={{
          flexGrow: 1,
          p: 3,
          overflow: 'auto',
          backgroundColor: (theme) => theme.palette.background.default
        }}>
          {children}
        </Box>
      </Box>
      {/* Alerte d'expiration d'abonnement */}
      <SubscriptionExpirationAlert />
    </Box>
  );
};

export default ResponsableLayout;
