import api from './api';

const bonLivraisonService = {
  // Récupérer tous les bons de livraison
  getAllBonLivraisons: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();

      if (params.statut) queryParams.append('statut', params.statut);
      if (params.livreurId) queryParams.append('livreurId', params.livreurId);
      if (params.clientId) queryParams.append('clientId', params.clientId);
      if (params.dateDebut) queryParams.append('dateDebut', params.dateDebut);
      if (params.dateFin) queryParams.append('dateFin', params.dateFin);
      if (params.search) queryParams.append('search', params.search);

      const queryString = queryParams.toString();
      const url = queryString ? `/bon-livraisons?${queryString}` : '/bon-livraisons';

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des bons de livraison:', error);
      throw error;
    }
  },

  // Récupérer un bon de livraison par ID
  getBonLivraisonById: async (id) => {
    try {
      const response = await api.get(`/bon-livraisons/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du bon de livraison:', error);
      throw error;
    }
  },

  // Créer un nouveau bon de livraison
  createBonLivraison: async (bonLivraisonData) => {
    try {
      const response = await api.post('/bon-livraisons', bonLivraisonData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du bon de livraison:', error);
      throw error;
    }
  },

  // Mettre à jour un bon de livraison
  updateBonLivraison: async (id, bonLivraisonData) => {
    try {
      const response = await api.put(`/bon-livraisons/${id}`, bonLivraisonData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du bon de livraison:', error);
      throw error;
    }
  },

  // Supprimer un bon de livraison
  deleteBonLivraison: async (id) => {
    try {
      const response = await api.delete(`/bon-livraisons/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la suppression du bon de livraison:', error);
      throw error;
    }
  },

  // Mettre à jour le statut d'un bon de livraison
  updateStatutBonLivraison: async (id, statutData) => {
    try {
      const response = await api.put(`/bon-livraisons/${id}/statut`, statutData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      throw error;
    }
  },

  // Récupérer les statistiques des livraisons
  getStatistiquesLivraisons: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();

      if (params.dateDebut) queryParams.append('dateDebut', params.dateDebut);
      if (params.dateFin) queryParams.append('dateFin', params.dateFin);

      const queryString = queryParams.toString();
      const url = queryString ? `/bon-livraisons/statistiques?${queryString}` : '/bon-livraisons/statistiques';

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  },

  // Valider les données d'un bon de livraison
  validateBonLivraisonData: (bonLivraisonData) => {
    const errors = {};

    if (!bonLivraisonData.clientId) {
      errors.clientId = 'Le client est obligatoire';
    }

    if (!bonLivraisonData.livreurId) {
      errors.livreurId = 'Le livreur est obligatoire';
    }

    if (!bonLivraisonData.dateLivraison) {
      errors.dateLivraison = 'La date de livraison est obligatoire';
    }

    if (!bonLivraisonData.lignes || bonLivraisonData.lignes.length === 0) {
      errors.lignes = 'Au moins une ligne de produit est obligatoire';
    } else {
      // Valider chaque ligne
      bonLivraisonData.lignes.forEach((ligne, index) => {
        if (!ligne.produitId) {
          errors[`ligne_${index}_produit`] = `Le produit est obligatoire pour la ligne ${index + 1}`;
        }
        if (!ligne.quantiteLivree || ligne.quantiteLivree <= 0) {
          errors[`ligne_${index}_quantite`] = `La quantité livrée doit être supérieure à 0 pour la ligne ${index + 1}`;
        }
        if (!ligne.quantiteCommandee || ligne.quantiteCommandee <= 0) {
          errors[`ligne_${index}_commandee`] = `La quantité commandée doit être supérieure à 0 pour la ligne ${index + 1}`;
        }
      });
    }

    if (!bonLivraisonData.adresseLivraison?.adresse) {
      errors.adresseLivraison = 'L\'adresse de livraison est obligatoire';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  // Formater les données d'un bon de livraison pour l'affichage
  formatBonLivraisonForDisplay: (bonLivraison) => {
    return {
      ...bonLivraison,
      statutLabel: bonLivraisonService.getStatutLabel(bonLivraison.statut),
      dateLivraisonFormatee: new Date(bonLivraison.dateLivraison).toLocaleDateString('fr-FR'),
      montantTotalFormate: bonLivraisonService.formatMontant(bonLivraison.montantTotal),
      pourcentageLivraison: bonLivraisonService.calculatePourcentageLivraison(bonLivraison.lignes),
      clientNom: bonLivraison.clientId?.nom || 'Client inconnu',
      livreurNom: bonLivraison.livreurId ?
        `${bonLivraison.livreurId.prenom} ${bonLivraison.livreurId.nom}` :
        'Livreur inconnu'
    };
  },

  // Calculer le pourcentage de livraison
  calculatePourcentageLivraison: (lignes) => {
    if (!lignes || lignes.length === 0) return 0;

    const totalCommande = lignes.reduce((sum, ligne) => sum + (ligne.quantiteCommandee || 0), 0);
    const totalLivre = lignes.reduce((sum, ligne) => sum + (ligne.quantiteLivree || 0), 0);

    return totalCommande > 0 ? Math.round((totalLivre / totalCommande) * 100) : 0;
  },

  // Formater un montant
  formatMontant: (montant) => {
    if (!montant) return '0,00 DT';
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 2
    }).format(montant).replace('TND', 'DT');
  },

  // Obtenir le libellé du statut
  getStatutLabel: (statut) => {
    const statutLabels = {
      'EN_PREPARATION': 'En préparation',
      'EN_COURS': 'En cours',
      'LIVREE': 'Livrée',
      'PARTIELLEMENT_LIVREE': 'Partiellement livrée',
      'ECHEC': 'Échec',
      'ANNULEE': 'Annulée',
      'RETOURNEE': 'Retournée'
    };
    return statutLabels[statut] || statut;
  },

  // Obtenir la couleur du statut pour l'affichage
  getStatutColor: (statut) => {
    const statutColors = {
      'EN_PREPARATION': 'info',
      'EN_COURS': 'warning',
      'LIVREE': 'success',
      'PARTIELLEMENT_LIVREE': 'warning',
      'ECHEC': 'error',
      'ANNULEE': 'default',
      'RETOURNEE': 'secondary'
    };
    return statutColors[statut] || 'default';
  },

  // Obtenir les options de statut
  getStatutOptions: () => {
    return [
      { value: 'EN_PREPARATION', label: 'En préparation' },
      { value: 'EN_COURS', label: 'En cours' },
      { value: 'LIVREE', label: 'Livrée' },
      { value: 'PARTIELLEMENT_LIVREE', label: 'Partiellement livrée' },
      { value: 'ECHEC', label: 'Échec' },
      { value: 'ANNULEE', label: 'Annulée' },
      { value: 'RETOURNEE', label: 'Retournée' }
    ];
  },

  // Vérifier si un bon de livraison peut être modifié
  canEdit: (bonLivraison) => {
    return ['EN_PREPARATION', 'EN_COURS'].includes(bonLivraison.statut);
  },

  // Vérifier si un bon de livraison peut être supprimé
  canDelete: (bonLivraison) => {
    return ['EN_PREPARATION', 'ANNULEE'].includes(bonLivraison.statut);
  },

  // Créer une ligne de produit vide
  createEmptyLigne: () => {
    return {
      produitId: '',
      nomProduit: '',
      description: '',
      quantiteLivree: 0,
      quantiteCommandee: 0,
      unite: 'unité',
      prixUnitaire: 0,
      montantLigne: 0,
      notes: ''
    };
  },

  // Calculer le montant d'une ligne
  calculateMontantLigne: (ligne) => {
    return (ligne.quantiteLivree || 0) * (ligne.prixUnitaire || 0);
  },

  // Calculer le montant total d'un bon de livraison
  calculateMontantTotal: (lignes, tauxTVA = 19) => {
    const montantHT = lignes.reduce((total, ligne) => {
      return total + bonLivraisonService.calculateMontantLigne(ligne);
    }, 0);

    const montantTTC = montantHT * (1 + tauxTVA / 100);

    return {
      montantHT,
      montantTTC,
      montantTotal: montantTTC
    };
  },

  // Generate PDF for a bon de livraison
  generatePdf: async (id) => {
    try {
      const response = await api.get(`/bon-livraisons/${id}/pdf`, {
        responseType: 'blob'
      });
      console.log("Response from generatePdf:", response);

      // Create blob and download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `bon-livraison-${id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la génération du PDF pour le bon de livraison ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de la génération du PDF: ${error.message}`);

      throw new Error(`Erreur lors de la génération du PDF: ${error.message}`);
    }
  },

  // Print a bon de livraison
  print: async (id) => {
    try {
      const response = await api.get(`/bon-livraisons/${id}/print`, {
        responseType: 'blob'
      });
      console.log("Response from print:", response);

      // Create blob and open in new window for printing
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const printWindow = window.open(url, '_blank');

      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };
      } else {
        // Fallback if popup is blocked
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      // Clean up
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
      }, 1000);

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'impression du bon de livraison ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de l'impression: ${error.message}`);

      throw new Error(`Erreur lors de l'impression: ${error.message}`);
    }
  },

  // Send bon de livraison by email
  sendEmail: async (id, emailData = {}) => {
    try {
      const response = await api.post(`/bon-livraisons/${id}/email`, emailData);
      console.log("Response from sendEmail:", response.data);

      // Afficher une alerte de succès
      alert("Email envoyé avec succès !");

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'envoi de l'email pour le bon de livraison ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de l'envoi de l'email: ${error.message}`);

      throw new Error(`Erreur lors de l'envoi de l'email: ${error.message}`);
    }
  }
};

// Ajouter la fonction de validation
bonLivraisonService.validateBonLivraisonData = (data) => {
  const errors = {};

  // Validation des champs obligatoires
  if (!data.livreurId) {
    errors.livreurId = 'Le livreur est obligatoire';
  }

  if (!data.factureId) {
    errors.factureId = 'La facture est obligatoire';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

export default bonLivraisonService;
