const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { verifyToken } = require('../middleware/authMiddleware');
const Facture = require('../models/FactureModel');
const Devis = require('../models/DevisModel');
const Client = require('../models/ClientModel');
const Produit = require('../models/ProduitModel');
const User = require('../models/UserModel');

// Helper function to get date range based on period
const getDateRange = (period = 'monthly', date = new Date()) => {
  const startDate = new Date(date);
  const endDate = new Date(date);

  switch (period) {
    case 'daily':
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'weekly':
      const day = startDate.getDay();
      startDate.setDate(startDate.getDate() - day + (day === 0 ? -6 : 1)); // Start from Monday
      startDate.setHours(0, 0, 0, 0);
      endDate.setDate(startDate.getDate() + 6); // End on Sunday
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'monthly':
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'quarterly':
      const quarter = Math.floor(startDate.getMonth() / 3);
      startDate.setMonth(quarter * 3);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(quarter * 3 + 3);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'yearly':
      startDate.setMonth(0);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(12);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'all-time':
      // For all-time, set a very wide range
      startDate.setFullYear(2000, 0, 1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setFullYear(2100, 11, 31);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'thisYear':
      startDate.setMonth(0);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(11);
      endDate.setDate(31);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'last3Months':
      startDate.setMonth(startDate.getMonth() - 3);
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'last12Months':
      startDate.setMonth(startDate.getMonth() - 12);
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'thisMonth':
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'thisWeek':
      const currentDay = startDate.getDay();
      startDate.setDate(startDate.getDate() - currentDay + (currentDay === 0 ? -6 : 1)); // Start from Monday
      startDate.setHours(0, 0, 0, 0);
      endDate.setDate(startDate.getDate() + 6); // End on Sunday
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'today':
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'custom':
      // For custom, the date range should be provided directly
      console.log('Custom date range:', startDate, endDate);
      break;
    default:
      // Default to monthly if invalid period is provided
      console.warn(`Invalid period: ${period}, defaulting to monthly`);
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
  }

  return { startDate, endDate };
};
