const express = require('express');
const router = express.Router();
const Entreprise = require('../models/EntrepriseModel');
const multer = require('multer');
const path = require('path');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({ storage });

// GET /company-profile: Fetch company profile (including preferences and theme)
router.get('/company-profile', async (req, res) => {
  try {
    const company = await Entreprise.find(); // Fetch all company profiles
    if (!company || company.length === 0) {
      return res.status(404).json({ message: "Aucun profil d'entreprise trouvé" });
    }
    res.status(200).json(company[0]); // Return the first company profile
  } catch (error) {
    console.error('Error fetching company profile:', error);
    res.status(500).json({ error: 'Error fetching company profile' });
  }
});

// POST /company-profile: Create or update company profile (including preferences and theme)
router.post('/company-profile', upload.fields([{ name: 'logo' }, { name: 'signature' }]), async (req, res) => {
  try {
    const data = req.body;
    const logoFile = req.files?.logo ? `/uploads/${req.files.logo[0].filename}` : data.logo;
    const signatureFile = req.files?.signature ? `/uploads/${req.files.signature[0].filename}` : data.signature;

    // Prepare the company data including preferences and theme
    const companyData = {
      nom: data.nom || '',
      raisonSociale: data.raisonSociale || '',
      numéroFiscal: data.numéroFiscal || '',
      numéroTVA: data.numéroTVA || '',
      formeJuridique: data.formeJuridique || '',
      capitalSocial: data.capitalSocial || '',
      adresse: data.adresse || '',
      codePostal: data.codePostal || '',
      ville: data.ville || '',
      pays: data.pays || 'France',
      telephone: data.telephone || '',
      email: data.email || '',
      siteWeb: data.siteWeb || '',
      nomContact: data.nomContact || '',
      emailContact: data.emailContact || '',
      telephoneContact: data.telephoneContact || '',
      prefixeFacture: data.prefixeFacture || 'FACT-',
      prefixeDevis: data.prefixeDevis || 'DEV-',
      prochainNumeroFacture: data.prochainNumeroFacture || 1,
      prochainNumeroDevis: data.prochainNumeroDevis || 1,
      tauxTVA: data.tauxTVA || 20,
      delaiPaiement: data.delaiPaiement || 30,
      mentionsLegales: data.mentionsLegales || '',
      piedPage: data.piedPage || 'Merci pour votre confiance',
      nomBanque: data.nomBanque || '',
      titulaireCompte: data.titulaireCompte || '',
      iban: data.iban || '',
      bicSwift: data.bicSwift || '',
      couleurPrincipale: data.couleurPrincipale || '#1976D2',
      couleurSecondaire: data.couleurSecondaire || '#F5F5F5',
      logo: logoFile || '',
      signature: signatureFile || '',
      signatureElectronique: data.signatureElectronique === 'true',
      paiementEnLigne: data.paiementEnLigne === 'true',
      notifications: data.notifications === 'true', // Handle notifications preference
      rappelsAutomatiques: data.rappelsAutomatiques === 'true', // Handle payment reminders
      theme: data.theme || 'light', // Handle theme preference
      numerotationChronologique: data.numerotationChronologique === 'true',
    };

    // Check if a company profile already exists
    const existingCompany = await Entreprise.findOne();
    let updatedCompany;

    if (existingCompany) {
      // Update existing company profile
      updatedCompany = await Entreprise.findOneAndUpdate({}, companyData, { new: true });
    } else {
      // Create a new company profile
      updatedCompany = new Entreprise(companyData);
      await updatedCompany.save();
    }

    res.status(200).json(updatedCompany);
  } catch (error) {
    console.error('Error updating company profile:', error);
    res.status(500).json({ error: 'Error updating company profile' });
  }
});

// POST /upload/logo: Upload company logo
router.post('/upload/logo', upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No logo file uploaded' });
    }
    const logoUrl = `/uploads/${req.file.filename}`;
    res.status(200).json({ url: logoUrl });
  } catch (error) {
    console.error('Error uploading logo:', error);
    res.status(500).json({ error: 'Error uploading logo' });
  }
});

// POST /upload/signature: Upload company signature
router.post('/upload/signature', upload.single('signature'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No signature file uploaded' });
    }
    const signatureUrl = `/uploads/${req.file.filename}`;
    res.status(200).json({ url: signatureUrl });
  } catch (error) {
    console.error('Error uploading signature:', error);
    res.status(500).json({ error: 'Error uploading signature' });
  }
});

// GET /profile: Simple profile for responsable entreprise
router.get('/profile', async (req, res) => {
  try {
    const company = await Entreprise.findOne();
    if (!company) {
      return res.status(200).json({
        nom: "",
        adresse: "",
        telephone: "",
        numeroFiscal: "",
      });
    }

    // Return only the simple fields
    res.status(200).json({
      nom: company.nom || "",
      adresse: company.adresse || "",
      telephone: company.telephone || "",
      numeroFiscal: company.numéroFiscal || company.numeroFiscal || "",
    });
  } catch (error) {
    console.error('Error fetching simple profile:', error);
    res.status(500).json({ error: 'Error fetching profile' });
  }
});

// PUT /profile: Simple profile update for responsable entreprise
router.put('/profile', async (req, res) => {
  try {
    const { nom, adresse, telephone, numeroFiscal } = req.body;

    // Check if a company profile already exists
    const existingCompany = await Entreprise.findOne();
    let updatedCompany;

    const simpleData = {
      nom: nom || '',
      adresse: adresse || '',
      telephone: telephone || '',
      numéroFiscal: numeroFiscal || '',
      numeroFiscal: numeroFiscal || '', // Support both field names
    };

    if (existingCompany) {
      // Update existing company profile with simple fields
      updatedCompany = await Entreprise.findOneAndUpdate({}, simpleData, { new: true });
    } else {
      // Create a new company profile with simple fields
      updatedCompany = new Entreprise(simpleData);
      await updatedCompany.save();
    }

    res.status(200).json({
      nom: updatedCompany.nom || "",
      adresse: updatedCompany.adresse || "",
      telephone: updatedCompany.telephone || "",
      numeroFiscal: updatedCompany.numéroFiscal || updatedCompany.numeroFiscal || "",
    });
  } catch (error) {
    console.error('Error updating simple profile:', error);
    res.status(500).json({ error: 'Error updating profile' });
  }
});

module.exports = router;