import React, { createContext, useState, useContext, useEffect } from "react";
import authService from "../services/authService";

// Create the context
const AuthContext = createContext(null);

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Initialize authentication
    const initAuth = async () => {
      try {
        const user = authService.getCurrentUser();
        const token = localStorage.getItem("token");

        if (user && token) {
          const isValid = await authService.verifyToken();
          if (isValid) {
            setCurrentUser(user);
            setIsAuthenticated(true);
          } else {
            // Clear stale data and reset state
            authService.logout();
            setCurrentUser(null);
            setIsAuthenticated(false);
          }
        } else {
          // No user or token, ensure unauthenticated state
          authService.logout();
          setCurrentUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("Erreur lors de l'initialisation de l'authentification:", error);
        authService.logout();
        setCurrentUser(null);
        setIsAuthenticated(false);
        setError("Échec de la vérification de l'authentification");
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Login function - sans paramètre de rôle (détection automatique)
  const login = async (email, password) => {
    try {
      setError(null);
      const data = await authService.login(email, password);
      setCurrentUser(data.user);
      setIsAuthenticated(true);
      return data;
    } catch (err) {
      setError(err.error || "Erreur de connexion");
      throw err;
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setError(null);
      const data = await authService.register(userData);
      return data;
    } catch (err) {
      setError(err.error || "Erreur d'inscription");
      throw err;
    }
  };

  // Logout function
  const logout = () => {
    authService.logout();
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  // Refresh user profile
  const refreshUserProfile = async () => {
    try {
      if (isAuthenticated) {
        const userData = await authService.fetchUserProfile();
        setCurrentUser(userData);
      }
    } catch (err) {
      console.error("Erreur lors de la récupération du profil:", err);
      if (err.status === 401) {
        logout();
      }
    }
  };

  // Context value
  const value = {
    currentUser,
    setCurrentUser,
    isAuthenticated,
    setIsAuthenticated,
    loading,
    error,
    login,
    register,
    logout,
    refreshUserProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook for using auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth doit être utilisé à l'intérieur d'un AuthProvider");
  }
  return context;
};

export default AuthContext;