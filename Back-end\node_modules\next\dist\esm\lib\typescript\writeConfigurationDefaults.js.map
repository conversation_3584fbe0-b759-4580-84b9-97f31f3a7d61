{"version": 3, "sources": ["../../../src/lib/typescript/writeConfigurationDefaults.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport { bold, cyan, white } from '../picocolors'\nimport * as Comment<PERSON>son from 'next/dist/compiled/comment-json'\nimport semver from 'next/dist/compiled/semver'\nimport os from 'os'\nimport type { CompilerOptions } from 'typescript'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\nimport * as Log from '../../build/output/log'\n\ntype DesiredCompilerOptionsShape = {\n  [K in keyof CompilerOptions]:\n    | { suggested: any; reason?: string }\n    | {\n        parsedValue?: any\n        parsedValues?: Array<any>\n        value: any\n        reason: string\n      }\n}\n\nfunction getDesiredCompilerOptions(\n  ts: typeof import('typescript'),\n  tsOptions?: CompilerOptions\n): DesiredCompilerOptionsShape {\n  const o: DesiredCompilerOptionsShape = {\n    target: {\n      suggested: 'ES2017',\n      reason:\n        'For top-level `await`. Note: Next.js only polyfills for the esmodules target.',\n    },\n    // These are suggested values and will be set when not present in the\n    // tsconfig.json\n    lib: { suggested: ['dom', 'dom.iterable', 'esnext'] },\n    allowJs: { suggested: true },\n    skipLibCheck: { suggested: true },\n    strict: { suggested: false },\n    ...(semver.lt(ts.version, '5.0.0')\n      ? { forceConsistentCasingInFileNames: { suggested: true } }\n      : undefined),\n    noEmit: { suggested: true },\n    ...(semver.gte(ts.version, '4.4.2')\n      ? { incremental: { suggested: true } }\n      : undefined),\n\n    // These values are required and cannot be changed by the user\n    // Keep this in sync with the webpack config\n    // 'parsedValue' matches the output value from ts.parseJsonConfigFileContent()\n    module: {\n      parsedValue: ts.ModuleKind.ESNext,\n      // All of these values work:\n      parsedValues: [\n        semver.gte(ts.version, '5.4.0') && (ts.ModuleKind as any).Preserve,\n        ts.ModuleKind.ES2020,\n        ts.ModuleKind.ESNext,\n        ts.ModuleKind.CommonJS,\n        ts.ModuleKind.AMD,\n        ts.ModuleKind.NodeNext,\n        ts.ModuleKind.Node16,\n      ],\n      value: 'esnext',\n      reason: 'for dynamic import() support',\n    },\n    // TODO: Semver check not needed once Next.js repo uses 5.4.\n    ...(semver.gte(ts.version, '5.4.0') &&\n    tsOptions?.module === (ts.ModuleKind as any).Preserve\n      ? {\n          // TypeScript 5.4 introduced `Preserve`. Using `Preserve` implies\n          // - `moduleResolution` is `Bundler`\n          // - `esModuleInterop` is `true`\n          // - `resolveJsonModule` is `true`\n          // This means that if the user is using Preserve, they don't need these options\n        }\n      : {\n          esModuleInterop: {\n            value: true,\n            reason: 'requirement for SWC / babel',\n          },\n          moduleResolution: {\n            // In TypeScript 5.0, `NodeJs` has renamed to `Node10`\n            parsedValue:\n              ts.ModuleResolutionKind.Bundler ??\n              ts.ModuleResolutionKind.NodeNext ??\n              (ts.ModuleResolutionKind as any).Node10 ??\n              ts.ModuleResolutionKind.NodeJs,\n            // All of these values work:\n            parsedValues: [\n              (ts.ModuleResolutionKind as any).Node10 ??\n                ts.ModuleResolutionKind.NodeJs,\n              // only newer TypeScript versions have this field, it\n              // will be filtered for new versions of TypeScript\n              (ts.ModuleResolutionKind as any).Node12,\n              ts.ModuleResolutionKind.Node16,\n              ts.ModuleResolutionKind.NodeNext,\n              ts.ModuleResolutionKind.Bundler,\n            ].filter((val) => typeof val !== 'undefined'),\n            value: 'node',\n            reason: 'to match webpack resolution',\n          },\n          resolveJsonModule: {\n            value: true,\n            reason: 'to match webpack resolution',\n          },\n        }),\n    ...(tsOptions?.verbatimModuleSyntax === true\n      ? undefined\n      : {\n          isolatedModules: {\n            value: true,\n            reason: 'requirement for SWC / Babel',\n          },\n        }),\n    jsx: {\n      parsedValue: ts.JsxEmit.Preserve,\n      value: 'preserve',\n      reason: 'next.js implements its own optimized jsx transform',\n    },\n  }\n\n  return o\n}\n\nexport function getRequiredConfiguration(\n  ts: typeof import('typescript')\n): Partial<import('typescript').CompilerOptions> {\n  const res: Partial<import('typescript').CompilerOptions> = {}\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts)\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const ev = desiredCompilerOptions[optionKey]\n    if (!('value' in ev)) {\n      continue\n    }\n    res[optionKey] = ev.parsedValue ?? ev.value\n  }\n\n  return res\n}\n\nconst localDevTestFilesExcludeAction =\n  'NEXT_PRIVATE_LOCAL_DEV_TEST_FILES_EXCLUDE'\n\nexport async function writeConfigurationDefaults(\n  ts: typeof import('typescript'),\n  tsConfigPath: string,\n  isFirstTimeSetup: boolean,\n  hasAppDir: boolean,\n  distDir: string,\n  hasPagesDir: boolean\n): Promise<void> {\n  if (isFirstTimeSetup) {\n    await fs.writeFile(tsConfigPath, '{}' + os.EOL)\n  }\n\n  const { options: tsOptions, raw: rawConfig } =\n    await getTypeScriptConfiguration(ts, tsConfigPath, true)\n\n  const userTsConfigContent = await fs.readFile(tsConfigPath, {\n    encoding: 'utf8',\n  })\n  const userTsConfig = CommentJson.parse(userTsConfigContent)\n  if (userTsConfig.compilerOptions == null && !('extends' in rawConfig)) {\n    userTsConfig.compilerOptions = {}\n    isFirstTimeSetup = true\n  }\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts, tsOptions)\n\n  const suggestedActions: string[] = []\n  const requiredActions: string[] = []\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const check = desiredCompilerOptions[optionKey]\n    if ('suggested' in check) {\n      if (!(optionKey in tsOptions)) {\n        if (!userTsConfig.compilerOptions) {\n          userTsConfig.compilerOptions = {}\n        }\n        userTsConfig.compilerOptions[optionKey] = check.suggested\n        suggestedActions.push(\n          cyan(optionKey) +\n            ' was set to ' +\n            bold(check.suggested) +\n            (check.reason ? ` (${check.reason})` : '')\n        )\n      }\n    } else if ('value' in check) {\n      const ev = tsOptions[optionKey]\n      if (\n        !('parsedValues' in check\n          ? check.parsedValues?.includes(ev)\n          : 'parsedValue' in check\n            ? check.parsedValue === ev\n            : check.value === ev)\n      ) {\n        if (!userTsConfig.compilerOptions) {\n          userTsConfig.compilerOptions = {}\n        }\n        userTsConfig.compilerOptions[optionKey] = check.value\n        requiredActions.push(\n          cyan(optionKey) +\n            ' was set to ' +\n            bold(check.value) +\n            ` (${check.reason})`\n        )\n      }\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = check\n    }\n  }\n\n  const nextAppTypes = `${distDir}/types/**/*.ts`\n\n  if (!('include' in rawConfig)) {\n    userTsConfig.include = hasAppDir\n      ? ['next-env.d.ts', nextAppTypes, '**/*.ts', '**/*.tsx']\n      : ['next-env.d.ts', '**/*.ts', '**/*.tsx']\n    suggestedActions.push(\n      cyan('include') +\n        ' was set to ' +\n        bold(\n          hasAppDir\n            ? `['next-env.d.ts', '${nextAppTypes}', '**/*.ts', '**/*.tsx']`\n            : `['next-env.d.ts', '**/*.ts', '**/*.tsx']`\n        )\n    )\n  } else if (hasAppDir && !rawConfig.include.includes(nextAppTypes)) {\n    if (!Array.isArray(userTsConfig.include)) {\n      userTsConfig.include = []\n    }\n    // rawConfig will resolve all extends and include paths (ex: tsconfig.json, tsconfig.base.json, etc.)\n    // if it doesn't match userTsConfig then update the userTsConfig to add the\n    // rawConfig's includes in addition to nextAppTypes\n    if (\n      rawConfig.include.length !== userTsConfig.include.length ||\n      JSON.stringify(rawConfig.include.sort()) !==\n        JSON.stringify(userTsConfig.include.sort())\n    ) {\n      userTsConfig.include.push(...rawConfig.include, nextAppTypes)\n      suggestedActions.push(\n        cyan('include') +\n          ' was set to ' +\n          bold(\n            `[${[...rawConfig.include, nextAppTypes]\n              .map((i) => `'${i}'`)\n              .join(', ')}]`\n          )\n      )\n    } else {\n      userTsConfig.include.push(nextAppTypes)\n      suggestedActions.push(\n        cyan('include') + ' was updated to add ' + bold(`'${nextAppTypes}'`)\n      )\n    }\n  }\n\n  // Enable the Next.js typescript plugin.\n  if (hasAppDir) {\n    // Check if the config or the resolved config has the plugin already.\n    const plugins = [\n      ...(Array.isArray(tsOptions.plugins) ? tsOptions.plugins : []),\n      ...(userTsConfig.compilerOptions &&\n      Array.isArray(userTsConfig.compilerOptions.plugins)\n        ? userTsConfig.compilerOptions.plugins\n        : []),\n    ]\n    const hasNextPlugin = plugins.some(\n      ({ name }: { name: string }) => name === 'next'\n    )\n\n    // If the TS config extends on another config, we can't add the `plugin` field\n    // because that will override the parent config's plugins.\n    // Instead we have to show a message to the user to add the plugin manually.\n    if (\n      !userTsConfig.compilerOptions ||\n      (plugins.length &&\n        !hasNextPlugin &&\n        'extends' in rawConfig &&\n        (!rawConfig.compilerOptions || !rawConfig.compilerOptions.plugins))\n    ) {\n      Log.info(\n        `\\nYour ${bold(\n          'tsconfig.json'\n        )} extends another configuration, which means we cannot add the Next.js TypeScript plugin automatically. To improve your development experience, we recommend adding the Next.js plugin (\\`${cyan(\n          '\"plugins\": [{ \"name\": \"next\" }]'\n        )}\\`) manually to your TypeScript configuration. Learn more: https://nextjs.org/docs/app/api-reference/config/typescript#the-typescript-plugin\\n`\n      )\n    } else if (!hasNextPlugin) {\n      if (!('plugins' in userTsConfig.compilerOptions)) {\n        userTsConfig.compilerOptions.plugins = []\n      }\n      userTsConfig.compilerOptions.plugins.push({ name: 'next' })\n      suggestedActions.push(\n        cyan('plugins') + ' was updated to add ' + bold(`{ name: 'next' }`)\n      )\n    }\n\n    // If `strict` is set to `false` and `strictNullChecks` is set to `false`,\n    // then set `strictNullChecks` to `true`.\n    if (\n      hasPagesDir &&\n      hasAppDir &&\n      !tsOptions.strict &&\n      !('strictNullChecks' in tsOptions)\n    ) {\n      userTsConfig.compilerOptions.strictNullChecks = true\n      suggestedActions.push(\n        cyan('strictNullChecks') + ' was set to ' + bold(`true`)\n      )\n    }\n  }\n\n  if (!('exclude' in rawConfig)) {\n    userTsConfig.exclude = ['node_modules']\n    suggestedActions.push(\n      cyan('exclude') + ' was set to ' + bold(`['node_modules']`)\n    )\n  }\n\n  // During local development inside Next.js repo, exclude the test files coverage by the local tsconfig\n  if (process.env.NEXT_PRIVATE_LOCAL_DEV && userTsConfig.exclude) {\n    const tsGlob = '**/*.test.ts'\n    const tsxGlob = '**/*.test.tsx'\n    let hasUpdates = false\n    if (!userTsConfig.exclude.includes(tsGlob)) {\n      userTsConfig.exclude.push(tsGlob)\n      hasUpdates = true\n    }\n    if (!userTsConfig.exclude.includes(tsxGlob)) {\n      userTsConfig.exclude.push(tsxGlob)\n      hasUpdates = true\n    }\n\n    if (hasUpdates) {\n      requiredActions.push(localDevTestFilesExcludeAction)\n    }\n  }\n\n  if (suggestedActions.length < 1 && requiredActions.length < 1) {\n    return\n  }\n\n  await fs.writeFile(\n    tsConfigPath,\n    CommentJson.stringify(userTsConfig, null, 2) + os.EOL\n  )\n\n  Log.info('')\n  if (isFirstTimeSetup) {\n    Log.info(\n      `We detected TypeScript in your project and created a ${cyan(\n        'tsconfig.json'\n      )} file for you.`\n    )\n    return\n  }\n\n  Log.info(\n    `We detected TypeScript in your project and reconfigured your ${cyan(\n      'tsconfig.json'\n    )} file for you.${\n      userTsConfig.compilerOptions?.strict\n        ? ''\n        : ` Strict-mode is set to ${cyan('false')} by default.`\n    }`\n  )\n\n  if (suggestedActions.length) {\n    Log.info(\n      `The following suggested values were added to your ${cyan(\n        'tsconfig.json'\n      )}. These values ${cyan('can be changed')} to fit your project's needs:\\n`\n    )\n\n    suggestedActions.forEach((action) => Log.info(`\\t- ${action}`))\n\n    Log.info('')\n  }\n\n  const requiredActionsToBeLogged = process.env.NEXT_PRIVATE_LOCAL_DEV\n    ? requiredActions.filter(\n        (action) => action !== localDevTestFilesExcludeAction\n      )\n    : requiredActions\n\n  if (requiredActionsToBeLogged.length) {\n    Log.info(\n      `The following ${white('mandatory changes')} were made to your ${cyan(\n        'tsconfig.json'\n      )}:\\n`\n    )\n\n    requiredActionsToBeLogged.forEach((action) => Log.info(`\\t- ${action}`))\n\n    Log.info('')\n  }\n}\n"], "names": ["promises", "fs", "bold", "cyan", "white", "CommentJson", "semver", "os", "getTypeScriptConfiguration", "Log", "getDesiredCompilerOptions", "ts", "tsOptions", "o", "target", "suggested", "reason", "lib", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "lt", "version", "forceConsistentCasingInFileNames", "undefined", "noEmit", "gte", "incremental", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "Preserve", "ES2020", "CommonJS", "AMD", "NodeNext", "Node16", "value", "esModuleInterop", "moduleResolution", "ModuleResolutionKind", "<PERSON><PERSON><PERSON>", "Node10", "NodeJs", "Node12", "filter", "val", "resolveJsonModule", "verbatimModuleSyntax", "isolatedModules", "jsx", "JsxEmit", "getRequiredConfiguration", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "localDevTestFilesExcludeAction", "writeConfigurationDefaults", "tsConfigPath", "isFirstTimeSetup", "hasAppDir", "distDir", "hasPagesDir", "userTsConfig", "writeFile", "EOL", "options", "raw", "rawConfig", "userTsConfigContent", "readFile", "encoding", "parse", "compilerOptions", "suggestedActions", "requiredActions", "check", "push", "includes", "_", "nextAppTypes", "include", "Array", "isArray", "length", "JSON", "stringify", "sort", "map", "i", "join", "plugins", "hasNextPlugin", "some", "name", "info", "strict<PERSON>ull<PERSON>hecks", "exclude", "process", "env", "NEXT_PRIVATE_LOCAL_DEV", "tsGlob", "tsxGlob", "hasUpdates", "for<PERSON>ach", "action", "requiredActionsToBeLogged"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,gBAAe;AACjD,YAAYC,iBAAiB,kCAAiC;AAC9D,OAAOC,YAAY,4BAA2B;AAC9C,OAAOC,QAAQ,KAAI;AAEnB,SAASC,0BAA0B,QAAQ,+BAA8B;AACzE,YAAYC,SAAS,yBAAwB;AAa7C,SAASC,0BACPC,EAA+B,EAC/BC,SAA2B;IAE3B,MAAMC,IAAiC;QACrCC,QAAQ;YACNC,WAAW;YACXC,QACE;QACJ;QACA,qEAAqE;QACrE,gBAAgB;QAChBC,KAAK;YAAEF,WAAW;gBAAC;gBAAO;gBAAgB;aAAS;QAAC;QACpDG,SAAS;YAAEH,WAAW;QAAK;QAC3BI,cAAc;YAAEJ,WAAW;QAAK;QAChCK,QAAQ;YAAEL,WAAW;QAAM;QAC3B,GAAIT,OAAOe,EAAE,CAACV,GAAGW,OAAO,EAAE,WACtB;YAAEC,kCAAkC;gBAAER,WAAW;YAAK;QAAE,IACxDS,SAAS;QACbC,QAAQ;YAAEV,WAAW;QAAK;QAC1B,GAAIT,OAAOoB,GAAG,CAACf,GAAGW,OAAO,EAAE,WACvB;YAAEK,aAAa;gBAAEZ,WAAW;YAAK;QAAE,IACnCS,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EI,QAAQ;YACNC,aAAalB,GAAGmB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,cAAc;gBACZ1B,OAAOoB,GAAG,CAACf,GAAGW,OAAO,EAAE,YAAY,AAACX,GAAGmB,UAAU,CAASG,QAAQ;gBAClEtB,GAAGmB,UAAU,CAACI,MAAM;gBACpBvB,GAAGmB,UAAU,CAACC,MAAM;gBACpBpB,GAAGmB,UAAU,CAACK,QAAQ;gBACtBxB,GAAGmB,UAAU,CAACM,GAAG;gBACjBzB,GAAGmB,UAAU,CAACO,QAAQ;gBACtB1B,GAAGmB,UAAU,CAACQ,MAAM;aACrB;YACDC,OAAO;YACPvB,QAAQ;QACV;QACA,4DAA4D;QAC5D,GAAIV,OAAOoB,GAAG,CAACf,GAAGW,OAAO,EAAE,YAC3BV,CAAAA,6BAAAA,UAAWgB,MAAM,MAAK,AAACjB,GAAGmB,UAAU,CAASG,QAAQ,GACjD;QAMA,IACA;YACEO,iBAAiB;gBACfD,OAAO;gBACPvB,QAAQ;YACV;YACAyB,kBAAkB;gBAChB,sDAAsD;gBACtDZ,aACElB,GAAG+B,oBAAoB,CAACC,OAAO,IAC/BhC,GAAG+B,oBAAoB,CAACL,QAAQ,IAChC,AAAC1B,GAAG+B,oBAAoB,CAASE,MAAM,IACvCjC,GAAG+B,oBAAoB,CAACG,MAAM;gBAChC,4BAA4B;gBAC5Bb,cAAc;oBACXrB,GAAG+B,oBAAoB,CAASE,MAAM,IACrCjC,GAAG+B,oBAAoB,CAACG,MAAM;oBAChC,qDAAqD;oBACrD,kDAAkD;oBACjDlC,GAAG+B,oBAAoB,CAASI,MAAM;oBACvCnC,GAAG+B,oBAAoB,CAACJ,MAAM;oBAC9B3B,GAAG+B,oBAAoB,CAACL,QAAQ;oBAChC1B,GAAG+B,oBAAoB,CAACC,OAAO;iBAChC,CAACI,MAAM,CAAC,CAACC,MAAQ,OAAOA,QAAQ;gBACjCT,OAAO;gBACPvB,QAAQ;YACV;YACAiC,mBAAmB;gBACjBV,OAAO;gBACPvB,QAAQ;YACV;QACF,CAAC;QACL,GAAIJ,CAAAA,6BAAAA,UAAWsC,oBAAoB,MAAK,OACpC1B,YACA;YACE2B,iBAAiB;gBACfZ,OAAO;gBACPvB,QAAQ;YACV;QACF,CAAC;QACLoC,KAAK;YACHvB,aAAalB,GAAG0C,OAAO,CAACpB,QAAQ;YAChCM,OAAO;YACPvB,QAAQ;QACV;IACF;IAEA,OAAOH;AACT;AAEA,OAAO,SAASyC,yBACd3C,EAA+B;IAE/B,MAAM4C,MAAqD,CAAC;IAE5D,MAAMC,yBAAyB9C,0BAA0BC;IACzD,KAAK,MAAM8C,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMI,KAAKJ,sBAAsB,CAACC,UAAU;QAC5C,IAAI,CAAE,CAAA,WAAWG,EAAC,GAAI;YACpB;QACF;QACAL,GAAG,CAACE,UAAU,GAAGG,GAAG/B,WAAW,IAAI+B,GAAGrB,KAAK;IAC7C;IAEA,OAAOgB;AACT;AAEA,MAAMM,iCACJ;AAEF,OAAO,eAAeC,2BACpBnD,EAA+B,EAC/BoD,YAAoB,EACpBC,gBAAyB,EACzBC,SAAkB,EAClBC,OAAe,EACfC,WAAoB;QAqNhBC;IAnNJ,IAAIJ,kBAAkB;QACpB,MAAM/D,GAAGoE,SAAS,CAACN,cAAc,OAAOxD,GAAG+D,GAAG;IAChD;IAEA,MAAM,EAAEC,SAAS3D,SAAS,EAAE4D,KAAKC,SAAS,EAAE,GAC1C,MAAMjE,2BAA2BG,IAAIoD,cAAc;IAErD,MAAMW,sBAAsB,MAAMzE,GAAG0E,QAAQ,CAACZ,cAAc;QAC1Da,UAAU;IACZ;IACA,MAAMR,eAAe/D,YAAYwE,KAAK,CAACH;IACvC,IAAIN,aAAaU,eAAe,IAAI,QAAQ,CAAE,CAAA,aAAaL,SAAQ,GAAI;QACrEL,aAAaU,eAAe,GAAG,CAAC;QAChCd,mBAAmB;IACrB;IAEA,MAAMR,yBAAyB9C,0BAA0BC,IAAIC;IAE7D,MAAMmE,mBAA6B,EAAE;IACrC,MAAMC,kBAA4B,EAAE;IACpC,KAAK,MAAMvB,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMyB,QAAQzB,sBAAsB,CAACC,UAAU;QAC/C,IAAI,eAAewB,OAAO;YACxB,IAAI,CAAExB,CAAAA,aAAa7C,SAAQ,GAAI;gBAC7B,IAAI,CAACwD,aAAaU,eAAe,EAAE;oBACjCV,aAAaU,eAAe,GAAG,CAAC;gBAClC;gBACAV,aAAaU,eAAe,CAACrB,UAAU,GAAGwB,MAAMlE,SAAS;gBACzDgE,iBAAiBG,IAAI,CACnB/E,KAAKsD,aACH,iBACAvD,KAAK+E,MAAMlE,SAAS,IACnBkE,CAAAA,MAAMjE,MAAM,GAAG,CAAC,EAAE,EAAEiE,MAAMjE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAC;YAE9C;QACF,OAAO,IAAI,WAAWiE,OAAO;gBAIrBA;YAHN,MAAMrB,KAAKhD,SAAS,CAAC6C,UAAU;YAC/B,IACE,CAAE,CAAA,kBAAkBwB,SAChBA,sBAAAA,MAAMjD,YAAY,qBAAlBiD,oBAAoBE,QAAQ,CAACvB,MAC7B,iBAAiBqB,QACfA,MAAMpD,WAAW,KAAK+B,KACtBqB,MAAM1C,KAAK,KAAKqB,EAAC,GACvB;gBACA,IAAI,CAACQ,aAAaU,eAAe,EAAE;oBACjCV,aAAaU,eAAe,GAAG,CAAC;gBAClC;gBACAV,aAAaU,eAAe,CAACrB,UAAU,GAAGwB,MAAM1C,KAAK;gBACrDyC,gBAAgBE,IAAI,CAClB/E,KAAKsD,aACH,iBACAvD,KAAK+E,MAAM1C,KAAK,IAChB,CAAC,EAAE,EAAE0C,MAAMjE,MAAM,CAAC,CAAC,CAAC;YAE1B;QACF,OAAO;YACL,6DAA6D;YAC7D,MAAMoE,IAAWH;QACnB;IACF;IAEA,MAAMI,eAAe,GAAGnB,QAAQ,cAAc,CAAC;IAE/C,IAAI,CAAE,CAAA,aAAaO,SAAQ,GAAI;QAC7BL,aAAakB,OAAO,GAAGrB,YACnB;YAAC;YAAiBoB;YAAc;YAAW;SAAW,GACtD;YAAC;YAAiB;YAAW;SAAW;QAC5CN,iBAAiBG,IAAI,CACnB/E,KAAK,aACH,iBACAD,KACE+D,YACI,CAAC,mBAAmB,EAAEoB,aAAa,yBAAyB,CAAC,GAC7D,CAAC,wCAAwC,CAAC;IAGtD,OAAO,IAAIpB,aAAa,CAACQ,UAAUa,OAAO,CAACH,QAAQ,CAACE,eAAe;QACjE,IAAI,CAACE,MAAMC,OAAO,CAACpB,aAAakB,OAAO,GAAG;YACxClB,aAAakB,OAAO,GAAG,EAAE;QAC3B;QACA,qGAAqG;QACrG,2EAA2E;QAC3E,mDAAmD;QACnD,IACEb,UAAUa,OAAO,CAACG,MAAM,KAAKrB,aAAakB,OAAO,CAACG,MAAM,IACxDC,KAAKC,SAAS,CAAClB,UAAUa,OAAO,CAACM,IAAI,QACnCF,KAAKC,SAAS,CAACvB,aAAakB,OAAO,CAACM,IAAI,KAC1C;YACAxB,aAAakB,OAAO,CAACJ,IAAI,IAAIT,UAAUa,OAAO,EAAED;YAChDN,iBAAiBG,IAAI,CACnB/E,KAAK,aACH,iBACAD,KACE,CAAC,CAAC,EAAE;mBAAIuE,UAAUa,OAAO;gBAAED;aAAa,CACrCQ,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CAAC,MAAM,CAAC,CAAC;QAGxB,OAAO;YACL3B,aAAakB,OAAO,CAACJ,IAAI,CAACG;YAC1BN,iBAAiBG,IAAI,CACnB/E,KAAK,aAAa,yBAAyBD,KAAK,CAAC,CAAC,EAAEmF,aAAa,CAAC,CAAC;QAEvE;IACF;IAEA,wCAAwC;IACxC,IAAIpB,WAAW;QACb,qEAAqE;QACrE,MAAM+B,UAAU;eACVT,MAAMC,OAAO,CAAC5E,UAAUoF,OAAO,IAAIpF,UAAUoF,OAAO,GAAG,EAAE;eACzD5B,aAAaU,eAAe,IAChCS,MAAMC,OAAO,CAACpB,aAAaU,eAAe,CAACkB,OAAO,IAC9C5B,aAAaU,eAAe,CAACkB,OAAO,GACpC,EAAE;SACP;QACD,MAAMC,gBAAgBD,QAAQE,IAAI,CAChC,CAAC,EAAEC,IAAI,EAAoB,GAAKA,SAAS;QAG3C,8EAA8E;QAC9E,0DAA0D;QAC1D,4EAA4E;QAC5E,IACE,CAAC/B,aAAaU,eAAe,IAC5BkB,QAAQP,MAAM,IACb,CAACQ,iBACD,aAAaxB,aACZ,CAAA,CAACA,UAAUK,eAAe,IAAI,CAACL,UAAUK,eAAe,CAACkB,OAAO,AAAD,GAClE;YACAvF,IAAI2F,IAAI,CACN,CAAC,OAAO,EAAElG,KACR,iBACA,yLAAyL,EAAEC,KAC3L,mCACA,8IAA8I,CAAC;QAErJ,OAAO,IAAI,CAAC8F,eAAe;YACzB,IAAI,CAAE,CAAA,aAAa7B,aAAaU,eAAe,AAAD,GAAI;gBAChDV,aAAaU,eAAe,CAACkB,OAAO,GAAG,EAAE;YAC3C;YACA5B,aAAaU,eAAe,CAACkB,OAAO,CAACd,IAAI,CAAC;gBAAEiB,MAAM;YAAO;YACzDpB,iBAAiBG,IAAI,CACnB/E,KAAK,aAAa,yBAAyBD,KAAK,CAAC,gBAAgB,CAAC;QAEtE;QAEA,0EAA0E;QAC1E,yCAAyC;QACzC,IACEiE,eACAF,aACA,CAACrD,UAAUQ,MAAM,IACjB,CAAE,CAAA,sBAAsBR,SAAQ,GAChC;YACAwD,aAAaU,eAAe,CAACuB,gBAAgB,GAAG;YAChDtB,iBAAiBG,IAAI,CACnB/E,KAAK,sBAAsB,iBAAiBD,KAAK,CAAC,IAAI,CAAC;QAE3D;IACF;IAEA,IAAI,CAAE,CAAA,aAAauE,SAAQ,GAAI;QAC7BL,aAAakC,OAAO,GAAG;YAAC;SAAe;QACvCvB,iBAAiBG,IAAI,CACnB/E,KAAK,aAAa,iBAAiBD,KAAK,CAAC,gBAAgB,CAAC;IAE9D;IAEA,sGAAsG;IACtG,IAAIqG,QAAQC,GAAG,CAACC,sBAAsB,IAAIrC,aAAakC,OAAO,EAAE;QAC9D,MAAMI,SAAS;QACf,MAAMC,UAAU;QAChB,IAAIC,aAAa;QACjB,IAAI,CAACxC,aAAakC,OAAO,CAACnB,QAAQ,CAACuB,SAAS;YAC1CtC,aAAakC,OAAO,CAACpB,IAAI,CAACwB;YAC1BE,aAAa;QACf;QACA,IAAI,CAACxC,aAAakC,OAAO,CAACnB,QAAQ,CAACwB,UAAU;YAC3CvC,aAAakC,OAAO,CAACpB,IAAI,CAACyB;YAC1BC,aAAa;QACf;QAEA,IAAIA,YAAY;YACd5B,gBAAgBE,IAAI,CAACrB;QACvB;IACF;IAEA,IAAIkB,iBAAiBU,MAAM,GAAG,KAAKT,gBAAgBS,MAAM,GAAG,GAAG;QAC7D;IACF;IAEA,MAAMxF,GAAGoE,SAAS,CAChBN,cACA1D,YAAYsF,SAAS,CAACvB,cAAc,MAAM,KAAK7D,GAAG+D,GAAG;IAGvD7D,IAAI2F,IAAI,CAAC;IACT,IAAIpC,kBAAkB;QACpBvD,IAAI2F,IAAI,CACN,CAAC,qDAAqD,EAAEjG,KACtD,iBACA,cAAc,CAAC;QAEnB;IACF;IAEAM,IAAI2F,IAAI,CACN,CAAC,6DAA6D,EAAEjG,KAC9D,iBACA,cAAc,EACdiE,EAAAA,gCAAAA,aAAaU,eAAe,qBAA5BV,8BAA8BhD,MAAM,IAChC,KACA,CAAC,uBAAuB,EAAEjB,KAAK,SAAS,YAAY,CAAC,EACzD;IAGJ,IAAI4E,iBAAiBU,MAAM,EAAE;QAC3BhF,IAAI2F,IAAI,CACN,CAAC,kDAAkD,EAAEjG,KACnD,iBACA,eAAe,EAAEA,KAAK,kBAAkB,+BAA+B,CAAC;QAG5E4E,iBAAiB8B,OAAO,CAAC,CAACC,SAAWrG,IAAI2F,IAAI,CAAC,CAAC,IAAI,EAAEU,QAAQ;QAE7DrG,IAAI2F,IAAI,CAAC;IACX;IAEA,MAAMW,4BAA4BR,QAAQC,GAAG,CAACC,sBAAsB,GAChEzB,gBAAgBjC,MAAM,CACpB,CAAC+D,SAAWA,WAAWjD,kCAEzBmB;IAEJ,IAAI+B,0BAA0BtB,MAAM,EAAE;QACpChF,IAAI2F,IAAI,CACN,CAAC,cAAc,EAAEhG,MAAM,qBAAqB,mBAAmB,EAAED,KAC/D,iBACA,GAAG,CAAC;QAGR4G,0BAA0BF,OAAO,CAAC,CAACC,SAAWrG,IAAI2F,IAAI,CAAC,CAAC,IAAI,EAAEU,QAAQ;QAEtErG,IAAI2F,IAAI,CAAC;IACX;AACF"}