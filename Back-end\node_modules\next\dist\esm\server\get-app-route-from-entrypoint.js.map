{"version": 3, "sources": ["../../src/server/get-app-route-from-entrypoint.ts"], "sourcesContent": ["import matchBundle from './match-bundle'\n\n// matches app/:path*.js\nconst APP_ROUTE_NAME_REGEX = /^app[/\\\\](.*)$/\n\nexport default function getAppRouteFromEntrypoint(entryFile: string) {\n  const pagePath = matchBundle(APP_ROUTE_NAME_REGEX, entryFile)\n  if (typeof pagePath === 'string' && !pagePath) {\n    return '/'\n  }\n\n  if (!pagePath) {\n    return null\n  }\n\n  return pagePath\n}\n"], "names": ["matchBundle", "APP_ROUTE_NAME_REGEX", "getAppRouteFromEntrypoint", "entryFile", "pagePath"], "mappings": "AAAA,OAAOA,iBAAiB,iBAAgB;AAExC,wBAAwB;AACxB,MAAMC,uBAAuB;AAE7B,eAAe,SAASC,0BAA0BC,SAAiB;IACjE,MAAMC,WAAWJ,YAAYC,sBAAsBE;IACnD,IAAI,OAAOC,aAAa,YAAY,CAACA,UAAU;QAC7C,OAAO;IACT;IAEA,IAAI,CAACA,UAAU;QACb,OAAO;IACT;IAEA,OAAOA;AACT"}