{"name": "@types/next-server", "version": "8.1.2", "description": "TypeScript definitions for next-server", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/dru89", "githubUsername": "dru89"}, {"name": "Brice BERNARD", "url": "https://github.com/brikou", "githubUsername": "brikou"}, {"name": "<PERSON>", "url": "https://github.com/jthegedus", "githubUsername": "j<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/resir014", "githubUsername": "resir014"}, {"name": "<PERSON>", "url": "https://github.com/scottdj92", "githubUsername": "scottdj92"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/joaovieira", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ajliv", "githubUsername": "a<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/next-server"}, "scripts": {}, "dependencies": {"@types/next": "*", "@types/node": "*", "@types/react": "*", "@types/react-loadable": "*"}, "typesPublisherContentHash": "d5e8a38470e00f7b9713c5e9ab1491276186bb7c2224c74a78692c4ed7a59905", "typeScriptVersion": "2.8"}