const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const Devis = require('../models/DevisModel');
const User = require('../models/UserModel');
const Client = require('../models/ClientModel');
const PDFDocument = require('pdfkit');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Middleware to log all requests to this router
router.use((req, res, next) => {
  console.log(`DevisRoute accessed: ${req.method} ${req.url}`);
  next();
});

// Function to generate a unique devis number based on company settings
const generateDevisNumber = async () => {
  try {
    // Récupérer les paramètres d'entreprise
    const Entreprise = require('../models/EntrepriseModel');
    const entreprise = await Entreprise.findOne();

    // Utiliser les paramètres d'entreprise ou des valeurs par défaut
    const prefix = entreprise?.prefixeDevis || "DEV-";
    const nextNum = entreprise?.prochainNumeroDevis || 1;
    const date = new Date();
    const year = date.getFullYear().toString();

    // Formater le numéro avec padding (4 chiffres)
    const formattedNum = String(nextNum).padStart(4, '0');
    const numero = `${prefix}${year}-${formattedNum}`;

    // Vérifier si ce numéro existe déjà
    const existingDevis = await Devis.findOne({ numéro: numero });
    if (existingDevis) {
      // Si le numéro existe déjà, incrémenter et réessayer
      if (entreprise) {
        entreprise.prochainNumeroDevis = nextNum + 1;
        await entreprise.save();
      }
      // Appel récursif pour générer un nouveau numéro
      return generateDevisNumber();
    }

    // Incrémenter le compteur pour le prochain devis
    if (entreprise) {
      entreprise.prochainNumeroDevis = nextNum + 1;
      await entreprise.save();
      console.log(`Prochain numéro de devis mis à jour: ${entreprise.prochainNumeroDevis}`);
    }

    return numero;
  } catch (error) {
    console.error("Erreur lors de la génération du numéro de devis:", error);
    // Fallback en cas d'erreur
    const date = new Date();
    const year = date.getFullYear().toString();
    const count = await Devis.countDocuments();
    return `DEV-${year}-${String(count + 1).padStart(4, '0')}`;
  }
};

// Route pour créer une demande de devis (client)
router.post('/demande', verifyToken, async (req, res) => {
  try {
    console.log('Creating new devis request with data:', req.body);

    // Vérifier que l'utilisateur est un client
    if (req.userRole !== 'CLIENT') {
      return res.status(403).json({
        message: 'Seuls les clients peuvent créer des demandes de devis'
      });
    }

    // Générer un numéro de devis
    const numeroDevis = await generateDevisNumber();

    // Créer la demande de devis
    const devis = new Devis({
      ...req.body,
      numéro: numeroDevis,
      dateCréation: new Date(),
      statut: 'WAITING_APPROVAL',
      estDemandeClient: true,
      clientId: req.body.clientId || req.userId, // Utiliser l'ID du client ou l'ID de l'utilisateur
      total: 0 // Le total sera calculé par le vendeur
    });

    await devis.save();
    console.log("Demande de devis created successfully:", devis);

    res.status(201).json(devis);
  } catch (error) {
    console.error('Error creating devis request:', error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        message: 'Erreur de validation',
        errors
      });
    }
    res.status(400).json({
      message: 'Erreur lors de la création de la demande de devis',
      error: error.message
    });
  }
});

// Route pour récupérer les demandes de devis en attente (vendeur/responsable)
router.get('/demandes', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un vendeur ou un responsable
    if (req.userRole !== 'VENDEUR' && req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({
        message: 'Accès refusé'
      });
    }

    let query = { estDemandeClient: true };

    // Si c'est un vendeur, filtrer par les clients qui lui sont assignés
    if (req.userRole === 'VENDEUR') {
      // Récupérer les clients assignés à ce vendeur
      const clients = await Client.find({ vendeurId: req.userId });
      const clientIds = clients.map(client => client._id);

      query.clientId = { $in: clientIds };
    }

    // Si c'est un responsable, filtrer par les vendeurs qui lui sont assignés
    if (req.userRole === 'RESPONSABLE') {
      // Récupérer les vendeurs assignés à ce responsable
      const vendeurs = await User.find({
        role: 'VENDEUR',
        responsables: { $in: [req.userId] }
      });
      const vendeurIds = vendeurs.map(vendeur => vendeur._id);

      // Récupérer les clients assignés à ces vendeurs
      const clients = await Client.find({ vendeurId: { $in: vendeurIds } });
      const clientIds = clients.map(client => client._id);

      query.clientId = { $in: clientIds };
    }

    const demandes = await Devis.find(query).populate('clientId');
    console.log(`Returning ${demandes.length} devis requests`);

    res.json(demandes);
  } catch (error) {
    console.error('Error fetching devis requests:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des demandes de devis',
      error: error.message
    });
  }
});

// Route pour valider une demande de devis (responsable)
router.patch('/:id/valider', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({
        message: 'Seuls les responsables peuvent valider les demandes de devis'
      });
    }

    // Récupérer le devis
    const devis = await Devis.findById(req.params.id);
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    // Mettre à jour le statut du devis
    devis.statut = 'APPROVED_INTERNAL';
    devis.validationResponsable = {
      statut: 'APPROVED',
      date: new Date(),
      commentaires: req.body.commentaires || '',
      validéPar: req.userId
    };
    devis.responsableId = req.userId;

    await devis.save();

    res.json(devis);
  } catch (error) {
    console.error('Error validating devis request:', error);
    res.status(500).json({
      message: 'Erreur lors de la validation de la demande de devis',
      error: error.message
    });
  }
});

// Route pour rejeter une demande de devis (responsable)
router.patch('/:id/rejeter', verifyToken, async (req, res) => {
  try {
    // Vérifier que l'utilisateur est un responsable
    if (req.userRole !== 'RESPONSABLE') {
      return res.status(403).json({
        message: 'Seuls les responsables peuvent rejeter les demandes de devis'
      });
    }

    // Récupérer le devis
    const devis = await Devis.findById(req.params.id);
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    // Mettre à jour le statut du devis
    devis.statut = 'REJECTED';
    devis.validationResponsable = {
      statut: 'REJECTED',
      date: new Date(),
      commentaires: req.body.commentaires || '',
      validéPar: req.userId
    };
    devis.responsableId = req.userId;

    await devis.save();

    res.json(devis);
  } catch (error) {
    console.error('Error rejecting devis request:', error);
    res.status(500).json({
      message: 'Erreur lors du rejet de la demande de devis',
      error: error.message
    });
  }
});

// Create a new devis (POST /api/devis)
router.post('/', verifyToken, async (req, res) => {
  try {
    console.log('Creating new devis with data:', req.body);

    // Vérifier si l'utilisateur est un RESPONSABLE et définir le vendeurId correctement
    if (req.userRole === 'RESPONSABLE') {
      console.log('RESPONSABLE creating a devis, setting vendeurId to user ID:', req.userId);
      req.body.vendeurId = req.userId;
      req.body.responsableId = req.userId;
    }

    // Si l'utilisateur est un VENDEUR, assigner le responsableId
    if (req.userRole === 'VENDEUR') {
      const User = require('../models/UserModel');
      const vendeurUser = await User.findById(req.userId);
      if (vendeurUser && vendeurUser.responsables && vendeurUser.responsables.length > 0) {
        req.body.responsableId = vendeurUser.responsables[0]; // Prendre le premier responsable
        console.log('VENDEUR creating a devis, setting responsableId to:', req.body.responsableId);
      }
    }

    // Normalize clientId (it might be an object with _id property)
    const mongoose = require('mongoose');
    if (req.body.clientId) {
      let clientIdToUse;

      if (typeof req.body.clientId === 'object' && req.body.clientId._id) {
        clientIdToUse = req.body.clientId._id;
      } else if (typeof req.body.clientId === 'string') {
        clientIdToUse = req.body.clientId;
      } else {
        clientIdToUse = req.body.clientId;
      }

      console.log("Normalized clientId:", clientIdToUse);

      // Vérifier si le client existe
      const Client = require('../models/ClientModel');
      const client = await Client.findById(clientIdToUse);
      if (client) {
        console.log("Client found:", client);

        // Convert to ObjectId
        req.body.clientId = new mongoose.Types.ObjectId(clientIdToUse);
        console.log("Final clientId for devis:", req.body.clientId);

        // Check if there are any enterprise users associated with this client
        const User = require('../models/UserModel');
        const enterpriseUsers = await User.find({
          role: 'ENTREPRISE',
          $or: [
            { entrepriseId: req.body.clientId },
            { email: client.email }
          ]
        });

        console.log(`Found ${enterpriseUsers.length} enterprise users associated with this client`);

        // If no enterprise user is associated with this client, update any enterprise user with matching email
        if (enterpriseUsers.length === 0) {
          const userWithSameEmail = await User.findOne({
            role: 'ENTREPRISE',
            email: client.email
          });

          if (userWithSameEmail) {
            console.log(`Found enterprise user with matching email: ${userWithSameEmail.email}`);
            userWithSameEmail.entrepriseId = client._id;
            await userWithSameEmail.save();
            console.log(`Updated enterprise user with client ID: ${client._id}`);
          } else {
            console.log(`No enterprise user found with email: ${client.email}`);
          }
        }
      } else {
        console.log(`Client with ID ${clientIdToUse} not found`);
      }
    }

    // Générer un numéro de devis si non fourni ou si le numéro existe déjà
    if (!req.body.numéro) {
      req.body.numéro = await generateDevisNumber();
      console.log(`Numéro de devis généré: ${req.body.numéro}`);
    } else {
      // Vérifier si le numéro fourni existe déjà
      const existingDevis = await Devis.findOne({ numéro: req.body.numéro });
      if (existingDevis) {
        // Générer un nouveau numéro unique
        req.body.numéro = await generateDevisNumber();
        console.log(`Numéro de devis existant, généré un nouveau: ${req.body.numéro}`);
      }
    }

    // Create a new devis
    const devis = new Devis({
      ...req.body,
      dateCréation: req.body.dateCréation || new Date(),
      vendeurId: req.userId // Set the vendeurId to the current user's ID
    });

    await devis.save();
    console.log("Devis created successfully:", devis);

    // Get client information for the notification
    let clientName = 'Client inconnu';
    if (devis.clientId) {
      const client = await Client.findById(devis.clientId);
      if (client) {
        clientName = client.nom;
      }
    }



    res.status(201).json(devis);
  } catch (error) {
    console.error('Error creating devis:', error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        message: 'Erreur de validation',
        errors
      });
    }
    res.status(400).json({
      message: 'Erreur lors de la création du devis',
      error: error.message
    });
  }
});

// Middleware pour filtrer les devis par entreprise ou responsable
const filterByEntreprise = async (req, res, next) => {
  try {
    if (req.userRole === 'RESPONSABLE') {
      // Récupérer l'ID du responsable associé à l'utilisateur
      const User = require('../models/UserModel');
      const Client = require('../models/ClientModel');
      const mongoose = require('mongoose');

      const user = await User.findById(req.userId);
      console.log('User found:', user);

      // Find all vendeurs associated with this RESPONSABLE user
      const vendeurs = await User.find({
        role: 'VENDEUR',
        responsables: { $in: [user._id] }
      });
      console.log(`Found ${vendeurs.length} vendeurs associated with this RESPONSABLE user`);

      // Store vendeur IDs for later use
      const vendeurIds = vendeurs.map(v => v._id);
      req.vendeurIds = vendeurIds;

      if (user && user.entrepriseId) {
        // Vérifier si le client existe
        const client = await Client.findById(user.entrepriseId);
        if (client) {
          req.entrepriseId = user.entrepriseId;
          console.log('Using entrepriseId from user:', req.entrepriseId);
          console.log('Client found:', client);

          // DEBUG: Vérifier les devis pour ce client
          const devis = await Devis.find({
            clientId: new mongoose.Types.ObjectId(user.entrepriseId)
          });
          console.log(`Found ${devis.length} quotes for client ID ${user.entrepriseId}`);

          // Check for quotes created by vendeurs associated with this ENTREPRISE
          if (vendeurIds.length > 0) {
            const vendeurDevis = await Devis.find({
              vendeurId: { $in: vendeurIds }
            });
            console.log(`Found ${vendeurDevis.length} quotes created by vendeurs associated with this ENTREPRISE`);
          }

          // Si aucun devis n'est trouvé, vérifier tous les devis
          if (devis.length === 0) {
            const allDevis = await Devis.find();
            console.log(`Total quotes in system: ${allDevis.length}`);

            if (allDevis.length > 0) {
              console.log('Sample quote clientId:', allDevis[0].clientId);

              // Check if any quotes match this client by string comparison
              const matchingDevis = allDevis.filter(d =>
                d.clientId && d.clientId.toString() === user.entrepriseId.toString()
              );
              console.log(`Found ${matchingDevis.length} matching quotes by string comparison`);

              if (matchingDevis.length > 0) {
                console.log('First matching quote:', matchingDevis[0]);
              }
            }
          }
        } else {
          console.log(`Client with ID ${user.entrepriseId} not found`);

          // Try to find a client with the same email
          const clientByEmail = await Client.findOne({ email: user.email });
          if (clientByEmail) {
            req.entrepriseId = clientByEmail._id;
            console.log('Found client by email:', clientByEmail._id);

            // Update user with the correct client ID
            await User.findByIdAndUpdate(user._id, { entrepriseId: clientByEmail._id });
            console.log('Updated user with entrepriseId from email match:', clientByEmail._id);
          }
        }
      } else {
        // Si l'entrepriseId n'est pas défini, essayer de trouver le client par email
        const client = await Client.findOne({ email: user.email });
        if (client) {
          req.entrepriseId = client._id;
          console.log('Found client by email:', client._id);

          // Mettre à jour l'utilisateur avec l'ID du client
          await User.findByIdAndUpdate(user._id, { entrepriseId: client._id });
          console.log('Updated user with entrepriseId:', client._id);
        } else {
          console.log('No client found for user with email:', user.email);

          // Créer un nouveau client pour cet utilisateur
          const newClient = new Client({
            nom: user.nomEntreprise || user.nom,
            adresse: user.adresseEntreprise || user.adresse || 'Adresse non spécifiée',
            email: user.email,
            contact: user.contact || user.nom
          });

          await newClient.save();
          console.log('Created new client for user:', newClient);

          // Mettre à jour l'utilisateur avec l'ID du nouveau client
          await User.findByIdAndUpdate(user._id, { entrepriseId: newClient._id });
          req.entrepriseId = newClient._id;
          console.log('Updated user with new entrepriseId:', newClient._id);
        }
      }
    }
    next();
  } catch (error) {
    console.error('Erreur lors du filtrage par entreprise:', error);
    next();
  }
};

// Read all devis (GET /api/devis)
router.get('/', verifyToken, filterByEntreprise, async (req, res) => {
  try {
    let query = {};

    // Si l'utilisateur est un responsable, filtrer par les devis qui lui sont associés
    if (req.userRole === 'RESPONSABLE') {
      console.log("Filtering quotes for responsable ID:", req.userId);

      const User = require('../models/UserModel');
      const mongoose = require('mongoose');

      // Find all vendeurs associated with this responsable
      const vendeurs = await User.find({
        role: 'VENDEUR',
        responsables: { $in: [req.userId] }
      });

      const vendeurIds = vendeurs.map(v => v._id);
      console.log('Found vendeurs for responsable:', vendeurIds);

      // Create a combined query that includes:
      // 1. Quotes created by this responsable
      // 2. Quotes created by vendeurs associated with this responsable
      let orConditions = [
        { vendeurId: new mongoose.Types.ObjectId(req.userId) }, // Quotes created by responsable
        { responsableId: new mongoose.Types.ObjectId(req.userId) } // Quotes assigned to responsable
      ];

      // Add condition for quotes created by vendeurs associated with this responsable
      if (vendeurIds.length > 0) {
        orConditions.push({ vendeurId: { $in: vendeurIds } });
      }

      if (orConditions.length > 0) {
        query = { $or: orConditions };
        console.log("Using combined query for RESPONSABLE user:", JSON.stringify(query));
      } else {
        console.log('No conditions found for responsable, returning empty result');
        return res.status(200).json([]);
      }
    }

    const devis = await Devis.find(query).populate('clientId');
    console.log(`Returning ${devis.length} quotes to client`);
    res.json(devis);
  } catch (error) {
    console.error('Error fetching devis:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des devis',
      error: error.message
    });
  }
});

// Read a devis by ID (GET /api/devis/:id)
router.get('/:id', verifyToken, filterByEntreprise, async (req, res) => {
  try {
    const devis = await Devis.findById(req.params.id).populate('clientId');
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    // Accès autorisé pour tous les utilisateurs authentifiés
    // Restriction d'accès désactivée

    res.json(devis);
  } catch (error) {
    console.error('Error fetching devis by ID:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération du devis',
      error: error.message
    });
  }
});

// Update devis status (PUT /api/devis/:id/status)
router.put('/:id/status', verifyToken, async (req, res) => {
  try {
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ message: 'Le statut est requis' });
    }

    // Vérifier que le statut est valide
    const validStatuses = ['DRAFT', 'SENT', 'PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED', 'BROUILLON', 'ENVOYÉ', 'ACCEPTÉ', 'REFUSÉ', 'EXPIRÉ'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Statut invalide' });
    }

    // Récupérer le devis
    const devis = await Devis.findById(req.params.id);
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    // Si l'utilisateur est un CLIENT, vérifier qu'il est bien le client associé au devis
    if (req.userRole === 'CLIENT') {
      const user = await User.findById(req.userId);

      if (!user || !user.clientId) {
        return res.status(403).json({ message: 'Accès refusé. Vous n\'êtes pas associé à un client.' });
      }

      if (devis.clientId.toString() !== user.clientId.toString()) {
        return res.status(403).json({ message: 'Accès refusé. Ce devis ne vous appartient pas.' });
      }

      // Les clients ne peuvent qu'accepter ou refuser les devis
      if (status !== 'ACCEPTED' && status !== 'REJECTED' && status !== 'ACCEPTÉ' && status !== 'REFUSÉ') {
        return res.status(403).json({ message: 'Accès refusé. Vous ne pouvez qu\'accepter ou refuser un devis.' });
      }
    }

    // Mettre à jour le statut du devis
    devis.statut = status;
    await devis.save();

    // Récupérer les informations du client pour la notification
    let clientName = 'Client inconnu';
    if (devis.clientId) {
      const client = await Client.findById(devis.clientId);
      if (client) {
        clientName = client.nom;
      }
    }

    // Envoyer une notification au vendeur si le devis est accepté ou refusé par le client
    if ((status === 'ACCEPTED' || status === 'REJECTED' || status === 'ACCEPTÉ' || status === 'REFUSÉ') && req.userRole === 'CLIENT') {
      // Récupérer le vendeur associé au devis
      if (devis.vendeurId) {
        const vendeur = await User.findById(devis.vendeurId);
        if (vendeur) {
          console.log(`Devis ${status === 'ACCEPTED' || status === 'ACCEPTÉ' ? 'accepté' : 'refusé'} par ${clientName}. Notification envoyée à ${vendeur.email}`);
          // Ici, vous pourriez implémenter l'envoi d'un email au vendeur
        }
      }
    }

    res.json(devis);
  } catch (error) {
    console.error('Error updating devis status:', error);
    res.status(500).json({
      message: 'Erreur lors de la mise à jour du statut du devis',
      error: error.message
    });
  }
});

// Update a devis (PUT /api/devis/:id)
router.put('/:id', verifyToken, async (req, res) => {
  try {
    console.log('Updating devis with ID:', req.params.id);
    console.log('Update data:', req.body);

    const devis = await Devis.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    // Get client information for the notification
    let clientName = 'Client inconnu';
    if (devis.clientId) {
      const client = await Client.findById(devis.clientId);
      if (client) {
        clientName = client.nom;
      }
    }



    res.json(devis);
  } catch (error) {
    console.error('Error updating devis:', error);
    res.status(400).json({
      message: 'Erreur lors de la mise à jour du devis',
      error: error.message
    });
  }
});

// Delete a devis (DELETE /api/devis/:id)
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const devis = await Devis.findByIdAndDelete(req.params.id);
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }



    res.status(204).send();
  } catch (error) {
    console.error('Error deleting devis:', error);
    res.status(500).json({
      message: 'Erreur lors de la suppression du devis',
      error: error.message
    });
  }
});

// Accept a devis (PATCH /api/devis/:id/accept)
router.patch('/:id/accept', verifyToken, async (req, res) => {
  try {
    console.log('Accepting devis with ID:', req.params.id);
    console.log('User role:', req.userRole);
    console.log('User ID:', req.userId);
    console.log('Request headers:', req.headers);

    // Vérifier que l'ID est valide pour MongoDB
    if (!req.params.id || !req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
      console.error('Invalid devis ID format:', req.params.id);
      return res.status(400).json({ message: 'Format d\'ID de devis invalide' });
    }

    // Vérifier d'abord si le devis existe
    const existingDevis = await Devis.findById(req.params.id);
    if (!existingDevis) {
      console.error('Devis not found with ID:', req.params.id);
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    console.log('Devis trouvé:', existingDevis);

    // Vérifier si le devis est déjà accepté ou refusé
    if (existingDevis.statut === 'ACCEPTED' || existingDevis.statut === 'ACCEPTÉ') {
      console.log('Devis already accepted:', req.params.id);
      return res.json(existingDevis); // Retourner le devis tel quel s'il est déjà accepté
    }

    if (existingDevis.statut === 'REJECTED' || existingDevis.statut === 'REFUSÉ') {
      console.error('Cannot accept a rejected devis:', req.params.id);
      return res.status(400).json({ message: 'Impossible d\'accepter un devis déjà refusé' });
    }

    // Mettre à jour le statut du devis (utiliser les deux formats pour compatibilité)
    const devis = await Devis.findByIdAndUpdate(
      req.params.id,
      { statut: 'ACCEPTED' }, // Utiliser ACCEPTED au lieu de ACCEPTÉ pour compatibilité
      { new: true, runValidators: true }
    );

    console.log('Devis mis à jour:', devis);



    res.json(devis);
  } catch (error) {
    console.error('Error accepting devis:', error);
    console.error('Error stack:', error.stack);

    // Envoyer une réponse d'erreur plus détaillée
    res.status(500).json({
      message: 'Erreur lors de l\'acceptation du devis',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Reject a devis (PATCH /api/devis/:id/reject)
router.patch('/:id/reject', verifyToken, async (req, res) => {
  try {
    console.log('Rejecting devis with ID:', req.params.id);
    console.log('User role:', req.userRole);
    console.log('User ID:', req.userId);
    console.log('Rejection reason:', req.body.reason);
    console.log('Request headers:', req.headers);
    console.log('Request body:', req.body);

    // Vérifier que l'ID est valide pour MongoDB
    if (!req.params.id || !req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
      console.error('Invalid devis ID format:', req.params.id);
      return res.status(400).json({ message: 'Format d\'ID de devis invalide' });
    }

    // Vérifier d'abord si le devis existe
    const existingDevis = await Devis.findById(req.params.id);
    if (!existingDevis) {
      console.error('Devis not found with ID:', req.params.id);
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    console.log('Devis trouvé:', existingDevis);

    // Vérifier si le devis est déjà refusé ou accepté
    if (existingDevis.statut === 'REJECTED' || existingDevis.statut === 'REFUSÉ') {
      console.log('Devis already rejected:', req.params.id);
      return res.json(existingDevis); // Retourner le devis tel quel s'il est déjà refusé
    }

    if (existingDevis.statut === 'ACCEPTED' || existingDevis.statut === 'ACCEPTÉ') {
      console.error('Cannot reject an accepted devis:', req.params.id);
      return res.status(400).json({ message: 'Impossible de refuser un devis déjà accepté' });
    }

    // Préparer les données de mise à jour
    const updateData = {
      statut: 'REJECTED' // Utiliser REJECTED au lieu de REFUSÉ pour compatibilité
    };

    // Si une raison est fournie, l'ajouter aux notes
    if (req.body.reason && req.body.reason.trim() !== '') {
      const existingNotes = existingDevis.notes || '';

      // Ajouter la raison du refus aux notes existantes
      updateData.notes = existingNotes
        ? `${existingNotes}\n\nMotif de refus: ${req.body.reason}`
        : `Motif de refus: ${req.body.reason}`;
    }

    console.log('Données de mise à jour:', updateData);

    const devis = await Devis.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    console.log('Devis mis à jour:', devis);



    res.json(devis);
  } catch (error) {
    console.error('Error rejecting devis:', error);
    console.error('Error stack:', error.stack);

    // Envoyer une réponse d'erreur plus détaillée
    res.status(500).json({
      message: 'Erreur lors du refus du devis',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Convertir un devis en facture (POST /api/devis/:id/convert)
router.post('/:id/convert', verifyToken, async (req, res) => {
  try {
    console.log('Converting devis to facture with ID:', req.params.id);

    // Accès autorisé pour tous les utilisateurs authentifiés
    // Restriction d'accès désactivée

    // Récupérer le devis
    const devis = await Devis.findById(req.params.id).populate('clientId');
    if (!devis) {
      return res.status(404).json({ message: 'Devis non trouvé' });
    }

    // Vérifier que le devis est dans un état qui permet la conversion
    if (devis.statut !== 'ACCEPTED' && devis.statut !== 'ACCEPTÉ') {
      return res.status(400).json({
        message: 'Seuls les devis acceptés peuvent être convertis en factures'
      });
    }

    console.log('Statut du devis avant conversion:', devis.statut);

    // Créer une nouvelle facture basée sur le devis
    const Facture = require('../models/FactureModel');

    // Générer un numéro de facture unique en utilisant la fonction du module FactureRoute
    const { generateInvoiceNumber } = require('./FactureRoute');
    const factureNumber = await generateInvoiceNumber();

    const facture = new Facture({
      numero: factureNumber,
      clientId: devis.clientId._id,
      dateEmission: new Date(),
      lignes: devis.lignes.map(ligne => ({
        produit: ligne.produit,
        description: ligne.description,
        quantite: ligne.quantite,
        prixUnitaire: ligne.prixUnitaire,
        montantHT: ligne.montantHT,
        montantTTC: ligne.montantTTC
      })),
      notes: devis.notes,
      statut: 'SENT',
      total: devis.total,
      tauxTVA: devis.tauxTVA || 20
    });

    await facture.save();

    // Mettre à jour le statut du devis
    devis.statut = 'ACCEPTED';
    await devis.save();

    res.status(201).json({
      message: 'Devis converti en facture avec succès',
      facture,
      devis
    });
  } catch (error) {
    console.error('Error converting devis to facture:', error);
    res.status(500).json({
      message: 'Erreur lors de la conversion du devis en facture',
      error: error.message
    });
  }
});

// Generate PDF for a quote
router.get("/:id/pdf", verifyToken, async (req, res) => {
  try {
    console.log(`Received GET /devis/${req.params.id}/pdf request`);
    const devis = await Devis.findById(req.params.id).populate("clientId");

    if (!devis) return res.status(404).json({ error: "Quote not found" });

    // Generate PDF directly on the server
    const pdfBuffer = await generateQuotePDF(devis);

    // Set the response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="devis-${devis.numéro}.pdf"`);

    // Send the PDF buffer directly to the client
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error generating PDF for quote:", error.message);
    res.status(500).json({ error: "Error while generating PDF for quote" });
  }
});

// Generate PDF for a quote
const generateQuotePDF = async (devis) => {
  return new Promise(async (resolve, reject) => {
    try {
      // Get template settings and parameters
      const ResponsableTemplate = require('../models/ResponsableTemplateModel');
      const BaseTemplate = require('../models/BaseTemplateModel');
      const Parametres = require('../models/ParametresModel');

      let responsableTemplate = null;
      let baseTemplate = null;
      let params = await Parametres.findOne();

      // Try to get responsable template settings based on the quote context
      let entrepriseId = null;

      // First, try to get enterprise ID from responsableId (if quote was created by responsable)
      if (devis.responsableId) {
        entrepriseId = devis.responsableId;
        console.log('Using responsableId as entrepriseId:', entrepriseId);
      }
      // If not, try to get enterprise ID from vendeurId (get the vendeur's enterprise)
      else if (devis.vendeurId) {
        const User = require('../models/UserModel');
        const vendeur = await User.findById(devis.vendeurId);
        if (vendeur && vendeur.entrepriseId) {
          entrepriseId = vendeur.entrepriseId;
          console.log('Using vendeur entrepriseId:', entrepriseId);
        }
      }
      // Fallback: try to get from client's enterprise (old logic)
      else if (devis.clientId && devis.clientId.entrepriseId) {
        entrepriseId = devis.clientId.entrepriseId;
        console.log('Using client entrepriseId (fallback):', entrepriseId);
      }

      if (entrepriseId) {
        responsableTemplate = await ResponsableTemplate.findOne({
          entrepriseId: entrepriseId
        }).populate('devisTemplate.baseTemplateId');

        if (responsableTemplate && responsableTemplate.devisTemplate.baseTemplateId) {
          baseTemplate = responsableTemplate.devisTemplate.baseTemplateId;
          console.log('Found responsable template for enterprise:', entrepriseId);
        }
      } else {
        console.log('No enterprise ID found for template lookup');
      }

      // Default colors and settings from template
      // Priority: ResponsableTemplate > TemplateSettings > Default
      let primaryColor = '#f57c00'; // Default BenYounes Web orange color
      let logoPath = null;

      if (responsableTemplate && responsableTemplate.devisTemplate) {
        // Use responsable customized colors and logo
        primaryColor = responsableTemplate.devisTemplate.color || primaryColor;
        logoPath = responsableTemplate.devisTemplate.logo;
      }

      // Fallback to global parameters
      if (!logoPath && params) {
        logoPath = params.logo;
      }

      // Nettoyer la couleur si elle est entourée de guillemets
      if (primaryColor && typeof primaryColor === 'string') {
        primaryColor = primaryColor.replace(/^["'](.+)["']$/, '$1');
      }

      // Validate the color format
      const isValidHex = /^#?([a-f\d]{3}|[a-f\d]{6})$/i.test(primaryColor);
      if (!isValidHex) {
        console.warn(`Invalid hex color: ${primaryColor}, falling back to default #f57c00`);
        primaryColor = '#f57c00'; // Fallback to BenYounes Web orange color
      }

      // Ensure the hex color has a # prefix
      if (!primaryColor.startsWith('#')) {
        primaryColor = '#' + primaryColor;
      }

      // Handle 3-digit hex colors
      if (primaryColor.length === 4) {
        primaryColor = '#' + primaryColor[1] + primaryColor[1] + primaryColor[2] + primaryColor[2] + primaryColor[3] + primaryColor[3];
      }

      console.log('Using primary color for quote (validated):', primaryColor);

      // Determine template layout (standard vs moderne)
      let templateLayout = 'standard'; // Default layout
      if (baseTemplate && baseTemplate.layout) {
        templateLayout = baseTemplate.layout;
      }
      console.log('Using template layout for quote:', templateLayout);

      const defaultCurrency = params?.defaultCurrency || 'EUR';

      console.log('Using template settings for quote:', {
        type: 'devis',
        color: primaryColor,
        currency: defaultCurrency,
        logo: logoPath || 'No logo',
        layout: templateLayout || 'standard'
      });

      // Format currency - always use DT (Tunisian Dinar)
      const formatCurrency = (amount) => {
        if (!amount && amount !== 0) return '0';

        // Format avec des points comme séparateurs de milliers et virgule pour les décimales
        const parts = amount.toFixed(2).split('.');
        const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        const decimalPart = parts[1];

        // Ajouter manuellement le symbole DT après le nombre
        return `${integerPart},${decimalPart} DT`;
      };

      // Create a PDF document with a single page and compressed content
      const doc = new PDFDocument({
        margin: 30, // Reduced margins to fit more content
        autoFirstPage: true,
        size: 'A4',
        layout: 'portrait',
        compress: true, // Enable compression
        bufferPages: true // Enable buffer pages to prevent automatic page breaks
      });

      // Create a buffer to store the PDF
      const buffers = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Calculate totals correctly
      let totalHT = 0;
      if (devis.lignes && devis.lignes.length > 0) {
        devis.lignes.forEach(ligne => {
          const montantHT = (ligne.montantHT || ligne.prixUnitaire * ligne.quantite);
          totalHT += montantHT;
        });
      }

      const tauxTVA = devis.tauxTVA || 0;
      const montantTVA = totalHT * (tauxTVA / 100);
      const totalTTC = totalHT + montantTVA;

      // White background for the whole document
      doc.rect(0, 0, doc.page.width, doc.page.height).fill('#FFFFFF');

      // No colored header background - using orange text instead
      console.log('Using orange text for header instead of background color');

      // Apply different layouts based on template
      if (templateLayout === 'moderne') {
        // MODERNE LAYOUT: Logo on left, Quote reference on right
        console.log('Applying MODERNE layout for quote');

        // Try to add logo on the LEFT side
        try {
          console.log('Logo path from database:', logoPath);

          if (logoPath) {
            // Nettoyer le chemin du logo si nécessaire
            const cleanLogoPath = logoPath.replace(/^["'](.+)["']$/, '$1');
            console.log('Cleaned logo path:', cleanLogoPath);

            // Construire différentes variantes de chemins pour trouver le logo
            const possiblePaths = [];

            // Chemin absolu si le chemin commence par /uploads/
            if (cleanLogoPath.startsWith('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Chemin relatif si le chemin commence par uploads/ sans le slash initial
            if (cleanLogoPath.startsWith('uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Essayer avec le nom de fichier uniquement
            const filename = path.basename(cleanLogoPath);
            possiblePaths.push(path.join(__dirname, '..', 'uploads', filename));

            // Essayer avec une recherche partielle basée sur l'ID du fichier
            if (filename.includes('-')) {
              const parts = filename.split('-');
              if (parts.length >= 2) {
                const uniqueId = parts[parts.length - 1];
                try {
                  const uploadFiles = fs.readdirSync(path.join(__dirname, '..', 'uploads'));
                  const matchingFile = uploadFiles.find(file => file.includes(uniqueId));
                  if (matchingFile) {
                    console.log('Found matching file by unique ID:', matchingFile);
                    possiblePaths.push(path.join(__dirname, '..', 'uploads', matchingFile));
                  }
                } catch (err) {
                  console.warn('Error listing upload directory:', err.message);
                }
              }
            }

            // Essayer dans le dossier uploads/logos
            possiblePaths.push(path.join(__dirname, '..', 'uploads', 'logos', filename));

            // Essayer sans le préfixe /uploads/
            if (cleanLogoPath.includes('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath.replace('/uploads/', '')));
            }

            // Essayer le chemin direct
            possiblePaths.push(cleanLogoPath);

            // Essayer avec le chemin complet
            possiblePaths.push(path.join(__dirname, '..', 'uploads', cleanLogoPath));

            console.log('Trying these paths for logo:', possiblePaths);

            // Essayer chaque chemin possible
            let logoAdded = false;
            for (const tryPath of possiblePaths) {
              console.log('Checking if logo exists at:', tryPath);
              if (fs.existsSync(tryPath)) {
                console.log('Logo found at:', tryPath);
                // Position the logo on the LEFT side for moderne layout
                doc.image(tryPath, 50, 15, { width: 70, height: 70 });
                console.log('Logo added to PDF successfully on LEFT side (moderne layout)');
                logoAdded = true;
                break;
              }
            }

            if (!logoAdded) {
              console.warn('Logo file not found at any path. Tried:', possiblePaths.join(', '));
            }
          } else {
            console.log('No logo specified for this template');
          }
        } catch (logoError) {
          console.warn('Could not add logo to PDF:', logoError.message);
          console.error('Logo error stack:', logoError.stack);
        }

        // Add quote title and date on the RIGHT side for moderne layout
        doc.fillColor(primaryColor)
           .fontSize(24)  // Larger font for moderne style
           .font('Helvetica-Bold')
           .text(`Devis`, 400, 30, { align: 'right' });

        doc.fillColor('#000000')
           .fontSize(14)
           .font('Helvetica')
           .text(`${devis.numéro}`, 400, 55, { align: 'right' });

        doc.fontSize(12)
           .text(`Date d'émission: ${new Date(devis.dateCréation).toLocaleDateString()}`, 400, 75, { align: 'right' });

      } else {
        // STANDARD LAYOUT: Logo on right, Quote reference on left (current layout)
        console.log('Applying STANDARD layout for quote');

        // Try to add logo on the RIGHT side (current behavior)
        try {
          console.log('Logo path from database:', logoPath);

          if (logoPath) {
            // Nettoyer le chemin du logo si nécessaire
            const cleanLogoPath = logoPath.replace(/^["'](.+)["']$/, '$1');
            console.log('Cleaned logo path:', cleanLogoPath);

            // Construire différentes variantes de chemins pour trouver le logo
            const possiblePaths = [];

            // Chemin absolu si le chemin commence par /uploads/
            if (cleanLogoPath.startsWith('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Chemin relatif si le chemin commence par uploads/ sans le slash initial
            if (cleanLogoPath.startsWith('uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath));
            }

            // Essayer avec le nom de fichier uniquement
            const filename = path.basename(cleanLogoPath);
            possiblePaths.push(path.join(__dirname, '..', 'uploads', filename));

            // Essayer avec une recherche partielle basée sur l'ID du fichier
            if (filename.includes('-')) {
              const parts = filename.split('-');
              if (parts.length >= 2) {
                const uniqueId = parts[parts.length - 1];
                try {
                  const uploadFiles = fs.readdirSync(path.join(__dirname, '..', 'uploads'));
                  const matchingFile = uploadFiles.find(file => file.includes(uniqueId));
                  if (matchingFile) {
                    console.log('Found matching file by unique ID:', matchingFile);
                    possiblePaths.push(path.join(__dirname, '..', 'uploads', matchingFile));
                  }
                } catch (err) {
                  console.warn('Error listing upload directory:', err.message);
                }
              }
            }

            // Essayer dans le dossier uploads/logos
            possiblePaths.push(path.join(__dirname, '..', 'uploads', 'logos', filename));

            // Essayer sans le préfixe /uploads/
            if (cleanLogoPath.includes('/uploads/')) {
              possiblePaths.push(path.join(__dirname, '..', cleanLogoPath.replace('/uploads/', '')));
            }

            // Essayer le chemin direct
            possiblePaths.push(cleanLogoPath);

            // Essayer avec le chemin complet
            possiblePaths.push(path.join(__dirname, '..', 'uploads', cleanLogoPath));

            console.log('Trying these paths for logo:', possiblePaths);

            // Essayer chaque chemin possible
            let logoAdded = false;
            for (const tryPath of possiblePaths) {
              console.log('Checking if logo exists at:', tryPath);
              if (fs.existsSync(tryPath)) {
                console.log('Logo found at:', tryPath);
                // Position the logo on the RIGHT side for standard layout
                doc.image(tryPath, 480, 15, { width: 70, height: 70 });
                console.log('Logo added to PDF successfully on RIGHT side (standard layout)');
                logoAdded = true;
                break;
              }
            }

            if (!logoAdded) {
              console.warn('Logo file not found at any path. Tried:', possiblePaths.join(', '));
            }
          } else {
            console.log('No logo specified for this template');
          }
        } catch (logoError) {
          console.warn('Could not add logo to PDF:', logoError.message);
          console.error('Logo error stack:', logoError.stack);
        }

        // Add quote title and date on the LEFT side for standard layout
        doc.fillColor(primaryColor)
           .fontSize(18)
           .font('Helvetica-Bold')
           .text(`Devis ${devis.numéro}`, 50, 50);

        doc.fontSize(12)
           .font('Helvetica')
           .text(`Date d'émission: ${new Date(devis.dateCréation).toLocaleDateString()}`, 50, 75);
      }

      // Reset text color
      doc.fillColor('#000000');

      // Émetteur and Destinataire sections side by side - more compact
      const leftColumnX = 40;
      const rightColumnX = 300;
      const sectionY = 100;
      const lineHeight = 15; // Reduced line height to fit more content

      // Émetteur section
      doc.fillColor(primaryColor)
         .fontSize(14)
         .font('Helvetica-Bold')
         .text('Émetteur', leftColumnX, sectionY);

      doc.fillColor('#000000')
         .fontSize(12)
         .font('Helvetica-Bold')
         .text('Société :', leftColumnX, sectionY + lineHeight);

      doc.font('Helvetica')
         .text('Wonderland SARL', leftColumnX + 100, sectionY + lineHeight);

      doc.font('Helvetica-Bold')
         .text('Votre contact :', leftColumnX, sectionY + lineHeight*2);

      doc.font('Helvetica')
         .text('WAJDI HB', leftColumnX + 100, sectionY + lineHeight*2);

      doc.font('Helvetica-Bold')
         .text('Adresse :', leftColumnX, sectionY + lineHeight*3);

      doc.font('Helvetica')
         .text('12 rue du Lapin Blanc', leftColumnX + 100, sectionY + lineHeight*3)
         .text('12345 Westerham', leftColumnX + 100, sectionY + lineHeight*4);

      // Destinataire section
      doc.fillColor(primaryColor)
         .fontSize(14)
         .font('Helvetica-Bold')
         .text('Destinataire', rightColumnX, sectionY);

      if (devis.clientId) {
        doc.fillColor('#000000')
           .fontSize(12)
           .font('Helvetica-Bold')
           .text('Nom :', rightColumnX, sectionY + lineHeight);

        doc.font('Helvetica')
           .text(devis.clientId.nom || 'N/A', rightColumnX + 70, sectionY + lineHeight);

        doc.font('Helvetica-Bold')
           .text('Adresse :', rightColumnX, sectionY + lineHeight*2);

        doc.font('Helvetica')
           .text(devis.clientId.adresse || 'N/A', rightColumnX + 70, sectionY + lineHeight*2);

        doc.font('Helvetica-Bold')
           .text('Email :', rightColumnX, sectionY + lineHeight*3);

        doc.font('Helvetica')
           .text(devis.clientId.email || 'N/A', rightColumnX + 70, sectionY + lineHeight*3);
      }

      // Détail section title
      const tableTop = 260;

      doc.fillColor(primaryColor)
         .fontSize(14)
         .font('Helvetica-Bold')
         .text('Détail', 50, tableTop - 30);

      // Apply different table styles based on template layout
      if (templateLayout === 'moderne') {
        // MODERNE TABLE STYLE: Professional elegant design with subtle shadows and clean lines
        console.log('Applying MODERNE table style for quote');

        // Define column widths and positions for better alignment
        const colType = { x: 60, width: 80 };
        const colDesc = { x: 140, width: 160 };
        const colPrice = { x: 300, width: 80 };
        const colQty = { x: 380, width: 50 };
        const colTVA = { x: 430, width: 40 };
        const colTotal = { x: 470, width: 70 };

        // Create a subtle shadow effect by drawing a slightly offset darker rectangle
        doc.fillColor('#e0e0e0').rect(52, tableTop + 2, 500, 25).fill();

        // Main header background with gradient-like effect
        doc.fillColor('#f8f9fa').rect(50, tableTop, 500, 25).fill();

        // Draw main table border with rounded corners effect
        doc.strokeColor('#d1d5db').lineWidth(1.5);
        doc.rect(50, tableTop, 500, 25).stroke();

        // Draw elegant vertical separators with subtle styling
        doc.strokeColor('#e5e7eb').lineWidth(1);
        doc.moveTo(colDesc.x - 10, tableTop).lineTo(colDesc.x - 10, tableTop + 25).stroke();
        doc.moveTo(colPrice.x - 10, tableTop).lineTo(colPrice.x - 10, tableTop + 25).stroke();
        doc.moveTo(colQty.x - 10, tableTop).lineTo(colQty.x - 10, tableTop + 25).stroke();
        doc.moveTo(colTVA.x - 10, tableTop).lineTo(colTVA.x - 10, tableTop + 25).stroke();
        doc.moveTo(colTotal.x - 10, tableTop).lineTo(colTotal.x - 10, tableTop + 25).stroke();

        // Add a subtle bottom border for the header
        doc.strokeColor(primaryColor).lineWidth(2);
        doc.moveTo(50, tableTop + 25).lineTo(550, tableTop + 25).stroke();

        // Table headers with professional typography
        doc.fillColor('#374151').fontSize(10).font('Helvetica-Bold');
        doc.text('TYPE', colType.x, tableTop + 10, { width: colType.width, align: 'left' });
        doc.text('DESCRIPTION', colDesc.x, tableTop + 10, { width: colDesc.width, align: 'left' });
        doc.text('PRIX UNIT.', colPrice.x, tableTop + 10, { width: colPrice.width, align: 'right' });
        doc.text('QTÉ', colQty.x, tableTop + 10, { width: colQty.width, align: 'center' });
        doc.text('TVA', colTVA.x, tableTop + 10, { width: colTVA.width, align: 'center' });
        doc.text('TOTAL HT', colTotal.x, tableTop + 10, { width: colTotal.width, align: 'right' });

      } else {
        // STANDARD TABLE STYLE: Background color header (current style)
        console.log('Applying STANDARD table style for quote');

        // Table header with background color
        doc.rect(50, tableTop, 500, 25).fill(primaryColor);

        // Define column widths and positions for better alignment
        const colType = { x: 60, width: 80 };
        const colDesc = { x: 140, width: 160 };
        const colPrice = { x: 300, width: 80 };
        const colQty = { x: 380, width: 50 };
        const colTVA = { x: 430, width: 40 };
        const colTotal = { x: 470, width: 70 };

        // Table headers with white text on colored background
        doc.fillColor('#FFFFFF').fontSize(12);
        doc.text('Type', colType.x, tableTop + 7, { width: colType.width, align: 'left' });
        doc.text('Description', colDesc.x, tableTop + 7, { width: colDesc.width, align: 'left' });
        doc.text('Prix unitaire', colPrice.x, tableTop + 7, { width: colPrice.width, align: 'right' });
        doc.text('Quantité', colQty.x, tableTop + 7, { width: colQty.width, align: 'center' });
        doc.text('TVA', colTVA.x, tableTop + 7, { width: colTVA.width, align: 'center' });
        doc.text('Total HT', colTotal.x, tableTop + 7, { width: colTotal.width, align: 'right' });
      }

      // Define column widths and positions for table rows (same for both layouts)
      const colType = { x: 60, width: 80 };
      const colDesc = { x: 140, width: 160 };
      const colPrice = { x: 300, width: 80 };
      const colQty = { x: 380, width: 50 };
      const colTVA = { x: 430, width: 40 };
      const colTotal = { x: 470, width: 70 };

      // Reset text color
      doc.fillColor('#000000');

      // Table rows - more compact
      let y = tableTop + 25;
      const rowHeight = templateLayout === 'moderne' ? 22 : 20; // Slightly taller rows for moderne

      if (devis.lignes && devis.lignes.length > 0) {
        devis.lignes.forEach((ligne, i) => {
          if (templateLayout === 'moderne') {
            // MODERNE LAYOUT: Professional row styling with subtle alternating backgrounds
            if (i % 2 === 0) {
              // Even rows: very light background
              doc.fillColor('#fafbfc').rect(50, y - 2, 500, rowHeight).fill();
            } else {
              // Odd rows: white background
              doc.fillColor('#ffffff').rect(50, y - 2, 500, rowHeight).fill();
            }

            // Draw subtle row borders
            doc.strokeColor('#f1f3f4').lineWidth(0.5);
            doc.rect(50, y - 2, 500, rowHeight).stroke();

            // Draw elegant vertical separators
            doc.strokeColor('#e8eaed').lineWidth(0.5);
            doc.moveTo(colDesc.x - 10, y - 2).lineTo(colDesc.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colPrice.x - 10, y - 2).lineTo(colPrice.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colQty.x - 10, y - 2).lineTo(colQty.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colTVA.x - 10, y - 2).lineTo(colTVA.x - 10, y - 2 + rowHeight).stroke();
            doc.moveTo(colTotal.x - 10, y - 2).lineTo(colTotal.x - 10, y - 2 + rowHeight).stroke();
          } else {
            // STANDARD LAYOUT: Alternate row background for better readability
            if (i % 2 === 0) {
              doc.rect(50, y - 5, 500, rowHeight).fill('#f5f5f5');
              doc.fillColor('#000000');
            }
          }

          const montantHT = (ligne.montantHT || ligne.prixUnitaire * ligne.quantite);

          // Determine type (Service or Produit)
          const type = ligne.type || 'Service';

          if (templateLayout === 'moderne') {
            // MODERNE LAYOUT: Professional typography with better spacing
            doc.fillColor('#374151').fontSize(9).font('Helvetica');

            // Add content with proper vertical centering
            const textY = y + 7; // Center text vertically in the row
            doc.text(type, colType.x, textY, { width: colType.width, align: 'left' });
            doc.text(ligne.description || 'N/A', colDesc.x, textY, { width: colDesc.width, align: 'left' });

            // Numbers in a slightly different color for better readability
            doc.fillColor('#1f2937');
            doc.text(formatCurrency(ligne.prixUnitaire || 0), colPrice.x, textY, { width: colPrice.width, align: 'right' });
            doc.text(ligne.quantite?.toString() || '0', colQty.x, textY, { width: colQty.width, align: 'center' });
            doc.text(`${tauxTVA}%`, colTVA.x, textY, { width: colTVA.width, align: 'center' });

            // Total in primary color for emphasis
            doc.fillColor(primaryColor).font('Helvetica-Bold');
            doc.text(formatCurrency(montantHT), colTotal.x, textY, { width: colTotal.width, align: 'right' });
          } else {
            // STANDARD LAYOUT: Ensure text doesn't overflow by using width constraints
            doc.text(type, colType.x, y, { width: colType.width, align: 'left' });
            doc.text(ligne.description || 'N/A', colDesc.x, y, { width: colDesc.width, align: 'left' });
            doc.text(formatCurrency(ligne.prixUnitaire || 0), colPrice.x, y, { width: colPrice.width, align: 'right' });
            doc.text(ligne.quantite?.toString() || '0', colQty.x, y, { width: colQty.width, align: 'center' });
            doc.text(`${tauxTVA}%`, colTVA.x, y, { width: colTVA.width, align: 'center' });
            doc.text(formatCurrency(montantHT), colTotal.x, y, { width: colTotal.width, align: 'right' });
          }

          y += rowHeight;
        });
      } else {
        doc.text('Aucune ligne de devis', 60, y);
        y += rowHeight;
      }

      // Add a line
      y += 10;
      doc.moveTo(50, y).lineTo(550, y).stroke();
      y += 20;

      // Totals section - with right-aligned labels next to the values
      // Create a background for the totals section
      doc.rect(50, y, 500, 60).fill('#f9f9f9');  // Reduced height from 70 to 60
      doc.fillColor('#000000');

      // Define columns for labels and values - labels now positioned near values
      const valueX = 500;
      const labelWidth = 100;  // Width for the label

      // Total HT - label right-aligned before the value
      doc.fontSize(9);  // Reduced font size
      doc.font('Helvetica');  // Regular font
      doc.text('Total HT:', valueX - labelWidth - 10, y + 10, { width: labelWidth, align: 'right' });  // Right-aligned label
      doc.text(formatCurrency(totalHT), valueX - 10, y + 10, { align: 'right' });  // Right-aligned value

      // TVA - label right-aligned before the value
      doc.font('Helvetica');  // Regular font
      doc.text(`TVA (${tauxTVA}%):`, valueX - labelWidth - 10, y + 25, { width: labelWidth, align: 'right' });  // Right-aligned label
      doc.text(formatCurrency(montantTVA), valueX - 10, y + 25, { align: 'right' });  // Right-aligned value

      // Total TTC with primary color - keeping this one bold but with smaller font
      doc.fontSize(10);  // Slightly larger than other totals but still smaller than before
      doc.font('Helvetica-Bold');
      doc.fillColor(primaryColor);
      doc.text('Total TTC:', valueX - labelWidth - 10, y + 45, { width: labelWidth, align: 'right' });  // Right-aligned label
      doc.text(formatCurrency(totalTTC), valueX - 10, y + 45, { align: 'right' });  // Right-aligned value

      // Reset styles
      doc.fillColor('#000000');
      doc.font('Helvetica');

      // Update y position for next section
      y += 80;

      // Add Conditions section - more compact
      doc.fillColor(primaryColor)
         .fontSize(14)
         .font('Helvetica-Bold')
         .text('Conditions', 50, y);

      // Create a background for the conditions section
      const conditionsY = y + 20;
      doc.rect(50, conditionsY, 500, 50).fill('#f9f9f9');

      // Reset text color
      doc.fillColor('#000000');

      // Conditions de règlement
      doc.fontSize(10)
         .font('Helvetica-Bold')
         .text('Conditions de règlement :', 65, conditionsY + 10);

      doc.font('Helvetica')
         .text('45 jours fin de mois', 250, conditionsY + 10);

      // Mode de règlement
      doc.font('Helvetica-Bold')
         .text('Mode de règlement :', 65, conditionsY + 30);

      doc.font('Helvetica')
         .text('Virement bancaire', 250, conditionsY + 30);

      // Update y position
      y = conditionsY + 60;

      // Notes section
      if (devis.notes) {
        doc.text('Notes:', 50, y, { underline: true });
        doc.text(devis.notes, 50, y + 20);
        y += 60; // Add space after notes
      }

      // Add status indicator for all quote statuses below the Conditions section
      if (devis.statut) {
        console.log('Adding status indicator below Conditions section for status:', devis.statut);

        // Ensure the color is valid and properly formatted
        let validColor = primaryColor;
        let statusText = '';

        // Determine color and text based on status
        switch(devis.statut) {
          case 'ACCEPTED':
          case 'ACCEPTÉ':
            statusText = 'ACCEPTÉ';
            validColor = '#4caf50'; // Green/success color
            break;
          case 'SENT':
          case 'ENVOYÉ':
            statusText = 'ENVOYÉ';
            validColor = '#ff9800'; // Orange/warning color
            break;
          case 'DRAFT':
          case 'BROUILLON':
            statusText = 'BROUILLON';
            validColor = '#2196f3'; // Blue/info color
            break;
          case 'REJECTED':
          case 'REFUSÉ':
            statusText = 'REFUSÉ';
            validColor = '#f44336'; // Red/error color
            break;
          case 'EXPIRED':
          case 'EXPIRÉ':
            statusText = 'EXPIRÉ';
            validColor = '#9e9e9e'; // Grey color
            break;
          case 'PENDING':
          case 'EN ATTENTE':
            statusText = 'EN ATTENTE';
            validColor = '#ff9800'; // Orange/warning color
            break;
          default:
            statusText = devis.statut;
            validColor = primaryColor;
        }

        // Validate the color format
        const isValidHex = /^#?([a-f\d]{3}|[a-f\d]{6})$/i.test(validColor);
        if (!isValidHex) {
          console.warn(`Invalid hex color for status: ${validColor}, falling back to default #f57c00`);
          validColor = '#f57c00'; // Fallback to BenYounes Web orange color
        }

        // Ensure the hex color has a # prefix
        if (!validColor.startsWith('#')) {
          validColor = '#' + validColor;
        }

        // Handle 3-digit hex colors
        if (validColor.length === 4) {
          validColor = '#' + validColor[1] + validColor[1] + validColor[2] + validColor[2] + validColor[3] + validColor[3];
        }

        console.log(`Using validated color for ${statusText} status:`, validColor);

        // Add status below the Conditions section - more compact with smaller size
        doc.rect(50, y, 60, 20).fill(validColor);  // Reduced size for consistency with invoices
        doc.fillColor('#FFFFFF');
        doc.fontSize(9);  // Smaller font size for consistency with invoices
        doc.text(statusText, 50, y + 5, { width: 60, align: 'center' });  // Adjusted position and translated to French
        doc.fillColor('#000000');

        console.log(`Added ${statusText} status with color:`, validColor);

        // Update y position
        y += 30;  // Reduced for consistency with invoices

        console.log('Added status with color:', validColor);
      }

      // Add footer as part of the main content instead of at the bottom of the page
      // This prevents the creation of a second page
      y += 20; // Add some space before the footer
      doc.fontSize(8) // Smaller font size
         .text(`Devis ${devis.numéro}`, 50, y);

      // Disable automatic page breaks to ensure everything stays on one page
      console.log('Ensuring content fits on a single page');

      // Finalize the PDF
      doc.end();
    } catch (error) {
      console.error('Error generating PDF:', error);
      reject(error);
    }
  });
};

// Send quote by email
router.post("/:id/email", verifyToken, async (req, res) => {
  try {
    console.log(`Received POST /devis/${req.params.id}/email request`);
    const devis = await Devis.findById(req.params.id).populate("clientId");

    if (!devis) return res.status(404).json({ error: "Quote not found" });

    // Get email data from request body
    let { to, subject, message } = req.body;

    let recipient = to;
    if (!recipient) {
      // If no recipient is specified, use the client's email
      if (devis.clientId && devis.clientId.email) {
        recipient = devis.clientId.email;
      } else {
        return res.status(400).json({ error: "Recipient email is required" });
      }
    }

    // Handle multiple recipients
    let recipients = recipient;
    if (typeof recipient === 'string' && recipient.includes(',')) {
      recipients = recipient.split(',').map(email => email.trim()).filter(email => email);
      console.log("Multiple recipients detected:", recipients);
    }

    // Generate PDF
    const pdfBuffer = await generateQuotePDF(devis);

    // Create a nodemailer transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Get company information from parameters if available
    let companyName = 'Mon Entreprise';
    let emailSignature = 'Cordialement,\nL\'équipe de Mon Entreprise';
    let defaultCurrency = 'EUR';
    let primaryColor = '#f57c00'; // BenYounes Web orange color

    try {
      const Parametres = require('../models/ParametresModel');
      const ResponsableTemplate = require('../models/ResponsableTemplateModel');

      const params = await Parametres.findOne();
      let responsableTemplate = null;

      // Try to get responsable template settings based on the quote context
      let entrepriseId = null;

      // First, try to get enterprise ID from responsableId (if quote was created by responsable)
      if (devis.responsableId) {
        entrepriseId = devis.responsableId;
        console.log('Email: Using responsableId as entrepriseId:', entrepriseId);
      }
      // If not, try to get enterprise ID from vendeurId (get the vendeur's enterprise)
      else if (devis.vendeurId) {
        const User = require('../models/UserModel');
        const vendeur = await User.findById(devis.vendeurId);
        if (vendeur && vendeur.entrepriseId) {
          entrepriseId = vendeur.entrepriseId;
          console.log('Email: Using vendeur entrepriseId:', entrepriseId);
        }
      }

      if (entrepriseId) {
        responsableTemplate = await ResponsableTemplate.findOne({
          entrepriseId: entrepriseId
        });
        console.log('Email: Found responsable template for enterprise:', entrepriseId);
      }

      if (params) {
        companyName = params.companyName || companyName;
        emailSignature = params.emailSignature || emailSignature;
        defaultCurrency = params.defaultCurrency || 'EUR';
      }

      // Get template color for styling
      // Priority: ResponsableTemplate > TemplateSettings > Default
      if (responsableTemplate && responsableTemplate.devisTemplate && responsableTemplate.devisTemplate.color) {
        primaryColor = responsableTemplate.devisTemplate.color.replace(/^["'](.+)["']$/, '$1');
        console.log('Email: Using responsable template color:', primaryColor);
      } else if (templateSettings && templateSettings.color) {
        primaryColor = templateSettings.color.replace(/^["'](.+)["']$/, '$1');
        console.log('Email: Using admin template color:', primaryColor);
      }

      console.log('Email template settings:', {
        primaryColor,
        defaultCurrency,
        companyName,
        hasResponsableTemplate: !!responsableTemplate
      });
    } catch (err) {
      console.warn('Could not load parameters or template settings:', err.message);
    }

    // Define a currency formatter function for the email - always use DT
    const formatCurrency = (amount) => {
      if (!amount && amount !== 0) return '0';

      // Format avec des points comme séparateurs de milliers et virgule pour les décimales
      const parts = amount.toFixed(2).split('.');
      const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
      const decimalPart = parts[1];

      // Ajouter manuellement le symbole DT après le nombre
      return `${integerPart},${decimalPart} DT`;
    };

    // Calculate quote totals
    const totalHT = devis.totalHT || 0;
    const tauxTVA = devis.tauxTVA || 0;
    const montantTVA = devis.montantTVA || 0;
    const totalTTC = devis.total || 0;

    // Email options
    const mailOptions = {
      from: `"${companyName}" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: recipients,
      subject: subject || `Devis ${devis.numéro}`,
      text: (message || `Veuillez trouver ci-joint le devis ${devis.numéro}.`) + `\n\n${emailSignature}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2 style="color: ${primaryColor}; margin: 0;">Devis ${devis.numéro}</h2>
            <div style="text-align: right;">
              <p style="margin: 0;">Date de création: ${new Date(devis.dateCréation).toLocaleDateString()}</p>
            </div>
          </div>

          <p>${message || `Veuillez trouver ci-joint le devis ${devis.numéro}.`}</p>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span style="font-weight: bold;">Total HT:</span>
              <span>${formatCurrency(totalHT)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span style="font-weight: bold;">TVA (${tauxTVA}%):</span>
              <span>${formatCurrency(montantTVA)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; color: ${primaryColor}; font-weight: bold;">
              <span>Total TTC:</span>
              <span>${formatCurrency(totalTTC)}</span>
            </div>
          </div>

          <div style="margin-top: 30px;">
            <h3 style="color: ${primaryColor}; margin-bottom: 10px;">Conditions</h3>
            <div style="display: flex; margin-bottom: 5px;">
              <span style="font-weight: bold; width: 200px;">Conditions de règlement:</span>
              <span>45 jours fin de mois</span>
            </div>
            <div style="display: flex;">
              <span style="font-weight: bold; width: 200px;">Mode de règlement:</span>
              <span>Virement bancaire</span>
            </div>
          </div>

          <hr style="border: 1px solid #eee; margin: 30px 0;">
          <p style="color: #666;">${emailSignature.replace(/\n/g, '<br>')}</p>
        </div>
      `,
      attachments: [
        {
          filename: `devis-${devis.numéro}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }
      ]
    };

    // Send the email
    await transporter.sendMail(mailOptions);
    console.log("Email sent successfully to:", typeof recipients === 'object' ? recipients.join(', ') : recipients);



    res.status(200).json({
      message: "Email sent successfully",
      to: recipient,
      subject: mailOptions.subject
    });
  } catch (error) {
    console.error("Error sending email for quote:", error.message);
    res.status(500).json({ error: "Error while sending email for quote", details: error.message });
  }
});

// Prepare quote for printing
router.get("/:id/print", verifyToken, async (req, res) => {
  try {
    console.log(`Received GET /devis/${req.params.id}/print request`);
    const devis = await Devis.findById(req.params.id).populate("clientId");

    if (!devis) return res.status(404).json({ error: "Quote not found" });

    // Generate PDF directly on the server
    const pdfBuffer = await generateQuotePDF(devis);

    // Set the response headers for PDF (but not as attachment for printing)
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="devis-${devis.numéro}.pdf"`);

    // Send the PDF buffer directly to the client
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error preparing print data for quote:", error.message);
    res.status(500).json({ error: "Error while preparing print data for quote" });
  }
});

module.exports = router;