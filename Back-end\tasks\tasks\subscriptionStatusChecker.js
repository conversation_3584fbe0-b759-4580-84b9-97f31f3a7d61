/**
 * Subscription Status Checker
 * 
 * This scheduled task runs periodically to check for expired subscriptions
 * and updates their status from 'ACTIF' to 'EXPIRE' in the database.
 * 
 * It also updates the abonnementActif flag in the user document.
 */

const cron = require('node-cron');
const Abonnement = require('../models/AbonnementModel');
const User = require('../models/UserModel');

/**
 * Check for expired subscriptions and update their status
 */
async function checkExpiredSubscriptions() {
  try {
    console.log('[Subscription Checker] Checking for expired subscriptions...');
    const now = new Date();
    
    // Find all active subscriptions that have expired
    const expiredSubscriptions = await Abonnement.find({
      statut: 'ACTIF',
      dateFin: { $lt: now }
    });
    
    if (expiredSubscriptions.length === 0) {
      console.log('[Subscription Checker] No expired subscriptions found.');
      return;
    }
    
    console.log(`[Subscription Checker] Found ${expiredSubscriptions.length} expired subscriptions.`);
    
    // Update each expired subscription
    for (const subscription of expiredSubscriptions) {
      console.log(`[Subscription Checker] Updating subscription ${subscription._id} for responsable ${subscription.responsableId}`);
      
      // Update subscription status
      subscription.statut = 'EXPIRE';
      await subscription.save();
      
      // Update user's subscription status
      await User.findByIdAndUpdate(subscription.responsableId, {
        abonnementActif: false
      });
      
      console.log(`[Subscription Checker] Subscription ${subscription._id} marked as EXPIRED`);
    }
    
    console.log(`[Subscription Checker] Successfully updated ${expiredSubscriptions.length} subscriptions.`);
  } catch (error) {
    console.error('[Subscription Checker] Error checking expired subscriptions:', error);
  }
}

/**
 * Initialize the subscription checker cron job
 * Runs every minute to check for expired subscriptions
 */
function initSubscriptionChecker() {
  // Schedule the task to run every minute
  // Cron format: second(optional) minute hour day-of-month month day-of-week
  const task = cron.schedule('* * * * *', async () => {
    await checkExpiredSubscriptions();
  });
  
  console.log('[Subscription Checker] Subscription status checker initialized and running every minute.');
  
  return task;
}

module.exports = {
  initSubscriptionChecker,
  checkExpiredSubscriptions
};
