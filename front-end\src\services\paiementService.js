import api from './api';

const paiementService = {
  // Récupérer tous les paiements
  getPaiements: async () => {
    try {
      const response = await api.get('/paiement');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des paiements:', error);
      throw error.response?.data || { error: 'Erreur lors de la récupération des paiements' };
    }
  },

  // Récupérer un paiement par ID
  getPaiementById: async (id) => {
    try {
      const response = await api.get(`/paiement/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération du paiement ${id}:`, error);
      throw error.response?.data || { error: `Erreur lors de la récupération du paiement ${id}` };
    }
  },

  // Récupérer les paiements d'une facture
  getPaiementsByFacture: async (factureId) => {
    try {
      const response = await api.get(`/paiement/facture/${factureId}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération des paiements pour la facture ${factureId}:`, error);
      throw error.response?.data || { error: `Erreur lors de la récupération des paiements pour la facture ${factureId}` };
    }
  },

  // Créer un nouveau paiement
  createPaiement: async (paiementData) => {
    try {
      const response = await api.post('/paiement', paiementData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du paiement:', error);
      throw error.response?.data || { error: 'Erreur lors de la création du paiement' };
    }
  },

  // Mettre à jour un paiement
  updatePaiement: async (id, paiementData) => {
    try {
      const response = await api.put(`/paiement/${id}`, paiementData);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du paiement ${id}:`, error);
      throw error.response?.data || { error: `Erreur lors de la mise à jour du paiement ${id}` };
    }
  },

  // Supprimer un paiement
  deletePaiement: async (id) => {
    try {
      const response = await api.delete(`/paiement/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression du paiement ${id}:`, error);
      throw error.response?.data || { error: `Erreur lors de la suppression du paiement ${id}` };
    }
  },

  // Enregistrer un paiement pour une facture
  payerFacture: async (factureId, paiementData) => {
    try {
      const response = await api.post(`/paiement/payer/${factureId}`, paiementData);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors du paiement de la facture ${factureId}:`, error);
      throw error.response?.data || { error: `Erreur lors du paiement de la facture ${factureId}` };
    }
  },

  // Envoyer un reçu de paiement par email
  sendReceiptByEmail: async (paiementId) => {
    try {
      const response = await api.post(`/paiement/${paiementId}/send-receipt`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'envoi du reçu de paiement ${paiementId} par email:`, error);
      throw error.response?.data || { error: `Erreur lors de l'envoi du reçu de paiement par email` };
    }
  },

  // Générer un reçu de paiement au format PDF
  generateReceipt: async (paiementId) => {
    try {
      const response = await api.get(`/paiement/${paiementId}/receipt`, { responseType: 'blob' });
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la génération du reçu de paiement ${paiementId}:`, error);
      throw error.response?.data || { error: `Erreur lors de la génération du reçu de paiement` };
    }
  }
};

export default paiementService;
