const mongoose = require('mongoose');
const { Schema } = mongoose;

const entrepriseSchema = new Schema({
  // Informations de base
  nom: { type: String, required: true },
  raisonSociale: { type: String },
  numéroFiscal: { type: String },
  numéroTVA: { type: String },
  formeJuridique: { type: String },
  capitalSocial: { type: String },

  // Coordonnées
  adresse: { type: String },
  codePostal: { type: String },
  ville: { type: String },
  pays: { type: String, default: 'France' },
  telephone: { type: String },
  email: { type: String },
  siteWeb: { type: String },

  // Contact principal
  nomContact: { type: String },
  emailContact: { type: String },
  telephoneContact: { type: String },

  // Paramètres de facturation
  prefixeFacture: { type: String, default: 'FACT-' },
  prefixeDevis: { type: String, default: 'DEV-' },
  prochainNumeroFacture: { type: Number, default: 1 },
  prochainNumeroDevis: { type: Number, default: 1 },
  tauxTVA: { type: Number, default: 20 },
  delaiPaiement: { type: Number, default: 30 },

  // Textes personnalisés
  mentionsLegales: { type: String },
  piedPage: { type: String, default: 'Merci pour votre confiance' },

  // Informations bancaires
  nomBanque: { type: String },
  titulaireCompte: { type: String },
  iban: { type: String },
  bicSwift: { type: String },

  // Personnalisation visuelle
  couleurPrincipale: { type: String, default: '#1976D2' },
  couleurSecondaire: { type: String, default: '#F5F5F5' },
  logo: { type: String },
  signature: { type: String },

  // Préférences
  notifications: { type: Boolean, default: true },
  rappelsAutomatiques: { type: Boolean, default: true },
  theme: { type: String, default: 'light' },
  numerotationChronologique: { type: Boolean, default: false },

  // Métadonnées
  createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Entreprise', entrepriseSchema);