{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport {\n  getSortedRoutes,\n  isDynamicRoute,\n  getSortedRouteObjects,\n} from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n  eventBuildFailed,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport { hadUnsupportedValue } from './analysis/get-page-static-info'\nimport {\n  createPagesMapping,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { getParamKeys } from '../server/request/fallback-params'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../server/lib/utils'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\nimport { isPersistentCachingEnabled } from '../shared/lib/turbopack/utils'\nimport { inlineStaticEnv } from '../lib/inline-static-env'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { durationToString } from './duration-to-string'\nimport { traceGlobals } from '../trace/shared'\nimport { extractNextErrorCode } from '../lib/error-telemetry-utils'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<Redirect>\n  rewrites?:\n    | Array<ManifestRewriteRoute>\n    | {\n        beforeFiles: Array<ManifestRewriteRoute>\n        afterFiles: Array<ManifestRewriteRoute>\n        fallback: Array<ManifestRewriteRoute>\n      }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: Array<ManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\nfunction pageToRoute(page: string) {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\ninterface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol?.replace(/:$/, '') as 'http' | 'https' | undefined,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\ntype StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  progress?: {\n    run: () => void\n    clear: () => void\n  }\n): StaticWorker {\n  // Get the node options without inspect and also remove the\n  // --max-old-space-size flag as it can cause memory issues.\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n  delete nodeOptions['max-old-space-size']\n  delete nodeOptions['max_old_space_size']\n\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    forkOptions: {\n      env: { ...process.env, NODE_OPTIONS: formatNodeOptions(nodeOptions) },\n    },\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = require('../export')\n    .default as typeof import('../export').default\n\n  const pagesWorker = createStaticWorker(config)\n  const appWorker = createStaticWorker(config)\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n\n  pagesWorker.end()\n  appWorker.end()\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  isTurbopack = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n  NextBuildContext.isCompileMode = isCompileMode\n  const buildStartTime = Date.now()\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(isTurbopack),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      if (experimentalBuildMode === 'generate-env') {\n        if (isTurbopack) {\n          Log.warn('generate-env is not needed with turbopack')\n          process.exit(0)\n        }\n        Log.info('Inlining static env ...')\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n\n        Log.info('Complete')\n        await flushAllTraces()\n        teardownTraceSubscriber()\n        process.exit(0)\n      }\n\n      // when using compile mode static env isn't inlined so we\n      // need to populate in normal runtime env\n      if (isCompileMode || isGenerateMode) {\n        populateStaticEnv(config)\n      }\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo(\n        dir,\n        false\n      )\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      // For app directory, we run type checking after build. That's because\n      // we dynamically generate types for each layout and page in the app\n      // directory.\n      if (!appDir && !isCompileMode)\n        await startTypeChecking(typeCheckingOptions)\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan.traceChild('collect-pages').traceAsyncFn(() =>\n              recursiveReadDir(pagesDir, {\n                pathnameFilter: validFileMatcher.isPageFile,\n              })\n            )\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = {\n        previewModeId: crypto.randomBytes(16).toString('hex'),\n        previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n        previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n      }\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths = Boolean(process.env.NEXT_PRIVATE_APP_PATHS)\n          ? providedAppPaths\n          : await nextBuildSpan\n              .traceChild('collect-app-paths')\n              .traceAsyncFn(() =>\n                recursiveReadDir(appDir, {\n                  pathnameFilter: (absolutePath) =>\n                    validFileMatcher.isAppRouterPage(absolutePath) ||\n                    // For now we only collect the root /not-found page in the app\n                    // directory as the 404 fallback\n                    validFileMatcher.isRootNotFound(absolutePath),\n                  ignorePartFilter: (part) => part.startsWith('_'),\n                })\n              )\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!isTurbopack) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppDynamicIOEnabled = Boolean(config.experimental.dynamicIO)\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = getSortedRoutes([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const dynamicRoutes: Array<ReturnType<typeof pageToRoute>> = []\n          const staticRoutes: typeof dynamicRoutes = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(pageToRoute(route))\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      if (rewrites.beforeFiles.length === 0 && rewrites.fallback.length === 0) {\n        routesManifest.rewrites = rewrites.afterFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        )\n      } else {\n        routesManifest.rewrites = {\n          beforeFiles: rewrites.beforeFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          afterFiles: rewrites.afterFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          fallback: rewrites.fallback.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n        }\n      }\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (isTurbopack) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              bundler: 'turbopack',\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const prospectiveRenders = new Map<\n        string,\n        { page: string; originalAppPath: string }\n      >()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config)\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              dynamicIO: isAppDynamicIOEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/not-found-error'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined'\n                ) {\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            dynamicIO: isAppDynamicIOEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n                          // As PPR isn't enabled for this route, if dynamic IO\n                          // is enabled, and this is a dynamic route, we should\n                          // complete a prospective render for the route so that\n                          // we can use the fallback behavior. This lets us\n                          // check that dynamic pages won't error when they\n                          // enable PPR.\n                          else if (config.experimental.dynamicIO && isDynamic) {\n                            prospectiveRenders.set(originalAppPath, {\n                              page,\n                              originalAppPath,\n                            })\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: undefined,\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: undefined,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          // TODO: add handling for statically rendering edge\n                          // pages and allow edge with Prerender outputs\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyPrelude: undefined,\n                })\n              })\n            })\n        )\n\n        if (hadUnsupportedValue) {\n          Log.error(\n            `Invalid config value exports detected, these can cause unexpected behavior from the configs not being applied. Please fix them to continue`\n          )\n          process.exit(1)\n        }\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (!isTurbopack && (edgeRuntimeAppCount || edgeRuntimePagesCount)) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n\n                // @ts-expect-error internal field TODO: fix this, should use a separate mechanism to pass the info.\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!isTurbopack\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !isTurbopack\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (isTurbopack) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = getSortedRoutes([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/dynamicIO',\n          invocationCount: config.experimental.dynamicIO ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n        {\n          featureName: 'turbopackPersistentCaching',\n          invocationCount: isPersistentCachingEnabled(config) ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      // we don't need to inline for turbopack build as\n      // it will handle it's own caching separate of compile\n      if (isGenerateMode && !isTurbopack) {\n        Log.info('Inlining static env ...')\n\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n      }\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n          const exportApp = require('../export')\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : undefined\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                  }\n                })\n              })\n\n              // If the app does have dynamic IO enabled but does not have PPR\n              // enabled, then we need to perform a prospective render for all\n              // the dynamic pages to ensure that they won't error during\n              // rendering (due to a missing prelude).\n              for (const {\n                page,\n                originalAppPath,\n              } of prospectiveRenders.values()) {\n                defaultMap[page] = {\n                  page: originalAppPath,\n                  _ssgPath: page,\n                  _fallbackRouteParams: getParamKeys(page),\n                  // Prospective renders are only enabled for app pages.\n                  _isAppDir: true,\n                  // Prospective renders are only enabled when PPR is disabled.\n                  _isRoutePPREnabled: false,\n                  _isProspectiveRender: true,\n                  // Dynamic IO does not currently support `dynamic === 'error'`.\n                  _isDynamicError: false,\n                }\n              }\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const routes: PrerenderedRoute[] = []\n            const dynamicRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            let unknownPrerenderRoutes: PrerenderedRoute[] = []\n            let knownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                knownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            unknownPrerenderRoutes = getSortedRouteObjects(\n              unknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            knownPrerenderRoutes = getSortedRouteObjects(\n              knownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...knownPrerenderRoutes,\n              ...unknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              // TODO: check if still needed?\n              // Exclude the /_not-found route.\n              if (prerenderedRoute.pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n                continue\n              }\n\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of routes.\n                routes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of routes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n              if (route.pathname === UNDERSCORE_NOT_FOUND_ROUTE) continue\n\n              const {\n                metadata = {},\n                hasEmptyPrelude,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: meta.status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicRoutes.push({\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: undefined,\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: undefined,\n                })\n              }\n\n              for (const route of dynamicRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                if (!isAppRouteHandler && metadata?.segmentPaths) {\n                  const dynamicRoute = routesManifest.dynamicRoutes.find(\n                    (r) => r.page === page\n                  )\n                  if (!dynamicRoute) {\n                    throw new Error('Dynamic route not found')\n                  }\n\n                  dynamicRoute.prefetchSegmentDataRoutes ??= []\n                  for (const segmentPath of metadata.segmentPaths) {\n                    const result = buildPrefetchSegmentDataRoute(\n                      route.pathname,\n                      segmentPath\n                    )\n                    dynamicRoute.prefetchSegmentDataRoutes.push(result)\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode =\n                  route.fallbackMode ?? FallbackMode.NOT_FOUND\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: route.fallbackRootParams,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n        })\n\n        // We need to write the manifest with rewrites after build as it might\n        // have been modified.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (isCompileMode) {\n        Log.info(\n          `Build ran with \"compile\" mode, to finalize the build run either \"generate\" or \"generate-env\" mode as well`\n        )\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } catch (e) {\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(\n        eventBuildFailed({\n          bundler: getBundlerForTelemetry(isTurbopack),\n          errorCode: getErrorCodeForTelemetry(e),\n          durationInSeconds: Math.floor((Date.now() - buildStartTime) / 1000),\n        })\n      )\n    }\n    throw e\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    if (isTurbopack && !process.env.__NEXT_TEST_MODE) {\n      warnAboutTurbopackBuilds(loadedConfig)\n    }\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: isTurbopack,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction warnAboutTurbopackBuilds(config?: NextConfigComplete) {\n  let warningStr =\n    `Support for Turbopack builds is experimental. ` +\n    bold(\n      `We don't recommend deploying mission-critical applications to production.`\n    )\n  warningStr +=\n    '\\n\\n- ' +\n    bold(\n      'Turbopack currently always builds production sourcemaps for the browser. This will include project sourcecode if deployed to production.'\n    )\n  warningStr +=\n    '\\n- It is expected that your bundle size might be different from `next build` with webpack. This will be improved as we work towards stability.'\n\n  if (!config?.experimental.turbopackPersistentCaching) {\n    warningStr +=\n      '\\n- This build is without disk caching; subsequent builds will become faster when disk caching becomes available.'\n  }\n\n  warningStr +=\n    '\\n- When comparing output to webpack builds, make sure to first clear the Next.js cache by deleting the `.next` directory.'\n  warningStr +=\n    '\\n\\nProvide feedback for Turbopack builds at https://github.com/vercel/next.js/discussions/77721'\n\n  Log.warn(warningStr)\n}\n\nfunction getBundlerForTelemetry(isTurbopack: boolean) {\n  if (isTurbopack) {\n    return 'turbopack'\n  }\n\n  if (process.env.NEXT_RSPACK) {\n    return 'rspack'\n  }\n\n  return 'webpack'\n}\n\nfunction getErrorCodeForTelemetry(err: unknown) {\n  const code = extractNextErrorCode(err)\n  if (code != null) {\n    return code\n  }\n\n  if (err instanceof Error && 'code' in err && typeof err.code === 'string') {\n    return err.code\n  }\n\n  if (err instanceof Error) {\n    return err.name\n  }\n\n  return 'Unknown'\n}\n"], "names": ["loadEnvConfig", "bold", "yellow", "crypto", "makeRe", "existsSync", "promises", "fs", "os", "Worker", "defaultConfig", "devalue", "findUp", "nanoid", "path", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "MIDDLEWARE_FILENAME", "PAGES_DIR_ALIAS", "INSTRUMENTATION_HOOK_FILENAME", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "NEXT_RESUME_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "MATCHED_PATH_HEADER", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "FileType", "fileExists", "findPagesDir", "loadCustomRoutes", "normalizeRouteRegex", "nonNullable", "recursiveDelete", "verifyPartytownSetup", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "EXPORT_DETAIL", "EXPORT_MARKER", "IMAGES_MANIFEST", "PAGES_MANIFEST", "PHASE_PRODUCTION_BUILD", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "STATIC_STATUS_PAGES", "MIDDLEWARE_MANIFEST", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "APP_BUILD_MANIFEST", "RSC_MODULE_TYPES", "NEXT_FONT_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "UNDERSCORE_NOT_FOUND_ROUTE", "DYNAMIC_CSS_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "getSortedRoutes", "isDynamicRoute", "getSortedRouteObjects", "loadConfig", "normalizePagePath", "getPagePath", "ciEnvironment", "turborepoTraceAccess", "TurborepoAccessTraceResult", "writeTurborepoAccessTraceResult", "eventBuildOptimize", "eventCliSession", "eventBuildFeatureUsage", "eventNextPlugins", "EVENT_BUILD_FEATURE_USAGE", "eventPackageUsedInGetServerSideProps", "eventBuildCompleted", "eventBuildFailed", "Telemetry", "hadUnsupportedValue", "createPagesMapping", "getStaticInfoIncludingLayouts", "sortByPageExts", "PAGE_TYPES", "generateBuildId", "isWriteable", "Log", "createSpinner", "trace", "flushAllTraces", "setGlobal", "detectConflictingPaths", "computeFromManifest", "getJsPageSizeInKb", "printCustomRoutes", "printTreeView", "copyTracedFiles", "isReservedPage", "isAppBuiltinNotFoundPage", "collectRoutesUsingEdgeRuntime", "collectMeta", "writeBuildId", "normalizeLocalePath", "isError", "isEdgeRuntime", "recursiveCopy", "recursiveReadDir", "lockfilePatchPromise", "teardownTraceSubscriber", "getNamedRouteRegex", "getFilesInDir", "eventSwcPlugins", "normalizeAppPath", "ACTION_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "webpackBuild", "NextBuildContext", "normalizePathSep", "isAppRouteRoute", "createClientRouterFilter", "createValidFileMatcher", "startTypeChecking", "generateInterceptionRoutesRewrites", "buildDataRoute", "collectBuildTraces", "formatManifest", "recordFrameworkVersion", "updateBuildDiagnostics", "recordFetchMetrics", "getStartServerInfo", "logStartInfo", "hasCustomExportOutput", "buildCustomRoute", "traceMemoryUsage", "generateEncryptionKeyBase64", "uploadTrace", "checkIsAppPPREnabled", "checkIsRoutePPREnabled", "FallbackMode", "fallbackModeToFallbackField", "RenderingMode", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "formatNodeOptions", "getParsedNodeOptionsWithoutInspect", "InvariantError", "HTML_LIMITED_BOT_UA_RE_STRING", "buildPrefetchSegmentDataRoute", "turbopackBuild", "isPersistentCachingEnabled", "inlineStaticEnv", "populateStaticEnv", "durationToString", "traceGlobals", "extractNextErrorCode", "ALLOWED_HEADERS", "pageToRoute", "page", "routeRegex", "prefixRouteKeys", "regex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "join", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "console", "log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "writeFunctionsConfigManifest", "writeRequiredServerFilesManifest", "requiredServerFiles", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "replace", "hostname", "port", "dot", "search", "localPatterns", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "createStaticWorker", "progress", "nodeOptions", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "forkOptions", "env", "process", "NODE_OPTIONS", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "pagesWorker", "appWorker", "buildExport", "nextConfig", "silent", "outdir", "end", "getBuildId", "isGenerateMode", "build", "reactProductionProfiling", "debugOutput", "runLint", "noMangling", "appDirOnly", "isTurbopack", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "buildStartTime", "Date", "now", "loadedConfig", "undefined", "buildMode", "isTurboBuild", "String", "__NEXT_VERSION", "mappedPages", "traceFn", "turborepoAccessTraceResult", "NEXT_DEPLOYMENT_ID", "deploymentId", "exit", "info", "customRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "publicDir", "pagesDir", "app", "<PERSON><PERSON><PERSON>", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "cwd", "isCustomServer", "turboFlag", "then", "events", "envInfo", "experimentalFeatures", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "code", "Error", "cleanDistDir", "error", "flush", "buildLintEvent", "featureName", "invocationCount", "eventName", "payload", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "pathnameFilter", "isPageFile", "middlewareDetectionRegExp", "RegExp", "instrumentationHookDetectionRegExp", "rootDir", "rootPaths", "Array", "from", "some", "include", "test", "hasMiddlewareFile", "previewProps", "previewModeId", "randomBytes", "toString", "previewModeSigningKey", "previewModeEncryptionKey", "isDev", "pagesType", "PAGES", "pagePaths", "mappedAppPages", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "absolutePath", "isAppRouterPage", "isRootNotFound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "pagePath", "appPath", "add", "basePath", "totalAppPagesCount", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "hasApp404", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "hasPublicPageFile", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppDynamicIOEnabled", "dynamicIO", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "ppr", "routesManifestPath", "routesManifest", "sortedRoutes", "staticRoutes", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "dataRoutes", "i18n", "rsc", "header", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "contentTypeHeader", "suffix", "prefetchSuffix", "prefetchSegmentHeader", "prefetchSegmentSuffix", "prefetchSegmentDirSuffix", "rewriteHeaders", "pathHeader", "query<PERSON>eader", "skipMiddlewareUrlNormalize", "chain", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "buildStage", "pagesManifestPath", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "NEXT_TURBOPACK_USE_WORKER", "durationString", "event", "bundler", "durationInSeconds", "round", "serverBuildPromise", "res", "buildTraceWorker", "edgeRuntimeRoutes", "Map", "hasSsrAmpPages", "catch", "edgeBuildPromise", "getBundlerForTelemetry", "postCompileSpinner", "buildManifestPath", "appBuildManifestPath", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "prospectiveRenders", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "key", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "gzipSize", "actionManifest", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "size", "totalSize", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isInsideAppDir", "staticInfo", "runtime", "maxDuration", "pageRuntime", "client", "edgeInfo", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "dynamic", "hasStaticProps", "isAmpOnly", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "delete", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyPrelude", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "instrumentationHookEntryFiles", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "useStaticPages404", "pg", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "values", "_isProspectiveRender", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "by<PERSON><PERSON>", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "traces", "turborepoAccessTraceResults", "ssgNotFoundPaths", "serverBundle", "unlink", "hasRevalidateZero", "isAppRouteHandler", "htmlBotsRegexString", "htmlLimitedBots", "bypassFor", "type", "unknown<PERSON>rerender<PERSON><PERSON><PERSON>", "knownPrerenderRoutes", "prerenderedRoute", "metadata", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "initialStatus", "status", "initialHeaders", "renderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "NOT_FOUND", "segmentPaths", "dynamicRoute", "prefetchSegmentDataRoutes", "segmentPath", "result", "isDynamicAppRoute", "fallbackCacheControl", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "postBuildSpinner", "buildTracesSpinner", "analysisEnd", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "usages", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "distPath", "e", "errorCode", "getErrorCodeForTelemetry", "cur", "__NEXT_TEST_MODE", "warnAboutTurbopackBuilds", "mode", "projectDir", "isTurboSession", "sync", "warningStr", "turbopackPersistentCaching", "NEXT_RSPACK", "name"], "mappings": "AAOA,OAAO,mCAAkC;AAEzC,SAASA,aAAa,QAA6B,YAAW;AAC9D,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,SAAQ;AAC3B,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,QAAQ,KAAI;AACnB,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,aAAa,QAAQ,0BAAyB;AACvD,OAAOC,aAAa,6BAA4B;AAChD,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,sCAAqC;AAC5D,OAAOC,UAAU,OAAM;AACvB,SACEC,0CAA0C,EAC1CC,8BAA8B,EAC9BC,mBAAmB,EACnBC,eAAe,EACfC,6BAA6B,EAC7BC,mBAAmB,EACnBC,UAAU,EACVC,kBAAkB,EAClBC,2BAA2B,EAC3BC,0CAA0C,EAC1CC,sCAAsC,EACtCC,kCAAkC,EAClCC,mBAAmB,EACnBC,uBAAuB,EACvBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,QAAQ,EAAEC,UAAU,QAAQ,qBAAoB;AACzD,SAASC,YAAY,QAAQ,wBAAuB;AACpD,OAAOC,oBACLC,mBAAmB,QACd,4BAA2B;AAQlC,SAASC,WAAW,QAAQ,sBAAqB;AACjD,SAASC,eAAe,QAAQ,0BAAyB;AACzD,SAASC,oBAAoB,QAAQ,gCAA+B;AACpE,SACEC,aAAa,EACbC,cAAc,EACdC,wBAAwB,EACxBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,EAClBC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,kCAAkC,EAClCC,yBAAyB,EACzBC,yBAAyB,EACzBC,gCAAgC,EAChCC,0BAA0B,EAC1BC,oBAAoB,EACpBC,oCAAoC,QAC/B,0BAAyB;AAChC,SACEC,eAAe,EACfC,cAAc,EACdC,qBAAqB,QAChB,6BAA4B;AAEnC,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,WAAW,QAAQ,oBAAmB;AAC/C,YAAYC,mBAAmB,oBAAmB;AAClD,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,+BAA+B,QAC1B,2BAA0B;AAEjC,SACEC,kBAAkB,EAClBC,eAAe,EACfC,sBAAsB,EACtBC,gBAAgB,EAChBC,yBAAyB,EACzBC,oCAAoC,EACpCC,mBAAmB,EACnBC,gBAAgB,QACX,sBAAqB;AAE5B,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,mBAAmB,QAAQ,kCAAiC;AACrE,SACEC,kBAAkB,EAClBC,6BAA6B,EAC7BC,cAAc,QACT,YAAW;AAClB,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,YAAYC,SAAS,eAAc;AACnC,OAAOC,mBAAmB,YAAW;AACrC,SAASC,KAAK,EAAEC,cAAc,EAAEC,SAAS,QAAmB,WAAU;AACtE,SACEC,sBAAsB,EACtBC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,wBAAwB,EACxBC,6BAA6B,EAC7BC,WAAW,QACN,UAAS;AAIhB,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,OAAOC,aAAa,kBAAiB;AAErC,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAuB;AACrD,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,QAAO;AACrE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,aAAa,QAAQ,0BAAyB;AACvD,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SACEC,aAAa,EACbC,2BAA2B,EAC3BC,UAAU,EACVC,uBAAuB,EACvBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,mCAAmC,EACnCC,0BAA0B,EAC1BC,2BAA2B,QACtB,0CAAyC;AAChD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,gBAAgB,QAA0B,kBAAiB;AACpE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,wBAAwB,QAAQ,qCAAoC;AAC7E,SAASC,sBAAsB,QAAQ,+BAA8B;AACrE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,kCAAkC,QAAQ,+CAA8C;AAEjG,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,kBAAkB,QACb,mCAAkC;AACzC,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,6BAA4B;AAE7E,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,2BAA2B,QAAQ,+CAA8C;AAE1F,OAAOC,iBAAiB,wBAAuB;AAC/C,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,iCAAgC;AACvC,SAASC,YAAY,EAAEC,2BAA2B,QAAQ,kBAAiB;AAC3E,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,oCAAmC;AAChE,SACEC,iBAAiB,EACjBC,kCAAkC,QAC7B,sBAAqB;AAC5B,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,6BAA6B,QAAQ,oCAAmC;AAEjF,SACEC,6BAA6B,QAExB,+DAA8D;AAErE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,0BAA0B,QAAQ,gCAA+B;AAC1E,SAASC,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,oBAAmB;AACrD,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,+BAA8B;AA2HnE;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACA7I;IACAJ;IACAC;IACAE;IACAD;CACD;AAiGD,SAASgJ,YAAYC,IAAY;IAC/B,MAAMC,aAAavD,mBAAmBsD,MAAM;QAC1CE,iBAAiB;IACnB;IACA,OAAO;QACLF;QACAG,OAAO3I,oBAAoByI,WAAWG,EAAE,CAACC,MAAM;QAC/CC,WAAWL,WAAWK,SAAS;QAC/BC,YAAYN,WAAWM,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWtK,KAAKuK,IAAI,CAACF,SAAS;IACpC,IAAI1G,cAAc6G,IAAI,IAAI,CAAC7G,cAAc8G,cAAc,EAAE;QACvD,MAAMC,WAAWnL,WAAW+K;QAE5B,IAAI,CAACI,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBC,QAAQC,GAAG,CACT,GAAG7F,IAAI8F,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOR;AACT;AAEA,eAAeS,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMxL,GAAGyL,SAAS,CAACF,UAAUC,SAAS;AACxC;AAEA,SAASE,aAAaH,QAAgB;IACpC,OAAOvL,GAAG2L,QAAQ,CAACJ,UAAU;AAC/B;AAEA,eAAeK,cACbL,QAAgB,EAChBM,QAAW;IAEX,MAAMP,cAAcC,UAAUnD,eAAeyD;AAC/C;AAEA,eAAeC,aAA+BP,QAAgB;IAC5D,OAAOQ,KAAKC,KAAK,CAAC,MAAMN,aAAaH;AACvC;AAEA,eAAeU,uBACbrB,OAAe,EACfiB,QAAyC;IAEzC,MAAMD,cAAcrL,KAAKuK,IAAI,CAACF,SAASrI,qBAAqBsJ;AAC9D;AAEA,eAAeK,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACPxB,OAAO,EACPyB,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKxG,oBAAoBwG,OAAOT,SAASU,QAAQ;WAC7DP,OAAOQ,IAAI,CAACb,kBAAkBc,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAE/M,QACtDkM,UACA,iDAAiD,CAAC;IAEpD,MAAMhB,cACJ/K,KAAKuK,IAAI,CAACF,SAAS3I,0BAA0BmK,SAAS,oBACtDe;AAEJ;AAmBA,eAAeC,6BACbxC,OAAe,EACfiB,QAAiC;IAEjC,MAAMD,cACJrL,KAAKuK,IAAI,CAACF,SAASlI,kBAAkBa,4BACrCsI;AAEJ;AAWA,eAAewB,iCACbzC,OAAe,EACf0C,mBAAgD;IAEhD,MAAM1B,cACJrL,KAAKuK,IAAI,CAACF,SAASjI,wBACnB2K;AAEJ;AAEA,eAAeC,oBACb3C,OAAe,EACf4C,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGhB,GAAG,CAAC,CAACiB;YAExDA;eAF+D;YACzE,iEAAiE;YACjEC,QAAQ,GAAED,cAAAA,EAAEC,QAAQ,qBAAVD,YAAYE,OAAO,CAAC,MAAM;YACpCC,UAAUpO,OAAOiO,EAAEG,QAAQ,EAAEzD,MAAM;YACnC0D,MAAMJ,EAAEI,IAAI;YACZnB,UAAUlN,OAAOiO,EAAEf,QAAQ,IAAI,MAAM;gBAAEoB,KAAK;YAAK,GAAG3D,MAAM;YAC1D4D,QAAQN,EAAEM,MAAM;QAClB;;IAEA,oEAAoE;IACpE,IAAIZ,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBa,aAAa,EAAE;QACjCZ,OAAOY,aAAa,GAAGb,OAAOC,MAAM,CAACY,aAAa,CAACxB,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEf,UAAUlN,OAAOiO,EAAEf,QAAQ,IAAI,MAAM;oBAAEoB,KAAK;gBAAK,GAAG3D,MAAM;gBAC1D4D,QAAQN,EAAEM,MAAM;YAClB,CAAA;IACF;IAEA,MAAMxC,cAAcrL,KAAKuK,IAAI,CAACF,SAASxI,kBAAkB;QACvDkM,SAAS;QACTb;IACF;AACF;AAEA,MAAMc,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB7D,OAAe,EACf8D,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BtB,mBAAgD,EAChDuB,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAMpJ,gBACJ,kFAAkF;QAClFsH,oBAAoB4B,MAAM,EAC1BtE,SACA8D,SAASW,KAAK,EACdV,sBACAC,uBACAtB,oBAAoBE,MAAM,EAC1BqB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMM,QAAQ;eACdhC,oBAAoBiC,KAAK;YAC5BhP,KAAKuK,IAAI,CAACwC,oBAAoBE,MAAM,CAAC5C,OAAO,EAAEjI;eAC3CsM,eAAeO,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQnP,IAAI,GAAG;oBACtDkP,IAAIG,IAAI,CAACF,QAAQnP,IAAI;gBACvB;gBACA,OAAOkP;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAMlE,WAAWhL,KAAKuK,IAAI,CAACwC,oBAAoB4B,MAAM,EAAEI;YACvD,MAAMO,aAAatP,KAAKuK,IAAI,CAC1BF,SACA2D,sBACAhO,KAAKuP,QAAQ,CAAClB,uBAAuBrD;YAEvC,MAAMvL,GAAG+P,KAAK,CAACxP,KAAKyP,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMjQ,GAAGkQ,QAAQ,CAAC3E,UAAUsE;QAC9B;QAEA,IAAIf,mBAAmB;YACrB,MAAMqB,mBAAmB5P,KAAKuK,IAAI,CAChCF,SACA2D,sBACAhO,KAAKuP,QAAQ,CAAClB,uBAAuBhE,UACrClI,kBACA;YAGF,MAAM1C,GAAG+P,KAAK,CAACxP,KAAKyP,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMjQ,GAAGkQ,QAAQ,CACf3P,KAAKuK,IAAI,CAACF,SAASlI,kBAAkB,kBACrCyN;QAEJ;QAEA,MAAM1J,cACJlG,KAAKuK,IAAI,CAACF,SAASlI,kBAAkB,UACrCnC,KAAKuK,IAAI,CACPF,SACA2D,sBACAhO,KAAKuP,QAAQ,CAAClB,uBAAuBhE,UACrClI,kBACA,UAEF;YAAE0N,WAAW;QAAK;QAEpB,IAAIlB,QAAQ;YACV,MAAMmB,oBAAoB9P,KAAKuK,IAAI,CAACF,SAASlI,kBAAkB;YAC/D,IAAI5C,WAAWuQ,oBAAoB;gBACjC,MAAM5J,cACJ4J,mBACA9P,KAAKuK,IAAI,CACPF,SACA2D,sBACAhO,KAAKuP,QAAQ,CAAClB,uBAAuBhE,UACrClI,kBACA,QAEF;oBAAE0N,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmB9C,MAA0B;IACpD,IACEA,OAAO+C,YAAY,CAACC,IAAI,IACxBhD,OAAO+C,YAAY,CAACC,IAAI,KAAKrQ,cAAcoQ,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAOhD,OAAO+C,YAAY,CAACC,IAAI;IACjC;IAEA,IAAIhD,OAAO+C,YAAY,CAACE,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACpD,OAAO+C,YAAY,CAACC,IAAI,IAAI,GAAGE,KAAKG,KAAK,CAAC5Q,GAAG6Q,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAItD,OAAO+C,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAOhD,OAAO+C,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMO,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAED,OAAO,SAASC,mBACd3D,MAA0B,EAC1B4D,QAGC;IAED,2DAA2D;IAC3D,2DAA2D;IAC3D,MAAMC,cAAc/H;IACpB,OAAO+H,WAAW,CAAC,qBAAqB;IACxC,OAAOA,WAAW,CAAC,qBAAqB;IAExC,OAAO,IAAInR,OAAO6Q,kBAAkB;QAClCO,QAAQhM;QACRiM,YAAYjB,mBAAmB9C;QAC/BgE,YAAY;YACVJ,4BAAAA,SAAUK,GAAG;QACf;QACAC,iBAAiB;YACfN,4BAAAA,SAAUO,KAAK;QACjB;QACAC,aAAa;YACXC,KAAK;gBAAE,GAAGC,QAAQD,GAAG;gBAAEE,cAAc1I,kBAAkBgI;YAAa;QACtE;QACAW,qBAAqBxE,OAAO+C,YAAY,CAAC0B,aAAa;QACtDC,gBAAgBhB;IAClB;AACF;AAEA,eAAeiB,uBACb3E,MAA0B,EAC1B4E,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpB7D,aAAmB;IAEnB,MAAM8D,YAAYvB,QAAQ,aACvBwB,OAAO;IAEV,MAAMC,cAActB,mBAAmB3D;IACvC,MAAMkF,YAAYvB,mBAAmB3D;IAErC,MAAM+E,UACJH,KACA;QACEO,aAAa;QACbC,YAAYpF;QACZ6E;QACAQ,QAAQ;QACRC,QAAQvS,KAAKuK,IAAI,CAACsH,KAAKE;QACvBf,YAAYjB,mBAAmB9C;IACjC,GACAiB;IAGFgE,YAAYM,GAAG;IACfL,UAAUK,GAAG;AACf;AAEA,eAAeC,WACbC,cAAuB,EACvBrI,OAAe,EACf6D,aAAmB,EACnBjB,MAA0B;IAE1B,IAAIyF,gBAAgB;QAClB,OAAO,MAAMjT,GAAG2L,QAAQ,CAACpL,KAAKuK,IAAI,CAACF,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM6D,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAMhK,gBAAgBoI,OAAOpI,eAAe,EAAE9E;AAChE;AAEA,eAAe,eAAe4S,MAC5Bd,GAAW,EACXe,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,cAAc,KAAK,EACnBC,qBAA0E,EAC1EC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMR,iBAAiBQ,0BAA0B;IACjD9L,iBAAiBgM,aAAa,GAAGA;IACjC,MAAMC,iBAAiBC,KAAKC,GAAG;IAE/B,IAAIC;IACJ,IAAI;QACF,MAAMtF,gBAAgBjJ,MAAM,cAAcwO,WAAW;YACnDC,WAAWR;YACXS,cAAcC,OAAOX;YACrBlF,SAASwD,QAAQD,GAAG,CAACuC,cAAc;QACrC;QAEAzM,iBAAiB8G,aAAa,GAAGA;QACjC9G,iBAAiByK,GAAG,GAAGA;QACvBzK,iBAAiB4L,UAAU,GAAGA;QAC9B5L,iBAAiBwL,wBAAwB,GAAGA;QAC5CxL,iBAAiB2L,UAAU,GAAGA;QAE9B,MAAM7E,cAAcW,YAAY,CAAC;gBA+XXiF;YA9XpB,4EAA4E;YAC5E,MAAM,EAAEpF,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACXmF,OAAO,CAAC,IAAM7U,cAAc2S,KAAK,OAAO9M;YAC3CqC,iBAAiBsH,cAAc,GAAGA;YAElC,MAAMsF,6BAA6B,IAAInQ;YACvC,MAAMoJ,SAA6B,MAAMiB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZjL,qBACE,IACEJ,WAAWzB,wBAAwB8P,KAAK;wBACtC,sCAAsC;wBACtCS,QAAQ;wBACRM;oBACF,IACFoB;YAGNR,eAAevG;YAEfsE,QAAQD,GAAG,CAAC2C,kBAAkB,GAAGhH,OAAOiH,YAAY,IAAI;YACxD9M,iBAAiB6F,MAAM,GAAGA;YAE1B,IAAI8E,eAAe;YACnB,IAAI5J,sBAAsB8E,SAAS;gBACjC8E,eAAe9E,OAAO5C,OAAO;gBAC7B4C,OAAO5C,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUrK,KAAKuK,IAAI,CAACsH,KAAK5E,OAAO5C,OAAO;YAC7CjD,iBAAiBiD,OAAO,GAAGA;YAC3BlF,UAAU,SAASpD;YACnBoD,UAAU,WAAWkF;YAErB,MAAMwB,UAAU,MAAM4G,WACpBC,gBACArI,SACA6D,eACAjB;YAEF7F,iBAAiByE,OAAO,GAAGA;YAE3B,IAAIqH,0BAA0B,gBAAgB;gBAC5C,IAAID,aAAa;oBACflO,IAAI+F,IAAI,CAAC;oBACTyG,QAAQ4C,IAAI,CAAC;gBACf;gBACApP,IAAIqP,IAAI,CAAC;gBACT,MAAMlG,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMxF,gBAAgB;wBACpBgB;wBACA4C;oBACF;gBACF;gBAEFlI,IAAIqP,IAAI,CAAC;gBACT,MAAMlP;gBACNmB;gBACAkL,QAAQ4C,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,yCAAyC;YACzC,IAAIf,iBAAiBV,gBAAgB;gBACnCpJ,kBAAkB2D;YACpB;YAEA,MAAMoH,eAA6B,MAAMnG,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAM1N,iBAAiB8L;YAEvC,MAAM,EAAEqH,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;YACzC,MAAMI,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9C1N,iBAAiByN,WAAW,GAAGA;YAC/BzN,iBAAiB2N,gBAAgB,GAAG9H,OAAO+H,iBAAiB;YAC5D5N,iBAAiB6N,iBAAiB,GAAGhI,OAAOiI,kBAAkB;YAE9D,MAAM5K,WAAWF,YAAYC;YAE7B,MAAM8K,YAAY,IAAI5Q,UAAU;gBAAE8F;YAAQ;YAE1ClF,UAAU,aAAagQ;YAEvB,MAAMC,YAAYpV,KAAKuK,IAAI,CAACsH,KAAK;YACjC,MAAM,EAAEwD,QAAQ,EAAE1G,MAAM,EAAE,GAAGzN,aAAa2Q;YAC1CzK,iBAAiBiO,QAAQ,GAAGA;YAC5BjO,iBAAiBuH,MAAM,GAAGA;YAE1B,MAAMmD,qBAA6C;gBACjDwD,KAAK,OAAO3G,WAAW;gBACvBG,OAAO,OAAOuG,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAME,gBAAgB,MAAMjN,4BAA4B;gBACtDkN,SAAS;gBACTnL;YACF;YACAjD,iBAAiBmO,aAAa,GAAGA;YAEjC,MAAME,WAAWzV,KACduP,QAAQ,CAACsC,KAAKwD,YAAY1G,UAAU,IACpC+G,UAAU,CAAC;YACd,MAAMC,eAAepW,WAAW6V;YAEhCD,UAAUS,MAAM,CACd5R,gBAAgB6N,KAAK5E,QAAQ;gBAC3B4I,gBAAgB;gBAChBC,YAAY;gBACZL;gBACAM,YAAY,CAAC,CAAE,MAAMjW,OAAO,YAAY;oBAAEkW,KAAKnE;gBAAI;gBACnDoE,gBAAgB;gBAChBC,WAAW;gBACXb,UAAU,CAAC,CAACA;gBACZ1G,QAAQ,CAAC,CAACA;YACZ;YAGFzK,iBAAiBlE,KAAK0Q,OAAO,CAACmB,MAAMsE,IAAI,CAAC,CAACC,SACxCjB,UAAUS,MAAM,CAACQ;YAGnB5P,gBAAgBxG,KAAK0Q,OAAO,CAACmB,MAAM5E,QAAQkJ,IAAI,CAAC,CAACC,SAC/CjB,UAAUS,MAAM,CAACQ;YAGnB,qDAAqD;YACrD,MAAM,EAAEC,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMrO,mBAC9C4J,KACA;YAEF3J,aAAa;gBACXqO,YAAY;gBACZC,QAAQ;gBACRH;gBACAC;YACF;YAEA,MAAMG,eAAeC,QAAQzJ,OAAO0J,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgB3D;YAEpC,MAAMgE,sBAA+D;gBACnEjF;gBACAlD;gBACA0G;gBACAvC;gBACA+D;gBACAJ;gBACAtB;gBACAjH;gBACAjB;gBACA3C;YACF;YAEA,MAAMyM,iBAAiB,MAAM7I,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMpP,GAAG+P,KAAK,CAACnF,SAAS;wBAAEqF,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOsH,KAAK;oBACZ,IAAIhR,QAAQgR,QAAQA,IAAIC,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMjS,YAAYuF,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAI6M,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIjK,OAAOkK,YAAY,IAAI,CAACzE,gBAAgB;gBAC1C,MAAMpR,gBAAgB+I,SAAS;YACjC;YAEA,sEAAsE;YACtE,oEAAoE;YACpE,aAAa;YACb,IAAI,CAACsE,UAAU,CAACyE,eACd,MAAM3L,kBAAkBqP;YAE1B,IAAInI,UAAU,mBAAmB1B,QAAQ;gBACvClI,IAAIqS,KAAK,CACP;gBAEF,MAAMjC,UAAUkC,KAAK;gBACrB9F,QAAQ4C,IAAI,CAAC;YACf;YAEA,MAAMmD,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBX,aAAa,IAAI;YACpC;YACA1B,UAAUS,MAAM,CAAC;gBACf6B,WAAWtT;gBACXuT,SAASJ;YACX;YAEA,MAAMK,mBAAmBnQ,uBACvByF,OAAO2K,cAAc,EACrBjJ;YAGF,MAAMkJ,oBAA8BrM,KAAKC,KAAK,CAC5C8F,QAAQD,GAAG,CAACwG,uBAAuB,IAAI;YAGzC,IAAIC,aAAarB,QAAQnF,QAAQD,GAAG,CAACwG,uBAAuB,IACxDD,oBACA,CAAC7E,cAAcqC,WACb,MAAMnH,cAAcU,UAAU,CAAC,iBAAiBC,YAAY,CAAC,IAC3D1I,iBAAiBkP,UAAU;oBACzB2C,gBAAgBL,iBAAiBM,UAAU;gBAC7C,MAEF,EAAE;YAER,MAAMC,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAEhY,oBAAoB,MAAM,EAAE8M,OAAO2K,cAAc,CAACrN,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAM6N,qCAAqC,IAAID,OAC7C,CAAC,CAAC,EAAE9X,8BAA8B,MAAM,EAAE4M,OAAO2K,cAAc,CAACrN,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAM8N,UAAUrY,KAAKuK,IAAI,CAAE8K,YAAY1G,QAAU;YACjD,MAAMS,WAAW;gBACf8I;gBACAE;aACD;YAED,MAAME,YAAYC,MAAMC,IAAI,CAAC,MAAMjS,cAAc8R,UAC9CjM,MAAM,CAAC,CAAC2C,OAASK,SAASqJ,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAAC5J,QACzDpC,IAAI,CAAChI,eAAesI,OAAO2K,cAAc,GACzCtL,GAAG,CAAC,CAACyC,OAAS/O,KAAKuK,IAAI,CAAC8N,SAAStJ,MAAMtB,OAAO,CAACoE,KAAK;YAEvD,MAAMrD,yBAAyB8J,UAAUG,IAAI,CAAC,CAAClL,IAC7CA,EAAE6B,QAAQ,CAAC/O;YAEb,MAAMuY,oBAAoBN,UAAUG,IAAI,CAAC,CAAClL,IACxCA,EAAE6B,QAAQ,CAACjP;YAGbiH,iBAAiBoH,sBAAsB,GAAGA;YAE1C,MAAMqK,eAAkC;gBACtCC,eAAezZ,OAAO0Z,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBAC/CC,uBAAuB5Z,OAAO0Z,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBACvDE,0BAA0B7Z,OAAO0Z,WAAW,CAAC,IAAIC,QAAQ,CAAC;YAC5D;YACA5R,iBAAiByR,YAAY,GAAGA;YAEhC,MAAM/E,cAAc,MAAM5F,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZpK,mBAAmB;oBACjB0U,OAAO;oBACPvB,gBAAgB3K,OAAO2K,cAAc;oBACrCwB,WAAWxU,WAAWyU,KAAK;oBAC3BC,WAAWvB;oBACX1C;oBACA1G;gBACF;YAEJvH,iBAAiB0M,WAAW,GAAGA;YAE/B,IAAIyF;YACJ,IAAInL;YAEJ,IAAIO,QAAQ;gBACV,MAAM6K,mBAA6BhO,KAAKC,KAAK,CAC3C8F,QAAQD,GAAG,CAACmI,sBAAsB,IAAI;gBAGxC,IAAIC,WAAWhD,QAAQnF,QAAQD,GAAG,CAACmI,sBAAsB,IACrDD,mBACA,MAAMtL,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC,IACZ1I,iBAAiBwI,QAAQ;wBACvBqJ,gBAAgB,CAAC2B,eACfhC,iBAAiBiC,eAAe,CAACD,iBACjC,8DAA8D;4BAC9D,gCAAgC;4BAChChC,iBAAiBkC,cAAc,CAACF;wBAClCG,kBAAkB,CAACC,OAASA,KAAKrE,UAAU,CAAC;oBAC9C;gBAGR6D,iBAAiB,MAAMrL,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZpK,mBAAmB;wBACjB6U,WAAWI;wBACXP,OAAO;wBACPC,WAAWxU,WAAWoV,GAAG;wBACzBpC,gBAAgB3K,OAAO2K,cAAc;wBACrCvC;wBACA1G;oBACF;gBAGJvH,iBAAiBmS,cAAc,GAAGA;YACpC;YAEA,MAAMU,kBAAkB,MAAMxV,mBAAmB;gBAC/C0U,OAAO;gBACPvB,gBAAgB3K,OAAO2K,cAAc;gBACrC0B,WAAWhB;gBACXc,WAAWxU,WAAWsV,IAAI;gBAC1B7E,UAAUA;gBACV1G;YACF;YACAvH,iBAAiB6S,eAAe,GAAGA;YAEnC,MAAME,gBAAgBlO,OAAOQ,IAAI,CAACqH;YAElC,MAAMsG,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAIrO;YACxB,IAAIuN,gBAAgB;gBAClBnL,uBAAuBnC,OAAOQ,IAAI,CAAC8M;gBACnC,KAAK,MAAMe,UAAUlM,qBAAsB;oBACzC,MAAMmM,uBAAuB9T,iBAAiB6T;oBAC9C,MAAME,WAAW1G,WAAW,CAACyG,qBAAqB;oBAClD,IAAIC,UAAU;wBACZ,MAAMC,UAAUlB,cAAc,CAACe,OAAO;wBACtCF,wBAAwB/K,IAAI,CAAC;4BAC3BmL,SAAS/M,OAAO,CAAC,uBAAuB;4BACxCgN,QAAQhN,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACA4M,YAAYK,GAAG,CAACH;gBAClB;YACF;YAEA,MAAMb,WAAWnB,MAAMC,IAAI,CAAC6B;YAC5B,2DAA2D;YAC3D9F,SAASG,WAAW,CAACrF,IAAI,IACpB3H,mCAAmCgS,UAAUzM,OAAO0N,QAAQ;YAGjEvT,iBAAiBmN,QAAQ,GAAGA;YAE5B,MAAMqG,qBAAqBlB,SAAS5E,MAAM;YAE1C,MAAM3G,WAAW;gBACfW,OAAOqL;gBACP7E,KAAKoE,SAAS5E,MAAM,GAAG,IAAI4E,WAAWjG;YACxC;YAEA,6DAA6D;YAC7D,IAAI,CAACR,aAAa;gBAChB,MAAM4H,yBAAyBT,wBAAwBtF,MAAM;gBAC7D,IAAIyE,kBAAkBsB,yBAAyB,GAAG;oBAChD9V,IAAIqS,KAAK,CACP,CAAC,6BAA6B,EAC5ByD,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAACL,UAAUC,QAAQ,IAAIL,wBAAyB;wBACzDrV,IAAIqS,KAAK,CAAC,CAAC,GAAG,EAAEoD,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAMtF,UAAUkC,KAAK;oBACrB9F,QAAQ4C,IAAI,CAAC;gBACf;YACF;YAEA,MAAM2G,yBAAmC,EAAE;YAC3C,MAAMC,eAAcjH,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB4B,UAAU,CAACtV;YACpD,MAAM4a,YAAY,CAAC,EAACzB,kCAAAA,cAAgB,CAACtW,iCAAiC;YACtE,MAAMgY,qBACJnH,WAAW,CAAC,UAAU,CAAC4B,UAAU,CAACtV;YAEpC,IAAIuV,cAAc;gBAChB,MAAMuF,6BAA6B3b,WACjCS,KAAKuK,IAAI,CAAC6K,WAAW;gBAEvB,IAAI8F,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAIhE,MAAMhX,iCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAMgO,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMjF,QAAQkK,YAAa;oBAC9B,MAAMqH,oBAAoB,MAAMla,WAC9BjB,KAAKuK,IAAI,CAAC6K,WAAWxL,SAAS,MAAM,WAAWA,OAC/C5I,SAASoa,IAAI;oBAEf,IAAID,mBAAmB;wBACrBL,uBAAuBzL,IAAI,CAACzF;oBAC9B;gBACF;gBAEA,MAAMyR,iBAAiBP,uBAAuBhG,MAAM;gBAEpD,IAAIuG,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAInE,MACR,CAAC,gCAAgC,EAC/BmE,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEP,uBAAuBvQ,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAM+Q,sBAAsBnN,SAASW,KAAK,CAAC1C,MAAM,CAAC,CAACxC;gBACjD,OACEA,KAAK2R,KAAK,CAAC,iCAAiCvb,KAAKyP,OAAO,CAAC7F,UAAU;YAEvE;YAEA,IAAI0R,oBAAoBxG,MAAM,EAAE;gBAC9B/P,IAAI+F,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5FwQ,oBAAoB/Q,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAMiR,0BAA0B;gBAAC;aAAS,CAAClP,GAAG,CAAC,CAACiB,IAC9CN,OAAO0N,QAAQ,GAAG,GAAG1N,OAAO0N,QAAQ,GAAGpN,GAAG,GAAGA;YAG/C,MAAMkO,wBAAwB/E,QAAQzJ,OAAO+C,YAAY,CAAC0L,SAAS;YACnE,MAAMC,0BAA0BjF,QAC9BzJ,OAAO+C,YAAY,CAAC4L,cAAc;YAEpC,MAAMC,kBAAkBrT,qBAAqByE,OAAO+C,YAAY,CAAC8L,GAAG;YAEpE,MAAMC,qBAAqB/b,KAAKuK,IAAI,CAACF,SAASnI;YAC9C,MAAM8Z,iBAAiC9N,cACpCU,UAAU,CAAC,4BACXmF,OAAO,CAAC;gBACP,MAAMkI,eAAe5Y,gBAAgB;uBAChC8K,SAASW,KAAK;uBACbX,SAASmH,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAM5I,gBAAuD,EAAE;gBAC/D,MAAMwP,eAAqC,EAAE;gBAE7C,KAAK,MAAM3P,SAAS0P,aAAc;oBAChC,IAAI3Y,eAAeiJ,QAAQ;wBACzBG,cAAc2C,IAAI,CAAC1F,YAAY4C;oBACjC,OAAO,IAAI,CAAC7G,eAAe6G,QAAQ;wBACjC2P,aAAa7M,IAAI,CAAC1F,YAAY4C;oBAChC;gBACF;gBAEA,OAAO;oBACLwB,SAAS;oBACToO,UAAU;oBACVC,eAAe,CAAC,CAACnP,OAAO+C,YAAY,CAACqM,mBAAmB;oBACxD1B,UAAU1N,OAAO0N,QAAQ;oBACzBnG,WAAWA,UAAUlI,GAAG,CAAC,CAACgQ,IACxBlU,iBAAiB,YAAYkU,GAAGd;oBAElClH,SAASA,QAAQhI,GAAG,CAAC,CAACgQ,IAAMlU,iBAAiB,UAAUkU;oBACvD5P;oBACAwP;oBACAK,YAAY,EAAE;oBACdC,MAAMvP,OAAOuP,IAAI,IAAI/I;oBACrBgJ,KAAK;wBACHC,QAAQ9V;wBACR,yFAAyF;wBACzF,4DAA4D;wBAC5D+V,YAAY,GAAG/V,WAAW,EAAE,EAAEE,8BAA8B,EAAE,EAAEH,4BAA4B,EAAE,EAAEK,qCAAqC;wBACrI4V,gBAAgBjW;wBAChBkW,mBAAmB9V;wBACnB+V,mBAAmBjW;wBACnBkW,QAAQxc;wBACRyc,gBAAgB1c;wBAChB2c,uBAAuBjW;wBACvBkW,uBAAuBnc;wBACvBoc,0BAA0Brc;oBAC5B;oBACAsc,gBAAgB;wBACdC,YAAYpW;wBACZqW,aAAapW;oBACf;oBACAqW,4BAA4BtQ,OAAOsQ,0BAA0B;oBAC7DzB,KAAKD,kBACD;wBACE2B,OAAO;4BACLlJ,SAAS;gCACP,CAAC9T,mBAAmB,EAAE;4BACxB;wBACF;oBACF,IACAiT;gBACN;YACF;YAEF,IAAIc,SAASG,WAAW,CAACI,MAAM,KAAK,KAAKP,SAASK,QAAQ,CAACE,MAAM,KAAK,GAAG;gBACvEkH,eAAezH,QAAQ,GAAGA,SAASI,UAAU,CAACrI,GAAG,CAAC,CAACgQ,IACjDlU,iBAAiB,WAAWkU;YAEhC,OAAO;gBACLN,eAAezH,QAAQ,GAAG;oBACxBG,aAAaH,SAASG,WAAW,CAACpI,GAAG,CAAC,CAACgQ,IACrClU,iBAAiB,WAAWkU;oBAE9B3H,YAAYJ,SAASI,UAAU,CAACrI,GAAG,CAAC,CAACgQ,IACnClU,iBAAiB,WAAWkU;oBAE9B1H,UAAUL,SAASK,QAAQ,CAACtI,GAAG,CAAC,CAACgQ,IAC/BlU,iBAAiB,WAAWkU;gBAEhC;YACF;YACA,IAAImB;YAIJ,IAAIxQ,OAAO+C,YAAY,CAAC0N,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAAC1Q,CAAAA,OAAOiI,kBAAkB,IAAI,EAAE,AAAD,EAAG9I,MAAM,CACnE,CAACkQ,IAAW,CAACA,EAAEsB,QAAQ;gBAEzBH,sBAAsBlW,yBACpB;uBAAImS;iBAAS,EACbzM,OAAO+C,YAAY,CAAC6N,2BAA2B,GAC3CF,uBACA,EAAE,EACN1Q,OAAO+C,YAAY,CAAC8N,6BAA6B;gBAEnD1W,iBAAiBqW,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAM1S,cACJ/K,KAAKuK,IAAI,CAACF,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAMvC,uBAAuByJ,QAAQD,GAAG,CAACuC,cAAc;YACvD,MAAM9L,uBAAuB;gBAC3BgW,YAAY;YACd;YAEA,MAAM1P,wBAAwBpB,OAAOoB,qBAAqB,IAAIwD;YAE9D,MAAMmM,oBAAoBhe,KAAKuK,IAAI,CACjCF,SACAlI,kBACAL;YAGF,IAAImc;YACJ,IAAIC,qBAA+CzK;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAM0K,iBACJlR,OAAO+C,YAAY,CAACoO,kBAAkB,IACrCnR,OAAO+C,YAAY,CAACoO,kBAAkB,KAAK3K,aAC1C,CAACxG,OAAOoR,OAAO;YACnB,MAAMC,6BACJrR,OAAO+C,YAAY,CAACuO,sBAAsB;YAC5C,MAAMC,qCACJvR,OAAO+C,YAAY,CAACyO,yBAAyB,IAC5CxR,OAAO+C,YAAY,CAACyO,yBAAyB,KAAKhL,aACjDL;YAEJlF,cAAcwQ,YAAY,CACxB,6BACA9K,OAAO,CAAC,CAAC3G,OAAOoR,OAAO;YAEzBnQ,cAAcwQ,YAAY,CAAC,oBAAoB9K,OAAOuK;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAItH,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAnS,IAAIqP,IAAI,CAAC;YACT/L,iBAAiB,kBAAkB6F;YAEnC,MAAMnG,uBAAuB;gBAC3BgW,YAAY;gBACZY,cAAc;oBACZR,gBAAgBvK,OAAOuK;gBACzB;YACF;YAEA,IAAIS,kBAAkBC,QAAQnO,OAAO;YACrC,IAAI,CAACgC,gBAAgB;gBACnB,IAAIO,aAAa;oBACf,MAAM,EACJ6L,UAAUC,gBAAgB,EAC1BH,iBAAiBrR,CAAC,EAClB,GAAGyR,MACJ,GAAG,MAAM7V,eACRoI,QAAQD,GAAG,CAAC2N,yBAAyB,KAAKxL,aACxClC,QAAQD,GAAG,CAAC2N,yBAAyB,KAAK;oBAE9CL,kBAAkBrR;oBAClBlF,iBAAiB,kBAAkB6F;oBAEnC+P,oBAAoBe,KAAKf,iBAAiB;oBAE1C,MAAMiB,iBAAiB3V,iBAAiBwV;oBACxCha,IAAIoa,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;oBAEtD/J,UAAUS,MAAM,CACdvR,oBAAoB0T,YAAY;wBAC9BqH,SAAS;wBACTC,mBAAmBlP,KAAKmP,KAAK,CAACP;wBAC9BnE;oBACF;gBAEJ,OAAO;oBACL,IACE0D,8BACAE,oCACA;wBACA,IAAIa,oBAAoB;wBAExB,MAAMtX,uBAAuB;4BAC3BgW,YAAY;wBACd;wBAEA,MAAMwB,qBAAqBpY,aAAagX,gBAAgB;4BACtD;yBACD,EAAEhI,IAAI,CAAC,CAACqJ;4BACPnX,iBAAiB,+BAA+B6F;4BAChD+P,oBAAoBuB,IAAIvB,iBAAiB;4BACzCoB,qBAAqBG,IAAIV,QAAQ;4BAEjC,IAAIN,oCAAoC;gCACtC,MAAMiB,mBAAmB,IAAI9f,OAC3B8Q,QAAQC,OAAO,CAAC,2BAChB;oCACEM,YAAY;oCACZW,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGFuM,qBAAqBuB,iBAClB7X,kBAAkB,CAAC;oCAClBiK;oCACA5E;oCACA5C;oCACA,+CAA+C;oCAC/CqV,mBAAmB9Z,8BAA8B,IAAI+Z;oCACrDlR,aAAa,EAAE;oCACfmR,gBAAgB;oCAChB3B;oCACA5P;gCACF,GACCwR,KAAK,CAAC,CAAC7I;oCACNrM,QAAQyM,KAAK,CAACJ;oCACdzF,QAAQ4C,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAACmK,4BAA4B;4BAC/B,MAAMiB;4BACN,MAAMxX,uBAAuB;gCAC3BgW,YAAY;4BACd;wBACF;wBAEA,MAAM+B,mBAAmB3Y,aAAagX,gBAAgB;4BACpD;yBACD,EAAEhI,IAAI,CAAC,CAACqJ;4BACPH,qBAAqBG,IAAIV,QAAQ;4BACjCzW,iBACE,oCACA6F;wBAEJ;wBACA,IAAIoQ,4BAA4B;4BAC9B,MAAMiB;4BACN,MAAMxX,uBAAuB;gCAC3BgW,YAAY;4BACd;wBACF;wBACA,MAAM+B;wBAEN,MAAM/X,uBAAuB;4BAC3BgW,YAAY;wBACd;wBAEA,MAAM5W,aAAagX,gBAAgB;4BAAC;yBAAS,EAAEhI,IAAI,CAAC,CAACqJ;4BACnDH,qBAAqBG,IAAIV,QAAQ;4BACjCzW,iBAAiB,+BAA+B6F;wBAClD;wBAEA,MAAMgR,iBAAiB3V,iBAAiB8V;wBACxCta,IAAIoa,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;wBAEtD/J,UAAUS,MAAM,CACdvR,oBAAoB0T,YAAY;4BAC9BqH,SAASW,uBAAuB9M;4BAChCoM;4BACAzE;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAEkE,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAM7X,aACpDgX,gBACA;wBAEF9V,iBAAiB,kBAAkB6F;wBAEnC+P,oBAAoBe,KAAKf,iBAAiB;wBAE1C9I,UAAUS,MAAM,CACdvR,oBAAoB0T,YAAY;4BAC9BqH,SAASW,uBAAuB9M;4BAChCoM,mBAAmBN;4BACnBnE;wBACF;oBAEJ;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAIjM,UAAU,CAACyE,iBAAiB,CAACV,gBAAgB;gBAC/C,MAAM3K,uBAAuB;oBAC3BgW,YAAY;gBACd;gBACA,MAAMtW,kBAAkBqP;gBACxBzO,iBAAiB,0BAA0B6F;YAC7C;YAEA,MAAM8R,qBAAqBhb,cAAc;YAEzC,MAAMib,oBAAoBjgB,KAAKuK,IAAI,CAACF,SAAS5I;YAC7C,MAAMye,uBAAuBlgB,KAAKuK,IAAI,CAACF,SAAS5H;YAEhD,IAAI0d,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMvU,WAAW,IAAIC;YACrB,MAAMuU,yBAAyB,IAAIvU;YACnC,MAAMwU,2BAA2B,IAAIxU;YACrC,MAAMyC,cAAc,IAAIzC;YACxB,MAAMyU,eAAe,IAAIzU;YACzB,MAAM0U,iBAAiB,IAAI1U;YAC3B,MAAM2U,mBAAmB,IAAI3U;YAC7B,MAAM4U,kBAAkB,IAAIjB;YAC5B,MAAMkB,cAAc,IAAIlB;YACxB,MAAMmB,qBAAqB,IAAInB;YAI/B,MAAMoB,qBAAqB,IAAIpB;YAC/B,MAAMqB,gBAAgB,IAAIrB;YAC1B,MAAMsB,oBAAoB,IAAItB;YAC9B,MAAMuB,YAAuB,IAAIvB;YACjC,IAAIwB,gBAAgB,MAAM5V,aAA4ByS;YACtD,MAAMoD,gBAAgB,MAAM7V,aAA4B0U;YACxD,MAAMoB,mBAAmB1S,SACrB,MAAMpD,aAA+B2U,wBACrCzM;YAEJ,MAAM6N,gBAAwC,CAAC;YAE/C,IAAI3S,QAAQ;gBACV,MAAM4S,mBAAmB,MAAMhW,aAC7BvL,KAAKuK,IAAI,CAACF,SAASlI,kBAAkBI;gBAGvC,IAAK,MAAMif,OAAOD,iBAAkB;oBAClCD,aAAa,CAACE,IAAI,GAAG/a,iBAAiB+a;gBACxC;gBAEA,MAAMnW,cACJrL,KAAKuK,IAAI,CAACF,SAAS7H,2BACnB8e;YAEJ;YAEA/P,QAAQD,GAAG,CAACmQ,UAAU,GAAG1f;YAEzB,MAAM2f,SAAS9Q,mBAAmB3D;YAElC,MAAM0U,gBAAgBpQ,QAAQqQ,MAAM;YACpC,MAAMC,kBAAkB3T,cAAcU,UAAU,CAAC;YAEjD,MAAMkT,0BAAmD;gBACvD/T,SAAS;gBACTgU,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnBtC,cAAc,EACduC,qBAAqB,EACtB,GAAG,MAAMN,gBAAgBhT,YAAY,CAAC;oBAcV5B;gBAb3B,IAAImG,eAAe;oBACjB,OAAO;wBACL4O,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrBtC,gBAAgB,CAAC,CAACvK;wBAClB8M,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChErV;gBACF,MAAMsV,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAa9L,SAAQzJ,2BAAAA,OAAO+C,YAAY,CAACyS,GAAG,qBAAvBxV,yBAAyByV,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgBjT,UAAU,CACvD;gBAEF,MAAMgU,oCACJD,uBAAuB9T,YAAY,CACjC,UACEoM,sBACC,MAAMyG,OAAOmB,wBAAwB,CAAC;wBACrCjZ,MAAM;wBACNS;wBACAkY;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuB9T,YAAY,CAC/D;wBAWa5B,cACMA;2BAXjBgO,sBACAyG,OAAOsB,YAAY,CAAC;wBAClBnR;wBACAjI,MAAM;wBACNS;wBACA+X;wBACAG;wBACA7G,WAAWD;wBACXG,gBAAgBD;wBAChBsH,kBAAkBhW,OAAOgW,gBAAgB;wBACzCnX,OAAO,GAAEmB,eAAAA,OAAOuP,IAAI,qBAAXvP,aAAanB,OAAO;wBAC7BoX,aAAa,GAAEjW,gBAAAA,OAAOuP,IAAI,qBAAXvP,cAAaiW,aAAa;wBACzCC,kBAAkBlW,OAAOmW,MAAM;wBAC/BC,WAAWpW,OAAO+C,YAAY,CAAC8L,GAAG;wBAClCwH,mBAAmBrW,OAAO+C,YAAY,CAACuT,SAAS;wBAChD1X;wBACA2W;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACEjZ,MAAM4Z;oBACNnZ;oBACAkY;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxD/Z,MAAM4Z;oBACNnZ;oBACAkY;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAItC,iBAAiB;gBAErB,MAAMgE,uBAAuB,MAAMve,oBACjC;oBAAEsN,OAAOyO;oBAAe9L,KAAK+L;gBAAiB,GAC9ChX,SACA4C,OAAO+C,YAAY,CAAC6T,QAAQ;gBAG9B,MAAMvV,qBAAyCmC,QAC7CzQ,KAAKuK,IAAI,CAACF,SAASlI,kBAAkBG;gBAGvC,MAAMwhB,iBAAiBnV,SAClB8B,QACCzQ,KAAKuK,IAAI,CACPF,SACAlI,kBACAY,4BAA4B,YAGhC;gBACJ,MAAMghB,oBAAoBD,iBAAiB,IAAI9X,QAAQ;gBACvD,IAAI8X,kBAAkBC,mBAAmB;oBACvC,IAAK,MAAMC,MAAMF,eAAeG,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASJ,eAAeG,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBrJ,GAAG,CAACwJ;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMF,eAAeM,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASJ,eAAeM,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBrJ,GAAG,CAACwJ;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM1C,OAAOvV,OAAOQ,IAAI,CAAC6B,sCAAAA,mBAAoByT,SAAS,EAAG;oBAC5D,IAAIP,IAAI9L,UAAU,CAAC,SAAS;wBAC1B4K;oBACF;gBACF;gBAEA,MAAMzB,QAAQwF,GAAG,CACfpY,OAAOC,OAAO,CAACiC,UACZc,MAAM,CACL,CAACC,KAAK,CAACsS,KAAKxS,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAMoV,WAAW9C;oBAEjB,KAAK,MAAM5X,QAAQoF,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAEiV;4BAAU1a;wBAAK;oBAC5B;oBAEA,OAAOsF;gBACT,GACA,EAAE,EAEH5C,GAAG,CAAC,CAAC,EAAEgY,QAAQ,EAAE1a,IAAI,EAAE;oBACtB,MAAM2a,gBAAgB1C,gBAAgBjT,UAAU,CAAC,cAAc;wBAC7DhF;oBACF;oBACA,OAAO2a,cAAc1V,YAAY,CAAC;wBAChC,MAAM2V,aAAa/gB,kBAAkBmG;wBACrC,MAAM,CAAC6a,MAAMC,UAAU,GAAG,MAAMpf,kBAC9Bgf,UACAE,YACAna,SACA+W,eACAC,kBACApU,OAAO+C,YAAY,CAAC6T,QAAQ,EAC5BD;wBAGF,IAAIe,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAIxK,WAAW;wBAEf,IAAI8J,aAAa,SAAS;4BACxB9J,WACEzC,WAAWkN,IAAI,CAAC,CAAC1X;gCACfA,IAAIlG,iBAAiBkG;gCACrB,OACEA,EAAEmI,UAAU,CAAC8O,aAAa,QAC1BjX,EAAEmI,UAAU,CAAC8O,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIU;wBAEJ,IAAIZ,aAAa,SAAS/K,gBAAgB;4BACxC,KAAK,MAAM,CAAC4L,cAAcC,eAAe,IAAInZ,OAAOC,OAAO,CACzDoV,eACC;gCACD,IAAI8D,mBAAmBxb,MAAM;oCAC3B4Q,WAAWjB,cAAc,CAAC4L,aAAa,CAAC1X,OAAO,CAC7C,yBACA;oCAEFyX,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAe1f,yBAAyB6U,YAC1C/J,QAAQC,OAAO,CACb,iDAEF1Q,KAAKuK,IAAI,CACP,AAAC+Z,CAAAA,aAAa,UAAUjP,WAAW1G,MAAK,KAAM,IAC9C6L;wBAGN,MAAM8K,iBAAiBhB,aAAa;wBACpC,MAAMiB,aAAa/K,WACf,MAAM9V,8BAA8B;4BAClC4gB;4BACAD;4BACAzN,gBAAgB3K,OAAO2K,cAAc;4BACrCjJ;4BACA1B;4BACAkM,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChEvP,MAAM0b,iBAAiBJ,kBAAmBtb;wBAC5C,KACA6J;wBAEJ,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAO8R,8BAAAA,WAAYC,OAAO,MAAK,eAC/B,QAAOD,8BAAAA,WAAYE,WAAW,MAAK,aACnC;4BACA3D,wBAAwBC,SAAS,CAACnY,KAAK,GAAG;gCACxC6b,WAAW,EAAEF,8BAAAA,WAAYE,WAAW;4BACtC;wBACF;wBAEA,MAAMC,cAAcpX,mBAAmByT,SAAS,CAC9CmD,mBAAmBtb,KACpB,GACG,SACA2b,8BAAAA,WAAYC,OAAO;wBAEvB,IAAI,CAACpS,eAAe;4BAClB0R,oBACER,aAAa,SACbiB,CAAAA,8BAAAA,WAAY9I,GAAG,MAAK/Z,iBAAiBijB,MAAM;4BAE7C,IAAIrB,aAAa,SAAS,CAAC5e,eAAekE,OAAO;gCAC/C,IAAI;oCACF,IAAIgc;oCAEJ,IAAI3f,cAAcyf,cAAc;wCAC9B,IAAIpB,aAAa,OAAO;4CACtBjE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAMuF,cACJvB,aAAa,UAAU1a,OAAOsb,mBAAmB;wCAEnDU,WAAWtX,mBAAmByT,SAAS,CAAC8D,YAAY;oCACtD;oCAEA,IAAIC,mBACFvB,cAAc3V,UAAU,CAAC;oCAC3B,IAAImX,eAAe,MAAMD,iBAAiBjX,YAAY,CACpD;4CASa5B,cACMA;wCATjB,OAAOyU,OAAOsB,YAAY,CAAC;4CACzBnR;4CACAjI;4CACAsb;4CACA7a;4CACA+X;4CACAG;4CACAU,kBAAkBhW,OAAOgW,gBAAgB;4CACzCnX,OAAO,GAAEmB,eAAAA,OAAOuP,IAAI,qBAAXvP,aAAanB,OAAO;4CAC7BoX,aAAa,GAAEjW,gBAAAA,OAAOuP,IAAI,qBAAXvP,cAAaiW,aAAa;4CACzC8C,UAAUF,iBAAiBG,KAAK;4CAChCP;4CACAE;4CACAtB;4CACA5I,WAAWD;4CACXG,gBAAgBD;4CAChBuK,cAAcjZ,OAAOiZ,YAAY;4CACjCC,eAAelZ,OAAO+C,YAAY,CAACmW,aAAa;4CAChDC,gBAAgBziB,cAAc8G,cAAc,GACxC,QACAwC,OAAO+C,YAAY,CAACoW,cAAc;4CACtCC,oBAAoBpZ,OAAOqZ,kBAAkB;4CAC7CnD,kBAAkBlW,OAAOmW,MAAM;4CAC/BC,WAAWpW,OAAO+C,YAAY,CAAC8L,GAAG;4CAClCwH,mBAAmBrW,OAAO+C,YAAY,CAACuT,SAAS;4CAChD1X;4CACA2W;wCACF;oCACF;oCAGF,IAAI8B,aAAa,SAASY,iBAAiB;wCACzCnE,mBAAmBwF,GAAG,CAACrB,iBAAiBtb;wCACxC,0CAA0C;wCAC1C,IAAI3D,cAAcyf,cAAc;4CAC9Bb,WAAW;4CACXD,QAAQ;4CAER7f,IAAIyhB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAYnjB,eAAesG;4CAEjC,IACE,OAAOmc,aAAapB,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoBoB,aAAapB,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAIoB,aAAapB,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEXhE,YAAY0F,GAAG,CAACrB,iBAAiB,EAAE;4CACrC,OAOK,IAAIjY,OAAO+C,YAAY,CAAC0L,SAAS,IAAI+K,WAAW;gDACnD3F,mBAAmByF,GAAG,CAACrB,iBAAiB;oDACtCtb;oDACAsb;gDACF;4CACF;4CAEA,IAAIa,aAAaW,iBAAiB,EAAE;gDAClC7F,YAAY0F,GAAG,CACbrB,iBACAa,aAAaW,iBAAiB;gDAEhC1B,gBAAgBe,aAAaW,iBAAiB,CAACpa,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;gDAE3BoY,QAAQ;4CACV;4CAEA,MAAM+B,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC5R,MAAM,GAAG;gDAE1C,IACE7H,OAAOmW,MAAM,KAAK,YAClBqD,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAI3P,MACR,CAAC,MAAM,EAAEtN,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAAC6c,WAAW;oDACd5F,YAAY0F,GAAG,CAACrB,iBAAiB;wDAC/B;4DACE1Y,UAAU5C;4DACVkd,iBAAiBld;4DACjBmd,qBAAqBtT;4DACrBuT,cACEjB,aAAakB,qBAAqB;4DACpCC,oBAAoBzT;wDACtB;qDACD;oDACDoR,WAAW;gDACb,OAAO,IACL,CAACgC,2BACAF,CAAAA,UAAUQ,OAAO,KAAK,WACrBR,UAAUQ,OAAO,KAAK,cAAa,GACrC;oDACAtG,YAAY0F,GAAG,CAACrB,iBAAiB,EAAE;oDACnCL,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAIoB,aAAakB,qBAAqB,EAAE;gDACtCjG,cAAcuF,GAAG,CACfrB,iBACAa,aAAakB,qBAAqB;4CAEtC;4CAEAhG,kBAAkBsF,GAAG,CAACrB,iBAAiByB;wCACzC;oCACF,OAAO;wCACL,IAAI1gB,cAAcyf,cAAc;4CAC9B,IAAIK,aAAaqB,cAAc,EAAE;gDAC/Bzc,QAAQG,IAAI,CACV,CAAC,kFAAkF,EAAElB,MAAM;4CAE/F;4CACA,mDAAmD;4CACnD,8CAA8C;4CAC9Cmc,aAAalB,QAAQ,GAAG;4CACxBkB,aAAaqB,cAAc,GAAG;wCAChC;wCAEA,IACErB,aAAalB,QAAQ,KAAK,SACzBkB,CAAAA,aAAahB,WAAW,IAAIgB,aAAasB,SAAS,AAAD,GAClD;4CACAzH,iBAAiB;wCACnB;wCAEA,IAAImG,aAAahB,WAAW,EAAE;4CAC5BA,cAAc;4CACdrE,eAAehG,GAAG,CAAC9Q;wCACrB;wCAEA,IAAImc,aAAa7D,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAI6D,aAAaqB,cAAc,EAAE;4CAC/Brb,SAAS2O,GAAG,CAAC9Q;4CACbgb,QAAQ;4CAER,IACEmB,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC5R,MAAM,GAAG,GACxC;gDACA8L,gBAAgB2F,GAAG,CACjB3c,MACAmc,aAAaW,iBAAiB;gDAEhC1B,gBAAgBe,aAAaW,iBAAiB,CAACpa,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;4CAE7B;4CAEA,IACEuZ,aAAakB,qBAAqB,KAClCve,aAAa4e,sBAAsB,EACnC;gDACA9G,yBAAyB9F,GAAG,CAAC9Q;4CAC/B,OAAO,IACLmc,aAAakB,qBAAqB,KAClCve,aAAa6e,SAAS,EACtB;gDACAhH,uBAAuB7F,GAAG,CAAC9Q;4CAC7B;wCACF,OAAO,IAAImc,aAAayB,cAAc,EAAE;4CACtC7G,iBAAiBjG,GAAG,CAAC9Q;wCACvB,OAAO,IACLmc,aAAalB,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAMrB,oCAAqC,OAC5C;4CACAhV,YAAYiM,GAAG,CAAC9Q;4CAChBib,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChD/Y,SAAS2O,GAAG,CAAC9Q;4CACbgb,QAAQ;wCACV;wCAEA,IAAI7J,eAAenR,SAAS,QAAQ;4CAClC,IACE,CAACmc,aAAalB,QAAQ,IACtB,CAACkB,aAAaqB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAIlQ,MACR,CAAC,cAAc,EAAEjX,4CAA4C,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAMwjB,mCACP,CAACsC,aAAaqB,cAAc,EAC5B;gDACA3Y,YAAYgZ,MAAM,CAAC7d;4CACrB;wCACF;wCAEA,IACEvH,oBAAoB+M,QAAQ,CAACxF,SAC7B,CAACmc,aAAalB,QAAQ,IACtB,CAACkB,aAAaqB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAIlQ,MACR,CAAC,OAAO,EAAEtN,KAAK,GAAG,EAAE3J,4CAA4C,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAO+W,KAAK;oCACZ,IACE,CAAChR,QAAQgR,QACTA,IAAI0Q,OAAO,KAAK,0BAEhB,MAAM1Q;oCACRyJ,aAAa/F,GAAG,CAAC9Q;gCACnB;4BACF;4BAEA,IAAI0a,aAAa,OAAO;gCACtB,IAAIM,SAASC,UAAU;oCACrB1E;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAc,UAAUqF,GAAG,CAAC3c,MAAM;4BAClB6a;4BACAC;4BACAG;4BACAD;4BACAD;4BACAI;4BACAC;4BACA2C,qBAAqBlU;4BACrB+R,SAASE;4BACTkC,cAAcnU;4BACdoU,kBAAkBpU;4BAClBqU,iBAAiBrU;wBACnB;oBACF;gBACF;gBAGJ,IAAIjP,qBAAqB;oBACvBO,IAAIqS,KAAK,CACP,CAAC,0IAA0I,CAAC;oBAE9I7F,QAAQ4C,IAAI,CAAC;gBACf;gBAEA,MAAM4T,kBAAkB,MAAMhF;gBAC9B,MAAMiF,qBACJ,AAAC,MAAMpF,qCACNmF,mBAAmBA,gBAAgBP,cAAc;gBAEpD,MAAMS,cAAc;oBAClBjG,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACAtC;oBACAuC,uBAAuB6F;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAIjI,oBAAoBA,mBAAmBkI,cAAc;YACzD7f,iBAAiB,iCAAiC6F;YAElD,IAAI8T,0BAA0B;gBAC5BrX,QAAQG,IAAI,CACV3L,KAAKC,OAAO,CAAC,SAAS,CAAC,KACrBA,OACE,CAAC,qJAAqJ,CAAC;gBAG7JuL,QAAQG,IAAI,CACV;YAEJ;YAEA,MAAM,EAAEob,YAAY,EAAE,GAAGjZ;YAEzB,MAAMkb,gCAA0C,EAAE;YAClD,IAAI3Z,wBAAwB;gBAC1B2Z,8BAA8B9Y,IAAI,CAChCrP,KAAKuK,IAAI,CAACpI,kBAAkB,GAAG9B,8BAA8B,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IAAI,CAAC4S,eAAgBoN,CAAAA,uBAAuBC,qBAAoB,GAAI;oBAClE6H,8BAA8B9Y,IAAI,CAChCrP,KAAKuK,IAAI,CACPpI,kBACA,CAAC,KAAK,EAAE9B,8BAA8B,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAM+nB,8BAA8Bla,cACjCU,UAAU,CAAC,kCACXmF,OAAO,CAAC;gBACP,MAAMsU,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAAC7G,KAAK8G,MAAM,IAAIrc,OAAOC,OAAO,CACvCe,OAAO+C,YAAY,CAACmW,aAAa,IAAI,CAAC,GACrC;oBACD,IAAI3E,OAAO8G,OAAO;wBAChBD,uBAAuB,CAAC7G,IAAI,GAAGxhB,KAAKuP,QAAQ,CAAClF,SAASie;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvDxa,SAAS;oBACTd,QAAQ;wBACN,GAAGA,MAAM;wBACTub,YAAY/U;wBACZ,GAAI9P,cAAc8G,cAAc,GAC5B;4BACEge,UAAU;wBACZ,IACA,CAAC,CAAC;wBACNvC,cAAcA,eACVlmB,KAAKuP,QAAQ,CAAClF,SAAS6b,gBACvBjZ,OAAOiZ,YAAY;wBACvBlW,cAAc;4BACZ,GAAG/C,OAAO+C,YAAY;4BACtBmW,eAAekC;4BACfK,iBAAiB/kB,cAAc8G,cAAc;4BAE7C,oGAAoG;4BACpGke,uBAAuBvV;wBACzB;oBACF;oBACAzE,QAAQkD;oBACR+W,gBAAgB5oB,KAAKuP,QAAQ,CAAClB,uBAAuBwD;oBACrD7C,OAAO;wBACL9M;wBACAlC,KAAKuP,QAAQ,CAAClF,SAAS2T;wBACvBvc;wBACAO;wBACAhC,KAAKuK,IAAI,CAACpI,kBAAkBa;wBAC5BhD,KAAKuK,IAAI,CAACpI,kBAAkBG;wBAC5BtC,KAAKuK,IAAI,CAACpI,kBAAkBU,4BAA4B;2BACpD,CAACoQ,cACD;4BACEjT,KAAKuK,IAAI,CACPpI,kBACAW,qCAAqC;4BAEvCb;yBACD,GACD,EAAE;2BACF0M,SACA;+BACM1B,OAAO+C,YAAY,CAACyS,GAAG,GACvB;gCACEziB,KAAKuK,IAAI,CACPpI,kBACAS,iCAAiC;gCAEnC5C,KAAKuK,IAAI,CACPpI,kBACAS,iCAAiC;6BAEpC,GACD,EAAE;4BACN5C,KAAKuK,IAAI,CAACpI,kBAAkBI;4BAC5BvC,KAAKuK,IAAI,CAAC/H;4BACVC;4BACAzC,KAAKuK,IAAI,CACPpI,kBACAY,4BAA4B;4BAE9B/C,KAAKuK,IAAI,CACPpI,kBACAY,4BAA4B;yBAE/B,GACD,EAAE;2BACFsS,YAAY,CAACpC,cACb;4BACE9P,uBAAuB;4BACvBnD,KAAKuK,IAAI,CAACpI,kBAAkBgB,uBAAuB;yBACpD,GACD,EAAE;wBACN3B;wBACAxB,KAAKuK,IAAI,CAACpI,kBAAkBQ,qBAAqB;wBACjD3C,KAAKuK,IAAI,CAACpI,kBAAkBQ,qBAAqB;2BAC9CwlB;qBACJ,CACE/b,MAAM,CAAC/K,aACPiL,GAAG,CAAC,CAACyC,OAAS/O,KAAKuK,IAAI,CAAC0C,OAAO5C,OAAO,EAAE0E;oBAC3C8Z,QAAQ,EAAE;gBACZ;gBAEA,OAAON;YACT;YAEF,IAAI,CAAC3I,gBAAgB;gBACnBwI,4BAA4BS,MAAM,CAACxZ,IAAI,CACrCrP,KAAKuP,QAAQ,CACXsC,KACA7R,KAAKuK,IAAI,CACPvK,KAAKyP,OAAO,CACVgB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMoY,iBAAiBxQ,UAAU2M,IAAI,CAAC,CAAC1X,IACrCA,EAAE6B,QAAQ,CAACjP;YAEb,IAAIoO,oBAAoB;YAExB,IAAIua,gBAAgB;gBAClB,MAAMvD,aAAa,MAAM7gB,8BAA8B;oBACrD4gB,gBAAgB;oBAChBD,cAAcrlB,KAAKuK,IAAI,CAACsH,KAAKiX;oBAC7B7b;oBACA0B;oBACAiJ,gBAAgB3K,OAAO2K,cAAc;oBACrCuB,OAAO;oBACPvP,MAAM;gBACR;gBAEA,IAAI2b,WAAWC,OAAO,KAAK,UAAU;wBAIvBD;oBAHZhX,oBAAoB;oBACpBuT,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClDyD,SAASD,WAAWC,OAAO;wBAC3BuD,UAAUxD,EAAAA,yBAAAA,WAAWyD,UAAU,qBAArBzD,uBAAuBwD,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAIjW,aAAa;wBACf,MAAM5H,cACJrL,KAAKuK,IAAI,CACPF,SACA,UACAwB,SACAzI,uCAEF0e,wBAAwBC,SAAS,CAAC,eAAe,CAACgH,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAMlc,6BAA6BxC,SAASyX;YAE5C,IAAI,CAACpP,kBAAkB,CAACwL,oBAAoB;gBAC1CA,qBAAqBtW,mBAAmB;oBACtCiK;oBACA5E;oBACA5C;oBACAqV,mBAAmB9Z,8BAA8Bsb;oBACjDzS,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACA0R;oBACA3B;oBACA5P;gBACF,GAAGwR,KAAK,CAAC,CAAC7I;oBACRrM,QAAQyM,KAAK,CAACJ;oBACdzF,QAAQ4C,IAAI,CAAC;gBACf;YACF;YAEA,IAAIwM,iBAAiB8D,IAAI,GAAG,KAAK1Y,SAAS0Y,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/DzI,eAAeO,UAAU,GAAGlZ,gBAAgB;uBACvCsd;uBACA5U;iBACJ,EAAEO,GAAG,CAAC,CAAC1C;oBACN,OAAOjC,eAAeiC,MAAMiC;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAMqC,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMxD,cAAc0Q,oBAAoBC;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAMmN,oBACJ,CAACnH,4BAA6B,CAAA,CAACG,yBAAyBpH,WAAU;YAEpE,IAAI0F,aAAagE,IAAI,GAAG,GAAG;gBACzB,MAAMzN,MAAM,qBAQX,CARW,IAAIE,MACd,CAAC,qCAAqC,EACpCuJ,aAAagE,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAIhE;iBAAa,CACnEnU,GAAG,CAAC,CAAC8c,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxB7e,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACAyM,IAAIC,IAAI,GAAG;gBACX,MAAMD;YACR;YAEA,MAAMlR,aAAauE,SAASwB;YAE5B,IAAIoB,OAAO+C,YAAY,CAACqZ,WAAW,EAAE;gBACnC,MAAMC,WACJ7Y,QAAQ;gBAEV,MAAM8Y,eAAe,MAAM,IAAI1K,QAAkB,CAACnO,SAAS8Y;oBACzDF,SACE,YACA;wBAAEtT,KAAKhW,KAAKuK,IAAI,CAACF,SAAS;oBAAU,GACpC,CAAC2M,KAAKhI;wBACJ,IAAIgI,KAAK;4BACP,OAAOwS,OAAOxS;wBAChB;wBACAtG,QAAQ1B;oBACV;gBAEJ;gBAEAoZ,4BAA4BpZ,KAAK,CAACK,IAAI,IACjCka,aAAajd,GAAG,CAAC,CAACtB,WACnBhL,KAAKuK,IAAI,CAAC0C,OAAO5C,OAAO,EAAE,UAAUW;YAG1C;YAEA,MAAMye,WAAqC;gBACzC;oBACElS,aAAa;oBACbC,iBAAiBvK,OAAO+C,YAAY,CAAC0L,SAAS,GAAG,IAAI;gBACvD;gBACA;oBACEnE,aAAa;oBACbC,iBAAiBvK,OAAO+C,YAAY,CAACqZ,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACE9R,aAAa;oBACbC,iBAAiBvK,OAAO+C,YAAY,CAAC0Z,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACEnS,aAAa;oBACbC,iBAAiBvK,OAAO+C,YAAY,CAAC8L,GAAG,GAAG,IAAI;gBACjD;gBACA;oBACEvE,aAAa;oBACbC,iBAAiBpO,2BAA2B6D,UAAU,IAAI;gBAC5D;aACD;YACDkI,UAAUS,MAAM,CACd6T,SAASnd,GAAG,CAAC,CAACqd;gBACZ,OAAO;oBACLlS,WAAWtT;oBACXuT,SAASiS;gBACX;YACF;YAGF,MAAM7c,iCACJzC,SACA+d;YAGF,iDAAiD;YACjD,sDAAsD;YACtD,IAAI1V,kBAAkB,CAACO,aAAa;gBAClClO,IAAIqP,IAAI,CAAC;gBAET,MAAMlG,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMxF,gBAAgB;wBACpBgB;wBACA4C;oBACF;gBACF;YACJ;YAEA,MAAMqB,qBAAyC,MAAM/C,aACnDvL,KAAKuK,IAAI,CAACF,SAASlI,kBAAkBG;YAGvC,MAAMsJ,oBAAuC;gBAC3CmC,SAAS;gBACT5B,QAAQ,CAAC;gBACTO,eAAe,CAAC;gBAChBkd,gBAAgB,EAAE;gBAClBC,SAAShR;YACX;YAEA,MAAMiR,qBAA+B,EAAE;YAEvC,MAAM,EAAEtN,IAAI,EAAE,GAAGvP;YAEjB,MAAM8c,wBAAwB1nB,oBAAoB+J,MAAM,CACtD,CAACxC,OACCkK,WAAW,CAAClK,KAAK,IACjBkK,WAAW,CAAClK,KAAK,CAAC8L,UAAU,CAAC;YAEjCqU,sBAAsBC,OAAO,CAAC,CAACpgB;gBAC7B,IAAI,CAACmC,SAASke,GAAG,CAACrgB,SAAS,CAACoY,0BAA0B;oBACpDvT,YAAYiM,GAAG,CAAC9Q;gBAClB;YACF;YAEA,MAAMsgB,cAAcH,sBAAsB3a,QAAQ,CAAC;YACnD,MAAM+a,sBACJ,CAACD,eAAe,CAAC/H,yBAAyB,CAACH;YAE7C,MAAMoI,gBAAgB;mBAAI3b;mBAAgB1C;aAAS;YACnD,MAAMse,iBAAiBxJ,YAAYoJ,GAAG,CAAChnB;YACvC,MAAMqnB,kBAAkBtP,aAAaqP;YAErC,MAAMtiB,uBAAuB;gBAC3BgW,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAAC3K,iBACAgX,CAAAA,cAActV,MAAM,GAAG,KACtBqU,qBACAgB,uBACAxb,MAAK,GACP;gBACA,MAAM4b,uBACJrc,cAAcU,UAAU,CAAC;gBAC3B,MAAM2b,qBAAqB1b,YAAY,CAAC;oBACtCzJ,uBACE;2BACKglB;2BACAjc,SAASW,KAAK,CAAC1C,MAAM,CAAC,CAACxC,OAAS,CAACwgB,cAAchb,QAAQ,CAACxF;qBAC5D,EACDmC,UACA,IAAI4T,IACFpH,MAAMC,IAAI,CAACoI,gBAAgB1U,OAAO,IAAII,GAAG,CACvC,CAAC,CAAC1C,MAAMuC,OAAO;wBACb,OAAO;4BAACvC;4BAAMuC,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAMC,QAAQ;yBAAE;oBACtD;oBAIN,MAAMwF,YAAYvB,QAAQ,aACvBwB,OAAO;oBAEV,MAAMuY,eAAmC;wBACvC,GAAGvd,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7Dwd,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7D3e,SAASie,OAAO,CAAC,CAACpgB;gCAChB,IAAItG,eAAesG,OAAO;oCACxBkgB,mBAAmBza,IAAI,CAACzF;oCAExB,IAAI2W,uBAAuB0J,GAAG,CAACrgB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAI4S,MAAM;4CACRkO,UAAU,CAAC,CAAC,CAAC,EAAElO,KAAK0G,aAAa,GAAGtZ,MAAM,CAAC,GAAG;gDAC5CA;gDACA+gB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAAC9gB,KAAK,GAAG;gDACjBA;gDACA+gB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAAC9gB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACdgX,gBAAgBoJ,OAAO,CAAC,CAAC7d,QAAQvC;gCAC/BuC,OAAO6d,OAAO,CAAC,CAACzd;oCACdme,UAAU,CAACne,MAAMC,QAAQ,CAAC,GAAG;wCAC3B5C;wCACAghB,UAAUre,MAAMua,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIqC,mBAAmB;gCACrBuB,UAAU,CAAC,OAAO,GAAG;oCACnB9gB,MAAMmR,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAIoP,qBAAqB;gCACvBO,UAAU,CAAC,OAAO,GAAG;oCACnB9gB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChDiX,YAAYmJ,OAAO,CAAC,CAAC7d,QAAQ+Y;gCAC3B,MAAMyB,YAAY1F,kBAAkB4J,GAAG,CAAC3F;gCACxC,MAAM4F,iBAAiBnE,CAAAA,6BAAAA,UAAWQ,OAAO,MAAK;gCAE9C,MAAMxC,oBAAoBgC,YACtBle,uBAAuBwE,OAAO+C,YAAY,CAAC8L,GAAG,EAAE6K,aAChDlT;gCAEJtH,OAAO6d,OAAO,CAAC,CAACzd;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAM2a,kBAAkB,IACxB3a,MAAM2a,kBAAkB,CAACpS,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEA4V,UAAU,CAACne,MAAMC,QAAQ,CAAC,GAAG;wCAC3B5C,MAAMsb;wCACN0F,UAAUre,MAAMua,eAAe;wCAC/BiE,sBAAsBxe,MAAMwa,mBAAmB;wCAC/CiE,iBAAiBF;wCACjBG,WAAW;wCACXC,oBAAoBvG;oCACtB;gCACF;4BACF;4BAEA,gEAAgE;4BAChE,gEAAgE;4BAChE,2DAA2D;4BAC3D,wCAAwC;4BACxC,KAAK,MAAM,EACT/a,IAAI,EACJsb,eAAe,EAChB,IAAIpE,mBAAmBqK,MAAM,GAAI;gCAChCT,UAAU,CAAC9gB,KAAK,GAAG;oCACjBA,MAAMsb;oCACN0F,UAAUhhB;oCACVmhB,sBAAsBliB,aAAae;oCACnC,sDAAsD;oCACtDqhB,WAAW;oCACX,6DAA6D;oCAC7DC,oBAAoB;oCACpBE,sBAAsB;oCACtB,+DAA+D;oCAC/DJ,iBAAiB;gCACnB;4BACF;4BAEA,IAAIxO,MAAM;gCACR,KAAK,MAAM5S,QAAQ;uCACd6E;uCACA1C;uCACCod,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCgB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMkB,QAAQtf,SAASke,GAAG,CAACrgB;oCAC3B,MAAM6c,YAAYnjB,eAAesG;oCACjC,MAAM0hB,aAAaD,SAAS9K,uBAAuB0J,GAAG,CAACrgB;oCAEvD,KAAK,MAAM2hB,UAAU/O,KAAK1Q,OAAO,CAAE;4CAMzB4e;wCALR,+DAA+D;wCAC/D,IAAIW,SAAS5E,aAAa,CAAC6E,YAAY;wCACvC,MAAMhc,aAAa,CAAC,CAAC,EAAEic,SAAS3hB,SAAS,MAAM,KAAKA,MAAM;wCAE1D8gB,UAAU,CAACpb,WAAW,GAAG;4CACvB1F,MAAM8gB,EAAAA,mBAAAA,UAAU,CAAC9gB,KAAK,qBAAhB8gB,iBAAkB9gB,IAAI,KAAIA;4CAChC4hB,SAASD;4CACTZ,gBAAgBW;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOX,UAAU,CAAC9gB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAO8gB;wBACT;oBACF;oBAEA,MAAMnY,SAASvS,KAAKuK,IAAI,CAACF,SAAS;oBAClC,MAAMohB,eAAe,MAAMzZ,UACzBH,KACA;wBACEQ,YAAYmY;wBACZ1Y;wBACAQ,QAAQ;wBACRF,aAAa;wBACbS;wBACA/D,OAAOsb;wBACP7X;wBACAmZ,eAAe;wBACf1a,YAAYjB,mBAAmBya;oBACjC,GACAtc;oBAGF,sDAAsD;oBACtD,IAAI,CAACud,cAAc;oBAEnB,MAAME,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCJ;wBADF,MAAMK,gBACJL,2BAAAA,aAAaM,MAAM,CAAClB,GAAG,CAACe,gCAAxBH,yBAAqCK,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAElF,YAAYiF;gCAAmBG,QAAQvY;4BAAU;wBAC5D;wBAEA,IACEqY,aAAalF,UAAU,KAAK,SAC5BkF,aAAalF,UAAU,GAAG,KAC1BkF,aAAaE,MAAM,KAAKvY,WACxB;4BACA,OAAO;gCACLmT,YAAYkF,aAAalF,UAAU;gCACnCoF,QAAQ/e,OAAOgf,UAAU;4BAC3B;wBACF;wBAEA,OAAOH;oBACT;oBAEA,IAAIjZ,eAAetB,QAAQD,GAAG,CAAC4a,sBAAsB,KAAK,KAAK;wBAC7DlkB,mBAAmByjB;oBACrB;oBAEA3nB,gCAAgC;wBAC9BuG,SAAS4C,OAAO5C,OAAO;wBACvB8hB,QAAQ;4BACNnY;+BACGyX,aAAaW,2BAA2B,CAACjB,MAAM;yBACnD;oBACH;oBAEAvf,kBAAkBge,cAAc,GAAGrR,MAAMC,IAAI,CAC3CiT,aAAaY,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMziB,QAAQ6E,YAAa;wBAC9B,MAAM6d,eAAe5oB,YAAYkG,MAAMS,SAASoJ,WAAW;wBAC3D,MAAMhU,GAAG8sB,MAAM,CAACD;oBAClB;oBAEAzL,YAAYmJ,OAAO,CAAC,CAACtD,mBAAmBxB;4BAWbhE;wBAVzB,MAAMtX,OAAOmX,mBAAmB8J,GAAG,CAAC3F;wBACpC,IAAI,CAACtb,MAAM,MAAM,qBAAoC,CAApC,IAAIZ,eAAe,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAM2d,YAAY1F,kBAAkB4J,GAAG,CAAC3F;wBACxC,IAAI,CAACyB,WAAW,MAAM,qBAA0C,CAA1C,IAAI3d,eAAe,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAIwjB,oBACF7F,UAAUC,UAAU,KAAK,KACzB+E,gBAAgB/hB,MAAMgd,UAAU,KAAK;wBAEvC,IAAI4F,uBAAqBtL,iBAAAA,UAAU2J,GAAG,CAACjhB,0BAAdsX,eAAqB2D,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrF3D,UAAUqF,GAAG,CAAC3c,MAAM;gCAClB,GAAIsX,UAAU2J,GAAG,CAACjhB,KAAK;gCACvBib,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAM6H,oBAAoBnlB,gBAAgB4d;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMP,oBACJ,CAAC8H,qBACDhkB,uBAAuBwE,OAAO+C,YAAY,CAAC8L,GAAG,EAAE6K,aAC5C,OACAlT;wBAEN,MAAMiZ,sBACJ,uEAAuE;wBACvEzf,OAAO0f,eAAe,IAAI1jB;wBAE5B,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAM2jB,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAUrL,KAAK9a;4BAAc;4BACrC;gCACEmmB,MAAM;gCACNrL,KAAK;gCACL8G,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7F3D,oBACA;gCACE;oCACEkI,MAAM;oCACNrL,KAAK;oCACL8G,OAAOoE;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAMvgB,SAA6B,EAAE;wBACrC,MAAMO,gBAAoC,EAAE;wBAE5C,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,IAAIogB,yBAA6C,EAAE;wBACnD,IAAIC,uBAA2C,EAAE;wBACjD,KAAK,MAAMC,oBAAoBtG,kBAAmB;4BAChD,IACEsG,iBAAiBjG,mBAAmB,IACpCiG,iBAAiBjG,mBAAmB,CAACjS,MAAM,GAAG,GAC9C;gCACAgY,uBAAuBzd,IAAI,CAAC2d;4BAC9B,OAAO;gCACLD,qBAAqB1d,IAAI,CAAC2d;4BAC5B;wBACF;wBAEAF,yBAAyBvpB,sBACvBupB,wBACA,CAACE,mBAAqBA,iBAAiBxgB,QAAQ;wBAEjDugB,uBAAuBxpB,sBACrBwpB,sBACA,CAACC,mBAAqBA,iBAAiBxgB,QAAQ;wBAGjDka,oBAAoB;+BACfqG;+BACAD;yBACJ;wBAED,KAAK,MAAME,oBAAoBtG,kBAAmB;4BAChD,+BAA+B;4BAC/B,iCAAiC;4BACjC,IAAIsG,iBAAiBxgB,QAAQ,KAAKtJ,4BAA4B;gCAC5D;4BACF;4BAEA,IACEyhB,qBACAqI,iBAAiBjG,mBAAmB,IACpCiG,iBAAiBjG,mBAAmB,CAACjS,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9BpI,cAAc2C,IAAI,CAAC2d;4BACrB,OAAO;gCACL,4DAA4D;gCAC5D,gCAAgC;gCAChC7gB,OAAOkD,IAAI,CAAC2d;4BACd;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAMzgB,SAASJ,OAAQ;4BAC1B,IAAI7I,eAAesG,SAAS2C,MAAMC,QAAQ,KAAK5C,MAAM;4BACrD,IAAI2C,MAAMC,QAAQ,KAAKtJ,4BAA4B;4BAEnD,MAAM,EACJ+pB,WAAW,CAAC,CAAC,EACbnF,eAAe,EACfoF,YAAY,EACb,GAAGzB,aAAaM,MAAM,CAAClB,GAAG,CAACte,MAAMC,QAAQ,KAAK,CAAC;4BAEhD,MAAMsf,eAAeH,gBACnBpf,MAAMC,QAAQ,EACdma,UAAUC,UAAU;4BAGtB1F,UAAUqF,GAAG,CAACha,MAAMC,QAAQ,EAAE;gCAC5B,GAAI0U,UAAU2J,GAAG,CAACte,MAAMC,QAAQ,CAAC;gCACjC0gB;gCACApF;gCACAH,qBAAqBmE;4BACvB;4BAEA,uEAAuE;4BACvE5K,UAAUqF,GAAG,CAAC3c,MAAM;gCAClB,GAAIsX,UAAU2J,GAAG,CAACjhB,KAAK;gCACvBsjB;gCACApF;gCACAH,qBAAqBmE;4BACvB;4BAEA,IAAIA,aAAalF,UAAU,KAAK,GAAG;gCACjC,MAAMuG,kBAAkB1pB,kBAAkB8I,MAAMC,QAAQ;gCAExD,IAAI4gB;gCACJ,IAAIX,mBAAmB;oCACrBW,YAAY;gCACd,OAAO;oCACLA,YAAYptB,KAAKqtB,KAAK,CAAC9iB,IAAI,CAAC,GAAG4iB,kBAAkB5sB,YAAY;gCAC/D;gCAEA,IAAI+sB;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAACb,qBAAqB5Q,iBAAiB;oCACzCyR,oBAAoBttB,KAAKqtB,KAAK,CAAC9iB,IAAI,CACjC,GAAG4iB,kBAAkB7sB,qBAAqB;gCAE9C;gCAEA,MAAMitB,OAAO1nB,YAAYonB;gCAEzBrhB,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;oCACzCghB,eAAeD,KAAKE,MAAM;oCAC1BC,gBAAgBH,KAAKjZ,OAAO;oCAC5BqZ,eAAe9R,kBACX8I,oBACE/b,cAAcglB,gBAAgB,GAC9BhlB,cAAcilB,MAAM,GACtBpa;oCACJqa,iBAAiBnJ;oCACjBoJ,uBAAuBnB;oCACvBoB,0BAA0BlC,aAAalF,UAAU;oCACjDqH,sBAAsBnC,aAAaE,MAAM;oCACzC3f,UAAUzC;oCACVwjB;oCACAE;oCACAY,aAAaxkB;gCACf;4BACF,OAAO;gCACL8iB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpBtL,UAAUqF,GAAG,CAACha,MAAMC,QAAQ,EAAE;oCAC5B,GAAI0U,UAAU2J,GAAG,CAACte,MAAMC,QAAQ,CAAC;oCACjCoY,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAAC2H,qBAAqBlpB,eAAesG,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAAC+a,mBAAmB;gCACtBjY,cAAc2C,IAAI,CAAC;oCACjB7C,UAAU5C;oCACVkd,iBAAiBld;oCACjBmd,qBAAqBtT;oCACrBuT,cACEhG,cAAc6J,GAAG,CAAC3F,oBAClBxc,aAAaylB,SAAS;oCACxBjH,oBAAoBzT;gCACtB;4BACF;4BAEA,KAAK,MAAMlH,SAASG,cAAe;oCAGhB+e,0BAwFMlf;gCA1FvB,MAAM4gB,kBAAkB1pB,kBAAkB8I,MAAMC,QAAQ;gCAExD,MAAMygB,YAAWxB,2BAAAA,aAAaM,MAAM,CAAClB,GAAG,CACtCte,MAAMC,QAAQ,sBADCif,yBAEdwB,QAAQ;gCAEX,MAAMnB,eAAeH,gBAAgBpf,MAAMC,QAAQ;gCAEnD,IAAI4gB,YAA2B;gCAC/B,IAAI,CAACX,mBAAmB;oCACtBW,YAAYptB,KAAKqtB,KAAK,CAAC9iB,IAAI,CAAC,GAAG4iB,kBAAkB5sB,YAAY;gCAC/D;gCAEA,IAAI+sB;gCACJ,IAAI,CAACb,qBAAqB5Q,iBAAiB;oCACzCyR,oBAAoBttB,KAAKqtB,KAAK,CAAC9iB,IAAI,CACjC,GAAG4iB,kBAAkB7sB,qBAAqB;gCAE9C;gCAEA,IAAI,CAACmsB,sBAAqBQ,4BAAAA,SAAUmB,YAAY,GAAE;oCAChD,MAAMC,eAAerS,eAAetP,aAAa,CAACuY,IAAI,CACpD,CAAC3I,IAAMA,EAAE1S,IAAI,KAAKA;oCAEpB,IAAI,CAACykB,cAAc;wCACjB,MAAM,qBAAoC,CAApC,IAAInX,MAAM,4BAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAmC;oCAC3C;oCAEAmX,aAAaC,yBAAyB,KAAK,EAAE;oCAC7C,KAAK,MAAMC,eAAetB,SAASmB,YAAY,CAAE;wCAC/C,MAAMI,SAAStlB,8BACbqD,MAAMC,QAAQ,EACd+hB;wCAEFF,aAAaC,yBAAyB,CAACjf,IAAI,CAACmf;oCAC9C;gCACF;gCAEAtN,UAAUqF,GAAG,CAACha,MAAMC,QAAQ,EAAE;oCAC5B,GAAI0U,UAAU2J,GAAG,CAACte,MAAMC,QAAQ,CAAC;oCACjCiiB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3CvB,cAAcvI;gCAChB;gCAEA,MAAMqC,eACJza,MAAMya,YAAY,IAAIte,aAAaylB,SAAS;gCAE9C,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAMO,uBACJ/J,qBAAqBqC,iBAAiBte,aAAa6e,SAAS,GACxDuE,eACArY;gCAEN,MAAMmB,WAAqBjM,4BACzBqe,cACAza,MAAMC,QAAQ;gCAGhB,MAAM+gB,OACJN,YACAtI,qBACAqC,iBAAiBte,aAAa6e,SAAS,GACnC1hB,YAAYonB,YACZ,CAAC;gCAEPrhB,kBAAkBc,aAAa,CAACH,MAAMC,QAAQ,CAAC,GAAG;oCAChDshB,iBAAiBnJ;oCACjBgJ,eAAe9R,kBACX8I,oBACE/b,cAAcglB,gBAAgB,GAC9BhlB,cAAcilB,MAAM,GACtBpa;oCACJsa,uBAAuBnB;oCACvB/iB,YAAYzI,oBACVkF,mBAAmBiG,MAAMC,QAAQ,EAAE;wCACjC1C,iBAAiB;oCACnB,GAAGE,EAAE,CAACC,MAAM;oCAEdmjB;oCACAxY;oCACA+Z,kBAAkB,EAAED,wCAAAA,qBAAsB9H,UAAU;oCACpDgI,cAAc,EAAEF,wCAAAA,qBAAsB1C,MAAM;oCAC5C6C,gBAAgBtB,KAAKE,MAAM;oCAC3BqB,iBAAiBvB,KAAKjZ,OAAO;oCAC7B4S,oBAAoB3a,MAAM2a,kBAAkB;oCAC5C6H,qBAAqBxiB,EAAAA,6BAAAA,MAAMwa,mBAAmB,qBAAzBxa,2BAA2BuI,MAAM,IAClDlL,OACA6J;oCACJub,gBAAgB,CAAC5B,YACb,OACAhsB,oBACEkF,mBAAmB8mB,WAAW;wCAC5BtjB,iBAAiB;wCACjBmlB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGllB,EAAE,CAACC,MAAM;oCAElBqjB;oCACA6B,wBAAwB,CAAC7B,oBACrB7Z,YACArS,oBACEkF,mBAAmBgnB,mBAAmB;wCACpCxjB,iBAAiB;wCACjBmlB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGllB,EAAE,CAACC,MAAM;oCAElBikB,aAAaxkB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAM0lB,mBAAmB,OACvBC,YACAzlB,MACAmF,MACAsc,OACAiE,KACAC,oBAAoB,KAAK;wBAEzB,OAAOhF,qBACJ3b,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZE,OAAO,GAAGA,KAAK,CAAC,EAAEugB,KAAK;4BACvB,MAAME,OAAOxvB,KAAKuK,IAAI,CAACgI,QAAQxD;4BAC/B,MAAMyL,WAAW9W,YACf2rB,YACAhlB,SACAoJ,WACA;4BAGF,MAAMgc,eAAezvB,KAClBuP,QAAQ,CACPvP,KAAKuK,IAAI,CAACF,SAASlI,mBACnBnC,KAAKuK,IAAI,CACPvK,KAAKuK,IAAI,CACPiQ,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5B6U,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACNrjB,GAAG,CAAC,IAAM,MACV/B,IAAI,CAAC,OAEVwE,OAGHtB,OAAO,CAAC,OAAO;4BAElB,IACE,CAAC4d,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhDhpB,CAAAA,oBAAoB+M,QAAQ,CAACxF,SAC7B,CAACmgB,sBAAsB3a,QAAQ,CAACxF,KAAI,GAGxC;gCACAuX,aAAa,CAACvX,KAAK,GAAG6lB;4BACxB;4BAEA,MAAMG,OAAO5vB,KAAKuK,IAAI,CAACF,SAASlI,kBAAkBstB;4BAClD,MAAMI,aACJjkB,kBAAkBge,cAAc,CAACxa,QAAQ,CAACxF;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAAC4S,QAAQ+S,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMpwB,GAAG+P,KAAK,CAACxP,KAAKyP,OAAO,CAACmgB,OAAO;oCAAElgB,WAAW;gCAAK;gCACrD,MAAMjQ,GAAGqwB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAIpT,QAAQ,CAAC6O,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOlK,aAAa,CAACvX,KAAK;4BAC5B;4BAEA,IAAI4S,MAAM;gCACR,IAAI+S,mBAAmB;gCAEvB,MAAMQ,YAAYnmB,SAAS,MAAM5J,KAAKgwB,OAAO,CAACjhB,QAAQ;gCACtD,MAAMkhB,sBAAsBR,aAAaC,KAAK,CAC5C,SAAS5a,MAAM;gCAGjB,KAAK,MAAMyW,UAAU/O,KAAK1Q,OAAO,CAAE;oCACjC,MAAMokB,UAAU,CAAC,CAAC,EAAE3E,SAAS3hB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACEyhB,SACAzf,kBAAkBge,cAAc,CAACxa,QAAQ,CAAC8gB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsBnwB,KACzBuK,IAAI,CACH,SACAghB,SAASwE,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BnmB,SAAS,MAAM,KAAKqmB,qBAErBxiB,OAAO,CAAC,OAAO;oCAElB,MAAM2iB,cAAcpwB,KAAKuK,IAAI,CAC3BgI,QACAgZ,SAASwE,WACTnmB,SAAS,MAAM,KAAKmF;oCAEtB,MAAMshB,cAAcrwB,KAAKuK,IAAI,CAC3BF,SACAlI,kBACAguB;oCAGF,IAAI,CAAC9E,OAAO;wCACVlK,aAAa,CAAC+O,QAAQ,GAAGC;oCAC3B;oCACA,MAAM1wB,GAAG+P,KAAK,CAACxP,KAAKyP,OAAO,CAAC4gB,cAAc;wCACxC3gB,WAAW;oCACb;oCACA,MAAMjQ,GAAGqwB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAO/F,qBACJ3b,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAM2gB,OAAOxvB,KAAKuK,IAAI,CACpBF,SACA,UACA,OACA;4BAEF,MAAM8lB,sBAAsBnwB,KACzBuK,IAAI,CAAC,SAAS,YACdkD,OAAO,CAAC,OAAO;4BAElB,IAAIlO,WAAWiwB,OAAO;gCACpB,MAAM/vB,GAAGkQ,QAAQ,CACf6f,MACAxvB,KAAKuK,IAAI,CAACF,SAAS,UAAU8lB;gCAE/BhP,aAAa,CAAC,OAAO,GAAGgP;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAI7F,iBAAiB;wBACnB,MAAMgG;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAACvV,eAAe,CAACC,aAAamO,mBAAmB;4BACnD,MAAMiG,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAIjF,qBAAqB;wBACvB,MAAMiF,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAMxlB,QAAQwgB,cAAe;wBAChC,MAAMiB,QAAQtf,SAASke,GAAG,CAACrgB;wBAC3B,MAAM2mB,sBAAsBhQ,uBAAuB0J,GAAG,CAACrgB;wBACvD,MAAM6c,YAAYnjB,eAAesG;wBACjC,MAAM4mB,SAAS9P,eAAeuJ,GAAG,CAACrgB;wBAClC,MAAMmF,OAAOtL,kBAAkBmG;wBAE/B,MAAM6mB,WAAWvP,UAAU2J,GAAG,CAACjhB;wBAC/B,MAAM8mB,eAAejF,aAAakF,MAAM,CAAC9F,GAAG,CAACjhB;wBAC7C,IAAI6mB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAASzL,aAAa,EAAE;gCAC1ByL,SAAS5I,gBAAgB,GAAG4I,SAASzL,aAAa,CAAC1Y,GAAG,CACpD,CAACkO;oCACC,MAAMsE,WAAW4R,aAAaE,eAAe,CAAC/F,GAAG,CAACrQ;oCAClD,IAAI,OAAOsE,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAI5H,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAO4H;gCACT;4BAEJ;4BACA2R,SAAS7I,YAAY,GAAG8I,aAAaE,eAAe,CAAC/F,GAAG,CAACjhB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMinB,gBAAgB,CAAExF,CAAAA,SAAS5E,aAAa,CAAC8J,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiBxlB,MAAMA,MAAMmF,MAAMsc,OAAO;wBAClD;wBAEA,IAAImF,UAAW,CAAA,CAACnF,SAAUA,SAAS,CAAC5E,SAAS,GAAI;4BAC/C,MAAMqK,UAAU,GAAG/hB,KAAK,IAAI,CAAC;4BAC7B,MAAMqgB,iBAAiBxlB,MAAMknB,SAASA,SAASzF,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAM+D,iBAAiBxlB,MAAMknB,SAASA,SAASzF,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAAC5E,WAAW;gCACd,MAAM2I,iBAAiBxlB,MAAMA,MAAMmF,MAAMsc,OAAO;gCAEhD,IAAI7O,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAM+O,UAAU/O,KAAK1Q,OAAO,CAAE;wCACjC,MAAMilB,aAAa,CAAC,CAAC,EAAExF,SAAS3hB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAMkiB,eAAeH,gBAAgBoF;wCAErCnlB,kBAAkBO,MAAM,CAAC4kB,WAAW,GAAG;4CACrC/C,0BAA0BlC,aAAalF,UAAU;4CACjDqH,sBAAsBnC,aAAaE,MAAM;4CACzC8B,iBAAiBra;4CACjBka,eAAela;4CACfpH,UAAU;4CACV+gB,WAAWptB,KAAKqtB,KAAK,CAAC9iB,IAAI,CACxB,eACAsB,SACA,GAAGkD,KAAK,KAAK,CAAC;4CAEhBue,mBAAmB7Z;4CACnBya,aAAaxkB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAMoiB,eAAeH,gBAAgB/hB;oCAErCgC,kBAAkBO,MAAM,CAACvC,KAAK,GAAG;wCAC/BokB,0BAA0BlC,aAAalF,UAAU;wCACjDqH,sBAAsBnC,aAAaE,MAAM;wCACzC8B,iBAAiBra;wCACjBka,eAAela;wCACfpH,UAAU;wCACV+gB,WAAWptB,KAAKqtB,KAAK,CAAC9iB,IAAI,CACxB,eACAsB,SACA,GAAGkD,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7Cue,mBAAmB7Z;wCACnBya,aAAaxkB;oCACf;gCACF;gCACA,IAAI+mB,UAAU;oCACZA,SAAS9I,mBAAmB,GAAGgE,gBAAgB/hB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAM2C,SAASqU,gBAAgBiK,GAAG,CAACjhB,SAAS,EAAE,CAAE;oCACnD,MAAMonB,WAAWvtB,kBAAkB8I,MAAMC,QAAQ;oCACjD,MAAM4iB,iBACJxlB,MACA2C,MAAMC,QAAQ,EACdwkB,UACA3F,OACA,QACA;oCAEF,MAAM+D,iBACJxlB,MACA2C,MAAMC,QAAQ,EACdwkB,UACA3F,OACA,QACA;oCAGF,IAAImF,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJxlB,MACAknB,SACAA,SACAzF,OACA,QACA;wCAEF,MAAM+D,iBACJxlB,MACAknB,SACAA,SACAzF,OACA,QACA;oCAEJ;oCAEA,MAAMS,eAAeH,gBAAgBpf,MAAMC,QAAQ;oCAEnDZ,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;wCACzCwhB,0BAA0BlC,aAAalF,UAAU;wCACjDqH,sBAAsBnC,aAAaE,MAAM;wCACzC8B,iBAAiBra;wCACjBka,eAAela;wCACfpH,UAAUzC;wCACVwjB,WAAWptB,KAAKqtB,KAAK,CAAC9iB,IAAI,CACxB,eACAsB,SACA,GAAGpI,kBAAkB8I,MAAMC,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7C8gB,mBAAmB7Z;wCACnBya,aAAaxkB;oCACf;oCAEA,IAAI+mB,UAAU;wCACZA,SAAS9I,mBAAmB,GAAGmE;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAMrsB,GAAGwxB,EAAE,CAAC1e,QAAQ;wBAAE7C,WAAW;wBAAMwhB,OAAO;oBAAK;oBACnD,MAAM7lB,cAAc2S,mBAAmBmD;gBACzC;gBAEA,sEAAsE;gBACtE,sBAAsB;gBACtB,MAAMjT,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMxD,cAAc0Q,oBAAoBC;YAC1D;YAEA,MAAMmV,mBAAmBnsB,cAAc;YACvC,IAAIosB,qBAAqBpsB,cAAc,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxC0c,OAAOlP,GAAG;YAEV,MAAM6e,cAAc9f,QAAQqQ,MAAM,CAACD;YACnCxM,UAAUS,MAAM,CACd7R,mBAAmBgU,YAAY;gBAC7BsH,mBAAmBgS,WAAW,CAAC,EAAE;gBACjCC,iBAAiB7iB,YAAYgW,IAAI;gBACjC8M,sBAAsBxlB,SAAS0Y,IAAI;gBACnC+M,sBAAsB7Q,iBAAiB8D,IAAI;gBAC3CgN,cACE1Z,WAAWjD,MAAM,GAChBrG,CAAAA,YAAYgW,IAAI,GAAG1Y,SAAS0Y,IAAI,GAAG9D,iBAAiB8D,IAAI,AAAD;gBAC1DiN,cAAcvI;gBACdwI,oBACE1P,CAAAA,gCAAAA,aAAc7S,QAAQ,CAAC,uBAAsB;gBAC/CwiB,eAAend,iBAAiBK,MAAM;gBACtC+c,cAAcvd,QAAQQ,MAAM;gBAC5Bgd,gBAAgBtd,UAAUM,MAAM,GAAG;gBACnCid,qBAAqBzd,QAAQlI,MAAM,CAAC,CAACkQ,IAAW,CAAC,CAACA,EAAE2N,GAAG,EAAEnV,MAAM;gBAC/Dkd,sBAAsBvd,iBAAiBrI,MAAM,CAAC,CAACkQ,IAAW,CAAC,CAACA,EAAE2N,GAAG,EAC9DnV,MAAM;gBACTmd,uBAAuBzd,UAAUpI,MAAM,CAAC,CAACkQ,IAAW,CAAC,CAACA,EAAE2N,GAAG,EAAEnV,MAAM;gBACnEod,iBAAiBtZ,oBAAoB,IAAI;gBACzCgC;gBACAuF;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAIlZ,iBAAiB+qB,cAAc,EAAE;gBACnC,MAAM/b,SAASnS,uBACbmD,iBAAiB+qB,cAAc,CAACC,MAAM;gBAExCjd,UAAUS,MAAM,CAACQ;gBACjBjB,UAAUS,MAAM,CACdxR,qCACEgD,iBAAiB+qB,cAAc,CAACE,6BAA6B;gBAGjE,MAAMC,kBAAkBlrB,iBAAiB+qB,cAAc,CAACG,eAAe;gBAEvE,KAAK,MAAM,CAAC9Q,KAAK8G,MAAM,IAAIrc,OAAOC,OAAO,CAAComB,iBAAkB;oBAC1Dnd,UAAUS,MAAM,CACd3R,uBAAuB;wBACrB;4BACEsT,aAAaiK;4BACbhK,iBAAiB8Q;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAIvc,SAAS0Y,IAAI,GAAG,KAAK9V,QAAQ;oBAmDpB1B;gBAlDX6c,mBAAmBE,OAAO,CAAC,CAACuI;oBAC1B,MAAMpF,kBAAkB1pB,kBAAkB8uB;oBAC1C,MAAMnF,YAAYptB,KAAKqtB,KAAK,CAAC9iB,IAAI,CAC/B,eACAsB,SACA,GAAGshB,gBAAgB,KAAK,CAAC;oBAG3BvhB,kBAAkBc,aAAa,CAAC6lB,SAAS,GAAG;wBAC1C1oB,YAAYzI,oBACVkF,mBAAmBisB,UAAU;4BAC3BzoB,iBAAiB;wBACnB,GAAGE,EAAE,CAACC,MAAM;wBAEd6jB,iBAAiBra;wBACjBka,eAAela;wBACf2Z;wBACAxY,UAAU4L,yBAAyByJ,GAAG,CAACsI,YACnC,OACAhS,uBAAuB0J,GAAG,CAACsI,YACzB,GAAGpF,gBAAgB,KAAK,CAAC,GACzB;wBACNwB,oBAAoBlb;wBACpBmb,gBAAgBnb;wBAChBsb,qBAAqBtb;wBACrByT,oBAAoBzT;wBACpBub,gBAAgB5tB,oBACdkF,mBAAmB8mB,WAAW;4BAC5BtjB,iBAAiB;4BACjBmlB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGllB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7CqjB,mBAAmB7Z;wBACnB0b,wBAAwB1b;wBACxBya,aAAaxkB;oBACf;gBACF;gBAEAtC,iBAAiB0R,aAAa,GAAGD,aAAaC,aAAa;gBAC3D1R,iBAAiBorB,mBAAmB,GAClCvlB,OAAO+C,YAAY,CAACwiB,mBAAmB;gBACzCprB,iBAAiBqrB,2BAA2B,GAC1CxlB,OAAO+C,YAAY,CAACyiB,2BAA2B;gBAEjD,MAAM/mB,uBAAuBrB,SAASuB;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9CvB;oBACAwB;oBACAC,OAAO,GAAEmB,eAAAA,OAAOuP,IAAI,qBAAXvP,aAAanB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAMJ,uBAAuBrB,SAAS;oBACpC0D,SAAS;oBACT5B,QAAQ,CAAC;oBACTO,eAAe,CAAC;oBAChBmd,SAAShR;oBACT+Q,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAM5c,oBAAoB3C,SAAS4C;YACnC,MAAM5B,cAAcrL,KAAKuK,IAAI,CAACF,SAASzI,gBAAgB;gBACrDmM,SAAS;gBACT2kB,kBAAkB,OAAOzlB,OAAOwd,aAAa,KAAK;gBAClDkI,qBAAqB1lB,OAAO2lB,aAAa,KAAK;gBAC9C1Q,qBAAqBA,wBAAwB;YAC/C;YACA,MAAMziB,GAAG8sB,MAAM,CAACvsB,KAAKuK,IAAI,CAACF,SAAS1I,gBAAgBke,KAAK,CAAC,CAAC7I;gBACxD,IAAIA,IAAIC,IAAI,KAAK,UAAU;oBACzB,OAAO4H,QAAQnO,OAAO;gBACxB;gBACA,OAAOmO,QAAQ2K,MAAM,CAACxS;YACxB;YAEA,IAAIN,QAAQzJ,OAAO+C,YAAY,CAAC0Z,iBAAiB,GAAG;gBAClD,MAAMxb,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAMtN,qBACJsQ,KACA7R,KAAKuK,IAAI,CAACF,SAAS3I;gBAEvB;YACJ;YAEA,MAAMwc;YAEN,IAAIkT,oBAAoB;gBACtBA,mBAAmBlJ,cAAc;gBACjCkJ,qBAAqB3d;YACvB;YAEA,IAAIL,eAAe;gBACjBrO,IAAIqP,IAAI,CACN,CAAC,yGAAyG,CAAC;YAE/G;YAEA,IAAInH,OAAOmW,MAAM,KAAK,UAAU;gBAC9B,MAAMxR,uBACJ3E,QACA4E,KACAC,oBACAC,cACA7D;YAEJ;YAEA,IAAIjB,OAAOmW,MAAM,KAAK,cAAc;gBAClC,MAAMnV,yBACJC,eACA7D,SACA8D,UACAC,sBACAC,uBACA+Z,6BACA9Z,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAIwiB,kBAAkBA,iBAAiBjJ,cAAc;YACrDvd,QAAQC,GAAG;YAEX,IAAIiI,aAAa;gBACf3E,cACGU,UAAU,CAAC,uBACXmF,OAAO,CAAC,IAAMxO,kBAAkB;wBAAEiP;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMpG,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7DrJ,cAAc2I,UAAU+S,WAAW;oBACjC2R,UAAUxoB;oBACVwB,SAASA;oBACTwJ;oBACA8T;oBACAvR,gBAAgB3K,OAAO2K,cAAc;oBACrCyJ;oBACAD;oBACA9S;oBACAuV,UAAU5W,OAAO+C,YAAY,CAAC6T,QAAQ;gBACxC;YAGF,MAAM3V,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMsG,UAAUkC,KAAK;YAErC,MAAMuH;QACR;IACF,EAAE,OAAOkU,GAAG;QACV,MAAM3d,YAAmC3L,aAAaqhB,GAAG,CAAC;QAC1D,IAAI1V,WAAW;YACbA,UAAUS,MAAM,CACdtR,iBAAiB;gBACf8a,SAASW,uBAAuB9M;gBAChC8f,WAAWC,yBAAyBF;gBACpCzT,mBAAmBlP,KAAKG,KAAK,CAAC,AAACgD,CAAAA,KAAKC,GAAG,KAAKF,cAAa,IAAK;YAChE;QAEJ;QACA,MAAMyf;IACR,SAAU;QACR,kDAAkD;QAClD,MAAM1sB,qBAAqB6sB,GAAG;QAE9B,IAAIhgB,eAAe,CAAC1B,QAAQD,GAAG,CAAC4hB,gBAAgB,EAAE;YAChDC,yBAAyB3f;QAC3B;QAEA,6DAA6D;QAC7D,MAAMtO;QACNmB;QAEA,IAAI8M,kBAAkBK,cAAc;YAClCjL,YAAY;gBACV4K;gBACAigB,MAAM;gBACNC,YAAYxhB;gBACZxH,SAASmJ,aAAanJ,OAAO;gBAC7BipB,gBAAgBrgB;gBAChBsgB,MAAM;YACR;QACF;IACF;AACF;AAEA,SAASJ,yBAAyBlmB,MAA2B;IAC3D,IAAIumB,aACF,CAAC,8CAA8C,CAAC,GAChDr0B,KACE,CAAC,yEAAyE,CAAC;IAE/Eq0B,cACE,WACAr0B,KACE;IAEJq0B,cACE;IAEF,IAAI,EAACvmB,0BAAAA,OAAQ+C,YAAY,CAACyjB,0BAA0B,GAAE;QACpDD,cACE;IACJ;IAEAA,cACE;IACFA,cACE;IAEFzuB,IAAI+F,IAAI,CAAC0oB;AACX;AAEA,SAASzT,uBAAuB9M,WAAoB;IAClD,IAAIA,aAAa;QACf,OAAO;IACT;IAEA,IAAI1B,QAAQD,GAAG,CAACoiB,WAAW,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASV,yBAAyBhc,GAAY;IAC5C,MAAMC,OAAOxN,qBAAqBuN;IAClC,IAAIC,QAAQ,MAAM;QAChB,OAAOA;IACT;IAEA,IAAID,eAAeE,SAAS,UAAUF,OAAO,OAAOA,IAAIC,IAAI,KAAK,UAAU;QACzE,OAAOD,IAAIC,IAAI;IACjB;IAEA,IAAID,eAAeE,OAAO;QACxB,OAAOF,IAAI2c,IAAI;IACjB;IAEA,OAAO;AACT"}