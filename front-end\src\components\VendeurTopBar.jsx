import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  Divider,
  Tooltip,
  styled,
  useTheme
} from '@mui/material';
import {
  AccountCircle as AccountCircleIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Add as AddIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  Storefront as StorefrontIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

// Styled components
const ActionButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.text.primary,
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 2px 6px rgba(0,0,0,0.08)',
  marginLeft: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const VendeurTopBar = ({ title, darkMode, toggleDarkMode, user }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);
  const [newDocumentAnchorEl, setNewDocumentAnchorEl] = useState(null);

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNewDocumentOpen = (event) => {
    setNewDocumentAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNewDocumentClose = () => {
    setNewDocumentAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
    handleMenuClose();
  };

  const handleProfileClick = () => {
    navigate("/vendeur/profile");
    handleMenuClose();
  };

  const handleSettingsClick = () => {
    navigate("/vendeur/parametres");
    handleMenuClose();
  };

  const handleNewInvoice = () => {
    navigate("/vendeur/factures/new");
    handleNewDocumentClose();
  };

  const handleNewQuote = () => {
    navigate("/vendeur/devis/new");
    handleNewDocumentClose();
  };



  return (
    <AppBar
      position="sticky"
      color="inherit"
      elevation={0}
      sx={{
        borderBottom: `1px solid ${theme.palette.divider}`,
        backgroundColor: theme.palette.background.paper,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {!title && (
            <Avatar
              sx={{
                bgcolor: theme.palette.primary.main,
                color: "#fff",
                width: 32,
                height: 32,
                mr: 1.5,
                boxShadow: "0 2px 8px rgba(58, 110, 165, 0.2)",
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <StorefrontIcon sx={{ fontSize: '1.2rem' }} />
            </Avatar>
          )}
          <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
            {title || "Espace Vendeur"}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Tooltip title="Nouveau document">
            <ActionButton
              onClick={handleNewDocumentOpen}
              size="medium"
            >
              <AddIcon fontSize="small" />
            </ActionButton>
          </Tooltip>



          <Tooltip title="Mode sombre">
            <ActionButton onClick={toggleDarkMode} size="medium">
              {darkMode ? <Brightness7Icon fontSize="small" /> : <Brightness4Icon fontSize="small" />}
            </ActionButton>
          </Tooltip>

          <Tooltip title="Profil">
            <ActionButton
              edge="end"
              onClick={handleProfileMenuOpen}
              size="medium"
            >
              {user?.avatar ? (
                <Avatar
                  src={user.avatar}
                  alt={user.nom}
                  sx={{ width: 28, height: 28 }}
                />
              ) : (
                <AccountCircleIcon fontSize="small" />
              )}
            </ActionButton>
          </Tooltip>
        </Box>

        {/* Profile Menu */}
        <Menu
          anchorEl={anchorEl}
          id="account-menu"
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            elevation: 2,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
              mt: 1.5,
              width: 200,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
            },
          }}
        >
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              {user?.nom || 'Vendeur'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {user?.email || '<EMAIL>'}
            </Typography>
          </Box>
          <Divider />
          <MenuItem onClick={handleProfileClick}>
            <ListItemIcon>
              <PersonIcon fontSize="small" />
            </ListItemIcon>
            Mon profil
          </MenuItem>
          <MenuItem onClick={handleSettingsClick}>
            <ListItemIcon>
              <SettingsIcon fontSize="small" />
            </ListItemIcon>
            Paramètres
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleLogout}>
            <ListItemIcon>
              <LogoutIcon fontSize="small" />
            </ListItemIcon>
            Déconnexion
          </MenuItem>
        </Menu>



        {/* New Document Menu */}
        <Menu
          anchorEl={newDocumentAnchorEl}
          id="new-document-menu"
          open={Boolean(newDocumentAnchorEl)}
          onClose={handleNewDocumentClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            elevation: 2,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
              mt: 1.5,
              width: 200,
            },
          }}
        >
          <MenuItem onClick={handleNewInvoice}>
            <ListItemIcon>
              <AddIcon fontSize="small" />
            </ListItemIcon>
            Nouvelle facture
          </MenuItem>
          <MenuItem onClick={handleNewQuote}>
            <ListItemIcon>
              <AddIcon fontSize="small" />
            </ListItemIcon>
            Nouveau devis
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default VendeurTopBar;
