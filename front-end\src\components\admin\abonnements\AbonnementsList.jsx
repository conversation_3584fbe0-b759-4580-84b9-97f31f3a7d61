import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
  useTheme,
  Alert,
  Snackbar
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import abonnementService from '../../../services/abonnementService';
import RenewalRequestsList from './RenewalRequestsList';

const AbonnementsList = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [abonnements, setAbonnements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [abonnementToDelete, setAbonnementToDelete] = useState(null);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('error');

  useEffect(() => {
    fetchAbonnements();
  }, []);

  const fetchAbonnements = async () => {
    try {
      setLoading(true);
      const data = await abonnementService.getAbonnements();
      setAbonnements(data);
    } catch (error) {
      setAlertMessage(error.message || 'Erreur lors de la récupération des abonnements');
      setAlertSeverity('error');
      setAlertOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleAddAbonnement = () => {
    navigate('/admin/abonnements/create');
  };

  const handleEditAbonnement = (id) => {
    navigate(`/admin/abonnements/edit/${id}`);
  };

  const handleDeleteClick = (abonnement) => {
    setAbonnementToDelete(abonnement);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await abonnementService.deleteAbonnement(abonnementToDelete._id);
      setAlertMessage('Abonnement supprimé avec succès');
      setAlertSeverity('success');
      setAlertOpen(true);
      fetchAbonnements();
    } catch (error) {
      setAlertMessage(error.message || 'Erreur lors de la suppression de l\'abonnement');
      setAlertSeverity('error');
      setAlertOpen(true);
    } finally {
      setDeleteDialogOpen(false);
      setAbonnementToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setAbonnementToDelete(null);
  };

  // Formater la date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  // Déterminer la couleur du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIF':
        return theme.palette.success.main;
      case 'SUSPENDU':
        return theme.palette.warning.main;
      case 'EXPIRE':
      case 'ANNULE':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };



  // Traduire la durée en français
  const translateDuree = (duree) => {
    switch (duree) {
      case '3_MOIS':
        return '3 mois';
      case '6_MOIS':
        return '6 mois';
      case '1_AN':
      case 'ANNEE':
        return '1 an';
      default:
        return duree;
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Typography variant="h4" component="h1" gutterBottom>
              Gestion des abonnements
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddAbonnement}
            >
              Nouvel abonnement
            </Button>
          </Grid>
        </Grid>

        {/* Liste des demandes de renouvellement */}
        <RenewalRequestsList />

        <Card sx={{ mt: 3 }}>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Entreprise</TableCell>
                        <TableCell>Responsable</TableCell>
                        <TableCell>Durée</TableCell>
                        <TableCell>Date de début</TableCell>
                        <TableCell>Date de fin</TableCell>
                        <TableCell>Statut</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {abonnements
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((abonnement) => (
                          <TableRow key={abonnement._id}>
                            <TableCell>{abonnement.entrepriseId?.nom || 'N/A'}</TableCell>
                            <TableCell>
                              {abonnement.responsableId?.prenom} {abonnement.responsableId?.nom}
                            </TableCell>
                            <TableCell>{translateDuree(abonnement.duree)}</TableCell>
                            <TableCell>{formatDate(abonnement.dateDebut)}</TableCell>
                            <TableCell>{formatDate(abonnement.dateFin)}</TableCell>
                            <TableCell>
                              <Box
                                sx={{
                                  backgroundColor: getStatusColor(abonnement.statut),
                                  color: '#fff',
                                  borderRadius: '4px',
                                  padding: '4px 8px',
                                  display: 'inline-block',
                                  fontWeight: 'bold'
                                }}
                              >
                                {abonnement.statut}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <IconButton
                                color="primary"
                                onClick={() => handleEditAbonnement(abonnement._id)}
                              >
                                <EditIcon />
                              </IconButton>
                              <IconButton
                                color="error"
                                onClick={() => handleDeleteClick(abonnement)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  component="div"
                  count={abonnements.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage="Lignes par page"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count}`}
                />
              </>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Dialog de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer cet abonnement ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Annuler
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar pour les alertes */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={() => setAlertOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setAlertOpen(false)}
          severity={alertSeverity}
          sx={{ width: '100%' }}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default AbonnementsList;
