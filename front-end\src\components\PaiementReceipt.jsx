import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Grid,
  Button,
  useTheme
} from '@mui/material';
import {
  Receipt as ReceiptIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import { formatCurrency, formatDate } from '../utils/formatters';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import logo from '../assets/logo.png';

const PaiementReceipt = ({ paiement, facture, entreprise, onClose, onPrint, onDownload, onEmail }) => {
  const theme = useTheme();

  const getPaymentModeLabel = (mode) => {
    switch (mode) {
      case 'BANK_TRANSFER':
        return 'Virement bancaire';
      case 'CHECK':
        return 'Chèque';
      case 'CASH':
        return 'Espèces';
      default:
        return mode;
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'Complété';
      case 'PENDING':
        return 'En attente';
      case 'FAILED':
        return 'Échoué';
      case 'REFUNDED':
        return 'Remboursé';
      default:
        return status;
    }
  };

  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      window.print();
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      generatePDF();
    }
  };

  const handleEmail = () => {
    if (onEmail) {
      onEmail();
    }
  };

  const generatePDF = () => {
    const doc = new jsPDF();

    // Add logo
    try {
      doc.addImage(logo, 'PNG', 15, 10, 50, 20);
    } catch (error) {
      console.error('Error adding logo:', error);
    }

    // Add title
    doc.setFontSize(22);
    doc.setTextColor(theme.palette.primary.main);
    doc.text('REÇU DE PAIEMENT', 105, 20, { align: 'center' });

    // Add receipt number and date
    doc.setFontSize(10);
    doc.setTextColor(100);
    doc.text(`Reçu N°: ${paiement._id.substring(0, 8).toUpperCase()}`, 200, 30, { align: 'right' });
    doc.text(`Date: ${formatDate(paiement.datePaiement)}`, 200, 35, { align: 'right' });

    // Add divider
    doc.setDrawColor(200);
    doc.line(15, 40, 195, 40);

    // Add company info
    doc.setFontSize(12);
    doc.setTextColor(0);
    doc.text('De:', 15, 50);
    doc.setFontSize(10);
    doc.text(entreprise?.nom || 'Votre Entreprise', 15, 55);
    doc.text(entreprise?.adresse || '123 Rue Principale', 15, 60);
    doc.text(entreprise?.codePostal + ' ' + entreprise?.ville || '75000 Paris', 15, 65);
    doc.text(entreprise?.email || '<EMAIL>', 15, 70);
    doc.text(entreprise?.telephone || '+33 1 23 45 67 89', 15, 75);

    // Add client info
    doc.setFontSize(12);
    doc.text('À:', 120, 50);
    doc.setFontSize(10);
    doc.text(facture?.clientId?.nom || 'Client', 120, 55);
    doc.text(facture?.clientId?.adresse || 'Adresse du client', 120, 60);
    doc.text((facture?.clientId?.codePostal || '') + ' ' + (facture?.clientId?.ville || ''), 120, 65);
    doc.text(facture?.clientId?.email || '<EMAIL>', 120, 70);
    doc.text(facture?.clientId?.telephone || '', 120, 75);

    // Add payment details
    doc.setFontSize(14);
    doc.setTextColor(theme.palette.primary.main);
    doc.text('Détails du paiement', 15, 90);

    doc.setFontSize(10);
    doc.setTextColor(0);

    const tableColumn = ['Description', 'Détails'];
    const tableRows = [
      ['Facture', facture?.numero || 'N/A'],
      ['Montant', formatCurrency(paiement.montant)],
      ['Mode de paiement', getPaymentModeLabel(paiement.modePaiement)],
      ['Date de paiement', formatDate(paiement.datePaiement)],
      ['Référence', paiement.reference || 'N/A'],
      ['Statut', getStatusLabel(paiement.statut)],
    ];

    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 95,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 6,
        lineColor: [200, 200, 200],
      },
      headStyles: {
        fillColor: [theme.palette.primary.main],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
    });

    // Add thank you note
    const finalY = doc.lastAutoTable.finalY + 20;
    doc.setFontSize(12);
    doc.setTextColor(theme.palette.primary.main);
    doc.text('Merci pour votre paiement!', 105, finalY, { align: 'center' });

    // Add footer
    doc.setFontSize(8);
    doc.setTextColor(100);
    doc.text('Ce reçu a été généré automatiquement et ne nécessite pas de signature.', 105, finalY + 10, { align: 'center' });

    // Save the PDF
    doc.save(`Recu_Paiement_${paiement._id.substring(0, 8)}.pdf`);
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        maxWidth: 800,
        mx: 'auto',
        borderRadius: 2,
        '@media print': {
          boxShadow: 'none',
          border: 'none',
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ReceiptIcon sx={{ fontSize: 32, color: theme.palette.primary.main, mr: 1 }} />
          <Typography variant="h5" fontWeight="bold" color="primary">
            Reçu de Paiement
          </Typography>
        </Box>
        <Box>
          <Typography variant="subtitle2" color="text.secondary">
            Reçu N°: {paiement._id.substring(0, 8).toUpperCase()}
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Date: {formatDate(paiement.datePaiement)}
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ mb: 3 }} />

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            De:
          </Typography>
          <Typography variant="body2">{entreprise?.nom || 'Votre Entreprise'}</Typography>
          <Typography variant="body2">{entreprise?.adresse || '123 Rue Principale'}</Typography>
          <Typography variant="body2">{entreprise?.codePostal} {entreprise?.ville || '75000 Paris'}</Typography>
          <Typography variant="body2">{entreprise?.email || '<EMAIL>'}</Typography>
          <Typography variant="body2">{entreprise?.telephone || '+33 1 23 45 67 89'}</Typography>
        </Grid>
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            À:
          </Typography>
          <Typography variant="body2">{facture?.clientId?.nom || 'Client'}</Typography>
          <Typography variant="body2">{facture?.clientId?.adresse || 'Adresse du client'}</Typography>
          <Typography variant="body2">{facture?.clientId?.codePostal} {facture?.clientId?.ville}</Typography>
          <Typography variant="body2">{facture?.clientId?.email || '<EMAIL>'}</Typography>
          <Typography variant="body2">{facture?.clientId?.telephone}</Typography>
        </Grid>
      </Grid>

      <Typography variant="h6" color="primary" gutterBottom>
        Détails du paiement
      </Typography>

      <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" color="text.secondary">Facture</Typography>
            <Typography variant="body1">{facture?.numero || 'N/A'}</Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" color="text.secondary">Montant</Typography>
            <Typography variant="body1" fontWeight="bold">{formatCurrency(paiement.montant)}</Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" color="text.secondary">Mode de paiement</Typography>
            <Typography variant="body1">{getPaymentModeLabel(paiement.modePaiement)}</Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" color="text.secondary">Date de paiement</Typography>
            <Typography variant="body1">{formatDate(paiement.datePaiement)}</Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" color="text.secondary">Référence</Typography>
            <Typography variant="body1">{paiement.reference || 'N/A'}</Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" color="text.secondary">Statut</Typography>
            <Typography
              variant="body1"
              sx={{
                color: paiement.statut === 'COMPLETED' ? 'success.main' :
                       paiement.statut === 'PENDING' ? 'warning.main' :
                       paiement.statut === 'FAILED' ? 'error.main' : 'info.main'
              }}
            >
              {getStatusLabel(paiement.statut)}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Typography variant="h6" color="primary" gutterBottom>
          Merci pour votre paiement!
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Ce reçu a été généré automatiquement et ne nécessite pas de signature.
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, '@media print': { display: 'none' } }}>
        <Button
          variant="outlined"
          startIcon={<PrintIcon />}
          onClick={handlePrint}
        >
          Imprimer
        </Button>
        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleDownload}
        >
          Télécharger PDF
        </Button>
        <Button
          variant="outlined"
          startIcon={<EmailIcon />}
          onClick={handleEmail}
        >
          Envoyer par email
        </Button>
        {onClose && (
          <Button
            variant="outlined"
            color="secondary"
            onClick={onClose}
          >
            Fermer
          </Button>
        )}
      </Box>
    </Paper>
  );
};

export default PaiementReceipt;
