import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  useTheme,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  SupervisorAccount as SupervisorAccountIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { getAllUsers, deleteUser } from '../services/userService';

// Mock data for users
const mockUsers = [
  {
    id: 1,
    nom: '<PERSON>',
    email: 'jean.du<PERSON>@example.com',
    role: 'VENDEUR',
    dateCreation: '2023-05-15',
    telephone: '+33 6 12 34 56 78',
    status: 'active'
  },
  {
    id: 2,
    nom: 'Marie Martin',
    email: '<EMAIL>',
    role: 'VENDEUR',
    dateCreation: '2023-05-28',
    telephone: '+33 6 23 45 67 89',
    status: 'active'
  },
  {
    id: 3,
    nom: 'Entreprise ABC',
    email: '<EMAIL>',
    role: 'ENTREPRISE',
    dateCreation: '2023-06-10',
    telephone: '+33 1 23 45 67 89',
    status: 'active'
  },
  {
    id: 4,
    nom: 'Société XYZ',
    email: '<EMAIL>',
    role: 'ENTREPRISE',
    dateCreation: '2023-06-15',
    telephone: '+33 1 34 56 78 90',
    status: 'inactive'
  },
];

const UsersPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // State
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);

  // Fetch users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const data = await getAllUsers();
        setUsers(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching users:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors de la récupération des utilisateurs',
          severity: 'error'
        });
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle search
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Filter users based on search term and selected tab
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    if (tabValue === 0) return matchesSearch; // All users
    if (tabValue === 1) return matchesSearch && user.role === 'VENDEUR'; // Vendeurs only
    if (tabValue === 2) return matchesSearch && user.role === 'ENTREPRISE'; // Entreprises only

    return matchesSearch;
  });

  // Handle menu open
  const handleMenuOpen = (event, user) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  // Handle edit user
  const handleEditUser = () => {
    if (selectedUser) {
      navigate(`/admin/utilisateurs/edit/${selectedUser._id}`);
    }
    handleMenuClose();
  };

  // Handle delete dialog open
  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  // Handle delete user
  const handleDeleteUser = async () => {
    if (selectedUser) {
      try {
        setLoading(true);
        await deleteUser(selectedUser._id);

        // Update local state
        setUsers(users.filter(user => user._id !== selectedUser._id));

        // Show success message
        setSnackbar({
          open: true,
          message: `L'utilisateur ${selectedUser.nom} a été supprimé avec succès.`,
          severity: 'success'
        });
      } catch (error) {
        console.error('Error deleting user:', error);
        setSnackbar({
          open: true,
          message: `Erreur lors de la suppression de l'utilisateur: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    }

    handleDeleteDialogClose();
  };

  // Handle add user
  const handleAddUser = () => {
    navigate('/admin/utilisateurs/new');
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Get role label
  const getRoleLabel = (role) => {
    switch (role) {
      case 'ADMIN':
        return 'Administrateur';
      case 'VENDEUR':
        return 'Vendeur';
      case 'ENTREPRISE':
        return 'Entreprise';
      case 'USER':
        return 'Utilisateur';
      default:
        return role;
    }
  };

  // Get role color
  const getRoleColor = (role) => {
    switch (role) {
      case 'ADMIN':
        return 'error';
      case 'VENDEUR':
        return 'primary';
      case 'ENTREPRISE':
        return 'secondary';
      case 'USER':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      default:
        return 'default';
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Gestion des utilisateurs
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gérez les vendeurs et les entreprises de votre système.
          </Typography>
        </Box>

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
          sx={{ borderRadius: 2 }}
        >
          Ajouter un utilisateur
        </Button>
      </Box>

      {/* Tabs and Search */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{ mb: 3 }}
      >
        <Card elevation={1}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
              >
                <Tab
                  label="Tous"
                  icon={<SupervisorAccountIcon />}
                  iconPosition="start"
                  sx={{ minHeight: 48, py: 1 }}
                />
                <Tab
                  label="Vendeurs"
                  icon={<PersonIcon />}
                  iconPosition="start"
                  sx={{ minHeight: 48, py: 1 }}
                />
                <Tab
                  label="Entreprises"
                  icon={<BusinessIcon />}
                  iconPosition="start"
                  sx={{ minHeight: 48, py: 1 }}
                />
              </Tabs>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TextField
                  placeholder="Rechercher..."
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  sx={{ width: 250 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    )
                  }}
                />

              </Box>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Users Table */}
      <Card
        component={motion.div}
        variants={itemVariants}
        elevation={1}
        sx={{
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }
        }}
      >
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nom</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Rôle</TableCell>
                <TableCell>Téléphone</TableCell>
                <TableCell>Date d'ajout</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from(new Array(5)).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Box sx={{ height: 20, width: '70%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '90%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 24, width: 80, bgcolor: '#f0f0f0', borderRadius: 10 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '60%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ height: 20, width: '50%', bgcolor: '#f0f0f0', borderRadius: 1 }} />
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ height: 20, width: 20, bgcolor: '#f0f0f0', borderRadius: '50%', ml: 'auto' }} />
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredUsers.length === 0 ? (
                // No results
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                      Aucun utilisateur trouvé
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                // User rows
                filteredUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>{user.nom}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={getRoleLabel(user.role)}
                        color={getRoleColor(user.role)}
                        size="small"
                        sx={{ fontWeight: 'medium' }}
                      />
                    </TableCell>
                    <TableCell>{user.telephone}</TableCell>
                    <TableCell>{user.dateCreation}</TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={(event) => handleMenuOpen(event, user)}
                      >
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleEditUser}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          Modifier
        </MenuItem>
        <MenuItem onClick={handleDeleteDialogOpen}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <Typography color="error">Supprimer</Typography>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer l'utilisateur "{selectedUser?.nom}" ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>Annuler</Button>
          <Button onClick={handleDeleteUser} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UsersPage;
