{"version": 3, "sources": ["../../../src/client/dev/on-demand-entries-client.ts"], "sourcesContent": ["import Router from '../router'\nimport { sendMessage } from '../components/react-dev-overlay/pages/websocket'\n\nexport default async (page?: string) => {\n  // Never send pings when using Turbopack as it's not used.\n  // Pings were originally used to keep track of active routes in on-demand-entries with webpack.\n  if (process.env.TURBOPACK) {\n    return\n  }\n  if (page) {\n    // in AMP the router isn't initialized on the client and\n    // client-transitions don't occur so ping initial page\n    setInterval(() => {\n      sendMessage(JSON.stringify({ event: 'ping', page }))\n    }, 2500)\n  } else {\n    Router.ready(() => {\n      setInterval(() => {\n        // when notFound: true is returned we should use the notFoundPage\n        // as the Router.pathname will point to the 404 page but we want\n        // to ping the source page that returned notFound: true instead\n        const notFoundSrcPage = self.__NEXT_DATA__.notFoundSrcPage\n        const pathname =\n          (Router.pathname === '/404' || Router.pathname === '/_error') &&\n          notFoundSrcPage\n            ? notFoundSrcPage\n            : Router.pathname\n\n        sendMessage(JSON.stringify({ event: 'ping', page: pathname }))\n      }, 2500)\n    })\n  }\n}\n"], "names": ["Router", "sendMessage", "page", "process", "env", "TURBOPACK", "setInterval", "JSON", "stringify", "event", "ready", "notFoundSrcPage", "self", "__NEXT_DATA__", "pathname"], "mappings": "AAAA,OAAOA,YAAY,YAAW;AAC9B,SAASC,WAAW,QAAQ,kDAAiD;AAE7E,eAAe,CAAA,OAAOC;IACpB,0DAA0D;IAC1D,+FAA+F;IAC/F,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB;IACF;IACA,IAAIH,MAAM;QACR,wDAAwD;QACxD,sDAAsD;QACtDI,YAAY;YACVL,YAAYM,KAAKC,SAAS,CAAC;gBAAEC,OAAO;gBAAQP;YAAK;QACnD,GAAG;IACL,OAAO;QACLF,OAAOU,KAAK,CAAC;YACXJ,YAAY;gBACV,iEAAiE;gBACjE,gEAAgE;gBAChE,+DAA+D;gBAC/D,MAAMK,kBAAkBC,KAAKC,aAAa,CAACF,eAAe;gBAC1D,MAAMG,WACJ,AAACd,CAAAA,OAAOc,QAAQ,KAAK,UAAUd,OAAOc,QAAQ,KAAK,SAAQ,KAC3DH,kBACIA,kBACAX,OAAOc,QAAQ;gBAErBb,YAAYM,KAAKC,SAAS,CAAC;oBAAEC,OAAO;oBAAQP,MAAMY;gBAAS;YAC7D,GAAG;QACL;IACF;AACF,CAAA,EAAC"}