// backend/models/ClientModel.js
const mongoose = require('mongoose');
const { Schema } = mongoose;

const clientSchema = new Schema({
  // Basic information
  nom: { type: String, required: true },
  adresse: { type: String, required: true },
  email: { type: String, required: true, unique: true, index: true }, // Ajout de la contrainte d'unicité
  contact: { type: String, required: true },
  telephone: { type: String }, // Ajout du champ téléphone
  cin: { type: String, required: true, unique: true, index: true }, // Numéro de Carte d'Identité Nationale

  // Logo
  logo: { type: String }, // URL to stored image

  // Assigned vendeur
  vendeurId: { type: Schema.Types.ObjectId, ref: 'users' },

  // Assigned responsables d'entreprise (multiple)
  responsables: [{ type: Schema.Types.ObjectId, ref: 'users' }],

  // Status and metadata
  actif: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Client', clientSchema);