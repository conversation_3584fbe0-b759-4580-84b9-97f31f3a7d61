import React, { useState } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  CalendarMonth as CalendarIcon,
  Refresh as RefreshIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Close as CloseIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import { format, startOfMonth, endOfMonth, startOfQuarter, endOfQuarter, startOfYear, endOfYear } from 'date-fns';
import { fr } from 'date-fns/locale';

// Predefined date ranges
const PREDEFINED_RANGES = [
  { id: 'thisMonth', label: '<PERSON> mois', color: '#4CAF50' },
  { id: 'thisQuarter', label: 'Ce trimestre', color: '#2196F3' },
  { id: 'thisYear', label: 'Cette année', color: '#9C27B0' },
  { id: 'allTime', label: 'Tout le temps', color: '#FF9800' },
];

// Function to get date range based on predefined range id
const getDateRangeFromId = (rangeId) => {
  const now = new Date();
  let startDate, endDate;

  switch (rangeId) {
    case 'thisMonth':
      startDate = startOfMonth(now);
      endDate = endOfMonth(now);
      break;
    case 'thisQuarter':
      startDate = startOfQuarter(now);
      endDate = endOfQuarter(now);
      break;
    case 'thisYear':
      startDate = startOfYear(now);
      endDate = endOfYear(now);
      break;
    case 'allTime':
      // Utiliser une date plus récente pour éviter les problèmes avec MongoDB
      startDate = new Date(2000, 0, 1);
      endDate = new Date(now.getFullYear() + 10, 11, 31);
      break;
    default:
      startDate = startOfMonth(now);
      endDate = endOfMonth(now);
  }

  // S'assurer que les dates sont au format ISO pour MongoDB
  return {
    startDate: new Date(startDate.setHours(0, 0, 0, 0)),
    endDate: new Date(endDate.setHours(23, 59, 59, 999))
  };
};

// Format date range for display
const formatDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return '';

  const formatStr = 'dd/MM/yyyy';
  return `${format(new Date(startDate), formatStr)} - ${format(new Date(endDate), formatStr)}`;
};

const SimpleDateFilter = ({
  onDateRangeChange,
  onRefresh,
  initialRange = 'thisMonth',
  showRefreshButton = true,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [customDialogOpen, setCustomDialogOpen] = useState(false);
  const [selectedRangeId, setSelectedRangeId] = useState(initialRange);
  const [dateRange, setDateRange] = useState(getDateRangeFromId(initialRange));
  const [startDate, setStartDate] = useState(dateRange.startDate);
  const [endDate, setEndDate] = useState(dateRange.endDate);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRangeSelect = (rangeId) => {
    setSelectedRangeId(rangeId);
    const newRange = getDateRangeFromId(rangeId);
    setDateRange(newRange);

    // Notify parent component
    if (onDateRangeChange) {
      onDateRangeChange(rangeId, newRange);
    }

    handleClose();
  };

  const handleCustomDialogOpen = () => {
    setCustomDialogOpen(true);
    handleClose();
  };

  const handleCustomDialogClose = () => {
    setCustomDialogOpen(false);
  };

  const handleCustomRangeApply = () => {
    // Créer des copies des dates pour éviter de modifier les objets originaux
    const formattedStartDate = new Date(startDate);
    const formattedEndDate = new Date(endDate);

    // S'assurer que les dates sont correctement formatées
    formattedStartDate.setHours(0, 0, 0, 0);
    formattedEndDate.setHours(23, 59, 59, 999);

    console.log('Date de début formatée:', formattedStartDate);
    console.log('Date de fin formatée:', formattedEndDate);

    const customRange = {
      startDate: formattedStartDate,
      endDate: formattedEndDate
    };

    setDateRange(customRange);
    setSelectedRangeId('custom');

    // Notify parent component
    if (onDateRangeChange) {
      console.log('Envoi des dates personnalisées au composant parent:', customRange);
      onDateRangeChange('custom', customRange);
    }

    setCustomDialogOpen(false);
  };

  const handleRefreshClick = () => {
    console.log('Bouton d\'actualisation cliqué dans SimpleDateFilter');
    console.log('Période sélectionnée:', selectedRangeId);
    console.log('Dates:', dateRange);

    if (onRefresh) {
      // Appeler la fonction onRefresh avec les paramètres actuels
      onRefresh(selectedRangeId, dateRange);
    }
  };

  // Get the label for the current selection
  const getSelectedRangeLabel = () => {
    if (selectedRangeId === 'custom') {
      return formatDateRange(dateRange.startDate, dateRange.endDate);
    }

    const predefined = PREDEFINED_RANGES.find(r => r.id === selectedRangeId);
    return predefined ? predefined.label : 'Période';
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Button
        variant="outlined"
        size="small"
        onClick={handleClick}
        startIcon={<CalendarIcon />}
        endIcon={<ArrowDownIcon />}
        sx={{
          borderRadius: 2,
          textTransform: 'none',
          fontWeight: 500,
          px: 2,
          py: 0.75,
          borderColor: 'divider',
          '&:hover': {
            borderColor: 'primary.main',
          },
          minWidth: 150
        }}
      >
        {getSelectedRangeLabel()}
      </Button>

      {showRefreshButton && (
        <Tooltip title="Actualiser">
          <IconButton
            size="small"
            onClick={handleRefreshClick}
            sx={{ ml: 1 }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            mt: 1,
            borderRadius: 2,
            minWidth: 220,
            overflow: 'visible',
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          }
        }}
      >
        {/* Périodes prédéfinies */}
        {PREDEFINED_RANGES.map((range) => (
          <MenuItem
            key={range.id}
            onClick={() => handleRangeSelect(range.id)}
            selected={selectedRangeId === range.id}
            sx={{ py: 1 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                component="span"
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: range.color,
                  mr: 1.5
                }}
              />
              <Typography variant="body2">{range.label}</Typography>
            </Box>
          </MenuItem>
        ))}

        {/* Custom range option */}
        <Divider sx={{ my: 1 }} />
        <MenuItem onClick={handleCustomDialogOpen} sx={{ py: 1 }}>
          <Typography variant="body2" color="primary.main">
            Date personnalisée
          </Typography>
        </MenuItem>
      </Menu>

      {/* Custom date range dialog */}
      <Dialog
        open={customDialogOpen}
        onClose={handleCustomDialogClose}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle sx={{
          pb: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Typography variant="h6">Date personnalisée</Typography>
          <IconButton size="small" onClick={handleCustomDialogClose}>
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
              <Typography variant="subtitle2" gutterBottom>Date de début</Typography>
              <DatePicker
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: "small",
                    margin: "dense",
                    sx: { mb: 2 }
                  }
                }}
              />
              <Typography variant="subtitle2" gutterBottom>Date de fin</Typography>
              <DatePicker
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: "small",
                    margin: "dense"
                  }
                }}
                minDate={startDate}
              />
            </LocalizationProvider>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={handleCustomDialogClose}
            variant="outlined"
          >
            Annuler
          </Button>
          <Button
            onClick={handleCustomRangeApply}
            variant="contained"
            color="primary"
            startIcon={<CheckIcon />}
          >
            Appliquer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SimpleDateFilter;
