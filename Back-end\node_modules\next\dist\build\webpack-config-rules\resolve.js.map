{"version": 3, "sources": ["../../../src/build/webpack-config-rules/resolve.ts"], "sourcesContent": ["import {\n  COMPILER_NAMES,\n  type CompilerNameValues,\n} from '../../shared/lib/constants'\n\n// exports.<conditionName>\nexport const edgeConditionNames = [\n  'edge-light',\n  // inherits the default conditions\n  '...',\n]\n\nconst mainFieldsPerCompiler = {\n  // For default case, prefer CJS over ESM on server side. e.g. pages dir SSR\n  [COMPILER_NAMES.server]: ['main', 'module'],\n  [COMPILER_NAMES.client]: ['browser', 'module', 'main'],\n  // For bundling-all strategy, prefer ESM over CJS\n  'server-esm': ['module', 'main'],\n}\n\nexport function getMainField(\n  compilerType: CompilerNameValues,\n  preferEsm: boolean\n) {\n  if (compilerType === COMPILER_NAMES.edgeServer) {\n    return edgeConditionNames\n  } else if (compilerType === COMPILER_NAMES.client) {\n    return mainFieldsPerCompiler[COMPILER_NAMES.client]\n  }\n\n  // Prefer module fields over main fields for isomorphic packages on server layer\n  return preferEsm\n    ? mainFieldsPerCompiler['server-esm']\n    : mainFieldsPerCompiler[COMPILER_NAMES.server]\n}\n"], "names": ["edgeConditionNames", "getMainField", "mainFieldsPerCompiler", "COMPILER_NAMES", "server", "client", "compilerType", "preferEsm", "edgeServer"], "mappings": ";;;;;;;;;;;;;;;IAMaA,kBAAkB;eAAlBA;;IAcGC,YAAY;eAAZA;;;2BAjBT;AAGA,MAAMD,qBAAqB;IAChC;IACA,kCAAkC;IAClC;CACD;AAED,MAAME,wBAAwB;IAC5B,2EAA2E;IAC3E,CAACC,yBAAc,CAACC,MAAM,CAAC,EAAE;QAAC;QAAQ;KAAS;IAC3C,CAACD,yBAAc,CAACE,MAAM,CAAC,EAAE;QAAC;QAAW;QAAU;KAAO;IACtD,iDAAiD;IACjD,cAAc;QAAC;QAAU;KAAO;AAClC;AAEO,SAASJ,aACdK,YAAgC,EAChCC,SAAkB;IAElB,IAAID,iBAAiBH,yBAAc,CAACK,UAAU,EAAE;QAC9C,OAAOR;IACT,OAAO,IAAIM,iBAAiBH,yBAAc,CAACE,MAAM,EAAE;QACjD,OAAOH,qBAAqB,CAACC,yBAAc,CAACE,MAAM,CAAC;IACrD;IAEA,gFAAgF;IAChF,OAAOE,YACHL,qBAAqB,CAAC,aAAa,GACnCA,qBAAqB,CAACC,yBAAc,CAACC,MAAM,CAAC;AAClD"}