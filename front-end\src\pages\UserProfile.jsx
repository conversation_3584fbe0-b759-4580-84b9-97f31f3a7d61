import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Avatar,
  Divider,
  Grid,
  Paper,
  CircularProgress,
  IconButton,
  Alert,
  Snackbar,
  Container,
  Chip,
  InputAdornment,
  useTheme,
  alpha,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  PhotoCamera,
  Save,
  Person,
  Email,
  Phone,
  Business,
  LocationOn,
  Edit,
  Lock,
  Badge,
  Security,
  AccountCircle,
  CheckCircle
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/authService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { type: 'spring', stiffness: 100 }
  }
};

// Constantes pour le style
const cardStyle = {
  marginBottom: 3,
  overflow: 'visible',
  position: 'relative',
  boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 15px 35px rgba(0,0,0,0.1)'
  }
};

const profileImageStyle = {
  width: 120, // Reduced from 140 to 120
  height: 120, // Reduced from 140 to 120
  border: '4px solid #fff', // Reduced from 5px to 4px
  boxShadow: '0 6px 20px rgba(0,0,0,0.15)',
  margin: '0 auto',
  position: 'relative',
  zIndex: 1,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)'
  }
};

const uploadButtonStyle = {
  position: 'absolute',
  bottom: 0,
  right: 0,
  backgroundColor: 'primary.main',
  color: 'white',
  borderRadius: '50%',
  padding: '8px',
  boxShadow: '0 4px 10px rgba(0,0,0,0.3)',
  zIndex: 10,
  '&:hover': {
    backgroundColor: 'primary.dark',
    transform: 'scale(1.1)'
  },
  transition: 'all 0.2s ease'
};

const headerBgStyle = {
  height: 100, // Further reduced to 100px
  background: (theme) => `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  borderRadius: '16px 16px 0 0',
  position: 'relative',
  marginBottom: 20, // Further reduced to 20px
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%)',
    opacity: 0.6
  }
};

const sectionCardStyle = {
  height: '100%',
  borderRadius: 3,
  boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
    transform: 'translateY(-5px)'
  }
};

const sectionTitleStyle = {
  mb: 3,
  fontWeight: 600,
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 40,
    height: 3,
    backgroundColor: 'primary.main',
    borderRadius: 1.5
  }
};

const inputStyle = {
  '& .MuiOutlinedInput-root': {
    borderRadius: 2,
    transition: 'all 0.2s ease',
    '&:hover': {
      boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.1)'
    },
    '&.Mui-focused': {
      boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.2)'
    }
  }
};

const UserProfile = () => {
  const { currentUser, refreshUserProfile } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [userData, setUserData] = useState({
    nom: '',
    email: '',
    telephone: '',
    adresse: '',
    contact: '',
    role: 'USER',
    profileImage: ''
  });
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordErrors, setPasswordErrors] = useState({});

  useEffect(() => {
    // Charger le profil utilisateur complet au chargement de la page
    const loadUserProfile = async () => {
      setLoading(true);
      try {
        // Si currentUser existe déjà dans le contexte, l'utiliser
        if (currentUser) {
          setUserData({
            nom: currentUser.nom || '',
            email: currentUser.email || '',
            telephone: currentUser.telephone || '',
            adresse: currentUser.adresse || '',
            contact: currentUser.contact || '',
            role: currentUser.role || 'USER',
            profileImage: currentUser.profileImage || ''
          });
        }

        // Récupérer les données complètes du profil depuis le serveur
        const profileData = await authService.fetchUserProfile();
        if (profileData) {
          setUserData({
            nom: profileData.nom || '',
            email: profileData.email || '',
            telephone: profileData.telephone || '',
            adresse: profileData.adresse || '',
            contact: profileData.contact || '',
            role: profileData.role || 'USER',
            profileImage: profileData.profileImage || ''
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement du profil:', error);
        showSnackbar('Erreur lors du chargement du profil utilisateur', 'error');
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, [currentUser]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUserData({
      ...userData,
      [name]: value
    });
  };

  const handleImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setProfileImage(e.target.files[0]);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setOpenSnackbar(true);
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      // Si une image a été sélectionnée, l'envoyer d'abord
      let profileImageUrl = userData.profileImage;
      if (profileImage) {
        try {
          console.log('Uploading profile image...');
          const imageResponse = await authService.uploadProfileImage(profileImage);
          console.log('Image upload response:', imageResponse);
          profileImageUrl = imageResponse.profileImage;
        } catch (imageError) {
          console.error('Error uploading profile image:', imageError);
          showSnackbar("Erreur lors du téléchargement de l'image de profil", 'error');
          // Continue with profile update even if image upload fails
        }
      }

      // Mise à jour des informations du profil
      const updatedData = {
        ...userData,
        profileImage: profileImageUrl
      };

      console.log('Updating profile with data:', updatedData);
      const response = await authService.updateProfile(updatedData);
      console.log('Profile update response:', response);

      showSnackbar('Profil mis à jour avec succès');
      setEditing(false);
      setProfileImage(null); // Reset the selected image

      // Rafraîchir les données utilisateur dans le contexte
      await refreshUserProfile();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      const errorMessage = error.error || 'Erreur lors de la mise à jour du profil';
      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  const handlePasswordDialogOpen = () => {
    setPasswordDialogOpen(true);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordErrors({});
  };

  const handlePasswordDialogClose = () => {
    setPasswordDialogOpen(false);
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value
    });

    // Clear error when user types
    if (passwordErrors[name]) {
      setPasswordErrors({
        ...passwordErrors,
        [name]: ''
      });
    }
  };

  const validatePasswordForm = () => {
    const errors = {};

    if (!passwordData.currentPassword) {
      errors.currentPassword = 'Le mot de passe actuel est requis';
    }

    if (!passwordData.newPassword) {
      errors.newPassword = 'Le nouveau mot de passe est requis';
    } else if (passwordData.newPassword.length < 6) {
      errors.newPassword = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    if (!passwordData.confirmPassword) {
      errors.confirmPassword = 'Veuillez confirmer le nouveau mot de passe';
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSavePassword = async () => {
    if (!validatePasswordForm()) {
      return;
    }

    setLoading(true);
    try {
      await authService.changePassword(
        passwordData.currentPassword,
        passwordData.newPassword
      );

      showSnackbar('Mot de passe modifié avec succès');
      handlePasswordDialogClose();
    } catch (error) {
      console.error('Erreur lors du changement de mot de passe:', error);
      const errorMessage = error.error || 'Erreur lors du changement de mot de passe';

      if (errorMessage.includes('actuel')) {
        setPasswordErrors({
          ...passwordErrors,
          currentPassword: errorMessage
        });
      } else {
        showSnackbar(errorMessage, 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const theme = useTheme();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 4,
          flexWrap: 'wrap',
          gap: 2
        }}>
          <motion.div variants={itemVariants}>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 700,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: { xs: 1, md: 0 }
              }}
            >
              {userData.role === 'ADMIN' ? 'Profil Administrateur' :
               userData.role === 'VENDEUR' ? 'Profil Vendeur' :
               userData.role === 'ENTREPRISE' ? 'Profil Entreprise' :
               'Profil Client'}
            </Typography>
          </motion.div>

          {!loading && (
            <motion.div variants={itemVariants}>
              <Button
                variant={editing ? "contained" : "outlined"}
                color="primary"
                startIcon={editing ? <Save /> : <Edit />}
                onClick={() => editing ? handleSaveProfile() : setEditing(true)}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  py: 1,
                  boxShadow: editing ? 4 : 0,
                  transition: 'all 0.3s ease'
                }}
              >
                {editing ? 'Enregistrer les modifications' : 'Modifier le profil'}
              </Button>
            </motion.div>
          )}
        </Box>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
            <CircularProgress size={60} thickness={4} />
          </Box>
        )}

        {!loading && (
          <motion.div variants={containerVariants}>
            <motion.div variants={itemVariants}>
              <Card sx={cardStyle} component={motion.div} whileHover={{ y: -5 }}>
                <Box sx={headerBgStyle}>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 20,
                      left: 20,
                      zIndex: 2
                    }}
                  >
                    <Chip
                      label={
                        userData.role === 'ADMIN' ? 'Administrateur' :
                        userData.role === 'VENDEUR' ? 'Vendeur' :
                        userData.role === 'ENTREPRISE' ? 'Entreprise' :
                        'Client'
                      }
                      color="primary"
                      icon={<Badge />}
                      sx={{
                        fontWeight: 600,
                        px: 1,
                        backgroundColor: alpha(theme.palette.primary.main, 0.9),
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  </Box>
                </Box>
                <Box sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', md: 'row' },
                  alignItems: { xs: 'center', md: 'flex-start' },
                  justifyContent: { md: 'space-between' },
                  mt: { xs: -4, md: -4 }, // Further reduced to -4 to minimize white space
                  position: 'relative',
                  px: { xs: 3, md: 4 },
                  pb: 4
                }}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    alignItems: { xs: 'center', md: 'flex-start' },
                    gap: { md: 4 }
                  }}>
                    <Box sx={{ position: 'relative' }}>
                      <Avatar
                        src={profileImage ? URL.createObjectURL(profileImage) : userData.profileImage || ''}
                        alt={userData.nom}
                        sx={profileImageStyle}
                      >
                        {userData.nom && userData.nom.charAt(0).toUpperCase()}
                      </Avatar>

                      <input
                        accept="image/*"
                        id="profile-image-input"
                        type="file"
                        onChange={handleImageChange}
                        style={{ display: 'none' }}
                        disabled={!editing}
                      />
                      {editing && (
                        <label htmlFor="profile-image-input">
                          <Tooltip title="Changer la photo de profil">
                            <IconButton
                              component="span"
                              sx={uploadButtonStyle}
                              color="primary"
                            >
                              <PhotoCamera />
                            </IconButton>
                          </Tooltip>
                        </label>
                      )}
                    </Box>

                    <Box sx={{
                      mt: { xs: 3, md: 4 },
                      textAlign: { xs: 'center', md: 'left' }
                    }}>
                      <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                        {userData.nom}
                      </Typography>

                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mb: 1,
                        justifyContent: { xs: 'center', md: 'flex-start' }
                      }}>
                        <Email fontSize="small" color="primary" sx={{ mr: 1 }} />
                        <Typography variant="body1">
                          {userData.email}
                        </Typography>
                      </Box>

                      {userData.telephone && (
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: { xs: 'center', md: 'flex-start' }
                        }}>
                          <Phone fontSize="small" color="primary" sx={{ mr: 1 }} />
                          <Typography variant="body1">
                            {userData.telephone}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>

                  <Box sx={{
                    mt: { xs: 3, md: 4 },
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: { xs: 'center', md: 'flex-end' }
                  }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1,
                      p: 1,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.1)
                    }}>
                      <CheckCircle fontSize="small" color="success" sx={{ mr: 1 }} />
                      <Typography variant="body2" color="success.main" fontWeight={500}>
                        Compte vérifié
                      </Typography>
                    </Box>

                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                      Membre depuis {new Date(userData.dateCreation).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </motion.div>

            <Grid container spacing={3} sx={{ mt: 3 }}>
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Card sx={sectionCardStyle}>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="h6" sx={sectionTitleStyle}>
                        <AccountCircle sx={{ mr: 1.5, color: 'primary.main' }} />
                        Informations personnelles
                      </Typography>

                      <Box sx={{ mt: 4 }}>
                        <TextField
                          fullWidth
                          label="Nom complet"
                          name="nom"
                          value={userData.nom}
                          onChange={handleInputChange}
                          disabled={!editing}
                          margin="normal"
                          sx={inputStyle}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Person color={editing ? "primary" : "action"} />
                              </InputAdornment>
                            ),
                          }}
                        />

                        <TextField
                          fullWidth
                          label="Email"
                          name="email"
                          value={userData.email}
                          onChange={handleInputChange}
                          disabled={!editing}
                          margin="normal"
                          sx={inputStyle}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Email color={editing ? "primary" : "action"} />
                              </InputAdornment>
                            ),
                          }}
                        />

                        <TextField
                          fullWidth
                          label="Téléphone"
                          name="telephone"
                          value={userData.telephone}
                          onChange={handleInputChange}
                          disabled={!editing}
                          margin="normal"
                          sx={inputStyle}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Phone color={editing ? "primary" : "action"} />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>

              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Card sx={sectionCardStyle}>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="h6" sx={sectionTitleStyle}>
                        <Business sx={{ mr: 1.5, color: 'primary.main' }} />
                        Informations complémentaires
                      </Typography>

                      <Box sx={{ mt: 4 }}>
                        <TextField
                          fullWidth
                          label="Personne à contacter"
                          name="contact"
                          value={userData.contact}
                          onChange={handleInputChange}
                          disabled={!editing}
                          margin="normal"
                          sx={inputStyle}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Badge color={editing ? "primary" : "action"} />
                              </InputAdornment>
                            ),
                          }}
                        />

                        <TextField
                          fullWidth
                          label="Adresse"
                          name="adresse"
                          value={userData.adresse}
                          onChange={handleInputChange}
                          disabled={!editing}
                          margin="normal"
                          sx={inputStyle}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <LocationOn color={editing ? "primary" : "action"} />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Box>

                      <Divider sx={{ my: 3, borderStyle: 'dashed' }} />

                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle1" sx={{
                          fontWeight: 600,
                          mb: 2,
                          display: 'flex',
                          alignItems: 'center',
                          color: 'text.primary'
                        }}>
                          <Security sx={{ mr: 1.5, color: theme.palette.warning.main }} />
                          Sécurité du compte
                        </Typography>

                        <Button
                          variant="outlined"
                          color="primary"
                          size="medium"
                          startIcon={<Lock />}
                          onClick={handlePasswordDialogOpen}
                          sx={{
                            mt: 1,
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 500
                          }}
                        >
                          Changer le mot de passe
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        )}

        <Snackbar
          open={openSnackbar}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbarSeverity}
            sx={{
              width: '100%',
              boxShadow: 3,
              borderRadius: 2
            }}
            variant="filled"
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>

        {/* Password Change Dialog */}
        <Dialog
          open={passwordDialogOpen}
          onClose={handlePasswordDialogClose}
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: '0 8px 25px rgba(0,0,0,0.2)',
              width: '100%',
              maxWidth: 450
            }
          }}
        >
          <DialogTitle sx={{
            borderBottom: '1px solid',
            borderColor: 'divider',
            pb: 2,
            fontWeight: 600
          }}>
            Changer le mot de passe
          </DialogTitle>
          <DialogContent sx={{ pt: 3, pb: 1 }}>
            <DialogContentText sx={{ mb: 3, color: 'text.secondary' }}>
              Pour changer votre mot de passe, veuillez entrer votre mot de passe actuel et votre nouveau mot de passe.
            </DialogContentText>

            <TextField
              margin="dense"
              label="Mot de passe actuel"
              type="password"
              fullWidth
              name="currentPassword"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange}
              error={!!passwordErrors.currentPassword}
              helperText={passwordErrors.currentPassword}
              sx={inputStyle}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color={passwordErrors.currentPassword ? "error" : "primary"} />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              margin="dense"
              label="Nouveau mot de passe"
              type="password"
              fullWidth
              name="newPassword"
              value={passwordData.newPassword}
              onChange={handlePasswordChange}
              error={!!passwordErrors.newPassword}
              helperText={passwordErrors.newPassword}
              sx={{ ...inputStyle, mt: 2 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Security color={passwordErrors.newPassword ? "error" : "primary"} />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              margin="dense"
              label="Confirmer le nouveau mot de passe"
              type="password"
              fullWidth
              name="confirmPassword"
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange}
              error={!!passwordErrors.confirmPassword}
              helperText={passwordErrors.confirmPassword}
              sx={{ ...inputStyle, mt: 2, mb: 1 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <CheckCircle color={passwordErrors.confirmPassword ? "error" : "primary"} />
                  </InputAdornment>
                ),
              }}
            />
          </DialogContent>
          <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
            <Button
              onClick={handlePasswordDialogClose}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              Annuler
            </Button>
            <Button
              onClick={handleSavePassword}
              variant="contained"
              color="primary"
              disabled={loading}
              sx={{
                borderRadius: 2,
                ml: 1,
                position: 'relative'
              }}
            >
              {loading ? 'Enregistrement...' : 'Enregistrer'}
              {loading && (
                <CircularProgress
                  size={24}
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    marginTop: '-12px',
                    marginLeft: '-12px',
                  }}
                />
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Container>
  );
};

export default UserProfile;