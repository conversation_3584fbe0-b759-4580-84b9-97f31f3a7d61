{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-pathname-normalizer.ts"], "sourcesContent": ["import { normalizeAppPath } from '../../../../shared/lib/router/utils/app-paths'\nimport { Normalizers } from '../../normalizers'\nimport { wrapNormalizerFn } from '../../wrap-normalizer-fn'\nimport { UnderscoreNormalizer } from '../../underscore-normalizer'\nimport type { Normalizer } from '../../normalizer'\n\nexport class App<PERSON>athnameNormalizer extends Normalizers {\n  constructor() {\n    super([\n      // The pathname to match should have the trailing `/page` and other route\n      // group information stripped from it.\n      wrapNormalizerFn(normalizeAppPath),\n      // The page should have the `%5F` characters replaced with `_` characters.\n      new UnderscoreNormalizer(),\n    ])\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(page)\n  }\n}\n\nexport class DevAppPathnameNormalizer extends Normalizers {\n  constructor(pageNormalizer: Normalizer) {\n    super([\n      // This should normalize the filename to a page.\n      pageNormalizer,\n      // Normalize the app page to a pathname.\n      new AppPathnameNormalizer(),\n    ])\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["AppPathnameNormalizer", "DevAppPathnameNormalizer", "Normalizers", "constructor", "wrapNormalizerFn", "normalizeAppPath", "UnderscoreNormalizer", "normalize", "page", "pageNormalizer", "filename"], "mappings": ";;;;;;;;;;;;;;;IAMaA,qBAAqB;eAArBA;;IAgBAC,wBAAwB;eAAxBA;;;0BAtBoB;6BACL;kCACK;sCACI;AAG9B,MAAMD,8BAA8BE,wBAAW;IACpDC,aAAc;QACZ,KAAK,CAAC;YACJ,yEAAyE;YACzE,sCAAsC;YACtCC,IAAAA,kCAAgB,EAACC,0BAAgB;YACjC,0EAA0E;YAC1E,IAAIC,0CAAoB;SACzB;IACH;IAEOC,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF;AAEO,MAAMP,iCAAiCC,wBAAW;IACvDC,YAAYM,cAA0B,CAAE;QACtC,KAAK,CAAC;YACJ,gDAAgD;YAChDA;YACA,wCAAwC;YACxC,IAAIT;SACL;IACH;IAEOO,UAAUG,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACH,UAAUG;IACzB;AACF"}