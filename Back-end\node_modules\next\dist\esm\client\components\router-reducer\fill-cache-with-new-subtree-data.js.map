{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { Segment } from '../../../server/app-render/types'\nimport { invalidateCacheByRouterState } from './invalidate-cache-by-router-state'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\n/**\n * Common logic for filling cache with new sub tree data.\n */\nfunction fillCacheHelper(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry: PrefetchCacheEntry | undefined,\n  fillLazyItems: boolean\n): void {\n  const {\n    segmentPath,\n    seedData: cacheNodeSeedData,\n    tree: treePatch,\n    head,\n  } = flightData\n  let newCacheNode = newCache\n  let existingCacheNode = existingCache\n\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n\n    // segmentPath is a repeating tuple of parallelRouteKey and segment\n    // we know we've hit the last entry we've reached our final pair\n    const isLastEntry = i === segmentPath.length - 2\n    const cacheKey = createRouterCacheKey(segment)\n\n    const existingChildSegmentMap =\n      existingCacheNode.parallelRoutes.get(parallelRouteKey)\n\n    if (!existingChildSegmentMap) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    let childSegmentMap = newCacheNode.parallelRoutes.get(parallelRouteKey)\n    if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n      childSegmentMap = new Map(existingChildSegmentMap)\n      newCacheNode.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n    }\n\n    const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n    let childCacheNode = childSegmentMap.get(cacheKey)\n\n    if (isLastEntry) {\n      if (\n        cacheNodeSeedData &&\n        (!childCacheNode ||\n          !childCacheNode.lazyData ||\n          childCacheNode === existingChildCacheNode)\n      ) {\n        const incomingSegment = cacheNodeSeedData[0]\n        const rsc = cacheNodeSeedData[1]\n        const loading = cacheNodeSeedData[3]\n\n        childCacheNode = {\n          lazyData: null,\n          // When `fillLazyItems` is false, we only want to fill the RSC data for the layout,\n          // not the page segment.\n          rsc:\n            fillLazyItems || incomingSegment !== PAGE_SEGMENT_KEY ? rsc : null,\n          prefetchRsc: null,\n          head: null,\n          prefetchHead: null,\n          loading,\n          parallelRoutes:\n            fillLazyItems && existingChildCacheNode\n              ? new Map(existingChildCacheNode.parallelRoutes)\n              : new Map(),\n          navigatedAt,\n        }\n\n        if (existingChildCacheNode && fillLazyItems) {\n          invalidateCacheByRouterState(\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch\n          )\n        }\n        if (fillLazyItems) {\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            prefetchEntry\n          )\n        }\n\n        childSegmentMap.set(cacheKey, childCacheNode)\n      }\n      continue\n    }\n\n    if (!childCacheNode || !existingChildCacheNode) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    if (childCacheNode === existingChildCacheNode) {\n      childCacheNode = {\n        lazyData: childCacheNode.lazyData,\n        rsc: childCacheNode.rsc,\n        prefetchRsc: childCacheNode.prefetchRsc,\n        head: childCacheNode.head,\n        prefetchHead: childCacheNode.prefetchHead,\n        parallelRoutes: new Map(childCacheNode.parallelRoutes),\n        loading: childCacheNode.loading,\n      } as CacheNode\n      childSegmentMap.set(cacheKey, childCacheNode)\n    }\n\n    // Move deeper into the cache nodes\n    newCacheNode = childCacheNode\n    existingCacheNode = existingChildCacheNode\n  }\n}\n\n/**\n * Fill cache with rsc based on flightDataPath\n */\nexport function fillCacheWithNewSubTreeData(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    true\n  )\n}\n\nexport function fillCacheWithNewSubTreeDataButOnlyLoading(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    false\n  )\n}\n"], "names": ["invalidateCacheByRouterState", "fillLazyItemsTillLeafWithHead", "createRouterCache<PERSON>ey", "PAGE_SEGMENT_KEY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigatedAt", "newCache", "existingCache", "flightData", "prefetchEntry", "fillLazyItems", "segmentPath", "seedData", "cacheNodeSeedData", "tree", "treePatch", "head", "newCacheNode", "existingCacheNode", "i", "length", "parallelRouteKey", "segment", "isLastEntry", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "lazyData", "incomingSegment", "rsc", "loading", "prefetchRsc", "prefetchHead", "fillCacheWithNewSubTreeData", "fillCacheWithNewSubTreeDataButOnlyLoading"], "mappings": "AAEA,SAASA,4BAA4B,QAAQ,qCAAoC;AACjF,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE,SAASC,gBAAgB,QAAQ,8BAA6B;AAG9D;;CAEC,GACD,SAASC,gBACPC,WAAmB,EACnBC,QAAmB,EACnBC,aAAwB,EACxBC,UAAgC,EAChCC,aAA6C,EAC7CC,aAAsB;IAEtB,MAAM,EACJC,WAAW,EACXC,UAAUC,iBAAiB,EAC3BC,MAAMC,SAAS,EACfC,IAAI,EACL,GAAGR;IACJ,IAAIS,eAAeX;IACnB,IAAIY,oBAAoBX;IAExB,IAAK,IAAIY,IAAI,GAAGA,IAAIR,YAAYS,MAAM,EAAED,KAAK,EAAG;QAC9C,MAAME,mBAA2BV,WAAW,CAACQ,EAAE;QAC/C,MAAMG,UAAmBX,WAAW,CAACQ,IAAI,EAAE;QAE3C,mEAAmE;QACnE,gEAAgE;QAChE,MAAMI,cAAcJ,MAAMR,YAAYS,MAAM,GAAG;QAC/C,MAAMI,WAAWtB,qBAAqBoB;QAEtC,MAAMG,0BACJP,kBAAkBQ,cAAc,CAACC,GAAG,CAACN;QAEvC,IAAI,CAACI,yBAAyB;YAG5B;QACF;QAEA,IAAIG,kBAAkBX,aAAaS,cAAc,CAACC,GAAG,CAACN;QACtD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;YACnEG,kBAAkB,IAAIC,IAAIJ;YAC1BR,aAAaS,cAAc,CAACI,GAAG,CAACT,kBAAkBO;QACpD;QAEA,MAAMG,yBAAyBN,wBAAwBE,GAAG,CAACH;QAC3D,IAAIQ,iBAAiBJ,gBAAgBD,GAAG,CAACH;QAEzC,IAAID,aAAa;YACf,IACEV,qBACC,CAAA,CAACmB,kBACA,CAACA,eAAeC,QAAQ,IACxBD,mBAAmBD,sBAAqB,GAC1C;gBACA,MAAMG,kBAAkBrB,iBAAiB,CAAC,EAAE;gBAC5C,MAAMsB,MAAMtB,iBAAiB,CAAC,EAAE;gBAChC,MAAMuB,UAAUvB,iBAAiB,CAAC,EAAE;gBAEpCmB,iBAAiB;oBACfC,UAAU;oBACV,mFAAmF;oBACnF,wBAAwB;oBACxBE,KACEzB,iBAAiBwB,oBAAoB/B,mBAAmBgC,MAAM;oBAChEE,aAAa;oBACbrB,MAAM;oBACNsB,cAAc;oBACdF;oBACAV,gBACEhB,iBAAiBqB,yBACb,IAAIF,IAAIE,uBAAuBL,cAAc,IAC7C,IAAIG;oBACVxB;gBACF;gBAEA,IAAI0B,0BAA0BrB,eAAe;oBAC3CV,6BACEgC,gBACAD,wBACAhB;gBAEJ;gBACA,IAAIL,eAAe;oBACjBT,8BACEI,aACA2B,gBACAD,wBACAhB,WACAF,mBACAG,MACAP;gBAEJ;gBAEAmB,gBAAgBE,GAAG,CAACN,UAAUQ;YAChC;YACA;QACF;QAEA,IAAI,CAACA,kBAAkB,CAACD,wBAAwB;YAG9C;QACF;QAEA,IAAIC,mBAAmBD,wBAAwB;YAC7CC,iBAAiB;gBACfC,UAAUD,eAAeC,QAAQ;gBACjCE,KAAKH,eAAeG,GAAG;gBACvBE,aAAaL,eAAeK,WAAW;gBACvCrB,MAAMgB,eAAehB,IAAI;gBACzBsB,cAAcN,eAAeM,YAAY;gBACzCZ,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;gBACrDU,SAASJ,eAAeI,OAAO;YACjC;YACAR,gBAAgBE,GAAG,CAACN,UAAUQ;QAChC;QAEA,mCAAmC;QACnCf,eAAee;QACfd,oBAAoBa;IACtB;AACF;AAEA;;CAEC,GACD,OAAO,SAASQ,4BACdlC,WAAmB,EACnBC,QAAmB,EACnBC,aAAwB,EACxBC,UAAgC,EAChCC,aAAkC;IAElCL,gBACEC,aACAC,UACAC,eACAC,YACAC,eACA;AAEJ;AAEA,OAAO,SAAS+B,0CACdnC,WAAmB,EACnBC,QAAmB,EACnBC,aAAwB,EACxBC,UAAgC,EAChCC,aAAkC;IAElCL,gBACEC,aACAC,UACAC,eACAC,YACAC,eACA;AAEJ"}