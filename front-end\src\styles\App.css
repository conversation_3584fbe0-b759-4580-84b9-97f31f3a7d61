@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Inter", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  position: relative;
}

.card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eaedf3;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-body {
  padding: 20px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
}

.status-badge.draft {
  background-color: #f5f5f5;
  color: #757575;
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #ff9800;
}

.status-badge.finalized {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.paid {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-badge.late {
  background-color: #ffebee;
  color: #f44336;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  text-align: left;
  padding: 16px;
  font-weight: 600;
  color: #64748b;
  border-bottom: 1px solid #eaedf3;
}

.data-table td {
  padding: 16px;
  border-bottom: 1px solid #eaedf3;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:hover {
  background-color: #f9fafb;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 16px;
  width: 100%;
  max-width: 400px;
  transition: all 0.2s;
}

.search-bar:focus-within {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.search-bar input {
  border: none;
  outline: none;
  width: 100%;
  padding: 8px;
  font-size: 14px;
}

.search-bar svg {
  color: #64748b;
}

.filter-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.summary-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.summary-card-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.summary-card-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.summary-card-subtitle {
  font-size: 14px;
  color: #64748b;
}

.tabs-container {
  margin-bottom: 24px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #eaedf3;
}

.tab {
  padding: 12px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
}

.tab.active {
  border-bottom: 2px solid #1976d2;
  color: #1976d2;
}

.tab:hover:not(.active) {
  background-color: #f9fafb;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1976d2;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #eaedf3;
}

/* Dark mode overrides */
.dark-mode .card {
  background-color: #1e1e1e;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.dark-mode .card-header {
  border-bottom: 1px solid #333;
}

.dark-mode .data-table th,
.dark-mode .data-table td {
  border-bottom: 1px solid #333;
}

.dark-mode .data-table tr:hover {
  background-color: #2a2a2a;
}

.dark-mode .search-bar {
  background-color: #1e1e1e;
  border: 1px solid #333;
}

.dark-mode .search-bar input {
  background-color: transparent;
  color: #fff;
}

.dark-mode .tab:hover:not(.active) {
  background-color: #2a2a2a;
}

/* Responsive design */
@media (max-width: 768px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-bar {
    max-width: 100%;
  }
}