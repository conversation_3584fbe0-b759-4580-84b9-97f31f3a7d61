const jwt = require('jsonwebtoken');

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Middleware to check if user is admin
const isAdmin = (req, res, next) => {
  if (req.userRole !== 'ADMIN') {
    // Au lieu de renvoyer une erreur 403, on renvoie un message d'information avec un code 200
    return res.status(200).json({
      redirect: true,
      message: 'Redirection vers votre tableau de bord',
      role: req.userRole
    });
  }
  next();
};

// Middleware to check if user is vendeur
const isVendeur = (req, res, next) => {
  if (req.userRole !== 'VENDEUR' && req.userRole !== 'ADMIN') {
    // Au lieu de renvoyer une erreur 403, on renvoie un message d'information avec un code 200
    return res.status(200).json({
      redirect: true,
      message: 'Redirection vers votre tableau de bord',
      role: req.userRole
    });
  }
  next();
};

// Middleware to check if user is responsable d'entreprise
const isResponsable = (req, res, next) => {
  // Permettre aux utilisateurs RESPONSABLE et ADMIN d'accéder aux routes protégées par ce middleware
  if (req.userRole !== 'RESPONSABLE' && req.userRole !== 'ADMIN') {
    // Au lieu de renvoyer une erreur 403, on renvoie un message d'information avec un code 200
    return res.status(200).json({
      redirect: true,
      message: 'Redirection vers votre tableau de bord',
      role: req.userRole
    });
  }
  next();
};

// Middleware to check if user is client
const isClient = (req, res, next) => {
  if (req.userRole !== 'CLIENT') {
    // Au lieu de renvoyer une erreur 403, on renvoie un message d'information avec un code 200
    return res.status(200).json({
      redirect: true,
      message: 'Redirection vers votre tableau de bord',
      role: req.userRole
    });
  }
  next();
};

// Middleware pour vérifier si l'abonnement du responsable est actif
const hasActiveSubscription = async (req, res, next) => {
  try {
    // Si l'utilisateur est un admin, on le laisse passer
    if (req.userRole === 'ADMIN') {
      return next();
    }

    // Si l'utilisateur est un responsable, on vérifie son abonnement
    if (req.userRole === 'RESPONSABLE') {
      const User = require('../models/UserModel');
      const Abonnement = require('../models/AbonnementModel');

      const user = await User.findById(req.userId);

      if (!user) {
        return res.status(404).json({ message: 'Utilisateur non trouvé' });
      }

      // Si l'utilisateur n'a pas d'abonnement actif
      if (!user.abonnementActif) {
        return res.status(403).json({
          message: 'Votre abonnement n\'est pas actif. Veuillez contacter l\'administrateur.',
          subscriptionRequired: true
        });
      }

      // Vérifier si l'abonnement existe et n'est pas expiré
      if (user.abonnementId) {
        const abonnement = await Abonnement.findById(user.abonnementId);

        // Ajouter des logs pour le débogage
        console.log('Vérification de l\'abonnement pour l\'utilisateur:', req.userId);
        console.log('Abonnement trouvé:', abonnement ? 'Oui' : 'Non');

        if (abonnement) {
          console.log('Statut de l\'abonnement:', abonnement.statut);
          console.log('Date de fin:', abonnement.dateFin);
          console.log('Date actuelle:', new Date());
          console.log('Durée de l\'abonnement:', abonnement.duree);

          // Calculer la différence en minutes
          const diffTime = new Date(abonnement.dateFin) - new Date();
          const diffMinutes = Math.floor(diffTime / (1000 * 60));
          console.log('Minutes restantes:', diffMinutes);

          // Pour les abonnements de 30 minutes, ajouter une tolérance de 1 minute
          // pour éviter les problèmes de synchronisation d'horloge
          const isExpired = abonnement.duree === '30_MIN'
            ? diffMinutes < -1 // Tolérance de 1 minute pour les abonnements de 30 minutes
            : abonnement.dateFin < new Date();

          if (!abonnement || abonnement.statut !== 'ACTIF' || isExpired) {
            console.log('Abonnement expiré ou inactif');

            // Ne pas mettre à jour le statut immédiatement pour les abonnements de 30 minutes
            // afin de permettre à l'utilisateur de voir son tableau de bord
            if (abonnement.duree !== '30_MIN') {
              // Mettre à jour le statut de l'abonnement de l'utilisateur
              await User.findByIdAndUpdate(req.userId, { abonnementActif: false });
            }

            // Pour les abonnements de 30 minutes, on laisse passer mais on avertit
            if (abonnement.duree === '30_MIN') {
              console.log('Abonnement de 30 minutes, on laisse passer avec avertissement');
              // On continue sans bloquer l'accès
              next();
              return;
            }

            return res.status(403).json({
              message: 'Votre abonnement a expiré. Veuillez contacter l\'administrateur pour le renouveler.',
              subscriptionExpired: true
            });
          }
        } else {
          console.log('Aucun abonnement trouvé pour l\'utilisateur:', req.userId);
        }
      }
    }

    next();
  } catch (error) {
    console.error('Erreur lors de la vérification de l\'abonnement:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la vérification de l\'abonnement' });
  }
};

module.exports = {
  verifyToken,
  isAdmin,
  isVendeur,
  isResponsable,
  isClient,
  hasActiveSubscription
};
