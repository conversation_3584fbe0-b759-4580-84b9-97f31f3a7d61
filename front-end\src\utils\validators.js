/**
 * Valide une adresse email
 * @param {string} email - Email à valider
 * @returns {boolean} True si l'email est valide
 */
export const validateEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
};

/**
 * Valide un numéro de téléphone français
 * @param {string} phone - Numéro de téléphone
 * @returns {boolean} True si le numéro est valide
 */
export const validatePhone = (phone) => {
  const re = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
  return re.test(phone);
};

/**
 * Valide qu'un champ n'est pas vide
 * @param {string} value - Valeur à valider
 * @returns {boolean} True si la valeur n'est pas vide
 */
export const validateRequired = (value) => {
  return value !== undefined && value !== null && value.toString().trim() !== '';
};

/**
 * Valide un nombre positif
 * @param {number} value - Nombre à valider
 * @returns {boolean} True si le nombre est positif
 */
export const validatePositiveNumber = (value) => {
  return !isNaN(value) && parseFloat(value) >= 0;
};

/**
 * Valide un montant (nombre avec 2 décimales max)
 * @param {number} value - Montant à valider
 * @returns {boolean} True si le montant est valide
 */
export const validateAmount = (value) => {
  const re = /^\d+(\.\d{1,2})?$/;
  return re.test(String(value));
};

/**
 * Valide un numéro de TVA français
 * @param {string} tva - Numéro de TVA
 * @returns {boolean} True si le numéro est valide
 */
export const validateFrenchVAT = (tva) => {
  const re = /^FR[a-zA-Z0-9]{2}\d{9}$/;
  return re.test(tva);
};

/**
 * Valide une date (doit être dans le futur)
 * @param {Date} date - Date à valider
 * @returns {boolean} True si la date est dans le futur
 */
export const validateFutureDate = (date) => {
  if (!date) return false;
  const d = new Date(date);
  if (isNaN(d.getTime())) return false;
  return d > new Date();
};

/**
 * Valide une date (doit être dans le passé)
 * @param {Date} date - Date à valider
 * @returns {boolean} True si la date est dans le passé
 */
export const validatePastDate = (date) => {
  if (!date) return false;
  const d = new Date(date);
  if (isNaN(d.getTime())) return false;
  return d < new Date();
};

/**
 * Valide une URL
 * @param {string} url - URL à valider
 * @returns {boolean} True si l'URL est valide
 */
export const validateURL = (url) => {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * Valide un mot de passe (8 caractères min, 1 majuscule, 1 chiffre)
 * @param {string} password - Mot de passe à valider
 * @returns {boolean} True si le mot de passe est valide
 */
export const validatePassword = (password) => {
  const re = /^(?=.*[A-Z])(?=.*\d).{8,}$/;
  return re.test(password);
};

/**
 * Valide un code postal français
 * @param {string} zipCode - Code postal à valider
 * @returns {boolean} True si le code postal est valide
 */
export const validateFrenchZipCode = (zipCode) => {
  const re = /^(?:[0-8]\d|9[0-8])\d{3}$/;
  return re.test(zipCode);
};

/**
 * Valide un SIRET
 * @param {string} siret - Numéro SIRET à valider
 * @returns {boolean} True si le SIRET est valide
 */
export const validateSIRET = (siret) => {
  if (!siret || siret.length !== 14 || !/^\d+$/.test(siret)) {
    return false;
  }

  let sum = 0;
  for (let i = 0; i < 14; i++) {
    let tmp = parseInt(siret.charAt(i), 10) * (i % 2 === 0 ? 2 : 1);
    sum += tmp >= 10 ? tmp - 9 : tmp;
  }

  return sum % 10 === 0;
};

/**
 * Valide un IBAN
 * @param {string} iban - IBAN à valider
 * @returns {boolean} True si l'IBAN est valide
 */
export const validateIBAN = (iban) => {
  const re = /^[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}$/;
  return re.test(iban);
};

/**
 * Valide un BIC
 * @param {string} bic - BIC à valider
 * @returns {boolean} True si le BIC est valide
 */
export const validateBIC = (bic) => {
  const re = /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
  return re.test(bic);
};