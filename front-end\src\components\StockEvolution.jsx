import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Chip,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tooltip,
  Tab,
  Tabs
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Inventory as InventoryIcon,
  History as HistoryIcon,
  Edit as EditIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import produitService from '../services/produitService';
import { formatStockQuantity } from '../utils/formatters';

const StockEvolution = () => {
  const [stockData, setStockData] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [stockHistory, setStockHistory] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [openHistoryDialog, setOpenHistoryDialog] = useState(false);
  const [openAdjustDialog, setOpenAdjustDialog] = useState(false);
  const [adjustmentData, setAdjustmentData] = useState({
    quantiteStock: 0,
    motif: ''
  });
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    fetchStockData();
  }, []);

  const fetchStockData = async () => {
    try {
      setLoading(true);
      // Récupérer les produits avec gestion de stock activée
      const products = await produitService.getProduits({ gestionStock: true });

      // Récupérer les statistiques de stock
      const stats = await produitService.getProductStats();

      // Filtrer les produits avec stock bas
      const lowStock = products.filter(product =>
        product.gestionStock &&
        product.quantiteStock <= product.seuilAlerte
      );

      setStockData(products);
      setLowStockProducts(lowStock);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors de la récupération des données de stock:', error);
      setError('Impossible de charger les données de stock');
      setLoading(false);
    }
  };

  const fetchProductStockHistory = async (productId) => {
    try {
      setHistoryLoading(true);
      const history = await produitService.getStockHistory(productId);
      setStockHistory(history);
      setHistoryLoading(false);
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des stocks:', error);
      setStockHistory([]);
      setHistoryLoading(false);
    }
  };

  const handleOpenHistory = (product) => {
    setSelectedProduct(product);
    fetchProductStockHistory(product._id);
    setOpenHistoryDialog(true);
  };

  const handleCloseHistory = () => {
    setOpenHistoryDialog(false);
    setSelectedProduct(null);
    setStockHistory([]);
  };

  const handleOpenAdjustment = (product) => {
    setSelectedProduct(product);
    setAdjustmentData({
      quantiteStock: product.quantiteStock,
      motif: ''
    });
    setOpenAdjustDialog(true);
  };

  const handleCloseAdjustment = () => {
    setOpenAdjustDialog(false);
    setSelectedProduct(null);
    setAdjustmentData({
      quantiteStock: 0,
      motif: ''
    });
  };

  const handleAdjustmentChange = (e) => {
    const { name, value } = e.target;
    setAdjustmentData(prev => ({
      ...prev,
      [name]: name === 'quantiteStock' ? parseInt(value) : value
    }));
  };

  const handleAdjustStock = async () => {
    try {
      if (!selectedProduct) return;

      const result = await produitService.updateStock(
        selectedProduct._id,
        adjustmentData.quantiteStock,
        adjustmentData.motif
      );

      if (result.success) {
        // Mettre à jour les données locales
        fetchStockData();
        handleCloseAdjustment();
      } else {
        setError(result.message || 'Erreur lors de l\'ajustement du stock');
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajustement du stock:', error);
      setError('Erreur lors de l\'ajustement du stock');
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getStockPercentage = (current, threshold) => {
    // Calculer le pourcentage de stock restant par rapport au seuil d'alerte
    // Si le seuil est 0, éviter la division par zéro
    if (threshold === 0) return 100;

    // Calculer le pourcentage avec un maximum de 100%
    const percentage = Math.min(100, (current / (threshold * 2)) * 100);
    return percentage;
  };

  const getStockStatusColor = (current, threshold) => {
    if (current <= threshold / 2) return 'error';
    if (current <= threshold) return 'warning';
    return 'success';
  };

  if (loading) return <LinearProgress />;

  if (error) return <Alert severity="error">{error}</Alert>;

  // Formater la date
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      return 'Date inconnue';
    }
  };

  // Formater le type d'opération
  const formatOperationType = (type) => {
    switch (type) {
      case 'VENTE':
        return { label: 'Vente', color: 'error', icon: <ArrowDownwardIcon /> };
      case 'RETOUR':
        return { label: 'Retour', color: 'success', icon: <ArrowUpwardIcon /> };
      case 'AJUSTEMENT':
        return { label: 'Ajustement', color: 'info', icon: <EditIcon /> };
      case 'INVENTAIRE':
        return { label: 'Inventaire', color: 'secondary', icon: <RefreshIcon /> };
      default:
        return { label: 'Autre', color: 'default', icon: <HistoryIcon /> };
    }
  };

  return (
    <Box sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Évolution des Stocks
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchStockData}
        >
          Actualiser
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InventoryIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Produits en Stock</Typography>
              </Box>
              <Typography variant="h3" align="center">
                {stockData.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WarningIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Produits en Stock Bas</Typography>
              </Box>
              <Typography variant="h3" align="center" color={lowStockProducts.length > 0 ? 'error' : 'success'}>
                {lowStockProducts.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUpIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Valeur Totale du Stock</Typography>
              </Box>
              <Typography variant="h3" align="center">
                {stockData.reduce((total, product) =>
                  total + (product.prix * product.quantiteStock), 0).toLocaleString('fr-FR', {
                    style: 'currency',
                    currency: 'TND'
                  })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {lowStockProducts.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="subtitle1">
              {lowStockProducts.length} produit(s) en stock bas nécessitent votre attention
            </Typography>
          </Alert>

          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Produit</TableCell>
                  <TableCell>Catégorie</TableCell>
                  <TableCell>Stock Actuel</TableCell>
                  <TableCell>Seuil d'Alerte</TableCell>
                  <TableCell>Niveau de Stock</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lowStockProducts.map((product) => (
                  <TableRow key={product._id} hover>
                    <TableCell>{product.nom}</TableCell>
                    <TableCell>
                      <Chip label={product.category} size="small" />
                    </TableCell>
                    <TableCell>{formatStockQuantity(product.quantiteStock, '')}</TableCell>
                    <TableCell>{product.seuilAlerte}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={getStockPercentage(product.quantiteStock, product.seuilAlerte)}
                            color={getStockStatusColor(product.quantiteStock, product.seuilAlerte)}
                            sx={{ height: 10, borderRadius: 5 }}
                          />
                        </Box>
                        <Box sx={{ minWidth: 35 }}>
                          <Typography variant="body2" color="text.secondary">
                            {Math.round(getStockPercentage(product.quantiteStock, product.seuilAlerte))}%
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Ajuster le stock">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleOpenAdjustment(product)}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Voir l'historique">
                        <IconButton
                          size="small"
                          color="info"
                          onClick={() => handleOpenHistory(product)}
                        >
                          <HistoryIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      <Typography variant="h6" gutterBottom>
        Tous les Produits en Stock
      </Typography>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Produit</TableCell>
              <TableCell>Catégorie</TableCell>
              <TableCell>Prix Unitaire</TableCell>
              <TableCell>Quantité en Stock</TableCell>
              <TableCell>Valeur Totale</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {stockData.map((product) => (
              <TableRow key={product._id} hover>
                <TableCell>{product.nom}</TableCell>
                <TableCell>
                  <Chip label={product.category} size="small" />
                </TableCell>
                <TableCell>
                  {product.prix.toLocaleString('fr-FR', {
                    style: 'currency',
                    currency: 'TND'
                  })}
                </TableCell>
                <TableCell>{formatStockQuantity(product.quantiteStock, '')}</TableCell>
                <TableCell>
                  {(product.prix * product.quantiteStock).toLocaleString('fr-FR', {
                    style: 'currency',
                    currency: 'TND'
                  })}
                </TableCell>
                <TableCell>
                  <Chip
                    icon={product.quantiteStock <= product.seuilAlerte ? <WarningIcon /> : <CheckCircleIcon />}
                    label={product.quantiteStock <= product.seuilAlerte ? 'Stock Bas' : 'En Stock'}
                    color={product.quantiteStock <= product.seuilAlerte ? 'warning' : 'success'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Tooltip title="Ajuster le stock">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleOpenAdjustment(product)}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Voir l'historique">
                    <IconButton
                      size="small"
                      color="info"
                      onClick={() => handleOpenHistory(product)}
                    >
                      <HistoryIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialogue d'historique des stocks */}
      <Dialog
        open={openHistoryDialog}
        onClose={handleCloseHistory}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Historique des mouvements de stock - {selectedProduct?.nom}
        </DialogTitle>
        <DialogContent>
          {historyLoading ? (
            <LinearProgress sx={{ my: 2 }} />
          ) : stockHistory.length === 0 ? (
            <Alert severity="info" sx={{ my: 2 }}>
              Aucun historique de stock disponible pour ce produit
            </Alert>
          ) : (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Avant</TableCell>
                    <TableCell>Après</TableCell>
                    <TableCell>Différence</TableCell>
                    <TableCell>Motif</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {stockHistory.map((entry) => {
                    const operationType = formatOperationType(entry.typeOperation);
                    return (
                      <TableRow key={entry._id} hover>
                        <TableCell>{formatDate(entry.date)}</TableCell>
                        <TableCell>
                          <Chip
                            icon={operationType.icon}
                            label={operationType.label}
                            color={operationType.color}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{formatStockQuantity(entry.quantiteAvant, '')}</TableCell>
                        <TableCell>{formatStockQuantity(entry.quantiteApres, '')}</TableCell>
                        <TableCell>
                          <Typography
                            color={entry.difference > 0 ? 'success.main' : entry.difference < 0 ? 'error.main' : 'text.primary'}
                            fontWeight="bold"
                          >
                            {entry.difference > 0 ? '+' : ''}{Math.round(entry.difference)}
                          </Typography>
                        </TableCell>
                        <TableCell>{entry.motif || '-'}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseHistory}>Fermer</Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue d'ajustement de stock */}
      <Dialog
        open={openAdjustDialog}
        onClose={handleCloseAdjustment}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Ajuster le stock - {selectedProduct?.nom}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              name="quantiteStock"
              label="Nouvelle quantité en stock"
              type="number"
              value={adjustmentData.quantiteStock}
              onChange={handleAdjustmentChange}
              fullWidth
              margin="normal"
              InputProps={{
                inputProps: { min: 0, step: 1 }
              }}
            />
            <TextField
              name="motif"
              label="Motif de l'ajustement"
              value={adjustmentData.motif}
              onChange={handleAdjustmentChange}
              fullWidth
              margin="normal"
              placeholder="Ex: Inventaire, Réception de commande, Perte, etc."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAdjustment}>Annuler</Button>
          <Button
            onClick={handleAdjustStock}
            variant="contained"
            color="primary"
          >
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StockEvolution;
