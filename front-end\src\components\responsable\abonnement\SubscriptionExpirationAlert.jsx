import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert,
  useTheme
} from '@mui/material';
import { Warning as WarningIcon, Autorenew as AutorenewIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import abonnementService from '../../../services/abonnementService';
import { useAuth } from '../../../contexts/AuthContext';

const SubscriptionExpirationAlert = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expirationData, setExpirationData] = useState(null);
  const [renewalRequested, setRenewalRequested] = useState(false);

  // Fonction pour vérifier l'expiration de l'abonnement
  const checkExpiration = useCallback(async () => {
    try {
      const data = await abonnementService.checkExpiration();
      setExpirationData(data);

      // Ouvrir la boîte de dialogue si l'abonnement est sur le point d'expirer
      if (data.isExpiring) {
        setOpen(true);

        // Si l'abonnement expire dans 2 minutes ou moins, programmer la déconnexion automatique
        if (data.minutesRestantes <= 2) {
          // Calculer le temps restant en millisecondes
          const timeLeftMs = data.minutesRestantes * 60 * 1000;

          // Programmer la déconnexion automatique quand le temps est écoulé
          setTimeout(() => {
            console.log('Abonnement expiré - Déconnexion automatique');
            logout();
            navigate('/login');
          }, timeLeftMs);
        }
      } else if (data.isExpired) {
        // Si l'abonnement est déjà expiré, déconnecter immédiatement
        console.log('Abonnement déjà expiré - Déconnexion immédiate');
        logout();
        navigate('/login');
      }
    } catch (err) {
      console.error('Erreur lors de la vérification de l\'expiration:', err);
      setError(err.message);
    }
  }, [logout, navigate]);

  // Vérifier l'expiration toutes les 10 secondes pour être plus réactif
  useEffect(() => {
    checkExpiration();

    const intervalId = setInterval(() => {
      checkExpiration();
    }, 10000); // 10 secondes pour être plus réactif

    return () => clearInterval(intervalId);
  }, [checkExpiration]);

  // Gérer la demande de renouvellement
  const handleRenewal = async () => {
    setLoading(true);
    try {
      await abonnementService.demanderRenouvellement({
        duree: expirationData?.duree || '30_MIN'
      });
      setRenewalRequested(true);
      setLoading(false);

      // Fermer la boîte de dialogue après 3 secondes
      setTimeout(() => {
        setOpen(false);
      }, 3000);
    } catch (err) {
      console.error('Erreur lors de la demande de renouvellement:', err);
      setError(err.message);
      setLoading(false);
    }
  };

  // Gérer la fermeture de la boîte de dialogue
  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Dialog
      open={open}
      onClose={renewalRequested ? handleClose : undefined} // Only allow closing if renewal was requested
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={!renewalRequested} // Prevent closing with Escape key
      hideBackdrop={false}
      // In MUI v5, use the following to prevent backdrop click closing
      onBackdropClick={!renewalRequested ? (event) => event.stopPropagation() : undefined}
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: theme.shadows[10]
        }
      }}
    >
      <DialogTitle sx={{
        bgcolor: expirationData?.isExpired ? theme.palette.error.light : theme.palette.warning.light,
        color: expirationData?.isExpired ? theme.palette.error.dark : theme.palette.warning.dark,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <WarningIcon color={expirationData?.isExpired ? "error" : "warning"} />
        <Typography variant="h6" component="span" fontWeight={600}>
          {expirationData?.isExpired
            ? "Attention : Votre abonnement a expiré"
            : "Attention : Votre abonnement expire bientôt"}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ py: 3 }}>
        {error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : renewalRequested ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            Votre demande de renouvellement a été envoyée avec succès. Un administrateur la traitera prochainement.
          </Alert>
        ) : (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1" paragraph>
              {expirationData?.isExpired
                ? "Votre abonnement a expiré. Vous allez être automatiquement déconnecté."
                : `Votre abonnement expire dans moins de ${expirationData?.minutesRestantes || 2} minute(s). Vous serez automatiquement déconnecté à l'expiration.`
              }
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
              Pour continuer à utiliser l'application, vous devez demander un renouvellement immédiatement.
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        {!renewalRequested && (
          <Button
            onClick={handleRenewal}
            variant="contained"
            color="primary"
            fullWidth
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <AutorenewIcon />}
            disabled={loading}
          >
            {loading ? 'Demande en cours...' : 'Demander un renouvellement'}
          </Button>
        )}
        {renewalRequested && (
          <Button onClick={handleClose} color="primary">
            Fermer
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default SubscriptionExpirationAlert;
