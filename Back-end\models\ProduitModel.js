const mongoose = require('mongoose');
const { Schema } = mongoose;

const produitSchema = new Schema({
    // Informations de base
    nom: { type: String, required: true },
    description: { type: String },
    descriptionDetaille: { type: String },
    prix: { type: Number, required: true },
    imageUrl: { type: String },
    category: {
        type: String,
        enum: ['Service', 'Produit physique', 'Logiciel', 'Abonnement', 'Formation', 'Non classé'],
        default: 'Non classé'
    },
    unite: { type: String, default: 'unité' },

    // Gestion de stock
    gestionStock: { type: Boolean, default: false },
    quantiteStock: { type: Number, default: 0 },
    seuilAlerte: { type: Number, default: 5 },

    // Statistiques simplifiées
    statistiques: {
        nombreVentes: { type: Number, default: 0 },
        chiffreAffaires: { type: Number, default: 0 }
    },

    // Timestamps
    dateCreation: { type: Date, default: Date.now },
    dateMiseAJour: { type: Date, default: Date.now }
}, { timestamps: true });

// Pre-save middleware to update dateMiseAJour
produitSchema.pre('save', function(next) {
    this.dateMiseAJour = Date.now();
    next();
});

module.exports = mongoose.model('Produit', produitSchema);