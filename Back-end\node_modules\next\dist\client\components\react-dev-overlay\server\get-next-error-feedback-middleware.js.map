{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/get-next-error-feedback-middleware.ts"], "sourcesContent": ["import { eventErrorFeedback } from '../../../../telemetry/events/error-feedback'\nimport { middlewareResponse } from './middleware-response'\nimport type { ServerResponse, IncomingMessage } from 'http'\nimport type { Telemetry } from '../../../../telemetry/storage'\n\n// Handles HTTP requests to /__nextjs_error_feedback endpoint for collecting user feedback on error messages\nexport function getNextErrorFeedbackMiddleware(telemetry: Telemetry) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_error_feedback') {\n      return next()\n    }\n\n    try {\n      const errorCode = searchParams.get('errorCode')\n      const wasHelpful = searchParams.get('wasHelpful')\n\n      if (!errorCode || !wasHelpful) {\n        return middlewareResponse.badRequest(res)\n      }\n\n      await telemetry.record(\n        eventErrorFeedback({\n          errorCode,\n          wasHelpful: wasHelpful === 'true',\n        })\n      )\n\n      return middlewareResponse.noContent(res)\n    } catch (error) {\n      return middlewareResponse.internalServerError(res)\n    }\n  }\n}\n"], "names": ["getNextErrorFeedbackMiddleware", "telemetry", "req", "res", "next", "pathname", "searchParams", "URL", "url", "errorCode", "get", "wasHelpful", "middlewareResponse", "badRequest", "record", "eventErrorFeedback", "noContent", "error", "internalServerError"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;+BANmB;oCACA;AAK5B,SAASA,+BAA+BC,SAAoB;IACjE,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIM,GAAG;QAE7D,IAAIH,aAAa,4BAA4B;YAC3C,OAAOD;QACT;QAEA,IAAI;YACF,MAAMK,YAAYH,aAAaI,GAAG,CAAC;YACnC,MAAMC,aAAaL,aAAaI,GAAG,CAAC;YAEpC,IAAI,CAACD,aAAa,CAACE,YAAY;gBAC7B,OAAOC,sCAAkB,CAACC,UAAU,CAACV;YACvC;YAEA,MAAMF,UAAUa,MAAM,CACpBC,IAAAA,iCAAkB,EAAC;gBACjBN;gBACAE,YAAYA,eAAe;YAC7B;YAGF,OAAOC,sCAAkB,CAACI,SAAS,CAACb;QACtC,EAAE,OAAOc,OAAO;YACd,OAAOL,sCAAkB,CAACM,mBAAmB,CAACf;QAChD;IACF;AACF"}