import axios from "axios";

const API_URL = process.env.REACT_APP_API_URL || "http://localhost:5000/api";

// Créer une instance axios
export const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 30000, // 30 seconds timeout
});

// Ajouter le token aux requêtes si disponible
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Gérer les erreurs de réponse
api.interceptors.response.use(
  (response) => {
    // Vérifier si la réponse contient un indicateur de redirection
    if (response.data && response.data.redirect === true) {
      // Récupérer le rôle de l'utilisateur depuis la réponse ou depuis le localStorage
      const role = response.data.role || JSON.parse(localStorage.getItem("user"))?.role;

      // Rediriger vers la page d'accueil appropriée en fonction du rôle
      if (role === 'ADMIN') {
        window.location.href = "/admin/analytics";
      } else if (role === 'RESPONSABLE') {
        window.location.href = "/responsable/analytics";
      } else if (role === 'VENDEUR') {
        window.location.href = "/vendeur/analytics";
      } else if (role === 'CLIENT') {
        window.location.href = "/client/dashboard";
      } else {
        window.location.href = "/";
      }
    }
    return response;
  },
  (error) => {
    // Gérer l'expiration du token
    if (error.response && error.response.status === 401) {
      // Rediriger vers la page de connexion en cas d'erreur d'authentification
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }

    // Gérer les erreurs d'accès refusé (403)
    if (error.response && error.response.status === 403) {
      // Récupérer l'utilisateur actuel
      const userStr = localStorage.getItem("user");
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          // Rediriger vers la page d'accueil appropriée en fonction du rôle
          if (user.role === 'ADMIN') {
            window.location.href = "/admin/analytics";
          } else if (user.role === 'RESPONSABLE') {
            window.location.href = "/responsable/analytics";
          } else if (user.role === 'VENDEUR') {
            window.location.href = "/vendeur/analytics";
          } else if (user.role === 'CLIENT') {
            window.location.href = "/client/dashboard";
          } else {
            window.location.href = "/";
          }
          return Promise.reject(error);
        } catch (e) {
          console.error("Erreur lors du parsing de l'utilisateur:", e);
        }
      }
      // Si on ne peut pas rediriger en fonction du rôle, rediriger vers la page d'accueil
      window.location.href = "/";
    }

    // Gérer les erreurs réseau (ERR_BAD_RESPONSE, etc.)
    if (!error.response) {
      console.error('Erreur réseau:', error.message);
      error.customMessage = 'Erreur de connexion au serveur. Veuillez réessayer.';

      // Ajouter des informations supplémentaires pour le débogage
      console.error('Détails de l\'erreur réseau:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        config: error.config ? {
          url: error.config.url,
          method: error.config.method,
          headers: error.config.headers,
          timeout: error.config.timeout
        } : 'No config available'
      });
    } else {
      // Log des erreurs avec réponse du serveur
      console.error('Erreur API avec réponse:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
        config: error.config ? {
          url: error.config.url,
          method: error.config.method
        } : 'No config available'
      });
    }

    return Promise.reject(error);
  },
);

// Exporter aussi l'ancienne façon pour compatibilité
export default api;