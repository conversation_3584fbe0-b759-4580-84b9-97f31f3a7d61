{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/hmr-refresh-reducer.ts"], "sourcesContent": ["import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  HmrRefreshAction,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\n\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(\n  state: ReadonlyReducerState,\n  action: HmrRefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  const navigatedAt = Date.now()\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [state.tree[0], state.tree[1], state.tree[2], 'refetch'],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n    isHmrRefresh: true,\n  })\n\n  return cache.lazyData.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n\n      for (const normalizedFlightData of flightData) {\n        const { tree: treePatch, isRootRender } = normalizedFlightData\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n        const applied = applyFlightData(\n          navigatedAt,\n          currentCache,\n          cache,\n          normalizedFlightData\n        )\n\n        if (applied) {\n          mutable.cache = cache\n          currentCache = cache\n        }\n\n        mutable.patchedTree = newTree\n        mutable.canonicalUrl = href\n\n        currentTree = newTree\n      }\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n\nfunction hmrRefreshReducerNoop(\n  state: ReadonlyReducerState,\n  _action: HmrRefreshAction\n): ReducerState {\n  return state\n}\n\nexport const hmrRefreshReducer =\n  process.env.NODE_ENV === 'production'\n    ? hmrRefreshReducerNoop\n    : hmrRefreshReducerImpl\n"], "names": ["hmrRefreshReducer", "hmrRefreshReducerImpl", "state", "action", "origin", "mutable", "href", "canonicalUrl", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "tree", "navigatedAt", "Date", "now", "lazyData", "fetchServerResponse", "URL", "flightRouterState", "nextUrl", "isHmrRefresh", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "normalizedFlightData", "treePatch", "isRootRender", "console", "log", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applied", "applyFlightData", "patchedTree", "handleMutable", "hmrRefreshReducerNoop", "_action", "process", "env", "NODE_ENV"], "mappings": ";;;;+BA+HaA;;;eAAAA;;;qCA/HuB;mCACF;6CACU;6CACA;iCAOV;+BACJ;iCACE;2BAEK;uCACC;mDACY;AAElD,wFAAwF;AACxF,SAASC,sBACPC,KAA2B,EAC3BC,MAAwB;IAExB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/BF,QAAQG,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAC7C,sFAAsF;IACtF,sHAAsH;IACtH,MAAMC,iBAAiBC,IAAAA,oEAAiC,EAACV,MAAMW,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxC,MAAMC,cAAcC,KAAKC,GAAG;IAC5BP,MAAMQ,QAAQ,GAAGC,IAAAA,wCAAmB,EAAC,IAAIC,IAAIb,MAAMF,SAAS;QAC1DgB,mBAAmB;YAAClB,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAE;SAAU;QAC3EQ,SAASV,iBAAiBT,MAAMmB,OAAO,GAAG;QAC1CC,cAAc;IAChB;IAEA,OAAOb,MAAMQ,QAAQ,CAACM,IAAI,CACxB;YAAC,EAAEC,UAAU,EAAEjB,cAAckB,oBAAoB,EAAE;QACjD,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBxB,OACAG,SACAmB,YACAtB,MAAMyB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DnB,MAAMQ,QAAQ,GAAG;QAEjB,IAAIY,cAAc3B,MAAMW,IAAI;QAC5B,IAAIiB,eAAe5B,MAAMO,KAAK;QAE9B,KAAK,MAAMsB,wBAAwBP,WAAY;YAC7C,MAAM,EAAEX,MAAMmB,SAAS,EAAEC,YAAY,EAAE,GAAGF;YAC1C,IAAI,CAACE,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOjC;YACT;YAEA,MAAMkC,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJR,aACAG,WACA9B,MAAMK,YAAY;YAGpB,IAAI6B,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAACpC,OAAOC,QAAQ6B;YAC9C;YAEA,IAAIO,IAAAA,wDAA2B,EAACV,aAAaO,UAAU;gBACrD,OAAOV,IAAAA,kCAAiB,EACtBxB,OACAG,SACAC,MACAJ,MAAMyB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMY,2BAA2Bf,uBAC7BgB,IAAAA,oCAAiB,EAAChB,wBAClBiB;YAEJ,IAAIjB,sBAAsB;gBACxBpB,QAAQE,YAAY,GAAGiC;YACzB;YACA,MAAMG,UAAUC,IAAAA,gCAAe,EAC7B9B,aACAgB,cACArB,OACAsB;YAGF,IAAIY,SAAS;gBACXtC,QAAQI,KAAK,GAAGA;gBAChBqB,eAAerB;YACjB;YAEAJ,QAAQwC,WAAW,GAAGT;YACtB/B,QAAQE,YAAY,GAAGD;YAEvBuB,cAAcO;QAChB;QACA,OAAOU,IAAAA,4BAAa,EAAC5C,OAAOG;IAC9B,GACA,IAAMH;AAEV;AAEA,SAAS6C,sBACP7C,KAA2B,EAC3B8C,OAAyB;IAEzB,OAAO9C;AACT;AAEO,MAAMF,oBACXiD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBJ,wBACA9C"}