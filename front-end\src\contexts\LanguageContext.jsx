import React, { createContext, useContext, useState, useEffect } from 'react';
import { getParametresData, updateParametresData } from '../services/parametresService';

// Create the language context
const LanguageContext = createContext();

// Language provider component
export const LanguageProvider = ({ children }) => {
  // Initialize with localStorage value or default to French
  const [language, setLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('language');
    return savedLanguage === 'en' || savedLanguage === 'fr' ? savedLanguage : 'fr';
  });
  const [loading, setLoading] = useState(true);

  // Fetch the language setting from the backend on component mount
  useEffect(() => {
    const fetchLanguage = async () => {
      try {
        const response = await getParametresData();
        // The getParametresData function now returns default values on error
        if (response.data && response.data.defaultLanguage) {
          const backendLanguage = response.data.defaultLanguage;
          // Only update if different from localStorage to avoid unnecessary renders
          if (backendLanguage !== language && (backendLanguage === 'fr' || backendLanguage === 'en')) {
            setLanguage(backendLanguage);
            localStorage.setItem('language', backendLanguage);
          }
        }
      } catch (error) {
        // This should not happen anymore, but just in case
        console.error('Error fetching language setting:', error);
        // Ensure we use a valid language
        const defaultLang = 'fr';
        if (language !== defaultLang) {
          setLanguage(defaultLang);
          localStorage.setItem('language', defaultLang);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchLanguage();
  }, []);

  // Function to change the language
  const changeLanguage = async (newLanguage) => {
    if (newLanguage === 'fr' || newLanguage === 'en') {
      // Update local state immediately for better user experience
      setLanguage(newLanguage);
      localStorage.setItem('language', newLanguage);

      try {
        // Update the language in the backend settings
        // The updateParametresData function now handles network errors internally
        const result = await updateParametresData({
          defaultLanguage: newLanguage
        });

        if (!result.success && result.message === 'Error updating parameters') {
          console.warn('Language was updated locally but not saved to the server');
        }
      } catch (error) {
        // This should not happen anymore, but just in case
        console.error('Error updating language setting in backend:', error);
      }
    }
  };

  // Context value
  const value = {
    language,
    changeLanguage,
    loading,
    isEnglish: language === 'en',
    isFrench: language === 'fr',
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook for using language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage doit être utilisé à l'intérieur d'un LanguageProvider");
  }
  return context;
};

export default LanguageContext;
