import api from './api';

const responsableEntrepriseService = {
  // Récupérer toutes les entreprises
  getAllEntreprises: async () => {
    try {
      console.log('Récupération de toutes les entreprises');
      const response = await api.get('/entreprises');
      console.log('Réponse de l\'API entreprises:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des entreprises:', error);
      // Retourner un tableau vide au lieu de lancer une erreur
      console.log('Retour d\'un tableau vide comme fallback');
      return [];
    }
  },

  // Récupérer une entreprise par ID
  getEntrepriseById: async (id) => {
    try {
      const response = await api.get(`/entreprises/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'entreprise ${id}:`, error);
      throw new Error(error.response?.data?.message || `Erreur lors de la récupération de l'entreprise ${id}`);
    }
  },

  // Récupérer l'entreprise d'un responsable
  getEntrepriseByResponsable: async (responsableId) => {
    try {
      console.log(`Récupération de l'entreprise pour le responsable ${responsableId}`);
      // Vérifier que l'ID est valide
      if (!responsableId || responsableId === 'undefined') {
        console.log('ID de responsable invalide:', responsableId);
        return null;
      }

      const response = await api.get(`/entreprises/responsable/${responsableId}`);
      console.log(`Entreprise trouvée pour le responsable ${responsableId}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'entreprise pour le responsable ${responsableId}:`, error);
      // Si l'entreprise n'est pas trouvée, on retourne null au lieu de lancer une erreur
      if (error.response?.status === 404) {
        console.log(`Aucune entreprise trouvée pour le responsable ${responsableId}`);
        return null;
      }
      console.log(`Erreur lors de la récupération de l'entreprise pour le responsable ${responsableId}:`, error.response?.data);
      // Retourner null au lieu de lancer une erreur pour éviter de bloquer l'interface
      return null;
    }
  },

  // Créer une entreprise manuellement (admin uniquement)
  createEntreprise: async (entrepriseData) => {
    try {
      const response = await api.post('/entreprises', entrepriseData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de l\'entreprise:', error);
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'entreprise');
    }
  }
};

export default responsableEntrepriseService;
