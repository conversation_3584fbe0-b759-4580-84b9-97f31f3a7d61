const mongoose = require('mongoose');
const { Schema } = mongoose;

const parametresSchema = new Schema({
  // Paramètres système
  defaultCurrency: { type: String, default: 'TND' },
  defaultTaxRate: { type: Number, default: 19 },

  // Paramètres d'email
  emailSignature: { type: String, default: 'Cordialement,\nL\'équipe de Mon Entreprise' },

  // Paramètres de facturation
  invoicePrefix: { type: String, default: 'FACT-' },
  quotePrefix: { type: String, default: 'DEV-' },
  paymentDelay: { type: Number, default: 30 },

  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Middleware pour mettre à jour automatiquement la date de modification
parametresSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Parametres', parametresSchema);