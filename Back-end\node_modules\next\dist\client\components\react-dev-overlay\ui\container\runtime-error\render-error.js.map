{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/container/runtime-error/render-error.tsx"], "sourcesContent": ["import type {\n  OverlayState,\n  UnhandledErrorAction,\n  UnhandledRejectionAction,\n} from '../../../shared'\n\nimport { useMemo, useState, useEffect } from 'react'\nimport {\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n} from '../../../shared'\nimport {\n  getErrorByType,\n  type ReadyRuntimeError,\n} from '../../../utils/get-error-by-type'\n\nexport type SupportedErrorEvent = {\n  id: number\n  event: UnhandledErrorAction | UnhandledRejectionAction\n}\n\nfunction getErrorSignature(ev: SupportedErrorEvent): string {\n  const { event } = ev\n  // eslint-disable-next-line default-case -- <PERSON><PERSON> checks this\n  switch (event.type) {\n    case ACTION_UNHANDLED_ERROR:\n    case ACTION_UNHANDLED_REJECTION: {\n      return `${event.reason.name}::${event.reason.message}::${event.reason.stack}`\n    }\n  }\n}\n\ntype Props = {\n  children: (params: {\n    runtimeErrors: ReadyRuntimeError[]\n    totalErrorCount: number\n  }) => React.ReactNode\n  state: OverlayState\n  isAppDir: boolean\n}\n\nexport const RenderError = (props: Props) => {\n  const { state } = props\n  const isBuildError = !!state.buildError\n\n  if (isBuildError) {\n    return <RenderBuildError {...props} />\n  } else {\n    return <RenderRuntimeError {...props} />\n  }\n}\n\nconst RenderRuntimeError = ({ children, state, isAppDir }: Props) => {\n  const { errors } = state\n\n  const [lookups, setLookups] = useState<{\n    [eventId: string]: ReadyRuntimeError\n  }>({})\n\n  const [runtimeErrors, nextError] = useMemo<\n    [ReadyRuntimeError[], SupportedErrorEvent | null]\n  >(() => {\n    let ready: ReadyRuntimeError[] = []\n    let next: SupportedErrorEvent | null = null\n\n    // Ensure errors are displayed in the order they occurred in:\n    for (let idx = 0; idx < errors.length; ++idx) {\n      const e = errors[idx]\n      const { id } = e\n      if (id in lookups) {\n        ready.push(lookups[id])\n        continue\n      }\n\n      // Check for duplicate errors\n      if (idx > 0) {\n        const prev = errors[idx - 1]\n        if (getErrorSignature(prev) === getErrorSignature(e)) {\n          continue\n        }\n      }\n\n      next = e\n      break\n    }\n\n    return [ready, next]\n  }, [errors, lookups])\n\n  useEffect(() => {\n    if (nextError == null) {\n      return\n    }\n\n    let mounted = true\n\n    getErrorByType(nextError, isAppDir).then((resolved) => {\n      if (mounted) {\n        // We don't care if the desired error changed while we were resolving,\n        // thus we're not tracking it using a ref. Once the work has been done,\n        // we'll store it.\n        setLookups((m) => ({ ...m, [resolved.id]: resolved }))\n      }\n    })\n\n    return () => {\n      mounted = false\n    }\n  }, [nextError, isAppDir])\n\n  const totalErrorCount = errors.filter((err, idx) => {\n    const prev = errors[idx - 1]\n    // Check for duplicates\n    if (idx > 0) return getErrorSignature(prev) !== getErrorSignature(err)\n    return true\n  }).length\n\n  return children({ runtimeErrors, totalErrorCount })\n}\n\nconst RenderBuildError = ({ children }: Props) => {\n  return children({\n    runtimeErrors: [],\n    // Build errors and missing root layout tags persist until fixed,\n    // so we can set a fixed error count of 1\n    totalErrorCount: 1,\n  })\n}\n"], "names": ["RenderError", "getErrorSignature", "ev", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "reason", "name", "message", "stack", "props", "state", "isBuildError", "buildError", "RenderBuildError", "RenderRuntimeError", "children", "isAppDir", "errors", "lookups", "setLookups", "useState", "runtimeErrors", "nextError", "useMemo", "ready", "next", "idx", "length", "e", "id", "push", "prev", "useEffect", "mounted", "getErrorByType", "then", "resolved", "m", "totalErrorCount", "filter", "err"], "mappings": ";;;;+BAyCaA;;;eAAAA;;;;uBAnCgC;wBAItC;gCAIA;AAOP,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,kEAAkE;IAClE,OAAQC,MAAMC,IAAI;QAChB,KAAKC,8BAAsB;QAC3B,KAAKC,kCAA0B;YAAE;gBAC/B,OAAO,AAAGH,MAAMI,MAAM,CAACC,IAAI,GAAC,OAAIL,MAAMI,MAAM,CAACE,OAAO,GAAC,OAAIN,MAAMI,MAAM,CAACG,KAAK;YAC7E;IACF;AACF;AAWO,MAAMV,cAAc,CAACW;IAC1B,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,eAAe,CAAC,CAACD,MAAME,UAAU;IAEvC,IAAID,cAAc;QAChB,qBAAO,qBAACE;YAAkB,GAAGJ,KAAK;;IACpC,OAAO;QACL,qBAAO,qBAACK;YAAoB,GAAGL,KAAK;;IACtC;AACF;AAEA,MAAMK,qBAAqB;QAAC,EAAEC,QAAQ,EAAEL,KAAK,EAAEM,QAAQ,EAAS;IAC9D,MAAM,EAAEC,MAAM,EAAE,GAAGP;IAEnB,MAAM,CAACQ,SAASC,WAAW,GAAGC,IAAAA,eAAQ,EAEnC,CAAC;IAEJ,MAAM,CAACC,eAAeC,UAAU,GAAGC,IAAAA,cAAO,EAExC;QACA,IAAIC,QAA6B,EAAE;QACnC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMT,OAAOU,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAIX,MAAM,CAACS,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMX,SAAS;gBACjBM,MAAMM,IAAI,CAACZ,OAAO,CAACW,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOd,MAAM,CAACS,MAAM,EAAE;gBAC5B,IAAI3B,kBAAkBgC,UAAUhC,kBAAkB6B,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACR;QAAQC;KAAQ;IAEpBc,IAAAA,gBAAS,EAAC;QACR,IAAIV,aAAa,MAAM;YACrB;QACF;QAEA,IAAIW,UAAU;QAEdC,IAAAA,8BAAc,EAACZ,WAAWN,UAAUmB,IAAI,CAAC,CAACC;YACxC,IAAIH,SAAS;gBACX,sEAAsE;gBACtE,uEAAuE;gBACvE,kBAAkB;gBAClBd,WAAW,CAACkB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAASP,EAAE,CAAC,EAAEO;oBAAS,CAAA;YACrD;QACF;QAEA,OAAO;YACLH,UAAU;QACZ;IACF,GAAG;QAACX;QAAWN;KAAS;IAExB,MAAMsB,kBAAkBrB,OAAOsB,MAAM,CAAC,CAACC,KAAKd;QAC1C,MAAMK,OAAOd,MAAM,CAACS,MAAM,EAAE;QAC5B,uBAAuB;QACvB,IAAIA,MAAM,GAAG,OAAO3B,kBAAkBgC,UAAUhC,kBAAkByC;QAClE,OAAO;IACT,GAAGb,MAAM;IAET,OAAOZ,SAAS;QAAEM;QAAeiB;IAAgB;AACnD;AAEA,MAAMzB,mBAAmB;QAAC,EAAEE,QAAQ,EAAS;IAC3C,OAAOA,SAAS;QACdM,eAAe,EAAE;QACjB,iEAAiE;QACjE,yCAAyC;QACzCiB,iBAAiB;IACnB;AACF"}