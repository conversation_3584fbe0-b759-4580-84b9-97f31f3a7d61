const mongoose = require('mongoose');
const { Schema } = mongoose;

const livreurSchema = new Schema({
    // Informations personnelles
    nom: { 
        type: String, 
        required: [true, 'Le nom est obligatoire'],
        trim: true
    },
    prenom: { 
        type: String, 
        required: [true, 'Le prénom est obligatoire'],
        trim: true
    },
    telephone: { 
        type: String, 
        required: [true, 'Le numéro de téléphone est obligatoire'],
        trim: true
    },
    email: { 
        type: String, 
        trim: true,
        lowercase: true
    },
    cin: { 
        type: String, 
        trim: true
    },
    adresse: { 
        type: String, 
        trim: true
    },

    // Informations véhicule
    vehicule: {
        type: { 
            type: String, 
            enum: ['Voiture', 'Moto', 'Camionnette', 'Camion', 'Vé<PERSON>', 'Autre'],
            default: 'Voiture'
        },
        marque: { type: String, trim: true },
        modele: { type: String, trim: true },
        immatriculation: { type: String, trim: true },
        couleur: { type: String, trim: true }
    },

    // Statut et disponibilité
    statut: {
        type: String,
        enum: ['ACTIF', 'INACTIF', 'SUSPENDU'],
        default: 'ACTIF'
    },
    disponible: {
        type: Boolean,
        default: true
    },

    // Statistiques de livraison
    statistiques: {
        nombreLivraisons: { type: Number, default: 0 },
        livraisonsReussies: { type: Number, default: 0 },
        livraisonsEchouees: { type: Number, default: 0 },
        tauxReussite: { type: Number, default: 0 }, // Calculé automatiquement
        derniereLivraison: { type: Date }
    },

    // Relations
    responsableId: { 
        type: Schema.Types.ObjectId, 
        ref: 'users',
        required: [true, 'Le responsable est obligatoire']
    },

    // Notes et commentaires
    notes: { type: String, trim: true },

    // Timestamps
    dateCreation: { type: Date, default: Date.now },
    dateMiseAJour: { type: Date, default: Date.now }
}, { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Index pour améliorer les performances
livreurSchema.index({ responsableId: 1 });
livreurSchema.index({ statut: 1 });
livreurSchema.index({ telephone: 1 });

// Middleware pour mettre à jour la date de modification
livreurSchema.pre('save', function(next) {
    this.dateMiseAJour = Date.now();
    
    // Calculer le taux de réussite
    if (this.statistiques.nombreLivraisons > 0) {
        this.statistiques.tauxReussite = Math.round(
            (this.statistiques.livraisonsReussies / this.statistiques.nombreLivraisons) * 100
        );
    }
    
    next();
});

// Méthode pour obtenir le nom complet
livreurSchema.virtual('nomComplet').get(function() {
    return `${this.prenom} ${this.nom}`;
});

// Méthode pour vérifier si le livreur est disponible
livreurSchema.methods.estDisponible = function() {
    return this.statut === 'ACTIF' && this.disponible;
};

// Méthode pour mettre à jour les statistiques après une livraison
livreurSchema.methods.mettreAJourStatistiques = function(succes = true) {
    this.statistiques.nombreLivraisons += 1;
    if (succes) {
        this.statistiques.livraisonsReussies += 1;
    } else {
        this.statistiques.livraisonsEchouees += 1;
    }
    this.statistiques.derniereLivraison = new Date();
    
    // Recalculer le taux de réussite
    this.statistiques.tauxReussite = Math.round(
        (this.statistiques.livraisonsReussies / this.statistiques.nombreLivraisons) * 100
    );
    
    return this.save();
};

module.exports = mongoose.model('Livreur', livreurSchema);
