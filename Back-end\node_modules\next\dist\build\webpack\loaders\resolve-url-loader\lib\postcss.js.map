{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/postcss.ts"], "sourcesContent": ["/*\nThe MIT License (MIT)\n\nCopyright (c) 2016 Ben <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport path from 'path'\nimport { prepend, remove } from './file-protocol'\n\nconst OR<PERSON>HAN_CR_REGEX = /\\r(?!\\n)(.|\\n)?/g\n\nexport default function process(\n  postcss: any,\n  sourceFile: any,\n  sourceContent: any,\n  params: any\n) {\n  // #107 libsass emits orphan CR not considered newline, postcss does consider newline (content vs source-map mismatch)\n\n  postcssPlugin.postcss = true\n\n  // prepend file protocol to all sources to avoid problems with source map\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return postcss([postcssPlugin])\n    .process(sourceContent, {\n      from: prepend(sourceFile),\n      map: params.outputSourceMap && {\n        prev: !!params.inputSourceMap && prepend(params.inputSourceMap),\n        inline: false,\n        annotation: false,\n        sourcesContent: true, // #98 sourcesContent missing from output map\n      },\n    })\n    .then((result: any) => ({\n      content: result.css,\n      map: params.outputSourceMap ? remove(result.map.toJSON()) : null,\n    }))\n\n  /**\n   * Plugin for postcss that follows SASS transpilation.\n   */\n  function postcssPlugin() {\n    return {\n      postcssPlugin: 'postcss-resolve-url',\n      Once: function (root: any) {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        root.walkDecls(eachDeclaration)\n      },\n    }\n\n    /**\n     * Process a declaration from the syntax tree.\n     * @param declaration\n     */\n    function eachDeclaration(declaration: any) {\n      const isValid = declaration.value && declaration.value.indexOf('url') >= 0\n      if (isValid) {\n        // reverse the original source-map to find the original source file before transpilation\n        const startPosApparent = declaration.source.start,\n          startPosOriginal =\n            params.sourceMapConsumer &&\n            params.sourceMapConsumer.originalPositionFor(startPosApparent)\n\n        // we require a valid directory for the specified file\n        const directory =\n          startPosOriginal &&\n          startPosOriginal.source &&\n          remove(path.dirname(startPosOriginal.source))\n        if (directory) {\n          declaration.value = params.transformDeclaration(\n            declaration.value,\n            directory\n          )\n        }\n        // source-map present but invalid entry\n        else if (params.sourceMapConsumer) {\n          throw new Error(\n            'source-map information is not available at url() declaration ' +\n              (ORPHAN_CR_REGEX.test(sourceContent)\n                ? '(found orphan CR, try removeCR option)'\n                : '(no orphan CR found)')\n          )\n        }\n      }\n    }\n  }\n}\n"], "names": ["process", "ORPHAN_CR_REGEX", "postcss", "sourceFile", "sourceContent", "params", "postcssPlugin", "from", "prepend", "map", "outputSourceMap", "prev", "inputSourceMap", "inline", "annotation", "sourcesContent", "then", "result", "content", "css", "remove", "toJSON", "Once", "root", "walkDecls", "eachDeclaration", "declaration", "<PERSON><PERSON><PERSON><PERSON>", "value", "indexOf", "startPosApparent", "source", "start", "startPosOriginal", "sourceMapConsumer", "originalPositionFor", "directory", "path", "dirname", "transformDeclaration", "Error", "test"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BAOA;;;eAAwBA;;;6DALP;8BACe;;;;;;AAEhC,MAAMC,kBAAkB;AAET,SAASD,QACtBE,OAAY,EACZC,UAAe,EACfC,aAAkB,EAClBC,MAAW;IAEX,sHAAsH;IAEtHC,cAAcJ,OAAO,GAAG;IAExB,yEAAyE;IACzE,mEAAmE;IACnE,OAAOA,QAAQ;QAACI;KAAc,EAC3BN,OAAO,CAACI,eAAe;QACtBG,MAAMC,IAAAA,qBAAO,EAACL;QACdM,KAAKJ,OAAOK,eAAe,IAAI;YAC7BC,MAAM,CAAC,CAACN,OAAOO,cAAc,IAAIJ,IAAAA,qBAAO,EAACH,OAAOO,cAAc;YAC9DC,QAAQ;YACRC,YAAY;YACZC,gBAAgB;QAClB;IACF,GACCC,IAAI,CAAC,CAACC,SAAiB,CAAA;YACtBC,SAASD,OAAOE,GAAG;YACnBV,KAAKJ,OAAOK,eAAe,GAAGU,IAAAA,oBAAM,EAACH,OAAOR,GAAG,CAACY,MAAM,MAAM;QAC9D,CAAA;IAEF;;GAEC,GACD,SAASf;QACP,OAAO;YACLA,eAAe;YACfgB,MAAM,SAAUC,IAAS;gBACvB,mEAAmE;gBACnEA,KAAKC,SAAS,CAACC;YACjB;QACF;QAEA;;;KAGC,GACD,SAASA,gBAAgBC,WAAgB;YACvC,MAAMC,UAAUD,YAAYE,KAAK,IAAIF,YAAYE,KAAK,CAACC,OAAO,CAAC,UAAU;YACzE,IAAIF,SAAS;gBACX,wFAAwF;gBACxF,MAAMG,mBAAmBJ,YAAYK,MAAM,CAACC,KAAK,EAC/CC,mBACE5B,OAAO6B,iBAAiB,IACxB7B,OAAO6B,iBAAiB,CAACC,mBAAmB,CAACL;gBAEjD,sDAAsD;gBACtD,MAAMM,YACJH,oBACAA,iBAAiBF,MAAM,IACvBX,IAAAA,oBAAM,EAACiB,aAAI,CAACC,OAAO,CAACL,iBAAiBF,MAAM;gBAC7C,IAAIK,WAAW;oBACbV,YAAYE,KAAK,GAAGvB,OAAOkC,oBAAoB,CAC7Cb,YAAYE,KAAK,EACjBQ;gBAEJ,OAEK,IAAI/B,OAAO6B,iBAAiB,EAAE;oBACjC,MAAM,qBAKL,CALK,IAAIM,MACR,kEACGvC,CAAAA,gBAAgBwC,IAAI,CAACrC,iBAClB,2CACA,sBAAqB,IAJvB,qBAAA;+BAAA;oCAAA;sCAAA;oBAKN;gBACF;YACF;QACF;IACF;AACF"}