import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  Divider,
  Card,
  useTheme,
  InputAdornment,
  IconButton,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Grid,
  Paper,
  CircularProgress,
  MenuItem,
  Select,
  InputLabel,
  FormHelperText,
  Link
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { getUsersByRole } from '../services/userService';
import logo from '../assets/benyounes_logo.png';
// Icons
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import BusinessIcon from '@mui/icons-material/Business';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import DescriptionIcon from '@mui/icons-material/Description';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

const SignUp = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { register, login } = useAuth();
  const [formData, setFormData] = useState({
    // Common fields
    prenom: '',
    nom: '',
    email: '',
    motDePasse: '',
    role: 'CLIENT',
    // Admin specific fields
    nomEntreprise: '',
    adresseEntreprise: '',
    telephoneEntreprise: '',
    numeroFiscal: '',
    // Client specific fields
    adresse: '',
    telephone: '',
    contact: '',
    cin: '', // Numéro de Carte d'Identité Nationale
    // Relation fields
    responsables: []
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [entreprisesList, setEntreprisesList] = useState([]);
  const [validationErrors, setValidationErrors] = useState({
    cin: ''
  });

  useEffect(() => {
    // Simulate loading for demo purposes
    const timer = setTimeout(() => {
      // Any initialization code can go here
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Fetch responsables when role is CLIENT
  useEffect(() => {
    if (formData.role === 'CLIENT') {
      const fetchResponsables = async () => {
        try {
          setLoading(true);
          console.log('Fetching responsables for role:', formData.role);
          const responsables = await getUsersByRole('RESPONSABLE');
          console.log('Responsables fetched:', responsables);

          if (Array.isArray(responsables) && responsables.length > 0) {
            setEntreprisesList(responsables);
            setError(''); // Clear any previous error
          } else {
            console.log('No responsables found or empty array returned');
            setEntreprisesList([]);
            // Ne pas afficher d'erreur si le tableau est vide, c'est peut-être normal
          }
        } catch (error) {
          console.error('Error fetching responsables:', error);
          setEntreprisesList([]); // Ensure we have an empty array at least
          setError('Erreur lors de la récupération des responsables d\'entreprise. Veuillez réessayer.');
        } finally {
          setLoading(false);
        }
      };

      fetchResponsables();
    }
  }, [formData.role]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Validation spécifique pour le CIN
    if (name === 'cin') {
      // Vérifier que le CIN contient exactement 8 chiffres
      const cinRegex = /^\d{8}$/;
      if (!cinRegex.test(value)) {
        setValidationErrors(prev => ({
          ...prev,
          cin: 'Le CIN doit contenir exactement 8 chiffres'
        }));
      } else {
        setValidationErrors(prev => ({
          ...prev,
          cin: ''
        }));
      }
    }
  };

  const handleRoleChange = (e) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, role: value }));
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    // Common fields validation
    if (!formData.prenom || !formData.nom || !formData.email || !formData.motDePasse) {
      return false;
    }

    // Pour les clients, exiger adresse, téléphone et CIN
    if (formData.role === 'CLIENT') {
      // Vérifier que le CIN contient exactement 8 chiffres
      const cinRegex = /^\d{8}$/;
      if (!cinRegex.test(formData.cin)) {
        setValidationErrors(prev => ({
          ...prev,
          cin: 'Le CIN doit contenir exactement 8 chiffres'
        }));
        return false;
      }

      return formData.adresse && formData.telephone && formData.cin;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Form submitted with data:', formData);
    setError('');
    setSuccess('');
    setLoading(true);

    if (!validateForm()) {
      setError('Veuillez remplir tous les champs obligatoires');
      setLoading(false);
      return;
    }

    try {
      // Prepare data based on role
      const userData = { ...formData };

      // Register the user
      await register(userData);
      setSuccess('Inscription réussie ! Connexion en cours...');

      // Automatically log the user in after signup
      const loginResponse = await login(formData.email, formData.motDePasse, formData.role);
      console.log('Auto-login response:', loginResponse);

      // Redirect to the client dashboard
      setTimeout(() => {
        navigate('/client');
      }, 2000);
    } catch (err) {
      console.error('Signup error:', err);
      setError(err.response?.data?.error || 'Erreur lors de l\'inscription');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      component={motion.div}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      sx={{
        display: 'flex',
        minHeight: '100vh',
        backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient 15s ease infinite',
        alignItems: 'center',
        justifyContent: 'center',
        padding: { xs: 2, md: 4 },
        '@keyframes gradient': {
          '0%': {
            backgroundPosition: '0% 50%'
          },
          '50%': {
            backgroundPosition: '100% 50%'
          },
          '100%': {
            backgroundPosition: '0% 50%'
          },
        },
      }}
    >
      <Card
        elevation={6}
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          width: '100%',
          maxWidth: '1000px',
          borderRadius: 4,
          overflow: 'hidden',
          background: '#fff',
        }}
      >
        {/* Left Side - Brand Section */}
        <Box
          component={motion.div}
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.45 },
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            backgroundSize: '200% 200%',
            animation: 'gradientBg 10s ease infinite',
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 4,
            position: 'relative',
            overflow: 'hidden',
            '@keyframes gradientBg': {
              '0%': {
                backgroundPosition: '0% 50%'
              },
              '50%': {
                backgroundPosition: '100% 50%'
              },
              '100%': {
                backgroundPosition: '0% 50%'
              },
            },
          }}
        >
          {/* Background Pattern */}
          <Box sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            opacity: 0.1,
            backgroundImage: `
              radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
              radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 40%)
            `,
            zIndex: 0,
          }} />

          <Box sx={{
            textAlign: 'center',
            position: 'relative',
            zIndex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}>
            <Box
              component={motion.img}
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              src={logo}
              alt="BENYOUNES WEB Logo"
              sx={{
                width: '200px',
                mb: 3,
                filter: 'brightness(1.05)',
                animation: 'float 6s ease-in-out infinite',
                '@keyframes float': {
                  '0%': {
                    transform: 'translateY(0px)'
                  },
                  '50%': {
                    transform: 'translateY(-10px)'
                  },
                  '100%': {
                    transform: 'translateY(0px)'
                  },
                },
              }}
            />

            <Typography
              component={motion.h1}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.5 }}
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 2,
                letterSpacing: '0.5px',
                textShadow: '0 2px 10px rgba(0,0,0,0.1)',
              }}
            >
              Bienvenue !
            </Typography>

            <Typography
              component={motion.p}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.5 }}
              variant="body1"
              sx={{
                mb: 3,
                opacity: 0.9,
                maxWidth: '350px',
                textAlign: 'center',
                lineHeight: 1.6,
              }}
            >
              Créez votre compte pour commencer à gérer vos factures et devis en toute simplicité.
            </Typography>

            <Box sx={{ mt: 3, display: { xs: 'none', md: 'block' }}}>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                © {new Date().getFullYear()} BENYOUNES WEB - Tous droits réservés
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Right Side - Form */}
        <Box
          component={motion.div}
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.55 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            padding: { xs: 3, sm: 4, md: 5 },
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Box sx={{ maxWidth: '450px', width: '100%', mx: 'auto' }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 1,
                color: theme.palette.text.primary,
              }}
            >
              Inscription
            </Typography>

            <Typography
              variant="body2"
              sx={{
                mb: 4,
                color: theme.palette.text.secondary,
              }}
            >
              Remplissez le formulaire pour créer votre compte
            </Typography>

            {error && (
              <Alert
                severity="error"
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                {error}
              </Alert>
            )}

            {success && (
              <Alert
                severity="success"
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                {success}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Box
                component={motion.div}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Type de compte */}
                <FormControl
                  fullWidth
                  margin="normal"
                  sx={{
                    mb: 3,
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: theme.palette.background.default,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      backgroundColor: '#fff',
                      boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                    }
                  }}
                >
                  <FormLabel id="role-radio-group-label" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <AccountCircleIcon color="action" sx={{ mr: 1 }} />
                    Type de compte
                  </FormLabel>
                  <Typography variant="body1" sx={{ color: theme.palette.text.secondary }}>
                    Inscription en tant que client
                  </Typography>
                  <input type="hidden" name="role" value="CLIENT" />
                </FormControl>

                {/* Informations de base */}
                <Typography variant="h6" sx={{ mt: 2, mb: 2 }}>
                  Informations de base
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Prénom"
                      name="prenom"
                      value={formData.prenom}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 2,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          backgroundColor: theme.palette.background.default,
                          transition: 'all 0.3s ease',
                          '&:hover, &.Mui-focused': {
                            backgroundColor: '#fff',
                            boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                          }
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Nom"
                      name="nom"
                      value={formData.nom}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 2,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          backgroundColor: theme.palette.background.default,
                          transition: 'all 0.3s ease',
                          '&:hover, &.Mui-focused': {
                            backgroundColor: '#fff',
                            boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                          }
                        },
                      }}
                    />
                  </Grid>
                </Grid>

                <TextField
                  label="Email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                <TextField
                  label="Mot de passe"
                  name="motDePasse"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.motDePasse}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                          aria-label={showPassword ? 'hide password' : 'show password'}
                        >
                          {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                {/* Informations spécifiques au rôle */}
                {(formData.role === 'ADMIN' || formData.role === 'ENTREPRISE') && (
                  <>
                    <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                      Informations de l'entreprise
                    </Typography>

                    <TextField
                      label="Nom de l'entreprise"
                      name="nomEntreprise"
                      value={formData.nomEntreprise}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <BusinessIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      label="Adresse de l'entreprise"
                      name="adresseEntreprise"
                      value={formData.adresseEntreprise}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationOnIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      label="Téléphone de l'entreprise"
                      name="telephoneEntreprise"
                      value={formData.telephoneEntreprise}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PhoneIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      label="Numéro fiscal"
                      name="numeroFiscal"
                      value={formData.numeroFiscal}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <DescriptionIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />
                  </>
                )}

                {(formData.role === 'VENDEUR' || formData.role === 'ENTREPRISE' || formData.role === 'CLIENT') && (
                  <>
                    <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                      Informations personnelles
                    </Typography>

                    <TextField
                      label="Adresse"
                      name="adresse"
                      value={formData.adresse}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationOnIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      label="Téléphone"
                      name="telephone"
                      value={formData.telephone}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PhoneIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      label="Personne à contacter (optionnel)"
                      name="contact"
                      value={formData.contact}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    <TextField
                      label="Numéro CIN (8 chiffres)"
                      name="cin"
                      value={formData.cin}
                      onChange={handleChange}
                      fullWidth
                      margin="normal"
                      required
                      error={!!validationErrors.cin}
                      helperText={validationErrors.cin}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <DescriptionIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />

                    {formData.role === 'CLIENT' && (
                      <FormControl fullWidth margin="normal" sx={{ mb: 2 }}>
                        <InputLabel>Responsables d'entreprise (optionnel)</InputLabel>
                        <Select
                          name="responsables"
                          multiple
                          value={formData.responsables || []}
                          onChange={(e) => {
                            const selectedResponsables = e.target.value;
                            setFormData(prev => ({
                              ...prev,
                              responsables: selectedResponsables
                            }));
                          }}
                          label="Responsables d'entreprise (optionnel)"
                          disabled={loading}
                          startAdornment={
                            <InputAdornment position="start">
                              <BusinessIcon color="action" />
                            </InputAdornment>
                          }
                        >
                          {loading ? (
                            <MenuItem disabled>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <CircularProgress size={20} sx={{ mr: 1 }} />
                                <em>Chargement des responsables...</em>
                              </Box>
                            </MenuItem>
                          ) : entreprisesList.length > 0 ? (
                            entreprisesList.map((responsable) => (
                              <MenuItem key={responsable._id} value={responsable._id}>
                                {responsable.nomEntreprise || responsable.nom}
                              </MenuItem>
                            ))
                          ) : (
                            <MenuItem disabled>
                              <em>Aucun responsable d'entreprise disponible</em>
                            </MenuItem>
                          )}
                        </Select>
                        <FormHelperText>
                          Vous pouvez sélectionner un ou plusieurs responsables d'entreprise
                        </FormHelperText>
                      </FormControl>
                    )}
                  </>
                )}

                <Typography
                  variant="body2"
                  sx={{
                    display: 'block',
                    mt: 3,
                    mb: 3,
                    color: theme.palette.text.secondary,
                  }}
                >
                  En créant un compte, vous acceptez les <Typography component="span" variant="body2" sx={{ color: theme.palette.primary.main, fontWeight: 500 }}>Conditions d'utilisation</Typography> et la <Typography component="span" variant="body2" sx={{ color: theme.palette.primary.main, fontWeight: 500 }}>Politique de confidentialité</Typography>.
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                  <Button
                    onClick={() => navigate('/login')}
                    variant="outlined"
                    sx={{
                      borderRadius: 2,
                      padding: '10px 20px',
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 8px rgba(0,0,0,0.05)',
                      },
                    }}
                  >
                    Retour
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={loading}
                    disableElevation
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
                    sx={{
                      borderRadius: 2,
                      padding: '10px 20px',
                      backgroundColor: theme.palette.primary.main,
                      position: 'relative',
                      overflow: 'hidden',
                      transition: 'all 0.3s',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: '-100%',
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                        transition: 'all 0.5s',
                      },
                      '&:hover': {
                        backgroundColor: theme.palette.primary.dark,
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(63, 81, 255, 0.25)',
                        '&::after': {
                          left: '100%',
                        }
                      },
                    }}
                  >
                    {loading ? 'Création en cours...' : 'S\'inscrire'}
                  </Button>
                </Box>
              </Box>
            </form>



            <Typography
              variant="body2"
              sx={{
                mt: 3,
                textAlign: 'center',
                color: theme.palette.text.secondary,
              }}
            >
              Vous avez déjà un compte ?{' '}
              <Typography
                component="a"
                href="/login"
                variant="body2"
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 600,
                  '&:hover': {
                    textDecoration: 'underline',
                  }
                }}
              >
                Connectez-vous
              </Typography>
            </Typography>
          </Box>
        </Box>
      </Card>
    </Box>
  );
};

export default SignUp;