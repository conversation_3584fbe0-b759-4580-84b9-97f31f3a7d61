import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  Snackbar
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import abonnementService from '../../../services/abonnementService';
import userService from '../../../services/userService';
import clientService from '../../../services/clientService';
import responsableEntrepriseService from '../../../services/responsableEntrepriseService';

const AbonnementForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [responsables, setResponsables] = useState([]);
  const [entreprises, setEntreprises] = useState([]);
  const [formData, setFormData] = useState({
    responsableId: '',
    entrepriseId: '',
    duree: '1_AN',
    dateDebut: new Date().toISOString().split('T')[0],
    statut: 'ACTIF',
    notes: ''
  });
  const [errors, setErrors] = useState({});
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState('error');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Charger les responsables d'entreprise
        const usersData = await userService.getAllUsers();
        const responsablesData = usersData.filter(user => user.role === 'RESPONSABLE');
        console.log('Responsables récupérés:', responsablesData);
        setResponsables(responsablesData);

        // Charger les entreprises des responsables
        try {
          const entreprisesData = await responsableEntrepriseService.getAllEntreprises();
          console.log('Entreprises des responsables récupérées:', entreprisesData);

          // Créer un tableau combiné d'entreprises
          let combinedEntreprises = [];

          if (entreprisesData && entreprisesData.length > 0) {
            combinedEntreprises = [...entreprisesData];
          }

          // Ajouter les clients comme fallback
          const clientsData = await clientService.getClients();
          console.log('Clients récupérés comme fallback:', clientsData);

          // Filtrer les clients qui ne sont pas déjà dans les entreprises
          const clientsToAdd = clientsData.filter(client =>
            !combinedEntreprises.some(ent => ent._id === client._id)
          );

          combinedEntreprises = [...combinedEntreprises, ...clientsToAdd];
          console.log('Liste combinée d\'entreprises:', combinedEntreprises);

          setEntreprises(combinedEntreprises);
        } catch (entrepriseError) {
          console.error('Erreur lors de la récupération des entreprises des responsables:', entrepriseError);
          // Fallback: charger uniquement les clients
          const clientsData = await clientService.getClients();
          console.log('Clients récupérés après erreur:', clientsData);
          setEntreprises(clientsData);
        }

        // Si en mode édition, charger les données de l'abonnement
        if (isEditMode) {
          const abonnementData = await abonnementService.getAbonnementById(id);
          console.log('Données d\'abonnement récupérées:', abonnementData);

          // Formater la date pour l'input date
          const dateDebut = new Date(abonnementData.dateDebut).toISOString().split('T')[0];

          setFormData({
            ...abonnementData,
            dateDebut,
            responsableId: abonnementData.responsableId._id,
            entrepriseId: abonnementData.entrepriseId._id
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setAlertMessage(error.message || 'Erreur lors du chargement des données');
        setAlertSeverity('error');
        setAlertOpen(true);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, isEditMode]);

  const handleChange = async (e) => {
    const { name, value } = e.target;

    // Si le responsable change, essayer de trouver son entreprise associée
    if (name === 'responsableId') {
      try {
        console.log('Responsable sélectionné:', value);

        // Mettre à jour d'abord le responsable dans le formulaire
        setFormData(prev => ({
          ...prev,
          [name]: value,
        }));

        // Effacer l'erreur pour ce champ
        if (errors[name]) {
          setErrors(prev => ({
            ...prev,
            [name]: null
          }));
        }

        // Récupérer l'entreprise associée au responsable
        const entreprise = await responsableEntrepriseService.getEntrepriseByResponsable(value);

        if (entreprise) {
          console.log('Entreprise associée trouvée via API:', entreprise);
          // Mettre à jour le formulaire avec l'entreprise associée
          setFormData(prev => ({
            ...prev,
            entrepriseId: entreprise._id
          }));

          // Effacer l'erreur pour le champ entreprise
          if (errors.entrepriseId) {
            setErrors(prev => ({
              ...prev,
              entrepriseId: null
            }));
          }
          return;
        } else {
          console.log('Aucune entreprise associée trouvée via API pour le responsable:', value);

          // Fallback: chercher par le nom d'entreprise dans l'utilisateur
          const selectedResponsable = responsables.find(resp => resp._id === value);
          console.log('Responsable trouvé dans la liste:', selectedResponsable);

          if (selectedResponsable && selectedResponsable.nomEntreprise) {
            console.log('Nom d\'entreprise trouvé dans le responsable:', selectedResponsable.nomEntreprise);

            // Chercher l'entreprise correspondante dans la liste des entreprises
            const matchingEntreprise = entreprises.find(
              ent => ent.nom === selectedResponsable.nomEntreprise ||
                    ent.nomEntreprise === selectedResponsable.nomEntreprise
            );

            if (matchingEntreprise) {
              console.log('Entreprise correspondante trouvée par nom:', matchingEntreprise);
              // Mettre à jour le formulaire avec l'entreprise associée
              setFormData(prev => ({
                ...prev,
                entrepriseId: matchingEntreprise._id
              }));

              // Effacer l'erreur pour le champ entreprise
              if (errors.entrepriseId) {
                setErrors(prev => ({
                  ...prev,
                  entrepriseId: null
                }));
              }
              return;
            } else {
              console.log('Aucune entreprise correspondante trouvée par nom');

              // Chercher un client avec le même nom que l'entreprise
              const matchingClient = entreprises.find(
                ent => ent.nom && selectedResponsable.nomEntreprise &&
                      ent.nom.toLowerCase().includes(selectedResponsable.nomEntreprise.toLowerCase())
              );

              if (matchingClient) {
                console.log('Client correspondant trouvé par nom partiel:', matchingClient);
                setFormData(prev => ({
                  ...prev,
                  entrepriseId: matchingClient._id
                }));

                // Effacer l'erreur pour le champ entreprise
                if (errors.entrepriseId) {
                  setErrors(prev => ({
                    ...prev,
                    entrepriseId: null
                  }));
                }
                return;
              }
            }
          }
        }
      } catch (error) {
        console.error('Erreur lors de la récupération de l\'entreprise associée:', error);
      }
      return;
    }

    // Pour les autres champs
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Effacer l'erreur pour ce champ
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.responsableId) {
      newErrors.responsableId = 'Le responsable est requis';
    }

    if (!formData.entrepriseId) {
      // Si un responsable est sélectionné mais pas d'entreprise, essayer de trouver une entreprise automatiquement
      if (formData.responsableId) {
        const selectedResponsable = responsables.find(resp => resp._id === formData.responsableId);

        if (selectedResponsable && selectedResponsable.nomEntreprise) {
          // Chercher une entreprise correspondante
          const matchingEntreprise = entreprises.find(
            ent => ent.nom === selectedResponsable.nomEntreprise ||
                  ent.nomEntreprise === selectedResponsable.nomEntreprise ||
                  (ent.nom && selectedResponsable.nomEntreprise &&
                   ent.nom.toLowerCase().includes(selectedResponsable.nomEntreprise.toLowerCase()))
          );

          if (matchingEntreprise) {
            // Mettre à jour le formulaire avec l'entreprise trouvée
            console.log('Entreprise trouvée automatiquement lors de la validation:', matchingEntreprise);
            setFormData(prev => ({
              ...prev,
              entrepriseId: matchingEntreprise._id
            }));
          } else {
            newErrors.entrepriseId = 'L\'entreprise est requise';
          }
        } else {
          newErrors.entrepriseId = 'L\'entreprise est requise';
        }
      } else {
        newErrors.entrepriseId = 'L\'entreprise est requise';
      }
    }

    if (!formData.duree) {
      newErrors.duree = 'La durée est requise';
    }

    if (!formData.dateDebut) {
      newErrors.dateDebut = 'La date de début est requise';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Fonction pour créer une entreprise pour un responsable si nécessaire
  const createEntrepriseForResponsable = async (responsableId) => {
    try {
      // Vérifier si le responsable existe
      const selectedResponsable = responsables.find(resp => resp._id === responsableId);
      if (!selectedResponsable) {
        console.error('Responsable non trouvé:', responsableId);
        return null;
      }

      console.log('Création d\'une entreprise pour le responsable:', selectedResponsable);

      // Préparer les données de l'entreprise
      const entrepriseData = {
        nom: selectedResponsable.nomEntreprise || selectedResponsable.nom,
        responsableId: selectedResponsable._id,
        email: selectedResponsable.email,
        telephone: selectedResponsable.telephoneEntreprise || selectedResponsable.telephone,
        adresse: selectedResponsable.adresseEntreprise || selectedResponsable.adresse,
        numeroFiscal: selectedResponsable.numeroFiscal
      };

      // Créer l'entreprise
      const newEntreprise = await responsableEntrepriseService.createEntreprise(entrepriseData);
      console.log('Nouvelle entreprise créée:', newEntreprise);

      return newEntreprise;
    } catch (error) {
      console.error('Erreur lors de la création de l\'entreprise:', error);
      return null;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setAlertMessage('Veuillez corriger les erreurs dans le formulaire');
      setAlertSeverity('error');
      setAlertOpen(true);
      return;
    }

    setSaving(true);
    try {
      // Si un responsable est sélectionné mais pas d'entreprise, essayer de créer une entreprise
      if (formData.responsableId && !formData.entrepriseId) {
        console.log('Tentative de création d\'une entreprise pour le responsable:', formData.responsableId);
        const newEntreprise = await createEntrepriseForResponsable(formData.responsableId);

        if (newEntreprise) {
          // Mettre à jour le formulaire avec la nouvelle entreprise
          setFormData(prev => ({
            ...prev,
            entrepriseId: newEntreprise._id
          }));
        } else {
          setAlertMessage('Impossible de créer une entreprise pour ce responsable. Veuillez en sélectionner une manuellement.');
          setAlertSeverity('error');
          setAlertOpen(true);
          setSaving(false);
          return;
        }
      }

      // Créer ou mettre à jour l'abonnement
      if (isEditMode) {
        await abonnementService.updateAbonnement(id, formData);
        setAlertMessage('Abonnement mis à jour avec succès');
        setAlertSeverity('success');
        setAlertOpen(true);
      } else {
        await abonnementService.createAbonnement(formData);
        setAlertMessage('Abonnement créé avec succès');
        setAlertSeverity('success');
        setAlertOpen(true);
      }

      // Attendre un peu pour que l'utilisateur puisse voir le message de succès
      setTimeout(() => {
        navigate('/admin/abonnements');
      }, 1500);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'abonnement:', error);
      setAlertMessage(error.message || 'Erreur lors de l\'enregistrement de l\'abonnement');
      setAlertSeverity('error');
      setAlertOpen(true);
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/abonnements');
  };

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {isEditMode ? 'Modifier l\'abonnement' : 'Créer un nouvel abonnement'}
        </Typography>

        <Card sx={{ mt: 3 }}>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth error={Boolean(errors.responsableId)}>
                    <InputLabel>Responsable</InputLabel>
                    <Select
                      name="responsableId"
                      value={formData.responsableId}
                      onChange={handleChange}
                      label="Responsable"
                    >
                      {responsables.map((responsable) => (
                        <MenuItem key={responsable._id} value={responsable._id}>
                          {responsable.prenom} {responsable.nom} ({responsable.email})
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.responsableId && (
                      <FormHelperText>{errors.responsableId}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth error={Boolean(errors.entrepriseId)}>
                    <InputLabel>Entreprise</InputLabel>
                    <Select
                      name="entrepriseId"
                      value={formData.entrepriseId || ''}
                      onChange={handleChange}
                      label="Entreprise"
                    >
                      <MenuItem value="">
                        <em>Sélectionnez une entreprise</em>
                      </MenuItem>
                      {/* Afficher toutes les entreprises, mais mettre en évidence celle associée au responsable */}
                      {entreprises.map((entreprise) => {
                        // Vérifier si cette entreprise est associée au responsable sélectionné
                        const selectedResponsable = responsables.find(resp => resp._id === formData.responsableId);

                        // Plusieurs façons de déterminer si l'entreprise est associée au responsable
                        const isAssociatedByName = selectedResponsable &&
                          selectedResponsable.nomEntreprise &&
                          entreprise.nom &&
                          (entreprise.nom === selectedResponsable.nomEntreprise ||
                           entreprise.nom.toLowerCase().includes(selectedResponsable.nomEntreprise.toLowerCase()));

                        const isAssociatedById = entreprise.responsableId === formData.responsableId;

                        const isAssociated = isAssociatedByName || isAssociatedById;

                        // Déterminer le nom à afficher
                        const displayName = entreprise.nomEntreprise || entreprise.nom || "Entreprise sans nom";

                        return (
                          <MenuItem
                            key={entreprise._id}
                            value={entreprise._id}
                            sx={isAssociated ? { fontWeight: 'bold', color: 'primary.main' } : {}}
                          >
                            {displayName} {isAssociated ? '(associée au responsable)' : ''}
                          </MenuItem>
                        );
                      })}
                    </Select>
                    {errors.entrepriseId ? (
                      <FormHelperText error>{errors.entrepriseId}</FormHelperText>
                    ) : (
                      <FormHelperText>
                        Sélectionnez l'entreprise associée au responsable. Les entreprises associées sont en surbrillance.
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth error={Boolean(errors.duree)}>
                    <InputLabel>Durée</InputLabel>
                    <Select
                      name="duree"
                      value={formData.duree}
                      onChange={handleChange}
                      label="Durée"
                    >
                      <MenuItem value="10_MIN">10 minutes (test)</MenuItem>
                      <MenuItem value="1_JOUR">1 jour</MenuItem>
                      <MenuItem value="7_JOURS">7 jours</MenuItem>
                      <MenuItem value="3_MOIS">3 mois</MenuItem>
                      <MenuItem value="6_MOIS">6 mois</MenuItem>
                      <MenuItem value="1_AN">1 an</MenuItem>
                    </Select>
                    {errors.duree && (
                      <FormHelperText>{errors.duree}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Date de début"
                    type="date"
                    name="dateDebut"
                    value={formData.dateDebut}
                    onChange={handleChange}
                    error={Boolean(errors.dateDebut)}
                    helperText={errors.dateDebut}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                {isEditMode && (
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Statut</InputLabel>
                      <Select
                        name="statut"
                        value={formData.statut}
                        onChange={handleChange}
                        label="Statut"
                      >
                        <MenuItem value="ACTIF">Actif</MenuItem>
                        <MenuItem value="SUSPENDU">Suspendu</MenuItem>
                        <MenuItem value="EXPIRE">Expiré</MenuItem>
                        <MenuItem value="ANNULE">Annulé</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    multiline
                    rows={4}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={handleCancel}
                      disabled={saving}
                    >
                      Annuler
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={saving}
                      startIcon={saving ? <CircularProgress size={20} /> : null}
                    >
                      {saving ? 'Enregistrement...' : isEditMode ? 'Mettre à jour' : 'Créer'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Box>

      {/* Snackbar pour les alertes */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={() => setAlertOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setAlertOpen(false)}
          severity={alertSeverity}
          sx={{ width: '100%' }}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default AbonnementForm;
