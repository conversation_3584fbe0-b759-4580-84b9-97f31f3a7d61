{"version": 3, "sources": ["../../src/client/next-dev-turbopack.ts"], "sourcesContent": ["// TODO: Remove use of `any` type.\nimport { initialize, version, router, emitter } from './'\nimport initHMR from './dev/hot-middleware-client'\n\nimport { pageBootstrap } from './page-bootstrap'\n//@ts-expect-error requires \"moduleResolution\": \"node16\" in tsconfig.json and not .ts extension\nimport { connect } from '@vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts'\nimport type { TurbopackMsgToBrowser } from '../server/dev/hot-reloader-types'\n\nwindow.next = {\n  version: `${version}-turbo`,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n;(self as any).__next_set_public_path__ = () => {}\n;(self as any).__webpack_hash__ = ''\n\n// for the page loader\ndeclare let __turbopack_load__: any\n\nconst devClient = initHMR()\ninitialize({\n  devClient,\n})\n  .then(({ assetPrefix }) => {\n    // for the page loader\n    ;(self as any).__turbopack_load_page_chunks__ = (\n      page: string,\n      chunksData: any\n    ) => {\n      const chunkPromises = chunksData.map(__turbopack_load__)\n\n      Promise.all(chunkPromises).catch((err) =>\n        console.error('failed to load chunks for page ' + page, err)\n      )\n    }\n\n    connect({\n      addMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n        devClient.addTurbopackMessageListener(cb)\n      },\n      sendMessage: devClient.sendTurbopackMessage,\n      onUpdateError: devClient.handleUpdateError,\n    })\n\n    return pageBootstrap(assetPrefix)\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": ["initialize", "version", "router", "emitter", "initHMR", "pageBootstrap", "connect", "window", "next", "self", "__next_set_public_path__", "__webpack_hash__", "devClient", "then", "assetPrefix", "__turbopack_load_page_chunks__", "page", "chunksData", "chunkPromises", "map", "__turbopack_load__", "Promise", "all", "catch", "err", "console", "error", "addMessageListener", "cb", "addTurbopackMessageListener", "sendMessage", "sendTurbopackMessage", "onUpdateError", "handleUpdateError"], "mappings": "AAAA,kCAAkC;AAClC,SAASA,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,QAAQ,KAAI;AACzD,OAAOC,aAAa,8BAA6B;AAEjD,SAASC,aAAa,QAAQ,mBAAkB;AAChD,+FAA+F;AAC/F,SAASC,OAAO,QAAQ,4EAA2E;AAGnGC,OAAOC,IAAI,GAAG;IACZP,SAAS,AAAC,KAAEA,UAAQ;IACpB,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA;IACT;IACAC;AACF;AACEM,KAAaC,wBAAwB,GAAG,KAAO;AAC/CD,KAAaE,gBAAgB,GAAG;AAKlC,MAAMC,YAAYR;AAClBJ,WAAW;IACTY;AACF,GACGC,IAAI,CAAC;QAAC,EAAEC,WAAW,EAAE;IACpB,sBAAsB;;IACpBL,KAAaM,8BAA8B,GAAG,CAC9CC,MACAC;QAEA,MAAMC,gBAAgBD,WAAWE,GAAG,CAACC;QAErCC,QAAQC,GAAG,CAACJ,eAAeK,KAAK,CAAC,CAACC,MAChCC,QAAQC,KAAK,CAAC,oCAAoCV,MAAMQ;IAE5D;IAEAlB,QAAQ;QACNqB,oBAAmBC,EAAwC;YACzDhB,UAAUiB,2BAA2B,CAACD;QACxC;QACAE,aAAalB,UAAUmB,oBAAoB;QAC3CC,eAAepB,UAAUqB,iBAAiB;IAC5C;IAEA,OAAO5B,cAAcS;AACvB,GACCS,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC"}