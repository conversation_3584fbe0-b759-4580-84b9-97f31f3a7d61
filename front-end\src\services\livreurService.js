import api from './api';

const livreurService = {
  // Récupérer tous les livreurs
  getAllLivreurs: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();

      if (params.statut) queryParams.append('statut', params.statut);
      if (params.disponible !== undefined) queryParams.append('disponible', params.disponible);
      if (params.search) queryParams.append('search', params.search);

      const queryString = queryParams.toString();
      const url = queryString ? `/livreurs?${queryString}` : '/livreurs';

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des livreurs:', error);
      throw error;
    }
  },

  // Récupérer un livreur par ID
  getLivreurById: async (id) => {
    try {
      const response = await api.get(`/livreurs/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du livreur:', error);
      throw error;
    }
  },

  // Créer un nouveau livreur
  createLivreur: async (livreurData) => {
    try {
      const response = await api.post('/livreurs', livreurData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du livreur:', error);
      throw error;
    }
  },

  // Mettre à jour un livreur
  updateLivreur: async (id, livreurData) => {
    try {
      const response = await api.put(`/livreurs/${id}`, livreurData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du livreur:', error);
      throw error;
    }
  },

  // Supprimer un livreur
  deleteLivreur: async (id) => {
    try {
      const response = await api.delete(`/livreurs/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la suppression du livreur:', error);
      throw error;
    }
  },

  // Récupérer les livreurs disponibles
  getLivreursDisponibles: async () => {
    try {
      const response = await api.get('/livreurs/disponibles');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des livreurs disponibles:', error);
      throw error;
    }
  },

  // Mettre à jour le statut d'un livreur
  updateStatutLivreur: async (id, statut, disponible) => {
    try {
      const response = await api.put(`/livreurs/${id}`, {
        statut,
        disponible
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut du livreur:', error);
      throw error;
    }
  },

  // Rechercher des livreurs
  searchLivreurs: async (searchTerm) => {
    try {
      const response = await api.get(`/livreurs?search=${encodeURIComponent(searchTerm)}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la recherche de livreurs:', error);
      throw error;
    }
  },

  // Filtrer les livreurs par statut
  getLivreursByStatut: async (statut) => {
    try {
      const response = await api.get(`/livreurs?statut=${statut}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors du filtrage des livreurs par statut:', error);
      throw error;
    }
  },

  // Valider les données d'un livreur
  validateLivreurData: (livreurData) => {
    const errors = {};

    if (!livreurData.nom || livreurData.nom.trim() === '') {
      errors.nom = 'Le nom est obligatoire';
    }

    if (!livreurData.prenom || livreurData.prenom.trim() === '') {
      errors.prenom = 'Le prénom est obligatoire';
    }

    if (!livreurData.telephone || livreurData.telephone.trim() === '') {
      errors.telephone = 'Le numéro de téléphone est obligatoire';
    } else if (!/^[0-9\s\-\+\(\)]+$/.test(livreurData.telephone)) {
      errors.telephone = 'Le numéro de téléphone n\'est pas valide';
    }

    if (livreurData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(livreurData.email)) {
      errors.email = 'L\'adresse email n\'est pas valide';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  // Formater les données d'un livreur pour l'affichage
  formatLivreurForDisplay: (livreur) => {
    return {
      ...livreur,
      nomComplet: `${livreur.prenom} ${livreur.nom}`,
      vehiculeInfo: livreur.vehicule ?
        `${livreur.vehicule.type}${livreur.vehicule.marque ? ` - ${livreur.vehicule.marque}` : ''}${livreur.vehicule.modele ? ` ${livreur.vehicule.modele}` : ''}`
        : 'Non spécifié',
      statutLabel: livreurService.getStatutLabel(livreur.statut),
      disponibiliteLabel: livreur.disponible ? 'Disponible' : 'Non disponible',
      tauxReussiteFormate: livreur.statistiques?.tauxReussite ? `${livreur.statistiques.tauxReussite}%` : '0%'
    };
  },

  // Obtenir le libellé du statut
  getStatutLabel: (statut) => {
    const statutLabels = {
      'ACTIF': 'Actif',
      'INACTIF': 'Inactif',
      'SUSPENDU': 'Suspendu'
    };
    return statutLabels[statut] || statut;
  },

  // Obtenir la couleur du statut pour l'affichage
  getStatutColor: (statut) => {
    const statutColors = {
      'ACTIF': 'success',
      'INACTIF': 'default',
      'SUSPENDU': 'error'
    };
    return statutColors[statut] || 'default';
  },

  // Obtenir les options de véhicules
  getVehiculeTypes: () => {
    return [
      { value: 'Voiture', label: 'Voiture' },
      { value: 'Moto', label: 'Moto' },
      { value: 'Camionnette', label: 'Camionnette' },
      { value: 'Camion', label: 'Camion' },
      { value: 'Vélo', label: 'Vélo' },
      { value: 'Autre', label: 'Autre' }
    ];
  },

  // Obtenir les options de statut
  getStatutOptions: () => {
    return [
      { value: 'ACTIF', label: 'Actif' },
      { value: 'INACTIF', label: 'Inactif' },
      { value: 'SUSPENDU', label: 'Suspendu' }
    ];
  }
};

export default livreurService;
