{"version": 3, "sources": ["../../../../src/server/web/spec-extension/fetch-event.ts"], "sourcesContent": ["import type { WaitUntil } from '../../after/builtin-request-context'\nimport { PageSignatureError } from '../error'\nimport type { NextRequest } from './request'\n\nconst responseSymbol = Symbol('response')\nconst passThroughSymbol = Symbol('passThrough')\nconst waitUntilSymbol = Symbol('waitUntil')\n\nclass FetchEvent {\n  // TODO(after): get rid of the 'internal' variant and always use an external waitUntil\n  // (this means removing `FetchEventResult.waitUntil` which also requires a builder change)\n  readonly [waitUntilSymbol]:\n    | { kind: 'internal'; promises: Promise<any>[] }\n    | { kind: 'external'; function: WaitUntil };\n\n  [responseSymbol]?: Promise<Response>;\n  [passThroughSymbol] = false\n\n  constructor(_request: Request, waitUntil?: WaitUntil) {\n    this[waitUntilSymbol] = waitUntil\n      ? { kind: 'external', function: waitUntil }\n      : { kind: 'internal', promises: [] }\n  }\n\n  // TODO: is this dead code? NextFetchEvent never lets this get called\n  respondWith(response: Response | Promise<Response>): void {\n    if (!this[responseSymbol]) {\n      this[responseSymbol] = Promise.resolve(response)\n    }\n  }\n\n  // TODO: is this dead code? passThroughSymbol is unused\n  passThroughOnException(): void {\n    this[passThroughSymbol] = true\n  }\n\n  waitUntil(promise: Promise<any>): void {\n    if (this[waitUntilSymbol].kind === 'external') {\n      // if we received an external waitUntil, we delegate to it\n      // TODO(after): this will make us not go through `getServerError(error, 'edge-server')` in `sandbox`\n      const waitUntil = this[waitUntilSymbol].function\n      return waitUntil(promise)\n    } else {\n      // if we didn't receive an external waitUntil, we make it work on our own\n      // (and expect the caller to do something with the promises)\n      this[waitUntilSymbol].promises.push(promise)\n    }\n  }\n}\n\nexport function getWaitUntilPromiseFromEvent(\n  event: FetchEvent\n): Promise<void> | undefined {\n  return event[waitUntilSymbol].kind === 'internal'\n    ? Promise.all(event[waitUntilSymbol].promises).then(() => {})\n    : undefined\n}\n\nexport class NextFetchEvent extends FetchEvent {\n  sourcePage: string\n\n  constructor(params: {\n    request: NextRequest\n    page: string\n    context: { waitUntil: WaitUntil } | undefined\n  }) {\n    super(params.request, params.context?.waitUntil)\n    this.sourcePage = params.page\n  }\n\n  /**\n   * @deprecated The `request` is now the first parameter and the API is now async.\n   *\n   * Read more: https://nextjs.org/docs/messages/middleware-new-signature\n   */\n  get request() {\n    throw new PageSignatureError({\n      page: this.sourcePage,\n    })\n  }\n\n  /**\n   * @deprecated Using `respondWith` is no longer needed.\n   *\n   * Read more: https://nextjs.org/docs/messages/middleware-new-signature\n   */\n  respondWith() {\n    throw new PageSignatureError({\n      page: this.sourcePage,\n    })\n  }\n}\n"], "names": ["PageSignatureError", "responseSymbol", "Symbol", "passThroughSymbol", "waitUntilSymbol", "FetchEvent", "constructor", "_request", "waitUntil", "kind", "function", "promises", "respondWith", "response", "Promise", "resolve", "passThroughOnException", "promise", "push", "getWaitUntilPromiseFromEvent", "event", "all", "then", "undefined", "NextFetchEvent", "params", "request", "context", "sourcePage", "page"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,WAAU;AAG7C,MAAMC,iBAAiBC,OAAO;AAC9B,MAAMC,oBAAoBD,OAAO;AACjC,MAAME,kBAAkBF,OAAO;AAE/B,MAAMG;IAUJC,YAAYC,QAAiB,EAAEC,SAAqB,CAAE;YAFtD,CAACL,kBAAkB,GAAG;QAGpB,IAAI,CAACC,gBAAgB,GAAGI,YACpB;YAAEC,MAAM;YAAYC,UAAUF;QAAU,IACxC;YAAEC,MAAM;YAAYE,UAAU,EAAE;QAAC;IACvC;IAEA,qEAAqE;IACrEC,YAAYC,QAAsC,EAAQ;QACxD,IAAI,CAAC,IAAI,CAACZ,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAGa,QAAQC,OAAO,CAACF;QACzC;IACF;IAEA,uDAAuD;IACvDG,yBAA+B;QAC7B,IAAI,CAACb,kBAAkB,GAAG;IAC5B;IAEAK,UAAUS,OAAqB,EAAQ;QACrC,IAAI,IAAI,CAACb,gBAAgB,CAACK,IAAI,KAAK,YAAY;YAC7C,0DAA0D;YAC1D,oGAAoG;YACpG,MAAMD,YAAY,IAAI,CAACJ,gBAAgB,CAACM,QAAQ;YAChD,OAAOF,UAAUS;QACnB,OAAO;YACL,yEAAyE;YACzE,4DAA4D;YAC5D,IAAI,CAACb,gBAAgB,CAACO,QAAQ,CAACO,IAAI,CAACD;QACtC;IACF;AACF;AAEA,OAAO,SAASE,6BACdC,KAAiB;IAEjB,OAAOA,KAAK,CAAChB,gBAAgB,CAACK,IAAI,KAAK,aACnCK,QAAQO,GAAG,CAACD,KAAK,CAAChB,gBAAgB,CAACO,QAAQ,EAAEW,IAAI,CAAC,KAAO,KACzDC;AACN;AAEA,OAAO,MAAMC,uBAAuBnB;IAGlCC,YAAYmB,MAIX,CAAE;YACqBA;QAAtB,KAAK,CAACA,OAAOC,OAAO,GAAED,kBAAAA,OAAOE,OAAO,qBAAdF,gBAAgBjB,SAAS;QAC/C,IAAI,CAACoB,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA;;;;GAIC,GACD,IAAIH,UAAU;QACZ,MAAM,qBAEJ,CAFI,IAAI1B,mBAAmB;YAC3B6B,MAAM,IAAI,CAACD,UAAU;QACvB,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IAEA;;;;GAIC,GACDhB,cAAc;QACZ,MAAM,qBAEJ,CAFI,IAAIZ,mBAAmB;YAC3B6B,MAAM,IAAI,CAACD,UAAU;QACvB,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;AACF"}