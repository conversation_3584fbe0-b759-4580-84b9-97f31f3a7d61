{"version": 3, "sources": ["../../../../src/build/babel/plugins/jsx-pragma.ts"], "sourcesContent": ["import type {\n  NodePath,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\nimport jsx from 'next/dist/compiled/babel/plugin-syntax-jsx'\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj<any> {\n  return {\n    inherits: jsx,\n    visitor: {\n      JSXElement(_path, state) {\n        state.set('jsx', true)\n      },\n\n      // Fragment syntax is still JSX since it compiles to createElement(),\n      // but JSXFragment is not a JSXElement\n      JSXFragment(_path, state) {\n        state.set('jsx', true)\n      },\n\n      Program: {\n        exit(path: NodePath<BabelTypes.Program>, state) {\n          if (state.get('jsx')) {\n            const pragma = t.identifier(state.opts.pragma)\n            let importAs = pragma\n\n            // if there's already a React in scope, use that instead of adding an import\n            const existingBinding =\n              state.opts.reuseImport !== false &&\n              state.opts.importAs &&\n              path.scope.getBinding(state.opts.importAs)\n\n            // var _jsx = _pragma.createElement;\n            if (state.opts.property) {\n              if (state.opts.importAs) {\n                importAs = t.identifier(state.opts.importAs)\n              } else {\n                importAs = path.scope.generateUidIdentifier('pragma')\n              }\n\n              const mapping = t.variableDeclaration('var', [\n                t.variableDeclarator(\n                  pragma,\n                  t.memberExpression(\n                    importAs,\n                    t.identifier(state.opts.property)\n                  )\n                ),\n              ])\n\n              // if the React binding came from a require('react'),\n              // make sure that our usage comes after it.\n              let newPath: NodePath<BabelTypes.VariableDeclaration>\n\n              if (\n                existingBinding &&\n                t.isVariableDeclarator(existingBinding.path.node) &&\n                t.isCallExpression(existingBinding.path.node.init) &&\n                t.isIdentifier(existingBinding.path.node.init.callee) &&\n                existingBinding.path.node.init.callee.name === 'require'\n              ) {\n                ;[newPath] =\n                  existingBinding.path.parentPath.insertAfter(mapping)\n              } else {\n                ;[newPath] = path.unshiftContainer('body', mapping)\n              }\n\n              for (const declar of newPath.get('declarations')) {\n                path.scope.registerBinding(\n                  newPath.node.kind,\n                  declar as NodePath<BabelTypes.Node>\n                )\n              }\n            }\n\n            if (!existingBinding) {\n              const importSpecifier = t.importDeclaration(\n                [\n                  state.opts.import\n                    ? // import { $import as _pragma } from '$module'\n                      t.importSpecifier(\n                        importAs,\n                        t.identifier(state.opts.import)\n                      )\n                    : state.opts.importNamespace\n                      ? t.importNamespaceSpecifier(importAs)\n                      : // import _pragma from '$module'\n                        t.importDefaultSpecifier(importAs),\n                ],\n                t.stringLiteral(state.opts.module || 'react')\n              )\n\n              const [newPath] = path.unshiftContainer('body', importSpecifier)\n              for (const specifier of newPath.get('specifiers')) {\n                path.scope.registerBinding(\n                  'module',\n                  specifier as NodePath<BabelTypes.Node>\n                )\n              }\n            }\n          }\n        },\n      },\n    },\n  }\n}\n"], "names": ["jsx", "types", "t", "inherits", "visitor", "JSXElement", "_path", "state", "set", "JSXFragment", "Program", "exit", "path", "get", "pragma", "identifier", "opts", "importAs", "existingBinding", "reuseImport", "scope", "getBinding", "property", "generateUidIdentifier", "mapping", "variableDeclaration", "variableDeclarator", "memberExpression", "newPath", "isVariableDeclarator", "node", "isCallExpression", "init", "isIdentifier", "callee", "name", "parentPath", "insertAfter", "unshiftContainer", "declar", "registerBinding", "kind", "importSpecifier", "importDeclaration", "import", "importNamespace", "importNamespaceSpecifier", "importDefaultSpecifier", "stringLiteral", "module", "specifier"], "mappings": "AAKA,OAAOA,SAAS,6CAA4C;AAE5D,eAAe,SAAU,EACvBC,OAAOC,CAAC,EAGT;IACC,OAAO;QACLC,UAAUH;QACVI,SAAS;YACPC,YAAWC,KAAK,EAAEC,KAAK;gBACrBA,MAAMC,GAAG,CAAC,OAAO;YACnB;YAEA,qEAAqE;YACrE,sCAAsC;YACtCC,aAAYH,KAAK,EAAEC,KAAK;gBACtBA,MAAMC,GAAG,CAAC,OAAO;YACnB;YAEAE,SAAS;gBACPC,MAAKC,IAAkC,EAAEL,KAAK;oBAC5C,IAAIA,MAAMM,GAAG,CAAC,QAAQ;wBACpB,MAAMC,SAASZ,EAAEa,UAAU,CAACR,MAAMS,IAAI,CAACF,MAAM;wBAC7C,IAAIG,WAAWH;wBAEf,4EAA4E;wBAC5E,MAAMI,kBACJX,MAAMS,IAAI,CAACG,WAAW,KAAK,SAC3BZ,MAAMS,IAAI,CAACC,QAAQ,IACnBL,KAAKQ,KAAK,CAACC,UAAU,CAACd,MAAMS,IAAI,CAACC,QAAQ;wBAE3C,oCAAoC;wBACpC,IAAIV,MAAMS,IAAI,CAACM,QAAQ,EAAE;4BACvB,IAAIf,MAAMS,IAAI,CAACC,QAAQ,EAAE;gCACvBA,WAAWf,EAAEa,UAAU,CAACR,MAAMS,IAAI,CAACC,QAAQ;4BAC7C,OAAO;gCACLA,WAAWL,KAAKQ,KAAK,CAACG,qBAAqB,CAAC;4BAC9C;4BAEA,MAAMC,UAAUtB,EAAEuB,mBAAmB,CAAC,OAAO;gCAC3CvB,EAAEwB,kBAAkB,CAClBZ,QACAZ,EAAEyB,gBAAgB,CAChBV,UACAf,EAAEa,UAAU,CAACR,MAAMS,IAAI,CAACM,QAAQ;6BAGrC;4BAED,qDAAqD;4BACrD,2CAA2C;4BAC3C,IAAIM;4BAEJ,IACEV,mBACAhB,EAAE2B,oBAAoB,CAACX,gBAAgBN,IAAI,CAACkB,IAAI,KAChD5B,EAAE6B,gBAAgB,CAACb,gBAAgBN,IAAI,CAACkB,IAAI,CAACE,IAAI,KACjD9B,EAAE+B,YAAY,CAACf,gBAAgBN,IAAI,CAACkB,IAAI,CAACE,IAAI,CAACE,MAAM,KACpDhB,gBAAgBN,IAAI,CAACkB,IAAI,CAACE,IAAI,CAACE,MAAM,CAACC,IAAI,KAAK,WAC/C;;gCACC,CAACP,QAAQ,GACRV,gBAAgBN,IAAI,CAACwB,UAAU,CAACC,WAAW,CAACb;4BAChD,OAAO;;gCACJ,CAACI,QAAQ,GAAGhB,KAAK0B,gBAAgB,CAAC,QAAQd;4BAC7C;4BAEA,KAAK,MAAMe,UAAUX,QAAQf,GAAG,CAAC,gBAAiB;gCAChDD,KAAKQ,KAAK,CAACoB,eAAe,CACxBZ,QAAQE,IAAI,CAACW,IAAI,EACjBF;4BAEJ;wBACF;wBAEA,IAAI,CAACrB,iBAAiB;4BACpB,MAAMwB,kBAAkBxC,EAAEyC,iBAAiB,CACzC;gCACEpC,MAAMS,IAAI,CAAC4B,MAAM,GAEb1C,EAAEwC,eAAe,CACfzB,UACAf,EAAEa,UAAU,CAACR,MAAMS,IAAI,CAAC4B,MAAM,KAEhCrC,MAAMS,IAAI,CAAC6B,eAAe,GACxB3C,EAAE4C,wBAAwB,CAAC7B,YAE3Bf,EAAE6C,sBAAsB,CAAC9B;6BAChC,EACDf,EAAE8C,aAAa,CAACzC,MAAMS,IAAI,CAACiC,MAAM,IAAI;4BAGvC,MAAM,CAACrB,QAAQ,GAAGhB,KAAK0B,gBAAgB,CAAC,QAAQI;4BAChD,KAAK,MAAMQ,aAAatB,QAAQf,GAAG,CAAC,cAAe;gCACjDD,KAAKQ,KAAK,CAACoB,eAAe,CACxB,UACAU;4BAEJ;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF"}