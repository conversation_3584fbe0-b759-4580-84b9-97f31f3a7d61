const mongoose = require('mongoose');
const { Schema } = mongoose;

const profileSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    required: true,
    unique: true
  },
  // Informations personnelles
  profileImage: { type: String },
  bio: { type: String },

  // Préférences d'interface
  theme: { type: String, default: 'light' },
  notifications: { type: Boolean, default: true },

  // Timestamps
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt field on save
profileSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Profile', profileSchema);
