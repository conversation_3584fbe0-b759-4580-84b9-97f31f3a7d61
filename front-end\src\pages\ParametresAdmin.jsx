import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Snackbar,
  Alert,
  useTheme,
  Paper,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Email as EmailIcon,
  Euro as EuroIcon,
  Percent as PercentIcon,
  Language as LanguageIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { getParametresData, updateParametresData } from '../services/parametresService';
import { translate } from '../utils/translations';

const ParametresAdmin = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { language, changeLanguage } = useLanguage();

  // State
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Settings state
  const [settings, setSettings] = useState({
    // General settings
    defaultCurrency: 'TND',
    defaultLanguage: 'fr', // French is the only option now
    defaultTaxRate: 20,
    companyLogo: null,

    // Invoice settings
    invoicePrefix: 'FACT-',
    quotePrefix: 'DEV-'
  });

  // Fetch settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // Essayer d'abord de charger depuis le localStorage pour un affichage immédiat
        try {
          const localSettings = localStorage.getItem('appSettings');
          if (localSettings) {
            const parsedSettings = JSON.parse(localSettings);
            setSettings(prevSettings => ({
              ...prevSettings,
              ...parsedSettings
            }));
          }
        } catch (localError) {
          console.error('Error loading settings from localStorage:', localError);
        }

        // Ensuite, charger depuis le backend
        const response = await getParametresData();

        if (response.data) {
          // Map backend data to frontend state
          const updatedSettings = {
            // General settings
            defaultCurrency: 'TND', // Always use TND
            defaultLanguage: 'fr', // Always use French
            defaultTaxRate: response.data.defaultTaxRate || 20,
            companyLogo: response.data.logo || null,

            // Invoice settings
            invoicePrefix: response.data.invoicePrefix || 'FACT-',
            quotePrefix: response.data.quotePrefix || 'DEV-'
          };

          setSettings(updatedSettings);

          // Mettre à jour le localStorage avec les données du backend
          saveSettingsToLocalStorage(updatedSettings);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching settings:', error);
        setLoading(false);
        setSnackbar({
          open: true,
          message: translate('settings_fetch_error', language),
          severity: 'error'
        });
      }
    };

    fetchSettings();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle settings change with auto-save
  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedSettings = {
      ...settings,
      [name]: value
    };

    setSettings(updatedSettings);

    // Auto-save the changes
    autoSaveSettings(updatedSettings);
  };

  // Handle switch change with auto-save
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    const updatedSettings = {
      ...settings,
      [name]: checked
    };

    setSettings(updatedSettings);

    // Auto-save the changes
    autoSaveSettings(updatedSettings);
  };

  // Fonction pour sauvegarder les paramètres dans le localStorage
  const saveSettingsToLocalStorage = (settings) => {
    try {
      localStorage.setItem('appSettings', JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving settings to localStorage:', error);
    }
  };

  // Auto-save settings with debounce
  const [saveTimeout, setSaveTimeout] = useState(null);
  const [lastNotificationTime, setLastNotificationTime] = useState(0);
  const autoSaveSettings = (updatedSettings) => {
    // Clear previous timeout to implement debounce
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    // Sauvegarder immédiatement dans le localStorage pour une mise à jour instantanée
    saveSettingsToLocalStorage(updatedSettings);

    // Set new timeout to save after 1 second of inactivity
    const timeoutId = setTimeout(async () => {
      try {
        setSaving(true);
        await updateParametresData(updatedSettings);
        setSaving(false);

        // Only show notification if it's been more than 10 seconds since the last one
        // or if it's an error (always show errors)
        const now = Date.now();
        if (now - lastNotificationTime > 10000) {
          setLastNotificationTime(now);
          setSnackbar({
            open: true,
            message: translate('settings_saved_auto', language) || 'Paramètres sauvegardés automatiquement',
            severity: 'success'
          });
        }
      } catch (error) {
        console.error('Error auto-saving settings:', error);
        setSaving(false);
        setSnackbar({
          open: true,
          message: translate('settings_save_error', language),
          severity: 'error'
        });
      }
    }, 1000);

    setSaveTimeout(timeoutId);
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setSaving(true);

      // Sauvegarder dans le localStorage
      saveSettingsToLocalStorage(settings);

      // Call the API to update settings
      const response = await updateParametresData(settings);

      console.log('Settings saved successfully:', response);

      setSnackbar({
        open: true,
        message: translate('settings_saved', language),
        severity: 'success'
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      setSnackbar({
        open: true,
        message: translate('settings_save_error', language),
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle reset to defaults
  const handleResetDefaults = async () => {
    const defaultSettings = {
      // General settings
      defaultCurrency: 'TND',
      defaultLanguage: 'fr',
      defaultTaxRate: 20,
      companyLogo: null,

      // Invoice settings
      invoicePrefix: 'FACT-',
      quotePrefix: 'DEV-'
    };

    setSettings(defaultSettings);

    // Réinitialiser le localStorage
    saveSettingsToLocalStorage(defaultSettings);

    try {
      // Save the default settings to the database
      await updateParametresData(defaultSettings);

      setSnackbar({
        open: true,
        message: translate('settings_reset', language),
        severity: 'info'
      });
    } catch (error) {
      console.error('Error resetting settings:', error);
      setSnackbar({
        open: true,
        message: translate('settings_reset_error', language),
        severity: 'error'
      });
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{ p: 3 }}
    >
      {/* Header */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{
          mb: 4,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            {translate('system_settings', language)}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {translate('configure_global_settings', language)}
          </Typography>
        </Box>

        <Box>
          {saving ? (
            <Button
              variant="contained"
              startIcon={<CircularProgress size={16} color="inherit" />}
              disabled
              sx={{ borderRadius: 2, mr: 1 }}
            >
              {translate('saving', language)}
            </Button>
          ) : (
            <Tooltip title={translate('settings_saved_auto', language)}>
              <span>
                <Button
                  variant="outlined"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveSettings}
                  disabled={loading}
                  sx={{ borderRadius: 2, mr: 1 }}
                >
                  {translate('save', language)}
                </Button>
              </span>
            </Tooltip>
          )}
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleResetDefaults}
            disabled={saving || loading}
            sx={{ borderRadius: 2 }}
          >
            {translate('reset', language)}
          </Button>
        </Box>
      </Box>

      {/* Tabs */}
      <Box
        component={motion.div}
        variants={itemVariants}
        sx={{ mb: 3 }}
      >
        <Paper>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab label={translate('general', language)} icon={<SettingsIcon />} iconPosition="start" />
            <Tab label={translate('billing', language)} icon={<EuroIcon />} iconPosition="start" />
          </Tabs>
        </Paper>
      </Box>

      {/* Settings Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* General Settings */}
          {tabValue === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card
                  component={motion.div}
                  variants={itemVariants}
                  elevation={1}
                  sx={{
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                    }
                  }}
                >
                  <CardHeader
                    title={translate('general_settings', language)}
                    titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
                  />
                  <Divider />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>{translate('default_currency', language)}</InputLabel>
                          <Select
                            name="defaultCurrency"
                            value="TND"
                            disabled
                            label={translate('default_currency', language)}
                          >
                            <MenuItem value="TND">Dinar Tunisien (DT)</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          label="Taux de TVA par défaut (%)"
                          name="defaultTaxRate"
                          type="number"
                          value={settings.defaultTaxRate}
                          onChange={handleChange}
                          fullWidth
                          margin="normal"
                          InputProps={{
                            endAdornment: <PercentIcon color="action" />,
                            inputProps: { min: 0, max: 100 }
                          }}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>


            </Grid>
          )}

          {/* Invoice Settings */}
          {tabValue === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card
                  component={motion.div}
                  variants={itemVariants}
                  elevation={1}
                  sx={{
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                    }
                  }}
                >
                  <CardHeader
                    title="Paramètres de facturation"
                    titleTypographyProps={{ variant: 'h6', fontWeight: 'bold' }}
                  />
                  <Divider />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Préfixe des factures"
                          name="invoicePrefix"
                          value={settings.invoicePrefix}
                          onChange={handleChange}
                          fullWidth
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Préfixe des devis"
                          name="quotePrefix"
                          value={settings.quotePrefix}
                          onChange={handleChange}
                          fullWidth
                          margin="normal"
                        />
                      </Grid>

                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={snackbar.message === translate('settings_saved_auto', language) ? 2000 : 6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant={snackbar.message === translate('settings_saved_auto', language) ? "standard" : "filled"}
          sx={{
            width: '100%',
            opacity: snackbar.message === translate('settings_saved_auto', language) ? 0.9 : 1
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ParametresAdmin;
