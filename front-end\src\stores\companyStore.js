import { create } from 'zustand';
import { getCompanyData, updateCompanyData } from '../services/entrepriseService';

export const useCompanyStore = create((set, get) => ({
  profile: null,
  loading: false,
  error: null,

  fetchProfile: async () => {
    set({ loading: true, error: null });
    try {
      const data = await getCompanyData();
      set({ profile: data, loading: false });
    } catch (error) {
      console.error('Error fetching company profile:', error);
      set({ error: 'Erreur lors du chargement du profil', loading: false });
    }
  },

  saveProfile: async (profile, logoFile, signatureFile) => {
    set({ loading: true, error: null });
    try {
      const savedData = await updateCompanyData(profile, logoFile, signatureFile);
      set({ profile: savedData, loading: false });
      return true;
    } catch (error) {
      console.error('Error saving company profile:', error);
      set({ 
        error: error.message || 'Erreur lors de l\'enregistrement du profil', 
        loading: false 
      });
      return false;
    }
  }
}));