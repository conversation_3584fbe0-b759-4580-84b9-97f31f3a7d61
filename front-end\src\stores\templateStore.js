import { create } from 'zustand';
import {
  getAllTemplates,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  setDefaultTemplate as apiSetDefaultTemplate
} from '../services/templateService';

/**
 * Store Zustand pour gérer les templates de documents
 */
export const useTemplateStore = create((set, get) => ({
  // État initial
  templates: [],
  activeTab: 'all', // 'all', 'facture', 'devis'
  loading: false,
  error: null,

  // Récupérer tous les templates
  fetchTemplates: async () => {
    set({ loading: true, error: null });
    try {
      const activeTab = get().activeTab;

      // Si un onglet spécifique est sélectionné, filtrer par type
      const type = activeTab !== 'all' ? activeTab : null;
      const templates = await getAllTemplates(type);

      // Normaliser les templates (adaptation entre les différentes structures)
      const normalizedTemplates = templates.map(t => ({
        id: t._id || t.id,
        name: t.name,
        type: t.type,
        layout: t.layout || 'standard',
        isActive: t.isActive !== undefined ? t.isActive : true,
        createdAt: t.createdAt,
        updatedAt: t.updatedAt
      }));

      set({ templates: normalizedTemplates, loading: false });
    } catch (error) {
      console.error('Error fetching templates:', error);
      set({
        error: error.message || 'Erreur lors du chargement des templates',
        loading: false
      });
    }
  },

  // Définir l'onglet actif
  setActiveTab: (tab) => {
    set({ activeTab: tab });
    // Recharger les templates à chaque changement d'onglet
    get().fetchTemplates();
  },

  // Créer un nouveau template
  createTemplate: async (template) => {
    set({ loading: true, error: null });
    try {
      const newTemplate = await createTemplate(template);

      // Normaliser le template créé
      const normalizedTemplate = {
        id: newTemplate._id || newTemplate.id,
        name: newTemplate.name,
        type: newTemplate.type,
        isDefault: newTemplate.isDefault,
        isActive: newTemplate.isActive !== undefined ? newTemplate.isActive : true,
        color: newTemplate.color || '#3b82f6',
        secondaryColor: newTemplate.secondaryColor || '#f8fafc',
        font: newTemplate.font || 'Inter',
        layout: newTemplate.layout || 'standard',
        image: newTemplate.image || `/templates/template-${newTemplate.type.toLowerCase()}.png`,
        thumbnail: newTemplate.thumbnail || `/templates/thumbnails/template-${newTemplate.type.toLowerCase()}-${newTemplate.layout || 'standard'}.png`,
        createdAt: newTemplate.createdAt,
        updatedAt: newTemplate.updatedAt
      };

      set((state) => ({
        templates: [...state.templates, normalizedTemplate],
        loading: false
      }));

      return true;
    } catch (error) {
      console.error('Error creating template:', error);
      set({
        error: error.message || 'Erreur lors de la création du template',
        loading: false
      });
      return false;
    }
  },

  // Mettre à jour un template existant
  updateTemplate: async (id, templateData) => {
    set({ loading: true, error: null });
    try {
      const updatedTemplate = await updateTemplate(id, templateData);

      // Normaliser le template mis à jour
      const normalizedTemplate = {
        id: updatedTemplate._id || updatedTemplate.id,
        name: updatedTemplate.name,
        type: updatedTemplate.type,
        layout: updatedTemplate.layout || 'standard',
        isActive: updatedTemplate.isActive !== undefined ? updatedTemplate.isActive : true,
        createdAt: updatedTemplate.createdAt,
        updatedAt: updatedTemplate.updatedAt
      };

      set((state) => ({
        templates: state.templates.map(t =>
          (t.id === id || t._id === id) ? normalizedTemplate : t
        ),
        loading: false
      }));

      return true;
    } catch (error) {
      console.error('Error updating template:', error);
      set({
        error: error.message || 'Erreur lors de la mise à jour du template',
        loading: false
      });
      return false;
    }
  },

  // Supprimer un template
  deleteTemplate: async (id) => {
    set({ loading: true, error: null });
    try {
      await deleteTemplate(id);

      set((state) => ({
        templates: state.templates.filter(t => t.id !== id && t._id !== id),
        loading: false
      }));

      return true;
    } catch (error) {
      console.error('Error deleting template:', error);
      set({
        error: error.message || 'Erreur lors de la suppression du template',
        loading: false
      });
      return false;
    }
  },

  // Définir un template comme template par défaut
  setDefaultTemplate: async (id, type) => {
    set({ loading: true, error: null });
    try {
      await apiSetDefaultTemplate(id, type);

      // Mettre à jour l'état local pour refléter la modification
      set((state) => ({
        templates: state.templates.map(t =>
          t.type === type
            ? { ...t, isDefault: (t.id === id || t._id === id) }
            : t
        ),
        loading: false
      }));

      return true;
    } catch (error) {
      console.error('Error setting default template:', error);
      set({
        error: error.message || 'Erreur lors de la définition du template par défaut',
        loading: false
      });
      return false;
    }
  }
}));