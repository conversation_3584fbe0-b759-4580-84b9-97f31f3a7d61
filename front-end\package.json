{"name": "front-end", "version": "1.0.0", "proxy": "http://localhost:5000", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.2", "@mui/x-date-pickers": "^6.20.2", "axios": "^1.8.4", "chart.js": "^4.4.9", "date-fns": "^2.30.0", "framer-motion": "^12.7.4", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-react": "^0.487.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "notistack": "^3.0.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "recharts": "^2.15.1", "sharp": "^0.34.1", "socket.io-client": "^4.7.5", "zustand": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}