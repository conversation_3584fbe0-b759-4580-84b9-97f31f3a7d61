{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport type { Span } from '../../../trace'\nimport isError from '../../../lib/is-error'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport type { NodeFileTraceReasons } from 'next/dist/compiled/@vercel/nft'\nimport {\n  CLIENT_REFERENCE_MANIFEST,\n  TRACE_OUTPUT_VERSION,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from '../../webpack-config'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getPageFilePath } from '../../entries'\nimport { resolveExternal } from '../../handle-externals'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { getCompilationSpan } from '../utils'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nexport const TRACE_IGNORES = [\n  '**/*/next/dist/server/next.js',\n  '**/*/next/dist/bin/next',\n]\n\nconst NOT_TRACEABLE = [\n  '.wasm',\n  '.png',\n  '.jpg',\n  '.jpeg',\n  '.gif',\n  '.webp',\n  '.avif',\n  '.ico',\n  '.bmp',\n  '.svg',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string; request?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nexport function getFilesMapFromReasons(\n  fileList: Set<string>,\n  reasons: NodeFileTraceReasons,\n  ignoreFn?: (file: string, parent?: string) => Boolean\n) {\n  // this uses the reasons tree to collect files specific to a\n  // certain parent allowing us to not have to trace each parent\n  // separately\n  const parentFilesMap = new Map<string, Map<string, { ignored: boolean }>>()\n\n  function propagateToParents(\n    parents: Set<string>,\n    file: string,\n    seen = new Set<string>()\n  ) {\n    for (const parent of parents || []) {\n      if (!seen.has(parent)) {\n        seen.add(parent)\n        let parentFiles = parentFilesMap.get(parent)\n\n        if (!parentFiles) {\n          parentFiles = new Map()\n          parentFilesMap.set(parent, parentFiles)\n        }\n        const ignored = Boolean(ignoreFn?.(file, parent))\n        parentFiles.set(file, { ignored })\n\n        const parentReason = reasons.get(parent)\n\n        if (parentReason?.parents) {\n          propagateToParents(parentReason.parents, file, seen)\n        }\n      }\n    }\n  }\n\n  for (const file of fileList!) {\n    const reason = reasons!.get(file)\n    const isInitial =\n      reason?.type.length === 1 && reason.type.includes('initial')\n\n    if (\n      !reason ||\n      !reason.parents ||\n      (isInitial && reason.parents.size === 0)\n    ) {\n      continue\n    }\n    propagateToParents(reason.parents, file)\n  }\n  return parentFilesMap\n}\n\nexport interface TurbotraceAction {\n  action: 'print' | 'annotate'\n  input: string[]\n  contextDirectory: string\n  processCwd: string\n  showAll?: boolean\n  memoryLimit?: number\n}\n\nexport interface BuildTraceContext {\n  entriesTrace?: {\n    action: TurbotraceAction\n    appDir: string\n    outputPath: string\n    depModArray: string[]\n    entryNameMap: Record<string, string>\n    absolutePathByEntryName: Record<string, string>\n  }\n  chunksTrace?: {\n    action: TurbotraceAction\n    outputPath: string\n    entryNameFilesMap: Record<string, Array<string>>\n  }\n}\n\nexport class TraceEntryPointsPlugin implements webpack.WebpackPluginInstance {\n  public buildTraceContext: BuildTraceContext = {}\n\n  private rootDir: string\n  private appDir: string | undefined\n  private pagesDir: string | undefined\n  private appDirEnabled?: boolean\n  private tracingRoot: string\n  private entryTraces: Map<string, Map<string, { bundled: boolean }>>\n  private traceIgnores: string[]\n  private esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  private compilerType: CompilerNameValues\n\n  constructor({\n    rootDir,\n    appDir,\n    pagesDir,\n    compilerType,\n    appDirEnabled,\n    traceIgnores,\n    esmExternals,\n    outputFileTracingRoot,\n  }: {\n    rootDir: string\n    compilerType: CompilerNameValues\n    appDir: string | undefined\n    pagesDir: string | undefined\n    appDirEnabled?: boolean\n    traceIgnores?: string[]\n    outputFileTracingRoot?: string\n    esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  }) {\n    this.rootDir = rootDir\n    this.appDir = appDir\n    this.pagesDir = pagesDir\n    this.entryTraces = new Map()\n    this.esmExternals = esmExternals\n    this.appDirEnabled = appDirEnabled\n    this.traceIgnores = traceIgnores || []\n    this.tracingRoot = outputFileTracingRoot || rootDir\n    this.compilerType = compilerType\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  async createTraceAssets(compilation: webpack.Compilation, span: Span) {\n    const outputPath = compilation.outputOptions.path || ''\n\n    await span.traceChild('create-trace-assets').traceAsyncFn(async () => {\n      const entryFilesMap = new Map<any, Set<string>>()\n      const chunksToTrace = new Set<string>()\n      const entryNameFilesMap = new Map<string, Array<string>>()\n\n      const isTraceable = (file: string) =>\n        !NOT_TRACEABLE.some((suffix) => {\n          return file.endsWith(suffix)\n        })\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        const entryFiles = new Set<string>()\n\n        for (const chunk of entrypoint\n          .getEntrypointChunk()\n          .getAllReferencedChunks()) {\n          for (const file of chunk.files) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n          for (const file of chunk.auxiliaryFiles) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n        }\n        entryFilesMap.set(entrypoint, entryFiles)\n        entryNameFilesMap.set(entrypoint.name || '', [...entryFiles])\n      }\n\n      // startTrace existed and callable\n      this.buildTraceContext.chunksTrace = {\n        action: {\n          action: 'annotate',\n          input: [...chunksToTrace],\n          contextDirectory: this.tracingRoot,\n          processCwd: this.rootDir,\n        },\n        outputPath,\n        entryNameFilesMap: Object.fromEntries(entryNameFilesMap),\n      }\n\n      // server compiler outputs to `server/chunks` so we traverse up\n      // one, but edge-server does not so don't for that one\n      const outputPrefix = this.compilerType === 'server' ? '../' : ''\n\n      for (const [entrypoint, entryFiles] of entryFilesMap) {\n        const traceOutputName = `${outputPrefix}${entrypoint.name}.js.nft.json`\n        const traceOutputPath = nodePath.dirname(\n          nodePath.join(outputPath, traceOutputName)\n        )\n\n        // don't include the entry itself in the trace\n        entryFiles.delete(\n          nodePath.join(outputPath, `${outputPrefix}${entrypoint.name}.js`)\n        )\n\n        if (entrypoint.name.startsWith('app/') && this.appDir) {\n          const appDirRelativeEntryPath =\n            this.buildTraceContext.entriesTrace?.absolutePathByEntryName[\n              entrypoint.name\n            ]?.replace(this.appDir, '')\n\n          const entryIsStaticMetadataRoute =\n            appDirRelativeEntryPath &&\n            isMetadataRouteFile(appDirRelativeEntryPath, [], true)\n\n          // Include the client reference manifest in the trace, but not for\n          // static metadata routes, for which we don't generate those.\n          if (!entryIsStaticMetadataRoute) {\n            entryFiles.add(\n              nodePath.join(\n                outputPath,\n                outputPrefix,\n                entrypoint.name.replace(/%5F/g, '_') +\n                  '_' +\n                  CLIENT_REFERENCE_MANIFEST +\n                  '.js'\n              )\n            )\n          }\n        }\n\n        const finalFiles: string[] = []\n\n        await Promise.all(\n          [\n            ...new Set([\n              ...entryFiles,\n              ...(this.entryTraces.get(entrypoint.name)?.keys() || []),\n            ]),\n          ].map(async (file) => {\n            const fileInfo = this.entryTraces.get(entrypoint.name)?.get(file)\n\n            const relativeFile = nodePath\n              .relative(traceOutputPath, file)\n              .replace(/\\\\/g, '/')\n\n            if (file) {\n              if (!fileInfo?.bundled) {\n                finalFiles.push(relativeFile)\n              }\n            }\n          })\n        )\n\n        compilation.emitAsset(\n          traceOutputName,\n          new sources.RawSource(\n            JSON.stringify({\n              version: TRACE_OUTPUT_VERSION,\n              files: finalFiles,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    })\n  }\n\n  tapfinishModules(\n    compilation: webpack.Compilation,\n    traceEntrypointsPluginSpan: Span,\n    doResolve: (\n      request: string,\n      parent: string,\n      job: import('@vercel/nft/out/node-file-trace').Job,\n      isEsmRequested: boolean\n    ) => Promise<string>,\n    readlink: any,\n    stat: any\n  ) {\n    compilation.hooks.finishModules.tapAsync(\n      PLUGIN_NAME,\n      async (_stats: any, callback: any) => {\n        const finishModulesSpan =\n          traceEntrypointsPluginSpan.traceChild('finish-modules')\n        await finishModulesSpan\n          .traceAsyncFn(async () => {\n            // we create entry -> module maps so that we can\n            // look them up faster instead of having to iterate\n            // over the compilation modules list\n            const entryNameMap = new Map<string, string>()\n            const entryModMap = new Map<string, any>()\n            const additionalEntries = new Map<string, Map<string, any>>()\n            const absolutePathByEntryName = new Map<string, string>()\n\n            const depModMap = new Map<string, any>()\n\n            await finishModulesSpan\n              .traceChild('get-entries')\n              .traceAsyncFn(async () => {\n                for (const [name, entry] of compilation.entries.entries()) {\n                  const normalizedName = name?.replace(/\\\\/g, '/')\n\n                  const isPage = normalizedName.startsWith('pages/')\n                  const isApp =\n                    this.appDirEnabled && normalizedName.startsWith('app/')\n\n                  if (isApp || isPage) {\n                    for (const dep of entry.dependencies) {\n                      if (!dep) continue\n                      const entryMod = getModuleFromDependency(compilation, dep)\n\n                      // Handle case where entry is a loader coming from Next.js.\n                      // For example edge-loader or app-loader.\n                      if (entryMod && entryMod.resource === '') {\n                        const moduleBuildInfo = getModuleBuildInfo(entryMod)\n                        // All loaders that are used to create entries have a `route` property on the buildInfo.\n                        if (moduleBuildInfo.route) {\n                          const absolutePath = getPageFilePath({\n                            absolutePagePath:\n                              moduleBuildInfo.route.absolutePagePath,\n                            rootDir: this.rootDir,\n                            appDir: this.appDir,\n                            pagesDir: this.pagesDir,\n                          })\n\n                          // Ensures we don't handle non-pages.\n                          if (\n                            (this.pagesDir &&\n                              absolutePath.startsWith(this.pagesDir)) ||\n                            (this.appDir &&\n                              absolutePath.startsWith(this.appDir))\n                          ) {\n                            entryModMap.set(absolutePath, entryMod)\n                            entryNameMap.set(absolutePath, name)\n                            absolutePathByEntryName.set(name, absolutePath)\n                          }\n                        }\n\n                        // If there was no `route` property, we can assume that it was something custom instead.\n                        // In order to trace these we add them to the additionalEntries map.\n                        if (entryMod.request) {\n                          let curMap = additionalEntries.get(name)\n\n                          if (!curMap) {\n                            curMap = new Map()\n                            additionalEntries.set(name, curMap)\n                          }\n                          depModMap.set(entryMod.request, entryMod)\n                          curMap.set(entryMod.resource, entryMod)\n                        }\n                      }\n\n                      if (entryMod && entryMod.resource) {\n                        entryNameMap.set(entryMod.resource, name)\n                        entryModMap.set(entryMod.resource, entryMod)\n\n                        let curMap = additionalEntries.get(name)\n\n                        if (!curMap) {\n                          curMap = new Map()\n                          additionalEntries.set(name, curMap)\n                        }\n                        depModMap.set(entryMod.resource, entryMod)\n                        curMap.set(entryMod.resource, entryMod)\n                      }\n                    }\n                  }\n                }\n              })\n\n            const readFile = async (\n              path: string\n            ): Promise<Buffer | string | null> => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              let source: Buffer | string = mod?.originalSource?.()?.buffer()\n              return source || ''\n            }\n\n            const entryPaths = Array.from(entryModMap.keys())\n\n            const collectDependencies = async (mod: any, parent: string) => {\n              if (!mod || !mod.dependencies) return\n\n              for (const dep of mod.dependencies) {\n                const depMod = getModuleFromDependency(compilation, dep)\n\n                if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                  depModMap.set(depMod.resource, depMod)\n                  await collectDependencies(depMod, parent)\n                }\n              }\n            }\n            const entriesToTrace = [...entryPaths]\n\n            for (const entry of entryPaths) {\n              await collectDependencies(entryModMap.get(entry), entry)\n              const entryName = entryNameMap.get(entry)!\n              const curExtraEntries = additionalEntries.get(entryName)\n\n              if (curExtraEntries) {\n                entriesToTrace.push(...curExtraEntries.keys())\n              }\n            }\n\n            const contextDirectory = this.tracingRoot\n            const chunks = [...entriesToTrace]\n\n            this.buildTraceContext.entriesTrace = {\n              action: {\n                action: 'print',\n                input: chunks,\n                contextDirectory,\n                processCwd: this.rootDir,\n              },\n              appDir: this.rootDir,\n              depModArray: Array.from(depModMap.keys()),\n              entryNameMap: Object.fromEntries(entryNameMap),\n              absolutePathByEntryName: Object.fromEntries(\n                absolutePathByEntryName\n              ),\n              outputPath: compilation.outputOptions.path!,\n            }\n\n            let fileList: Set<string>\n            let reasons: NodeFileTraceReasons\n            const ignores = [\n              ...TRACE_IGNORES,\n              ...this.traceIgnores,\n              '**/node_modules/**',\n            ]\n\n            // pre-compile the ignore matcher to avoid repeating on every ignoreFn call\n            const isIgnoreMatcher = picomatch(ignores, {\n              contains: true,\n              dot: true,\n            })\n            const ignoreFn = (path: string) => {\n              return isIgnoreMatcher(path)\n            }\n\n            await finishModulesSpan\n              .traceChild('node-file-trace-plugin', {\n                traceEntryCount: entriesToTrace.length + '',\n              })\n              .traceAsyncFn(async () => {\n                const result = await nodeFileTrace(entriesToTrace, {\n                  base: this.tracingRoot,\n                  processCwd: this.rootDir,\n                  readFile,\n                  readlink,\n                  stat,\n                  resolve: doResolve\n                    ? async (id, parent, job, isCjs) => {\n                        return doResolve(id, parent, job, !isCjs)\n                      }\n                    : undefined,\n                  ignore: ignoreFn,\n                  mixedModules: true,\n                })\n                // @ts-ignore\n                fileList = result.fileList\n                result.esmFileList.forEach((file) => fileList.add(file))\n                reasons = result.reasons\n              })\n\n            await finishModulesSpan\n              .traceChild('collect-traced-files')\n              .traceAsyncFn(() => {\n                const parentFilesMap = getFilesMapFromReasons(\n                  fileList,\n                  reasons,\n                  (file) => {\n                    // if a file was imported and a loader handled it\n                    // we don't include it in the trace e.g.\n                    // static image imports, CSS imports\n                    file = nodePath.join(this.tracingRoot, file)\n                    const depMod = depModMap.get(file)\n                    const isAsset = reasons\n                      .get(nodePath.relative(this.tracingRoot, file))\n                      ?.type.includes('asset')\n\n                    return (\n                      !isAsset &&\n                      Array.isArray(depMod?.loaders) &&\n                      depMod.loaders.length > 0\n                    )\n                  }\n                )\n\n                for (const entry of entryPaths) {\n                  const entryName = entryNameMap.get(entry)!\n                  const normalizedEntry = nodePath.relative(\n                    this.tracingRoot,\n                    entry\n                  )\n                  const curExtraEntries = additionalEntries.get(entryName)\n                  const finalDeps = new Map<string, { bundled: boolean }>()\n\n                  // ensure we include entry source file as well for\n                  // hash comparison\n                  finalDeps.set(entry, {\n                    bundled: true,\n                  })\n\n                  for (const [dep, info] of parentFilesMap\n                    .get(normalizedEntry)\n                    ?.entries() || []) {\n                    finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                      bundled: info.ignored,\n                    })\n                  }\n\n                  if (curExtraEntries) {\n                    for (const extraEntry of curExtraEntries.keys()) {\n                      const normalizedExtraEntry = nodePath.relative(\n                        this.tracingRoot,\n                        extraEntry\n                      )\n                      finalDeps.set(extraEntry, { bundled: false })\n\n                      for (const [dep, info] of parentFilesMap\n                        .get(normalizedExtraEntry)\n                        ?.entries() || []) {\n                        finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                          bundled: info.ignored,\n                        })\n                      }\n                    }\n                  }\n                  this.entryTraces.set(entryName, finalDeps)\n                }\n              })\n          })\n          .then(\n            () => callback(),\n            (err) => callback(err)\n          )\n      }\n    )\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const compilationSpan =\n        getCompilationSpan(compilation) || getCompilationSpan(compiler)!\n      const traceEntrypointsPluginSpan = compilationSpan.traceChild(\n        'next-trace-entrypoint-plugin'\n      )\n\n      const readlink = async (path: string): Promise<string | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(\n              compilation.inputFileSystem\n                .readlink as typeof import('fs').readlink\n            )(path, (err, link) => {\n              if (err) return reject(err)\n              resolve(link)\n            })\n          })\n        } catch (e) {\n          if (\n            isError(e) &&\n            (e.code === 'EINVAL' || e.code === 'ENOENT' || e.code === 'UNKNOWN')\n          ) {\n            return null\n          }\n          throw e\n        }\n      }\n      const stat = async (path: string): Promise<import('fs').Stats | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(compilation.inputFileSystem.stat as typeof import('fs').stat)(\n              path,\n              (err, stats) => {\n                if (err) return reject(err)\n                resolve(stats)\n              }\n            )\n          })\n        } catch (e) {\n          if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n            return null\n          }\n          throw e\n        }\n      }\n\n      traceEntrypointsPluginSpan.traceFn(() => {\n        compilation.hooks.processAssets.tapAsync(\n          {\n            name: PLUGIN_NAME,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (_, callback: any) => {\n            this.createTraceAssets(compilation, traceEntrypointsPluginSpan)\n              .then(() => callback())\n              .catch((err) => callback(err))\n          }\n        )\n\n        let resolver = compilation.resolverFactory.get('normal')\n\n        function getPkgName(name: string) {\n          const segments = name.split('/')\n          if (name[0] === '@' && segments.length > 1)\n            return segments.length > 1 ? segments.slice(0, 2).join('/') : null\n          return segments.length ? segments[0] : null\n        }\n\n        const getResolve = (\n          options: Parameters<typeof resolver.withOptions>[0]\n        ) => {\n          const curResolver = resolver.withOptions(options)\n\n          return (\n            parent: string,\n            request: string,\n            job: import('@vercel/nft/out/node-file-trace').Job\n          ) =>\n            new Promise<[string, boolean]>((resolve, reject) => {\n              const context = nodePath.dirname(parent)\n\n              curResolver.resolve(\n                {},\n                context,\n                request,\n                {\n                  fileDependencies: compilation.fileDependencies,\n                  missingDependencies: compilation.missingDependencies,\n                  contextDependencies: compilation.contextDependencies,\n                },\n                async (err: any, result?, resContext?) => {\n                  if (err) return reject(err)\n\n                  if (!result) {\n                    return reject(new Error('module not found'))\n                  }\n\n                  // webpack resolver doesn't strip loader query info\n                  // from the result so use path instead\n                  if (result.includes('?') || result.includes('!')) {\n                    result = resContext?.path || result\n                  }\n\n                  try {\n                    // we need to collect all parent package.json's used\n                    // as webpack's resolve doesn't expose this and parent\n                    // package.json could be needed for resolving e.g. stylis\n                    // stylis/package.json -> stylis/dist/umd/package.json\n                    if (result.includes('node_modules')) {\n                      let requestPath = result\n                        .replace(/\\\\/g, '/')\n                        .replace(/\\0/g, '')\n\n                      if (\n                        !nodePath.isAbsolute(request) &&\n                        request.includes('/') &&\n                        resContext?.descriptionFileRoot\n                      ) {\n                        requestPath = (\n                          resContext.descriptionFileRoot +\n                          request.slice(getPkgName(request)?.length || 0) +\n                          nodePath.sep +\n                          'package.json'\n                        )\n                          .replace(/\\\\/g, '/')\n                          .replace(/\\0/g, '')\n                      }\n\n                      const rootSeparatorIndex = requestPath.indexOf('/')\n                      let separatorIndex: number\n                      while (\n                        (separatorIndex = requestPath.lastIndexOf('/')) >\n                        rootSeparatorIndex\n                      ) {\n                        requestPath = requestPath.slice(0, separatorIndex)\n                        const curPackageJsonPath = `${requestPath}/package.json`\n                        if (await job.isFile(curPackageJsonPath)) {\n                          await job.emitFile(\n                            await job.realpath(curPackageJsonPath),\n                            'resolve',\n                            parent\n                          )\n                        }\n                      }\n                    }\n                  } catch (_err) {\n                    // we failed to resolve the package.json boundary,\n                    // we don't block emitting the initial asset from this\n                  }\n                  resolve([result, options.dependencyType === 'esm'])\n                }\n              )\n            })\n        }\n\n        const CJS_RESOLVE_OPTIONS = {\n          ...NODE_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_CJS_RESOLVE_OPTIONS = {\n          ...CJS_RESOLVE_OPTIONS,\n          alias: false,\n        }\n        const ESM_RESOLVE_OPTIONS = {\n          ...NODE_ESM_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_ESM_RESOLVE_OPTIONS = {\n          ...ESM_RESOLVE_OPTIONS,\n          alias: false,\n        }\n\n        const doResolve = async (\n          request: string,\n          parent: string,\n          job: import('@vercel/nft/out/node-file-trace').Job,\n          isEsmRequested: boolean\n        ): Promise<string> => {\n          const context = nodePath.dirname(parent)\n          // When in esm externals mode, and using import, we resolve with\n          // ESM resolving options.\n          const { res } = await resolveExternal(\n            this.rootDir,\n            this.esmExternals,\n            context,\n            request,\n            isEsmRequested,\n            (options) => (_: string, resRequest: string) => {\n              return getResolve(options)(parent, resRequest, job)\n            },\n            undefined,\n            undefined,\n            ESM_RESOLVE_OPTIONS,\n            CJS_RESOLVE_OPTIONS,\n            BASE_ESM_RESOLVE_OPTIONS,\n            BASE_CJS_RESOLVE_OPTIONS\n          )\n\n          if (!res) {\n            throw new Error(`failed to resolve ${request} from ${parent}`)\n          }\n          return res.replace(/\\0/g, '')\n        }\n\n        this.tapfinishModules(\n          compilation,\n          traceEntrypointsPluginSpan,\n          doResolve,\n          readlink,\n          stat\n        )\n      })\n    })\n  }\n}\n"], "names": ["TRACE_IGNORES", "TraceEntryPointsPlugin", "getFilesMapFromReasons", "PLUGIN_NAME", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "ignored", "Boolean", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "constructor", "rootDir", "appDir", "pagesDir", "compilerType", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "buildTraceContext", "entryTraces", "tracingRoot", "createTraceAssets", "span", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "chunksToTrace", "entryNameFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "nodePath", "join", "auxiliaryFiles", "name", "chunksTrace", "action", "input", "contextDirectory", "processCwd", "Object", "fromEntries", "outputPrefix", "traceOutputName", "traceOutputPath", "dirname", "delete", "startsWith", "appDirRelativeEntryPath", "entriesTrace", "absolutePathByEntryName", "replace", "entryIsStaticMetadataRoute", "isMetadataRouteFile", "CLIENT_REFERENCE_MANIFEST", "finalFiles", "Promise", "all", "keys", "map", "fileInfo", "relativeFile", "relative", "bundled", "push", "emitAsset", "sources", "RawSource", "JSON", "stringify", "version", "TRACE_OUTPUT_VERSION", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "readlink", "stat", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "entry", "entries", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "getModuleBuildInfo", "route", "absolutePath", "getPageFilePath", "absolutePagePath", "request", "curMap", "readFile", "mod", "source", "originalSource", "buffer", "entryPaths", "Array", "from", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "chunks", "depModArray", "ignores", "isIgnoreMatcher", "picomatch", "contains", "dot", "traceEntryCount", "result", "nodeFileTrace", "base", "resolve", "id", "job", "isCjs", "undefined", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "info", "extraEntry", "normalizedExtraEntry", "then", "err", "apply", "compiler", "tap", "compilationSpan", "getCompilationSpan", "reject", "inputFileSystem", "link", "e", "isError", "code", "stats", "traceFn", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "_", "catch", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "options", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "resolveExternal", "resRequest"], "mappings": ";;;;;;;;;;;;;;;;IAwBaA,aAAa;eAAbA;;IAuGAC,sBAAsB;eAAtBA;;IA9EGC,sBAAsB;eAAtBA;;;6DAjDK;gEAED;qBACU;2BAMvB;yBAC0B;+BAI1B;kEAEe;oCACa;yBACH;iCACA;iCACI;uBACD;;;;;;AAEnC,MAAMC,cAAc;AACb,MAAMH,gBAAgB;IAC3B;IACA;CACD;AAED,MAAMI,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEO,SAASL,uBACdQ,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIT;oBAClBD,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBACA,MAAMG,UAAUC,QAAQf,4BAAAA,SAAWK,MAAMG;gBACzCG,YAAYE,GAAG,CAACR,MAAM;oBAAES;gBAAQ;gBAEhC,MAAME,eAAejB,QAAQa,GAAG,CAACJ;gBAEjC,IAAIQ,gCAAAA,aAAcZ,OAAO,EAAE;oBACzBD,mBAAmBa,aAAaZ,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMmB,SAASlB,QAASa,GAAG,CAACP;QAC5B,MAAMa,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOb,OAAO,IACdc,aAAaD,OAAOb,OAAO,CAACkB,IAAI,KAAK,GACtC;YACA;QACF;QACAnB,mBAAmBc,OAAOb,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA2BO,MAAMZ;IAaXkC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EAUtB,CAAE;aA9BIC,oBAAuC,CAAC;QA+B7C,IAAI,CAACR,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACO,WAAW,GAAG,IAAI/B;QACvB,IAAI,CAAC4B,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACK,WAAW,GAAGH,yBAAyBP;QAC5C,IAAI,CAACG,YAAY,GAAGA;IACtB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMQ,kBAAkBzC,WAAgC,EAAE0C,IAAU,EAAE;QACpE,MAAMC,aAAa3C,YAAY4C,aAAa,CAACC,IAAI,IAAI;QAErD,MAAMH,KAAKI,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAIxC;YAC1B,MAAMyC,gBAAgB,IAAIpC;YAC1B,MAAMqC,oBAAoB,IAAI1C;YAE9B,MAAM2C,cAAc,CAACxC,OACnB,CAACb,cAAcsD,IAAI,CAAC,CAACC;oBACnB,OAAO1C,KAAK2C,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAcvD,YAAYwD,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAI7C;gBAEvB,KAAK,MAAM8C,SAASJ,WACjBK,kBAAkB,GAClBC,sBAAsB,GAAI;oBAC3B,KAAK,MAAMlD,QAAQgD,MAAMG,KAAK,CAAE;wBAC9B,IAAIX,YAAYxC,OAAO;4BACrB,MAAMoD,WAAWC,aAAQ,CAACC,IAAI,CAACtB,YAAYhC;4BAC3CsC,cAAcjC,GAAG,CAAC+C;4BAClBL,WAAW1C,GAAG,CAAC+C;wBACjB;oBACF;oBACA,KAAK,MAAMpD,QAAQgD,MAAMO,cAAc,CAAE;wBACvC,IAAIf,YAAYxC,OAAO;4BACrB,MAAMoD,WAAWC,aAAQ,CAACC,IAAI,CAACtB,YAAYhC;4BAC3CsC,cAAcjC,GAAG,CAAC+C;4BAClBL,WAAW1C,GAAG,CAAC+C;wBACjB;oBACF;gBACF;gBACAf,cAAc7B,GAAG,CAACoC,YAAYG;gBAC9BR,kBAAkB/B,GAAG,CAACoC,WAAWY,IAAI,IAAI,IAAI;uBAAIT;iBAAW;YAC9D;YAEA,kCAAkC;YAClC,IAAI,CAACpB,iBAAiB,CAAC8B,WAAW,GAAG;gBACnCC,QAAQ;oBACNA,QAAQ;oBACRC,OAAO;2BAAIrB;qBAAc;oBACzBsB,kBAAkB,IAAI,CAAC/B,WAAW;oBAClCgC,YAAY,IAAI,CAAC1C,OAAO;gBAC1B;gBACAa;gBACAO,mBAAmBuB,OAAOC,WAAW,CAACxB;YACxC;YAEA,+DAA+D;YAC/D,sDAAsD;YACtD,MAAMyB,eAAe,IAAI,CAAC1C,YAAY,KAAK,WAAW,QAAQ;YAE9D,KAAK,MAAM,CAACsB,YAAYG,WAAW,IAAIV,cAAe;oBA2C1C;gBA1CV,MAAM4B,kBAAkB,GAAGD,eAAepB,WAAWY,IAAI,CAAC,YAAY,CAAC;gBACvE,MAAMU,kBAAkBb,aAAQ,CAACc,OAAO,CACtCd,aAAQ,CAACC,IAAI,CAACtB,YAAYiC;gBAG5B,8CAA8C;gBAC9ClB,WAAWqB,MAAM,CACff,aAAQ,CAACC,IAAI,CAACtB,YAAY,GAAGgC,eAAepB,WAAWY,IAAI,CAAC,GAAG,CAAC;gBAGlE,IAAIZ,WAAWY,IAAI,CAACa,UAAU,CAAC,WAAW,IAAI,CAACjD,MAAM,EAAE;wBAEnD,8EAAA;oBADF,MAAMkD,2BACJ,uCAAA,IAAI,CAAC3C,iBAAiB,CAAC4C,YAAY,sBAAnC,+EAAA,qCAAqCC,uBAAuB,CAC1D5B,WAAWY,IAAI,CAChB,qBAFD,6EAEGiB,OAAO,CAAC,IAAI,CAACrD,MAAM,EAAE;oBAE1B,MAAMsD,6BACJJ,2BACAK,IAAAA,oCAAmB,EAACL,yBAAyB,EAAE,EAAE;oBAEnD,kEAAkE;oBAClE,6DAA6D;oBAC7D,IAAI,CAACI,4BAA4B;wBAC/B3B,WAAW1C,GAAG,CACZgD,aAAQ,CAACC,IAAI,CACXtB,YACAgC,cACApB,WAAWY,IAAI,CAACiB,OAAO,CAAC,QAAQ,OAC9B,MACAG,oCAAyB,GACzB;oBAGR;gBACF;gBAEA,MAAMC,aAAuB,EAAE;gBAE/B,MAAMC,QAAQC,GAAG,CACf;uBACK,IAAI7E,IAAI;2BACN6C;2BACC,EAAA,wBAAA,IAAI,CAACnB,WAAW,CAACrB,GAAG,CAACqC,WAAWY,IAAI,sBAApC,sBAAuCwB,IAAI,OAAM,EAAE;qBACxD;iBACF,CAACC,GAAG,CAAC,OAAOjF;wBACM;oBAAjB,MAAMkF,YAAW,wBAAA,IAAI,CAACtD,WAAW,CAACrB,GAAG,CAACqC,WAAWY,IAAI,sBAApC,sBAAuCjD,GAAG,CAACP;oBAE5D,MAAMmF,eAAe9B,aAAQ,CAC1B+B,QAAQ,CAAClB,iBAAiBlE,MAC1ByE,OAAO,CAAC,OAAO;oBAElB,IAAIzE,MAAM;wBACR,IAAI,EAACkF,4BAAAA,SAAUG,OAAO,GAAE;4BACtBR,WAAWS,IAAI,CAACH;wBAClB;oBACF;gBACF;gBAGF9F,YAAYkG,SAAS,CACnBtB,iBACA,IAAIuB,gBAAO,CAACC,SAAS,CACnBC,KAAKC,SAAS,CAAC;oBACbC,SAASC,+BAAoB;oBAC7B1C,OAAO0B;gBACT;YAGN;QACF;IACF;IAEAiB,iBACEzG,WAAgC,EAChC0G,0BAAgC,EAChCC,SAKoB,EACpBC,QAAa,EACbC,IAAS,EACT;QACA7G,YAAY8G,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtCnH,aACA,OAAOoH,QAAaC;YAClB,MAAMC,oBACJT,2BAA2B5D,UAAU,CAAC;YACxC,MAAMqE,kBACHpE,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMqE,eAAe,IAAI5G;gBACzB,MAAM6G,cAAc,IAAI7G;gBACxB,MAAM8G,oBAAoB,IAAI9G;gBAC9B,MAAM2E,0BAA0B,IAAI3E;gBAEpC,MAAM+G,YAAY,IAAI/G;gBAEtB,MAAM2G,kBACHrE,UAAU,CAAC,eACXC,YAAY,CAAC;oBACZ,KAAK,MAAM,CAACoB,MAAMqD,MAAM,IAAIxH,YAAYyH,OAAO,CAACA,OAAO,GAAI;wBACzD,MAAMC,iBAAiBvD,wBAAAA,KAAMiB,OAAO,CAAC,OAAO;wBAE5C,MAAMuC,SAASD,eAAe1C,UAAU,CAAC;wBACzC,MAAM4C,QACJ,IAAI,CAAC1F,aAAa,IAAIwF,eAAe1C,UAAU,CAAC;wBAElD,IAAI4C,SAASD,QAAQ;4BACnB,KAAK,MAAM1H,OAAOuH,MAAMK,YAAY,CAAE;gCACpC,IAAI,CAAC5H,KAAK;gCACV,MAAM6H,WAAW/H,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAI6H,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBC,IAAAA,sCAAkB,EAACH;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBE,KAAK,EAAE;wCACzB,MAAMC,eAAeC,IAAAA,wBAAe,EAAC;4CACnCC,kBACEL,gBAAgBE,KAAK,CAACG,gBAAgB;4CACxCvG,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZmG,aAAanD,UAAU,CAAC,IAAI,CAAChD,QAAQ,KACtC,IAAI,CAACD,MAAM,IACVoG,aAAanD,UAAU,CAAC,IAAI,CAACjD,MAAM,GACrC;4CACAsF,YAAYlG,GAAG,CAACgH,cAAcL;4CAC9BV,aAAajG,GAAG,CAACgH,cAAchE;4CAC/BgB,wBAAwBhE,GAAG,CAACgD,MAAMgE;wCACpC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIL,SAASQ,OAAO,EAAE;wCACpB,IAAIC,SAASjB,kBAAkBpG,GAAG,CAACiD;wCAEnC,IAAI,CAACoE,QAAQ;4CACXA,SAAS,IAAI/H;4CACb8G,kBAAkBnG,GAAG,CAACgD,MAAMoE;wCAC9B;wCACAhB,UAAUpG,GAAG,CAAC2G,SAASQ,OAAO,EAAER;wCAChCS,OAAOpH,GAAG,CAAC2G,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCX,aAAajG,GAAG,CAAC2G,SAASC,QAAQ,EAAE5D;oCACpCkD,YAAYlG,GAAG,CAAC2G,SAASC,QAAQ,EAAED;oCAEnC,IAAIS,SAASjB,kBAAkBpG,GAAG,CAACiD;oCAEnC,IAAI,CAACoE,QAAQ;wCACXA,SAAS,IAAI/H;wCACb8G,kBAAkBnG,GAAG,CAACgD,MAAMoE;oCAC9B;oCACAhB,UAAUpG,GAAG,CAAC2G,SAASC,QAAQ,EAAED;oCACjCS,OAAOpH,GAAG,CAAC2G,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEF,MAAMU,WAAW,OACf3F;wBAM8B4F,qBAAAA;oBAJ9B,MAAMA,MAAMlB,UAAUrG,GAAG,CAAC2B,SAASwE,YAAYnG,GAAG,CAAC2B;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,IAAI6F,SAA0BD,wBAAAA,uBAAAA,IAAKE,cAAc,sBAAnBF,sBAAAA,0BAAAA,yBAAAA,oBAAyBG,MAAM;oBAC7D,OAAOF,UAAU;gBACnB;gBAEA,MAAMG,aAAaC,MAAMC,IAAI,CAAC1B,YAAY1B,IAAI;gBAE9C,MAAMqD,sBAAsB,OAAOP,KAAU3H;oBAC3C,IAAI,CAAC2H,OAAO,CAACA,IAAIZ,YAAY,EAAE;oBAE/B,KAAK,MAAM5H,OAAOwI,IAAIZ,YAAY,CAAE;wBAClC,MAAMoB,SAASlJ,wBAAwBC,aAAaC;wBAEpD,IAAIgJ,CAAAA,0BAAAA,OAAQlB,QAAQ,KAAI,CAACR,UAAUrG,GAAG,CAAC+H,OAAOlB,QAAQ,GAAG;4BACvDR,UAAUpG,GAAG,CAAC8H,OAAOlB,QAAQ,EAAEkB;4BAC/B,MAAMD,oBAAoBC,QAAQnI;wBACpC;oBACF;gBACF;gBACA,MAAMoI,iBAAiB;uBAAIL;iBAAW;gBAEtC,KAAK,MAAMrB,SAASqB,WAAY;oBAC9B,MAAMG,oBAAoB3B,YAAYnG,GAAG,CAACsG,QAAQA;oBAClD,MAAM2B,YAAY/B,aAAalG,GAAG,CAACsG;oBACnC,MAAM4B,kBAAkB9B,kBAAkBpG,GAAG,CAACiI;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAejD,IAAI,IAAImD,gBAAgBzD,IAAI;oBAC7C;gBACF;gBAEA,MAAMpB,mBAAmB,IAAI,CAAC/B,WAAW;gBACzC,MAAM6G,SAAS;uBAAIH;iBAAe;gBAElC,IAAI,CAAC5G,iBAAiB,CAAC4C,YAAY,GAAG;oBACpCb,QAAQ;wBACNA,QAAQ;wBACRC,OAAO+E;wBACP9E;wBACAC,YAAY,IAAI,CAAC1C,OAAO;oBAC1B;oBACAC,QAAQ,IAAI,CAACD,OAAO;oBACpBwH,aAAaR,MAAMC,IAAI,CAACxB,UAAU5B,IAAI;oBACtCyB,cAAc3C,OAAOC,WAAW,CAAC0C;oBACjCjC,yBAAyBV,OAAOC,WAAW,CACzCS;oBAEFxC,YAAY3C,YAAY4C,aAAa,CAACC,IAAI;gBAC5C;gBAEA,IAAIzC;gBACJ,IAAIC;gBACJ,MAAMkJ,UAAU;uBACX7J;uBACA,IAAI,CAACyC,YAAY;oBACpB;iBACD;gBAED,2EAA2E;gBAC3E,MAAMqH,kBAAkBC,IAAAA,kBAAS,EAACF,SAAS;oBACzCG,UAAU;oBACVC,KAAK;gBACP;gBACA,MAAMrJ,WAAW,CAACuC;oBAChB,OAAO2G,gBAAgB3G;gBACzB;gBAEA,MAAMsE,kBACHrE,UAAU,CAAC,0BAA0B;oBACpC8G,iBAAiBV,eAAexH,MAAM,GAAG;gBAC3C,GACCqB,YAAY,CAAC;oBACZ,MAAM8G,SAAS,MAAMC,IAAAA,kBAAa,EAACZ,gBAAgB;wBACjDa,MAAM,IAAI,CAACvH,WAAW;wBACtBgC,YAAY,IAAI,CAAC1C,OAAO;wBACxB0G;wBACA5B;wBACAC;wBACAmD,SAASrD,YACL,OAAOsD,IAAInJ,QAAQoJ,KAAKC;4BACtB,OAAOxD,UAAUsD,IAAInJ,QAAQoJ,KAAK,CAACC;wBACrC,IACAC;wBACJC,QAAQ/J;wBACRgK,cAAc;oBAChB;oBACA,aAAa;oBACblK,WAAWyJ,OAAOzJ,QAAQ;oBAC1ByJ,OAAOU,WAAW,CAACC,OAAO,CAAC,CAAC7J,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAUwJ,OAAOxJ,OAAO;gBAC1B;gBAEF,MAAM8G,kBACHrE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAMxC,iBAAiBX,uBACrBQ,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAOqD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAE7B;wBACvC,MAAMsI,SAAS1B,UAAUrG,GAAG,CAACP;wBAC7B,MAAM8J,WAAUpK,eAAAA,QACba,GAAG,CAAC8C,aAAQ,CAAC+B,QAAQ,CAAC,IAAI,CAACvD,WAAW,EAAE7B,2BAD3BN,aAEZoB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAAC8I,WACD3B,MAAM4B,OAAO,CAACzB,0BAAAA,OAAQ0B,OAAO,KAC7B1B,OAAO0B,OAAO,CAACjJ,MAAM,GAAG;oBAE5B;oBAGF,KAAK,MAAM8F,SAASqB,WAAY;4BAeJtI;wBAd1B,MAAM4I,YAAY/B,aAAalG,GAAG,CAACsG;wBACnC,MAAMoD,kBAAkB5G,aAAQ,CAAC+B,QAAQ,CACvC,IAAI,CAACvD,WAAW,EAChBgF;wBAEF,MAAM4B,kBAAkB9B,kBAAkBpG,GAAG,CAACiI;wBAC9C,MAAM0B,YAAY,IAAIrK;wBAEtB,kDAAkD;wBAClD,kBAAkB;wBAClBqK,UAAU1J,GAAG,CAACqG,OAAO;4BACnBxB,SAAS;wBACX;wBAEA,KAAK,MAAM,CAAC/F,KAAK6K,KAAK,IAAIvK,EAAAA,sBAAAA,eACvBW,GAAG,CAAC0J,qCADmBrK,oBAEtBkH,OAAO,OAAM,EAAE,CAAE;4BACnBoD,UAAU1J,GAAG,CAAC6C,aAAQ,CAACC,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAEvC,MAAM;gCAClD+F,SAAS8E,KAAK1J,OAAO;4BACvB;wBACF;wBAEA,IAAIgI,iBAAiB;4BACnB,KAAK,MAAM2B,cAAc3B,gBAAgBzD,IAAI,GAAI;oCAOrBpF;gCAN1B,MAAMyK,uBAAuBhH,aAAQ,CAAC+B,QAAQ,CAC5C,IAAI,CAACvD,WAAW,EAChBuI;gCAEFF,UAAU1J,GAAG,CAAC4J,YAAY;oCAAE/E,SAAS;gCAAM;gCAE3C,KAAK,MAAM,CAAC/F,KAAK6K,KAAK,IAAIvK,EAAAA,uBAAAA,eACvBW,GAAG,CAAC8J,0CADmBzK,qBAEtBkH,OAAO,OAAM,EAAE,CAAE;oCACnBoD,UAAU1J,GAAG,CAAC6C,aAAQ,CAACC,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAEvC,MAAM;wCAClD+F,SAAS8E,KAAK1J,OAAO;oCACvB;gCACF;4BACF;wBACF;wBACA,IAAI,CAACmB,WAAW,CAACpB,GAAG,CAACgI,WAAW0B;oBAClC;gBACF;YACJ,GACCI,IAAI,CACH,IAAM/D,YACN,CAACgE,MAAQhE,SAASgE;QAExB;IAEJ;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAAStE,KAAK,CAAC9G,WAAW,CAACqL,GAAG,CAACxL,aAAa,CAACG;YAC3C,MAAMsL,kBACJC,IAAAA,yBAAkB,EAACvL,gBAAgBuL,IAAAA,yBAAkB,EAACH;YACxD,MAAM1E,6BAA6B4E,gBAAgBxI,UAAU,CAC3D;YAGF,MAAM8D,WAAW,OAAO/D;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAI4C,QAAQ,CAACuE,SAASwB;;wBAE/BxL,YAAYyL,eAAe,CACxB7E,QAAQ,CACX/D,MAAM,CAACqI,KAAKQ;4BACZ,IAAIR,KAAK,OAAOM,OAAON;4BACvBlB,QAAQ0B;wBACV;oBACF;gBACF,EAAE,OAAOC,GAAG;oBACV,IACEC,IAAAA,gBAAO,EAACD,MACPA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YACA,MAAM9E,OAAO,OAAOhE;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAI4C,QAAQ,CAACuE,SAASwB;;wBAC/BxL,YAAYyL,eAAe,CAAC5E,IAAI,CAChChE,MACA,CAACqI,KAAKY;4BACJ,IAAIZ,KAAK,OAAOM,OAAON;4BACvBlB,QAAQ8B;wBACV;oBAEJ;gBACF,EAAE,OAAOH,GAAG;oBACV,IAAIC,IAAAA,gBAAO,EAACD,MAAOA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEAjF,2BAA2BqF,OAAO,CAAC;gBACjC/L,YAAY8G,KAAK,CAACkF,aAAa,CAAChF,QAAQ,CACtC;oBACE7C,MAAMtE;oBACNoM,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACC,GAAGnF;oBACF,IAAI,CAACzE,iBAAiB,CAACzC,aAAa0G,4BACjCuE,IAAI,CAAC,IAAM/D,YACXoF,KAAK,CAAC,CAACpB,MAAQhE,SAASgE;gBAC7B;gBAGF,IAAIqB,WAAWvM,YAAYwM,eAAe,CAACtL,GAAG,CAAC;gBAE/C,SAASuL,WAAWtI,IAAY;oBAC9B,MAAMuI,WAAWvI,KAAKwI,KAAK,CAAC;oBAC5B,IAAIxI,IAAI,CAAC,EAAE,KAAK,OAAOuI,SAAShL,MAAM,GAAG,GACvC,OAAOgL,SAAShL,MAAM,GAAG,IAAIgL,SAASE,KAAK,CAAC,GAAG,GAAG3I,IAAI,CAAC,OAAO;oBAChE,OAAOyI,SAAShL,MAAM,GAAGgL,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CACjBC;oBAEA,MAAMC,cAAcR,SAASS,WAAW,CAACF;oBAEzC,OAAO,CACLhM,QACAwH,SACA4B,MAEA,IAAIzE,QAA2B,CAACuE,SAASwB;4BACvC,MAAMyB,UAAUjJ,aAAQ,CAACc,OAAO,CAAChE;4BAEjCiM,YAAY/C,OAAO,CACjB,CAAC,GACDiD,SACA3E,SACA;gCACE4E,kBAAkBlN,YAAYkN,gBAAgB;gCAC9CC,qBAAqBnN,YAAYmN,mBAAmB;gCACpDC,qBAAqBpN,YAAYoN,mBAAmB;4BACtD,GACA,OAAOlC,KAAUrB,QAASwD;gCACxB,IAAInC,KAAK,OAAOM,OAAON;gCAEvB,IAAI,CAACrB,QAAQ;oCACX,OAAO2B,OAAO,qBAA6B,CAA7B,IAAI8B,MAAM,qBAAV,qBAAA;+CAAA;oDAAA;sDAAA;oCAA4B;gCAC5C;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAIzD,OAAOlI,QAAQ,CAAC,QAAQkI,OAAOlI,QAAQ,CAAC,MAAM;oCAChDkI,SAASwD,CAAAA,8BAAAA,WAAYxK,IAAI,KAAIgH;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAOlI,QAAQ,CAAC,iBAAiB;wCACnC,IAAI4L,cAAc1D,OACfzE,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAACpB,aAAQ,CAACwJ,UAAU,CAAClF,YACrBA,QAAQ3G,QAAQ,CAAC,SACjB0L,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBhB;4CAFhBc,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9BnF,QAAQsE,KAAK,CAACH,EAAAA,cAAAA,WAAWnE,6BAAXmE,YAAqB/K,MAAM,KAAI,KAC7CsC,aAAQ,CAAC0J,GAAG,GACZ,cAAa,EAEZtI,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAMuI,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYX,KAAK,CAAC,GAAGiB;4CACnC,MAAME,qBAAqB,GAAGR,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAMrD,IAAI8D,MAAM,CAACD,qBAAqB;gDACxC,MAAM7D,IAAI+D,QAAQ,CAChB,MAAM/D,IAAIgE,QAAQ,CAACH,qBACnB,WACAjN;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAOqN,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACAnE,QAAQ;oCAACH;oCAAQiD,QAAQsB,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAGC,mCAAoB;oBACvBC,gBAAgBnE;oBAChBoE,SAASpE;oBACTqE,YAAYrE;gBACd;gBACA,MAAMsE,2BAA2B;oBAC/B,GAAGL,mBAAmB;oBACtBM,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAGC,uCAAwB;oBAC3BN,gBAAgBnE;oBAChBoE,SAASpE;oBACTqE,YAAYrE;gBACd;gBACA,MAAM0E,2BAA2B;oBAC/B,GAAGF,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAMhI,YAAY,OAChB2B,SACAxH,QACAoJ,KACA6E;oBAEA,MAAM9B,UAAUjJ,aAAQ,CAACc,OAAO,CAAChE;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAEkO,GAAG,EAAE,GAAG,MAAMC,IAAAA,gCAAe,EACnC,IAAI,CAACnN,OAAO,EACZ,IAAI,CAACM,YAAY,EACjB6K,SACA3E,SACAyG,gBACA,CAACjC,UAAY,CAACT,GAAW6C;4BACvB,OAAOrC,WAAWC,SAAShM,QAAQoO,YAAYhF;wBACjD,GACAE,WACAA,WACAwE,qBACAP,qBACAS,0BACAJ;oBAGF,IAAI,CAACM,KAAK;wBACR,MAAM,qBAAwD,CAAxD,IAAI1B,MAAM,CAAC,kBAAkB,EAAEhF,QAAQ,MAAM,EAAExH,QAAQ,GAAvD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAuD;oBAC/D;oBACA,OAAOkO,IAAI5J,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAACqB,gBAAgB,CACnBzG,aACA0G,4BACAC,WACAC,UACAC;YAEJ;QACF;IACF;AACF"}