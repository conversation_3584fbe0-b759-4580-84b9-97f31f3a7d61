{"version": 3, "sources": ["../../../src/shared/lib/image-config-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n"], "names": ["ImageConfigContext", "React", "createContext", "imageConfigDefault", "process", "env", "NODE_ENV", "displayName"], "mappings": ";;;;+BAIaA;;;eAAAA;;;;gEAJK;6BAEiB;AAE5B,MAAMA,qBACXC,cAAK,CAACC,aAAa,CAAsBC,+BAAkB;AAE7D,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCN,mBAAmBO,WAAW,GAAG;AACnC"}