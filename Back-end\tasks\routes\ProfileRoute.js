const express = require('express');
const router = express.Router();
const Profile = require('../models/ProfileModel');
const User = require('../models/UserModel');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// JWT Secret (should be in environment variables)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Set up multer for profile image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/profiles';
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, 'profile-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 2 * 1024 * 1024 }, // Limit file size to 2MB
  fileFilter: (req, file, cb) => {
    const fileTypes = /jpeg|jpg|png/;
    const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = fileTypes.test(file.mimetype);
    if (extname && mimetype) {
      return cb(null, true);
    }
    cb(new Error('Seuls les fichiers JPEG et PNG sont autorisés'));
  },
});

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Get user profile
router.get('/profile', verifyToken, async (req, res) => {
  try {
    // Find profile by userId
    let profile = await Profile.findOne({ userId: req.userId });

    // If profile doesn't exist, get basic info from User model
    if (!profile) {
      const user = await User.findById(req.userId).select('-motDePasse');
      if (!user) {
        return res.status(404).json({ error: 'Utilisateur non trouvé' });
      }

      // Create a new profile with user data
      profile = new Profile({
        userId: req.userId,
        profileImage: user.profileImage,
        telephone: user.telephone,
        adresse: user.adresse,
        contact: user.contact,
        nomEntreprise: user.nomEntreprise,
        adresseEntreprise: user.adresseEntreprise,
        telephoneEntreprise: user.telephoneEntreprise,
        numeroFiscal: user.numeroFiscal
      });

      await profile.save();

      // Combine user and profile data
      const userData = {
        id: user._id,
        nom: user.nom,
        email: user.email,
        role: user.role,
        dateCreation: user.dateCreation,
        ...profile.toObject()
      };

      return res.status(200).json(userData);
    }

    // Get user data to combine with profile
    const user = await User.findById(req.userId).select('-motDePasse');
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Combine user and profile data
    const userData = {
      id: user._id,
      nom: user.nom,
      email: user.email,
      role: user.role,
      dateCreation: user.dateCreation,
      ...profile.toObject()
    };

    res.status(200).json(userData);
  } catch (error) {
    console.error('Erreur lors de la récupération du profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération du profil' });
  }
});

// Update user profile
router.put('/profile', verifyToken, async (req, res) => {
  try {
    const {
      telephone, adresse, contact,
      nomEntreprise, adresseEntreprise, telephoneEntreprise, numeroFiscal,
      theme, notifications, bio
    } = req.body;

    // Find user to update basic info
    const user = await User.findById(req.userId);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Update user fields if provided
    if (req.body.nom) user.nom = req.body.nom;
    if (req.body.email) {
      // Check if email is already used by another user
      if (req.body.email !== user.email) {
        const existingUser = await User.findOne({ email: req.body.email });
        if (existingUser && existingUser._id.toString() !== req.userId) {
          return res.status(400).json({ error: 'Cet email est déjà utilisé par un autre utilisateur' });
        }
      }
      user.email = req.body.email;
    }

    // Save user changes
    await user.save();

    // Find or create profile
    let profile = await Profile.findOne({ userId: req.userId });
    if (!profile) {
      profile = new Profile({ userId: req.userId });
    }

    // Update profile fields if provided
    if (telephone !== undefined) profile.telephone = telephone;
    if (adresse !== undefined) profile.adresse = adresse;
    if (contact !== undefined) profile.contact = contact;
    if (bio !== undefined) profile.bio = bio;
    if (theme !== undefined) profile.theme = theme;
    if (notifications !== undefined) profile.notifications = notifications;

    // Update role-specific fields
    if (user.role === 'ADMIN') {
      if (nomEntreprise !== undefined) profile.nomEntreprise = nomEntreprise;
      if (adresseEntreprise !== undefined) profile.adresseEntreprise = adresseEntreprise;
      if (telephoneEntreprise !== undefined) profile.telephoneEntreprise = telephoneEntreprise;
      if (numeroFiscal !== undefined) profile.numeroFiscal = numeroFiscal;
    }

    // Save profile changes
    await profile.save();

    // Prepare response with combined data
    const userData = {
      id: user._id,
      nom: user.nom,
      email: user.email,
      role: user.role,
      dateCreation: user.dateCreation,
      ...profile.toObject()
    };

    res.status(200).json({ message: 'Profil mis à jour avec succès', user: userData });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour du profil' });
  }
});

// Upload profile image for current user
router.post('/profile/upload-image', verifyToken, upload.single('profileImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Aucune image n\'a été téléchargée' });
    }

    // Find user to update profileImage
    const user = await User.findById(req.userId);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Save profile image path to user
    const profileImagePath = `/uploads/profiles/${req.file.filename}`;
    user.profileImage = profileImagePath;
    await user.save();

    // Update or create profile
    let profile = await Profile.findOne({ userId: req.userId });
    if (profile) {
      profile.profileImage = profileImagePath;
      await profile.save();
    } else {
      profile = new Profile({
        userId: req.userId,
        profileImage: profileImagePath
      });
      await profile.save();
    }

    res.status(200).json({
      message: 'Image de profil téléchargée avec succès',
      profileImage: profileImagePath
    });
  } catch (error) {
    console.error('Erreur lors du téléchargement de l\'image de profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors du téléchargement de l\'image de profil' });
  }
});

// Upload profile image for a specific user (admin or entreprise can upload for vendeurs)
router.post('/profile/upload/:id', upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Aucune image n\'a été téléchargée' });
    }

    // Find user to update profileImage
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ error: 'Utilisateur non trouvé' });
    }

    // Save profile image path to user
    const profileImagePath = `/uploads/profiles/${req.file.filename}`;
    user.profileImage = profileImagePath;
    await user.save();

    // Update or create profile
    let profile = await Profile.findOne({ userId: req.params.id });
    if (profile) {
      profile.profileImage = profileImagePath;
      await profile.save();
    } else {
      profile = new Profile({
        userId: req.params.id,
        profileImage: profileImagePath
      });
      await profile.save();
    }

    res.status(200).json({
      message: 'Image de profil téléchargée avec succès',
      profileImage: profileImagePath
    });
  } catch (error) {
    console.error('Erreur lors du téléchargement de l\'image de profil:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors du téléchargement de l\'image de profil' });
  }
});

module.exports = router;
