const mongoose = require('mongoose');

// URL de connexion à MongoDB Atlas
const URL = "mongodb+srv://Gestion:<EMAIL>/";

// Options de connexion simplifiées (sans options dépréciées)
const options = {
  serverSelectionTimeoutMS: 60000, // 60 secondes
  socketTimeoutMS: 90000, // 90 secondes
  connectTimeoutMS: 60000 // 60 secondes
};

// Connexion à MongoDB simplifiée
mongoose.connect(URL, options)
  .then(() => {
    console.log("Connexion à MongoDB établie avec succès");
  })
  .catch((err) => {
    console.error("Erreur de connexion à MongoDB:", err);
  });

// Événement de connexion MongoDB
mongoose.connection.on('connected', () => {
  console.log('Mongoose connecté à MongoDB');
});