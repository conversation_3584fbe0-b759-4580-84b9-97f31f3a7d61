import React, { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogTitle, 
  IconButton, 
  Box,
  Alert,
  Snackbar
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import PaiementReceipt from './PaiementReceipt';
import factureService from '../services/factureService';
import paiementService from '../services/paiementService';

const PaiementReceiptDialog = ({ open, onClose, paiement, facture, entreprise }) => {
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // The download functionality is handled in the PaiementReceipt component
  };

  const handleEmail = async () => {
    try {
      // Call API to send email with receipt
      await paiementService.sendReceiptByEmail(paiement._id);
      
      setSnackbar({
        open: true,
        message: 'Reçu envoyé par email avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi du reçu par email:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'envoi du reçu par email',
        severity: 'error'
      });
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        aria-labelledby="receipt-dialog-title"
        PaperProps={{
          sx: {
            borderRadius: 2,
            '@media print': {
              boxShadow: 'none',
              width: '100%',
              height: '100%',
              maxWidth: 'none',
              margin: 0,
            }
          }
        }}
      >
        <DialogTitle id="receipt-dialog-title" sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', '@media print': { display: 'none' } }}>
            <IconButton
              aria-label="close"
              onClick={onClose}
              sx={{ color: 'grey.500' }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <PaiementReceipt
            paiement={paiement}
            facture={facture}
            entreprise={entreprise}
            onClose={onClose}
            onPrint={handlePrint}
            onDownload={handleDownload}
            onEmail={handleEmail}
          />
        </DialogContent>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity} 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default PaiementReceiptDialog;
