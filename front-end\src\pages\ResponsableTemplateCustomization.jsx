import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  CardMedia,
  CardActionArea,
  Tabs,
  Tab,
  Paper,
  Divider,
  Avatar,
  IconButton,
  Tooltip,
  Stack,
  Badge,
  Fade,
  Zoom
} from '@mui/material';
import {
  Save as SaveIcon,
  Palette as PaletteIcon,
  Image as ImageIcon,
  Preview as PreviewIcon,
  CloudUpload as UploadIcon,
  Business as BusinessIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { ChromePicker } from 'react-color';
import { responsableTemplateService } from '../services/responsableTemplateService';
import { baseTemplateService } from '../services/baseTemplateService';

// Add CSS animation for spinning icon
const spinKeyframes = `
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

// Inject the CSS
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = spinKeyframes;
  document.head.appendChild(style);
}

const ResponsableTemplateCustomization = () => {
  const [templateSettings, setTemplateSettings] = useState(null);
  const [baseTemplates, setBaseTemplates] = useState({ facture: [], devis: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activeTab, setActiveTab] = useState('facture');
  const [showColorPicker, setShowColorPicker] = useState({ facture: false, devis: false });
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch current template settings
      const settings = await responsableTemplateService.getTemplateSettings();
      setTemplateSettings(settings);

      // Fetch available base templates from admin
      const factureTemplates = await baseTemplateService.getAllTemplates('facture');
      const devisTemplates = await baseTemplateService.getAllTemplates('devis');

      setBaseTemplates({
        facture: factureTemplates || [],
        devis: devisTemplates || []
      });
    } catch (err) {
      setError('Erreur lors du chargement des paramètres de template');
      console.error('Error fetching template data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Helper function to get selected template
  const getSelectedTemplate = (type) => {
    const selectedId = templateSettings[`${type}Template`]?.baseTemplateId;
    return baseTemplates[type]?.find(template => template._id === selectedId);
  };

  const handleTemplateChange = (type, baseTemplateId) => {
    setTemplateSettings(prev => ({
      ...prev,
      [`${type}Template`]: {
        ...prev[`${type}Template`],
        baseTemplateId
      }
    }));
  };

  const handleColorChange = (type, colorType, color) => {
    setTemplateSettings(prev => ({
      ...prev,
      [`${type}Template`]: {
        ...prev[`${type}Template`],
        [colorType]: color.hex
      }
    }));
  };

  const handleLogoUpload = async (type, file) => {
    try {
      const formData = new FormData();
      formData.append(`${type}Logo`, file);
      formData.append('templateData', JSON.stringify(templateSettings));

      const updatedSettings = await responsableTemplateService.updateTemplateSettings(formData);
      setTemplateSettings(updatedSettings);
      setSuccess('Logo mis à jour avec succès');
    } catch (err) {
      setError('Erreur lors du téléchargement du logo');
      console.error(err);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const formData = new FormData();
      formData.append('templateData', JSON.stringify(templateSettings));

      const updatedSettings = await responsableTemplateService.updateTemplateSettings(formData);
      setTemplateSettings(updatedSettings);
      setSuccess('✅ Paramètres sauvegardés avec succès ! Vos templates sont maintenant personnalisés.');

      // Auto-hide success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('Erreur lors de la sauvegarde des paramètres');
      console.error('Save error:', err);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '50vh',
        gap: 2
      }}>
        <RefreshIcon sx={{ fontSize: 48, color: '#1976d2', animation: 'spin 1s linear infinite' }} />
        <Typography variant="h6" color="primary">Chargement de vos templates...</Typography>
        <Typography variant="body2" color="text.secondary">
          Récupération des modèles disponibles
        </Typography>
      </Box>
    );
  }

  if (!templateSettings) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={fetchData}>
              Réessayer
            </Button>
          }
        >
          Impossible de charger les paramètres de template. Vérifiez votre connexion.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Professional Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{
            fontWeight: 'bold',
            color: '#1976d2',
            mb: 1
          }}>
            Personnalisation des Templates
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Choisissez vos modèles et personnalisez-les avec vos couleurs et logo
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<VisibilityIcon />}
            onClick={() => setPreviewMode(!previewMode)}
            sx={{ borderRadius: 2 }}
          >
            {previewMode ? 'Masquer' : 'Aperçu'}
          </Button>
          <Button
            variant="contained"
            startIcon={saving ? <RefreshIcon sx={{ animation: 'spin 1s linear infinite' }} /> : <SaveIcon />}
            onClick={handleSave}
            disabled={saving}
            sx={{
              borderRadius: 2,
              px: 3,
              background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
              boxShadow: '0 3px 5px 2px rgba(25, 118, 210, .3)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
              }
            }}
          >
            {saving ? 'Sauvegarde...' : 'Sauvegarder'}
          </Button>
        </Stack>
      </Box>

      {/* Enhanced Alert Messages */}
      <Fade in={!!error}>
        <Box>
          {error && (
            <Alert
              severity="error"
              sx={{ mb: 3, borderRadius: 2 }}
              onClose={() => setError(null)}
              action={
                <Button color="inherit" size="small" onClick={fetchData}>
                  Réessayer
                </Button>
              }
            >
              {error}
            </Alert>
          )}
        </Box>
      </Fade>

      <Fade in={!!success}>
        <Box>
          {success && (
            <Alert
              severity="success"
              sx={{ mb: 3, borderRadius: 2 }}
              onClose={() => setSuccess(null)}
              icon={<CheckIcon />}
            >
              {success}
            </Alert>
          )}
        </Box>
      </Fade>

      {/* Main Content Card */}
      <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.1)' }}>
        <CardContent sx={{ p: 4 }}>
          {/* Professional Tabs */}
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              mb: 4,
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1.1rem',
                minHeight: 60,
                px: 3
              },
              '& .MuiTabs-indicator': {
                height: 3,
                borderRadius: 2
              }
            }}
          >
            <Tab
              label="Templates Facture"
              value="facture"
              icon={<DescriptionIcon />}
              iconPosition="start"
              sx={{
                color: activeTab === 'facture' ? '#1976d2' : 'text.secondary',
                '&.Mui-selected': { color: '#1976d2' }
              }}
            />
            <Tab
              label="Templates Devis"
              value="devis"
              icon={<DescriptionIcon />}
              iconPosition="start"
              sx={{
                color: activeTab === 'devis' ? '#1976d2' : 'text.secondary',
                '&.Mui-selected': { color: '#1976d2' }
              }}
            />
          </Tabs>

          <Grid container spacing={4}>
            {/* Professional Template Selection */}
            <Grid item xs={12} md={6}>
              <Paper sx={{
                p: 3,
                height: 'fit-content',
                borderRadius: 3,
                background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                border: '1px solid #e3f2fd'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Avatar sx={{ bgcolor: '#1976d2', width: 40, height: 40 }}>
                    <PreviewIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#1976d2' }}>
                      Choisir un Template {activeTab === 'facture' ? 'Facture' : 'Devis'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sélectionnez le style créé par l'administrateur
                    </Typography>
                  </Box>
                </Box>

                {baseTemplates[activeTab]?.length === 0 ? (
                  <Paper sx={{
                    p: 4,
                    textAlign: 'center',
                    bgcolor: '#f8f9fa',
                    border: '2px dashed #e0e0e0',
                    borderRadius: 2
                  }}>
                    <BusinessIcon sx={{ fontSize: 48, color: '#bdbdbd', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      Aucun template disponible
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      L'administrateur n'a pas encore créé de templates pour {activeTab}
                    </Typography>
                  </Paper>
                ) : (
                  <Grid container spacing={2}>
                    {baseTemplates[activeTab]?.map((template) => {
                      const isSelected = templateSettings[`${activeTab}Template`]?.baseTemplateId === template._id;
                      return (
                        <Grid item xs={12} key={template._id}>
                          <Zoom in={true} style={{ transitionDelay: '100ms' }}>
                            <Card
                              sx={{
                                cursor: 'pointer',
                                border: isSelected ? '3px solid' : '2px solid',
                                borderColor: isSelected ? '#1976d2' : 'transparent',
                                borderRadius: 2,
                                transition: 'all 0.3s ease',
                                position: 'relative',
                                background: isSelected
                                  ? 'linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%)'
                                  : 'white',
                                '&:hover': {
                                  transform: 'translateY(-2px)',
                                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                                  borderColor: '#1976d2'
                                }
                              }}
                              onClick={() => handleTemplateChange(activeTab, template._id)}
                            >
                              {isSelected && (
                                <Badge
                                  badgeContent={<CheckIcon sx={{ fontSize: 16 }} />}
                                  sx={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    zIndex: 1,
                                    '& .MuiBadge-badge': {
                                      bgcolor: '#4caf50',
                                      color: 'white',
                                      width: 28,
                                      height: 28,
                                      borderRadius: '50%'
                                    }
                                  }}
                                />
                              )}
                              <CardActionArea>
                                <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
                                  {/* Template Preview */}
                                  <Box sx={{
                                    width: 80,
                                    height: 60,
                                    bgcolor: '#f8f9fa',
                                    borderRadius: 1,
                                    border: '1px solid #e0e0e0',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    p: 0.5,
                                    flexShrink: 0
                                  }}>
                                    {/* Mini preview based on layout */}
                                    <Box sx={{
                                      display: 'flex',
                                      justifyContent: template.layout === 'standard' ? 'space-between' : 'flex-start',
                                      alignItems: 'center',
                                      mb: 0.5,
                                      p: 0.5,
                                      bgcolor: template.layout === 'standard' ? '#1976d2' : 'transparent',
                                      borderRadius: 0.25,
                                      minHeight: 12
                                    }}>
                                      {template.layout === 'moderne' && (
                                        <Box sx={{ width: 8, height: 6, bgcolor: '#1976d2', borderRadius: 0.25, mr: 0.5 }} />
                                      )}
                                      <Box sx={{ flex: 1, height: 2, bgcolor: template.layout === 'standard' ? 'white' : '#1976d2', borderRadius: 0.25 }} />
                                      {template.layout === 'standard' && (
                                        <Box sx={{ width: 8, height: 6, bgcolor: 'white', borderRadius: 0.25 }} />
                                      )}
                                    </Box>
                                    {/* Table preview */}
                                    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 0.25 }}>
                                      {[1,2,3].map(i => (
                                        <Box key={i} sx={{
                                          height: 3,
                                          bgcolor: template.layout === 'standard' ? (i % 2 === 0 ? '#f0f0f0' : 'white') : '#e3f2fd',
                                          borderRadius: 0.125,
                                          border: template.layout === 'moderne' ? '0.5px solid #1976d2' : 'none'
                                        }} />
                                      ))}
                                    </Box>
                                  </Box>

                                  {/* Template Info */}
                                  <Box sx={{ flex: 1 }}>
                                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5 }}>
                                      {template.name}
                                    </Typography>
                                    <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                                      <Chip
                                        label={template.layout === 'standard' ? 'Standard' : 'Moderne'}
                                        size="small"
                                        sx={{
                                          bgcolor: template.layout === 'standard' ? '#4caf50' : '#ff9800',
                                          color: 'white',
                                          fontWeight: 600,
                                          fontSize: '0.7rem'
                                        }}
                                      />
                                    </Box>
                                    <Typography variant="caption" color="text.secondary">
                                      {template.layout === 'standard'
                                        ? 'Logo à droite • En-têtes colorés • Tableaux rayés'
                                        : 'Logo à gauche • Design minimal • Tableaux bordés'
                                      }
                                    </Typography>
                                  </Box>
                                </Box>
                              </CardActionArea>
                            </Card>
                          </Zoom>
                        </Grid>
                      );
                    })}
                  </Grid>
                )}
              </Paper>
            </Grid>

            {/* Professional Customization Panel */}
            <Grid item xs={12} md={6}>
              <Paper sx={{
                p: 3,
                borderRadius: 3,
                background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                border: '1px solid #e3f2fd'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Avatar sx={{ bgcolor: '#ff9800', width: 40, height: 40 }}>
                    <PaletteIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#ff9800' }}>
                      Personnalisation
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Ajoutez vos couleurs et votre logo
                    </Typography>
                  </Box>
                </Box>

                {/* Enhanced Color Customization */}
                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle1" gutterBottom sx={{
                    fontWeight: 600,
                    color: '#1976d2',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2
                  }}>
                    <PaletteIcon fontSize="small" />
                    Couleur Principale
                  </Typography>

                  <Paper sx={{ p: 2, bgcolor: 'white', borderRadius: 2, border: '1px solid #e0e0e0' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          backgroundColor: templateSettings[`${activeTab}Template`]?.color || '#f57c00',
                          border: '3px solid white',
                          borderRadius: 2,
                          cursor: 'pointer',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          transition: 'transform 0.2s ease',
                          '&:hover': {
                            transform: 'scale(1.05)'
                          }
                        }}
                        onClick={() => setShowColorPicker(prev => ({ ...prev, [activeTab]: !prev[activeTab] }))}
                      />
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                          Code couleur
                        </Typography>
                        <TextField
                          size="small"
                          value={templateSettings[`${activeTab}Template`]?.color || '#f57c00'}
                          onChange={(e) => handleColorChange(activeTab, 'color', { hex: e.target.value })}
                          sx={{
                            width: '100%',
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2
                            }
                          }}
                          placeholder="#f57c00"
                        />
                      </Box>
                    </Box>

                    {showColorPicker[activeTab] && (
                      <Box sx={{ position: 'relative', mt: 2 }}>
                        <Box
                          sx={{ position: 'fixed', top: 0, right: 0, bottom: 0, left: 0, zIndex: 1 }}
                          onClick={() => setShowColorPicker(prev => ({ ...prev, [activeTab]: false }))}
                        />
                        <Box sx={{ position: 'relative', zIndex: 2 }}>
                          <ChromePicker
                            color={templateSettings[`${activeTab}Template`]?.color || '#f57c00'}
                            onChange={(color) => handleColorChange(activeTab, 'color', color)}
                            disableAlpha
                          />
                        </Box>
                      </Box>
                    )}

                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      Cette couleur sera appliquée aux en-têtes et éléments principaux
                    </Typography>
                  </Paper>
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* Enhanced Logo Upload */}
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{
                    fontWeight: 600,
                    color: '#1976d2',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2
                  }}>
                    <ImageIcon fontSize="small" />
                    Logo de l'Entreprise
                  </Typography>

                  <Paper sx={{ p: 3, bgcolor: 'white', borderRadius: 2, border: '1px solid #e0e0e0' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 2 }}>
                      <Box sx={{ position: 'relative' }}>
                        {templateSettings[`${activeTab}Template`]?.logo ? (
                          <Avatar
                            src={`${process.env.REACT_APP_API_URL}/${templateSettings[`${activeTab}Template`].logo}`}
                            sx={{
                              width: 80,
                              height: 80,
                              border: '3px solid #e0e0e0',
                              borderRadius: 2
                            }}
                            variant="rounded"
                          />
                        ) : (
                          <Avatar sx={{
                            width: 80,
                            height: 80,
                            bgcolor: '#f5f5f5',
                            border: '2px dashed #bdbdbd',
                            borderRadius: 2
                          }} variant="rounded">
                            <ImageIcon sx={{ fontSize: 32, color: '#bdbdbd' }} />
                          </Avatar>
                        )}
                        <Badge
                          badgeContent={templateSettings[`${activeTab}Template`]?.logo ? <CheckIcon sx={{ fontSize: 12 }} /> : '+'}
                          sx={{
                            position: 'absolute',
                            bottom: -8,
                            right: -8,
                            '& .MuiBadge-badge': {
                              bgcolor: templateSettings[`${activeTab}Template`]?.logo ? '#4caf50' : '#1976d2',
                              color: 'white',
                              width: 24,
                              height: 24,
                              borderRadius: '50%',
                              fontSize: '0.7rem'
                            }
                          }}
                        />
                      </Box>

                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ mb: 2, fontWeight: 500 }}>
                          {templateSettings[`${activeTab}Template`]?.logo
                            ? 'Logo actuel'
                            : 'Aucun logo sélectionné'
                          }
                        </Typography>
                        <Button
                          variant="contained"
                          component="label"
                          startIcon={<UploadIcon />}
                          sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600
                          }}
                          fullWidth
                        >
                          {templateSettings[`${activeTab}Template`]?.logo ? 'Changer le logo' : 'Ajouter un logo'}
                          <input
                            type="file"
                            hidden
                            accept="image/*"
                            onChange={(e) => {
                              if (e.target.files[0]) {
                                handleLogoUpload(activeTab, e.target.files[0]);
                              }
                            }}
                          />
                        </Button>
                      </Box>
                    </Box>

                    <Alert severity="info" sx={{ mt: 2, borderRadius: 2 }}>
                      <Typography variant="caption">
                        <strong>Formats acceptés:</strong> JPG, PNG, SVG (max 5MB)<br/>
                        <strong>Position:</strong> {getSelectedTemplate(activeTab)?.layout === 'standard' ? 'À droite' : 'À gauche'} du document
                      </Typography>
                    </Alert>
                  </Paper>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Enhanced Preview Section */}
          {(previewMode || getSelectedTemplate(activeTab)) && (
            <Fade in={true}>
              <Box sx={{ mt: 4 }}>
                <Paper sx={{
                  p: 4,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
                  border: '1px solid #e3f2fd'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                    <Avatar sx={{ bgcolor: '#4caf50', width: 40, height: 40 }}>
                      <VisibilityIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#4caf50' }}>
                        Aperçu du Template {activeTab === 'facture' ? 'Facture' : 'Devis'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Voici comment apparaîtra votre document personnalisé
                      </Typography>
                    </Box>
                  </Box>

                  <Paper sx={{
                    border: '2px solid #e0e0e0',
                    borderRadius: 3,
                    p: 4,
                    bgcolor: '#fff',
                    minHeight: 400,
                    position: 'relative',
                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
                  }}>
                    {/* Professional Template Preview */}
                    <Box sx={{
                      color: templateSettings[`${activeTab}Template`]?.color || '#f57c00',
                      fontFamily: 'Inter'
                    }}>
                      {/* Header Section */}
                      <Box sx={{
                        display: 'flex',
                        justifyContent: getSelectedTemplate(activeTab)?.layout === 'standard' ? 'space-between' : 'flex-start',
                        alignItems: 'flex-start',
                        mb: 4,
                        p: 2,
                        bgcolor: getSelectedTemplate(activeTab)?.layout === 'standard'
                          ? templateSettings[`${activeTab}Template`]?.color || '#f57c00'
                          : 'transparent',
                        borderRadius: getSelectedTemplate(activeTab)?.layout === 'standard' ? 2 : 0,
                        color: getSelectedTemplate(activeTab)?.layout === 'standard' ? 'white' : 'inherit'
                      }}>
                        {getSelectedTemplate(activeTab)?.layout === 'moderne' && templateSettings[`${activeTab}Template`]?.logo && (
                          <Box sx={{ mr: 3 }}>
                            <img
                              src={`${process.env.REACT_APP_API_URL}/${templateSettings[`${activeTab}Template`].logo}`}
                              alt="Logo"
                              style={{ maxWidth: 120, maxHeight: 80, objectFit: 'contain' }}
                            />
                          </Box>
                        )}

                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h4" sx={{
                            fontWeight: 'bold',
                            mb: 1,
                            color: getSelectedTemplate(activeTab)?.layout === 'moderne'
                              ? templateSettings[`${activeTab}Template`]?.color || '#f57c00'
                              : 'inherit'
                          }}>
                            {activeTab === 'facture' ? 'FACTURE' : 'DEVIS'} #2024-001
                          </Typography>
                          <Typography variant="body1" sx={{
                            opacity: 0.9,
                            color: getSelectedTemplate(activeTab)?.layout === 'moderne'
                              ? 'text.secondary'
                              : 'inherit'
                          }}>
                            Date: {new Date().toLocaleDateString('fr-FR')}
                          </Typography>
                        </Box>

                        {getSelectedTemplate(activeTab)?.layout === 'standard' && templateSettings[`${activeTab}Template`]?.logo && (
                          <Box>
                            <img
                              src={`${process.env.REACT_APP_API_URL}/${templateSettings[`${activeTab}Template`].logo}`}
                              alt="Logo"
                              style={{ maxWidth: 120, maxHeight: 80, objectFit: 'contain' }}
                            />
                          </Box>
                        )}
                      </Box>

                      {/* Client Info Section */}
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" sx={{
                          mb: 2,
                          color: templateSettings[`${activeTab}Template`]?.color || '#f57c00',
                          fontWeight: 600
                        }}>
                          Informations Client
                        </Typography>
                        <Box sx={{
                          p: 2,
                          bgcolor: '#f8f9fa',
                          borderRadius: 2,
                          border: getSelectedTemplate(activeTab)?.layout === 'moderne'
                            ? `2px solid ${templateSettings[`${activeTab}Template`]?.color || '#f57c00'}`
                            : '1px solid #e0e0e0'
                        }}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Entreprise ABC</strong>
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            123 Rue de la Paix, 75001 Paris<br/>
                            <EMAIL>
                          </Typography>
                        </Box>
                      </Box>

                      {/* Table Preview */}
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" sx={{
                          mb: 2,
                          color: templateSettings[`${activeTab}Template`]?.color || '#f57c00',
                          fontWeight: 600
                        }}>
                          Articles
                        </Typography>
                        <Box sx={{
                          border: getSelectedTemplate(activeTab)?.layout === 'moderne'
                            ? `2px solid ${templateSettings[`${activeTab}Template`]?.color || '#f57c00'}`
                            : '1px solid #e0e0e0',
                          borderRadius: 2,
                          overflow: 'hidden'
                        }}>
                          {/* Table Header */}
                          <Box sx={{
                            display: 'grid',
                            gridTemplateColumns: '2fr 1fr 1fr 1fr',
                            gap: 2,
                            p: 2,
                            bgcolor: getSelectedTemplate(activeTab)?.layout === 'standard'
                              ? templateSettings[`${activeTab}Template`]?.color || '#f57c00'
                              : '#f8f9fa',
                            color: getSelectedTemplate(activeTab)?.layout === 'standard' ? 'white' : 'inherit',
                            fontWeight: 600
                          }}>
                            <Typography variant="body2">Description</Typography>
                            <Typography variant="body2">Quantité</Typography>
                            <Typography variant="body2">Prix unitaire</Typography>
                            <Typography variant="body2">Total</Typography>
                          </Box>
                          {/* Table Rows */}
                          {[
                            { desc: 'Service de consultation', qty: '1', price: '500.00 DT', total: '500.00 DT' },
                            { desc: 'Formation équipe', qty: '2', price: '300.00 DT', total: '600.00 DT' },
                            { desc: 'Support technique', qty: '1', price: '200.00 DT', total: '200.00 DT' }
                          ].map((row, index) => (
                            <Box key={index} sx={{
                              display: 'grid',
                              gridTemplateColumns: '2fr 1fr 1fr 1fr',
                              gap: 2,
                              p: 2,
                              bgcolor: getSelectedTemplate(activeTab)?.layout === 'standard' && index % 2 === 1
                                ? '#f8f9fa'
                                : 'white',
                              borderTop: getSelectedTemplate(activeTab)?.layout === 'moderne'
                                ? `1px solid ${templateSettings[`${activeTab}Template`]?.color || '#f57c00'}`
                                : '1px solid #e0e0e0'
                            }}>
                              <Typography variant="body2">{row.desc}</Typography>
                              <Typography variant="body2">{row.qty}</Typography>
                              <Typography variant="body2">{row.price}</Typography>
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>{row.total}</Typography>
                            </Box>
                          ))}
                        </Box>
                      </Box>

                      {/* Total Section */}
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
                        <Box sx={{
                          p: 3,
                          bgcolor: templateSettings[`${activeTab}Template`]?.color || '#f57c00',
                          color: 'white',
                          borderRadius: 2,
                          minWidth: 250
                        }}>
                          <Typography variant="h5" sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                            Total: 1,300.00 DT
                          </Typography>
                        </Box>
                      </Box>

                      {/* Footer */}
                      <Box sx={{
                        mt: 4,
                        pt: 3,
                        borderTop: `2px solid ${templateSettings[`${activeTab}Template`]?.color || '#f57c00'}`,
                        textAlign: 'center'
                      }}>
                        <Typography variant="body2" color="text.secondary">
                          Merci pour votre confiance • Document généré avec vos paramètres personnalisés
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Paper>
              </Box>
            </Fade>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default ResponsableTemplateCustomization;
