{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "sourcesContent": ["import type { RouteMetadata } from '../../../export/routes/types'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from './'\nimport type { CacheFs } from '../../../shared/lib/utils'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchValue,\n  type IncrementalCacheValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\n\nimport { LRUCache } from '../lru-cache'\nimport path from '../../../shared/lib/isomorphic/path'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_DATA_SUFFIX,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n} from '../../../lib/constants'\nimport { isStale, tagsManifest } from './tags-manifest.external'\nimport { MultiFileWriter } from '../../../lib/multi-file-writer'\n\ntype FileSystemCacheContext = Omit<\n  CacheHandlerContext,\n  'fs' | 'serverDistDir'\n> & {\n  fs: CacheFs\n  serverDistDir: string\n}\n\nlet memoryCache: LRUCache<CacheHandlerValue> | undefined\n\nexport default class FileSystemCache implements CacheHandler {\n  private fs: FileSystemCacheContext['fs']\n  private flushToDisk?: FileSystemCacheContext['flushToDisk']\n  private serverDistDir: FileSystemCacheContext['serverDistDir']\n  private revalidatedTags: string[]\n  private debug: boolean\n\n  constructor(ctx: FileSystemCacheContext) {\n    this.fs = ctx.fs\n    this.flushToDisk = ctx.flushToDisk\n    this.serverDistDir = ctx.serverDistDir\n    this.revalidatedTags = ctx.revalidatedTags\n    this.debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!memoryCache) {\n        if (this.debug) {\n          console.log('using memory store for fetch cache')\n        }\n\n        memoryCache = new LRUCache(ctx.maxMemoryCacheSize, function length({\n          value,\n        }) {\n          if (!value) {\n            return 25\n          } else if (value.kind === CachedRouteKind.REDIRECT) {\n            return JSON.stringify(value.props).length\n          } else if (value.kind === CachedRouteKind.IMAGE) {\n            throw new Error('invariant image should not be incremental-cache')\n          } else if (value.kind === CachedRouteKind.FETCH) {\n            return JSON.stringify(value.data || '').length\n          } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n            return value.body.length\n          }\n          // rough estimate of size of cache value\n          return (\n            value.html.length +\n            (JSON.stringify(\n              value.kind === CachedRouteKind.APP_PAGE\n                ? value.rscData\n                : value.pageData\n            )?.length || 0)\n          )\n        })\n      }\n    } else if (this.debug) {\n      console.log('not using memory store for fetch cache')\n    }\n  }\n\n  public resetRequestCache(): void {}\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n\n    if (this.debug) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (tags.length === 0) {\n      return\n    }\n\n    for (const tag of tags) {\n      if (!tagsManifest.has(tag)) {\n        tagsManifest.set(tag, Date.now())\n      }\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { kind } = ctx\n\n    let data = memoryCache?.get(key)\n\n    if (this.debug) {\n      if (kind === IncrementalCacheKind.FETCH) {\n        console.log('get', key, ctx.tags, kind, !!data)\n      } else {\n        console.log('get', key, kind, !!data)\n      }\n    }\n\n    // let's check the disk for seed data\n    if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n      if (kind === IncrementalCacheKind.APP_ROUTE) {\n        try {\n          const filePath = this.getFilePath(\n            `${key}.body`,\n            IncrementalCacheKind.APP_ROUTE\n          )\n          const fileData = await this.fs.readFile(filePath)\n          const { mtime } = await this.fs.stat(filePath)\n\n          const meta = JSON.parse(\n            await this.fs.readFile(\n              filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n              'utf8'\n            )\n          )\n\n          const cacheEntry: CacheHandlerValue = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_ROUTE,\n              body: fileData,\n              headers: meta.headers,\n              status: meta.status,\n            },\n          }\n          return cacheEntry\n        } catch {\n          return null\n        }\n      }\n\n      try {\n        const filePath = this.getFilePath(\n          kind === IncrementalCacheKind.FETCH ? key : `${key}.html`,\n          kind\n        )\n\n        const fileData = await this.fs.readFile(filePath, 'utf8')\n        const { mtime } = await this.fs.stat(filePath)\n\n        if (kind === IncrementalCacheKind.FETCH) {\n          const { tags, fetchIdx, fetchUrl } = ctx\n\n          if (!this.flushToDisk) return null\n\n          const lastModified = mtime.getTime()\n          const parsedData: CachedFetchValue = JSON.parse(fileData)\n          data = {\n            lastModified,\n            value: parsedData,\n          }\n\n          if (data.value?.kind === CachedRouteKind.FETCH) {\n            const storedTags = data.value?.tags\n\n            // update stored tags if a new one is being added\n            // TODO: remove this when we can send the tags\n            // via header on GET same as SET\n            if (!tags?.every((tag) => storedTags?.includes(tag))) {\n              if (this.debug) {\n                console.log('tags vs storedTags mismatch', tags, storedTags)\n              }\n              await this.set(key, data.value, {\n                fetchCache: true,\n                tags,\n                fetchIdx,\n                fetchUrl,\n              })\n            }\n          }\n        } else if (kind === IncrementalCacheKind.APP_PAGE) {\n          // We try to load the metadata file, but if it fails, we don't\n          // error. We also don't load it if this is a fallback.\n          let meta: RouteMetadata | undefined\n          try {\n            meta = JSON.parse(\n              await this.fs.readFile(\n                filePath.replace(/\\.html$/, NEXT_META_SUFFIX),\n                'utf8'\n              )\n            )\n          } catch {}\n\n          let maybeSegmentData: Map<string, Buffer> | undefined\n          if (meta?.segmentPaths) {\n            // Collect all the segment data for this page.\n            // TODO: To optimize file system reads, we should consider creating\n            // separate cache entries for each segment, rather than storing them\n            // all on the page's entry. Though the behavior is\n            // identical regardless.\n            const segmentData: Map<string, Buffer> = new Map()\n            maybeSegmentData = segmentData\n            const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX\n            await Promise.all(\n              meta.segmentPaths.map(async (segmentPath: string) => {\n                const segmentDataFilePath = this.getFilePath(\n                  segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX,\n                  IncrementalCacheKind.APP_PAGE\n                )\n                try {\n                  segmentData.set(\n                    segmentPath,\n                    await this.fs.readFile(segmentDataFilePath)\n                  )\n                } catch {\n                  // This shouldn't happen, but if for some reason we fail to\n                  // load a segment from the filesystem, treat it the same as if\n                  // the segment is dynamic and does not have a prefetch.\n                }\n              })\n            )\n          }\n\n          let rscData: Buffer | undefined\n          if (!ctx.isFallback) {\n            rscData = await this.fs.readFile(\n              this.getFilePath(\n                `${key}${ctx.isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`,\n                IncrementalCacheKind.APP_PAGE\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_PAGE,\n              html: fileData,\n              rscData,\n              postponed: meta?.postponed,\n              headers: meta?.headers,\n              status: meta?.status,\n              segmentData: maybeSegmentData,\n            },\n          }\n        } else if (kind === IncrementalCacheKind.PAGES) {\n          let meta: RouteMetadata | undefined\n          let pageData: string | object = {}\n\n          if (!ctx.isFallback) {\n            pageData = JSON.parse(\n              await this.fs.readFile(\n                this.getFilePath(\n                  `${key}${NEXT_DATA_SUFFIX}`,\n                  IncrementalCacheKind.PAGES\n                ),\n                'utf8'\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.PAGES,\n              html: fileData,\n              pageData,\n              headers: meta?.headers,\n              status: meta?.status,\n            },\n          }\n        } else {\n          throw new Error(\n            `Invariant: Unexpected route kind ${kind} in file system cache.`\n          )\n        }\n\n        if (data) {\n          memoryCache?.set(key, data)\n        }\n      } catch {\n        return null\n      }\n    }\n\n    if (\n      data?.value?.kind === CachedRouteKind.APP_PAGE ||\n      data?.value?.kind === CachedRouteKind.PAGES\n    ) {\n      let cacheTags: undefined | string[]\n      const tagsHeader = data.value.headers?.[NEXT_CACHE_TAGS_HEADER]\n\n      if (typeof tagsHeader === 'string') {\n        cacheTags = tagsHeader.split(',')\n      }\n\n      if (cacheTags?.length) {\n        // we trigger a blocking validation if an ISR page\n        // had a tag revalidated, if we want to be a background\n        // revalidation instead we return data.lastModified = -1\n        if (isStale(cacheTags, data?.lastModified || Date.now())) {\n          return null\n        }\n      }\n    } else if (data?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags =\n        ctx.kind === IncrementalCacheKind.FETCH\n          ? [...(ctx.tags || []), ...(ctx.softTags || [])]\n          : []\n\n      const wasRevalidated = combinedTags.some((tag) => {\n        if (this.revalidatedTags.includes(tag)) {\n          return true\n        }\n\n        return isStale([tag], data?.lastModified || Date.now())\n      })\n      // When revalidate tag is called we don't return\n      // stale data so it's updated right away\n      if (wasRevalidated) {\n        data = undefined\n      }\n    }\n\n    return data ?? null\n  }\n\n  public async set(\n    key: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ) {\n    memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (this.debug) {\n      console.log('set', key)\n    }\n\n    if (!this.flushToDisk || !data) return\n\n    // Create a new writer that will prepare to write all the files to disk\n    // after their containing directory is created.\n    const writer = new MultiFileWriter(this.fs)\n\n    if (data.kind === CachedRouteKind.APP_ROUTE) {\n      const filePath = this.getFilePath(\n        `${key}.body`,\n        IncrementalCacheKind.APP_ROUTE\n      )\n\n      writer.append(filePath, data.body)\n\n      const meta: RouteMetadata = {\n        headers: data.headers,\n        status: data.status,\n        postponed: undefined,\n        segmentPaths: undefined,\n      }\n\n      writer.append(\n        filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n        JSON.stringify(meta, null, 2)\n      )\n    } else if (\n      data.kind === CachedRouteKind.PAGES ||\n      data.kind === CachedRouteKind.APP_PAGE\n    ) {\n      const isAppPath = data.kind === CachedRouteKind.APP_PAGE\n      const htmlPath = this.getFilePath(\n        `${key}.html`,\n        isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES\n      )\n\n      writer.append(htmlPath, data.html)\n\n      // Fallbacks don't generate a data file.\n      if (!ctx.fetchCache && !ctx.isFallback) {\n        writer.append(\n          this.getFilePath(\n            `${key}${\n              isAppPath\n                ? ctx.isRoutePPREnabled\n                  ? RSC_PREFETCH_SUFFIX\n                  : RSC_SUFFIX\n                : NEXT_DATA_SUFFIX\n            }`,\n            isAppPath\n              ? IncrementalCacheKind.APP_PAGE\n              : IncrementalCacheKind.PAGES\n          ),\n          isAppPath ? data.rscData! : JSON.stringify(data.pageData)\n        )\n      }\n\n      if (data?.kind === CachedRouteKind.APP_PAGE) {\n        let segmentPaths: string[] | undefined\n        if (data.segmentData) {\n          segmentPaths = []\n          const segmentsDir = htmlPath.replace(\n            /\\.html$/,\n            RSC_SEGMENTS_DIR_SUFFIX\n          )\n\n          for (const [segmentPath, buffer] of data.segmentData) {\n            segmentPaths.push(segmentPath)\n            const segmentDataFilePath =\n              segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX\n            writer.append(segmentDataFilePath, buffer)\n          }\n        }\n\n        const meta: RouteMetadata = {\n          headers: data.headers,\n          status: data.status,\n          postponed: data.postponed,\n          segmentPaths,\n        }\n\n        writer.append(\n          htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX),\n          JSON.stringify(meta)\n        )\n      }\n    } else if (data.kind === CachedRouteKind.FETCH) {\n      const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH)\n      writer.append(\n        filePath,\n        JSON.stringify({\n          ...data,\n          tags: ctx.fetchCache ? ctx.tags : [],\n        })\n      )\n    }\n\n    // Wait for all FS operations to complete.\n    await writer.wait()\n  }\n\n  private getFilePath(pathname: string, kind: IncrementalCacheKind): string {\n    switch (kind) {\n      case IncrementalCacheKind.FETCH:\n        // we store in .next/cache/fetch-cache so it can be persisted\n        // across deploys\n        return path.join(\n          this.serverDistDir,\n          '..',\n          'cache',\n          'fetch-cache',\n          pathname\n        )\n      case IncrementalCacheKind.PAGES:\n        return path.join(this.serverDistDir, 'pages', pathname)\n      case IncrementalCacheKind.IMAGE:\n      case IncrementalCacheKind.APP_PAGE:\n      case IncrementalCacheKind.APP_ROUTE:\n        return path.join(this.serverDistDir, 'app', pathname)\n      default:\n        throw new Error(`Unexpected file path kind: ${kind}`)\n    }\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "L<PERSON><PERSON><PERSON>", "path", "NEXT_CACHE_TAGS_HEADER", "NEXT_DATA_SUFFIX", "NEXT_META_SUFFIX", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SUFFIX", "isStale", "tagsManifest", "MultiFileWriter", "memoryCache", "FileSystemCache", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "revalidatedTags", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "maxMemoryCacheSize", "console", "log", "length", "value", "JSON", "kind", "REDIRECT", "stringify", "props", "IMAGE", "Error", "FETCH", "data", "APP_ROUTE", "body", "html", "APP_PAGE", "rscData", "pageData", "resetRequestCache", "revalidateTag", "args", "tags", "tag", "has", "set", "Date", "now", "get", "key", "NEXT_RUNTIME", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "parse", "replace", "cacheEntry", "lastModified", "getTime", "headers", "status", "fetchIdx", "fetchUrl", "parsedData", "storedTags", "every", "includes", "fetchCache", "maybeSegmentData", "segmentPaths", "segmentData", "Map", "segmentsDir", "Promise", "all", "map", "segmentPath", "segmentDataFilePath", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "postponed", "PAGES", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "split", "combinedTags", "softTags", "wasRevalidated", "some", "undefined", "writer", "append", "isAppPath", "htmlPath", "buffer", "push", "wait", "pathname", "join"], "mappings": "AAGA,SACEA,eAAe,EACfC,oBAAoB,QAKf,uBAAsB;AAE7B,SAASC,QAAQ,QAAQ,eAAc;AACvC,OAAOC,UAAU,sCAAqC;AACtD,SACEC,sBAAsB,EACtBC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,kBAAkB,EAClBC,uBAAuB,EACvBC,UAAU,QACL,yBAAwB;AAC/B,SAASC,OAAO,EAAEC,YAAY,QAAQ,2BAA0B;AAChE,SAASC,eAAe,QAAQ,iCAAgC;AAUhE,IAAIC;AAEJ,eAAe,MAAMC;IAOnBC,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,eAAe,GAAGJ,IAAII,eAAe;QAC1C,IAAI,CAACC,KAAK,GAAG,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QAEnD,IAAIR,IAAIS,kBAAkB,EAAE;YAC1B,IAAI,CAACZ,aAAa;gBAChB,IAAI,IAAI,CAACQ,KAAK,EAAE;oBACdK,QAAQC,GAAG,CAAC;gBACd;gBAEAd,cAAc,IAAIZ,SAASe,IAAIS,kBAAkB,EAAE,SAASG,OAAO,EACjEC,KAAK,EACN;wBAeIC;oBAdH,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAKhC,gBAAgBiC,QAAQ,EAAE;wBAClD,OAAOF,KAAKG,SAAS,CAACJ,MAAMK,KAAK,EAAEN,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAKhC,gBAAgBoC,KAAK,EAAE;wBAC/C,MAAM,qBAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAA2D;oBACnE,OAAO,IAAIP,MAAME,IAAI,KAAKhC,gBAAgBsC,KAAK,EAAE;wBAC/C,OAAOP,KAAKG,SAAS,CAACJ,MAAMS,IAAI,IAAI,IAAIV,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAKhC,gBAAgBwC,SAAS,EAAE;wBACnD,OAAOV,MAAMW,IAAI,CAACZ,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMY,IAAI,CAACb,MAAM,GAChBE,CAAAA,EAAAA,kBAAAA,KAAKG,SAAS,CACbJ,MAAME,IAAI,KAAKhC,gBAAgB2C,QAAQ,GACnCb,MAAMc,OAAO,GACbd,MAAMe,QAAQ,sBAHnBd,gBAIEF,MAAM,KAAI,CAAA;gBAEjB;YACF;QACF,OAAO,IAAI,IAAI,CAACP,KAAK,EAAE;YACrBK,QAAQC,GAAG,CAAC;QACd;IACF;IAEOkB,oBAA0B,CAAC;IAElC,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAI,IAAI,CAAC3B,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,iBAAiBqB;QAC/B;QAEA,IAAIA,KAAKpB,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,KAAK,MAAMqB,OAAOD,KAAM;YACtB,IAAI,CAACrC,aAAauC,GAAG,CAACD,MAAM;gBAC1BtC,aAAawC,GAAG,CAACF,KAAKG,KAAKC,GAAG;YAChC;QACF;IACF;IAEA,MAAaC,IAAI,GAAGP,IAAqC,EAAE;YAgMvDT,aACAA,cAiBSA;QAjNX,MAAM,CAACiB,KAAKvC,IAAI,GAAG+B;QACnB,MAAM,EAAEhB,IAAI,EAAE,GAAGf;QAEjB,IAAIsB,OAAOzB,+BAAAA,YAAayC,GAAG,CAACC;QAE5B,IAAI,IAAI,CAAClC,KAAK,EAAE;YACd,IAAIU,SAAS/B,qBAAqBqC,KAAK,EAAE;gBACvCX,QAAQC,GAAG,CAAC,OAAO4B,KAAKvC,IAAIgC,IAAI,EAAEjB,MAAM,CAAC,CAACO;YAC5C,OAAO;gBACLZ,QAAQC,GAAG,CAAC,OAAO4B,KAAKxB,MAAM,CAAC,CAACO;YAClC;QACF;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQhB,QAAQC,GAAG,CAACiC,YAAY,KAAK,QAAQ;YAChD,IAAIzB,SAAS/B,qBAAqBuC,SAAS,EAAE;gBAC3C,IAAI;oBACF,MAAMkB,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGH,IAAI,KAAK,CAAC,EACbvD,qBAAqBuC,SAAS;oBAEhC,MAAMoB,WAAW,MAAM,IAAI,CAAC1C,EAAE,CAAC2C,QAAQ,CAACH;oBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC5C,EAAE,CAAC6C,IAAI,CAACL;oBAErC,MAAMM,OAAOjC,KAAKkC,KAAK,CACrB,MAAM,IAAI,CAAC/C,EAAE,CAAC2C,QAAQ,CACpBH,SAASQ,OAAO,CAAC,WAAW5D,mBAC5B;oBAIJ,MAAM6D,aAAgC;wBACpCC,cAAcN,MAAMO,OAAO;wBAC3BvC,OAAO;4BACLE,MAAMhC,gBAAgBwC,SAAS;4BAC/BC,MAAMmB;4BACNU,SAASN,KAAKM,OAAO;4BACrBC,QAAQP,KAAKO,MAAM;wBACrB;oBACF;oBACA,OAAOJ;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI;gBACF,MAAMT,WAAW,IAAI,CAACC,WAAW,CAC/B3B,SAAS/B,qBAAqBqC,KAAK,GAAGkB,MAAM,GAAGA,IAAI,KAAK,CAAC,EACzDxB;gBAGF,MAAM4B,WAAW,MAAM,IAAI,CAAC1C,EAAE,CAAC2C,QAAQ,CAACH,UAAU;gBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC5C,EAAE,CAAC6C,IAAI,CAACL;gBAErC,IAAI1B,SAAS/B,qBAAqBqC,KAAK,EAAE;wBAYnCC;oBAXJ,MAAM,EAAEU,IAAI,EAAEuB,QAAQ,EAAEC,QAAQ,EAAE,GAAGxD;oBAErC,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,OAAO;oBAE9B,MAAMiD,eAAeN,MAAMO,OAAO;oBAClC,MAAMK,aAA+B3C,KAAKkC,KAAK,CAACL;oBAChDrB,OAAO;wBACL6B;wBACAtC,OAAO4C;oBACT;oBAEA,IAAInC,EAAAA,eAAAA,KAAKT,KAAK,qBAAVS,aAAYP,IAAI,MAAKhC,gBAAgBsC,KAAK,EAAE;4BAC3BC;wBAAnB,MAAMoC,cAAapC,eAAAA,KAAKT,KAAK,qBAAVS,aAAYU,IAAI;wBAEnC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAM2B,KAAK,CAAC,CAAC1B,MAAQyB,8BAAAA,WAAYE,QAAQ,CAAC3B,QAAO;4BACpD,IAAI,IAAI,CAAC5B,KAAK,EAAE;gCACdK,QAAQC,GAAG,CAAC,+BAA+BqB,MAAM0B;4BACnD;4BACA,MAAM,IAAI,CAACvB,GAAG,CAACI,KAAKjB,KAAKT,KAAK,EAAE;gCAC9BgD,YAAY;gCACZ7B;gCACAuB;gCACAC;4BACF;wBACF;oBACF;gBACF,OAAO,IAAIzC,SAAS/B,qBAAqB0C,QAAQ,EAAE;oBACjD,8DAA8D;oBAC9D,sDAAsD;oBACtD,IAAIqB;oBACJ,IAAI;wBACFA,OAAOjC,KAAKkC,KAAK,CACf,MAAM,IAAI,CAAC/C,EAAE,CAAC2C,QAAQ,CACpBH,SAASQ,OAAO,CAAC,WAAW5D,mBAC5B;oBAGN,EAAE,OAAM,CAAC;oBAET,IAAIyE;oBACJ,IAAIf,wBAAAA,KAAMgB,YAAY,EAAE;wBACtB,8CAA8C;wBAC9C,mEAAmE;wBACnE,oEAAoE;wBACpE,kDAAkD;wBAClD,wBAAwB;wBACxB,MAAMC,cAAmC,IAAIC;wBAC7CH,mBAAmBE;wBACnB,MAAME,cAAc3B,MAAM/C;wBAC1B,MAAM2E,QAAQC,GAAG,CACfrB,KAAKgB,YAAY,CAACM,GAAG,CAAC,OAAOC;4BAC3B,MAAMC,sBAAsB,IAAI,CAAC7B,WAAW,CAC1CwB,cAAcI,cAAc/E,oBAC5BP,qBAAqB0C,QAAQ;4BAE/B,IAAI;gCACFsC,YAAY7B,GAAG,CACbmC,aACA,MAAM,IAAI,CAACrE,EAAE,CAAC2C,QAAQ,CAAC2B;4BAE3B,EAAE,OAAM;4BACN,2DAA2D;4BAC3D,8DAA8D;4BAC9D,uDAAuD;4BACzD;wBACF;oBAEJ;oBAEA,IAAI5C;oBACJ,IAAI,CAAC3B,IAAIwE,UAAU,EAAE;wBACnB7C,UAAU,MAAM,IAAI,CAAC1B,EAAE,CAAC2C,QAAQ,CAC9B,IAAI,CAACF,WAAW,CACd,GAAGH,MAAMvC,IAAIyE,iBAAiB,GAAGnF,sBAAsBG,YAAY,EACnET,qBAAqB0C,QAAQ;oBAGnC;oBAEAJ,OAAO;wBACL6B,cAAcN,MAAMO,OAAO;wBAC3BvC,OAAO;4BACLE,MAAMhC,gBAAgB2C,QAAQ;4BAC9BD,MAAMkB;4BACNhB;4BACA+C,SAAS,EAAE3B,wBAAAA,KAAM2B,SAAS;4BAC1BrB,OAAO,EAAEN,wBAAAA,KAAMM,OAAO;4BACtBC,MAAM,EAAEP,wBAAAA,KAAMO,MAAM;4BACpBU,aAAaF;wBACf;oBACF;gBACF,OAAO,IAAI/C,SAAS/B,qBAAqB2F,KAAK,EAAE;oBAC9C,IAAI5B;oBACJ,IAAInB,WAA4B,CAAC;oBAEjC,IAAI,CAAC5B,IAAIwE,UAAU,EAAE;wBACnB5C,WAAWd,KAAKkC,KAAK,CACnB,MAAM,IAAI,CAAC/C,EAAE,CAAC2C,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,GAAGH,MAAMnD,kBAAkB,EAC3BJ,qBAAqB2F,KAAK,GAE5B;oBAGN;oBAEArD,OAAO;wBACL6B,cAAcN,MAAMO,OAAO;wBAC3BvC,OAAO;4BACLE,MAAMhC,gBAAgB4F,KAAK;4BAC3BlD,MAAMkB;4BACNf;4BACAyB,OAAO,EAAEN,wBAAAA,KAAMM,OAAO;4BACtBC,MAAM,EAAEP,wBAAAA,KAAMO,MAAM;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAM,qBAEL,CAFK,IAAIlC,MACR,CAAC,iCAAiC,EAAEL,KAAK,sBAAsB,CAAC,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIO,MAAM;oBACRzB,+BAAAA,YAAasC,GAAG,CAACI,KAAKjB;gBACxB;YACF,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IACEA,CAAAA,yBAAAA,cAAAA,KAAMT,KAAK,qBAAXS,YAAaP,IAAI,MAAKhC,gBAAgB2C,QAAQ,IAC9CJ,CAAAA,yBAAAA,eAAAA,KAAMT,KAAK,qBAAXS,aAAaP,IAAI,MAAKhC,gBAAgB4F,KAAK,EAC3C;gBAEmBrD;YADnB,IAAIsD;YACJ,MAAMC,cAAavD,sBAAAA,KAAKT,KAAK,CAACwC,OAAO,qBAAlB/B,mBAAoB,CAACnC,uBAAuB;YAE/D,IAAI,OAAO0F,eAAe,UAAU;gBAClCD,YAAYC,WAAWC,KAAK,CAAC;YAC/B;YAEA,IAAIF,6BAAAA,UAAWhE,MAAM,EAAE;gBACrB,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIlB,QAAQkF,WAAWtD,CAAAA,wBAAAA,KAAM6B,YAAY,KAAIf,KAAKC,GAAG,KAAK;oBACxD,OAAO;gBACT;YACF;QACF,OAAO,IAAIf,CAAAA,yBAAAA,eAAAA,KAAMT,KAAK,qBAAXS,aAAaP,IAAI,MAAKhC,gBAAgBsC,KAAK,EAAE;YACtD,MAAM0D,eACJ/E,IAAIe,IAAI,KAAK/B,qBAAqBqC,KAAK,GACnC;mBAAKrB,IAAIgC,IAAI,IAAI,EAAE;mBAAOhC,IAAIgF,QAAQ,IAAI,EAAE;aAAE,GAC9C,EAAE;YAER,MAAMC,iBAAiBF,aAAaG,IAAI,CAAC,CAACjD;gBACxC,IAAI,IAAI,CAAC7B,eAAe,CAACwD,QAAQ,CAAC3B,MAAM;oBACtC,OAAO;gBACT;gBAEA,OAAOvC,QAAQ;oBAACuC;iBAAI,EAAEX,CAAAA,wBAAAA,KAAM6B,YAAY,KAAIf,KAAKC,GAAG;YACtD;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAI4C,gBAAgB;gBAClB3D,OAAO6D;YACT;QACF;QAEA,OAAO7D,QAAQ;IACjB;IAEA,MAAaa,IACXI,GAAW,EACXjB,IAAkC,EAClCtB,GAAyE,EACzE;QACAH,+BAAAA,YAAasC,GAAG,CAACI,KAAK;YACpB1B,OAAOS;YACP6B,cAAcf,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAAChC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAO4B;QACrB;QAEA,IAAI,CAAC,IAAI,CAACrC,WAAW,IAAI,CAACoB,MAAM;QAEhC,uEAAuE;QACvE,+CAA+C;QAC/C,MAAM8D,SAAS,IAAIxF,gBAAgB,IAAI,CAACK,EAAE;QAE1C,IAAIqB,KAAKP,IAAI,KAAKhC,gBAAgBwC,SAAS,EAAE;YAC3C,MAAMkB,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGH,IAAI,KAAK,CAAC,EACbvD,qBAAqBuC,SAAS;YAGhC6D,OAAOC,MAAM,CAAC5C,UAAUnB,KAAKE,IAAI;YAEjC,MAAMuB,OAAsB;gBAC1BM,SAAS/B,KAAK+B,OAAO;gBACrBC,QAAQhC,KAAKgC,MAAM;gBACnBoB,WAAWS;gBACXpB,cAAcoB;YAChB;YAEAC,OAAOC,MAAM,CACX5C,SAASQ,OAAO,CAAC,WAAW5D,mBAC5ByB,KAAKG,SAAS,CAAC8B,MAAM,MAAM;QAE/B,OAAO,IACLzB,KAAKP,IAAI,KAAKhC,gBAAgB4F,KAAK,IACnCrD,KAAKP,IAAI,KAAKhC,gBAAgB2C,QAAQ,EACtC;YACA,MAAM4D,YAAYhE,KAAKP,IAAI,KAAKhC,gBAAgB2C,QAAQ;YACxD,MAAM6D,WAAW,IAAI,CAAC7C,WAAW,CAC/B,GAAGH,IAAI,KAAK,CAAC,EACb+C,YAAYtG,qBAAqB0C,QAAQ,GAAG1C,qBAAqB2F,KAAK;YAGxES,OAAOC,MAAM,CAACE,UAAUjE,KAAKG,IAAI;YAEjC,wCAAwC;YACxC,IAAI,CAACzB,IAAI6D,UAAU,IAAI,CAAC7D,IAAIwE,UAAU,EAAE;gBACtCY,OAAOC,MAAM,CACX,IAAI,CAAC3C,WAAW,CACd,GAAGH,MACD+C,YACItF,IAAIyE,iBAAiB,GACnBnF,sBACAG,aACFL,kBACJ,EACFkG,YACItG,qBAAqB0C,QAAQ,GAC7B1C,qBAAqB2F,KAAK,GAEhCW,YAAYhE,KAAKK,OAAO,GAAIb,KAAKG,SAAS,CAACK,KAAKM,QAAQ;YAE5D;YAEA,IAAIN,CAAAA,wBAAAA,KAAMP,IAAI,MAAKhC,gBAAgB2C,QAAQ,EAAE;gBAC3C,IAAIqC;gBACJ,IAAIzC,KAAK0C,WAAW,EAAE;oBACpBD,eAAe,EAAE;oBACjB,MAAMG,cAAcqB,SAAStC,OAAO,CAClC,WACAzD;oBAGF,KAAK,MAAM,CAAC8E,aAAakB,OAAO,IAAIlE,KAAK0C,WAAW,CAAE;wBACpDD,aAAa0B,IAAI,CAACnB;wBAClB,MAAMC,sBACJL,cAAcI,cAAc/E;wBAC9B6F,OAAOC,MAAM,CAACd,qBAAqBiB;oBACrC;gBACF;gBAEA,MAAMzC,OAAsB;oBAC1BM,SAAS/B,KAAK+B,OAAO;oBACrBC,QAAQhC,KAAKgC,MAAM;oBACnBoB,WAAWpD,KAAKoD,SAAS;oBACzBX;gBACF;gBAEAqB,OAAOC,MAAM,CACXE,SAAStC,OAAO,CAAC,WAAW5D,mBAC5ByB,KAAKG,SAAS,CAAC8B;YAEnB;QACF,OAAO,IAAIzB,KAAKP,IAAI,KAAKhC,gBAAgBsC,KAAK,EAAE;YAC9C,MAAMoB,WAAW,IAAI,CAACC,WAAW,CAACH,KAAKvD,qBAAqBqC,KAAK;YACjE+D,OAAOC,MAAM,CACX5C,UACA3B,KAAKG,SAAS,CAAC;gBACb,GAAGK,IAAI;gBACPU,MAAMhC,IAAI6D,UAAU,GAAG7D,IAAIgC,IAAI,GAAG,EAAE;YACtC;QAEJ;QAEA,0CAA0C;QAC1C,MAAMoD,OAAOM,IAAI;IACnB;IAEQhD,YAAYiD,QAAgB,EAAE5E,IAA0B,EAAU;QACxE,OAAQA;YACN,KAAK/B,qBAAqBqC,KAAK;gBAC7B,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOnC,KAAK0G,IAAI,CACd,IAAI,CAACzF,aAAa,EAClB,MACA,SACA,eACAwF;YAEJ,KAAK3G,qBAAqB2F,KAAK;gBAC7B,OAAOzF,KAAK0G,IAAI,CAAC,IAAI,CAACzF,aAAa,EAAE,SAASwF;YAChD,KAAK3G,qBAAqBmC,KAAK;YAC/B,KAAKnC,qBAAqB0C,QAAQ;YAClC,KAAK1C,qBAAqBuC,SAAS;gBACjC,OAAOrC,KAAK0G,IAAI,CAAC,IAAI,CAACzF,aAAa,EAAE,OAAOwF;YAC9C;gBACE,MAAM,qBAA+C,CAA/C,IAAIvE,MAAM,CAAC,2BAA2B,EAAEL,MAAM,GAA9C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;QACxD;IACF;AACF"}