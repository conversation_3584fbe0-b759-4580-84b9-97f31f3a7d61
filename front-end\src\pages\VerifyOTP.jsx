import React, { useState, useEffect } from "react"
import { Link as RouterLink, useNavigate, useLocation } from "react-router-dom"
import {
  Box,
  <PERSON>ton,
  Card,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  useTheme,
  InputAdornment
} from "@mui/material"
import { <PERSON><PERSON><PERSON>, <PERSON>R<PERSON><PERSON>, Vpn<PERSON>ey } from "@mui/icons-material"
import { motion } from "framer-motion"
import authService from "../services/authService"
import { useAuth } from "../contexts/AuthContext"
import logo from '../assets/benyounes_logo.png'

const VerifyOTP = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const theme = useTheme()
  const { setCurrentUser, setIsAuthenticated } = useAuth()

  const [email, setEmail] = useState("")
  const [otp, setOtp] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [step, setStep] = useState(1) // 1: OTP verification, 2: New password
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState({ type: "", text: "" })

  useEffect(() => {
    // Récupérer l'email depuis l'état de navigation ou les paramètres d'URL
    if (location.state?.email) {
      setEmail(location.state.email)
    } else {
      // Essayer de récupérer l'email depuis les paramètres d'URL
      const params = new URLSearchParams(location.search);
      const emailParam = params.get('email');

      if (emailParam) {
        setEmail(emailParam);
      } else {
        // Demander à l'utilisateur de saisir son email
        const userEmail = prompt("Veuillez saisir votre adresse email pour continuer la réinitialisation du mot de passe");

        if (userEmail) {
          setEmail(userEmail);
        } else {
          // Si l'utilisateur annule, rediriger vers la page de mot de passe oublié
          navigate('/forgot-password');
        }
      }
    }
  }, [location, navigate])

  const handleOtpChange = (e) => {
    // Limiter à 6 chiffres
    const value = e.target.value.replace(/[^0-9]/g, '')
    if (value.length <= 6) {
      setOtp(value)
    }
  }

  const handlePasswordChange = (e) => {
    setPassword(e.target.value)
  }

  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value)
  }

  const handleVerifyOtp = async (e) => {
    e.preventDefault()

    if (!otp || otp.length !== 6) {
      setMessage({ type: "error", text: "Veuillez saisir le code à 6 chiffres." })
      return
    }

    setIsSubmitting(true)
    setMessage({ type: "", text: "" })

    try {
      await authService.verifyOTP(email, otp)
      setMessage({
        type: "success",
        text: "Code vérifié avec succès. Veuillez définir votre nouveau mot de passe.",
      })

      // Passer à l'étape de définition du nouveau mot de passe
      setStep(2)
    } catch (error) {
      setMessage({
        type: "error",
        text: error.error || "Code invalide ou expiré. Veuillez réessayer.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleResetPassword = async (e) => {
    e.preventDefault()

    // Validation
    if (!password) {
      setMessage({ type: "error", text: "Veuillez saisir un nouveau mot de passe." })
      return
    }

    if (password.length < 8) {
      setMessage({ type: "error", text: "Le mot de passe doit contenir au moins 8 caractères." })
      return
    }

    if (password !== confirmPassword) {
      setMessage({ type: "error", text: "Les mots de passe ne correspondent pas." })
      return
    }

    setIsSubmitting(true)
    setMessage({ type: "", text: "" })

    try {
      const response = await authService.resetPasswordWithOTP(email, otp, password)

      // Mettre à jour le contexte d'authentification
      if (response.user && response.token) {
        setCurrentUser(response.user)
        setIsAuthenticated(true)
      }

      setMessage({
        type: "success",
        text: "Votre mot de passe a été réinitialisé avec succès. Vous allez être redirigé vers votre tableau de bord.",
      })

      // Redirect after a short delay
      setTimeout(() => {
        // Redirect based on user role
        if (response.user && response.user.role) {
          if (response.user.role === 'ADMIN') {
            navigate('/admin/analytics')
          } else if (response.user.role === 'ENTREPRISE') {
            navigate('/entreprise/analytics')
          } else if (response.user.role === 'VENDEUR') {
            navigate('/vendeur/analytics')
          } else {
            navigate('/client')
          }
        } else {
          navigate('/login')
        }
      }, 3000)
    } catch (error) {
      setMessage({
        type: "error",
        text: error.error || "Une erreur est survenue lors de la réinitialisation du mot de passe.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Box
      component={motion.div}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      sx={{
        display: 'flex',
        minHeight: '100vh',
        backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient 15s ease infinite',
        alignItems: 'center',
        justifyContent: 'center',
        padding: { xs: 2, md: 4 },
        '@keyframes gradient': {
          '0%': {
            backgroundPosition: '0% 50%'
          },
          '50%': {
            backgroundPosition: '100% 50%'
          },
          '100%': {
            backgroundPosition: '0% 50%'
          },
        },
      }}
    >
      <Card
        elevation={6}
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          width: '100%',
          maxWidth: '1000px',
          borderRadius: 4,
          overflow: 'hidden',
          background: '#fff',
        }}
      >
        {/* Left Side - Brand Section */}
        <Box
          component={motion.div}
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.45 },
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            backgroundSize: '200% 200%',
            animation: 'gradientBg 10s ease infinite',
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 4,
            position: 'relative',
            overflow: 'hidden',
            '@keyframes gradientBg': {
              '0%': {
                backgroundPosition: '0% 50%'
              },
              '50%': {
                backgroundPosition: '100% 50%'
              },
              '100%': {
                backgroundPosition: '0% 50%'
              },
            },
          }}
        >
          {/* Background Pattern */}
          <Box sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            opacity: 0.1,
            backgroundImage: `
              radial-gradient(circle at 20% 35%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
              radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 40%)
            `,
            zIndex: 0,
          }} />

          <Box sx={{
            textAlign: 'center',
            position: 'relative',
            zIndex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}>
            <Box
              component={motion.img}
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              src={logo}
              alt="BENYOUNES WEB Logo"
              sx={{
                width: '200px',
                mb: 3,
                filter: 'brightness(1.05)',
              }}
            />

            <Typography
              component={motion.h1}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.5 }}
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 2,
                letterSpacing: '0.5px',
                textShadow: '0 2px 10px rgba(0,0,0,0.1)',
              }}
            >
              Réinitialisation de mot de passe
            </Typography>

            <Typography
              component={motion.p}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.5 }}
              variant="body1"
              sx={{
                mb: 3,
                opacity: 0.9,
                maxWidth: '350px',
                textAlign: 'center',
                lineHeight: 1.6,
              }}
            >
              {step === 1
                ? "Veuillez saisir le code de vérification reçu par email pour réinitialiser votre mot de passe."
                : "Définissez votre nouveau mot de passe sécurisé pour accéder à votre compte."
              }
            </Typography>
          </Box>
        </Box>

        {/* Right Side - Form */}
        <Box
          component={motion.div}
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          sx={{
            flex: { xs: 1, md: 0.55 },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            padding: { xs: 3, sm: 4, md: 5 },
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Box sx={{ maxWidth: '450px', width: '100%', mx: 'auto', position: 'relative' }}>
            <Button
              startIcon={<ArrowBack />}
              onClick={() => navigate('/forgot-password')}
              sx={{
                mb: 4,
                color: theme.palette.text.secondary,
                '&:hover': {
                  backgroundColor: 'transparent',
                  color: theme.palette.primary.main,
                }
              }}
            >
              Retour
            </Button>

            <Typography
              variant="h4"
              sx={{
                fontWeight: 600,
                mb: 1,
                color: theme.palette.text.primary,
              }}
            >
              {step === 1 ? "Vérification du code" : "Nouveau mot de passe"}
            </Typography>

            <Typography
              variant="body2"
              sx={{
                mb: 4,
                color: theme.palette.text.secondary,
              }}
            >
              {step === 1
                ? `Un code a été envoyé à ${email}. Veuillez le saisir ci-dessous.`
                : "Veuillez saisir et confirmer votre nouveau mot de passe."
              }
            </Typography>

            {message.text && (
              <Alert
                severity={message.type}
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                {message.text}
              </Alert>
            )}

            {step === 1 ? (
              <form onSubmit={handleVerifyOtp}>
                <TextField
                  label="Code de vérification"
                  name="otp"
                  type="text"
                  value={otp}
                  onChange={handleOtpChange}
                  fullWidth
                  margin="normal"
                  required
                  inputProps={{
                    maxLength: 6,
                    inputMode: 'numeric',
                    pattern: '[0-9]*'
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <VpnKey color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  disabled={isSubmitting}
                  disableElevation
                  startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <VpnKey />}
                  sx={{
                    mt: 2,
                    mb: 3,
                    backgroundColor: theme.palette.primary.main,
                    color: '#fff',
                    borderRadius: 2,
                    padding: '12px',
                    fontWeight: 600,
                    textTransform: 'none',
                    fontSize: '1rem',
                    transition: 'all 0.3s',
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                      boxShadow: '0 4px 12px rgba(63, 81, 255, 0.25)',
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  {isSubmitting ? "Vérification..." : "Vérifier le code"}
                </Button>
              </form>
            ) : (
              <form onSubmit={handleResetPassword}>
                <TextField
                  label="Nouveau mot de passe"
                  name="password"
                  type="password"
                  value={password}
                  onChange={handlePasswordChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockReset color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                <TextField
                  label="Confirmer le mot de passe"
                  name="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  fullWidth
                  margin="normal"
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockReset color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      backgroundColor: theme.palette.background.default,
                      transition: 'all 0.3s ease',
                      '&:hover, &.Mui-focused': {
                        backgroundColor: '#fff',
                        boxShadow: '0 0 0 1px rgba(63, 81, 255, 0.2)'
                      }
                    },
                  }}
                />

                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  disabled={isSubmitting}
                  disableElevation
                  startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <LockReset />}
                  sx={{
                    mt: 2,
                    mb: 3,
                    backgroundColor: theme.palette.primary.main,
                    color: '#fff',
                    borderRadius: 2,
                    padding: '12px',
                    fontWeight: 600,
                    textTransform: 'none',
                    fontSize: '1rem',
                    transition: 'all 0.3s',
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                      boxShadow: '0 4px 12px rgba(63, 81, 255, 0.25)',
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  {isSubmitting ? "Réinitialisation..." : "Réinitialiser le mot de passe"}
                </Button>
              </form>
            )}
          </Box>
        </Box>
      </Card>
    </Box>
  )
}

export default VerifyOTP
