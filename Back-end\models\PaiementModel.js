const mongoose = require('mongoose');
const { Schema } = mongoose;

const paiementSchema = new Schema({
    // Informations de base
    montant: {
        type: Number,
        required: true,
        min: 0
    },
    modePaiement: {
        type: String,
        enum: ['CHEQUE', 'VIREMENT_BANCAIRE', 'ESPECES'],
        required: true,
        default: 'VIREMENT_BANCAIRE'
    },
    datePaiement: {
        type: Date,
        default: Date.now
    },
    factureId: {
        type: Schema.Types.ObjectId,
        ref: 'Facture',
        required: true
    },

    // Informations complémentaires
    reference: {
        type: String,
        trim: true
    },
    notes: {
        type: String,
        trim: true
    },

    // Statut
    statut: {
        type: String,
        enum: ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'],
        default: 'COMPLETED'
    },

    // Métadonnées
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    }
}, {
    timestamps: true
});

// Méthode pour formater le montant
paiementSchema.methods.formatMontant = function() {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
    }).format(this.montant);
};

// Méthode pour formater la date
paiementSchema.methods.formatDate = function() {
    return new Intl.DateTimeFormat('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(this.datePaiement));
};

// Méthode pour formater le mode de paiement
paiementSchema.methods.formatModePaiement = function() {
    const modes = {
        'CHEQUE': 'Chèque',
        'VIREMENT_BANCAIRE': 'Virement bancaire',
        'ESPECES': 'Espèces'
    };
    return modes[this.modePaiement] || this.modePaiement;
};

module.exports = mongoose.model('Paiement', paiementSchema);
