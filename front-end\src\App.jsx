"use client"

import { useState } from "react"
import { Routes, Route, Navigate } from "react-router-dom"
import { ThemeProvider, createTheme } from "@mui/material/styles"
import CssBaseline from "@mui/material/CssBaseline"
import { useAuth } from "./contexts/AuthContext"

import { LanguageProvider } from "./contexts/LanguageContext"

// Pages d'authentification
import Login from "./pages/Login"
import AdminLogin from "./pages/AdminLogin"
import SignUp from "./pages/SignUp"
import ForgotPassword from "./pages/ForgotPassword"
import VerifyOTP from "./pages/VerifyOTP"

// Pages principales de l'application
import Factures from "./pages/Factures"
import FactureForm from "./components/FactureForm"
import Devis from "./pages/Devis"
import DevisForm from "./components/DevisForm"
import Clients from "./pages/Clients"
import Produits from "./pages/Produits"
import Parametres from "./pages/Parametres"
import ParametresAdmin from "./pages/ParametresAdmin"
import TemplateSettings from "./pages/TemplateSettings"
import AdminTemplateManagement from "./pages/AdminTemplateManagement"
import ResponsableTemplateCustomization from "./pages/ResponsableTemplateCustomization"
import Entreprise from "./pages/Entreprise"
import UserProfile from "./pages/UserProfile"
import UsersPage from "./pages/UsersPage"
import VendeursPage from "./pages/VendeursPage"
import EntreprisesPage from "./pages/EntreprisesPage"
import UserForm from "./pages/UserForm"
import EntreprisePaiements from "./pages/EntreprisePaiements"
import EntrepriseDocumentView from "./pages/EntrepriseDocumentView"
import PowerBIDashboard from "./pages/PowerBIDashboard"
import AdminAnalyticsDashboard from "./pages/AdminAnalyticsDashboard"
import VendeurBIDashboard from "./pages/VendeurBIDashboard"
import EntrepriseBIDashboard from "./pages/EntrepriseBIDashboard"
import VendeurPaiements from "./pages/VendeurPaiements"
import EquipeManagement from "./pages/EquipeManagement"
import ResponsableAbonnementPage from "./pages/ResponsableAbonnementPage"
import Livreurs from "./pages/Livreurs"
import BonLivraison from "./pages/BonLivraison"

// Pages pour les clients
import ClientDashboard from "./pages/ClientDashboard"
import ClientFactures from "./pages/ClientFactures"
import ClientDevis from "./pages/ClientDevis"
import ClientProfile from "./pages/ClientProfile"
import ClientPaiements from "./pages/ClientPaiements"
import ClientFactureView from "./components/ClientFactureView"
import ClientDevisView from "./components/ClientDevisView"
import ClientDemandeDevisForm from "./components/ClientDemandeDevisForm"
import ResponsableDevisValidationForm from "./components/ResponsableDevisValidationForm"

// Composants d'abonnement
import AbonnementsList from "./components/admin/abonnements/AbonnementsList"
import AbonnementForm from "./components/admin/abonnements/AbonnementForm"
import AbonnementDetails from "./components/admin/abonnements/AbonnementDetails"

// Composants de layout
import AdminLayout from "./layouts/AdminLayout"
import VendeurLayout from "./layouts/VendeurLayout"
import ClientLayout from "./layouts/ClientLayout"
import ResponsableLayout from "./layouts/ResponsableLayout"
import AuthLayout from "./layouts/AuthLayout"

// Context d'authentification
import { AuthProvider } from "./contexts/AuthContext"
import { ProtectedRoute, RoleBasedRoute } from "./utils/authUtils"

// Not Found Page
const NotFound = () => (
  <div style={{ textAlign: 'center', padding: '40px' }}>
    <h1>404 - Page Not Found</h1>
    <p>The page you are looking for does not exist.</p>
    <a href="/">Go to Dashboard</a>
  </div>
)

function App() {
  const [darkMode, setDarkMode] = useState(localStorage.getItem("darkMode") === "true")

  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
    localStorage.setItem("darkMode", !darkMode)
  }

  const theme = createTheme({
    palette: {
      mode: darkMode ? "dark" : "light",
      primary: {
        main: "#3a6ea5",
        light: "#4f83b9",
        dark: "#2a5080",
        contrastText: "#ffffff"
      },
      secondary: {
        main: "#ff6b6b",
        light: "#ff8c8c",
        dark: "#e54b4b",
        contrastText: "#ffffff"
      },
      success: {
        main: "#2ecc71",
        light: "#55d98d",
        dark: "#27ae60"
      },
      warning: {
        main: "#f39c12",
        light: "#f7b541",
        dark: "#d68910"
      },
      error: {
        main: "#e74c3c",
        light: "#ec7063",
        dark: "#c0392b"
      },
      info: {
        main: "#3498db",
        light: "#5dade2",
        dark: "#2980b9"
      },
      background: {
        default: darkMode ? "#121820" : "#f8fafc",
        paper: darkMode ? "#1e2430" : "#ffffff",
      },
      text: {
        primary: darkMode ? "#f0f2f5" : "#2c3e50",
        secondary: darkMode ? "#a0aec0" : "#718096",
      },
      divider: darkMode ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.08)",
      status: {
        finalized: "#2ecc71",
        pending: "#f39c12",
        draft: "#a0aec0",
        paid: "#3498db",
        late: "#e74c3c",
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      h1: { fontSize: "2.25rem", fontWeight: 700, lineHeight: 1.2 },
      h2: { fontSize: "1.875rem", fontWeight: 700, lineHeight: 1.3 },
      h3: { fontSize: "1.5rem", fontWeight: 600, lineHeight: 1.35 },
      h4: { fontSize: "1.25rem", fontWeight: 600, lineHeight: 1.4 },
      h5: { fontSize: "1.125rem", fontWeight: 600, lineHeight: 1.4 },
      h6: { fontSize: "1rem", fontWeight: 600, lineHeight: 1.5 },
      subtitle1: { fontSize: "0.9375rem", lineHeight: 1.5, fontWeight: 500 },
      subtitle2: { fontSize: "0.875rem", lineHeight: 1.5, fontWeight: 500 },
      body1: { fontSize: "0.9375rem", lineHeight: 1.5 },
      body2: { fontSize: "0.875rem", lineHeight: 1.5 },
      button: { textTransform: "none", fontWeight: 500 },
    },
    shape: { borderRadius: 12 },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 10,
            textTransform: "none",
            fontWeight: 600,
            padding: "8px 20px",
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              transform: "translateY(-2px)",
              boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
            },
          },
          contained: {
            boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
          },
          containedPrimary: {
            boxShadow: "0 2px 6px rgba(58, 110, 165, 0.2)"
          },
          containedSecondary: {
            boxShadow: "0 2px 6px rgba(255, 107, 107, 0.2)"
          },
          outlined: {
            borderWidth: 2,
            "&:hover": {
              borderWidth: 2,
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            boxShadow: darkMode
              ? "0 4px 20px rgba(0, 0, 0, 0.3)"
              : "0 8px 30px rgba(0, 0, 0, 0.04), 0 2px 8px rgba(0, 0, 0, 0.02)",
            overflow: "hidden",
            transition: "transform 0.3s ease, box-shadow 0.3s ease",
            "&:hover": {
              transform: "translateY(-4px)",
              boxShadow: darkMode
                ? "0 10px 30px rgba(0, 0, 0, 0.4)"
                : "0 12px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04)",
            },
          },
        },
      },
      MuiCardContent: {
        styleOverrides: {
          root: {
            padding: "24px",
            "&:last-child": {
              paddingBottom: "24px",
            },
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            padding: "16px",
            borderColor: darkMode ? "rgba(255,255,255,0.08)" : "rgba(0,0,0,0.06)",
          },
          head: {
            fontWeight: 600,
            backgroundColor: darkMode ? "#2a3441" : "#f1f5f9",
          },
        },
      },
      MuiTableRow: {
        styleOverrides: {
          root: {
            "&:last-child td": {
              borderBottom: 0,
            },
            "&:hover": {
              backgroundColor: darkMode ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.02)",
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 600,
            padding: "0 2px",
          },
          filled: {
            boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundImage: "none",
            borderRadius: 16,
          },
          elevation1: {
            boxShadow: darkMode
              ? "0 2px 8px rgba(0, 0, 0, 0.3)"
              : "0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02)",
          },
          elevation2: {
            boxShadow: darkMode
              ? "0 3px 12px rgba(0, 0, 0, 0.4)"
              : "0 4px 16px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.03)",
          },
        },
      },
      MuiOutlinedInput: {
        styleOverrides: {
          root: {
            borderRadius: 10,
            transition: "box-shadow 0.2s ease-in-out",
            "& fieldset": {
              borderColor: darkMode ? "rgba(255,255,255,0.12)" : "rgba(0,0,0,0.12)",
            },
            "&:hover fieldset": {
              borderColor: darkMode ? "rgba(255,255,255,0.2)" : "rgba(0,0,0,0.2)",
            },
            "&.Mui-focused": {
              boxShadow: darkMode
                ? "0 0 0 3px rgba(58, 110, 165, 0.2)"
                : "0 0 0 3px rgba(58, 110, 165, 0.1)",
            },
          },
        },
      },
      MuiDialog: {
        styleOverrides: {
          paper: {
            borderRadius: 20,
            boxShadow: "0 12px 40px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08)",
          },
        },
      },
      MuiDivider: {
        styleOverrides: {
          root: {
            borderColor: darkMode ? "rgba(255,255,255,0.08)" : "rgba(0,0,0,0.08)",
            margin: "8px 0",
          },
        },
      },
      MuiTabs: {
        styleOverrides: {
          root: {
            borderBottom: `1px solid ${darkMode ? "rgba(255,255,255,0.08)" : "rgba(0,0,0,0.08)"}`,
          },
          indicator: {
            height: 3,
            borderRadius: "3px 3px 0 0",
          },
        },
      },
      MuiTab: {
        styleOverrides: {
          root: {
            textTransform: "none",
            fontWeight: 600,
            padding: "12px 24px",
            minWidth: "auto",
            "&.Mui-selected": {
              fontWeight: 700,
              color: darkMode ? theme => theme.palette.primary.light : theme => theme.palette.primary.main,
            },
          },
        },
      },
      MuiMenuItem: {
        styleOverrides: {
          root: {
            paddingTop: 10,
            paddingBottom: 10,
            fontWeight: 500,
            "&:hover": {
              backgroundColor: darkMode ? "rgba(255,255,255,0.04)" : "rgba(0,0,0,0.04)",
            },
          },
        },
      },
    },
  })

  const AppContent = () => {
    const { loading, isAuthenticated, currentUser } = useAuth();

    if (loading) {
      return <div style={{ textAlign: 'center', padding: '40px' }}>Chargement...</div>;
    }

    return (
      <Routes>
        {/* Default Route: Redirect to appropriate dashboard based on role */}
        <Route
          path="/"
          element={
            isAuthenticated ? (
              currentUser && currentUser.role === 'ADMIN' ? (
                <Navigate to="/admin/analytics" replace />
              ) : currentUser && currentUser.role === 'RESPONSABLE' ? (
                <Navigate to="/responsable/analytics" replace />
              ) : currentUser && currentUser.role === 'VENDEUR' ? (
                <Navigate to="/vendeur/analytics" replace />
              ) : currentUser && currentUser.role === 'CLIENT' ? (
                <Navigate to="/client/dashboard" replace />
              ) : (
                <Navigate to="/login" replace />
              )
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Auth Routes */}
        <Route path="/login" element={<AuthLayout><Login /></AuthLayout>} />
        <Route path="/admin-login" element={<AuthLayout><AdminLogin /></AuthLayout>} />
        <Route path="/signup" element={<AuthLayout><SignUp /></AuthLayout>} />
        <Route path="/forgot-password" element={<AuthLayout><ForgotPassword /></AuthLayout>} />
        <Route path="/verify-otp" element={<AuthLayout><VerifyOTP /></AuthLayout>} />

        {/* Admin Routes */}
        <Route
          path="/admin"
          element={
            <RoleBasedRoute roles="ADMIN">
              <Navigate to="/admin/analytics" replace />
            </RoleBasedRoute>
          }
        />

        <Route
          path="/admin/clients"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Clients />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/produits"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Produits />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/parametres"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ParametresAdmin />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/template-settings"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <TemplateSettings />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/templates"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <AdminTemplateManagement />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/entreprise"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Entreprise />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/profile"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <UserProfile />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/admin/analytics"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <AdminAnalyticsDashboard />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/utilisateurs"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <UsersPage />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/utilisateurs/vendeurs"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <VendeursPage />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/utilisateurs/entreprises"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <EntreprisesPage />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/utilisateurs/new"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <UserForm />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/utilisateurs/edit/:id"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <UserForm />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />

        {/* Routes pour les abonnements */}
        <Route
          path="/admin/abonnements"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <AbonnementsList />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/abonnements/create"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <AbonnementForm />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/abonnements/edit/:id"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <AbonnementForm />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/admin/abonnements/:id"
          element={
            <RoleBasedRoute roles="ADMIN">
              <AdminLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <AbonnementDetails />
              </AdminLayout>
            </RoleBasedRoute>
          }
        />

        {/* Vendeur Routes */}
        <Route
          path="/vendeur"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <Navigate to="/vendeur/analytics" replace />
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/dashboard"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <Navigate to="/vendeur/analytics" replace />
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/factures"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Factures />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/factures/new"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <FactureForm />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/factures/:id"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <FactureForm />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/devis"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Devis />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/devis/new"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <DevisForm />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/devis/:id"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <DevisForm />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/clients"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Clients />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/produits"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Produits />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/paiements"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <VendeurPaiements />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/profile"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <UserProfile />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/vendeur/analytics"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <VendeurBIDashboard />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/vendeur/bon-livraisons"
          element={
            <RoleBasedRoute roles="VENDEUR">
              <VendeurLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <BonLivraison />
              </VendeurLayout>
            </RoleBasedRoute>
          }
        />

        {/* Responsable d'entreprise Routes */}
        <Route
          path="/responsable"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <Navigate to="/responsable/analytics" replace />
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/factures"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Factures />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/factures/new"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <FactureForm />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/factures/:id"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <FactureForm />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/devis"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Devis />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/devis/new"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <DevisForm />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/devis/:id"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <DevisForm />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/devis/validation/:id"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ResponsableDevisValidationForm />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/paiements"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <EntreprisePaiements />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/profile"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <UserProfile />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/responsable/entreprise"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Entreprise />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/responsable/analytics"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <EntrepriseBIDashboard />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/responsable/clients"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Clients />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/responsable/produits"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Produits />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/responsable/vendeurs"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <EquipeManagement />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        <Route
          path="/responsable/abonnement"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ResponsableAbonnementPage />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/livreurs"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Livreurs />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/bon-livraisons"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <BonLivraison />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/livreurs"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <Livreurs />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/bon-livraisons"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <BonLivraison />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/responsable/templates"
          element={
            <RoleBasedRoute roles="RESPONSABLE">
              <ResponsableLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ResponsableTemplateCustomization />
              </ResponsableLayout>
            </RoleBasedRoute>
          }
        />

        {/* Legacy Routes - Redirect to appropriate dashboard */}
        <Route
          path="/factures"
          element={
            <ProtectedRoute>
              {currentUser && currentUser.role === 'ADMIN' ? (
                <Navigate to="/admin/analytics" replace />
              ) : currentUser && currentUser.role === 'RESPONSABLE' ? (
                <Navigate to="/responsable/factures" replace />
              ) : currentUser && currentUser.role === 'VENDEUR' ? (
                <Navigate to="/vendeur/factures" replace />
              ) : (
                <Navigate to="/login" replace />
              )}
            </ProtectedRoute>
          }
        />
        <Route
          path="/devis"
          element={
            <ProtectedRoute>
              {currentUser && currentUser.role === 'ADMIN' ? (
                <Navigate to="/admin/analytics" replace />
              ) : currentUser && currentUser.role === 'RESPONSABLE' ? (
                <Navigate to="/responsable/devis" replace />
              ) : currentUser && currentUser.role === 'VENDEUR' ? (
                <Navigate to="/vendeur/devis" replace />
              ) : (
                <Navigate to="/login" replace />
              )}
            </ProtectedRoute>
          }
        />
        {/* Client Routes */}
        <Route
          path="/client"
          element={
            <RoleBasedRoute roles="CLIENT">
              <Navigate to="/client/dashboard" replace />
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/dashboard"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientDashboard />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/factures"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientFactures />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/factures/:id"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientFactureView />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/devis"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientDevis />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/devis/demande"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientDemandeDevisForm />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/devis/:id"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientDevisView />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/profile"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientProfile />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/paiements"
          element={
            <RoleBasedRoute roles="CLIENT">
              <ClientLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
                <ClientPaiements />
              </ClientLayout>
            </RoleBasedRoute>
          }
        />
        <Route
          path="/client/analytics"
          element={
            <RoleBasedRoute roles="CLIENT">
              <Navigate to="/client/dashboard" replace />
            </RoleBasedRoute>
          }
        />

        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              {currentUser && currentUser.role === 'ADMIN' ? (
                <Navigate to="/admin/profile" replace />
              ) : currentUser && currentUser.role === 'RESPONSABLE' ? (
                <Navigate to="/responsable/profile" replace />
              ) : currentUser && currentUser.role === 'VENDEUR' ? (
                <Navigate to="/vendeur/profile" replace />
              ) : currentUser && currentUser.role === 'CLIENT' ? (
                <Navigate to="/client/profile" replace />
              ) : (
                <Navigate to="/login" replace />
              )}
            </ProtectedRoute>
          }
        />

        {/* Access Denied Route - Removed */}

        {/* Catch-all Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    );
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <LanguageProvider>
          <AppContent />
        </LanguageProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;