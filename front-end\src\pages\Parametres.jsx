import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Ta<PERSON>,
  Tab,
  Card,
  CardContent,
  Button,
  Avatar,
  Divider,
  FormControlLabel,
  Switch,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  PhotoCamera,
  Lock,
  Notifications,
  Delete,
} from '@mui/icons-material';
// No need to import Sidebar as we're using AdminLayout
import { useSnackbar } from 'notistack';
import { getParametresData, updateParametresData, changePassword, deleteAccount } from '../services/parametresService';
import { useNavigate } from 'react-router-dom';

const Parametres = () => {
  const [tabValue, setTabValue] = useState(0);
  const [parametres, setParametres] = useState({
    logo: '',
    notifications: true,
    rappelsPaiement: true,
  });
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState('');
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getParametresData();
        if (response.data) {
          setParametres({
            logo: response.data.logo || '',
            notifications: response.data.notifications || true,
            rappelsPaiement: response.data.rappelsPaiement || true,
          });
          setLogoPreview(response.data.logo || '');
        }
      } catch (error) {
        enqueueSnackbar('Erreur lors du chargement des données', { variant: 'error' });
      }
    };

    fetchData();
  }, [enqueueSnackbar]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleLogoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = async () => {
        setLogoPreview(reader.result);
        // Auto-save logo
        try {
          const updatedParametres = { ...parametres, logo: '' }; // Clear logo path until saved
          const response = await updateParametresData(updatedParametres, file);
          setParametres({ ...parametres, logo: response.logo });
          setLogoFile(null); // Clear file after saving
          enqueueSnackbar('Logo enregistré avec succès', { variant: 'success' });
        } catch (error) {
          enqueueSnackbar('Erreur lors de l\'enregistrement du logo', { variant: 'error' });
          setLogoPreview(parametres.logo || ''); // Revert preview on error
          setLogoFile(null);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePreferenceChange = async (e) => {
    const { name, checked } = e.target;
    const updatedParametres = { ...parametres, [name]: checked };
    setParametres(updatedParametres);
    try {
      await updateParametresData(updatedParametres);
      enqueueSnackbar('Préférences enregistrées avec succès', { variant: 'success' });
    } catch (error) {
      enqueueSnackbar('Erreur lors de l\'enregistrement des préférences', { variant: 'error' });
    }
  };

  const handleOpenPasswordDialog = () => {
    setOpenPasswordDialog(true);
  };

  const handleClosePasswordDialog = () => {
    setOpenPasswordDialog(false);
    setCurrentPassword('');
    setNewPassword('');
  };

  const handleChangePassword = async () => {
    // Validate inputs
    if (!currentPassword || !newPassword) {
      enqueueSnackbar('Veuillez remplir tous les champs', { variant: 'error' });
      return;
    }
    if (newPassword.length < 8) {
      enqueueSnackbar('Le nouveau mot de passe doit contenir au moins 8 caractères', { variant: 'error' });
      return;
    }

    try {
      await changePassword(currentPassword, newPassword);
      enqueueSnackbar('Mot de passe changé avec succès', { variant: 'success' });
      handleClosePasswordDialog();
    } catch (error) {
      enqueueSnackbar(error.message || 'Erreur lors du changement de mot de passe', { variant: 'error' });
    }
  };

  const handleOpenDeleteDialog = () => {
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleDeleteAccount = async () => {
    try {
      await deleteAccount();
      enqueueSnackbar('Compte supprimé avec succès', { variant: 'success' });
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      navigate('/login');
    } catch (error) {
      enqueueSnackbar(error.message || 'Erreur lors de la suppression du compte', { variant: 'error' });
    }
  };

  return (
    <>
      {/* AdminLayout is already wrapping this component */}
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          Paramètres
        </Typography>

        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{ mb: 3 }}
        >
          <Tab label="Sécurité" icon={<Lock />} />
          <Tab label="Préférences" icon={<Notifications />} />
        </Tabs>

        {tabValue === 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sécurité et accès
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                <Avatar
                  src={logoPreview}
                  sx={{
                    width: 80,
                    height: 80,
                    mr: 3,
                    border: '2px solid #e2e8f0'
                  }}
                />
                <Box>
                  <input
                    accept="image/*"
                    style={{ display: 'none' }}
                    id="logo-upload"
                    type="file"
                    onChange={handleLogoChange}
                  />
                  <label htmlFor="logo-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<PhotoCamera />}
                      sx={{ mr: 2 }}
                    >
                      Changer le logo
                    </Button>
                  </label>
                  <Typography variant="caption">
                    PNG, JPG (max. 2MB)
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Mot de passe
                </Typography>
                <Button
                  variant="outlined"
                  sx={{ mr: 2 }}
                  onClick={handleOpenPasswordDialog}
                >
                  Changer le mot de passe
                </Button>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box>
                <Typography variant="subtitle1" gutterBottom sx={{ color: 'error.main' }}>
                  Zone dangereuse
                </Typography>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<Delete />}
                  onClick={handleOpenDeleteDialog}
                >
                  Supprimer le compte
                </Button>
                <Typography variant="body2" color="text.secondary">
                  Cette action est irréversible
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        {tabValue === 1 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Préférences de notification
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={parametres.notifications}
                    onChange={handlePreferenceChange}
                    name="notifications"
                  />
                }
                label="Activer les notifications par email"
                sx={{ mb: 2 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={parametres.rappelsPaiement}
                    onChange={handlePreferenceChange}
                    name="rappelsPaiement"
                  />
                }
                label="Envoyer des rappels de paiement automatiques"
              />
            </CardContent>
          </Card>
        )}

      {/* Password Change Dialog */}
      <Dialog open={openPasswordDialog} onClose={handleClosePasswordDialog}>
        <DialogTitle>Changer le mot de passe</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Mot de passe actuel"
            type="password"
            fullWidth
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Nouveau mot de passe"
            type="password"
            fullWidth
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePasswordDialog}>Annuler</Button>
          <Button onClick={handleChangePassword} variant="contained">Changer</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Account Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirmer la suppression du compte</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible et toutes vos données seront perdues.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Annuler</Button>
          <Button onClick={handleDeleteAccount} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Parametres;