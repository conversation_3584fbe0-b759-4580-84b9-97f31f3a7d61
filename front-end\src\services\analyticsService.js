import api from './api';
import { downloadCSV } from '../utils/csvExporter';

const analyticsService = {
  // Get dashboard analytics data
  getDashboardData: async (period = 'monthly', date = null) => {
    try {
      const params = { period };
      if (date) params.date = date;

      const response = await api.get('/analytics/dashboard', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error.response?.data || { error: 'Error fetching dashboard data' };
    }
  },

  // Get vendeur dashboard statistics
  getVendeurDashboardStats: async (dateRange = 'month', customRange = null) => {
    try {
      console.log('getVendeurDashboardStats called with dateRange:', dateRange, 'customRange:', customRange);

      // Normaliser le format de dateRange pour la compatibilité avec le backend
      let normalizedDateRange = dateRange;

      // Convertir les formats de SimpleDateFilter vers les formats du backend
      if (dateRange === 'daily') normalizedDateRange = 'day';
      if (dateRange === 'weekly') normalizedDateRange = 'week';
      if (dateRange === 'monthly') normalizedDateRange = 'month';
      if (dateRange === 'quarterly') normalizedDateRange = 'quarter';
      if (dateRange === 'yearly') normalizedDateRange = 'year';
      if (dateRange === 'all-time') normalizedDateRange = 'all-time';
      if (dateRange === 'thisMonth') normalizedDateRange = 'month';
      if (dateRange === 'thisQuarter') normalizedDateRange = 'quarter';
      if (dateRange === 'thisYear') normalizedDateRange = 'year';
      if (dateRange === 'allTime') normalizedDateRange = 'all-time';

      // Utiliser la période normalisée pour filtrer les données correctement
      const params = { dateRange: normalizedDateRange };

      // Add custom date range if provided
      if (customRange && customRange.startDate && customRange.endDate) {
        params.startDate = new Date(customRange.startDate).toISOString();
        params.endDate = new Date(customRange.endDate).toISOString();
      }

      console.log('Fetching vendeur dashboard with params:', params);

      const response = await api.get('/analytics/vendeur-dashboard', { params });

      // Vérifier si les données sont vides et fournir des valeurs par défaut
      const defaultData = {
        factures: 0,
        devis: 0,
        clients: 0,
        totalRevenue: 0,
        paidInvoices: 0,
        pendingInvoices: 0,
        overdueInvoices: 0,
        acceptedDevis: 0,
        salesByMonth: {
          invoices: Array(12).fill(0),
          quotes: Array(12).fill(0)
        },
        salesByWeek: {
          invoices: Array(5).fill(0),
          quotes: Array(5).fill(0)
        },
        salesByDay: {
          invoices: Array(7).fill(0),
          quotes: Array(7).fill(0)
        },
        salesByHour: {
          invoices: Array(24).fill(0),
          quotes: Array(24).fill(0)
        },
        categoryAnalysis: [0, 0, 0],
        clientDistribution: [0, 0],
        recentInvoices: [],
        recentQuotes: [],
        topClients: []
      };

      return { ...defaultData, ...response.data };
    } catch (error) {
      console.error('Error fetching vendeur dashboard stats:', error);
      // En cas d'erreur, retourner des données vides plutôt que de lancer une exception
      return {
        factures: 0,
        devis: 0,
        clients: 0,
        totalRevenue: 0,
        paidInvoices: 0,
        pendingInvoices: 0,
        overdueInvoices: 0,
        acceptedDevis: 0,
        salesByMonth: {
          invoices: Array(12).fill(0),
          quotes: Array(12).fill(0)
        },
        categoryAnalysis: [0, 0, 0],
        clientDistribution: [0, 0],
        recentInvoices: [],
        recentQuotes: [],
        topClients: []
      };
    }
  },

  // Get revenue analytics data
  getRevenueData: async (period = 'monthly', startDate = null, endDate = null) => {
    try {
      console.log('getRevenueData called with period:', period, 'startDate:', startDate, 'endDate:', endDate);

      // Normaliser le format de period pour la compatibilité avec le backend
      let normalizedPeriod = period;

      // Convertir les formats de SimpleDateFilter vers les formats du backend
      if (period === 'daily') normalizedPeriod = 'day';
      if (period === 'weekly') normalizedPeriod = 'week';
      if (period === 'monthly') normalizedPeriod = 'month';
      if (period === 'quarterly') normalizedPeriod = 'quarter';
      if (period === 'yearly') normalizedPeriod = 'year';
      if (period === 'all-time') normalizedPeriod = 'all-time';
      if (period === 'thisMonth') normalizedPeriod = 'month';
      if (period === 'thisQuarter') normalizedPeriod = 'quarter';
      if (period === 'thisYear') normalizedPeriod = 'year';
      if (period === 'allTime') normalizedPeriod = 'all-time';

      const params = { period: normalizedPeriod };
      if (startDate) params.startDate = startDate;
      if (endDate) params.endDate = endDate;

      console.log('Fetching revenue data with params:', params);

      const response = await api.get('/analytics/revenue', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      // Retourner des données vides en cas d'erreur au lieu de lancer une exception
      return {
        data: Array(12).fill().map((_, i) => {
          const month = new Date(2023, i, 1).toLocaleDateString('fr-FR', { month: 'short' });
          return { date: month, revenue: 0 };
        }),
        total: 0,
        growth: 0
      };
    }
  },

  // Get top clients data
  getTopClients: async (limit = 10, period = 'all-time') => {
    try {
      const params = { limit, period };

      const response = await api.get('/analytics/top-clients', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching top clients:', error);
      // Retourner un tableau vide en cas d'erreur
      return [];
    }
  },

  // Get top products data
  getTopProducts: async (limit = 10, period = 'all-time') => {
    try {
      const params = { limit, period };

      const response = await api.get('/analytics/top-products', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching top products:', error);
      // Retourner un tableau vide en cas d'erreur
      return [];
    }
  },

  // Get recent invoices
  getRecentInvoices: async (limit = 5, period = 'all-time') => {
    try {
      const params = { limit, period };

      const response = await api.get('/analytics/recent-invoices', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching recent invoices:', error);
      // Retourner un tableau vide en cas d'erreur
      return [];
    }
  },

  // Get payment status distribution
  getPaymentStatus: async (period = 'all-time') => {
    try {
      const params = { period };

      const response = await api.get('/analytics/payment-status', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching payment status:', error);
      // Retourner des données vides en cas d'erreur
      return [
        { name: 'Aucune donnée', value: 1, color: '#10B981' }
      ];
    }
  },

  // Get key performance indicators
  getKPI: async () => {
    try {
      const response = await api.get('/analytics/kpi');
      return response.data;
    } catch (error) {
      console.error('Error fetching KPI data:', error);
      // Retourner des données vides en cas d'erreur
      return {
        revenue: 0,
        invoices: 0,
        clients: 0,
        products: 0,
        growth: 0
      };
    }
  },

  // Admin-specific analytics functions

  // Get system-wide metrics for admin dashboard
  getSystemMetrics: async (period = 'monthly') => {
    try {
      console.log('getSystemMetrics called with period:', period);

      // Normaliser le format de period pour la compatibilité avec le backend
      let normalizedPeriod = period;

      // Convertir les formats de SimpleDateFilter vers les formats du backend
      if (period === 'daily') normalizedPeriod = 'day';
      if (period === 'weekly') normalizedPeriod = 'week';
      if (period === 'monthly') normalizedPeriod = 'month';
      if (period === 'quarterly') normalizedPeriod = 'quarter';
      if (period === 'yearly') normalizedPeriod = 'year';
      if (period === 'all-time') normalizedPeriod = 'all-time';
      if (period === 'thisMonth') normalizedPeriod = 'month';
      if (period === 'thisQuarter') normalizedPeriod = 'quarter';
      if (period === 'thisYear') normalizedPeriod = 'year';
      if (period === 'allTime') normalizedPeriod = 'all-time';

      const params = { period: normalizedPeriod };
      console.log('Fetching system metrics with params:', params);

      const response = await api.get('/analytics/system-metrics', { params });

      // Valeurs par défaut pour les métriques système
      const defaultMetrics = {
        totalRevenue: 0,
        revenueGrowth: 0,
        totalDocuments: 0,
        documentGrowth: 0,
        invoices: 0,
        quotes: 0,
        newClients: 0,
        newUsers: 0,
        period: normalizedPeriod,
        dateRange: {
          start: new Date(),
          end: new Date()
        }
      };

      return { ...defaultMetrics, ...response.data };
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      // Retourner des valeurs par défaut en cas d'erreur
      return {
        totalRevenue: 0,
        revenueGrowth: 0,
        totalDocuments: 0,
        documentGrowth: 0,
        invoices: 0,
        quotes: 0,
        newClients: 0,
        newUsers: 0,
        period: period,
        dateRange: {
          start: new Date(),
          end: new Date()
        }
      };
    }
  },

  // Get system health information
  getSystemHealth: async () => {
    try {
      const response = await api.get('/analytics/system-health');

      // Valeurs par défaut pour la santé du système
      const defaultHealth = {
        status: 'Normal',
        uptime: '0%',
        responseTime: '0ms',
        errorRate: '0%',
        lastIncident: null,
        storage: {
          total: 500,
          used: 0,
          free: 500
        },
        documents: {
          invoices: 0,
          quotes: 0,
          clients: 0,
          users: 0,
          products: 0,
          total: 0
        }
      };

      return { ...defaultHealth, ...response.data };
    } catch (error) {
      console.error('Error fetching system health:', error);
      // Retourner des valeurs par défaut en cas d'erreur
      return {
        status: 'Erreur',
        uptime: '0%',
        responseTime: '0ms',
        errorRate: '100%',
        lastIncident: new Date(),
        storage: {
          total: 500,
          used: 0,
          free: 500
        },
        documents: {
          invoices: 0,
          quotes: 0,
          clients: 0,
          users: 0,
          products: 0,
          total: 0
        }
      };
    }
  },

  // Get document statistics
  getDocumentStats: async (period = 'monthly') => {
    try {
      const params = { period };
      const response = await api.get('/analytics/document-stats', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching document statistics:', error);
      throw error.response?.data || { error: 'Error fetching document statistics' };
    }
  },

  // Export data for Power BI
  exportInvoiceData: async (startDate = null, endDate = null) => {
    try {
      const params = {};
      if (startDate) params.startDate = startDate;
      if (endDate) params.endDate = endDate;

      const response = await api.get('/analytics/export/invoices', { params });
      return response.data;
    } catch (error) {
      console.error('Error exporting invoice data:', error);
      // Return empty array instead of throwing error
      if (error.response?.status === 403 || error.response?.status === 500) {
        return [];
      }
      return [];
    }
  },

  exportClientData: async () => {
    try {
      const response = await api.get('/analytics/export/clients');
      return response.data;
    } catch (error) {
      console.error('Error exporting client data:', error);
      // Return empty array instead of throwing error
      if (error.response?.status === 403 || error.response?.status === 500) {
        return [];
      }
      return [];
    }
  },

  exportProductData: async () => {
    try {
      const response = await api.get('/analytics/export/products');
      return response.data;
    } catch (error) {
      console.error('Error exporting product data:', error);
      // Return empty array instead of throwing error
      if (error.response?.status === 403 || error.response?.status === 500) {
        return [];
      }
      return [];
    }
  },

  // Admin-specific export functions

  exportSystemData: async (period = 'monthly') => {
    try {
      const params = { period, format: 'csv' };
      const response = await api.get('/analytics/export/system', { params });

      // Download the CSV file
      downloadCSV(response.data, `system_metrics_${period}_${new Date().toISOString().split('T')[0]}.csv`);
      return true;
    } catch (error) {
      console.error('Error exporting system data:', error);
      throw error.response?.data || { error: 'Error exporting system data' };
    }
  },

  exportRevenueData: async (period = 'monthly') => {
    try {
      const params = { period, format: 'csv' };
      const response = await api.get('/analytics/export/revenue', { params });

      // Download the CSV file
      downloadCSV(response.data, `revenue_data_${period}_${new Date().toISOString().split('T')[0]}.csv`);
      return true;
    } catch (error) {
      console.error('Error exporting revenue data:', error);
      throw error.response?.data || { error: 'Error exporting revenue data' };
    }
  },
};

export default analyticsService;
