{"version": 3, "file": "server_selection.js", "sourceRoot": "", "sources": ["../../src/sdam/server_selection.ts"], "names": [], "mappings": ";;;AAuBA,wDAUC;AAMD,gDAYC;AAMD,0EAgBC;AAgMD,oEAoEC;AA7UD,oCAA8E;AAC9E,wDAAoD;AACpD,qCAAoD;AAIpD,0BAA0B;AAC1B,MAAM,iBAAiB,GAAG,KAAK,CAAC;AAChC,MAAM,8BAA8B,GAAG,EAAE,CAAC;AAE1C,iDAAiD;AACpC,QAAA,gCAAgC,GAAG,EAAE,CAAC;AASnD;;GAEG;AACH,SAAgB,sBAAsB;IACpC,OAAO,SAAS,cAAc,CAC5B,mBAAwC,EACxC,OAA4B;QAE5B,OAAO,oBAAoB,CACzB,mBAAmB,EACnB,OAAO,CAAC,MAAM,CAAC,CAAC,CAAoB,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CACvD,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAgB,kBAAkB,CAAC,WAA+B;IAChE,OAAO,SAAS,kBAAkB,CAChC,mBAAwC,EACxC,OAA4B;QAE5B,IAAI,CAAC,WAAW;YAAE,OAAO,EAAE,CAAC;QAC5B,+DAA+D;QAC/D,2BAA2B;QAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACzB,OAAO,EAAE,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC,IAAI,KAAK,mBAAU,CAAC,OAAO,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAgB,+BAA+B,CAC7C,WAAoB,EACpB,cAA+B;IAE/B,2DAA2D;IAC3D,8BAA8B;IAC9B,8CAA8C;IAC9C,oDAAoD;IACpD,IACE,CAAC,cAAc;QACf,CAAC,WAAW;QACZ,CAAC,WAAW,IAAI,WAAW,GAAG,wCAAgC,CAAC,EAC/D,CAAC;QACD,OAAO,4BAA4B,CAAC,gCAAc,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,4BAA4B,CAAC,cAAc,CAAC,CAAC;AACtD,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,mBAAmB,CAC1B,cAA8B,EAC9B,mBAAwC,EACxC,OAA4B;IAE5B,IAAI,cAAc,CAAC,mBAAmB,IAAI,IAAI,IAAI,cAAc,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;QACzF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,YAAY,GAAG,cAAc,CAAC,mBAAmB,CAAC;IACxD,MAAM,oBAAoB,GACxB,CAAC,mBAAmB,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,GAAG,IAAI,CAAC;IACxE,IAAI,YAAY,GAAG,oBAAoB,EAAE,CAAC;QACxC,MAAM,IAAI,iCAAyB,CACjC,iDAAiD,oBAAoB,UAAU,CAChF,CAAC;IACJ,CAAC;IAED,IAAI,YAAY,GAAG,8BAA8B,EAAE,CAAC;QAClD,MAAM,IAAI,iCAAyB,CACjC,iDAAiD,8BAA8B,UAAU,CAC1F,CAAC;IACJ,CAAC;IAED,IAAI,mBAAmB,CAAC,IAAI,KAAK,qBAAY,CAAC,qBAAqB,EAAE,CAAC;QACpE,MAAM,OAAO,GAAsB,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CACxF,aAAa,CACd,CAAC,CAAC,CAAC,CAAC;QAEL,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAA2B,EAAE,MAAyB,EAAE,EAAE;YAC/E,MAAM,WAAW,GACf,MAAM,CAAC,cAAc;gBACrB,MAAM,CAAC,aAAa;gBACpB,CAAC,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC;gBAChD,mBAAmB,CAAC,oBAAoB,CAAC;YAE3C,MAAM,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC;YACrC,MAAM,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,IAAI,CAAC,CAAC;YACpE,IAAI,SAAS,IAAI,mBAAmB,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED,IAAI,mBAAmB,CAAC,IAAI,KAAK,qBAAY,CAAC,mBAAmB,EAAE,CAAC;QAClE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAsB,EAAE,CAAoB,EAAE,EAAE,CAC3E,CAAC,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAC9C,CAAC;QAEF,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAA2B,EAAE,MAAyB,EAAE,EAAE;YAC/E,MAAM,WAAW,GACf,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;YAEvF,MAAM,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC;YACrC,MAAM,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,IAAI,CAAC,CAAC;YACpE,IAAI,SAAS,IAAI,mBAAmB,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,SAAS,WAAW,CAAC,MAAc,EAAE,UAAkB;IACrD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAS,aAAa,CACpB,cAA8B,EAC9B,OAA4B;IAE5B,IACE,cAAc,CAAC,IAAI,IAAI,IAAI;QAC3B,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EACxE,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACpD,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,qBAAqB,GAAG,OAAO,CAAC,MAAM,CAC1C,CAAC,OAA4B,EAAE,MAAyB,EAAE,EAAE;YAC1D,IAAI,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;gBAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAO,OAAO,CAAC;QACjB,CAAC,EACD,EAAE,CACH,CAAC;QAEF,IAAI,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACjC,OAAO,qBAAqB,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,oBAAoB,CAC3B,mBAAwC,EACxC,OAA4B;IAE5B,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CACxB,CAAC,GAAW,EAAE,MAAyB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,EAC/E,QAAQ,CACT,CAAC;IAEF,MAAM,IAAI,GAAG,GAAG,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;IACxD,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAA2B,EAAE,MAAyB,EAAE,EAAE;QAC/E,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,IAAI,MAAM,CAAC,aAAa,IAAI,GAAG;YAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrF,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,UAAU;AACV,SAAS,aAAa,CAAC,MAAyB;IAC9C,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,SAAS,CAAC;AAC9C,CAAC;AAED,SAAS,eAAe,CAAC,MAAyB;IAChD,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW,CAAC;AAChD,CAAC;AAED,SAAS,aAAa,CAAC,MAAyB;IAC9C,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,SAAS,CAAC;AACxF,CAAC;AAED,SAAS,WAAW,CAAC,MAAyB;IAC5C,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,OAAO,CAAC;AAC5C,CAAC;AAED,SAAS,kBAAkB,CAAC,MAAyB;IACnD,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,YAAY,CAAC;AACjD,CAAC;AAED;;;;GAIG;AACH,SAAgB,4BAA4B,CAAC,cAA8B;IACzE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;QAC9B,MAAM,IAAI,iCAAyB,CAAC,mCAAmC,CAAC,CAAC;IAC3E,CAAC;IAED,OAAO,SAAS,qBAAqB,CACnC,mBAAwC,EACxC,OAA4B,EAC5B,gBAAqC,EAAE;QAEvC,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;QAChE,IACE,iBAAiB;YACjB,cAAc,CAAC,cAAc;YAC7B,cAAc,CAAC,cAAc,GAAG,iBAAiB,EACjD,CAAC;YACD,MAAM,IAAI,+BAAuB,CAC/B,yBAAyB,cAAc,CAAC,cAAc,0BAA0B,iBAAiB,GAAG,CACrG,CAAC;QACJ,CAAC;QAED,IAAI,mBAAmB,CAAC,IAAI,KAAK,qBAAY,CAAC,YAAY,EAAE,CAAC;YAC3D,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,mBAAmB,CAAC,IAAI,KAAK,qBAAY,CAAC,OAAO,EAAE,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,mBAAmB,CAAC,IAAI,KAAK,qBAAY,CAAC,MAAM,EAAE,CAAC;YACrD,OAAO,oBAAoB,CAAC,mBAAmB,EAAE,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,mBAAmB,CAAC,IAAI,KAAK,qBAAY,CAAC,OAAO,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBACvC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC;YAClE,OAAO,oBAAoB,CAAC,mBAAmB,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QACnF,CAAC;QAED,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;QACjC,IAAI,IAAI,KAAK,gCAAc,CAAC,OAAO,EAAE,CAAC;YACpC,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,KAAK,gCAAc,CAAC,iBAAiB,EAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC7C,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClB,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,KAAK,gCAAc,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC;QACjF,MAAM,eAAe,GAAG,oBAAoB,CAC1C,mBAAmB,EACnB,aAAa,CACX,cAAc,EACd,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACjF,CACF,CAAC;QAEF,IAAI,IAAI,KAAK,gCAAc,CAAC,mBAAmB,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChF,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC"}