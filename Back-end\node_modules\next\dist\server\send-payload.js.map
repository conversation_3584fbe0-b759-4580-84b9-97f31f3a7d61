{"version": 3, "sources": ["../../src/server/send-payload.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type RenderResult from './render-result'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { isResSent } from '../shared/lib/utils'\nimport { generateETag } from './lib/etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { RSC_CONTENT_TYPE_HEADER } from '../client/components/app-router-headers'\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  result,\n  type,\n  generateEtags,\n  poweredByHeader,\n  cacheControl,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  result: RenderResult\n  type: 'html' | 'json' | 'rsc'\n  generateEtags: boolean\n  poweredByHeader: boolean\n  cacheControl: CacheControl | undefined\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  // If cache control is already set on the response we don't\n  // override it to allow users to customize it via next.config\n  if (cacheControl && !res.getHeader('Cache-Control')) {\n    res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n  }\n\n  const payload = result.isDynamic ? null : result.toUnchunkedString()\n\n  if (generateEtags && payload !== null) {\n    const etag = generateETag(payload)\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      result.contentType\n        ? result.contentType\n        : type === 'rsc'\n          ? RSC_CONTENT_TYPE_HEADER\n          : type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n    )\n  }\n\n  if (payload) {\n    res.setHeader('Content-Length', Buffer.byteLength(payload))\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n    return\n  }\n\n  if (payload !== null) {\n    res.end(payload)\n    return\n  }\n\n  // Pipe the render result to the response after we get a writer for it.\n  await result.pipeToNodeResponse(res)\n}\n"], "names": ["sendEtagResponse", "sendRenderResult", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "fresh", "headers", "statusCode", "end", "result", "type", "generateEtags", "poweredByHeader", "cacheControl", "isResSent", "<PERSON><PERSON><PERSON><PERSON>", "getCacheControlHeader", "payload", "isDynamic", "toUnchunkedString", "generateETag", "contentType", "RSC_CONTENT_TYPE_HEADER", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;;;;;;;;;;;;IAUgBA,gBAAgB;eAAhBA;;IAwBMC,gBAAgB;eAAhBA;;;uBA9BI;sBACG;8DACX;8BACoB;kCACE;;;;;;AAEjC,SAASD,iBACdE,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIE,IAAAA,cAAK,EAACJ,IAAIK,OAAO,EAAE;QAAEH;IAAK,IAAI;QAChCD,IAAIK,UAAU,GAAG;QACjBL,IAAIM,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeR,iBAAiB,EACrCC,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,YAAY,EASb;IACC,IAAIC,IAAAA,gBAAS,EAACZ,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAIS,gBAAgB,CAACX,IAAIa,SAAS,CAAC,kBAAkB;QACnDb,IAAIE,SAAS,CAAC,iBAAiBY,IAAAA,mCAAqB,EAACH;IACvD;IAEA,MAAMI,UAAUR,OAAOS,SAAS,GAAG,OAAOT,OAAOU,iBAAiB;IAElE,IAAIR,iBAAiBM,YAAY,MAAM;QACrC,MAAMd,OAAOiB,IAAAA,kBAAY,EAACH;QAC1B,IAAIlB,iBAAiBE,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIa,SAAS,CAAC,iBAAiB;QAClCb,IAAIE,SAAS,CACX,gBACAK,OAAOY,WAAW,GACdZ,OAAOY,WAAW,GAClBX,SAAS,QACPY,yCAAuB,GACvBZ,SAAS,SACP,qBACA;IAEZ;IAEA,IAAIO,SAAS;QACXf,IAAIE,SAAS,CAAC,kBAAkBmB,OAAOC,UAAU,CAACP;IACpD;IAEA,IAAIhB,IAAIwB,MAAM,KAAK,QAAQ;QACzBvB,IAAIM,GAAG,CAAC;QACR;IACF;IAEA,IAAIS,YAAY,MAAM;QACpBf,IAAIM,GAAG,CAACS;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMR,OAAOiB,kBAAkB,CAACxB;AAClC"}