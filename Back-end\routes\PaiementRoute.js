const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const Paiement = require('../models/PaiementModel');
const Facture = require('../models/FactureModel');
const { updateStockFromInvoice } = require('../utils/stockUtils');

// Clé secrète pour JWT (devrait être dans les variables d'environnement en production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token and extract userId
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Middleware pour filtrer les paiements par entreprise ou client
const filterByEntreprise = async (req, res, next) => {
  try {
    // Récupérer l'ID de l'utilisateur et son rôle
    const User = require('../models/UserModel');
    const Client = require('../models/ClientModel');
    const mongoose = require('mongoose');

    console.log('Filtering payments for user:', req.userId, 'with role:', req.userRole);

    if (req.userRole === 'ENTREPRISE') {
      const user = await User.findById(req.userId);
      console.log('User found:', user);

      // Trouver tous les vendeurs associés à cette entreprise
      const vendeurs = await User.find({
        role: 'VENDEUR',
        entreprises: { $in: [req.userId] }
      });
      console.log(`Found ${vendeurs.length} vendeurs associated with this ENTREPRISE user`);

      // Stocker les IDs des vendeurs pour une utilisation ultérieure
      const vendeurIds = vendeurs.map(v => v._id);
      req.vendeurIds = vendeurIds;

      if (user && user.entrepriseId) {
        req.entrepriseId = user.entrepriseId;
        console.log('Using entrepriseId from user:', req.entrepriseId);
      } else {
        // Si l'entrepriseId n'est pas défini, essayer de trouver le client par email
        const client = await Client.findOne({ email: user.email });
        if (client) {
          req.entrepriseId = client._id;
          console.log('Found client by email:', client._id);

          // Mettre à jour l'utilisateur avec l'ID du client
          await User.findByIdAndUpdate(user._id, { entrepriseId: client._id });
          console.log('Updated user with entrepriseId:', client._id);
        } else {
          console.log('No client found for user with email:', user.email);
        }
      }
    } else if (req.userRole === 'CLIENT') {
      // Pour les clients, stocker leur ID pour filtrer les paiements
      const user = await User.findById(req.userId);
      if (user) {
        req.clientId = user._id;
        console.log('Client user ID:', req.clientId);
      }
    }
    next();
  } catch (error) {
    console.error('Erreur lors du filtrage par entreprise ou client:', error);
    next();
  }
};

// Récupérer tous les paiements
router.get('/', verifyToken, filterByEntreprise, async (req, res) => {
    try {
        let query = {};
        const mongoose = require('mongoose');

        console.log("GET /paiement - User role:", req.userRole);

        if (req.userRole === 'ENTREPRISE') {
            console.log("Filtering payments for enterprise user:", req.userId);

            // Pour une entreprise, nous voulons:
            // 1. Les paiements des factures créées par l'entreprise elle-même
            // 2. Les paiements des factures créées par ses vendeurs

            // Créer une condition OR pour les factures
            let factureConditions = [];

            // Ajouter la condition pour les factures créées par l'entreprise
            factureConditions.push({ vendeurId: new mongoose.Types.ObjectId(req.userId) });

            // Ajouter la condition pour les factures créées par les vendeurs de l'entreprise
            if (req.vendeurIds && req.vendeurIds.length > 0) {
                const vendeurObjectIds = req.vendeurIds.map(id =>
                    new mongoose.Types.ObjectId(id)
                );
                factureConditions.push({ vendeurId: { $in: vendeurObjectIds } });
            }

            // Récupérer toutes les factures qui correspondent à ces conditions
            const factures = await Facture.find({ $or: factureConditions });
            console.log(`Found ${factures.length} invoices for enterprise and its vendeurs`);

            if (factures.length > 0) {
                const factureIds = factures.map(facture => facture._id);
                query.factureId = { $in: factureIds };
            } else {
                // Si aucune facture n'est trouvée, renvoyer un tableau vide
                return res.status(200).json([]);
            }
        } else if (req.userRole === 'CLIENT') {
            console.log("Filtering payments for client user:", req.userId);

            // Pour un client, nous voulons les paiements des factures où il est le client
            const factures = await Facture.find({ clientId: new mongoose.Types.ObjectId(req.userId) });
            console.log(`Found ${factures.length} invoices for client`);

            if (factures.length > 0) {
                const factureIds = factures.map(facture => facture._id);
                query.factureId = { $in: factureIds };
            } else {
                // Si aucune facture n'est trouvée, renvoyer un tableau vide
                return res.status(200).json([]);
            }
        } else if (req.userRole === 'VENDEUR') {
            console.log("Filtering payments for vendeur user:", req.userId);

            // Pour un vendeur, nous voulons les paiements des factures qu'il a créées
            const factures = await Facture.find({ vendeurId: new mongoose.Types.ObjectId(req.userId) });
            console.log(`Found ${factures.length} invoices for vendeur`);

            if (factures.length > 0) {
                const factureIds = factures.map(facture => facture._id);
                query.factureId = { $in: factureIds };
            } else {
                // Si aucune facture n'est trouvée, renvoyer un tableau vide
                return res.status(200).json([]);
            }
        }
        // Pour ADMIN, aucun filtre n'est appliqué (tous les paiements sont retournés)

        console.log("Final query for payments:", query);
        const paiements = await Paiement.find(query)
            .populate({
                path: 'factureId',
                populate: {
                    path: 'clientId',
                    model: 'Client'
                }
            })
            .populate({
                path: 'factureId',
                populate: {
                    path: 'vendeurId',
                    model: 'users',
                    select: 'nom email'
                }
            });

        console.log(`Found ${paiements.length} payments`);
        res.status(200).json(paiements);
    } catch (error) {
        console.error('Erreur lors de la récupération des paiements:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des paiements' });
    }
});

// Récupérer un paiement par ID
router.get('/:id', verifyToken, async (req, res) => {
    try {
        const paiement = await Paiement.findById(req.params.id).populate('factureId');
        if (!paiement) {
            return res.status(404).json({ error: 'Paiement non trouvé' });
        }
        res.status(200).json(paiement);
    } catch (error) {
        console.error('Erreur lors de la récupération du paiement:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération du paiement' });
    }
});

// Récupérer les paiements d'une facture
router.get('/facture/:factureId', verifyToken, async (req, res) => {
    try {
        const paiements = await Paiement.find({ factureId: req.params.factureId }).populate('factureId');
        res.status(200).json(paiements);
    } catch (error) {
        console.error('Erreur lors de la récupération des paiements de la facture:', error);
        res.status(500).json({ error: 'Erreur lors de la récupération des paiements de la facture' });
    }
});

// Créer un nouveau paiement
router.post('/', verifyToken, async (req, res) => {
    try {
        const paiement = new Paiement(req.body);
        await paiement.save();

        // Mettre à jour le statut de la facture si nécessaire
        if (req.body.factureId) {
            const facture = await Facture.findById(req.body.factureId);
            if (facture) {
                facture.statut = 'PAID';
                await facture.save();

                // Mettre à jour le stock des produits
                const stockResult = await updateStockFromInvoice(facture._id);
                console.log('Résultat de la mise à jour du stock:', stockResult);

                // Si des produits sont en stock bas, on les affiche dans la console
                if (stockResult.lowStockProducts && stockResult.lowStockProducts.length > 0) {
                    console.log('Produits en stock bas:', stockResult.lowStockProducts);
                }
            }
        }

        res.status(201).json(paiement);
    } catch (error) {
        console.error('Erreur lors de la création du paiement:', error);
        res.status(500).json({ error: 'Erreur lors de la création du paiement' });
    }
});

// Payer une facture
router.post('/payer/:factureId', verifyToken, filterByEntreprise, async (req, res) => {
    try {
        console.log(`POST /paiement/payer/${req.params.factureId} - User role: ${req.userRole}, User ID: ${req.userId}`);
        console.log('Request body:', req.body);

        const facture = await Facture.findById(req.params.factureId)
            .populate('clientId')
            .populate('vendeurId');

        if (!facture) {
            console.log(`Facture with ID ${req.params.factureId} not found`);
            return res.status(404).json({ error: 'Facture non trouvée' });
        }

        console.log('Found invoice:', {
            id: facture._id,
            numero: facture.numero,
            clientId: facture.clientId?._id || facture.clientId,
            vendeurId: facture.vendeurId?._id || facture.vendeurId,
            statut: facture.statut,
            total: facture.total
        });

        // Vérifier si la facture est déjà payée
        if (facture.statut === 'PAID') {
            console.log(`Invoice ${facture._id} is already paid`);
            return res.status(400).json({ error: 'Cette facture a déjà été payée' });
        }

        // Créer le paiement avec toutes les informations nécessaires
        const paiement = new Paiement({
            factureId: facture._id,
            montant: req.body.montant || facture.total,
            modePaiement: req.body.modePaiement || 'BANK_TRANSFER',
            datePaiement: req.body.datePaiement || new Date(),
            reference: req.body.reference || `REF-${Date.now()}`,
            notes: req.body.notes || '',
            statut: 'COMPLETED',
            createdBy: req.userId
        });

        console.log('Creating payment:', paiement);
        const savedPaiement = await paiement.save();
        console.log('Payment saved with ID:', savedPaiement._id);

        // Mettre à jour le statut de la facture
        facture.statut = 'PAID';
        await facture.save();
        console.log(`Invoice ${facture._id} status updated to PAID`);

        // Mettre à jour le stock des produits
        const stockResult = await updateStockFromInvoice(facture._id);
        console.log('Résultat de la mise à jour du stock:', stockResult);

        // Préparer la réponse avec les informations de stock
        const response = {
            message: 'Paiement enregistré avec succès',
            paiement: savedPaiement,
            facture,
            stockUpdate: {
                success: stockResult.success,
                message: stockResult.message
            }
        };

        // Ajouter les informations sur les produits mis à jour si disponibles
        if (stockResult.updatedProducts && stockResult.updatedProducts.length > 0) {
            response.stockUpdate.updatedProducts = stockResult.updatedProducts;
        }

        // Ajouter les informations sur les produits en stock bas si disponibles
        if (stockResult.lowStockProducts && stockResult.lowStockProducts.length > 0) {
            response.stockUpdate.lowStockProducts = stockResult.lowStockProducts;
            console.log('Produits en stock bas:', stockResult.lowStockProducts);
        }

        res.status(201).json(response);
    } catch (error) {
        console.error('Erreur lors du paiement de la facture:', error);
        res.status(500).json({ error: 'Erreur lors du paiement de la facture', details: error.message });
    }
});

// Mettre à jour un paiement
router.put('/:id', verifyToken, async (req, res) => {
    try {
        // Accès autorisé pour tous les utilisateurs authentifiés
        // Restriction d'accès désactivée

        const paiement = await Paiement.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!paiement) {
            return res.status(404).json({ error: 'Paiement non trouvé' });
        }
        res.status(200).json(paiement);
    } catch (error) {
        console.error('Erreur lors de la mise à jour du paiement:', error);
        res.status(500).json({ error: 'Erreur lors de la mise à jour du paiement' });
    }
});

// Supprimer un paiement
router.delete('/:id', verifyToken, async (req, res) => {
    try {
        // Accès autorisé pour tous les utilisateurs authentifiés
        // Restriction d'accès désactivée

        const paiement = await Paiement.findByIdAndDelete(req.params.id);
        if (!paiement) {
            return res.status(404).json({ error: 'Paiement non trouvé' });
        }

        // Mettre à jour le statut de la facture si nécessaire
        if (paiement.factureId) {
            const facture = await Facture.findById(paiement.factureId);
            if (facture && facture.statut === 'PAID') {
                facture.statut = 'SENT';
                await facture.save();

                // Si la facture était payée et qu'on supprime le paiement, on réaugmente le stock
                if (facture.stockAjuste) {
                    const stockResult = await updateStockFromInvoice(facture._id, true); // true = annulation
                    console.log('Résultat de la réaugmentation du stock:', stockResult);
                }
            }
        }

        res.status(204).send();
    } catch (error) {
        console.error('Erreur lors de la suppression du paiement:', error);
        res.status(500).json({ error: 'Erreur lors de la suppression du paiement' });
    }
});

module.exports = router;