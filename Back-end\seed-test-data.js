const mongoose = require('mongoose');
require('./connectdb.js');

const User = require('./models/UserModel');
const Client = require('./models/ClientModel');
const Produit = require('./models/ProduitModel');
const Livreur = require('./models/LivreurModel');
const bcrypt = require('bcrypt');

async function seedTestData() {
    try {
        console.log('🌱 Création des données de test...\n');

        // Créer un responsable d'entreprise
        let responsable = await User.findOne({ email: '<EMAIL>' });
        if (!responsable) {
            const hashedPassword = await bcrypt.hash('123456', 10);
            responsable = new User({
                nom: 'Dupont',
                prenom: 'Jean',
                email: '<EMAIL>',
                motDePasse: hashedPassword,
                role: 'RESPONSABLE',
                nomEntreprise: 'Entreprise Test',
                adresseEntreprise: '123 Rue de Test, Tunis',
                telephoneEntreprise: '71234567',
                numeroFiscal: 'TEST001'
            });
            await responsable.save();
            console.log('✅ Responsable créé');
        }

        // Créer un vendeur
        let vendeur = await User.findOne({ email: '<EMAIL>' });
        if (!vendeur) {
            const hashedPassword = await bcrypt.hash('123456', 10);
            vendeur = new User({
                nom: 'Martin',
                prenom: 'Pierre',
                email: '<EMAIL>',
                motDePasse: hashedPassword,
                role: 'VENDEUR',
                responsables: [responsable._id]
            });
            await vendeur.save();
            console.log('✅ Vendeur créé');
        }

        // Créer des clients
        const clientsData = [
            { nom: 'Client Test 1', email: '<EMAIL>', telephone: '20123456', adresse: 'Adresse 1, Tunis' },
            { nom: 'Client Test 2', email: '<EMAIL>', telephone: '20123457', adresse: 'Adresse 2, Sfax' },
            { nom: 'Client Test 3', email: '<EMAIL>', telephone: '20123458', adresse: 'Adresse 3, Sousse' }
        ];

        for (const clientData of clientsData) {
            const existingClient = await Client.findOne({ email: clientData.email });
            if (!existingClient) {
                const client = new Client({
                    ...clientData,
                    vendeurId: vendeur._id,
                    responsableId: responsable._id
                });
                await client.save();
                console.log(`✅ Client créé: ${clientData.nom}`);
            }
        }

        // Créer des produits
        const produitsData = [
            { nom: 'Produit A', prix: 25.50, quantiteStock: 100, description: 'Description du produit A' },
            { nom: 'Produit B', prix: 45.00, quantiteStock: 50, description: 'Description du produit B' },
            { nom: 'Produit C', prix: 15.75, quantiteStock: 200, description: 'Description du produit C' },
            { nom: 'Produit D', prix: 89.99, quantiteStock: 30, description: 'Description du produit D' }
        ];

        for (const produitData of produitsData) {
            const existingProduit = await Produit.findOne({ nom: produitData.nom });
            if (!existingProduit) {
                const produit = new Produit({
                    ...produitData,
                    vendeurId: vendeur._id,
                    responsableId: responsable._id
                });
                await produit.save();
                console.log(`✅ Produit créé: ${produitData.nom}`);
            }
        }

        // Créer des livreurs
        const livreursData = [
            { 
                nom: 'Livreur', 
                prenom: 'Ahmed', 
                telephone: '98123456', 
                email: '<EMAIL>',
                statut: 'ACTIF',
                disponible: true,
                vehicule: { type: 'Moto', marque: 'Honda' }
            },
            { 
                nom: 'Livreur', 
                prenom: 'Mohamed', 
                telephone: '98123457', 
                email: '<EMAIL>',
                statut: 'ACTIF',
                disponible: true,
                vehicule: { type: 'Voiture', marque: 'Peugeot' }
            },
            { 
                nom: 'Livreur', 
                prenom: 'Ali', 
                telephone: '98123458', 
                email: '<EMAIL>',
                statut: 'ACTIF',
                disponible: false,
                vehicule: { type: 'Camionnette', marque: 'Renault' }
            }
        ];

        for (const livreurData of livreursData) {
            const existingLivreur = await Livreur.findOne({ telephone: livreurData.telephone });
            if (!existingLivreur) {
                const livreur = new Livreur({
                    ...livreurData,
                    responsableId: responsable._id
                });
                await livreur.save();
                console.log(`✅ Livreur créé: ${livreurData.prenom} ${livreurData.nom}`);
            }
        }

        console.log('\n🎉 Données de test créées avec succès!');
        console.log('\nComptes de test:');
        console.log('- Responsable: <EMAIL> / 123456');
        console.log('- Vendeur: <EMAIL> / 123456');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Erreur lors de la création des données de test:', error);
        process.exit(1);
    }
}

seedTestData();
