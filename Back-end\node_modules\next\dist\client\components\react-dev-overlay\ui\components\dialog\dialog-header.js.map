{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/dialog-header.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type DialogHeaderProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nconst DialogHeader: React.FC<DialogHeaderProps> = function DialogHeader({\n  children,\n  className,\n}) {\n  return (\n    <div data-nextjs-dialog-header className={className}>\n      {children}\n    </div>\n  )\n}\n\nexport { DialogHeader }\n"], "names": ["DialogHeader", "children", "className", "div", "data-nextjs-dialog-header"], "mappings": ";;;;+BAkBSA;;;eAAAA;;;;;iEAlBc;AAOvB,MAAMA,eAA4C,SAASA,aAAa,KAGvE;IAHuE,IAAA,EACtEC,QAAQ,EACRC,SAAS,EACV,GAHuE;IAItE,qBACE,qBAACC;QAAIC,2BAAyB;QAACF,WAAWA;kBACvCD;;AAGP"}