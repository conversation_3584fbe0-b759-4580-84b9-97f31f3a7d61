import React from 'react';
import { Box } from '@mui/material';
import AdminSidebar from '../components/AdminSidebar';
import AdminTopBar from '../components/AdminTopBar';
import { useAuth } from '../contexts/AuthContext';

const AdminLayout = ({ children, darkMode, toggleDarkMode }) => {
  const { currentUser } = useAuth();

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: (theme) => theme.palette.background.default
    }}>
      <AdminSidebar />
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <AdminTopBar
          title="Dashboard Admin"
          darkMode={darkMode}
          toggleDarkMode={toggleDarkMode}
          user={currentUser}
        />
        <Box sx={{
          flexGrow: 1,
          p: 3,
          overflow: 'auto',
          backgroundColor: (theme) => theme.palette.background.default
        }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default AdminLayout;
