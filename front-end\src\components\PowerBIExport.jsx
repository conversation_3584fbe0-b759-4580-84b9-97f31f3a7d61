import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  Snackbar,
  CircularProgress,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { fr } from 'date-fns/locale';
import { 
  FileDownload as FileDownloadIcon,
  TableChart as TableChartIcon,
  People as PeopleIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import analyticsService from '../services/analyticsService';
import { exportToCSV } from '../utils/csvExporter';

const PowerBIExport = () => {
  const [dataType, setDataType] = useState('invoices');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleExport = async () => {
    try {
      setLoading(true);

      let data = [];
      let headers = [];
      let fileName = '';

      switch (dataType) {
        case 'invoices':
          data = await analyticsService.exportInvoiceData(
            startDate ? startDate.toISOString() : null,
            endDate ? endDate.toISOString() : null
          );
          headers = [
            { title: 'ID', key: 'id' },
            { title: 'Numéro', key: 'numero' },
            { title: 'Date d\'émission', key: 'dateEmission' },
            { title: 'Client', key: 'client' },
            { title: 'Statut', key: 'statut' },
            { title: 'Total HT', key: 'total' },
            { title: 'Taux TVA', key: 'tauxTVA' },
            { title: 'Total TTC', key: 'totalTTC' },
            { title: 'Notes', key: 'notes' },
            { title: 'Date de création', key: 'createdAt' },
          ];
          fileName = 'factures_export.csv';
          break;

        case 'clients':
          data = await analyticsService.exportClientData();
          headers = [
            { title: 'ID', key: 'id' },
            { title: 'Nom', key: 'nom' },
            { title: 'Email', key: 'email' },
            { title: 'Téléphone', key: 'telephone' },
            { title: 'Adresse', key: 'adresse' },
            { title: 'Contact', key: 'contact' },
            { title: 'Nombre de factures', key: 'invoiceCount' },
            { title: 'Factures payées', key: 'paidInvoiceCount' },
            { title: 'Chiffre d\'affaires', key: 'totalRevenue' },
            { title: 'Date de création', key: 'createdAt' },
          ];
          fileName = 'clients_export.csv';
          break;

        case 'products':
          data = await analyticsService.exportProductData();
          headers = [
            { title: 'Nom', key: 'name' },
            { title: 'Quantité totale', key: 'totalQuantity' },
            { title: 'Chiffre d\'affaires', key: 'totalRevenue' },
            { title: 'Nombre de factures', key: 'invoiceCount' },
            { title: 'Factures payées', key: 'paidInvoiceCount' },
            { title: 'Prix unitaire moyen', key: 'averageUnitPrice' },
          ];
          fileName = 'produits_export.csv';
          break;

        default:
          throw new Error('Type de données non pris en charge');
      }

      // Export data to CSV
      exportToCSV(data, headers, fileName);

      setSnackbar({
        open: true,
        message: 'Données exportées avec succès',
        severity: 'success',
      });
    } catch (error) {
      console.error('Error exporting data:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de l\'exportation des données',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader 
        title="Exporter les données pour Power BI" 
        subheader="Exportez vos données au format CSV pour les importer dans Power BI"
      />
      <Divider />
      <CardContent>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Sélectionnez le type de données à exporter
            </Typography>
            <FormControl fullWidth>
              <InputLabel id="data-type-label">Type de données</InputLabel>
              <Select
                labelId="data-type-label"
                id="data-type-select"
                value={dataType}
                label="Type de données"
                onChange={(e) => setDataType(e.target.value)}
              >
                <MenuItem value="invoices">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TableChartIcon sx={{ mr: 1 }} />
                    Factures
                  </Box>
                </MenuItem>
                <MenuItem value="clients">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PeopleIcon sx={{ mr: 1 }} />
                    Clients
                  </Box>
                </MenuItem>
                <MenuItem value="products">
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <InventoryIcon sx={{ mr: 1 }} />
                    Produits/Services
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {dataType === 'invoices' && (
            <>
              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
                  <DatePicker
                    label="Date de début"
                    value={startDate}
                    onChange={(newValue) => setStartDate(newValue)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
                  <DatePicker
                    label="Date de fin"
                    value={endDate}
                    onChange={(newValue) => setEndDate(newValue)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
            </>
          )}

          <Grid item xs={12}>
            <Alert severity="info" sx={{ mb: 2 }}>
              Les données exportées peuvent être importées directement dans Power BI pour créer des tableaux de bord interactifs.
            </Alert>
            <Button
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <FileDownloadIcon />}
              onClick={handleExport}
              disabled={loading}
              fullWidth
            >
              {loading ? 'Exportation en cours...' : 'Exporter les données'}
            </Button>
          </Grid>
        </Grid>
      </CardContent>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default PowerBIExport;
