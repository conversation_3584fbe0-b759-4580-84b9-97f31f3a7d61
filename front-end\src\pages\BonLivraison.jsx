import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  MenuItem,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  LocalShipping as TruckIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  PictureAsPdf as PdfIcon,
  Email as EmailIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import bonLivraisonService from '../services/bonLivraisonService';
import BonLivraisonForm from '../components/BonLivraisonForm';

const BonLivraison = () => {
  const { currentUser } = useAuth();
  const [bonLivraisons, setBonLivraisons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statutFilter, setStatutFilter] = useState('');
  const [dateDebutFilter, setDateDebutFilter] = useState('');
  const [dateFinFilter, setDateFinFilter] = useState('');

  // Dialog states
  const [openForm, setOpenForm] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [selectedBonLivraison, setSelectedBonLivraison] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  // Charger les bons de livraison
  const loadBonLivraisons = async () => {
    try {
      setLoading(true);
      const params = {};
      if (searchTerm) params.search = searchTerm;
      if (statutFilter) params.statut = statutFilter;
      if (dateDebutFilter) params.dateDebut = dateDebutFilter;
      if (dateFinFilter) params.dateFin = dateFinFilter;

      const data = await bonLivraisonService.getAllBonLivraisons(params);
      setBonLivraisons(data);
    } catch (error) {
      console.error('Erreur lors du chargement des bons de livraison:', error);
      setError('Erreur lors du chargement des bons de livraison');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBonLivraisons();
  }, [searchTerm, statutFilter, dateDebutFilter, dateFinFilter]);

  // Gérer l'ajout d'un bon de livraison
  const handleAddBonLivraison = () => {
    setSelectedBonLivraison(null);
    setIsEditing(false);
    setOpenForm(true);
  };

  // Gérer la modification d'un bon de livraison
  const handleEditBonLivraison = (bonLivraison) => {
    setSelectedBonLivraison(bonLivraison);
    setIsEditing(true);
    setOpenForm(true);
  };

  // Gérer la visualisation d'un bon de livraison
  const handleViewBonLivraison = (bonLivraison) => {
    setSelectedBonLivraison(bonLivraison);
    setOpenViewDialog(true);
  };

  // Gérer la suppression d'un bon de livraison
  const handleDeleteBonLivraison = (bonLivraison) => {
    setSelectedBonLivraison(bonLivraison);
    setOpenDeleteDialog(true);
  };

  // Confirmer la suppression
  const confirmDelete = async () => {
    try {
      await bonLivraisonService.deleteBonLivraison(selectedBonLivraison._id);
      setSuccess('Bon de livraison supprimé avec succès');
      loadBonLivraisons();
      setOpenDeleteDialog(false);
      setSelectedBonLivraison(null);
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      setError(error.response?.data?.message || 'Erreur lors de la suppression du bon de livraison');
    }
  };

  // Gérer la soumission du formulaire
  const handleFormSubmit = async (bonLivraisonData) => {
    try {
      if (isEditing) {
        await bonLivraisonService.updateBonLivraison(selectedBonLivraison._id, bonLivraisonData);
        setSuccess('Bon de livraison modifié avec succès');
      } else {
        await bonLivraisonService.createBonLivraison(bonLivraisonData);
        setSuccess('Bon de livraison créé avec succès');
      }
      loadBonLivraisons();
      setOpenForm(false);
      setSelectedBonLivraison(null);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(error.response?.data?.message || 'Erreur lors de la sauvegarde du bon de livraison');
    }
  };

  // Mettre à jour le statut
  const handleUpdateStatut = async (bonLivraison, newStatut) => {
    try {
      await bonLivraisonService.updateStatutBonLivraison(bonLivraison._id, { statut: newStatut });
      setSuccess('Statut mis à jour avec succès');
      loadBonLivraisons();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
      setError(error.response?.data?.message || 'Erreur lors de la mise à jour du statut');
    }
  };

  // Gérer la génération de PDF
  const handleGeneratePdf = async (id) => {
    try {
      await bonLivraisonService.generatePdf(id);
      setSuccess('PDF généré avec succès');
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      setError('Erreur lors de la génération du PDF');
    }
  };

  // Gérer l'impression
  const handlePrint = async (id) => {
    try {
      await bonLivraisonService.print(id);
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
      setError('Erreur lors de l\'impression');
    }
  };

  // Gérer l'envoi par email
  const handleSendEmail = async (id, emailData = {}) => {
    try {
      await bonLivraisonService.sendEmail(id, emailData);
      setSuccess('Email envoyé avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      setError('Erreur lors de l\'envoi de l\'email');
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* En-tête */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          Bons de Livraison
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddBonLivraison}
          sx={{ borderRadius: 2 }}
        >
          Nouveau Bon de Livraison
        </Button>
      </Box>

      {/* Filtres */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                select
                label="Statut"
                value={statutFilter}
                onChange={(e) => setStatutFilter(e.target.value)}
              >
                <MenuItem value="">Tous les statuts</MenuItem>
                {bonLivraisonService.getStatutOptions().map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                type="date"
                label="Date début"
                value={dateDebutFilter}
                onChange={(e) => setDateDebutFilter(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                type="date"
                label="Date fin"
                value={dateFinFilter}
                onChange={(e) => setDateFinFilter(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm('');
                  setStatutFilter('');
                  setDateDebutFilter('');
                  setDateFinFilter('');
                }}
              >
                Réinitialiser
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Liste des bons de livraison */}
      <Card>
        <CardContent>
          {loading ? (
            <Typography>Chargement...</Typography>
          ) : bonLivraisons.length === 0 ? (
            <Typography>Aucun bon de livraison trouvé</Typography>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Numéro</TableCell>
                    <TableCell>Client</TableCell>
                    <TableCell>Livreur</TableCell>
                    <TableCell>Date Livraison</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Progression</TableCell>
                    <TableCell>Montant</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {bonLivraisons.map((bonLivraison) => {
                    const formatted = bonLivraisonService.formatBonLivraisonForDisplay(bonLivraison);
                    const canEdit = bonLivraisonService.canEdit(bonLivraison);
                    const canDelete = bonLivraisonService.canDelete(bonLivraison);

                    return (
                      <TableRow key={bonLivraison._id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <AssignmentIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="subtitle2" fontWeight="bold">
                              {bonLivraison.numero}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">{formatted.clientNom}</Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <TruckIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">{formatted.livreurNom}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">{formatted.dateLivraisonFormatee}</Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={formatted.statutLabel}
                            color={bonLivraisonService.getStatutColor(bonLivraison.statut)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 120 }}>
                            <LinearProgress
                              variant="determinate"
                              value={formatted.pourcentageLivraison}
                              sx={{ flexGrow: 1, mr: 1 }}
                            />
                            <Typography variant="caption">
                              {formatted.pourcentageLivraison}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {formatted.montantTotalFormate}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip title="Voir">
                            <IconButton
                              size="small"
                              onClick={() => handleViewBonLivraison(bonLivraison)}
                              color="info"
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          {canEdit && (
                            <Tooltip title="Modifier">
                              <IconButton
                                size="small"
                                onClick={() => handleEditBonLivraison(bonLivraison)}
                                color="primary"
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {canDelete && (
                            <Tooltip title="Supprimer">
                              <IconButton
                                size="small"
                                onClick={() => handleDeleteBonLivraison(bonLivraison)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {bonLivraison.statut === 'EN_COURS' && (
                            <Tooltip title="Marquer comme livré">
                              <IconButton
                                size="small"
                                onClick={() => handleUpdateStatut(bonLivraison, 'LIVREE')}
                                color="success"
                              >
                                <CheckIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {bonLivraison.statut === 'EN_COURS' && (
                            <Tooltip title="Marquer comme échec">
                              <IconButton
                                size="small"
                                onClick={() => handleUpdateStatut(bonLivraison, 'ECHEC')}
                                color="error"
                              >
                                <ErrorIcon />
                              </IconButton>
                            </Tooltip>
                          )}

                          {/* Actions PDF, Email, Print */}
                          <Tooltip title="Télécharger PDF">
                            <IconButton
                              size="small"
                              onClick={() => handleGeneratePdf(bonLivraison._id)}
                              color="primary"
                            >
                              <PdfIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Envoyer par email">
                            <IconButton
                              size="small"
                              onClick={() => handleSendEmail(bonLivraison._id)}
                              color="secondary"
                            >
                              <EmailIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Imprimer">
                            <IconButton
                              size="small"
                              onClick={() => handlePrint(bonLivraison._id)}
                              color="info"
                            >
                              <PrintIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Dialog de formulaire */}
      <Dialog open={openForm} onClose={() => setOpenForm(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          {isEditing ? 'Modifier le bon de livraison' : 'Nouveau bon de livraison'}
        </DialogTitle>
        <DialogContent>
          <BonLivraisonForm
            bonLivraison={selectedBonLivraison}
            onSubmit={handleFormSubmit}
            onCancel={() => setOpenForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog de visualisation */}
      <Dialog open={openViewDialog} onClose={() => setOpenViewDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Détails du bon de livraison</DialogTitle>
        <DialogContent>
          {selectedBonLivraison && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                {/* Informations générales */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Informations générales
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">
                            Numéro
                          </Typography>
                          <Typography variant="body1" fontWeight="bold">
                            {selectedBonLivraison.numero}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">
                            Date de livraison
                          </Typography>
                          <Typography variant="body1">
                            {new Date(selectedBonLivraison.dateLivraison).toLocaleDateString('fr-FR')}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">
                            Statut
                          </Typography>
                          <Chip
                            label={bonLivraisonService.getStatutLabel(selectedBonLivraison.statut)}
                            color={bonLivraisonService.getStatutColor(selectedBonLivraison.statut)}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">
                            Montant total
                          </Typography>
                          <Typography variant="body1" fontWeight="bold">
                            {bonLivraisonService.formatMontant(selectedBonLivraison.montantTotal)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Informations client */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Client
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Nom
                      </Typography>
                      <Typography variant="body1" fontWeight="bold" gutterBottom>
                        {selectedBonLivraison.clientId?.nom || 'Non spécifié'}
                      </Typography>

                      {selectedBonLivraison.clientId?.email && (
                        <>
                          <Typography variant="body2" color="text.secondary">
                            Email
                          </Typography>
                          <Typography variant="body1" gutterBottom>
                            {selectedBonLivraison.clientId.email}
                          </Typography>
                        </>
                      )}

                      {selectedBonLivraison.clientId?.telephone && (
                        <>
                          <Typography variant="body2" color="text.secondary">
                            Téléphone
                          </Typography>
                          <Typography variant="body1">
                            {selectedBonLivraison.clientId.telephone}
                          </Typography>
                        </>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Informations livreur */}
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        <TruckIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Livreur
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Nom
                      </Typography>
                      <Typography variant="body1" fontWeight="bold" gutterBottom>
                        {selectedBonLivraison.livreurId?.prenom} {selectedBonLivraison.livreurId?.nom}
                      </Typography>

                      {selectedBonLivraison.livreurId?.telephone && (
                        <>
                          <Typography variant="body2" color="text.secondary">
                            Téléphone
                          </Typography>
                          <Typography variant="body1" gutterBottom>
                            {selectedBonLivraison.livreurId.telephone}
                          </Typography>
                        </>
                      )}

                      {selectedBonLivraison.livreurId?.vehicule && (
                        <>
                          <Typography variant="body2" color="text.secondary">
                            Véhicule
                          </Typography>
                          <Typography variant="body1">
                            {selectedBonLivraison.livreurId.vehicule?.type || 'Non spécifié'}
                            {selectedBonLivraison.livreurId.vehicule?.marque ? ` - ${selectedBonLivraison.livreurId.vehicule.marque}` : ''}
                            {selectedBonLivraison.livreurId.vehicule?.modele ? ` ${selectedBonLivraison.livreurId.vehicule.modele}` : ''}
                            {selectedBonLivraison.livreurId.vehicule?.immatriculation ? ` (${selectedBonLivraison.livreurId.vehicule.immatriculation})` : ''}
                          </Typography>
                        </>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Facture liée */}
                {selectedBonLivraison.factureId && (
                  <Grid item xs={12}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="primary">
                          Facture liée
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Numéro de facture
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {selectedBonLivraison.factureId?.numero || selectedBonLivraison.factureId}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                {/* Notes */}
                {selectedBonLivraison.notes && (
                  <Grid item xs={12}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="primary">
                          Notes
                        </Typography>
                        <Typography variant="body1">
                          {selectedBonLivraison.notes}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenViewDialog(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de confirmation de suppression */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer le bon de livraison{' '}
            <strong>{selectedBonLivraison?.numero}</strong> ?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Annuler</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifications */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
      >
        <Alert severity="error" onClose={() => setError('')}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess('')}
      >
        <Alert severity="success" onClose={() => setSuccess('')}>
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BonLivraison;
