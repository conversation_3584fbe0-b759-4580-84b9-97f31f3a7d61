import api from "./api";
import { getTemplateSettings } from "./templateService";
import { formatCurrency, getDefaultCurrency, normalizeStatus } from "../utils/formatters";

const devisService = {
  // Récupérer les demandes de devis en attente (pour les vendeurs)
  getDemandesDevis: async () => {
    try {
      const response = await api.get("/devis/demandes");
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la récupération des demandes de devis");
    }
  },

  // Créer une demande de devis (pour les clients)
  createDemandeDevis: async (demandeData) => {
    try {
      console.log('Sending createDemandeDevis request:', demandeData);
      const response = await api.post("/devis/demande", demandeData);
      return response.data;
    } catch (error) {
      console.error('Error in createDemandeDevis:', error);
      const serverError = error.response?.data;
      throw new Error(
        serverError?.message ||
        serverError?.errors?.join('\n') ||
        "Erreur lors de la création de la demande de devis"
      );
    }
  },

  // Valider une demande de devis (pour les responsables)
  validerDemandeDevis: async (id, commentaires = "") => {
    try {
      const response = await api.patch(`/devis/${id}/valider`, {
        commentaires,
        statut: 'APPROVED_INTERNAL'
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la validation du devis ${id}`);
    }
  },

  // Rejeter une demande de devis (pour les responsables)
  rejeterDemandeDevis: async (id, commentaires = "") => {
    try {
      const response = await api.patch(`/devis/${id}/rejeter`, {
        commentaires,
        statut: 'REJECTED'
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors du rejet du devis ${id}`);
    }
  },
  getDevis: async () => {
    try {
      const response = await api.get("/devis"); // Remove /api
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la récupération des devis");
    }
  },

  getDevisById: async (id) => {
    try {
      const response = await api.get(`/devis/${id}`); // Remove /api
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la récupération du devis ${id}`);
    }
  },

  createDevis: async (devisData) => {
    try {
      console.log('Sending createDevis request:', devisData);
      const response = await api.post("/devis", devisData); // Remove /api
      return response.data;
    } catch (error) {
      console.error('Error in createDevis:', error);
      const serverError = error.response?.data;
      if (error.response?.status === 404) {
        throw new Error("Le serveur n'a pas trouvé la route /api/devis. Vérifiez que le backend est en cours d'exécution et que la route est correctement configurée.");
      }
      if (error.response?.status === 409) {
        throw new Error(serverError?.message || "Ce numéro de devis existe déjà");
      }
      throw new Error(
        serverError?.message ||
        serverError?.errors?.join('\n') ||
        "Erreur lors de la création du devis"
      );
    }
  },

  updateDevis: async (id, devisData) => {
    try {
      const response = await api.put(`/devis/${id}`, devisData); // Remove /api
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.errors?.join('\n') ||
        `Erreur lors de la mise à jour du devis ${id}`
      );
    }
  },

  deleteDevis: async (id) => {
    try {
      const response = await api.delete(`/devis/${id}`); // Remove /api
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la suppression du devis ${id}`);
    }
  },

  updateDevisStatus: async (id, status) => {
    try {
      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus(status);
      console.log(`Mise à jour du statut du devis ${id} vers ${status} (normalisé: ${normalizedStatus})`);

      // Vérifier que l'ID est valide
      if (!id) {
        throw new Error('ID du devis non valide');
      }

      // Vérifier que le statut est valide
      const validStatuses = ['DRAFT', 'SENT', 'PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED'];
      if (!validStatuses.includes(normalizedStatus)) {
        throw new Error(`Statut invalide après normalisation: ${normalizedStatus}`);
      }

      // Ajouter un timestamp pour éviter les problèmes de cache
      const timestamp = new Date().getTime();
      const response = await api.put(`/devis/${id}/status?_=${timestamp}`, { status: normalizedStatus });

      console.log('Réponse de mise à jour du statut:', response.data);

      // Vérifier que le statut a bien été mis à jour
      if (response.data.statut !== normalizedStatus) {
        console.warn(`Warning: Quote status was not updated to ${normalizedStatus}. Current status: ${response.data.statut}`);
      } else {
        console.log(`Quote status successfully updated to ${normalizedStatus}`);
      }

      return response.data;
    } catch (error) {
      console.error('Erreur détaillée lors de la mise à jour du statut:', error);

      // Journaliser l'erreur complète pour le débogage
      console.error('Erreur complète:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      });

      if (error.response) {
        console.error('Réponse d\'erreur:', error.response.data);
        throw new Error(error.response.data.message || `Erreur lors de la mise à jour du statut du devis ${id}`);
      } else if (error.request) {
        console.error('Pas de réponse reçue:', error.request);
        throw new Error('Erreur de connexion au serveur. Veuillez réessayer.');
      } else if (error.customMessage) {
        // Utiliser le message personnalisé défini dans l'intercepteur
        throw new Error(error.customMessage);
      } else {
        console.error('Erreur de configuration:', error.message);
        throw new Error(`Erreur lors de la mise à jour du statut du devis: ${error.message}`);
      }
    }
  },

  // Méthodes spécifiques pour accepter et refuser un devis
  acceptDevis: async (id) => {
    try {
      console.log(`Acceptation du devis ${id} via la route PATCH /devis/${id}/accept`);

      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus("ACCEPTED");
      console.log(`Normalized status for acceptance: ${normalizedStatus}`);

      const response = await api.patch(`/devis/${id}/accept`, { status: normalizedStatus });
      console.log("Réponse de l'acceptation du devis:", response.data);

      // Vérifier que le statut a bien été mis à jour
      if (response.data.statut !== normalizedStatus) {
        console.warn(`Warning: Quote status was not updated to ${normalizedStatus}. Current status: ${response.data.statut}`);
      } else {
        console.log(`Quote status successfully updated to ${normalizedStatus}`);
      }

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'acceptation du devis ${id}:`, error);

      // Journaliser l'erreur complète pour le débogage
      console.error('Erreur complète:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      });

      if (error.response) {
        console.error('Réponse d\'erreur:', error.response.data);
        throw new Error(error.response.data.message || `Erreur lors de l'acceptation du devis ${id}`);
      } else if (error.request) {
        console.error('Pas de réponse reçue:', error.request);
        throw new Error('Erreur de connexion au serveur. Veuillez réessayer.');
      } else {
        console.error('Erreur de configuration:', error.message);
        throw new Error(`Erreur lors de l'acceptation du devis: ${error.message}`);
      }
    }
  },

  rejectDevis: async (id, reason = "") => {
    try {
      console.log(`Refus du devis ${id} via la route PATCH /devis/${id}/reject`);

      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus("REJECTED");
      console.log(`Normalized status for rejection: ${normalizedStatus}`);

      const response = await api.patch(`/devis/${id}/reject`, {
        status: normalizedStatus,
        reason
      });
      console.log("Réponse du refus du devis:", response.data);

      // Vérifier que le statut a bien été mis à jour
      if (response.data.statut !== normalizedStatus) {
        console.warn(`Warning: Quote status was not updated to ${normalizedStatus}. Current status: ${response.data.statut}`);
      } else {
        console.log(`Quote status successfully updated to ${normalizedStatus}`);
      }

      return response.data;
    } catch (error) {
      console.error(`Erreur lors du refus du devis ${id}:`, error);

      // Journaliser l'erreur complète pour le débogage
      console.error('Erreur complète:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      });

      if (error.response) {
        console.error('Réponse d\'erreur:', error.response.data);
        throw new Error(error.response.data.message || `Erreur lors du refus du devis ${id}`);
      } else if (error.request) {
        console.error('Pas de réponse reçue:', error.request);
        throw new Error('Erreur de connexion au serveur. Veuillez réessayer.');
      } else {
        console.error('Erreur de configuration:', error.message);
        throw new Error(`Erreur lors du refus du devis: ${error.message}`);
      }
    }
  },

  convertToFacture: async (id) => {
    try {
      const response = await api.post(`/devis/${id}/convert`); // Remove /api
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de la conversion du devis ${id} en facture`);
    }
  },

  sendDevis: async (id, emailData) => {
    try {
      const response = await api.post(`/devis/${id}/send`, emailData); // Remove /api
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || `Erreur lors de l'envoi du devis ${id}`);
    }
  },

  // Generate PDF for a quote
  generatePdf: async (id) => {
    try {
      console.log(`Generating PDF for quote ${id} using backend API`);

      // Use the backend API to generate the PDF
      const response = await api.get(`/devis/${id}/pdf`, {
        responseType: 'blob' // Important: set the response type to blob
      });

      // Create a blob URL from the PDF data
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      // Create a link element and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `devis-${id}.pdf`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      return { success: true, message: "PDF généré avec succès" };
    } catch (error) {
      console.error(`Erreur lors de la génération du PDF pour le devis ${id}:`, error);

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de la génération du PDF: ${error.message}`);

      throw new Error(`Erreur lors de la génération du PDF: ${error.message}`);
    }
  },

  // Send quote by email
  sendEmail: async (id, emailData = {}) => {
    try {
      const response = await api.post(`/devis/${id}/email`, emailData);
      console.log("Response from sendEmail:", response.data);

      // Afficher une alerte de succès
      alert("Email envoyé avec succès !");

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'envoi de l'email pour le devis ${id}:`, error.message);
      if (error.response) {
        console.error("Réponse du serveur:", error.response.data);
        console.error("Statut:", error.response.status);
      }

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de l'envoi de l'email: ${error.message}`);

      throw new Error(`Erreur lors de l'envoi de l'email: ${error.message}`);
    }
  },

  // Print quote
  printDevis: async (id) => {
    try {
      console.log(`Printing quote ${id} using backend API`);

      // Use the backend API to generate the PDF for printing
      const response = await api.get(`/devis/${id}/print`, {
        responseType: 'blob' // Important: set the response type to blob
      });

      // Create a blob URL from the PDF data
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      // Open the PDF in a new window for printing
      const printWindow = window.open(url, '_blank');

      // If the window was successfully opened, trigger print
      if (printWindow) {
        printWindow.addEventListener('load', () => {
          setTimeout(() => {
            printWindow.print();
          }, 1000); // Small delay to ensure the PDF is loaded
        });
      } else {
        // Si la fenêtre n'a pas pu être ouverte (bloqueur de popups)
        alert("Veuillez autoriser les popups pour imprimer le document.");
      }

      return { success: true, message: "Document envoyé à l'impression avec succès" };
    } catch (error) {
      console.error(`Erreur lors de l'impression du devis ${id}:`, error);

      // Afficher une alerte pour informer l'utilisateur
      alert(`Erreur lors de l'impression: ${error.message}`);

      throw new Error(`Erreur lors de l'impression: ${error.message}`);
    }
  }
};

export default devisService;