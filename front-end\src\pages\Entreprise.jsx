import React, { useState, useEffect } from "react"
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Snackbar,
  Alert,
} from "@mui/material"
import {
  Business as BusinessIcon,
  Save as SaveIcon,
} from "@mui/icons-material"

const Entreprise = () => {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  })
  const [company, setCompany] = useState({
    nom: "",
    adresse: "",
    telephone: "",
    numeroFiscal: "",
  })

  useEffect(() => {
    fetchCompanyData()
  }, [])

  const fetchCompanyData = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem("token")
      const response = await fetch("http://localhost:5000/api/entreprise/profile", {
        headers: {
          Authorization: `<PERSON><PERSON> ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setCompany({
          nom: data.nom || "",
          adresse: data.adresse || "",
          telephone: data.telephone || "",
          numeroFiscal: data.numeroFiscal || "",
        })
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des données:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setCompany((prev) => ({ ...prev, [name]: value }))
  }

  const handleSave = async () => {
    setSaving(true)
    setSnackbar({ open: false, message: "", severity: "success" })

    // Validation simple
    if (!company.nom) {
      setSnackbar({
        open: true,
        message: "Le nom de l'entreprise est requis",
        severity: "error",
      })
      setSaving(false)
      return
    }

    try {
      const token = localStorage.getItem("token")
      const response = await fetch("http://localhost:5000/api/entreprise/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(company),
      })

      if (response.ok) {
        setSnackbar({
          open: true,
          message: "Informations de l'entreprise sauvegardées avec succès",
          severity: "success",
        })
      } else {
        throw new Error("Erreur lors de la sauvegarde")
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: "Erreur lors de la sauvegarde des informations",
        severity: "error",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }))
  }

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100vh" }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          <BusinessIcon sx={{ mr: 1, verticalAlign: "middle" }} />
          Mon Entreprise
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? "Enregistrement..." : "Enregistrer"}
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Informations de l'entreprise
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                name="nom"
                label="Nom de l'entreprise"
                value={company.nom}
                onChange={handleChange}
                fullWidth
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="numeroFiscal"
                label="Numéro fiscal"
                value={company.numeroFiscal}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="adresse"
                label="Adresse de l'entreprise"
                value={company.adresse}
                onChange={handleChange}
                fullWidth
                margin="normal"
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="telephone"
                label="Téléphone de l'entreprise"
                value={company.telephone}
                onChange={handleChange}
                fullWidth
                margin="normal"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: "100%" }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default Entreprise
