/**
 * Utility for exporting data to CSV format
 */

/**
 * Convert JSON data to CSV string
 * @param {Array} data - Array of objects to convert to CSV
 * @param {Array} headers - Array of header objects with title and key properties
 * @returns {string} CSV formatted string
 */
export const convertToCSV = (data, headers) => {
  if (!data || !data.length || !headers || !headers.length) {
    return '';
  }

  // Create header row
  const headerRow = headers.map(header => `"${header.title}"`).join(',');
  
  // Create data rows
  const rows = data.map(item => {
    return headers.map(header => {
      // Get the value using the key
      const value = item[header.key];
      
      // Handle different data types
      if (value === null || value === undefined) {
        return '""';
      } else if (typeof value === 'string') {
        // Escape quotes and wrap in quotes
        return `"${value.replace(/"/g, '""')}"`;
      } else if (value instanceof Date) {
        return `"${value.toISOString()}"`;
      } else {
        return `"${value}"`;
      }
    }).join(',');
  }).join('\n');
  
  // Combine header and rows
  return `${headerRow}\n${rows}`;
};

/**
 * Download CSV data as a file
 * @param {string} csvContent - CSV content to download
 * @param {string} fileName - Name of the file to download
 */
export const downloadCSV = (csvContent, fileName) => {
  // Create a blob with the CSV content
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // Create a download link
  const link = document.createElement('a');
  
  // Create a URL for the blob
  const url = URL.createObjectURL(blob);
  
  // Set link properties
  link.setAttribute('href', url);
  link.setAttribute('download', fileName);
  link.style.visibility = 'hidden';
  
  // Add link to document
  document.body.appendChild(link);
  
  // Click the link to trigger download
  link.click();
  
  // Clean up
  document.body.removeChild(link);
};

/**
 * Export data to CSV and download
 * @param {Array} data - Array of objects to export
 * @param {Array} headers - Array of header objects with title and key properties
 * @param {string} fileName - Name of the file to download
 */
export const exportToCSV = (data, headers, fileName) => {
  const csvContent = convertToCSV(data, headers);
  downloadCSV(csvContent, fileName);
};
