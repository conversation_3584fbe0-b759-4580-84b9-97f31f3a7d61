{"version": 3, "sources": ["../../../src/shared/lib/get-rspack.ts"], "sourcesContent": ["import { warnOnce } from '../../build/output/log'\n\nexport function getRspackCore() {\n  warnRspack()\n  try {\n    const paths = [require.resolve('next-rspack')]\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require(require.resolve('@rspack/core', { paths }))\n  } catch (e) {\n    if (e instanceof Error && 'code' in e && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        '@rspack/core is not available. Please make sure `next-rspack` is correctly installed.'\n      )\n    }\n\n    throw e\n  }\n}\n\nexport function getRspackReactRefresh() {\n  warnRspack()\n  try {\n    const paths = [require.resolve('next-rspack')]\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const plugin = require(\n      require.resolve('@rspack/plugin-react-refresh', { paths })\n    )\n    const entry = require.resolve(\n      '@rspack/plugin-react-refresh/react-refresh-entry',\n      { paths }\n    )\n    plugin.entry = entry\n    return plugin\n  } catch (e) {\n    if (e instanceof Error && 'code' in e && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        '@rspack/plugin-react-refresh is not available. Please make sure `next-rspack` is correctly installed.'\n      )\n    }\n\n    throw e\n  }\n}\n\nfunction warnRspack() {\n  warnOnce(\n    `\\`next-rspack\\` is currently experimental. It's not an official Next.js plugin, and is supported by the Rspack team in partnership with Next.js. Help improve Next.js and Rspack by providing feedback at https://github.com/vercel/next.js/discussions/77800`\n  )\n}\n"], "names": ["warnOnce", "getRspackCore", "warnRspack", "paths", "require", "resolve", "e", "Error", "code", "getRspackReactRefresh", "plugin", "entry"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,yBAAwB;AAEjD,OAAO,SAASC;IACdC;IACA,IAAI;QACF,MAAMC,QAAQ;YAACC,QAAQC,OAAO,CAAC;SAAe;QAC9C,6DAA6D;QAC7D,OAAOD,QAAQA,QAAQC,OAAO,CAAC,gBAAgB;YAAEF;QAAM;IACzD,EAAE,OAAOG,GAAG;QACV,IAAIA,aAAaC,SAAS,UAAUD,KAAKA,EAAEE,IAAI,KAAK,oBAAoB;YACtE,MAAM,qBAEL,CAFK,IAAID,MACR,0FADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMD;IACR;AACF;AAEA,OAAO,SAASG;IACdP;IACA,IAAI;QACF,MAAMC,QAAQ;YAACC,QAAQC,OAAO,CAAC;SAAe;QAC9C,6DAA6D;QAC7D,MAAMK,SAASN,QACbA,QAAQC,OAAO,CAAC,gCAAgC;YAAEF;QAAM;QAE1D,MAAMQ,QAAQP,QAAQC,OAAO,CAC3B,oDACA;YAAEF;QAAM;QAEVO,OAAOC,KAAK,GAAGA;QACf,OAAOD;IACT,EAAE,OAAOJ,GAAG;QACV,IAAIA,aAAaC,SAAS,UAAUD,KAAKA,EAAEE,IAAI,KAAK,oBAAoB;YACtE,MAAM,qBAEL,CAFK,IAAID,MACR,0GADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMD;IACR;AACF;AAEA,SAASJ;IACPF,SACG;AAEL"}