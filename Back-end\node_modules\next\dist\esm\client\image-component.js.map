{"version": 3, "sources": ["../../src/client/image-component.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n"], "names": ["React", "useRef", "useEffect", "useCallback", "useContext", "useMemo", "useState", "forwardRef", "use", "ReactDOM", "Head", "getImgProps", "imageConfigDefault", "ImageConfigContext", "warnOnce", "RouterContext", "defaultLoader", "useMergedRef", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "placeholder", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "unoptimized", "sizesInput", "src", "p", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "current", "event", "Event", "Object", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "NODE_ENV", "origSrc", "URL", "searchParams", "get", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "width", "innerWidth", "position", "getComputedStyle", "valid", "includes", "map", "String", "join", "height", "heightModified", "toString", "widthModified", "getDynamicProps", "fetchPriority", "Boolean", "fetchpriority", "ImageElement", "forwardedRef", "srcSet", "sizes", "decoding", "className", "style", "loading", "fill", "setShowAltText", "onLoad", "onError", "rest", "ownRef", "console", "error", "complete", "ref", "data-nimg", "ImagePreload", "isAppRouter", "imgAttributes", "opts", "as", "imageSrcSet", "imageSizes", "crossOrigin", "referrerPolicy", "preload", "link", "rel", "href", "undefined", "Image", "props", "pagesRouter", "configContext", "config", "c", "allSizes", "deviceSizes", "sort", "a", "b", "qualities", "onLoadingComplete", "blurComplete", "showAltText", "meta", "imgMeta", "imgConf", "priority"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,GAAG,QACE,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,OAAOC,UAAU,qBAAoB;AACrC,SAASC,WAAW,QAAQ,8BAA6B;AAYzD,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,SAASC,QAAQ,QAAQ,gCAA+B;AACxD,SAASC,aAAa,QAAQ,8CAA6C;AAE3E,iDAAiD;AACjD,OAAOC,mBAAmB,oCAAmC;AAC7D,SAASC,YAAY,QAAQ,mBAAkB;AAE/C,4CAA4C;AAC5C,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAE/C,IAAI,OAAOC,WAAW,aAAa;;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAmBA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASC,cACPC,GAA2B,EAC3BC,WAA6B,EAC7BC,SAAqD,EACrDC,oBAA2E,EAC3EC,eAAqC,EACrCC,WAAoB,EACpBC,UAA8B;IAE9B,MAAMC,MAAMP,uBAAAA,IAAKO,GAAG;IACpB,IAAI,CAACP,OAAOA,GAAG,CAAC,kBAAkB,KAAKO,KAAK;QAC1C;IACF;IACAP,GAAG,CAAC,kBAAkB,GAAGO;IACzB,MAAMC,IAAI,YAAYR,MAAMA,IAAIS,MAAM,KAAKC,QAAQC,OAAO;IAC1DH,EAAEI,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACb,IAAIc,aAAa,IAAI,CAACd,IAAIe,WAAW,EAAE;YAC1C,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA,IAAId,gBAAgB,SAAS;YAC3BG,gBAAgB;QAClB;QACA,IAAIF,6BAAAA,UAAWc,OAAO,EAAE;YACtB,+CAA+C;YAC/C,0CAA0C;YAC1C,2CAA2C;YAC3C,MAAMC,QAAQ,IAAIC,MAAM;YACxBC,OAAOC,cAAc,CAACH,OAAO,UAAU;gBAAEI,UAAU;gBAAOC,OAAOtB;YAAI;YACrE,IAAIuB,YAAY;YAChB,IAAIC,UAAU;YACdtB,UAAUc,OAAO,CAAC;gBAChB,GAAGC,KAAK;gBACRQ,aAAaR;gBACbS,eAAe1B;gBACf2B,QAAQ3B;gBACR4B,oBAAoB,IAAML;gBAC1BM,sBAAsB,IAAML;gBAC5BM,SAAS,KAAO;gBAChBC,gBAAgB;oBACdR,YAAY;oBACZN,MAAMc,cAAc;gBACtB;gBACAC,iBAAiB;oBACfR,UAAU;oBACVP,MAAMe,eAAe;gBACvB;YACF;QACF;QACA,IAAI7B,wCAAAA,qBAAsBa,OAAO,EAAE;YACjCb,qBAAqBa,OAAO,CAAChB;QAC/B;QACA,IAAIP,QAAQC,GAAG,CAACuC,QAAQ,KAAK,cAAc;YACzC,MAAMC,UAAU,IAAIC,IAAI5B,KAAK,YAAY6B,YAAY,CAACC,GAAG,CAAC,UAAU9B;YACpE,IAAIP,IAAIsC,YAAY,CAAC,iBAAiB,QAAQ;gBAC5C,IAAI,CAACjC,eAAgB,CAAA,CAACC,cAAcA,eAAe,OAAM,GAAI;oBAC3D,IAAIiC,qBACFvC,IAAIwC,qBAAqB,GAAGC,KAAK,GAAG7C,OAAO8C,UAAU;oBACvD,IAAIH,qBAAqB,KAAK;wBAC5B,IAAIjC,eAAe,SAAS;4BAC1BlB,SACE,AAAC,qBAAkB8C,UAAQ;wBAE/B,OAAO;4BACL9C,SACE,AAAC,qBAAkB8C,UAAQ;wBAE/B;oBACF;gBACF;gBACA,IAAIlC,IAAIc,aAAa,EAAE;oBACrB,MAAM,EAAE6B,QAAQ,EAAE,GAAG/C,OAAOgD,gBAAgB,CAAC5C,IAAIc,aAAa;oBAC9D,MAAM+B,QAAQ;wBAAC;wBAAY;wBAAS;qBAAW;oBAC/C,IAAI,CAACA,MAAMC,QAAQ,CAACH,WAAW;wBAC7BvD,SACE,AAAC,qBAAkB8C,UAAQ,wEAAqES,WAAS,wBAAqBE,MAC3HE,GAAG,CAACC,QACJC,IAAI,CAAC,OAAK;oBAEjB;gBACF;gBACA,IAAIjD,IAAIkD,MAAM,KAAK,GAAG;oBACpB9D,SACE,AAAC,qBAAkB8C,UAAQ;gBAE/B;YACF;YAEA,MAAMiB,iBACJnD,IAAIkD,MAAM,CAACE,QAAQ,OAAOpD,IAAIsC,YAAY,CAAC;YAC7C,MAAMe,gBAAgBrD,IAAIyC,KAAK,CAACW,QAAQ,OAAOpD,IAAIsC,YAAY,CAAC;YAChE,IACE,AAACa,kBAAkB,CAACE,iBACnB,CAACF,kBAAkBE,eACpB;gBACAjE,SACE,AAAC,qBAAkB8C,UAAQ;YAE/B;QACF;IACF;AACF;AAEA,SAASoB,gBACPC,aAAsB;IAEtB,IAAIC,QAAQ1E,MAAM;QAChB,kDAAkD;QAClD,iDAAiD;QACjD,mDAAmD;QACnD,OAAO;YAAEyE;QAAc;IACzB;IACA,uDAAuD;IACvD,4CAA4C;IAC5C,OAAO;QAAEE,eAAeF;IAAc;AACxC;AAEA,MAAMG,6BAAe7E,WACnB,QAwBE8E;QAvBA,EACEpD,GAAG,EACHqD,MAAM,EACNC,KAAK,EACLX,MAAM,EACNT,KAAK,EACLqB,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLT,aAAa,EACbtD,WAAW,EACXgE,OAAO,EACP5D,WAAW,EACX6D,IAAI,EACJhE,SAAS,EACTC,oBAAoB,EACpBC,eAAe,EACf+D,cAAc,EACd7D,UAAU,EACV8D,MAAM,EACNC,OAAO,EACP,GAAGC,MACJ;IAGD,MAAMC,SAAS9F,YACb,CAACuB;QACC,IAAI,CAACA,KAAK;YACR;QACF;QACA,IAAIqE,SAAS;YACX,2EAA2E;YAC3E,iFAAiF;YACjF,kFAAkF;YAClF,0CAA0C;YAC1CrE,IAAIO,GAAG,GAAGP,IAAIO,GAAG;QACnB;QACA,IAAId,QAAQC,GAAG,CAACuC,QAAQ,KAAK,cAAc;YACzC,IAAI,CAAC1B,KAAK;gBACRiE,QAAQC,KAAK,CAAE,6CAA4CzE;YAC7D;YACA,IAAIA,IAAIsC,YAAY,CAAC,WAAW,MAAM;gBACpCkC,QAAQC,KAAK,CACV;YAEL;QACF;QACA,IAAIzE,IAAI0E,QAAQ,EAAE;YAChB3E,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;QAEJ;IACF,GACA;QACEC;QACAN;QACAC;QACAC;QACAC;QACAiE;QACAhE;QACAC;KACD;IAGH,MAAMqE,MAAMpF,aAAaoE,cAAcY;IAEvC,qBACE,KAACvE;QACE,GAAGsE,IAAI;QACP,GAAGhB,gBAAgBC,cAAc;QAClC,qEAAqE;QACrE,wEAAwE;QACxE,qDAAqD;QACrDU,SAASA;QACTxB,OAAOA;QACPS,QAAQA;QACRY,UAAUA;QACVc,aAAWV,OAAO,SAAS;QAC3BH,WAAWA;QACXC,OAAOA;QACP,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtDH,OAAOA;QACPD,QAAQA;QACRrD,KAAKA;QACLoE,KAAKA;QACLP,QAAQ,CAACnD;YACP,MAAMjB,MAAMiB,MAAMS,aAAa;YAC/B3B,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;QAEJ;QACA+D,SAAS,CAACpD;YACR,qEAAqE;YACrEkD,eAAe;YACf,IAAIlE,gBAAgB,SAAS;gBAC3B,2EAA2E;gBAC3EG,gBAAgB;YAClB;YACA,IAAIiE,SAAS;gBACXA,QAAQpD;YACV;QACF;;AAGN;AAGF,SAAS4D,aAAa,KAMrB;IANqB,IAAA,EACpBC,WAAW,EACXC,aAAa,EAId,GANqB;IAOpB,MAAMC,OAAO;QACXC,IAAI;QACJC,aAAaH,cAAcnB,MAAM;QACjCuB,YAAYJ,cAAclB,KAAK;QAC/BuB,aAAaL,cAAcK,WAAW;QACtCC,gBAAgBN,cAAcM,cAAc;QAC5C,GAAG/B,gBAAgByB,cAAcxB,aAAa,CAAC;IACjD;IAEA,IAAIuB,eAAe/F,SAASuG,OAAO,EAAE;QACnC,mDAAmD;QACnDvG,SAASuG,OAAO,CACdP,cAAcxE,GAAG,EACjB,8DAA8D;QAC9DyE;QAEF,OAAO;IACT;IAEA,qBACE,KAAChG;kBACC,cAAA,KAACuG;YAOCC,KAAI;YACJ,sEAAsE;YACtE,qEAAqE;YACrE,sDAAsD;YACtD,EAAE;YACF,8EAA8E;YAC9EC,MAAMV,cAAcnB,MAAM,GAAG8B,YAAYX,cAAcxE,GAAG;YACzD,GAAGyE,IAAI;WAZN,YACAD,cAAcxE,GAAG,GACjBwE,cAAcnB,MAAM,GACpBmB,cAAclB,KAAK;;AAa7B;AAEA;;;;CAIC,GACD,OAAO,MAAM8B,sBAAQ9G,WACnB,CAAC+G,OAAOjC;IACN,MAAMkC,cAAcnH,WAAWW;IAC/B,0DAA0D;IAC1D,MAAMyF,cAAc,CAACe;IAErB,MAAMC,gBAAgBpH,WAAWS;IACjC,MAAM4G,SAASpH,QAAQ;YAIHqH;QAHlB,MAAMA,IAAIxG,aAAasG,iBAAiB5G;QACxC,MAAM+G,WAAW;eAAID,EAAEE,WAAW;eAAKF,EAAEb,UAAU;SAAC,CAACgB,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMH,cAAcF,EAAEE,WAAW,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAMC,aAAYN,eAAAA,EAAEM,SAAS,qBAAXN,aAAaG,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClD,OAAO;YAAE,GAAGL,CAAC;YAAEC;YAAUC;YAAaI;QAAU;IAClD,GAAG;QAACR;KAAc;IAElB,MAAM,EAAE1B,MAAM,EAAEmC,iBAAiB,EAAE,GAAGX;IACtC,MAAM1F,YAAY3B,OAAO6F;IAEzB5F,UAAU;QACR0B,UAAUc,OAAO,GAAGoD;IACtB,GAAG;QAACA;KAAO;IAEX,MAAMjE,uBAAuB5B,OAAOgI;IAEpC/H,UAAU;QACR2B,qBAAqBa,OAAO,GAAGuF;IACjC,GAAG;QAACA;KAAkB;IAEtB,MAAM,CAACC,cAAcpG,gBAAgB,GAAGxB,SAAS;IACjD,MAAM,CAAC6H,aAAatC,eAAe,GAAGvF,SAAS;IAE/C,MAAM,EAAEgH,OAAOb,aAAa,EAAE2B,MAAMC,OAAO,EAAE,GAAG1H,YAAY2G,OAAO;QACjEtG;QACAsH,SAASb;QACTS;QACAC;IACF;IAEA,qBACE;;0BAEI,KAAC/C;gBACE,GAAGqB,aAAa;gBACjB1E,aAAasG,QAAQtG,WAAW;gBAChCJ,aAAa0G,QAAQ1G,WAAW;gBAChCiE,MAAMyC,QAAQzC,IAAI;gBAClBhE,WAAWA;gBACXC,sBAAsBA;gBACtBC,iBAAiBA;gBACjB+D,gBAAgBA;gBAChB7D,YAAYsF,MAAM/B,KAAK;gBACvBc,KAAKhB;;YAGRgD,QAAQE,QAAQ,iBACf,KAAChC;gBACCC,aAAaA;gBACbC,eAAeA;iBAEf;;;AAGV,GACD"}