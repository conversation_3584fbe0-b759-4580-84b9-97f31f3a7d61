{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'node:http'\nimport type { SizeLimit } from '../../types'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { AppRenderContext, GenerateFlight } from './app-render'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\n\nimport {\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  ACTION_HEADER,\n} from '../../client/components/app-router-headers'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getRedirectTypeFromError,\n  getURLFromRedirectError,\n} from '../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectType,\n} from '../../client/components/redirect-error'\nimport RenderResult from '../render-result'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  filterReqHeaders,\n  actionsForbiddenHeaders,\n} from '../lib/server-ipc/utils'\nimport { getModifiedCookieValues } from '../web/spec-extension/adapters/request-cookies'\n\nimport {\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n} from '../../lib/constants'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { isCsrfOriginAllowed } from './csrf-protection'\nimport { warn } from '../../build/output/log'\nimport { RequestCookies, ResponseCookies } from '../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport { fromNodeOutgoingHttpHeaders } from '../web/utils'\nimport { selectWorkerForForwarding } from './action-utils'\nimport { isNodeNextRequest, isWebNextRequest } from '../base-http/helpers'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { synchronizeMutableCookies } from '../async-storage/request-store'\nimport type { TemporaryReferenceSet } from 'react-server-dom-webpack/server.edge'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { executeRevalidates } from '../revalidation-utils'\n\nfunction formDataFromSearchQueryString(query: string) {\n  const searchParams = new URLSearchParams(query)\n  const formData = new FormData()\n  for (const [key, value] of searchParams) {\n    formData.append(key, value)\n  }\n  return formData\n}\n\nfunction nodeHeadersToRecord(\n  headers: IncomingHttpHeaders | OutgoingHttpHeaders\n) {\n  const record: Record<string, string> = {}\n  for (const [key, value] of Object.entries(headers)) {\n    if (value !== undefined) {\n      record[key] = Array.isArray(value) ? value.join(', ') : `${value}`\n    }\n  }\n  return record\n}\n\nfunction getForwardedHeaders(\n  req: BaseNextRequest,\n  res: BaseNextResponse\n): Headers {\n  // Get request headers and cookies\n  const requestHeaders = req.headers\n  const requestCookies = new RequestCookies(HeadersAdapter.from(requestHeaders))\n\n  // Get response headers and cookies\n  const responseHeaders = res.getHeaders()\n  const responseCookies = new ResponseCookies(\n    fromNodeOutgoingHttpHeaders(responseHeaders)\n  )\n\n  // Merge request and response headers\n  const mergedHeaders = filterReqHeaders(\n    {\n      ...nodeHeadersToRecord(requestHeaders),\n      ...nodeHeadersToRecord(responseHeaders),\n    },\n    actionsForbiddenHeaders\n  ) as Record<string, string>\n\n  // Merge cookies into requestCookies, so responseCookies always take precedence\n  // and overwrite/delete those from requestCookies.\n  responseCookies.getAll().forEach((cookie) => {\n    if (typeof cookie.value === 'undefined') {\n      requestCookies.delete(cookie.name)\n    } else {\n      requestCookies.set(cookie)\n    }\n  })\n\n  // Update the 'cookie' header with the merged cookies\n  mergedHeaders['cookie'] = requestCookies.toString()\n\n  // Remove headers that should not be forwarded\n  delete mergedHeaders['transfer-encoding']\n\n  return new Headers(mergedHeaders)\n}\n\nfunction addRevalidationHeader(\n  res: BaseNextResponse,\n  {\n    workStore,\n    requestStore,\n  }: {\n    workStore: WorkStore\n    requestStore: RequestStore\n  }\n) {\n  // If a tag was revalidated, the client router needs to invalidate all the\n  // client router cache as they may be stale. And if a path was revalidated, the\n  // client needs to invalidate all subtrees below that path.\n\n  // To keep the header size small, we use a tuple of\n  // [[revalidatedPaths], isTagRevalidated ? 1 : 0, isCookieRevalidated ? 1 : 0]\n  // instead of a JSON object.\n\n  // TODO-APP: Currently the prefetch cache doesn't have subtree information,\n  // so we need to invalidate the entire cache if a path was revalidated.\n  // TODO-APP: Currently paths are treated as tags, so the second element of the tuple\n  // is always empty.\n\n  const isTagRevalidated = workStore.pendingRevalidatedTags?.length ? 1 : 0\n  const isCookieRevalidated = getModifiedCookieValues(\n    requestStore.mutableCookies\n  ).length\n    ? 1\n    : 0\n\n  res.setHeader(\n    'x-action-revalidated',\n    JSON.stringify([[], isTagRevalidated, isCookieRevalidated])\n  )\n}\n\n/**\n * Forwards a server action request to a separate worker. Used when the requested action is not available in the current worker.\n */\nasync function createForwardedActionResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  host: Host,\n  workerPathname: string,\n  basePath: string,\n  workStore: WorkStore\n) {\n  if (!host) {\n    throw new Error(\n      'Invariant: Missing `host` header from a forwarded Server Actions request.'\n    )\n  }\n\n  const forwardedHeaders = getForwardedHeaders(req, res)\n\n  // indicate that this action request was forwarded from another worker\n  // we use this to skip rendering the flight tree so that we don't update the UI\n  // with the response from the forwarded worker\n  forwardedHeaders.set('x-action-forwarded', '1')\n\n  const proto = workStore.incrementalCache?.requestProtocol || 'https'\n\n  // For standalone or the serverful mode, use the internal origin directly\n  // other than the host headers from the request.\n  const origin = process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${host.value}`\n\n  const fetchUrl = new URL(`${origin}${basePath}${workerPathname}`)\n\n  try {\n    let body: BodyInit | ReadableStream<Uint8Array> | undefined\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(req)\n    ) {\n      if (!req.body) {\n        throw new Error('Invariant: missing request body.')\n      }\n\n      body = req.body\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req)\n    ) {\n      body = req.stream()\n    } else {\n      throw new Error('Invariant: Unknown request type.')\n    }\n\n    // Forward the request to the new worker\n    const response = await fetch(fetchUrl, {\n      method: 'POST',\n      body,\n      duplex: 'half',\n      headers: forwardedHeaders,\n      redirect: 'manual',\n      next: {\n        // @ts-ignore\n        internal: 1,\n      },\n    })\n\n    if (\n      response.headers.get('content-type')?.startsWith(RSC_CONTENT_TYPE_HEADER)\n    ) {\n      // copy the headers from the redirect response to the response we're sending\n      for (const [key, value] of response.headers) {\n        if (!actionsForbiddenHeaders.includes(key)) {\n          res.setHeader(key, value)\n        }\n      }\n\n      return new FlightRenderResult(response.body!)\n    } else {\n      // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n      response.body?.cancel()\n    }\n  } catch (err) {\n    // we couldn't stream the forwarded response, so we'll just return an empty response\n    console.error(`failed to forward action response`, err)\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n/**\n * Returns the parsed redirect URL if we deem that it is hosted by us.\n *\n * We handle both relative and absolute redirect URLs.\n *\n * In case the redirect URL is not relative to the application we return `null`.\n */\nfunction getAppRelativeRedirectUrl(\n  basePath: string,\n  host: Host,\n  redirectUrl: string\n): URL | null {\n  if (redirectUrl.startsWith('/') || redirectUrl.startsWith('.')) {\n    // Make sure we are appending the basePath to relative URLS\n    return new URL(`${basePath}${redirectUrl}`, 'http://n')\n  }\n\n  const parsedRedirectUrl = new URL(redirectUrl)\n\n  if (host?.value !== parsedRedirectUrl.host) {\n    return null\n  }\n\n  // At this point the hosts are the same, just confirm we\n  // are routing to a path underneath the `basePath`\n  return parsedRedirectUrl.pathname.startsWith(basePath)\n    ? parsedRedirectUrl\n    : null\n}\n\nasync function createRedirectRenderResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  originalHost: Host,\n  redirectUrl: string,\n  redirectType: RedirectType,\n  basePath: string,\n  workStore: WorkStore\n) {\n  res.setHeader('x-action-redirect', `${redirectUrl};${redirectType}`)\n\n  // If we're redirecting to another route of this Next.js application, we'll\n  // try to stream the response from the other worker path. When that works,\n  // we can save an extra roundtrip and avoid a full page reload.\n  // When the redirect URL starts with a `/` or is to the same host, under the\n  // `basePath` we treat it as an app-relative redirect;\n  const appRelativeRedirectUrl = getAppRelativeRedirectUrl(\n    basePath,\n    originalHost,\n    redirectUrl\n  )\n\n  if (appRelativeRedirectUrl) {\n    if (!originalHost) {\n      throw new Error(\n        'Invariant: Missing `host` header from a forwarded Server Actions request.'\n      )\n    }\n\n    const forwardedHeaders = getForwardedHeaders(req, res)\n    forwardedHeaders.set(RSC_HEADER, '1')\n\n    const proto = workStore.incrementalCache?.requestProtocol || 'https'\n\n    // For standalone or the serverful mode, use the internal origin directly\n    // other than the host headers from the request.\n    const origin =\n      process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${originalHost.value}`\n\n    const fetchUrl = new URL(\n      `${origin}${appRelativeRedirectUrl.pathname}${appRelativeRedirectUrl.search}`\n    )\n\n    if (workStore.pendingRevalidatedTags) {\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n        workStore.pendingRevalidatedTags.join(',')\n      )\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n        workStore.incrementalCache?.prerenderManifest?.preview?.previewModeId ||\n          ''\n      )\n    }\n\n    // Ensures that when the path was revalidated we don't return a partial response on redirects\n    forwardedHeaders.delete(NEXT_ROUTER_STATE_TREE_HEADER)\n    // When an action follows a redirect, it's no longer handling an action: it's just a normal RSC request\n    // to the requested URL. We should remove the `next-action` header so that it's not treated as an action\n    forwardedHeaders.delete(ACTION_HEADER)\n\n    try {\n      const response = await fetch(fetchUrl, {\n        method: 'GET',\n        headers: forwardedHeaders,\n        next: {\n          // @ts-ignore\n          internal: 1,\n        },\n      })\n\n      if (\n        response.headers\n          .get('content-type')\n          ?.startsWith(RSC_CONTENT_TYPE_HEADER)\n      ) {\n        // copy the headers from the redirect response to the response we're sending\n        for (const [key, value] of response.headers) {\n          if (!actionsForbiddenHeaders.includes(key)) {\n            res.setHeader(key, value)\n          }\n        }\n\n        return new FlightRenderResult(response.body!)\n      } else {\n        // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n        response.body?.cancel()\n      }\n    } catch (err) {\n      // we couldn't stream the redirect response, so we'll just do a normal redirect\n      console.error(`failed to get redirect response`, err)\n    }\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n// Used to compare Host header and Origin header.\nconst enum HostType {\n  XForwardedHost = 'x-forwarded-host',\n  Host = 'host',\n}\ntype Host =\n  | {\n      type: HostType.XForwardedHost\n      value: string\n    }\n  | {\n      type: HostType.Host\n      value: string\n    }\n  | undefined\n\n/**\n * Ensures the value of the header can't create long logs.\n */\nfunction limitUntrustedHeaderValueForLogs(value: string) {\n  return value.length > 100 ? value.slice(0, 100) + '...' : value\n}\n\nexport function parseHostHeader(\n  headers: IncomingHttpHeaders,\n  originDomain?: string\n) {\n  const forwardedHostHeader = headers['x-forwarded-host']\n  const forwardedHostHeaderValue =\n    forwardedHostHeader && Array.isArray(forwardedHostHeader)\n      ? forwardedHostHeader[0]\n      : forwardedHostHeader?.split(',')?.[0]?.trim()\n  const hostHeader = headers['host']\n\n  if (originDomain) {\n    return forwardedHostHeaderValue === originDomain\n      ? {\n          type: HostType.XForwardedHost,\n          value: forwardedHostHeaderValue,\n        }\n      : hostHeader === originDomain\n        ? {\n            type: HostType.Host,\n            value: hostHeader,\n          }\n        : undefined\n  }\n\n  return forwardedHostHeaderValue\n    ? {\n        type: HostType.XForwardedHost,\n        value: forwardedHostHeaderValue,\n      }\n    : hostHeader\n      ? {\n          type: HostType.Host,\n          value: hostHeader,\n        }\n      : undefined\n}\n\ntype ServerModuleMap = Record<\n  string,\n  {\n    id: string\n    chunks: string[]\n    name: string\n  }\n>\n\ntype ServerActionsConfig = {\n  bodySizeLimit?: SizeLimit\n  allowedOrigins?: string[]\n}\n\nexport async function handleAction({\n  req,\n  res,\n  ComponentMod,\n  serverModuleMap,\n  generateFlight,\n  workStore,\n  requestStore,\n  serverActions,\n  ctx,\n}: {\n  req: BaseNextRequest\n  res: BaseNextResponse\n  ComponentMod: AppPageModule\n  serverModuleMap: ServerModuleMap\n  generateFlight: GenerateFlight\n  workStore: WorkStore\n  requestStore: RequestStore\n  serverActions?: ServerActionsConfig\n  ctx: AppRenderContext\n}): Promise<\n  | undefined\n  | {\n      type: 'not-found'\n    }\n  | {\n      type: 'done'\n      result: RenderResult | undefined\n      formState?: any\n    }\n> {\n  const contentType = req.headers['content-type']\n  const { serverActionsManifest, page } = ctx.renderOpts\n\n  const {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  } = getServerActionRequestMetadata(req)\n\n  // If it can't be a Server Action, skip handling.\n  // Note that this can be a false positive -- any multipart/urlencoded POST can get us here,\n  // But won't know if it's an MPA action or not until we call `decodeAction` below.\n  if (!isPossibleServerAction) {\n    return\n  }\n\n  if (workStore.isStaticGeneration) {\n    throw new Error(\n      \"Invariant: server actions can't be handled during static rendering\"\n    )\n  }\n\n  let temporaryReferences: TemporaryReferenceSet | undefined\n\n  const finalizeAndGenerateFlight: GenerateFlight = (...args) => {\n    // When we switch to the render phase, cookies() will return\n    // `workUnitStore.cookies` instead of `workUnitStore.userspaceMutableCookies`.\n    // We want the render to see any cookie writes that we performed during the action,\n    // so we need to update the immutable cookies to reflect the changes.\n    synchronizeMutableCookies(requestStore)\n\n    // The server action might have toggled draft mode, so we need to reflect\n    // that in the work store to be up-to-date for subsequent rendering.\n    workStore.isDraftMode = requestStore.draftMode.isEnabled\n\n    return generateFlight(...args)\n  }\n\n  // When running actions the default is no-store, you can still `cache: 'force-cache'`\n  workStore.fetchCache = 'default-no-store'\n\n  const originDomain =\n    typeof req.headers['origin'] === 'string'\n      ? new URL(req.headers['origin']).host\n      : undefined\n  const host = parseHostHeader(req.headers)\n\n  let warning: string | undefined = undefined\n\n  function warnBadServerActionRequest() {\n    if (warning) {\n      warn(warning)\n    }\n  }\n  // This is to prevent CSRF attacks. If `x-forwarded-host` is set, we need to\n  // ensure that the request is coming from the same host.\n  if (!originDomain) {\n    // This might be an old browser that doesn't send `host` header. We ignore\n    // this case.\n    warning = 'Missing `origin` header from a forwarded Server Actions request.'\n  } else if (!host || originDomain !== host.value) {\n    // If the customer sets a list of allowed origins, we'll allow the request.\n    // These are considered safe but might be different from forwarded host set\n    // by the infra (i.e. reverse proxies).\n    if (isCsrfOriginAllowed(originDomain, serverActions?.allowedOrigins)) {\n      // Ignore it\n    } else {\n      if (host) {\n        // This seems to be an CSRF attack. We should not proceed the action.\n        console.error(\n          `\\`${\n            host.type\n          }\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            host.value\n          )}\\` does not match \\`origin\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            originDomain\n          )}\\` from a forwarded Server Actions request. Aborting the action.`\n        )\n      } else {\n        // This is an attack. We should not proceed the action.\n        console.error(\n          `\\`x-forwarded-host\\` or \\`host\\` headers are not provided. One of these is needed to compare the \\`origin\\` header from a forwarded Server Actions request. Aborting the action.`\n        )\n      }\n\n      const error = new Error('Invalid Server Actions request.')\n\n      if (isFetchAction) {\n        res.statusCode = 500\n        await executeRevalidates(workStore)\n\n        const promise = Promise.reject(error)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n\n        return {\n          type: 'done',\n          result: await finalizeAndGenerateFlight(req, ctx, requestStore, {\n            actionResult: promise,\n            // if the page was not revalidated, we can skip the rendering the flight tree\n            skipFlight: !workStore.pathWasRevalidated,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      throw error\n    }\n  }\n\n  // ensure we avoid caching server actions unexpectedly\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  let boundActionArguments: unknown[] = []\n\n  const { actionAsyncStorage } = ComponentMod\n\n  let actionResult: RenderResult | undefined\n  let formState: any | undefined\n  let actionModId: string | undefined\n  const actionWasForwarded = Boolean(req.headers['x-action-forwarded'])\n\n  if (actionId) {\n    const forwardedWorker = selectWorkerForForwarding(\n      actionId,\n      page,\n      serverActionsManifest\n    )\n\n    // If forwardedWorker is truthy, it means there isn't a worker for the action\n    // in the current handler, so we forward the request to a worker that has the action.\n    if (forwardedWorker) {\n      return {\n        type: 'done',\n        result: await createForwardedActionResponse(\n          req,\n          res,\n          host,\n          forwardedWorker,\n          ctx.renderOpts.basePath,\n          workStore\n        ),\n      }\n    }\n  }\n\n  try {\n    await actionAsyncStorage.run({ isAction: true }, async () => {\n      if (\n        // The type check here ensures that `req` is correctly typed, and the\n        // environment variable check provides dead code elimination.\n        process.env.NEXT_RUNTIME === 'edge' &&\n        isWebNextRequest(req)\n      ) {\n        if (!req.body) {\n          throw new Error('invariant: Missing request body.')\n        }\n\n        // TODO: add body limit\n\n        // Use react-server-dom-webpack/server.edge\n        const {\n          createTemporaryReferenceSet,\n          decodeReply,\n          decodeAction,\n          decodeFormState,\n        } = ComponentMod\n\n        temporaryReferences = createTemporaryReferenceSet()\n\n        if (isMultipartAction) {\n          // TODO-APP: Add streaming support\n          const formData = await req.request.formData()\n          if (isFetchAction) {\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            const action = await decodeAction(formData, serverModuleMap)\n            if (typeof action === 'function') {\n              // Only warn if it's a server action, otherwise skip for other post requests\n              warnBadServerActionRequest()\n\n              let actionReturnedState: unknown\n              requestStore.phase = 'action'\n              try {\n                actionReturnedState = await workUnitAsyncStorage.run(\n                  requestStore,\n                  action\n                )\n              } finally {\n                requestStore.phase = 'render'\n              }\n\n              formState = await decodeFormState(\n                actionReturnedState,\n                formData,\n                serverModuleMap\n              )\n            }\n\n            // Skip the fetch path\n            return\n          }\n        } else {\n          try {\n            actionModId = getActionModIdOrError(actionId, serverModuleMap)\n          } catch (err) {\n            if (actionId !== null) {\n              console.error(err)\n            }\n            return {\n              type: 'not-found',\n            }\n          }\n\n          const chunks: Buffer[] = []\n          const reader = req.body.getReader()\n          while (true) {\n            const { done, value } = await reader.read()\n            if (done) {\n              break\n            }\n\n            chunks.push(value)\n          }\n\n          const actionData = Buffer.concat(chunks).toString('utf-8')\n\n          if (isURLEncodedAction) {\n            const formData = formDataFromSearchQueryString(actionData)\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            boundActionArguments = await decodeReply(\n              actionData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          }\n        }\n      } else if (\n        // The type check here ensures that `req` is correctly typed, and the\n        // environment variable check provides dead code elimination.\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        isNodeNextRequest(req)\n      ) {\n        // Use react-server-dom-webpack/server.node which supports streaming\n        const {\n          createTemporaryReferenceSet,\n          decodeReply,\n          decodeReplyFromBusboy,\n          decodeAction,\n          decodeFormState,\n        } = require(\n          `./react-server.node`\n        ) as typeof import('./react-server.node')\n\n        temporaryReferences = createTemporaryReferenceSet()\n\n        const { Transform } =\n          require('node:stream') as typeof import('node:stream')\n\n        const defaultBodySizeLimit = '1 MB'\n        const bodySizeLimit =\n          serverActions?.bodySizeLimit ?? defaultBodySizeLimit\n        const bodySizeLimitBytes =\n          bodySizeLimit !== defaultBodySizeLimit\n            ? (\n                require('next/dist/compiled/bytes') as typeof import('bytes')\n              ).parse(bodySizeLimit)\n            : 1024 * 1024 // 1 MB\n\n        let size = 0\n        const body = req.body.pipe(\n          new Transform({\n            transform(chunk, encoding, callback) {\n              size += Buffer.byteLength(chunk, encoding)\n              if (size > bodySizeLimitBytes) {\n                const { ApiError } = require('../api-utils')\n\n                callback(\n                  new ApiError(\n                    413,\n                    `Body exceeded ${bodySizeLimit} limit.\n                To configure the body size limit for Server Actions, see: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit`\n                  )\n                )\n                return\n              }\n\n              callback(null, chunk)\n            },\n          })\n        )\n\n        if (isMultipartAction) {\n          if (isFetchAction) {\n            const busboy = (require('busboy') as typeof import('busboy'))({\n              defParamCharset: 'utf8',\n              headers: req.headers,\n              limits: { fieldSize: bodySizeLimitBytes },\n            })\n\n            body.pipe(busboy)\n\n            boundActionArguments = await decodeReplyFromBusboy(\n              busboy,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            // React doesn't yet publish a busboy version of decodeAction\n            // so we polyfill the parsing of FormData.\n            const fakeRequest = new Request('http://localhost', {\n              method: 'POST',\n              // @ts-expect-error\n              headers: { 'Content-Type': contentType },\n              body: new ReadableStream({\n                start: (controller) => {\n                  body.on('data', (chunk) => {\n                    controller.enqueue(new Uint8Array(chunk))\n                  })\n                  body.on('end', () => {\n                    controller.close()\n                  })\n                  body.on('error', (err) => {\n                    controller.error(err)\n                  })\n                },\n              }),\n              duplex: 'half',\n            })\n            const formData = await fakeRequest.formData()\n            const action = await decodeAction(formData, serverModuleMap)\n            if (typeof action === 'function') {\n              // Only warn if it's a server action, otherwise skip for other post requests\n              warnBadServerActionRequest()\n\n              let actionReturnedState: unknown\n              requestStore.phase = 'action'\n              try {\n                actionReturnedState = await workUnitAsyncStorage.run(\n                  requestStore,\n                  action\n                )\n              } finally {\n                requestStore.phase = 'render'\n              }\n\n              formState = await decodeFormState(\n                actionReturnedState,\n                formData,\n                serverModuleMap\n              )\n            }\n\n            // Skip the fetch path\n            return\n          }\n        } else {\n          try {\n            actionModId = getActionModIdOrError(actionId, serverModuleMap)\n          } catch (err) {\n            if (actionId !== null) {\n              console.error(err)\n            }\n            return {\n              type: 'not-found',\n            }\n          }\n\n          const chunks: Buffer[] = []\n          for await (const chunk of req.body) {\n            chunks.push(Buffer.from(chunk))\n          }\n\n          const actionData = Buffer.concat(chunks).toString('utf-8')\n\n          if (isURLEncodedAction) {\n            const formData = formDataFromSearchQueryString(actionData)\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            boundActionArguments = await decodeReply(\n              actionData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          }\n        }\n      } else {\n        throw new Error('Invariant: Unknown request type.')\n      }\n\n      // actions.js\n      // app/page.js\n      //   action worker1\n      //     appRender1\n\n      // app/foo/page.js\n      //   action worker2\n      //     appRender\n\n      // / -> fire action -> POST / -> appRender1 -> modId for the action file\n      // /foo -> fire action -> POST /foo -> appRender2 -> modId for the action file\n\n      try {\n        actionModId =\n          actionModId ?? getActionModIdOrError(actionId, serverModuleMap)\n      } catch (err) {\n        if (actionId !== null) {\n          console.error(err)\n        }\n        return {\n          type: 'not-found',\n        }\n      }\n\n      const actionMod = (await ComponentMod.__next_app__.require(\n        actionModId\n      )) as Record<string, (...args: unknown[]) => Promise<unknown>>\n      const actionHandler =\n        actionMod[\n          // `actionId` must exist if we got here, as otherwise we would have thrown an error above\n          actionId!\n        ]\n\n      let returnVal: unknown\n      requestStore.phase = 'action'\n      try {\n        returnVal = await workUnitAsyncStorage.run(requestStore, () =>\n          actionHandler.apply(null, boundActionArguments)\n        )\n      } finally {\n        requestStore.phase = 'render'\n      }\n\n      // For form actions, we need to continue rendering the page.\n      if (isFetchAction) {\n        await executeRevalidates(workStore)\n        addRevalidationHeader(res, { workStore, requestStore })\n\n        actionResult = await finalizeAndGenerateFlight(req, ctx, requestStore, {\n          actionResult: Promise.resolve(returnVal),\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        })\n      }\n    })\n\n    return {\n      type: 'done',\n      result: actionResult,\n      formState,\n    }\n  } catch (err) {\n    if (isRedirectError(err)) {\n      const redirectUrl = getURLFromRedirectError(err)\n      const redirectType = getRedirectTypeFromError(err)\n\n      await executeRevalidates(workStore)\n      addRevalidationHeader(res, { workStore, requestStore })\n\n      // if it's a fetch action, we'll set the status code for logging/debugging purposes\n      // but we won't set a Location header, as the redirect will be handled by the client router\n      res.statusCode = RedirectStatusCode.SeeOther\n\n      if (isFetchAction) {\n        return {\n          type: 'done',\n          result: await createRedirectRenderResult(\n            req,\n            res,\n            host,\n            redirectUrl,\n            redirectType,\n            ctx.renderOpts.basePath,\n            workStore\n          ),\n        }\n      }\n\n      res.setHeader('Location', redirectUrl)\n      return {\n        type: 'done',\n        result: RenderResult.fromStatic(''),\n      }\n    } else if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n\n      await executeRevalidates(workStore)\n      addRevalidationHeader(res, { workStore, requestStore })\n\n      if (isFetchAction) {\n        const promise = Promise.reject(err)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n        return {\n          type: 'done',\n          result: await finalizeAndGenerateFlight(req, ctx, requestStore, {\n            skipFlight: false,\n            actionResult: promise,\n            temporaryReferences,\n          }),\n        }\n      }\n      return {\n        type: 'not-found',\n      }\n    }\n\n    if (isFetchAction) {\n      res.statusCode = 500\n      await executeRevalidates(workStore)\n      const promise = Promise.reject(err)\n      try {\n        // we need to await the promise to trigger the rejection early\n        // so that it's already handled by the time we call\n        // the RSC runtime. Otherwise, it will throw an unhandled\n        // promise rejection error in the renderer.\n        await promise\n      } catch {\n        // swallow error, it's gonna be handled on the client\n      }\n\n      return {\n        type: 'done',\n        result: await generateFlight(req, ctx, requestStore, {\n          actionResult: promise,\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        }),\n      }\n    }\n\n    throw err\n  }\n}\n\n/**\n * Attempts to find the module ID for the action from the module map. When this fails, it could be a deployment skew where\n * the action came from a different deployment. It could also simply be an invalid POST request that is not a server action.\n * In either case, we'll throw an error to be handled by the caller.\n */\nfunction getActionModIdOrError(\n  actionId: string | null,\n  serverModuleMap: ServerModuleMap\n): string {\n  // if we're missing the action ID header, we can't do any further processing\n  if (!actionId) {\n    throw new InvariantError(\"Missing 'next-action' header.\")\n  }\n\n  const actionModId = serverModuleMap[actionId]?.id\n\n  if (!actionModId) {\n    throw new Error(\n      `Failed to find Server Action \"${actionId}\". This request might be from an older or newer deployment.\\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n\n  return actionModId\n}\n"], "names": ["RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "ACTION_HEADER", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RenderResult", "FlightRenderResult", "filterReqHeaders", "actionsForbiddenHeaders", "getModifiedCookieValues", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "getServerActionRequestMetadata", "isCsrfOriginAllowed", "warn", "RequestCookies", "ResponseCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fromNodeOutgoingHttpHeaders", "selectWorkerForForwarding", "isNodeNextRequest", "isWebNextRequest", "RedirectStatusCode", "synchronizeMutableCookies", "workUnitAsyncStorage", "InvariantError", "executeRevalidates", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "from", "responseHeaders", "getHeaders", "responseCookies", "mergedHeaders", "getAll", "for<PERSON>ach", "cookie", "delete", "name", "set", "toString", "Headers", "addRevalidationHeader", "workStore", "requestStore", "isTagRevalidated", "pendingRevalidatedTags", "length", "isCookieRevalidated", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createForwardedActionResponse", "host", "workerPathname", "basePath", "Error", "forwardedHeaders", "proto", "incrementalCache", "requestProtocol", "origin", "process", "env", "__NEXT_PRIVATE_ORIGIN", "fetchUrl", "URL", "response", "body", "NEXT_RUNTIME", "stream", "fetch", "method", "duplex", "redirect", "next", "internal", "get", "startsWith", "includes", "cancel", "err", "console", "error", "fromStatic", "getAppRelativeRedirectUrl", "redirectUrl", "parsedRedirectUrl", "pathname", "createRedirectRenderResult", "originalHost", "redirectType", "appRelativeRedirectUrl", "search", "prerenderManifest", "preview", "previewModeId", "limitUntrustedHeaderValueForLogs", "slice", "parseHostHeader", "originDomain", "forwarded<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedHostHeaderValue", "split", "trim", "<PERSON><PERSON><PERSON><PERSON>", "type", "handleAction", "ComponentMod", "serverModuleMap", "generateFlight", "serverActions", "ctx", "contentType", "serverActionsManifest", "page", "renderOpts", "actionId", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "isPossibleServerAction", "isStaticGeneration", "temporaryReferences", "finalizeAndGenerateFlight", "args", "isDraftMode", "draftMode", "isEnabled", "fetchCache", "warning", "warnBadServerActionRequest", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "promise", "Promise", "reject", "result", "actionResult", "skipFlight", "pathWasRevalidated", "boundActionArguments", "actionAsyncStorage", "formState", "actionModId", "actionWasForwarded", "Boolean", "forwarded<PERSON><PERSON><PERSON>", "run", "isAction", "createTemporaryReferenceSet", "decodeReply", "decodeAction", "decodeFormState", "request", "action", "actionReturnedState", "phase", "getActionModIdOrError", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "push", "actionData", "<PERSON><PERSON><PERSON>", "concat", "decodeReplyFromBusboy", "require", "Transform", "defaultBodySizeLimit", "bodySizeLimit", "bodySizeLimitBytes", "parse", "size", "pipe", "transform", "chunk", "encoding", "callback", "byteLength", "ApiError", "busboy", "def<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limits", "fieldSize", "fakeRequest", "Request", "ReadableStream", "start", "controller", "on", "enqueue", "Uint8Array", "close", "actionMod", "__next_app__", "actionHandler", "returnVal", "apply", "resolve", "<PERSON><PERSON><PERSON>", "id"], "mappings": "AAOA,SACEA,UAAU,EACVC,uBAAuB,EACvBC,6BAA6B,EAC7BC,aAAa,QACR,6CAA4C;AACnD,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,oEAAmE;AAC1E,SACEC,wBAAwB,EACxBC,uBAAuB,QAClB,mCAAkC;AACzC,SACEC,eAAe,QAEV,yCAAwC;AAC/C,OAAOC,kBAAkB,mBAAkB;AAE3C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,gBAAgB,EAChBC,uBAAuB,QAClB,0BAAyB;AAChC,SAASC,uBAAuB,QAAQ,iDAAgD;AAExF,SACEC,kCAAkC,EAClCC,sCAAsC,QACjC,sBAAqB;AAC5B,SAASC,8BAA8B,QAAQ,oCAAmC;AAClF,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,SAASC,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,cAAc,EAAEC,eAAe,QAAQ,gCAA+B;AAC/E,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SAASC,2BAA2B,QAAQ,eAAc;AAC1D,SAASC,yBAAyB,QAAQ,iBAAgB;AAC1D,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,uBAAsB;AAC1E,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,yBAAyB,QAAQ,iCAAgC;AAE1E,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,kBAAkB,QAAQ,wBAAuB;AAE1D,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,GAAGV,OAAO;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAqB;IAErB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiB,IAAIlC,eAAeE,eAAeiC,IAAI,CAACF;IAE9D,mCAAmC;IACnC,MAAMG,kBAAkBJ,IAAIK,UAAU;IACtC,MAAMC,kBAAkB,IAAIrC,gBAC1BE,4BAA4BiC;IAG9B,qCAAqC;IACrC,MAAMG,gBAAgB/C,iBACpB;QACE,GAAG6B,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBe,gBAAgB;IACzC,GACA3C;IAGF,+EAA+E;IAC/E,kDAAkD;IAClD6C,gBAAgBE,MAAM,GAAGC,OAAO,CAAC,CAACC;QAChC,IAAI,OAAOA,OAAOvB,KAAK,KAAK,aAAa;YACvCe,eAAeS,MAAM,CAACD,OAAOE,IAAI;QACnC,OAAO;YACLV,eAAeW,GAAG,CAACH;QACrB;IACF;IAEA,qDAAqD;IACrDH,aAAa,CAAC,SAAS,GAAGL,eAAeY,QAAQ;IAEjD,8CAA8C;IAC9C,OAAOP,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIQ,QAAQR;AACrB;AAEA,SAASS,sBACPhB,GAAqB,EACrB,EACEiB,SAAS,EACTC,YAAY,EAIb;QAewBD;IAbzB,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAME,mBAAmBF,EAAAA,oCAAAA,UAAUG,sBAAsB,qBAAhCH,kCAAkCI,MAAM,IAAG,IAAI;IACxE,MAAMC,sBAAsB5D,wBAC1BwD,aAAaK,cAAc,EAC3BF,MAAM,GACJ,IACA;IAEJrB,IAAIwB,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAEP;QAAkBG;KAAoB;AAE9D;AAEA;;CAEC,GACD,eAAeK,8BACb5B,GAAoB,EACpBC,GAAqB,EACrB4B,IAAU,EACVC,cAAsB,EACtBC,QAAgB,EAChBb,SAAoB;QAeNA;IAbd,IAAI,CAACW,MAAM;QACT,MAAM,qBAEL,CAFK,IAAIG,MACR,8EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,mBAAmBlC,oBAAoBC,KAAKC;IAElD,sEAAsE;IACtE,+EAA+E;IAC/E,8CAA8C;IAC9CgC,iBAAiBnB,GAAG,CAAC,sBAAsB;IAE3C,MAAMoB,QAAQhB,EAAAA,8BAAAA,UAAUiB,gBAAgB,qBAA1BjB,4BAA4BkB,eAAe,KAAI;IAE7D,yEAAyE;IACzE,gDAAgD;IAChD,MAAMC,SAASC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGN,MAAM,GAAG,EAAEL,KAAKzC,KAAK,EAAE;IAE9E,MAAMqD,WAAW,IAAIC,IAAI,GAAGL,SAASN,WAAWD,gBAAgB;IAEhE,IAAI;YAsCAa;QArCF,IAAIC;QACJ,IACE,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BtE,iBAAiByB,MACjB;YACA,IAAI,CAACA,IAAI4C,IAAI,EAAE;gBACb,MAAM,qBAA6C,CAA7C,IAAIZ,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEAY,OAAO5C,IAAI4C,IAAI;QACjB,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BvE,kBAAkB0B,MAClB;YACA4C,OAAO5C,IAAI8C,MAAM;QACnB,OAAO;YACL,MAAM,qBAA6C,CAA7C,IAAId,MAAM,qCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA4C;QACpD;QAEA,wCAAwC;QACxC,MAAMW,WAAW,MAAMI,MAAMN,UAAU;YACrCO,QAAQ;YACRJ;YACAK,QAAQ;YACR1D,SAAS0C;YACTiB,UAAU;YACVC,MAAM;gBACJ,aAAa;gBACbC,UAAU;YACZ;QACF;QAEA,KACET,wBAAAA,SAASpD,OAAO,CAAC8D,GAAG,CAAC,oCAArBV,sBAAsCW,UAAU,CAACvG,0BACjD;YACA,4EAA4E;YAC5E,KAAK,MAAM,CAACoC,KAAKC,MAAM,IAAIuD,SAASpD,OAAO,CAAE;gBAC3C,IAAI,CAAC7B,wBAAwB6F,QAAQ,CAACpE,MAAM;oBAC1Cc,IAAIwB,SAAS,CAACtC,KAAKC;gBACrB;YACF;YAEA,OAAO,IAAI5B,mBAAmBmF,SAASC,IAAI;QAC7C,OAAO;gBACL,kFAAkF;YAClFD;aAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAea,MAAM;QACvB;IACF,EAAE,OAAOC,KAAK;QACZ,oFAAoF;QACpFC,QAAQC,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAEF;IACrD;IAEA,OAAOlG,aAAaqG,UAAU,CAAC;AACjC;AAEA;;;;;;CAMC,GACD,SAASC,0BACP9B,QAAgB,EAChBF,IAAU,EACViC,WAAmB;IAEnB,IAAIA,YAAYR,UAAU,CAAC,QAAQQ,YAAYR,UAAU,CAAC,MAAM;QAC9D,2DAA2D;QAC3D,OAAO,IAAIZ,IAAI,GAAGX,WAAW+B,aAAa,EAAE;IAC9C;IAEA,MAAMC,oBAAoB,IAAIrB,IAAIoB;IAElC,IAAIjC,CAAAA,wBAAAA,KAAMzC,KAAK,MAAK2E,kBAAkBlC,IAAI,EAAE;QAC1C,OAAO;IACT;IAEA,wDAAwD;IACxD,kDAAkD;IAClD,OAAOkC,kBAAkBC,QAAQ,CAACV,UAAU,CAACvB,YACzCgC,oBACA;AACN;AAEA,eAAeE,2BACbjE,GAAoB,EACpBC,GAAqB,EACrBiE,YAAkB,EAClBJ,WAAmB,EACnBK,YAA0B,EAC1BpC,QAAgB,EAChBb,SAAoB;IAEpBjB,IAAIwB,SAAS,CAAC,qBAAqB,GAAGqC,YAAY,CAAC,EAAEK,cAAc;IAEnE,2EAA2E;IAC3E,0EAA0E;IAC1E,+DAA+D;IAC/D,4EAA4E;IAC5E,sDAAsD;IACtD,MAAMC,yBAAyBP,0BAC7B9B,UACAmC,cACAJ;IAGF,IAAIM,wBAAwB;YAUZlD;QATd,IAAI,CAACgD,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAIlC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,mBAAmBlC,oBAAoBC,KAAKC;QAClDgC,iBAAiBnB,GAAG,CAAChE,YAAY;QAEjC,MAAMoF,QAAQhB,EAAAA,8BAAAA,UAAUiB,gBAAgB,qBAA1BjB,4BAA4BkB,eAAe,KAAI;QAE7D,yEAAyE;QACzE,gDAAgD;QAChD,MAAMC,SACJC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGN,MAAM,GAAG,EAAEgC,aAAa9E,KAAK,EAAE;QAEzE,MAAMqD,WAAW,IAAIC,IACnB,GAAGL,SAAS+B,uBAAuBJ,QAAQ,GAAGI,uBAAuBC,MAAM,EAAE;QAG/E,IAAInD,UAAUG,sBAAsB,EAAE;gBAOlCH,uDAAAA,+CAAAA;YANFe,iBAAiBnB,GAAG,CAClBlD,oCACAsD,UAAUG,sBAAsB,CAACvB,IAAI,CAAC;YAExCmC,iBAAiBnB,GAAG,CAClBjD,wCACAqD,EAAAA,+BAAAA,UAAUiB,gBAAgB,sBAA1BjB,gDAAAA,6BAA4BoD,iBAAiB,sBAA7CpD,wDAAAA,8CAA+CqD,OAAO,qBAAtDrD,sDAAwDsD,aAAa,KACnE;QAEN;QAEA,6FAA6F;QAC7FvC,iBAAiBrB,MAAM,CAAC5D;QACxB,uGAAuG;QACvG,wGAAwG;QACxGiF,iBAAiBrB,MAAM,CAAC3D;QAExB,IAAI;gBAWA0F;YAVF,MAAMA,WAAW,MAAMI,MAAMN,UAAU;gBACrCO,QAAQ;gBACRzD,SAAS0C;gBACTkB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,KACET,wBAAAA,SAASpD,OAAO,CACb8D,GAAG,CAAC,oCADPV,sBAEIW,UAAU,CAACvG,0BACf;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAACoC,KAAKC,MAAM,IAAIuD,SAASpD,OAAO,CAAE;oBAC3C,IAAI,CAAC7B,wBAAwB6F,QAAQ,CAACpE,MAAM;wBAC1Cc,IAAIwB,SAAS,CAACtC,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAI5B,mBAAmBmF,SAASC,IAAI;YAC7C,OAAO;oBACL,kFAAkF;gBAClFD;iBAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAea,MAAM;YACvB;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IAEA,OAAOlG,aAAaqG,UAAU,CAAC;AACjC;;AAkBA;;CAEC,GACD,SAASa,iCAAiCrF,KAAa;IACrD,OAAOA,MAAMkC,MAAM,GAAG,MAAMlC,MAAMsF,KAAK,CAAC,GAAG,OAAO,QAAQtF;AAC5D;AAEA,OAAO,SAASuF,gBACdpF,OAA4B,EAC5BqF,YAAqB;QAMfC,6BAAAA;IAJN,MAAMA,sBAAsBtF,OAAO,CAAC,mBAAmB;IACvD,MAAMuF,2BACJD,uBAAuBjF,MAAMC,OAAO,CAACgF,uBACjCA,mBAAmB,CAAC,EAAE,GACtBA,wCAAAA,6BAAAA,oBAAqBE,KAAK,CAAC,0BAA3BF,8BAAAA,0BAAiC,CAAC,EAAE,qBAApCA,4BAAsCG,IAAI;IAChD,MAAMC,aAAa1F,OAAO,CAAC,OAAO;IAElC,IAAIqF,cAAc;QAChB,OAAOE,6BAA6BF,eAChC;YACEM,IAAI;YACJ9F,OAAO0F;QACT,IACAG,eAAeL,eACb;YACEM,IAAI;YACJ9F,OAAO6F;QACT,IACAtF;IACR;IAEA,OAAOmF,2BACH;QACEI,IAAI;QACJ9F,OAAO0F;IACT,IACAG,aACE;QACEC,IAAI;QACJ9F,OAAO6F;IACT,IACAtF;AACR;AAgBA,OAAO,eAAewF,aAAa,EACjCnF,GAAG,EACHC,GAAG,EACHmF,YAAY,EACZC,eAAe,EACfC,cAAc,EACdpE,SAAS,EACTC,YAAY,EACZoE,aAAa,EACbC,GAAG,EAWJ;IAWC,MAAMC,cAAczF,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAM,EAAEmG,qBAAqB,EAAEC,IAAI,EAAE,GAAGH,IAAII,UAAU;IAEtD,MAAM,EACJC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EACbC,sBAAsB,EACvB,GAAGnI,+BAA+BkC;IAEnC,iDAAiD;IACjD,2FAA2F;IAC3F,kFAAkF;IAClF,IAAI,CAACiG,wBAAwB;QAC3B;IACF;IAEA,IAAI/E,UAAUgF,kBAAkB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIlE,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAImE;IAEJ,MAAMC,4BAA4C,CAAC,GAAGC;QACpD,4DAA4D;QAC5D,8EAA8E;QAC9E,mFAAmF;QACnF,qEAAqE;QACrE5H,0BAA0B0C;QAE1B,yEAAyE;QACzE,oEAAoE;QACpED,UAAUoF,WAAW,GAAGnF,aAAaoF,SAAS,CAACC,SAAS;QAExD,OAAOlB,kBAAkBe;IAC3B;IAEA,qFAAqF;IACrFnF,UAAUuF,UAAU,GAAG;IAEvB,MAAM7B,eACJ,OAAO5E,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAImD,IAAI1C,IAAIT,OAAO,CAAC,SAAS,EAAEsC,IAAI,GACnClC;IACN,MAAMkC,OAAO8C,gBAAgB3E,IAAIT,OAAO;IAExC,IAAImH,UAA8B/G;IAElC,SAASgH;QACP,IAAID,SAAS;YACX1I,KAAK0I;QACP;IACF;IACA,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAAC9B,cAAc;QACjB,0EAA0E;QAC1E,aAAa;QACb8B,UAAU;IACZ,OAAO,IAAI,CAAC7E,QAAQ+C,iBAAiB/C,KAAKzC,KAAK,EAAE;QAC/C,2EAA2E;QAC3E,2EAA2E;QAC3E,uCAAuC;QACvC,IAAIrB,oBAAoB6G,cAAcW,iCAAAA,cAAeqB,cAAc,GAAG;QACpE,YAAY;QACd,OAAO;YACL,IAAI/E,MAAM;gBACR,qEAAqE;gBACrE6B,QAAQC,KAAK,CACX,CAAC,EAAE,EACD9B,KAAKqD,IAAI,CACV,uBAAuB,EAAET,iCACxB5C,KAAKzC,KAAK,EACV,iDAAiD,EAAEqF,iCACnDG,cACA,gEAAgE,CAAC;YAEvE,OAAO;gBACL,uDAAuD;gBACvDlB,QAAQC,KAAK,CACX,CAAC,gLAAgL,CAAC;YAEtL;YAEA,MAAMA,QAAQ,qBAA4C,CAA5C,IAAI3B,MAAM,oCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA2C;YAEzD,IAAIgE,eAAe;gBACjB/F,IAAI4G,UAAU,GAAG;gBACjB,MAAMjI,mBAAmBsC;gBAEzB,MAAM4F,UAAUC,QAAQC,MAAM,CAACrD;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAMmD;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBAEA,OAAO;oBACL5B,MAAM;oBACN+B,QAAQ,MAAMb,0BAA0BpG,KAAKwF,KAAKrE,cAAc;wBAC9D+F,cAAcJ;wBACd,6EAA6E;wBAC7EK,YAAY,CAACjG,UAAUkG,kBAAkB;wBACzCjB;oBACF;gBACF;YACF;YAEA,MAAMxC;QACR;IACF;IAEA,sDAAsD;IACtD1D,IAAIwB,SAAS,CACX,iBACA;IAGF,IAAI4F,uBAAkC,EAAE;IAExC,MAAM,EAAEC,kBAAkB,EAAE,GAAGlC;IAE/B,IAAI8B;IACJ,IAAIK;IACJ,IAAIC;IACJ,MAAMC,qBAAqBC,QAAQ1H,IAAIT,OAAO,CAAC,qBAAqB;IAEpE,IAAIsG,UAAU;QACZ,MAAM8B,kBAAkBtJ,0BACtBwH,UACAF,MACAD;QAGF,6EAA6E;QAC7E,qFAAqF;QACrF,IAAIiC,iBAAiB;YACnB,OAAO;gBACLzC,MAAM;gBACN+B,QAAQ,MAAMrF,8BACZ5B,KACAC,KACA4B,MACA8F,iBACAnC,IAAII,UAAU,CAAC7D,QAAQ,EACvBb;YAEJ;QACF;IACF;IAEA,IAAI;QACF,MAAMoG,mBAAmBM,GAAG,CAAC;YAAEC,UAAU;QAAK,GAAG;YAC/C,IACE,qEAAqE;YACrE,6DAA6D;YAC7DvF,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BtE,iBAAiByB,MACjB;gBACA,IAAI,CAACA,IAAI4C,IAAI,EAAE;oBACb,MAAM,qBAA6C,CAA7C,IAAIZ,MAAM,qCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;gBAEA,uBAAuB;gBAEvB,2CAA2C;gBAC3C,MAAM,EACJ8F,2BAA2B,EAC3BC,WAAW,EACXC,YAAY,EACZC,eAAe,EAChB,GAAG7C;gBAEJe,sBAAsB2B;gBAEtB,IAAI/B,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAM9G,WAAW,MAAMe,IAAIkI,OAAO,CAACjJ,QAAQ;oBAC3C,IAAI+G,eAAe;wBACjBqB,uBAAuB,MAAMU,YAC3B9I,UACAoG,iBACA;4BAAEc;wBAAoB;oBAE1B,OAAO;wBACL,MAAMgC,SAAS,MAAMH,aAAa/I,UAAUoG;wBAC5C,IAAI,OAAO8C,WAAW,YAAY;4BAChC,4EAA4E;4BAC5ExB;4BAEA,IAAIyB;4BACJjH,aAAakH,KAAK,GAAG;4BACrB,IAAI;gCACFD,sBAAsB,MAAM1J,qBAAqBkJ,GAAG,CAClDzG,cACAgH;4BAEJ,SAAU;gCACRhH,aAAakH,KAAK,GAAG;4BACvB;4BAEAd,YAAY,MAAMU,gBAChBG,qBACAnJ,UACAoG;wBAEJ;wBAEA,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI;wBACFmC,cAAcc,sBAAsBzC,UAAUR;oBAChD,EAAE,OAAO5B,KAAK;wBACZ,IAAIoC,aAAa,MAAM;4BACrBnC,QAAQC,KAAK,CAACF;wBAChB;wBACA,OAAO;4BACLyB,MAAM;wBACR;oBACF;oBAEA,MAAMqD,SAAmB,EAAE;oBAC3B,MAAMC,SAASxI,IAAI4C,IAAI,CAAC6F,SAAS;oBACjC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAEtJ,KAAK,EAAE,GAAG,MAAMoJ,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,OAAOK,IAAI,CAACxJ;oBACd;oBAEA,MAAMyJ,aAAaC,OAAOC,MAAM,CAACR,QAAQxH,QAAQ,CAAC;oBAElD,IAAI+E,oBAAoB;wBACtB,MAAM7G,WAAWJ,8BAA8BgK;wBAC/CxB,uBAAuB,MAAMU,YAC3B9I,UACAoG,iBACA;4BAAEc;wBAAoB;oBAE1B,OAAO;wBACLkB,uBAAuB,MAAMU,YAC3Bc,YACAxD,iBACA;4BAAEc;wBAAoB;oBAE1B;gBACF;YACF,OAAO,IACL,qEAAqE;YACrE,6DAA6D;YAC7D7D,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BvE,kBAAkB0B,MAClB;gBACA,oEAAoE;gBACpE,MAAM,EACJ8H,2BAA2B,EAC3BC,WAAW,EACXiB,qBAAqB,EACrBhB,YAAY,EACZC,eAAe,EAChB,GAAGgB,QACF,CAAC,mBAAmB,CAAC;gBAGvB9C,sBAAsB2B;gBAEtB,MAAM,EAAEoB,SAAS,EAAE,GACjBD,QAAQ;gBAEV,MAAME,uBAAuB;gBAC7B,MAAMC,gBACJ7D,CAAAA,iCAAAA,cAAe6D,aAAa,KAAID;gBAClC,MAAME,qBACJD,kBAAkBD,uBACd,AACEF,QAAQ,4BACRK,KAAK,CAACF,iBACR,OAAO,KAAK,OAAO;;gBAEzB,IAAIG,OAAO;gBACX,MAAM3G,OAAO5C,IAAI4C,IAAI,CAAC4G,IAAI,CACxB,IAAIN,UAAU;oBACZO,WAAUC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ;wBACjCL,QAAQT,OAAOe,UAAU,CAACH,OAAOC;wBACjC,IAAIJ,OAAOF,oBAAoB;4BAC7B,MAAM,EAAES,QAAQ,EAAE,GAAGb,QAAQ;4BAE7BW,SACE,qBAIC,CAJD,IAAIE,SACF,KACA,CAAC,cAAc,EAAEV,cAAc;8JAC2G,CAAC,GAH7I,qBAAA;uCAAA;4CAAA;8CAAA;4BAIA;4BAEF;wBACF;wBAEAQ,SAAS,MAAMF;oBACjB;gBACF;gBAGF,IAAI3D,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,MAAM+D,SAAS,AAACd,QAAQ,UAAsC;4BAC5De,iBAAiB;4BACjBzK,SAASS,IAAIT,OAAO;4BACpB0K,QAAQ;gCAAEC,WAAWb;4BAAmB;wBAC1C;wBAEAzG,KAAK4G,IAAI,CAACO;wBAEV1C,uBAAuB,MAAM2B,sBAC3Be,QACA1E,iBACA;4BAAEc;wBAAoB;oBAE1B,OAAO;wBACL,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAMgE,cAAc,IAAIC,QAAQ,oBAAoB;4BAClDpH,QAAQ;4BACR,mBAAmB;4BACnBzD,SAAS;gCAAE,gBAAgBkG;4BAAY;4BACvC7C,MAAM,IAAIyH,eAAe;gCACvBC,OAAO,CAACC;oCACN3H,KAAK4H,EAAE,CAAC,QAAQ,CAACd;wCACfa,WAAWE,OAAO,CAAC,IAAIC,WAAWhB;oCACpC;oCACA9G,KAAK4H,EAAE,CAAC,OAAO;wCACbD,WAAWI,KAAK;oCAClB;oCACA/H,KAAK4H,EAAE,CAAC,SAAS,CAAC/G;wCAChB8G,WAAW5G,KAAK,CAACF;oCACnB;gCACF;4BACF;4BACAR,QAAQ;wBACV;wBACA,MAAMhE,WAAW,MAAMkL,YAAYlL,QAAQ;wBAC3C,MAAMkJ,SAAS,MAAMH,aAAa/I,UAAUoG;wBAC5C,IAAI,OAAO8C,WAAW,YAAY;4BAChC,4EAA4E;4BAC5ExB;4BAEA,IAAIyB;4BACJjH,aAAakH,KAAK,GAAG;4BACrB,IAAI;gCACFD,sBAAsB,MAAM1J,qBAAqBkJ,GAAG,CAClDzG,cACAgH;4BAEJ,SAAU;gCACRhH,aAAakH,KAAK,GAAG;4BACvB;4BAEAd,YAAY,MAAMU,gBAChBG,qBACAnJ,UACAoG;wBAEJ;wBAEA,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI;wBACFmC,cAAcc,sBAAsBzC,UAAUR;oBAChD,EAAE,OAAO5B,KAAK;wBACZ,IAAIoC,aAAa,MAAM;4BACrBnC,QAAQC,KAAK,CAACF;wBAChB;wBACA,OAAO;4BACLyB,MAAM;wBACR;oBACF;oBAEA,MAAMqD,SAAmB,EAAE;oBAC3B,WAAW,MAAMmB,SAAS1J,IAAI4C,IAAI,CAAE;wBAClC2F,OAAOK,IAAI,CAACE,OAAO1I,IAAI,CAACsJ;oBAC1B;oBAEA,MAAMb,aAAaC,OAAOC,MAAM,CAACR,QAAQxH,QAAQ,CAAC;oBAElD,IAAI+E,oBAAoB;wBACtB,MAAM7G,WAAWJ,8BAA8BgK;wBAC/CxB,uBAAuB,MAAMU,YAC3B9I,UACAoG,iBACA;4BAAEc;wBAAoB;oBAE1B,OAAO;wBACLkB,uBAAuB,MAAMU,YAC3Bc,YACAxD,iBACA;4BAAEc;wBAAoB;oBAE1B;gBACF;YACF,OAAO;gBACL,MAAM,qBAA6C,CAA7C,IAAInE,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAI;gBACFwF,cACEA,eAAec,sBAAsBzC,UAAUR;YACnD,EAAE,OAAO5B,KAAK;gBACZ,IAAIoC,aAAa,MAAM;oBACrBnC,QAAQC,KAAK,CAACF;gBAChB;gBACA,OAAO;oBACLyB,MAAM;gBACR;YACF;YAEA,MAAM0F,YAAa,MAAMxF,aAAayF,YAAY,CAAC5B,OAAO,CACxDzB;YAEF,MAAMsD,gBACJF,SAAS,CACP,yFAAyF;YACzF/E,SACD;YAEH,IAAIkF;YACJ5J,aAAakH,KAAK,GAAG;YACrB,IAAI;gBACF0C,YAAY,MAAMrM,qBAAqBkJ,GAAG,CAACzG,cAAc,IACvD2J,cAAcE,KAAK,CAAC,MAAM3D;YAE9B,SAAU;gBACRlG,aAAakH,KAAK,GAAG;YACvB;YAEA,4DAA4D;YAC5D,IAAIrC,eAAe;gBACjB,MAAMpH,mBAAmBsC;gBACzBD,sBAAsBhB,KAAK;oBAAEiB;oBAAWC;gBAAa;gBAErD+F,eAAe,MAAMd,0BAA0BpG,KAAKwF,KAAKrE,cAAc;oBACrE+F,cAAcH,QAAQkE,OAAO,CAACF;oBAC9B,iIAAiI;oBACjI5D,YAAY,CAACjG,UAAUkG,kBAAkB,IAAIK;oBAC7CtB;gBACF;YACF;QACF;QAEA,OAAO;YACLjB,MAAM;YACN+B,QAAQC;YACRK;QACF;IACF,EAAE,OAAO9D,KAAK;QACZ,IAAInG,gBAAgBmG,MAAM;YACxB,MAAMK,cAAczG,wBAAwBoG;YAC5C,MAAMU,eAAe/G,yBAAyBqG;YAE9C,MAAM7E,mBAAmBsC;YACzBD,sBAAsBhB,KAAK;gBAAEiB;gBAAWC;YAAa;YAErD,mFAAmF;YACnF,2FAA2F;YAC3FlB,IAAI4G,UAAU,GAAGrI,mBAAmB0M,QAAQ;YAE5C,IAAIlF,eAAe;gBACjB,OAAO;oBACLd,MAAM;oBACN+B,QAAQ,MAAMhD,2BACZjE,KACAC,KACA4B,MACAiC,aACAK,cACAqB,IAAII,UAAU,CAAC7D,QAAQ,EACvBb;gBAEJ;YACF;YAEAjB,IAAIwB,SAAS,CAAC,YAAYqC;YAC1B,OAAO;gBACLoB,MAAM;gBACN+B,QAAQ1J,aAAaqG,UAAU,CAAC;YAClC;QACF,OAAO,IAAIzG,0BAA0BsG,MAAM;YACzCxD,IAAI4G,UAAU,GAAG3J,4BAA4BuG;YAE7C,MAAM7E,mBAAmBsC;YACzBD,sBAAsBhB,KAAK;gBAAEiB;gBAAWC;YAAa;YAErD,IAAI6E,eAAe;gBACjB,MAAMc,UAAUC,QAAQC,MAAM,CAACvD;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAMqD;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBACA,OAAO;oBACL5B,MAAM;oBACN+B,QAAQ,MAAMb,0BAA0BpG,KAAKwF,KAAKrE,cAAc;wBAC9DgG,YAAY;wBACZD,cAAcJ;wBACdX;oBACF;gBACF;YACF;YACA,OAAO;gBACLjB,MAAM;YACR;QACF;QAEA,IAAIc,eAAe;YACjB/F,IAAI4G,UAAU,GAAG;YACjB,MAAMjI,mBAAmBsC;YACzB,MAAM4F,UAAUC,QAAQC,MAAM,CAACvD;YAC/B,IAAI;gBACF,8DAA8D;gBAC9D,mDAAmD;gBACnD,yDAAyD;gBACzD,2CAA2C;gBAC3C,MAAMqD;YACR,EAAE,OAAM;YACN,qDAAqD;YACvD;YAEA,OAAO;gBACL5B,MAAM;gBACN+B,QAAQ,MAAM3B,eAAetF,KAAKwF,KAAKrE,cAAc;oBACnD+F,cAAcJ;oBACd,iIAAiI;oBACjIK,YAAY,CAACjG,UAAUkG,kBAAkB,IAAIK;oBAC7CtB;gBACF;YACF;QACF;QAEA,MAAM1C;IACR;AACF;AAEA;;;;CAIC,GACD,SAAS6E,sBACPzC,QAAuB,EACvBR,eAAgC;QAOZA;IALpB,4EAA4E;IAC5E,IAAI,CAACQ,UAAU;QACb,MAAM,qBAAmD,CAAnD,IAAIlH,eAAe,kCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,MAAM6I,eAAcnC,4BAAAA,eAAe,CAACQ,SAAS,qBAAzBR,0BAA2B8F,EAAE;IAEjD,IAAI,CAAC3D,aAAa;QAChB,MAAM,qBAEL,CAFK,IAAIxF,MACR,CAAC,8BAA8B,EAAE6D,SAAS,qIAAqI,CAAC,GAD5K,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAO2B;AACT"}