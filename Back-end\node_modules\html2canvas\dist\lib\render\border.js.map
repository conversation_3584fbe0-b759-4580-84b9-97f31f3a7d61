{"version": 3, "file": "border.js", "sourceRoot": "", "sources": ["../../../src/render/border.ts"], "names": [], "mappings": ";;;AAEA,+CAA6C;AAEtC,IAAM,kBAAkB,GAAG,UAAC,MAAmB,EAAE,UAAkB;IACtE,QAAQ,UAAU,EAAE;QAChB,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,kBAAkB,CAC5B,CAAC;QACN,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,qBAAqB,CAC/B,CAAC;QACN,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,qBAAqB,EAC5B,MAAM,CAAC,mBAAmB,EAC1B,MAAM,CAAC,oBAAoB,CAC9B,CAAC;QACN,KAAK,CAAC,CAAC;QACP;YACI,OAAO,oBAAoB,CACvB,MAAM,CAAC,mBAAmB,EAC1B,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,iBAAiB,CAC3B,CAAC;KACT;AACL,CAAC,CAAC;AAhCW,QAAA,kBAAkB,sBAgC7B;AAEK,IAAM,6BAA6B,GAAG,UAAC,MAAmB,EAAE,UAAkB;IACjF,QAAQ,UAAU,EAAE;QAChB,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,2BAA2B,EAClC,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,4BAA4B,CACtC,CAAC;QACN,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,4BAA4B,EACnC,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,+BAA+B,CACzC,CAAC;QACN,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,+BAA+B,EACtC,MAAM,CAAC,mBAAmB,EAC1B,MAAM,CAAC,8BAA8B,CACxC,CAAC;QACN,KAAK,CAAC,CAAC;QACP;YACI,OAAO,oBAAoB,CACvB,MAAM,CAAC,mBAAmB,EAC1B,MAAM,CAAC,8BAA8B,EACrC,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,2BAA2B,CACrC,CAAC;KACT;AACL,CAAC,CAAC;AAhCW,QAAA,6BAA6B,iCAgCxC;AAEK,IAAM,6BAA6B,GAAG,UAAC,MAAmB,EAAE,UAAkB;IACjF,QAAQ,UAAU,EAAE;QAChB,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,2BAA2B,EAClC,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,4BAA4B,EACnC,MAAM,CAAC,kBAAkB,CAC5B,CAAC;QACN,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,4BAA4B,EACnC,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,+BAA+B,EACtC,MAAM,CAAC,qBAAqB,CAC/B,CAAC;QACN,KAAK,CAAC;YACF,OAAO,oBAAoB,CACvB,MAAM,CAAC,+BAA+B,EACtC,MAAM,CAAC,qBAAqB,EAC5B,MAAM,CAAC,8BAA8B,EACrC,MAAM,CAAC,oBAAoB,CAC9B,CAAC;QACN,KAAK,CAAC,CAAC;QACP;YACI,OAAO,oBAAoB,CACvB,MAAM,CAAC,8BAA8B,EACrC,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,2BAA2B,EAClC,MAAM,CAAC,iBAAiB,CAC3B,CAAC;KACT;AACL,CAAC,CAAC;AAhCW,QAAA,6BAA6B,iCAgCxC;AAEK,IAAM,wBAAwB,GAAG,UAAC,MAAmB,EAAE,UAAkB;IAC5E,QAAQ,UAAU,EAAE;QAChB,KAAK,CAAC;YACF,OAAO,0BAA0B,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAC/F,KAAK,CAAC;YACF,OAAO,0BAA0B,CAAC,MAAM,CAAC,oBAAoB,EAAE,MAAM,CAAC,uBAAuB,CAAC,CAAC;QACnG,KAAK,CAAC;YACF,OAAO,0BAA0B,CAAC,MAAM,CAAC,uBAAuB,EAAE,MAAM,CAAC,sBAAsB,CAAC,CAAC;QACrG,KAAK,CAAC,CAAC;QACP;YACI,OAAO,0BAA0B,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC;KACpG;AACL,CAAC,CAAC;AAZW,QAAA,wBAAwB,4BAYnC;AAEF,IAAM,0BAA0B,GAAG,UAAC,MAAY,EAAE,MAAY;IAC1D,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAI,4BAAa,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;KAC3C;SAAM;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrB;IAED,IAAI,4BAAa,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;KAC1C;SAAM;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrB;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,IAAM,oBAAoB,GAAG,UAAC,MAAY,EAAE,MAAY,EAAE,MAAY,EAAE,MAAY;IAChF,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAI,4BAAa,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;KAC3C;SAAM;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrB;IAED,IAAI,4BAAa,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;KAC1C;SAAM;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrB;IAED,IAAI,4BAAa,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;KACpD;SAAM;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrB;IAED,IAAI,4BAAa,CAAC,MAAM,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;KACrD;SAAM;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrB;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC"}