{"version": 3, "sources": ["../../src/server/config-utils.ts"], "sourcesContent": ["let installed: boolean = false\n\nexport function loadWebpackHook() {\n  const { init: initWebpack } = require('next/dist/compiled/webpack/webpack')\n  if (installed) {\n    return\n  }\n  installed = true\n  initWebpack()\n\n  // hook the Node.js require so that webpack requires are\n  // routed to the bundled and now initialized webpack version\n  require('../server/require-hook').addHookAliases(\n    [\n      ['webpack', 'next/dist/compiled/webpack/webpack-lib'],\n      ['webpack/package', 'next/dist/compiled/webpack/package'],\n      ['webpack/package.json', 'next/dist/compiled/webpack/package'],\n      ['webpack/lib/webpack', 'next/dist/compiled/webpack/webpack-lib'],\n      ['webpack/lib/webpack.js', 'next/dist/compiled/webpack/webpack-lib'],\n      [\n        'webpack/lib/node/NodeEnvironmentPlugin',\n        'next/dist/compiled/webpack/NodeEnvironmentPlugin',\n      ],\n      [\n        'webpack/lib/node/NodeEnvironmentPlugin.js',\n        'next/dist/compiled/webpack/NodeEnvironmentPlugin',\n      ],\n      [\n        'webpack/lib/BasicEvaluatedExpression',\n        'next/dist/compiled/webpack/BasicEvaluatedExpression',\n      ],\n      [\n        'webpack/lib/BasicEvaluatedExpression.js',\n        'next/dist/compiled/webpack/BasicEvaluatedExpression',\n      ],\n      [\n        'webpack/lib/node/NodeTargetPlugin',\n        'next/dist/compiled/webpack/NodeTargetPlugin',\n      ],\n      [\n        'webpack/lib/node/NodeTargetPlugin.js',\n        'next/dist/compiled/webpack/NodeTargetPlugin',\n      ],\n      [\n        'webpack/lib/node/NodeTemplatePlugin',\n        'next/dist/compiled/webpack/NodeTemplatePlugin',\n      ],\n      [\n        'webpack/lib/node/NodeTemplatePlugin.js',\n        'next/dist/compiled/webpack/NodeTemplatePlugin',\n      ],\n      [\n        'webpack/lib/LibraryTemplatePlugin',\n        'next/dist/compiled/webpack/LibraryTemplatePlugin',\n      ],\n      [\n        'webpack/lib/LibraryTemplatePlugin.js',\n        'next/dist/compiled/webpack/LibraryTemplatePlugin',\n      ],\n      [\n        'webpack/lib/SingleEntryPlugin',\n        'next/dist/compiled/webpack/SingleEntryPlugin',\n      ],\n      [\n        'webpack/lib/SingleEntryPlugin.js',\n        'next/dist/compiled/webpack/SingleEntryPlugin',\n      ],\n      [\n        'webpack/lib/optimize/LimitChunkCountPlugin',\n        'next/dist/compiled/webpack/LimitChunkCountPlugin',\n      ],\n      [\n        'webpack/lib/optimize/LimitChunkCountPlugin.js',\n        'next/dist/compiled/webpack/LimitChunkCountPlugin',\n      ],\n      [\n        'webpack/lib/webworker/WebWorkerTemplatePlugin',\n        'next/dist/compiled/webpack/WebWorkerTemplatePlugin',\n      ],\n      [\n        'webpack/lib/webworker/WebWorkerTemplatePlugin.js',\n        'next/dist/compiled/webpack/WebWorkerTemplatePlugin',\n      ],\n      [\n        'webpack/lib/ExternalsPlugin',\n        'next/dist/compiled/webpack/ExternalsPlugin',\n      ],\n      [\n        'webpack/lib/ExternalsPlugin.js',\n        'next/dist/compiled/webpack/ExternalsPlugin',\n      ],\n      [\n        'webpack/lib/web/FetchCompileWasmTemplatePlugin',\n        'next/dist/compiled/webpack/FetchCompileWasmTemplatePlugin',\n      ],\n      [\n        'webpack/lib/web/FetchCompileWasmTemplatePlugin.js',\n        'next/dist/compiled/webpack/FetchCompileWasmTemplatePlugin',\n      ],\n      [\n        'webpack/lib/web/FetchCompileWasmPlugin',\n        'next/dist/compiled/webpack/FetchCompileWasmPlugin',\n      ],\n      [\n        'webpack/lib/web/FetchCompileWasmPlugin.js',\n        'next/dist/compiled/webpack/FetchCompileWasmPlugin',\n      ],\n      [\n        'webpack/lib/web/FetchCompileAsyncWasmPlugin',\n        'next/dist/compiled/webpack/FetchCompileAsyncWasmPlugin',\n      ],\n      [\n        'webpack/lib/web/FetchCompileAsyncWasmPlugin.js',\n        'next/dist/compiled/webpack/FetchCompileAsyncWasmPlugin',\n      ],\n      [\n        'webpack/lib/ModuleFilenameHelpers',\n        'next/dist/compiled/webpack/ModuleFilenameHelpers',\n      ],\n      [\n        'webpack/lib/ModuleFilenameHelpers.js',\n        'next/dist/compiled/webpack/ModuleFilenameHelpers',\n      ],\n      ['webpack/lib/GraphHelpers', 'next/dist/compiled/webpack/GraphHelpers'],\n      [\n        'webpack/lib/GraphHelpers.js',\n        'next/dist/compiled/webpack/GraphHelpers',\n      ],\n      ['webpack/lib/NormalModule', 'next/dist/compiled/webpack/NormalModule'],\n      ['webpack-sources', 'next/dist/compiled/webpack/sources'],\n      ['webpack-sources/lib', 'next/dist/compiled/webpack/sources'],\n      ['webpack-sources/lib/index', 'next/dist/compiled/webpack/sources'],\n      ['webpack-sources/lib/index.js', 'next/dist/compiled/webpack/sources'],\n      ['@babel/runtime', 'next/dist/compiled/@babel/runtime/package.json'],\n      [\n        '@babel/runtime/package.json',\n        'next/dist/compiled/@babel/runtime/package.json',\n      ],\n    ].map(\n      // Use dynamic require.resolve to avoid statically analyzable since they're only for build time\n      ([request, replacement]) => [request, require.resolve(replacement)]\n    )\n  )\n}\n"], "names": ["loadWebpackHook", "installed", "init", "initWebpack", "require", "addHookAliases", "map", "request", "replacement", "resolve"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAFhB,IAAIC,YAAqB;AAElB,SAASD;IACd,MAAM,EAAEE,MAAMC,WAAW,EAAE,GAAGC,QAAQ;IACtC,IAAIH,WAAW;QACb;IACF;IACAA,YAAY;IACZE;IAEA,wDAAwD;IACxD,4DAA4D;IAC5DC,QAAQ,0BAA0BC,cAAc,CAC9C;QACE;YAAC;YAAW;SAAyC;QACrD;YAAC;YAAmB;SAAqC;QACzD;YAAC;YAAwB;SAAqC;QAC9D;YAAC;YAAuB;SAAyC;QACjE;YAAC;YAA0B;SAAyC;QACpE;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YAAC;YAA4B;SAA0C;QACvE;YACE;YACA;SACD;QACD;YAAC;YAA4B;SAA0C;QACvE;YAAC;YAAmB;SAAqC;QACzD;YAAC;YAAuB;SAAqC;QAC7D;YAAC;YAA6B;SAAqC;QACnE;YAAC;YAAgC;SAAqC;QACtE;YAAC;YAAkB;SAAiD;QACpE;YACE;YACA;SACD;KACF,CAACC,GAAG,CACH,+FAA+F;IAC/F,CAAC,CAACC,SAASC,YAAY,GAAK;YAACD;YAASH,QAAQK,OAAO,CAACD;SAAa;AAGzE"}