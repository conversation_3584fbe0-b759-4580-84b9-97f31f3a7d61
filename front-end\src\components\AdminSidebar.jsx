import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  useTheme,
  styled,
  IconButton,
  Tooltip,
  Collapse,
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import TemplateIcon from '@mui/icons-material/InsertDriveFile';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import PersonIcon from '@mui/icons-material/Person';
import BusinessCenterIcon from '@mui/icons-material/BusinessCenter';
import MenuIcon from '@mui/icons-material/Menu';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import DateRangeIcon from '@mui/icons-material/DateRange';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ReceiptIcon from '@mui/icons-material/Receipt';
import DescriptionIcon from '@mui/icons-material/Description';
import FolderIcon from '@mui/icons-material/Folder';
import SubscriptionsIcon from '@mui/icons-material/Subscriptions';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import { useMediaQuery } from '@mui/material';
import RenewalRequestsBadge from './admin/abonnements/RenewalRequestsBadge';

// Styled components
const drawerWidth = 260;
const miniDrawerWidth = 70;

const StyledDrawer = styled(Drawer)(({ theme, mini }) => ({
  width: mini ? miniDrawerWidth : drawerWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  overflowX: 'hidden',
  overflowY: 'hidden',
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  '& .MuiDrawer-paper': {
    width: mini ? miniDrawerWidth : drawerWidth,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    borderRight: 'none',
    overflowX: 'hidden',
    overflowY: 'hidden',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(1.8, 2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  minHeight: '60px',
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(to right, rgba(58, 110, 165, 0.1), rgba(42, 80, 128, 0.05))'
    : 'linear-gradient(to right, rgba(58, 110, 165, 0.05), rgba(42, 80, 128, 0.02))',
}));



const StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  borderRadius: '8px',
  margin: theme.spacing(0.5, 1),
  padding: theme.spacing(1, 2),
  backgroundColor: active ? theme.palette.action.selected : 'transparent',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme, active }) => ({
  minWidth: '40px',
  color: active ? theme.palette.primary.main : theme.palette.text.secondary,
}));

const StyledListItemText = styled(ListItemText)(({ theme, active }) => ({
  '& .MuiTypography-root': {
    fontWeight: active ? 600 : 400,
    color: active ? theme.palette.text.primary : theme.palette.text.secondary,
  },
}));

const AdminSidebar = () => {
  const theme = useTheme();
  const location = useLocation();
  const [mini, setMini] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [gestionOpen, setGestionOpen] = useState(true);

  const isActive = (path) => {
    return location.pathname === path;
  };

  const isActiveGroup = (paths) => {
    return paths.some(path => location.pathname.startsWith(path));
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMiniDrawerToggle = () => {
    setMini(!mini);
  };

  const handleGestionClick = () => {
    setGestionOpen(!gestionOpen);
  };

  const drawer = (
    <>
      <LogoContainer>
        <IconButton
          onClick={handleMiniDrawerToggle}
          sx={{
            mr: 1.5,
            color: theme.palette.primary.main,
            backgroundColor: theme.palette.mode === 'dark'
              ? 'rgba(58, 110, 165, 0.1)'
              : 'rgba(58, 110, 165, 0.05)',
            border: `1px solid ${theme.palette.mode === 'dark'
              ? 'rgba(58, 110, 165, 0.2)'
              : 'rgba(58, 110, 165, 0.1)'}`,
            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(58, 110, 165, 0.2)'
                : 'rgba(58, 110, 165, 0.1)',
              transform: 'translateY(-1px)',
              boxShadow: '0 3px 8px rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          {mini ? <MenuIcon /> : <ChevronLeftIcon />}
        </IconButton>
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            color: "#fff",
            width: 36,
            height: 36,
            boxShadow: "0 4px 12px rgba(58, 110, 165, 0.25)",
            mr: 1.5,
            border: '1.5px solid white',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'scale(1.05)',
              boxShadow: "0 6px 16px rgba(58, 110, 165, 0.35)",
            },
            display: mini ? 'none' : 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #3a6ea5, #4f83b9)',
          }}
        >
          <Typography sx={{
            fontWeight: 'bold',
            fontSize: '1.2rem',
            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15))'
          }}>A</Typography>
        </Avatar>
        {!mini && (
          <Box>
            <Typography variant="subtitle1" component="div" sx={{
              fontWeight: 700,
              fontSize: '0.95rem',
              color: theme.palette.primary.main,
              letterSpacing: '0.01em',
              position: 'relative',
              display: 'inline-block',
              mb: 0.1,
            }}>
              Gestion Admin
            </Typography>
            <Typography variant="caption" sx={{
              color: theme.palette.text.secondary,
              fontSize: '0.7rem',
              display: 'block',
              mt: 0,
              fontWeight: 500,
              letterSpacing: '0.01em',
              opacity: 0.75,
            }}>
              Administration système
            </Typography>
          </Box>
        )}
      </LogoContainer>

      <Box sx={{
        overflowY: "hidden",
        height: "calc(100vh - 60px)",
      }}>
        <List component="nav" sx={{ p: mini ? 1 : 2 }}>
          {/* Tableau de bord */}
          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Tableau de bord" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/admin/analytics"
                active={isActive("/admin/analytics")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActive("/admin/analytics")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <DashboardIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Tableau de bord" active={isActive("/admin/analytics")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {/* Gestion */}
          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Gestion" : ""} placement="right">
              <StyledListItemButton
                onClick={handleGestionClick}
                active={isActiveGroup(["/admin/utilisateurs/vendeurs", "/admin/utilisateurs/entreprises"])}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActiveGroup(["/admin/utilisateurs/vendeurs", "/admin/utilisateurs/entreprises"])}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <FolderIcon />
                </StyledListItemIcon>
                {!mini && (
                  <>
                    <StyledListItemText primary="Gestion" active={isActiveGroup(["/admin/utilisateurs/vendeurs", "/admin/utilisateurs/entreprises"])} />
                    {gestionOpen ? <ExpandLess /> : <ExpandMore />}
                  </>
                )}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <Collapse in={gestionOpen && !mini} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {/* Vendeurs */}
              <ListItem disablePadding>
                <StyledListItemButton
                  component={Link}
                  to="/admin/utilisateurs/vendeurs"
                  active={isActive("/admin/utilisateurs/vendeurs")}
                  sx={{ pl: 4 }}
                >
                  <StyledListItemIcon active={isActive("/admin/utilisateurs/vendeurs")} sx={{ minWidth: 30 }}>
                    <PersonIcon fontSize="small" />
                  </StyledListItemIcon>
                  <StyledListItemText
                    primary="Vendeurs"
                    active={isActive("/admin/utilisateurs/vendeurs")}
                    sx={{ '& .MuiTypography-root': { fontSize: '0.9rem' } }}
                  />
                </StyledListItemButton>
              </ListItem>

              {/* Responsables d'entreprise */}
              <ListItem disablePadding>
                <StyledListItemButton
                  component={Link}
                  to="/admin/utilisateurs/entreprises"
                  active={isActive("/admin/utilisateurs/entreprises")}
                  sx={{ pl: 4 }}
                >
                  <StyledListItemIcon active={isActive("/admin/utilisateurs/entreprises")} sx={{ minWidth: 30 }}>
                    <BusinessCenterIcon fontSize="small" />
                  </StyledListItemIcon>
                  <StyledListItemText
                    primary="Responsables d'entreprise"
                    active={isActive("/admin/utilisateurs/entreprises")}
                    sx={{ '& .MuiTypography-root': { fontSize: '0.9rem' } }}
                  />
                </StyledListItemButton>
              </ListItem>
            </List>
          </Collapse>

          {mini && gestionOpen && (
            <>
              {/* Vendeurs (mini mode) */}
              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title="Vendeurs" placement="right">
                  <StyledListItemButton
                    component={Link}
                    to="/admin/utilisateurs/vendeurs"
                    active={isActive("/admin/utilisateurs/vendeurs")}
                    sx={{
                      justifyContent: 'center',
                      px: 2
                    }}
                  >
                    <StyledListItemIcon
                      active={isActive("/admin/utilisateurs/vendeurs")}
                      sx={{ minWidth: 0 }}
                    >
                      <PersonIcon fontSize="small" />
                    </StyledListItemIcon>
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>

              {/* Responsables d'entreprise (mini mode) */}
              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title="Responsables d'entreprise" placement="right">
                  <StyledListItemButton
                    component={Link}
                    to="/admin/utilisateurs/entreprises"
                    active={isActive("/admin/utilisateurs/entreprises")}
                    sx={{
                      justifyContent: 'center',
                      px: 2
                    }}
                  >
                    <StyledListItemIcon
                      active={isActive("/admin/utilisateurs/entreprises")}
                      sx={{ minWidth: 0 }}
                    >
                      <BusinessCenterIcon fontSize="small" />
                    </StyledListItemIcon>
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>
            </>
          )}



          {/* Abonnements */}
          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Abonnements" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/admin/abonnements"
                active={isActiveGroup(["/admin/abonnements"])}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActiveGroup(["/admin/abonnements"])}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <RenewalRequestsBadge>
                    <SubscriptionsIcon />
                  </RenewalRequestsBadge>
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Abonnements" active={isActiveGroup(["/admin/abonnements"])} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {/* Templates */}
          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Templates" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/admin/templates"
                active={isActiveGroup(["/admin/templates", "/admin/template-settings"])}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActiveGroup(["/admin/templates", "/admin/template-settings"])}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <TemplateIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Templates" active={isActiveGroup(["/admin/templates", "/admin/template-settings"])} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>



          {/* Paramètres */}
          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Paramètres" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/admin/parametres"
                active={isActive("/admin/parametres")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActive("/admin/parametres")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <SettingsIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Paramètres" active={isActive("/admin/parametres")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {/* Mon Profil */}
          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Mon Profil" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to="/admin/profile"
                active={isActive("/admin/profile")}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActive("/admin/profile")}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <PersonIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Mon Profil" active={isActive("/admin/profile")} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>
        </List>
      </Box>
    </>
  );

  return (
    <>
      {isMobile ? (
        <>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              position: "fixed",
              top: 12,
              left: 12,
              zIndex: 1300,
              backgroundColor: theme.palette.background.paper,
              boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
              display: { md: "none" },
            }}
          >
            <MenuIcon />
          </IconButton>
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: "block", md: "none" },
              "& .MuiDrawer-paper": {
                width: drawerWidth,
                backgroundColor: theme.palette.background.paper,
                color: theme.palette.text.primary,
                overflowY: 'hidden',
              },
            }}
          >
            {drawer}
          </Drawer>
        </>
      ) : (
        <StyledDrawer variant="permanent" mini={mini}>
          {drawer}
        </StyledDrawer>
      )}
    </>
  )
}

export default AdminSidebar;
