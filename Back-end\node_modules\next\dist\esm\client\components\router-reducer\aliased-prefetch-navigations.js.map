{"version": 3, "sources": ["../../../../src/client/components/router-reducer/aliased-prefetch-navigations.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n} from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  addSearchParamsIfPageSegment,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { createEmptyCacheNode } from '../app-router'\nimport { applyRouterStatePatchToTree } from './apply-router-state-patch-to-tree'\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { fillCacheWithNewSubTreeDataButOnlyLoading } from './fill-cache-with-new-subtree-data'\nimport { handleMutable } from './handle-mutable'\nimport type { Mutable, ReadonlyReducerState } from './router-reducer-types'\n\n/**\n * This is a stop-gap until per-segment caching is implemented. It leverages the `aliased` flag that is added\n * to prefetch entries when it's determined that the loading state from that entry should be used for this navigation.\n * This function takes the aliased entry and only applies the loading state to the updated cache node.\n * We should remove this once per-segment fetching is implemented as ideally the prefetch cache will contain a\n * more granular segment map and so the router will be able to simply re-use the loading segment for the new navigation.\n */\nexport function handleAliasedPrefetchEntry(\n  navigatedAt: number,\n  state: ReadonlyReducerState,\n  flightData: string | NormalizedFlightData[],\n  url: URL,\n  mutable: Mutable\n) {\n  let currentTree = state.tree\n  let currentCache = state.cache\n  const href = createHrefFromUrl(url)\n  let applied\n\n  if (typeof flightData === 'string') {\n    return false\n  }\n\n  for (const normalizedFlightData of flightData) {\n    // If the segment doesn't have a loading component, we don't need to do anything.\n    if (!hasLoadingComponentInSeedData(normalizedFlightData.seedData)) {\n      continue\n    }\n\n    let treePatch = normalizedFlightData.tree\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    const { seedData, isRootRender, pathToSegment } = normalizedFlightData\n    // TODO-APP: remove ''\n    const flightSegmentPathWithLeadingEmpty = ['', ...pathToSegment]\n\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    let newTree = applyRouterStatePatchToTree(\n      flightSegmentPathWithLeadingEmpty,\n      currentTree,\n      treePatch,\n      href\n    )\n\n    const newCache = createEmptyCacheNode()\n\n    // The prefetch cache entry was aliased -- this signals that we only fill in the cache with the\n    // loading state and not the actual parallel route seed data.\n    if (isRootRender && seedData) {\n      // Fill in the cache with the new loading / rsc data\n      const rsc = seedData[1]\n      const loading = seedData[3]\n      newCache.loading = loading\n      newCache.rsc = rsc\n\n      // Construct a new tree and apply the aliased loading state for each parallel route\n      fillNewTreeWithOnlyLoadingSegments(\n        navigatedAt,\n        newCache,\n        currentCache,\n        treePatch,\n        seedData\n      )\n    } else {\n      // Copy rsc for the root node of the cache.\n      newCache.rsc = currentCache.rsc\n      newCache.prefetchRsc = currentCache.prefetchRsc\n      newCache.loading = currentCache.loading\n      newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n      // copy the loading state only into the leaf node (the part that changed)\n      fillCacheWithNewSubTreeDataButOnlyLoading(\n        navigatedAt,\n        newCache,\n        currentCache,\n        normalizedFlightData\n      )\n    }\n\n    // If we don't have an updated tree, there's no reason to update the cache, as the tree\n    // dictates what cache nodes to render.\n    if (newTree) {\n      currentTree = newTree\n      currentCache = newCache\n      applied = true\n    }\n  }\n\n  if (!applied) {\n    return false\n  }\n\n  mutable.patchedTree = currentTree\n  mutable.cache = currentCache\n  mutable.canonicalUrl = href\n  mutable.hashFragment = url.hash\n\n  return handleMutable(state, mutable)\n}\n\nfunction hasLoadingComponentInSeedData(seedData: CacheNodeSeedData | null) {\n  if (!seedData) return false\n\n  const parallelRoutes = seedData[2]\n  const loading = seedData[3]\n\n  if (loading) {\n    return true\n  }\n\n  for (const key in parallelRoutes) {\n    if (hasLoadingComponentInSeedData(parallelRoutes[key])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction fillNewTreeWithOnlyLoadingSegments(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null\n) {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    return\n  }\n\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const rsc = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        // copy the layout but null the page segment as that's not meant to be used\n        rsc: segmentForParallelRoute.includes(PAGE_SEGMENT_KEY) ? null : rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillNewTreeWithOnlyLoadingSegments(\n      navigatedAt,\n      newCacheNode,\n      existingCache,\n      parallelRouteState,\n      parallelSeedData\n    )\n  }\n}\n\n/**\n * Add search params to the page segments in the flight router state\n * Page segments that are associated with search params have a page segment key\n * followed by a query string. This function will add those params to the page segment.\n * This is useful if we return an aliased prefetch entry (ie, won't have search params)\n * but the canonical router URL has search params.\n */\nexport function addSearchParamsToPageSegments(\n  flightRouterState: FlightRouterState,\n  searchParams: Record<string, string | string[] | undefined>\n): FlightRouterState {\n  const [segment, parallelRoutes, ...rest] = flightRouterState\n\n  // If it's a page segment, modify the segment by adding search params\n  if (segment.includes(PAGE_SEGMENT_KEY)) {\n    const newSegment = addSearchParamsIfPageSegment(segment, searchParams)\n    return [newSegment, parallelRoutes, ...rest]\n  }\n\n  // Otherwise, recurse through the parallel routes and return a new tree\n  const updatedParallelRoutes: { [key: string]: FlightRouterState } = {}\n\n  for (const [key, parallelRoute] of Object.entries(parallelRoutes)) {\n    updatedParallelRoutes[key] = addSearchParamsToPageSegments(\n      parallelRoute,\n      searchParams\n    )\n  }\n\n  return [segment, updatedParallelRoutes, ...rest]\n}\n"], "names": ["addSearchParamsIfPageSegment", "PAGE_SEGMENT_KEY", "createEmptyCacheNode", "applyRouterStatePatchToTree", "createHrefFromUrl", "createRouterCache<PERSON>ey", "fillCacheWithNewSubTreeDataButOnlyLoading", "handleMutable", "handleAliasedPrefetchEntry", "navigatedAt", "state", "flightData", "url", "mutable", "currentTree", "tree", "currentCache", "cache", "href", "applied", "normalizedFlightData", "hasLoadingComponentInSeedData", "seedData", "treePatch", "addSearchParamsToPageSegments", "Object", "fromEntries", "searchParams", "isRootRender", "pathToSegment", "flightSegmentPathWithLeadingEmpty", "newTree", "newCache", "rsc", "loading", "fillNewTreeWithOnlyLoadingSegments", "prefetchRsc", "parallelRoutes", "Map", "patchedTree", "canonicalUrl", "hashFragment", "hash", "key", "existingCache", "routerState", "cacheNodeSeedData", "isLastSegment", "keys", "length", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "parallelSeedData", "undefined", "newCacheNode", "lazyData", "includes", "head", "prefetchHead", "existingParallelRoutes", "get", "set", "flightRouterState", "segment", "rest", "newSegment", "updatedParallelRoutes", "parallelRoute", "entries"], "mappings": "AAKA,SACEA,4BAA4B,EAC5BC,gBAAgB,QACX,8BAA6B;AAEpC,SAASC,oBAAoB,QAAQ,gBAAe;AACpD,SAASC,2BAA2B,QAAQ,qCAAoC;AAChF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,oBAAoB,QAAQ,4BAA2B;AAChE,SAASC,yCAAyC,QAAQ,qCAAoC;AAC9F,SAASC,aAAa,QAAQ,mBAAkB;AAGhD;;;;;;CAMC,GACD,OAAO,SAASC,2BACdC,WAAmB,EACnBC,KAA2B,EAC3BC,UAA2C,EAC3CC,GAAQ,EACRC,OAAgB;IAEhB,IAAIC,cAAcJ,MAAMK,IAAI;IAC5B,IAAIC,eAAeN,MAAMO,KAAK;IAC9B,MAAMC,OAAOd,kBAAkBQ;IAC/B,IAAIO;IAEJ,IAAI,OAAOR,eAAe,UAAU;QAClC,OAAO;IACT;IAEA,KAAK,MAAMS,wBAAwBT,WAAY;QAC7C,iFAAiF;QACjF,IAAI,CAACU,8BAA8BD,qBAAqBE,QAAQ,GAAG;YACjE;QACF;QAEA,IAAIC,YAAYH,qBAAqBL,IAAI;QACzC,uHAAuH;QACvH,gJAAgJ;QAChJ,sHAAsH;QACtHQ,YAAYC,8BACVD,WACAE,OAAOC,WAAW,CAACd,IAAIe,YAAY;QAGrC,MAAM,EAAEL,QAAQ,EAAEM,YAAY,EAAEC,aAAa,EAAE,GAAGT;QAClD,sBAAsB;QACtB,MAAMU,oCAAoC;YAAC;eAAOD;SAAc;QAEhE,uHAAuH;QACvH,gJAAgJ;QAChJ,sHAAsH;QACtHN,YAAYC,8BACVD,WACAE,OAAOC,WAAW,CAACd,IAAIe,YAAY;QAGrC,IAAII,UAAU5B,4BACZ2B,mCACAhB,aACAS,WACAL;QAGF,MAAMc,WAAW9B;QAEjB,+FAA+F;QAC/F,6DAA6D;QAC7D,IAAI0B,gBAAgBN,UAAU;YAC5B,oDAAoD;YACpD,MAAMW,MAAMX,QAAQ,CAAC,EAAE;YACvB,MAAMY,UAAUZ,QAAQ,CAAC,EAAE;YAC3BU,SAASE,OAAO,GAAGA;YACnBF,SAASC,GAAG,GAAGA;YAEf,mFAAmF;YACnFE,mCACE1B,aACAuB,UACAhB,cACAO,WACAD;QAEJ,OAAO;YACL,2CAA2C;YAC3CU,SAASC,GAAG,GAAGjB,aAAaiB,GAAG;YAC/BD,SAASI,WAAW,GAAGpB,aAAaoB,WAAW;YAC/CJ,SAASE,OAAO,GAAGlB,aAAakB,OAAO;YACvCF,SAASK,cAAc,GAAG,IAAIC,IAAItB,aAAaqB,cAAc;YAE7D,yEAAyE;YACzE/B,0CACEG,aACAuB,UACAhB,cACAI;QAEJ;QAEA,uFAAuF;QACvF,uCAAuC;QACvC,IAAIW,SAAS;YACXjB,cAAciB;YACdf,eAAegB;YACfb,UAAU;QACZ;IACF;IAEA,IAAI,CAACA,SAAS;QACZ,OAAO;IACT;IAEAN,QAAQ0B,WAAW,GAAGzB;IACtBD,QAAQI,KAAK,GAAGD;IAChBH,QAAQ2B,YAAY,GAAGtB;IACvBL,QAAQ4B,YAAY,GAAG7B,IAAI8B,IAAI;IAE/B,OAAOnC,cAAcG,OAAOG;AAC9B;AAEA,SAASQ,8BAA8BC,QAAkC;IACvE,IAAI,CAACA,UAAU,OAAO;IAEtB,MAAMe,iBAAiBf,QAAQ,CAAC,EAAE;IAClC,MAAMY,UAAUZ,QAAQ,CAAC,EAAE;IAE3B,IAAIY,SAAS;QACX,OAAO;IACT;IAEA,IAAK,MAAMS,OAAON,eAAgB;QAChC,IAAIhB,8BAA8BgB,cAAc,CAACM,IAAI,GAAG;YACtD,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASR,mCACP1B,WAAmB,EACnBuB,QAAmB,EACnBY,aAAwB,EACxBC,WAA8B,EAC9BC,iBAA2C;IAE3C,MAAMC,gBAAgBtB,OAAOuB,IAAI,CAACH,WAAW,CAAC,EAAE,EAAEI,MAAM,KAAK;IAC7D,IAAIF,eAAe;QACjB;IACF;IAEA,IAAK,MAAMJ,OAAOE,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMK,qBAAqBL,WAAW,CAAC,EAAE,CAACF,IAAI;QAC9C,MAAMQ,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAW/C,qBAAqB8C;QAEtC,MAAME,mBACJP,sBAAsB,QAAQA,iBAAiB,CAAC,EAAE,CAACH,IAAI,KAAKW,YACxDR,iBAAiB,CAAC,EAAE,CAACH,IAAI,GACzB;QAEN,IAAIY;QACJ,IAAIF,qBAAqB,MAAM;YAC7B,qCAAqC;YACrC,MAAMpB,MAAMoB,gBAAgB,CAAC,EAAE;YAC/B,MAAMnB,UAAUmB,gBAAgB,CAAC,EAAE;YACnCE,eAAe;gBACbC,UAAU;gBACV,2EAA2E;gBAC3EvB,KAAKkB,wBAAwBM,QAAQ,CAACxD,oBAAoB,OAAOgC;gBACjEG,aAAa;gBACbsB,MAAM;gBACNC,cAAc;gBACdtB,gBAAgB,IAAIC;gBACpBJ;gBACAzB;YACF;QACF,OAAO;YACL,kEAAkE;YAClE,iBAAiB;YACjB8C,eAAe;gBACbC,UAAU;gBACVvB,KAAK;gBACLG,aAAa;gBACbsB,MAAM;gBACNC,cAAc;gBACdtB,gBAAgB,IAAIC;gBACpBJ,SAAS;gBACTzB,aAAa,CAAC;YAChB;QACF;QAEA,MAAMmD,yBAAyB5B,SAASK,cAAc,CAACwB,GAAG,CAAClB;QAC3D,IAAIiB,wBAAwB;YAC1BA,uBAAuBE,GAAG,CAACV,UAAUG;QACvC,OAAO;YACLvB,SAASK,cAAc,CAACyB,GAAG,CAACnB,KAAK,IAAIL,IAAI;gBAAC;oBAACc;oBAAUG;iBAAa;aAAC;QACrE;QAEApB,mCACE1B,aACA8C,cACAX,eACAM,oBACAG;IAEJ;AACF;AAEA;;;;;;CAMC,GACD,OAAO,SAAS7B,8BACduC,iBAAoC,EACpCpC,YAA2D;IAE3D,MAAM,CAACqC,SAAS3B,gBAAgB,GAAG4B,KAAK,GAAGF;IAE3C,qEAAqE;IACrE,IAAIC,QAAQP,QAAQ,CAACxD,mBAAmB;QACtC,MAAMiE,aAAalE,6BAA6BgE,SAASrC;QACzD,OAAO;YAACuC;YAAY7B;eAAmB4B;SAAK;IAC9C;IAEA,uEAAuE;IACvE,MAAME,wBAA8D,CAAC;IAErE,KAAK,MAAM,CAACxB,KAAKyB,cAAc,IAAI3C,OAAO4C,OAAO,CAAChC,gBAAiB;QACjE8B,qBAAqB,CAACxB,IAAI,GAAGnB,8BAC3B4C,eACAzC;IAEJ;IAEA,OAAO;QAACqC;QAASG;WAA0BF;KAAK;AAClD"}