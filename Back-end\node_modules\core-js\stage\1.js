'use strict';
var parent = require('./2');

require('../proposals/array-filtering-stage-1');
require('../proposals/array-last');
require('../proposals/array-unique');
require('../proposals/collection-methods');
require('../proposals/collection-of-from');
require('../proposals/data-view-get-set-uint8-clamped');
require('../proposals/keys-composition');
require('../proposals/math-clamp');
require('../proposals/math-extensions');
require('../proposals/math-signbit');
require('../proposals/number-from-string');
require('../proposals/object-iteration');
require('../proposals/observable');
require('../proposals/pattern-matching-v2');
require('../proposals/seeded-random');
require('../proposals/string-code-points');
require('../proposals/string-cooked');
// TODO: Obsolete versions, remove from `core-js@4`:
require('../proposals/array-from-async');
require('../proposals/map-upsert');
require('../proposals/number-range');
require('../proposals/pattern-matching');
require('../proposals/string-replace-all');

module.exports = parent;
