const express = require('express');
const router = express.Router();
const ResponsableEntreprise = require('../models/ResponsableEntrepriseModel');
const User = require('../models/UserModel');
const jwt = require('jsonwebtoken');
const entrepriseService = require('../services/entrepriseService');

// JWT Secret (should be in environment variables in production)
const JWT_SECRET = process.env.JWT_SECRET || 'votre-cle-secrete-pour-jwt';

// Middleware to verify JWT token
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Token non fourni' });
    }

    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return res.status(401).json({ error: 'Token invalide' });
      }

      req.userId = decoded.userId;
      req.userRole = decoded.role;
      next();
    });
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error.message);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du token' });
  }
};

// Middleware to check if user is admin
const adminOnly = (req, res, next) => {
  if (req.userRole !== 'ADMIN') {
    return res.status(403).json({ error: 'Accès non autorisé. Seuls les administrateurs peuvent accéder à cette ressource.' });
  }
  next();
};

// Get all entreprises
router.get('/entreprises', verifyToken, async (req, res) => {
  try {
    console.log('Getting all entreprises. Requested by:', req.userId, 'with role:', req.userRole);

    const entreprises = await ResponsableEntreprise.find();
    console.log(`Found ${entreprises.length} entreprises`);

    res.status(200).json(entreprises);
  } catch (error) {
    console.error('Error fetching entreprises:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des entreprises' });
  }
});

// Get entreprise by responsable ID - IMPORTANT: This route must come BEFORE the generic ID route
router.get('/entreprises/responsable/:responsableId', verifyToken, async (req, res) => {
  try {
    console.log('Getting entreprise by responsable ID:', req.params.responsableId);

    const entreprise = await ResponsableEntreprise.findOne({ responsableId: req.params.responsableId });
    if (!entreprise) {
      return res.status(404).json({ error: 'Entreprise non trouvée pour ce responsable' });
    }

    res.status(200).json(entreprise);
  } catch (error) {
    console.error('Error fetching entreprise by responsable:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération de l\'entreprise' });
  }
});

// Get entreprise by ID
router.get('/entreprises/:id', verifyToken, async (req, res) => {
  try {
    console.log('Getting entreprise by ID:', req.params.id);

    const entreprise = await ResponsableEntreprise.findById(req.params.id);
    if (!entreprise) {
      return res.status(404).json({ error: 'Entreprise non trouvée' });
    }

    res.status(200).json(entreprise);
  } catch (error) {
    console.error('Error fetching entreprise:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération de l\'entreprise' });
  }
});

// Create entreprise manually (admin only)
router.post('/entreprises', verifyToken, adminOnly, async (req, res) => {
  try {
    console.log('Creating entreprise manually. Requested by admin:', req.userId);

    const entrepriseData = req.body;

    // Check if responsable exists
    if (entrepriseData.responsableId) {
      const responsable = await User.findById(entrepriseData.responsableId);
      if (!responsable) {
        return res.status(404).json({ error: 'Responsable non trouvé' });
      }

      // Check if responsable already has an entreprise
      const existingEntreprise = await ResponsableEntreprise.findOne({ responsableId: entrepriseData.responsableId });
      if (existingEntreprise) {
        return res.status(400).json({ error: 'Ce responsable a déjà une entreprise associée' });
      }
    }

    // Create entreprise
    const newEntreprise = new ResponsableEntreprise({
      ...entrepriseData,
      createdBy: req.userId
    });

    await newEntreprise.save();

    // Update responsable with entreprise ID if provided
    if (entrepriseData.responsableId) {
      await User.findByIdAndUpdate(entrepriseData.responsableId, {
        entrepriseId: newEntreprise._id,
        responsableId: newEntreprise._id
      });
    }

    res.status(201).json(newEntreprise);
  } catch (error) {
    console.error('Error creating entreprise:', error.message);
    res.status(500).json({ error: 'Erreur lors de la création de l\'entreprise' });
  }
});

module.exports = router;
