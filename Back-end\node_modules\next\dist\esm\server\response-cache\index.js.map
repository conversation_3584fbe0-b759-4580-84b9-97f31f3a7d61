{"version": 3, "sources": ["../../../src/server/response-cache/index.ts"], "sourcesContent": ["import type {\n  Response<PERSON>acheEntry,\n  ResponseGenerator,\n  ResponseCacheBase,\n  IncrementalResponseCacheEntry,\n  IncrementalResponseCache,\n} from './types'\n\nimport { Batcher } from '../../lib/batcher'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport {\n  fromResponseCacheEntry,\n  routeKindToIncrementalCacheKind,\n  toResponseCacheEntry,\n} from './utils'\nimport type { RouteK<PERSON> } from '../route-kind'\n\nexport * from './types'\n\nexport default class ResponseCache implements ResponseCacheBase {\n  private readonly batcher = Batcher.create<\n    { key: string; isOnDemandRevalidate: boolean },\n    IncrementalResponseCacheEntry | null,\n    string\n  >({\n    // Ensure on-demand revalidate doesn't block normal requests, it should be\n    // safe to run an on-demand revalidate for the same key as a normal request.\n    cacheKeyFn: ({ key, isOnDemandRevalidate }) =>\n      `${key}-${isOnDemandRevalidate ? '1' : '0'}`,\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  private previousCacheItem?: {\n    key: string\n    entry: IncrementalResponseCacheEntry | null\n    expiresAt: number\n  }\n\n  private minimalMode?: boolean\n\n  constructor(minimalMode: boolean) {\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n  }\n\n  public async get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      routeKind: RouteKind\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalResponseCache\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // If there is no key for the cache, we can't possibly look this up in the\n    // cache so just return the result of the response generator.\n    if (!key) {\n      return responseGenerator({ hasResolved: false, previousCacheEntry: null })\n    }\n\n    const {\n      incrementalCache,\n      isOnDemandRevalidate = false,\n      isFallback = false,\n      isRoutePPREnabled = false,\n    } = context\n\n    const response = await this.batcher.batch(\n      { key, isOnDemandRevalidate },\n      async (cacheKey, resolve) => {\n        // We keep the previous cache entry around to leverage when the\n        // incremental cache is disabled in minimal mode.\n        if (\n          this.minimalMode &&\n          this.previousCacheItem?.key === cacheKey &&\n          this.previousCacheItem.expiresAt > Date.now()\n        ) {\n          return this.previousCacheItem.entry\n        }\n\n        // Coerce the kindHint into a given kind for the incremental cache.\n        const kind = routeKindToIncrementalCacheKind(context.routeKind)\n\n        let resolved = false\n        let cachedResponse: IncrementalResponseCacheEntry | null = null\n        try {\n          cachedResponse = !this.minimalMode\n            ? await incrementalCache.get(key, {\n                kind,\n                isRoutePPREnabled: context.isRoutePPREnabled,\n                isFallback,\n              })\n            : null\n\n          if (cachedResponse && !isOnDemandRevalidate) {\n            resolve(cachedResponse)\n            resolved = true\n\n            if (!cachedResponse.isStale || context.isPrefetch) {\n              // The cached value is still valid, so we don't need\n              // to update it yet.\n              return null\n            }\n          }\n\n          const cacheEntry = await responseGenerator({\n            hasResolved: resolved,\n            previousCacheEntry: cachedResponse,\n            isRevalidating: true,\n          })\n\n          // If the cache entry couldn't be generated, we don't want to cache\n          // the result.\n          if (!cacheEntry) {\n            // Unset the previous cache item if it was set.\n            if (this.minimalMode) this.previousCacheItem = undefined\n            return null\n          }\n\n          const resolveValue = await fromResponseCacheEntry({\n            ...cacheEntry,\n            isMiss: !cachedResponse,\n          })\n          if (!resolveValue) {\n            // Unset the previous cache item if it was set.\n            if (this.minimalMode) this.previousCacheItem = undefined\n            return null\n          }\n\n          // For on-demand revalidate wait to resolve until cache is set.\n          // Otherwise resolve now.\n          if (!isOnDemandRevalidate && !resolved) {\n            resolve(resolveValue)\n            resolved = true\n          }\n\n          // We want to persist the result only if it has a cache control value\n          // defined.\n          if (resolveValue.cacheControl) {\n            if (this.minimalMode) {\n              this.previousCacheItem = {\n                key: cacheKey,\n                entry: resolveValue,\n                expiresAt: Date.now() + 1000,\n              }\n            } else {\n              await incrementalCache.set(key, resolveValue.value, {\n                cacheControl: resolveValue.cacheControl,\n                isRoutePPREnabled,\n                isFallback,\n              })\n            }\n          }\n\n          return resolveValue\n        } catch (err) {\n          // When a path is erroring we automatically re-set the existing cache\n          // with new revalidate and expire times to prevent non-stop retrying.\n          if (cachedResponse?.cacheControl) {\n            const newRevalidate = Math.min(\n              Math.max(cachedResponse.cacheControl.revalidate || 3, 3),\n              30\n            )\n\n            const newExpire =\n              cachedResponse.cacheControl.expire === undefined\n                ? undefined\n                : Math.max(\n                    newRevalidate + 3,\n                    cachedResponse.cacheControl.expire\n                  )\n\n            await incrementalCache.set(key, cachedResponse.value, {\n              cacheControl: { revalidate: newRevalidate, expire: newExpire },\n              isRoutePPREnabled,\n              isFallback,\n            })\n          }\n\n          // While revalidating in the background we can't reject as we already\n          // resolved the cache entry so log the error here.\n          if (resolved) {\n            console.error(err)\n            return null\n          }\n\n          // We haven't resolved yet, so let's throw to indicate an error.\n          throw err\n        }\n      }\n    )\n\n    return toResponseCacheEntry(response)\n  }\n}\n"], "names": ["<PERSON><PERSON>", "scheduleOnNextTick", "fromResponseCacheEntry", "routeKindToIncrementalCacheKind", "toResponseCacheEntry", "ResponseCache", "constructor", "minimalMode", "batcher", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "minimalModeKey", "get", "responseGenerator", "context", "hasResolved", "previousCacheEntry", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "response", "batch", "cache<PERSON>ey", "resolve", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kind", "routeKind", "resolved", "cachedResponse", "isStale", "isPrefetch", "cacheEntry", "isRevalidating", "undefined", "resolveValue", "isMiss", "cacheControl", "set", "value", "err", "newRevalidate", "Math", "min", "max", "revalidate", "newExpire", "expire", "console", "error"], "mappings": "AAQA,SAASA,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SACEC,sBAAsB,EACtBC,+BAA+B,EAC/BC,oBAAoB,QACf,UAAS;AAGhB,cAAc,UAAS;AAEvB,eAAe,MAAMC;IAwBnBC,YAAYC,WAAoB,CAAE;aAvBjBC,UAAUR,QAAQS,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,GAAGD,IAAI,CAAC,EAAEC,uBAAuB,MAAM,KAAK;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaZ;QACf;QAWE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMa,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGP;IACzB;IAEA,MAAaQ,IACXJ,GAAkB,EAClBK,iBAAoC,EACpCC,OAOC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACN,KAAK;YACR,OAAOK,kBAAkB;gBAAEE,aAAa;gBAAOC,oBAAoB;YAAK;QAC1E;QAEA,MAAM,EACJC,gBAAgB,EAChBR,uBAAuB,KAAK,EAC5BS,aAAa,KAAK,EAClBC,oBAAoB,KAAK,EAC1B,GAAGL;QAEJ,MAAMM,WAAW,MAAM,IAAI,CAACf,OAAO,CAACgB,KAAK,CACvC;YAAEb;YAAKC;QAAqB,GAC5B,OAAOa,UAAUC;gBAKb;YAJF,+DAA+D;YAC/D,iDAAiD;YACjD,IACE,IAAI,CAACnB,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACoB,iBAAiB,qBAAtB,wBAAwBhB,GAAG,MAAKc,YAChC,IAAI,CAACE,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;gBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;YACrC;YAEA,mEAAmE;YACnE,MAAMC,OAAO7B,gCAAgCc,QAAQgB,SAAS;YAE9D,IAAIC,WAAW;YACf,IAAIC,iBAAuD;YAC3D,IAAI;gBACFA,iBAAiB,CAAC,IAAI,CAAC5B,WAAW,GAC9B,MAAMa,iBAAiBL,GAAG,CAACJ,KAAK;oBAC9BqB;oBACAV,mBAAmBL,QAAQK,iBAAiB;oBAC5CD;gBACF,KACA;gBAEJ,IAAIc,kBAAkB,CAACvB,sBAAsB;oBAC3Cc,QAAQS;oBACRD,WAAW;oBAEX,IAAI,CAACC,eAAeC,OAAO,IAAInB,QAAQoB,UAAU,EAAE;wBACjD,oDAAoD;wBACpD,oBAAoB;wBACpB,OAAO;oBACT;gBACF;gBAEA,MAAMC,aAAa,MAAMtB,kBAAkB;oBACzCE,aAAagB;oBACbf,oBAAoBgB;oBACpBI,gBAAgB;gBAClB;gBAEA,mEAAmE;gBACnE,cAAc;gBACd,IAAI,CAACD,YAAY;oBACf,+CAA+C;oBAC/C,IAAI,IAAI,CAAC/B,WAAW,EAAE,IAAI,CAACoB,iBAAiB,GAAGa;oBAC/C,OAAO;gBACT;gBAEA,MAAMC,eAAe,MAAMvC,uBAAuB;oBAChD,GAAGoC,UAAU;oBACbI,QAAQ,CAACP;gBACX;gBACA,IAAI,CAACM,cAAc;oBACjB,+CAA+C;oBAC/C,IAAI,IAAI,CAAClC,WAAW,EAAE,IAAI,CAACoB,iBAAiB,GAAGa;oBAC/C,OAAO;gBACT;gBAEA,+DAA+D;gBAC/D,yBAAyB;gBACzB,IAAI,CAAC5B,wBAAwB,CAACsB,UAAU;oBACtCR,QAAQe;oBACRP,WAAW;gBACb;gBAEA,qEAAqE;gBACrE,WAAW;gBACX,IAAIO,aAAaE,YAAY,EAAE;oBAC7B,IAAI,IAAI,CAACpC,WAAW,EAAE;wBACpB,IAAI,CAACoB,iBAAiB,GAAG;4BACvBhB,KAAKc;4BACLM,OAAOU;4BACPb,WAAWC,KAAKC,GAAG,KAAK;wBAC1B;oBACF,OAAO;wBACL,MAAMV,iBAAiBwB,GAAG,CAACjC,KAAK8B,aAAaI,KAAK,EAAE;4BAClDF,cAAcF,aAAaE,YAAY;4BACvCrB;4BACAD;wBACF;oBACF;gBACF;gBAEA,OAAOoB;YACT,EAAE,OAAOK,KAAK;gBACZ,qEAAqE;gBACrE,qEAAqE;gBACrE,IAAIX,kCAAAA,eAAgBQ,YAAY,EAAE;oBAChC,MAAMI,gBAAgBC,KAAKC,GAAG,CAC5BD,KAAKE,GAAG,CAACf,eAAeQ,YAAY,CAACQ,UAAU,IAAI,GAAG,IACtD;oBAGF,MAAMC,YACJjB,eAAeQ,YAAY,CAACU,MAAM,KAAKb,YACnCA,YACAQ,KAAKE,GAAG,CACNH,gBAAgB,GAChBZ,eAAeQ,YAAY,CAACU,MAAM;oBAG1C,MAAMjC,iBAAiBwB,GAAG,CAACjC,KAAKwB,eAAeU,KAAK,EAAE;wBACpDF,cAAc;4BAAEQ,YAAYJ;4BAAeM,QAAQD;wBAAU;wBAC7D9B;wBACAD;oBACF;gBACF;gBAEA,qEAAqE;gBACrE,kDAAkD;gBAClD,IAAIa,UAAU;oBACZoB,QAAQC,KAAK,CAACT;oBACd,OAAO;gBACT;gBAEA,gEAAgE;gBAChE,MAAMA;YACR;QACF;QAGF,OAAO1C,qBAAqBmB;IAC9B;AACF"}