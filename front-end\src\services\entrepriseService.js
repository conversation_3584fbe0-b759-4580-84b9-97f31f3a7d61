import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const getCompanyData = async () => {
  try {
    // Ajout d'un timestamp pour éviter le cache
    const timestamp = new Date().getTime();
    const response = await api.get(`/company-profile?timestamp=${timestamp}`);

    console.log('Réponse de company-profile:', response.status, response.data);

    if (response.data && response.data.message === "Aucun profil d'entreprise trouvé") {
      console.log('Aucun profil d\'entreprise trouvé, retour null');
      return { data: null }; // Return null if no company profile is found
    }

    return { data: response.data };
  } catch (error) {
    console.error('Erreur lors de la récupération des données entreprise:', error);

    // Créer un profil d'entreprise par défaut
    const defaultCompanyData = {
      nom: 'Mon Entreprise',
      prefixeFacture: 'FACT-',
      prefixeDevis: 'DEV-',
      prochainNumeroFacture: 1,
      prochainNumeroDevis: 1,
      tauxTVA: 20,
      delaiPaiement: 30,
      piedPage: 'Merci pour votre confiance',
      couleurPrincipale: '#1976D2',
      couleurSecondaire: '#F5F5F5',
      theme: 'light'
    };

    console.log('Retour des données d\'entreprise par défaut:', defaultCompanyData);
    return { data: defaultCompanyData };
  }
};

export const updateCompanyData = async (data, logoFile, signatureFile) => {
  try {
    const companyData = {
      nom: data.nom || '',
      raisonSociale: data.raisonSociale || '',
      numéroFiscal: data.numéroFiscal || '',
      numéroTVA: data.numéroTVA || '',
      formeJuridique: data.formeJuridique || '',
      capitalSocial: data.capitalSocial || '',
      adresse: data.adresse || '',
      codePostal: data.codePostal || '',
      ville: data.ville || '',
      pays: data.pays || 'France',
      telephone: data.telephone || '',
      email: data.email || '',
      siteWeb: data.siteWeb || '',
      nomContact: data.nomContact || '',
      emailContact: data.emailContact || '',
      telephoneContact: data.telephoneContact || '',
      prefixeFacture: data.prefixeFacture || 'FACT-',
      prefixeDevis: data.prefixeDevis || 'DEV-',
      prochainNumeroFacture: data.prochainNumeroFacture || 1,
      prochainNumeroDevis: data.prochainNumeroDevis || 1,
      tauxTVA: data.tauxTVA || 20,
      delaiPaiement: data.delaiPaiement || 30,
      mentionsLegales: data.mentionsLegales || '',
      piedPage: data.piedPage || 'Merci pour votre confiance',
      nomBanque: data.nomBanque || '',
      titulaireCompte: data.titulaireCompte || '',
      iban: data.iban || '',
      bicSwift: data.bicSwift || '',
      couleurPrincipale: data.couleurPrincipale || '#1976D2',
      couleurSecondaire: data.couleurSecondaire || '#F5F5F5',
      logo: data.logo || '',
      signature: data.signature || '',
      signatureElectronique: data.signatureElectronique || false,
      paiementEnLigne: data.paiementEnLigne || false,
      notifications: data.notifications || false, // Include notifications
      rappelsAutomatiques: data.rappelsPaiement || false, // Map rappelsPaiement to rappelsAutomatiques
      theme: data.theme || 'light', // Include theme
      numerotationChronologique: data.numerotationChronologique || false,
    };

    const formData = new FormData();
    Object.keys(companyData).forEach((key) => {
      formData.append(key, companyData[key]);
    });

    if (logoFile) {
      formData.append('logo', logoFile);
    }
    if (signatureFile) {
      formData.append('signature', signatureFile);
    }

    const response = await api.post('/company-profile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour des données entreprise:', error);
    throw error.response?.data || new Error('Erreur lors de la mise à jour des données entreprise');
  }
};

export const updatePreferences = async (preferences) => {
  try {
    const companyData = {
      notifications: preferences.notifications || false,
      rappelsAutomatiques: preferences.rappelsPaiement || false,
    };

    const formData = new FormData();
    Object.keys(companyData).forEach((key) => {
      formData.append(key, companyData[key]);
    });

    const response = await api.post('/company-profile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour des préférences:', error);
    throw error.response?.data || new Error('Erreur lors de la mise à jour des préférences');
  }
};

export const updateTheme = async (theme) => {
  try {
    const companyData = {
      theme: theme || 'light',
    };

    const formData = new FormData();
    Object.keys(companyData).forEach((key) => {
      formData.append(key, companyData[key]);
    });

    const response = await api.post('/company-profile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour du thème:', error);
    throw error.response?.data || new Error('Erreur lors de la mise à jour du thème');
  }
};

export const uploadLogo = async (file) => {
  const formData = new FormData();
  formData.append('logo', file);
  try {
    const response = await api.post('/upload/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.url;
  } catch (error) {
    throw new Error("Erreur lors de l'upload du logo");
  }
};

export const uploadSignature = async (file) => {
  const formData = new FormData();
  formData.append('signature', file);
  try {
    const response = await api.post('/upload/signature', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.url;
  } catch (error) {
    throw new Error("Erreur lors de l'upload de la signature");
  }
};