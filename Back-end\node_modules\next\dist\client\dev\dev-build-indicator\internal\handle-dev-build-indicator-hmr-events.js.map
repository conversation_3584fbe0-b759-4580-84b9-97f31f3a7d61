{"version": 3, "sources": ["../../../../../src/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.ts"], "sourcesContent": ["import {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type HMR_ACTION_TYPES,\n} from '../../../../server/dev/hot-reloader-types'\nimport { devBuildIndicator } from './dev-build-indicator'\n\n/**\n * Handles HMR events to control the dev build indicator visibility.\n * Shows indicator when building and hides it when build completes or syncs.\n */\nexport const handleDevBuildIndicatorHmrEvents = (obj: HMR_ACTION_TYPES) => {\n  try {\n    if (!('action' in obj)) {\n      return\n    }\n\n    // eslint-disable-next-line default-case\n    switch (obj.action) {\n      case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n        devBuildIndicator.show()\n        break\n      case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n      case HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n        devBuildIndicator.hide()\n        break\n    }\n  } catch {}\n}\n"], "names": ["handleDevBuildIndicatorHmrEvents", "obj", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "devBuildIndicator", "show", "BUILT", "SYNC", "hide"], "mappings": ";;;;+BAUaA;;;eAAAA;;;kCAPN;mCAC2B;AAM3B,MAAMA,mCAAmC,CAACC;IAC/C,IAAI;QACF,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;YACtB;QACF;QAEA,wCAAwC;QACxC,OAAQA,IAAIC,MAAM;YAChB,KAAKC,6CAA2B,CAACC,QAAQ;gBACvCC,oCAAiB,CAACC,IAAI;gBACtB;YACF,KAAKH,6CAA2B,CAACI,KAAK;YACtC,KAAKJ,6CAA2B,CAACK,IAAI;gBACnCH,oCAAiB,CAACI,IAAI;gBACtB;QACJ;IACF,EAAE,UAAM,CAAC;AACX"}