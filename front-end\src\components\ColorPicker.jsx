"use client"

import React from "react"
import { Box, TextField, Popover } from "@mui/material"
import { ChromePicker } from "react-color"

const ColorPicker = ({ label, value, onChange }) => {
  const [anchorEl, setAnchorEl] = React.useState(null)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const open = <PERSON><PERSON><PERSON>(anchorEl)

  return (
    <Box>
      <TextField
        label={label}
        value={value}
        onClick={handleClick}
        InputProps={{
          startAdornment: (
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: value,
                border: "1px solid #ccc",
                mr: 1,
              }}
            />
          ),
          readOnly: true,
        }}
        fullWidth
      />
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <ChromePicker color={value} onChangeComplete={(color) => onChange(color.hex)} />
      </Popover>
    </Box>
  )
}

export default ColorPicker

