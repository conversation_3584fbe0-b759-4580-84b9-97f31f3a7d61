{"version": 3, "sources": ["../../../src/server/normalizers/prefixing-normalizer.ts"], "sourcesContent": ["import path from '../../shared/lib/isomorphic/path'\nimport type { Normalizer } from './normalizer'\n\nexport class PrefixingNormalizer implements Normalizer {\n  private readonly prefix: string\n\n  constructor(...prefixes: ReadonlyArray<string>) {\n    this.prefix = path.posix.join(...prefixes)\n  }\n\n  public normalize(pathname: string): string {\n    return path.posix.join(this.prefix, pathname)\n  }\n}\n"], "names": ["PrefixingNormalizer", "constructor", "prefixes", "prefix", "path", "posix", "join", "normalize", "pathname"], "mappings": ";;;;+BAGaA;;;eAAAA;;;6DAHI;;;;;;AAGV,MAAMA;IAGXC,YAAY,GAAGC,QAA+B,CAAE;QAC9C,IAAI,CAACC,MAAM,GAAGC,aAAI,CAACC,KAAK,CAACC,IAAI,IAAIJ;IACnC;IAEOK,UAAUC,QAAgB,EAAU;QACzC,OAAOJ,aAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACH,MAAM,EAAEK;IACtC;AACF"}