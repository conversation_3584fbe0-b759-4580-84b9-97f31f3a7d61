import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  useTheme,
  styled,
  IconButton,
  Tooltip,
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ReceiptIcon from '@mui/icons-material/Receipt';
import DescriptionIcon from '@mui/icons-material/Description';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import MenuIcon from '@mui/icons-material/Menu';
import PaymentIcon from '@mui/icons-material/Payment';
import BusinessIcon from '@mui/icons-material/Business';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import ApartmentIcon from '@mui/icons-material/Apartment';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import PeopleIcon from '@mui/icons-material/People';
import InventoryIcon from '@mui/icons-material/Inventory';
import GroupsIcon from '@mui/icons-material/Groups';
import FolderIcon from '@mui/icons-material/Folder';
import SubscriptionsIcon from '@mui/icons-material/Subscriptions';
import TemplateIcon from '@mui/icons-material/Article';
import { useMediaQuery } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import PersonIcon from '@mui/icons-material/Person';

// Styled components
const drawerWidth = 280; // Increased width to accommodate longer text
const miniDrawerWidth = 70;

const StyledDrawer = styled(Drawer)(({ theme, open, mini }) => ({
  width: mini ? miniDrawerWidth : drawerWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  overflowX: 'hidden',
  overflowY: 'auto', // Enable scrolling
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  '& .MuiDrawer-paper': {
    width: mini ? miniDrawerWidth : drawerWidth,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    borderRight: 'none',
    boxShadow: theme.palette.mode === 'dark' ? 'none' : '0 4px 20px rgba(0, 0, 0, 0.05)',
    overflowX: 'hidden',
    overflowY: 'auto', // Enable scrolling
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    // Custom scrollbar styling
    '&::-webkit-scrollbar': {
      width: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: 'transparent',
    },
    '&::-webkit-scrollbar-thumb': {
      background: theme.palette.mode === 'dark'
        ? 'rgba(255, 255, 255, 0.2)'
        : 'rgba(0, 0, 0, 0.2)',
      borderRadius: '3px',
      transition: 'background 0.2s ease',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: theme.palette.mode === 'dark'
        ? 'rgba(255, 255, 255, 0.3)'
        : 'rgba(0, 0, 0, 0.3)',
    },
    scrollbarWidth: 'thin',
    scrollbarColor: theme.palette.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.2) transparent'
      : 'rgba(0, 0, 0, 0.2) transparent',
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2, 1.5), // Adjusted padding
  borderBottom: `1px solid ${theme.palette.divider}`,
  minHeight: '70px', // Increased minimum height
  maxHeight: '90px', // Allow even more height for text
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(to right, rgba(58, 110, 165, 0.1), rgba(42, 80, 128, 0.05))'
    : 'linear-gradient(to right, rgba(58, 110, 165, 0.05), rgba(42, 80, 128, 0.02))',
  overflow: 'visible', // Ensure text is not clipped
  flexWrap: 'nowrap', // Prevent wrapping of flex items
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  fontWeight: 700,
  textTransform: 'uppercase',
  letterSpacing: '0.8px',
  color: theme.palette.mode === 'dark'
    ? theme.palette.primary.light
    : theme.palette.primary.dark,
  padding: theme.spacing(2.5, 2.5, 1),
  opacity: 0.85,
  marginTop: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: theme.spacing(2.5),
    width: '30px',
    height: '2px',
    backgroundColor: theme.palette.primary.main,
    opacity: 0.5,
    borderRadius: '2px',
  }
}));

const StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  borderRadius: 12,
  margin: theme.spacing(0.5, 1.5),
  padding: theme.spacing(1.2, 2),
  backgroundColor: active ?
    (theme.palette.mode === 'dark'
      ? `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.18)`
      : `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.1)`)
    : 'transparent',
  transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: active ?
    (theme.palette.mode === 'dark'
      ? '0 2px 8px rgba(0, 0, 0, 0.15)'
      : '0 2px 8px rgba(58, 110, 165, 0.08)')
    : 'none',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: active ? '4px' : '0px',
    backgroundColor: theme.palette.primary.main,
    borderRadius: '4px',
    transition: 'width 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  '&:hover': {
    backgroundColor: active
      ? (theme.palette.mode === 'dark'
        ? `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.25)`
        : `rgba(${parseInt(theme.palette.primary.main.slice(1, 3), 16)}, ${parseInt(theme.palette.primary.main.slice(3, 5), 16)}, ${parseInt(theme.palette.primary.main.slice(5, 7), 16)}, 0.15)`)
      : theme.palette.action.hover,
    transform: 'translateX(3px)',
    boxShadow: active ?
      (theme.palette.mode === 'dark'
        ? '0 4px 12px rgba(0, 0, 0, 0.2)'
        : '0 4px 12px rgba(58, 110, 165, 0.12)')
      : 'none',
    '&::before': {
      width: '4px',
    },
  },
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme, active }) => ({
  minWidth: '40px',
  color: active ? theme.palette.primary.main : theme.palette.text.secondary,
  '& .MuiSvgIcon-root': {
    fontSize: '1.3rem',
    transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
    transform: active ? 'scale(1.15)' : 'scale(1)',
    filter: active ? `drop-shadow(0 2px 4px ${theme.palette.mode === 'dark'
      ? 'rgba(0, 0, 0, 0.3)'
      : 'rgba(58, 110, 165, 0.2)'})` : 'none',
  }
}));

const StyledListItemText = styled(ListItemText)(({ theme, active }) => ({
  '& .MuiTypography-root': {
    fontWeight: active ? 600 : 500,
    fontSize: '0.95rem',
    color: active ? theme.palette.text.primary : theme.palette.text.secondary,
    transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
    letterSpacing: '0.01em',
    position: 'relative',
    display: 'inline-block',
  },
}));

const ClientSidebar = ({ darkMode }) => {
  const theme = useTheme();
  const location = useLocation();
  const { currentUser } = useAuth();
  const [open, setOpen] = useState(true);
  const [mini, setMini] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Check if user is a responsable or client
  const isResponsable = currentUser && currentUser.role === 'RESPONSABLE';
  const isClient = currentUser && currentUser.role === 'CLIENT';

  // Préfixe de chemin selon le rôle
  const pathPrefix = isClient ? '/client' : '/responsable';

  const isActive = (path) => {
    return location.pathname === path;
  };

  const isActivePrefix = (prefix) => {
    return location.pathname.startsWith(prefix);
  };

  const isActiveGroup = (paths) => {
    return paths.some(path => location.pathname.startsWith(path));
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMiniDrawerToggle = () => {
    setMini(!mini);
  };

  const drawer = (
    <>
      <LogoContainer>
        <IconButton
          onClick={handleMiniDrawerToggle}
          sx={{
            mr: 1,
            color: theme.palette.primary.main,
            backgroundColor: theme.palette.mode === 'dark'
              ? 'rgba(58, 110, 165, 0.1)'
              : 'rgba(58, 110, 165, 0.05)',
            border: `1px solid ${theme.palette.mode === 'dark'
              ? 'rgba(58, 110, 165, 0.2)'
              : 'rgba(58, 110, 165, 0.1)'}`,
            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(58, 110, 165, 0.2)'
                : 'rgba(58, 110, 165, 0.1)',
              transform: 'translateY(-1px)',
              boxShadow: '0 3px 8px rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          {mini ? <MenuIcon /> : <ChevronLeftIcon />}
        </IconButton>
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            color: "#fff",
            width: 32,
            height: 32,
            boxShadow: "0 4px 12px rgba(58, 110, 165, 0.25)",
            mr: 1,
            border: '1.5px solid white',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'scale(1.05)',
              boxShadow: "0 6px 16px rgba(58, 110, 165, 0.35)",
            },
            display: mini ? 'none' : 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #3a6ea5, #4f83b9)',
            flexShrink: 0, // Prevent avatar from shrinking
          }}
        >
          {isResponsable ?
            <ApartmentIcon sx={{
              fontSize: '1.2rem',
              filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15))'
            }} /> :
            <Typography sx={{
              fontWeight: 'bold',
              fontSize: '1.2rem',
              filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15))'
            }}>C</Typography>
          }
        </Avatar>
        {!mini && (
          <Box sx={{
            flex: 1,
            minWidth: 0, // Allow text to shrink
            overflow: 'visible',
            paddingLeft: 0.5, // Small padding for better spacing
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}>
            <Typography variant="subtitle1" component="div" sx={{
              fontWeight: 700,
              fontSize: '0.85rem', // Smaller font to ensure it fits
              color: theme.palette.primary.main,
              letterSpacing: '0.01em',
              position: 'relative',
              display: 'block',
              mb: 0.1,
              lineHeight: 1.1,
              whiteSpace: 'normal', // Allow wrapping if needed
              overflow: 'visible',
              textOverflow: 'clip',
              wordBreak: 'break-word', // Break long words if necessary
              maxWidth: '100%', // Ensure it doesn't exceed container
            }}>
              {isResponsable ? 'Responsable d\'Entreprise' : 'Espace Client'}
            </Typography>
            <Typography variant="caption" sx={{
              color: theme.palette.text.secondary,
              fontSize: '0.7rem',
              display: 'block',
              mt: 0,
              fontWeight: 500,
              letterSpacing: '0.01em',
              opacity: 0.75,
              lineHeight: 1.1,
            }}>
              {isResponsable ? 'Pilotage & Facturation' : 'Gestion client'}
            </Typography>
          </Box>
        )}
      </LogoContainer>

      <Box sx={{
        overflowY: "auto", // Enable automatic scrollbar
        height: "calc(100vh - 90px)", // Adjusted for new header height
        // Custom scrollbar styling
        '&::-webkit-scrollbar': {
          width: '6px',
        },
        '&::-webkit-scrollbar-track': {
          background: 'transparent',
        },
        '&::-webkit-scrollbar-thumb': {
          background: theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.2)'
            : 'rgba(0, 0, 0, 0.2)',
          borderRadius: '3px',
          transition: 'background 0.2s ease',
        },
        '&::-webkit-scrollbar-thumb:hover': {
          background: theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.3)'
            : 'rgba(0, 0, 0, 0.3)',
        },
        // Firefox scrollbar styling
        scrollbarWidth: 'thin',
        scrollbarColor: theme.palette.mode === 'dark'
          ? 'rgba(255, 255, 255, 0.2) transparent'
          : 'rgba(0, 0, 0, 0.2) transparent',
      }}>
        <List component="nav" sx={{ p: mini ? 1 : 2 }}>
          {!mini && <SectionTitle>Principal</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Tableau de bord" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to={isClient ? `${pathPrefix}/dashboard` : `${pathPrefix}/analytics`}
                active={isClient ? isActivePrefix(`${pathPrefix}/dashboard`) : isActivePrefix(`${pathPrefix}/analytics`)}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isClient ? isActivePrefix(`${pathPrefix}/dashboard`) : isActivePrefix(`${pathPrefix}/analytics`)}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <AnalyticsIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Tableau de bord" active={isClient ? isActivePrefix(`${pathPrefix}/dashboard`) : isActivePrefix(`${pathPrefix}/analytics`)} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && <SectionTitle>Documents</SectionTitle>}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Mes Factures" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to={`${pathPrefix}/factures`}
                active={isActivePrefix(`${pathPrefix}/factures`)}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActivePrefix(`${pathPrefix}/factures`)}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <ReceiptIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Mes Factures" active={isActivePrefix(`${pathPrefix}/factures`)} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Mes Devis" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to={`${pathPrefix}/devis`}
                active={isActivePrefix(`${pathPrefix}/devis`)}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActivePrefix(`${pathPrefix}/devis`)}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <DescriptionIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Mes Devis" active={isActivePrefix(`${pathPrefix}/devis`)} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Mes Paiements" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to={`${pathPrefix}/paiements`}
                active={isActivePrefix(`${pathPrefix}/paiements`)}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3
                }}
              >
                <StyledListItemIcon
                  active={isActivePrefix(`${pathPrefix}/paiements`)}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <PaymentIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Mes Paiements" active={isActivePrefix(`${pathPrefix}/paiements`)} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>

          {!mini && isResponsable && <SectionTitle>GESTION</SectionTitle>}

          {isResponsable && (
            <>
              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title={mini ? "Clients" : ""} placement="right">
                  <StyledListItemButton
                    component={Link}
                    to={`${pathPrefix}/clients`}
                    active={isActivePrefix(`${pathPrefix}/clients`)}
                    sx={{
                      justifyContent: mini ? 'center' : 'flex-start',
                      px: mini ? 2 : 3
                    }}
                  >
                    <StyledListItemIcon
                      active={isActivePrefix(`${pathPrefix}/clients`)}
                      sx={{ minWidth: mini ? 0 : 40 }}
                    >
                      <PeopleIcon />
                    </StyledListItemIcon>
                    {!mini && <StyledListItemText primary="Clients" active={isActivePrefix(`${pathPrefix}/clients`)} />}
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>

              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title={mini ? "Produits" : ""} placement="right">
                  <StyledListItemButton
                    component={Link}
                    to={`${pathPrefix}/produits`}
                    active={isActivePrefix(`${pathPrefix}/produits`)}
                    sx={{
                      justifyContent: mini ? 'center' : 'flex-start',
                      px: mini ? 2 : 3
                    }}
                  >
                    <StyledListItemIcon
                      active={isActivePrefix(`${pathPrefix}/produits`)}
                      sx={{ minWidth: mini ? 0 : 40 }}
                    >
                      <InventoryIcon />
                    </StyledListItemIcon>
                    {!mini && <StyledListItemText primary="Produits" active={isActivePrefix(`${pathPrefix}/produits`)} />}
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>
            </>
          )}

          {isResponsable && (
            <ListItem disablePadding sx={{ mb: 1 }}>
              <Tooltip title={mini ? "Vendeurs" : ""} placement="right">
                <StyledListItemButton
                  component={Link}
                  to={`${pathPrefix}/vendeurs`}
                  active={isActivePrefix(`${pathPrefix}/vendeurs`)}
                  sx={{
                    justifyContent: mini ? 'center' : 'flex-start',
                    px: mini ? 2 : 3
                  }}
                >
                  <StyledListItemIcon
                    active={isActivePrefix(`${pathPrefix}/vendeurs`)}
                    sx={{ minWidth: mini ? 0 : 40 }}
                  >
                    <GroupsIcon />
                  </StyledListItemIcon>
                  {!mini && <StyledListItemText primary="Vendeurs" active={isActivePrefix(`${pathPrefix}/vendeurs`)} />}
                </StyledListItemButton>
              </Tooltip>
            </ListItem>
          )}

          {/* Livraison Section - Only for Responsable */}
          {isResponsable && (
            <>
              {!mini && <SectionTitle sx={{ mt: 3 }}>Livraison</SectionTitle>}

              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title={mini ? "Livreurs" : ""} placement="right">
                  <StyledListItemButton
                    component={Link}
                    to={`${pathPrefix}/livreurs`}
                    active={isActivePrefix(`${pathPrefix}/livreurs`)}
                    sx={{
                      justifyContent: mini ? 'center' : 'flex-start',
                      px: mini ? 2 : 3
                    }}
                  >
                    <StyledListItemIcon
                      active={isActivePrefix(`${pathPrefix}/livreurs`)}
                      sx={{ minWidth: mini ? 0 : 40 }}
                    >
                      <PersonIcon />
                    </StyledListItemIcon>
                    {!mini && <StyledListItemText primary="Livreurs" active={isActivePrefix(`${pathPrefix}/livreurs`)} />}
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>

              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title={mini ? "Bons de Livraison" : ""} placement="right">
                  <StyledListItemButton
                    component={Link}
                    to={`${pathPrefix}/bon-livraisons`}
                    active={isActivePrefix(`${pathPrefix}/bon-livraisons`)}
                    sx={{
                      justifyContent: mini ? 'center' : 'flex-start',
                      px: mini ? 2 : 3
                    }}
                  >
                    <StyledListItemIcon
                      active={isActivePrefix(`${pathPrefix}/bon-livraisons`)}
                      sx={{ minWidth: mini ? 0 : 40 }}
                    >
                      <LocalShippingIcon />
                    </StyledListItemIcon>
                    {!mini && <StyledListItemText primary="Bons de Livraison" active={isActivePrefix(`${pathPrefix}/bon-livraisons`)} />}
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>
            </>
          )}

          {/* Templates Section - Only for Responsable */}
          {isResponsable && (
            <>
              {!mini && <SectionTitle sx={{ mt: 3 }}>Personnalisation</SectionTitle>}

              <ListItem disablePadding sx={{ mb: 1 }}>
                <Tooltip title={mini ? "Templates" : ""} placement="right">
                  <StyledListItemButton
                    component={Link}
                    to={`${pathPrefix}/templates`}
                    active={isActivePrefix(`${pathPrefix}/templates`)}
                    sx={{
                      justifyContent: mini ? 'center' : 'flex-start',
                      px: mini ? 2 : 3
                    }}
                  >
                    <StyledListItemIcon
                      active={isActivePrefix(`${pathPrefix}/templates`)}
                      sx={{ minWidth: mini ? 0 : 40 }}
                    >
                      <TemplateIcon />
                    </StyledListItemIcon>
                    {!mini && <StyledListItemText primary="Templates" active={isActivePrefix(`${pathPrefix}/templates`)} />}
                  </StyledListItemButton>
                </Tooltip>
              </ListItem>
            </>
          )}

          {!mini && <SectionTitle>COMPTE</SectionTitle>}

          {isResponsable && (
            <ListItem disablePadding sx={{ mb: 1 }}>
              <Tooltip title={mini ? "Mon Entreprise" : ""} placement="right">
                <StyledListItemButton
                  component={Link}
                  to={`${pathPrefix}/entreprise`}
                  active={isActivePrefix(`${pathPrefix}/entreprise`)}
                  sx={{
                    justifyContent: mini ? 'center' : 'flex-start',
                    px: mini ? 2 : 3
                  }}
                >
                  <StyledListItemIcon
                    active={isActivePrefix(`${pathPrefix}/entreprise`)}
                    sx={{ minWidth: mini ? 0 : 40 }}
                  >
                    <BusinessIcon />
                  </StyledListItemIcon>
                  {!mini && <StyledListItemText primary="Mon Entreprise" active={isActivePrefix(`${pathPrefix}/entreprise`)} />}
                </StyledListItemButton>
              </Tooltip>
            </ListItem>
          )}

          {isResponsable && (
            <ListItem disablePadding sx={{ mb: 1 }}>
              <Tooltip title={mini ? "Abonnement" : ""} placement="right">
                <StyledListItemButton
                  component={Link}
                  to={`${pathPrefix}/abonnement`}
                  active={isActivePrefix(`${pathPrefix}/abonnement`)}
                  sx={{
                    justifyContent: mini ? 'center' : 'flex-start',
                    px: mini ? 2 : 3
                  }}
                >
                  <StyledListItemIcon
                    active={isActivePrefix(`${pathPrefix}/abonnement`)}
                    sx={{ minWidth: mini ? 0 : 40 }}
                  >
                    <SubscriptionsIcon />
                  </StyledListItemIcon>
                  {!mini && <StyledListItemText primary="Abonnement" active={isActivePrefix(`${pathPrefix}/abonnement`)} />}
                </StyledListItemButton>
              </Tooltip>
            </ListItem>
          )}

          <ListItem disablePadding sx={{ mb: 1 }}>
            <Tooltip title={mini ? "Mon Profil" : ""} placement="right">
              <StyledListItemButton
                component={Link}
                to={`${pathPrefix}/profile`}
                active={isActivePrefix(`${pathPrefix}/profile`)}
                sx={{
                  justifyContent: mini ? 'center' : 'flex-start',
                  px: mini ? 2 : 3,
                  bgcolor: 'rgba(25, 118, 210, 0.08)',
                  '&:hover': {
                    bgcolor: 'rgba(25, 118, 210, 0.15)',
                  }
                }}
              >
                <StyledListItemIcon
                  active={isActivePrefix(`${pathPrefix}/profile`)}
                  sx={{ minWidth: mini ? 0 : 40 }}
                >
                  <AccountCircleIcon />
                </StyledListItemIcon>
                {!mini && <StyledListItemText primary="Mon Profil" active={isActivePrefix(`${pathPrefix}/profile`)} />}
              </StyledListItemButton>
            </Tooltip>
          </ListItem>
        </List>
      </Box>
    </>
  );

  return (
    <>
      {isMobile ? (
        <>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              position: "fixed",
              top: 12,
              left: 12,
              zIndex: 1300,
              backgroundColor: theme.palette.background.paper,
              boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
              display: { md: "none" },
            }}
          >
            <MenuIcon />
          </IconButton>
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: "block", md: "none" },
              "& .MuiDrawer-paper": {
                width: drawerWidth,
                backgroundColor: theme.palette.background.paper,
                color: theme.palette.text.primary,
                overflowY: 'auto', // Enable scrolling on mobile too
                // Custom scrollbar styling for mobile
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                  background: 'transparent',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.2)'
                    : 'rgba(0, 0, 0, 0.2)',
                  borderRadius: '3px',
                },
                scrollbarWidth: 'thin',
                scrollbarColor: theme.palette.mode === 'dark'
                  ? 'rgba(255, 255, 255, 0.2) transparent'
                  : 'rgba(0, 0, 0, 0.2) transparent',
              },
            }}
          >
            {drawer}
          </Drawer>
        </>
      ) : (
        <StyledDrawer variant="permanent" open={open} mini={mini}>
          {drawer}
        </StyledDrawer>
      )}
    </>
  )
}

export default ClientSidebar;
