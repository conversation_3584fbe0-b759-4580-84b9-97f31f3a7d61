import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '@mui/material/styles';
import { motion } from 'framer-motion';
import {
  Box,
  Typography,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Autocomplete,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Snackbar,
  Alert,
  Switch,
  alpha,
  Avatar,
  Stack
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Email as EmailIcon,
  Download as DownloadIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  Info as InfoIcon,
  ShoppingCart as ShoppingCartIcon,
  Notes as NotesIcon,
  PhotoCamera as PhotoCameraIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import { getCompanyData } from '../services/entrepriseService';
import devisService from '../services/devisService';
import factureService from '../services/factureService';
import clientService from '../services/clientService';
import produitService from '../services/produitService';
import { getUsersByRole } from '../services/userService';
import DocumentPreview from '../components/DocumentPreview';
import EmailDialog from '../components/EmailDialog';
import { formatCurrency as formatCurrencyUtil, getDefaultCurrency, normalizeStatus } from '../utils/formatters';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 12
    }
  }
};

const DevisForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [companyData, setCompanyData] = useState(null);
  const [clients, setClients] = useState([]);
  const [produits, setProduits] = useState([]);
  const [vendeurs, setVendeurs] = useState([]);
  const [loadingVendeurs, setLoadingVendeurs] = useState(false);
  const [openClientDialog, setOpenClientDialog] = useState(false);
  const [openProduitDialog, setOpenProduitDialog] = useState(false);
  const [openEmailDialog, setOpenEmailDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [lastNuméro, setLastNuméro] = useState(0); // Store the last number used

  const [devis, setDevis] = useState({
    numéro: '',
    client: null,
    dateEmission: new Date().toISOString().split('T')[0],
    dateValidite: (() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    })(),
    lignes: [],
    notes: '',
    statut: 'BROUILLON',
    montantHT: 0,
    montantTTC: 0,
    tauxTVA: 20
  });

  const [newClient, setNewClient] = useState({
    nom: '',
    adresse: '',
    codePostal: '',
    ville: '',
    pays: 'France',
    email: '', // Champ visible maintenant, pas besoin de valeur par défaut
    telephone: '',
    actif: true,
    vendeurId: null, // Null au lieu de chaîne vide pour éviter l'erreur de cast
    logo: null,
    contact: 'Contact principal', // Champ requis par le modèle
    cin: '', // Numéro de Carte d'Identité Nationale
    motDePasse: '', // Mot de passe pour le compte utilisateur
    createUserAccount: true // Option pour créer un compte utilisateur
  });

  const [showPassword, setShowPassword] = useState(false);
  const [clientLogoPreview, setClientLogoPreview] = useState(null);

  const [newProduit, setNewProduit] = useState({
    nom: '',
    description: '',
    descriptionDetaille: '',
    prix: 0,
    tauxTVA: 20,
    unite: 'unité',
    category: 'Non classé',
    gestionStock: false,
    quantiteStock: 0,
    seuilAlerte: 5,
    image: null
  });

  const [imagePreview, setImagePreview] = useState(null);

  const [produitTabValue, setProduitTabValue] = useState(0);

  // Function to generate a unique numéro
  const generateNuméro = async (prefix = 'DEV-', year = new Date().getFullYear()) => {
    try {
      const allDevis = await devisService.getDevis();
      const devisForYear = allDevis.filter(d => d.numéro && d.numéro.startsWith(`${prefix}${year}-`));
      let nextNum = 1;

      if (devisForYear.length > 0) {
        const lastNum = Math.max(
          ...devisForYear.map(d => {
            const numPart = parseInt(d.numéro.split('-')[2]);
            return isNaN(numPart) ? 0 : numPart;
          })
        );
        nextNum = lastNum + 1;
      }

      setLastNuméro(nextNum);
      const formattedNum = String(nextNum).padStart(4, '0');
      return `${prefix}${year}-${formattedNum}`;
    } catch (error) {
      console.error('Erreur lors de la génération du numéro:', error);
      const year = new Date().getFullYear();
      return `${prefix}${year}-0001`;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Récupérer les paramètres depuis le localStorage
        let defaultTaxRate = 20;
        try {
          const appSettings = localStorage.getItem('appSettings');
          if (appSettings) {
            const parsedSettings = JSON.parse(appSettings);
            if (parsedSettings.defaultTaxRate) {
              defaultTaxRate = parsedSettings.defaultTaxRate;
              console.log('Loaded tax rate from localStorage:', defaultTaxRate);
            }
          }
        } catch (localStorageError) {
          console.error('Error loading settings from localStorage:', localStorageError);
        }

        const company = await getCompanyData();
        if (company && company.length > 0) {
          setCompanyData(company[0]);
          // Utiliser le taux de TVA du localStorage en priorité
          setDevis(prev => ({
            ...prev,
            tauxTVA: defaultTaxRate
          }));

          if (!id) {
            const prefix = company[0].facturation?.prefixeDevis || 'DEV-';
            const year = new Date().getFullYear();
            const generatedNuméro = await generateNuméro(prefix, year);
            setDevis(prev => ({
              ...prev,
              numéro: generatedNuméro
            }));
          }
        } else {
          const year = new Date().getFullYear();
          setDevis(prev => ({
            ...prev,
            numéro: `DEV-${year}-0001`,
            tauxTVA: defaultTaxRate
          }));
        }

        const clientsData = await clientService.getClients();
        setClients(clientsData);

        const produitsData = await produitService.getProduits();
        console.log('Products loaded for devis form:', produitsData);
        setProduits(produitsData);

        if (id) {
          const devisData = await devisService.getDevisById(id);
          console.log('Loaded devis data:', devisData);
          setDevis({
            ...devisData,
            numéro: devisData.numéro,
            dateEmission: new Date(devisData.dateCréation).toISOString().split('T')[0],
            dateValidite: new Date(devisData.dateValidite || new Date(devisData.dateCréation).setDate(new Date(devisData.dateCréation).getDate() + 30)).toISOString().split('T')[0],
            client: clientsData.find(client => client._id === devisData.clientId),
            lignes: devisData.lignes || [],
            notes: devisData.notes || '',
            statut: devisData.statut || 'BROUILLON', // Keep the original uppercase status
            montantHT: devisData.montantHT || 0,
            montantTTC: devisData.montantTTC || 0,
            tauxTVA: devisData.tauxTVA || company[0]?.facturation?.tauxTva || 20
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setSnackbar({
          open: true,
          message: 'Erreur lors du chargement des données: ' + error.message,
          severity: 'error'
        });
        const year = new Date().getFullYear();
        setDevis(prev => ({
          ...prev,
          numéro: `DEV-${year}-0001`
        }));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, navigate]);

  useEffect(() => {
    const montantHT = devis.lignes.reduce((sum, ligne) =>
      sum + (ligne.quantite * ligne.prixUnitaire), 0);

    const montantTTC = montantHT * (1 + devis.tauxTVA / 100);

    setDevis(prev => ({
      ...prev,
      montantHT,
      montantTTC
    }));
  }, [devis.lignes, devis.tauxTVA]);

  // Fetch vendeurs when dialog opens
  useEffect(() => {
    const fetchVendeurs = async () => {
      if (openClientDialog) {
        try {
          setLoadingVendeurs(true);
          // Get vendeurs based on user role
          if (currentUser && currentUser.role === 'ADMIN') {
            // Admin can see all vendeurs
            const vendeursList = await getUsersByRole('VENDEUR');
            setVendeurs(vendeursList);
          } else if (currentUser && currentUser.role === 'RESPONSABLE') {
            // Responsable can only see their vendeurs
            const responsableId = currentUser._id || currentUser.id;
            console.log('Fetching vendeurs for responsable:', responsableId);
            try {
              // Utiliser une API spécifique pour récupérer les vendeurs d'un responsable
              const vendeursList = await getUsersByRole('VENDEUR');
              console.log('All vendeurs:', vendeursList);

              // Filtrer les vendeurs qui ont ce responsable dans leur liste de responsables
              const filteredVendeurs = vendeursList.filter(vendeur =>
                vendeur.responsables &&
                (Array.isArray(vendeur.responsables) ?
                  vendeur.responsables.some(id => id === responsableId || id.toString() === responsableId.toString()) :
                  vendeur.responsables === responsableId || vendeur.responsables.toString() === responsableId.toString())
              );
              console.log('Filtered vendeurs for responsable:', filteredVendeurs);
              setVendeurs(filteredVendeurs);
            } catch (error) {
              console.error('Error fetching vendeurs for responsable:', error);
              setVendeurs([]);
            }
          } else {
            // Vendeur can't assign other vendeurs
            setVendeurs([]);
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des vendeurs:', error);
          setSnackbar({
            open: true,
            message: 'Erreur lors de la récupération des vendeurs',
            severity: 'error',
          });
        } finally {
          setLoadingVendeurs(false);
        }
      }
    };

    fetchVendeurs();
  }, [openClientDialog, currentUser]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(`Changing ${name} to ${value}`);

    // For status field, normalize to match the backend enum values
    if (name === 'statut') {
      // Normaliser le statut pour s'assurer qu'il est en anglais
      const normalizedStatus = normalizeStatus(value);
      console.log('Setting status to:', value, '(normalized to:', normalizedStatus, ')');

      setDevis(prev => ({
        ...prev,
        [name]: normalizedStatus
      }));
    } else {
      setDevis(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleClientChange = (event, newValue) => {
    setDevis(prev => ({
      ...prev,
      client: newValue
    }));
  };

  const handleAddLigne = () => {
    setDevis(prev => ({
      ...prev,
      lignes: [...prev.lignes, {
        produit: null,
        description: '',
        quantite: 1,
        prixUnitaire: 0,
        montantHT: 0,
        montantTTC: 0
      }]
    }));
  };

  const handleRemoveLigne = (index) => {
    setDevis(prev => ({
      ...prev,
      lignes: prev.lignes.filter((_, i) => i !== index)
    }));
  };

  const handleLigneChange = (index, field, value) => {
    setDevis(prev => {
      const newLignes = [...prev.lignes];
      newLignes[index] = {
        ...newLignes[index],
        [field]: value
      };

      if (field === 'produit' && value) {
        newLignes[index].description = value.description || value.nom;
        newLignes[index].prixUnitaire = value.prix || value.prixHT || 0;
        console.log('Product selected:', value);
        console.log('Setting price to:', value.prix || value.prixHT || 0);
      }

      const quantite = newLignes[index].quantite || 0;
      const prixUnitaire = newLignes[index].prixUnitaire || 0;
      newLignes[index].montantHT = quantite * prixUnitaire;
      newLignes[index].montantTTC = newLignes[index].montantHT * (1 + prev.tauxTVA / 100);

      return { ...prev, lignes: newLignes };
    });
  };

  const handleSave = async (asDraft = true) => {
    setSaving(true);
    try {
      // Validate required fields
      if (!devis.numéro) {
        throw new Error('Le numéro de devis est obligatoire');
      }
      if (!devis.client) {
        throw new Error('Veuillez sélectionner un client');
      }
      if (devis.lignes.length === 0) {
        throw new Error('Veuillez ajouter au moins une ligne de produit');
      }
      if (!devis.dateValidite) {
        throw new Error('La date de validité est obligatoire');
      }

      // Set the statut based on asDraft only for new devis, otherwise keep the selected status
      let statut;
      if (id) {
        // For existing devis, use the current status but normalize it
        statut = normalizeStatus(devis.statut);
      } else {
        // For new devis, set based on asDraft parameter and normalize
        const rawStatus = asDraft ? 'BROUILLON' : 'ENVOYÉ';
        statut = normalizeStatus(rawStatus);
      }

      console.log('Saving devis with statut:', statut, '(normalized from:', devis.statut, ')');

      // Prepare data for API
      const devisToSave = {
        numéro: devis.numéro,
        dateCréation: new Date(devis.dateEmission),
        dateValidite: new Date(devis.dateValidite),
        statut: statut,
        total: devis.montantTTC,
        montantHT: devis.montantHT,
        montantTTC: devis.montantTTC,
        tauxTVA: devis.tauxTVA,
        notes: devis.notes,
        clientId: devis.client._id,
        lignes: devis.lignes.map(ligne => ({
          produit: ligne.produit?._id || null,
          description: ligne.description || '',
          quantite: Number(ligne.quantite) || 0,
          prixUnitaire: Number(ligne.prixUnitaire) || 0,
          montantHT: Number(ligne.montantHT) || 0,
          montantTTC: Number(ligne.montantTTC) || 0
        }))
      };

      // Validate dates
      if (isNaN(devisToSave.dateCréation.getTime())) {
        throw new Error('La date d\'émission est invalide');
      }
      if (isNaN(devisToSave.dateValidite.getTime())) {
        throw new Error('La date de validité est invalide');
      }

      // Log the data being sent for debugging
      console.log('Data being sent to backend:', devisToSave);

      if (!devisToSave.clientId) {
        throw new Error('Client is required');
      }

      let result;
      if (id) {
        result = await devisService.updateDevis(id, devisToSave);
      } else {
        result = await devisService.createDevis(devisToSave);

        // Increment the local counter and generate the next numéro
        const prefix = companyData?.facturation?.prefixeDevis || 'DEV-';
        const year = new Date().getFullYear();
        const nextNum = lastNuméro + 1;
        setLastNuméro(nextNum);
        const formattedNum = String(nextNum).padStart(4, '0');
        const nextNuméro = `${prefix}${year}-${formattedNum}`;
        console.log('Next numéro after saving:', nextNuméro);

        // Reset the form with the new numéro
        setDevis({
          numéro: nextNuméro,
          client: null,
          dateEmission: new Date().toISOString().split('T')[0],
          dateValidite: (() => {
            const date = new Date();
            date.setDate(date.getDate() + 30);
            return date.toISOString().split('T')[0];
          })(),
          lignes: [],
          notes: '',
          statut: 'BROUILLON',
          montantHT: 0,
          montantTTC: 0,
          tauxTVA: companyData?.facturation?.tauxTva || 20
        });
      }

      setSnackbar({
        open: true,
        message: id ? 'Devis mis à jour avec succès' : 'Devis créé avec succès',
        severity: 'success'
      });

      // Add a button to navigate to the devis list
      setTimeout(() => {
        if (!id) {
          if (currentUser && currentUser.role === 'VENDEUR') {
            navigate('/vendeur/devis');
          } else if (currentUser && currentUser.role === 'ADMIN') {
            navigate('/admin/devis');
          } else if (currentUser && currentUser.role === 'RESPONSABLE') {
            navigate('/responsable/devis');
          } else {
            navigate('/devis');
          }
        }
      }, 1500);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      const errorMessage = error.response?.data?.message || error.response?.data?.errors?.join('\n') || error.message;
      setSnackbar({
        open: true,
        message: 'Erreur lors de la sauvegarde du devis: ' + errorMessage,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCreateFacture = async () => {
    try {
      const result = await factureService.createFromDevis(id);
      setSnackbar({
        open: true,
        message: 'Facture créée à partir du devis avec succès',
        severity: 'success'
      });

      // Navigate to the appropriate facture page based on user role
      if (currentUser && currentUser.role === 'VENDEUR') {
        navigate(`/vendeur/factures/${result._id}`);
      } else if (currentUser && currentUser.role === 'ADMIN') {
        navigate(`/admin/factures/${result._id}`);
      } else if (currentUser && currentUser.role === 'RESPONSABLE') {
        navigate(`/responsable/factures/${result._id}`);
      } else {
        navigate(`/factures/${result._id}`);
      }
    } catch (error) {
      console.error('Erreur lors de la création de la facture:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la création de la facture: ' + error.message,
        severity: 'error'
      });
    }
  };

  const handleCreateClient = async () => {
    try {
      // Vérifier les champs obligatoires
      if (!newClient.nom) {
        throw new Error('Le nom du client est obligatoire');
      }

      // Vérifier que le CIN contient exactement 8 chiffres
      if (newClient.cin) {
        const cinRegex = /^\d{8}$/;
        if (!cinRegex.test(newClient.cin)) {
          setSnackbar({
            open: true,
            message: 'Le CIN doit contenir exactement 8 chiffres',
            severity: 'error',
          });
          return;
        }
      } else {
        setSnackbar({
          open: true,
          message: 'Le numéro CIN est obligatoire',
          severity: 'error',
        });
        return;
      }

      // Vérifier que le mot de passe est fourni si createUserAccount est activé
      if (newClient.createUserAccount && !newClient.motDePasse) {
        setSnackbar({
          open: true,
          message: 'Veuillez fournir un mot de passe pour le compte utilisateur',
          severity: 'error',
        });
        return;
      }

      // Créer le client sans le logo d'abord
      const clientData = { ...newClient };
      delete clientData.logo; // Supprimer le logo de l'objet client

      // Si l'utilisateur est un vendeur, assigner automatiquement son ID comme vendeurId
      if (currentUser && currentUser.role === 'VENDEUR') {
        clientData.vendeurId = currentUser._id || currentUser.id;
        console.log('Assignation automatique du vendeur:', clientData.vendeurId);
      }
      // Sinon, gérer correctement le vendeurId (null si vide)
      else if (!clientData.vendeurId) {
        clientData.vendeurId = null;
      }

      // S'assurer que les champs requis sont présents
      if (!clientData.email) {
        clientData.email = '<EMAIL>'; // Valeur par défaut si le champ est vide
      }

      if (!clientData.contact) {
        clientData.contact = 'Contact principal';
      }

      // Ajouter le responsable actuel aux responsables du client si c'est un responsable d'entreprise
      if (currentUser && currentUser.role === 'RESPONSABLE') {
        clientData.responsables = [currentUser._id || currentUser.id];
      }

      console.log('Données client à envoyer:', clientData);

      const result = await clientService.createClient(clientData);

      // Si un logo a été sélectionné, l'uploader
      if (newClient.logo) {
        try {
          await clientService.uploadClientLogo(result._id, newClient.logo);
          // Mettre à jour le résultat avec le logo
          result.logo = clientLogoPreview;
        } catch (logoError) {
          console.error('Erreur lors de l\'upload du logo:', logoError);
          // Continuer même si l'upload du logo échoue
        }
      }

      setClients(prev => [...prev, result]);
      setDevis(prev => ({
        ...prev,
        client: result
      }));
      setOpenClientDialog(false);

      // Réinitialiser le formulaire
      setNewClient({
        nom: '',
        adresse: '',
        codePostal: '',
        ville: '',
        pays: 'France',
        email: '',
        telephone: '',
        actif: true,
        vendeurId: null,
        logo: null,
        contact: 'Contact principal',
        cin: '',
        motDePasse: '',
        createUserAccount: true
      });
      setClientLogoPreview(null);

      setSnackbar({
        open: true,
        message: 'Client créé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de la création du client:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la création du client: ' + error.message,
        severity: 'error'
      });
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewProduit(prev => ({ ...prev, image: file }));

      // Créer une prévisualisation
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClientLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewClient(prev => ({ ...prev, logo: file }));

      // Créer une prévisualisation
      const reader = new FileReader();
      reader.onloadend = () => {
        setClientLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCreateProduit = async () => {
    try {
      const result = await produitService.createProduit(newProduit);
      setProduits(prev => [...prev, result]);
      setOpenProduitDialog(false);
      setNewProduit({
        nom: '',
        description: '',
        descriptionDetaille: '',
        prix: 0,
        tauxTVA: 20,
        unite: 'unité',
        category: 'Non classé',
        gestionStock: false,
        quantiteStock: 0,
        seuilAlerte: 5,
        image: null
      });
      setImagePreview(null);
      setProduitTabValue(0);
      setSnackbar({
        open: true,
        message: 'Produit créé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de la création du produit:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la création du produit: ' + error.message,
        severity: 'error'
      });
    }
  };

  const handleOpenEmailDialog = () => {
    if (!id || devis.statut === 'BROUILLON') return;
    setOpenEmailDialog(true);
  };

  const handleCloseEmailDialog = () => {
    setOpenEmailDialog(false);
  };

  const handleSendEmail = async (emailData) => {
    try {
      if (!id) return;

      await devisService.sendEmail(id, emailData);

      setSnackbar({
        open: true,
        message: 'Email envoyé avec succès',
        severity: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      setSnackbar({
        open: true,
        message: `Erreur lors de l'envoi de l'email: ${error.message}`,
        severity: 'error'
      });
      throw error;
    }
  };

  // Utiliser directement la fonction formatCurrency importée

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress size={60} thickness={4} />
      </Box>
    );
  }

  return (
    <Box
      component={motion.div}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      sx={{
        p: { xs: 1, md: 3 },
        backgroundColor: alpha(theme.palette.background.default, 0.7)
      }}
    >
      {/* Header Section */}
      <Paper
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          p: 2,
          mb: 3,
          borderRadius: 2,
          background: `linear-gradient(to right, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.main, 0.8)})`,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              onClick={() => {
                if (currentUser && currentUser.role === 'VENDEUR') {
                  navigate('/vendeur/devis');
                } else if (currentUser && currentUser.role === 'ADMIN') {
                  navigate('/admin/devis');
                } else if (currentUser && currentUser.role === 'RESPONSABLE') {
                  navigate('/responsable/devis');
                } else {
                  navigate('/devis');
                }
              }}
              sx={{
                mr: 2,
                color: 'white',
                backgroundColor: alpha('#fff', 0.2),
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.3),
                }
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: 'white' }}>
                {id ? 'Modifier le devis' : 'Nouveau devis'}
              </Typography>
              <Typography variant="subtitle1" sx={{ color: alpha('#fff', 0.9), mt: 0.5 }}>
                {id ? 'Modifiez les détails de votre devis' : 'Créez un nouveau devis pour votre client'}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={() => handleSave(true)}
              disabled={saving}
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: alpha('#fff', 0.1),
                }
              }}
            >
              Enregistrer comme brouillon
            </Button>
            <Button
              variant="contained"
              startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
              onClick={() => handleSave(false)}
              disabled={saving || !devis.client || !devis.numéro || !devis.dateValidite}
              sx={{
                backgroundColor: 'white',
                color: theme.palette.primary.main,
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.9),
                }
              }}
            >
              {saving ? 'Enregistrement...' : 'Finaliser le devis'}
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Tabs Section */}
      <Paper
        component={motion.div}
        variants={itemVariants}
        elevation={2}
        sx={{
          mb: 3,
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
          sx={{
            '& .MuiTab-root': {
              py: 2,
              fontSize: '1rem',
              fontWeight: 500,
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.05),
              },
            },
            '& .Mui-selected': {
              fontWeight: 600,
            }
          }}
        >
          <Tab
            icon={<InfoIcon sx={{ mr: 1 }} />}
            label="Informations"
            iconPosition="start"
          />
          <Tab
            icon={<VisibilityIcon sx={{ mr: 1 }} />}
            label="Aperçu"
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Informations générales
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Numéro de devis
                    </Typography>
                    <TextField
                      name="numero"
                      value={devis.numéro}
                      onChange={handleChange}
                      fullWidth
                      disabled
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                      error={!devis.numéro}
                      helperText={!devis.numéro ? 'Ce champ est obligatoire' : ''}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Date d'émission
                    </Typography>
                    <TextField
                      name="dateEmission"
                      type="date"
                      value={devis.dateEmission}
                      onChange={handleChange}
                      fullWidth
                      variant="outlined"
                      InputLabelProps={{ shrink: true }}
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Date de validité
                    </Typography>
                    <TextField
                      name="dateValidite"
                      type="date"
                      value={devis.dateValidite}
                      onChange={handleChange}
                      fullWidth
                      variant="outlined"
                      InputLabelProps={{ shrink: true }}
                      required
                      error={!devis.dateValidite}
                      helperText={!devis.dateValidite ? 'Ce champ est obligatoire' : ''}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Client
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Autocomplete
                        options={clients}
                        getOptionLabel={(option) => option.nom || ''}
                        value={devis.client}
                        onChange={handleClientChange}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Sélectionnez un client"
                            fullWidth
                            required
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white',
                                borderRadius: 1.5,
                              }
                            }}
                          />
                        )}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                              <Typography variant="body1" fontWeight={500}>
                                {option.nom}
                              </Typography>
                              {option.email && (
                                <Typography variant="body2" color="text.secondary">
                                  {option.email}
                                </Typography>
                              )}
                            </Box>
                          </li>
                        )}
                        sx={{ flex: 1 }}
                      />
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => setOpenClientDialog(true)}
                        sx={{
                          height: 56,
                          minWidth: 56,
                          width: 56,
                          borderRadius: '50%'
                        }}
                      >
                        <AddIcon />
                      </Button>
                    </Box>
                    {!devis.client && (
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mt: 1,
                        color: theme.palette.warning.main
                      }}>
                        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Veuillez sélectionner un client pour le devis
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ShoppingCartIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Produits et services
                </Typography>
                <Box sx={{ flexGrow: 1 }} />
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddLigne}
                  sx={{
                    borderRadius: 8,
                    px: 2,
                    py: 1,
                    boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                    '&:hover': {
                      boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
                    }
                  }}
                >
                  Ajouter une ligne
                </Button>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <TableContainer sx={{
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                mb: 3,
                overflow: 'hidden'
              }}>
                <Table>
                  <TableHead sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>Produit/Service</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Quantité</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Prix unitaire HT</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Total HT</TableCell>
                      <TableCell width="50"></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {devis.lignes.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 2 }}>
                            <ShoppingCartIcon
                              sx={{
                                fontSize: 48,
                                color: alpha(theme.palette.text.secondary, 0.5),
                                mb: 2
                              }}
                            />
                            <Typography variant="body1" color="text.secondary" gutterBottom>
                              Aucun produit ou service ajouté
                            </Typography>
                            <Button
                              variant="outlined"
                              startIcon={<AddIcon />}
                              onClick={handleAddLigne}
                              sx={{ mt: 1 }}
                            >
                              Ajouter votre premier produit
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : (
                      devis.lignes.map((ligne, index) => (
                        <TableRow
                          key={index}
                          sx={{
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.02)
                            },
                            transition: 'background-color 0.2s'
                          }}
                        >
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Autocomplete
                                options={produits}
                                getOptionLabel={(option) => option.nom || ''}
                                value={ligne.produit}
                                onChange={(e, newValue) => handleLigneChange(index, 'produit', newValue)}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    size="small"
                                    fullWidth
                                    placeholder="Sélectionner un produit"
                                    sx={{
                                      '& .MuiOutlinedInput-root': {
                                        backgroundColor: 'white',
                                        borderRadius: 1.5,
                                      }
                                    }}
                                  />
                                )}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                      <Typography variant="body1" fontWeight={500}>
                                        {option.nom}
                                      </Typography>
                                      <Typography variant="body2" color="text.secondary">
                                        {formatCurrencyUtil(option.prix || 0)}
                                      </Typography>
                                    </Box>
                                  </li>
                                )}
                                sx={{ width: 200 }}
                                isOptionEqualToValue={(option, value) => option._id === (value?._id || value)}
                              />
                              {index === 0 && (
                                <IconButton
                                  size="small"
                                  onClick={() => setOpenProduitDialog(true)}
                                  sx={{
                                    color: theme.palette.primary.main,
                                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                    '&:hover': {
                                      backgroundColor: alpha(theme.palette.primary.main, 0.2),
                                    }
                                  }}
                                >
                                  <AddIcon fontSize="small" />
                                </IconButton>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <TextField
                              value={ligne.description}
                              onChange={(e) => handleLigneChange(index, 'description', e.target.value)}
                              size="small"
                              fullWidth
                              placeholder="Description du produit ou service"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: 1.5,
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <TextField
                              type="number"
                              value={ligne.quantite}
                              onChange={(e) => handleLigneChange(index, 'quantite', parseFloat(e.target.value) || 0)}
                              size="small"
                              InputProps={{ inputProps: { min: 0, step: 1 } }}
                              sx={{
                                width: 80,
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: 1.5,
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <TextField
                              type="number"
                              value={ligne.prixUnitaire}
                              onChange={(e) => handleLigneChange(index, 'prixUnitaire', parseFloat(e.target.value) || 0)}
                              size="small"
                              InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                              sx={{
                                width: 100,
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: 1.5,
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Typography fontWeight={500}>
                              {formatCurrencyUtil(ligne.montantHT || 0)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => handleRemoveLigne(index)}
                              sx={{
                                color: theme.palette.error.main,
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.error.main, 0.1),
                                }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box
                sx={{
                  mt: 3,
                  display: 'flex',
                  justifyContent: 'flex-end',
                  p: 2,
                  borderRadius: 2,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Grid container spacing={2} sx={{ maxWidth: 400 }}>
                  <Grid item xs={6}>
                    <Typography variant="body1" color="text.secondary">Total HT:</Typography>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1" fontWeight={500}>{formatCurrencyUtil(devis.montantHT)}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body1" color="text.secondary" sx={{ mr: 1 }}>TVA:</Typography>
                      <TextField
                        type="number"
                        name="tauxTVA"
                        value={devis.tauxTVA}
                        onChange={handleChange}
                        size="small"
                        InputProps={{
                          inputProps: { min: 0, max: 100, step: 0.1 },
                          endAdornment: <Typography variant="body2">%</Typography>
                        }}
                        sx={{
                          width: 80,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                          }
                        }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'right' }}>
                    <Typography variant="body1" fontWeight={500}>
                      {formatCurrencyUtil(devis.montantHT * (devis.tauxTVA / 100))}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>Total TTC:</Typography>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'right' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                      {formatCurrencyUtil(devis.montantTTC)}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Paper>

            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotesIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Notes et conditions
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                  Notes pour le client
                </Typography>
                <TextField
                  name="notes"
                  multiline
                  rows={4}
                  value={devis.notes}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Ex: Ce devis est valable 30 jours. Merci pour votre confiance."
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: 1.5,
                    }
                  }}
                />
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper
              component={motion.div}
              variants={itemVariants}
              elevation={2}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InfoIcon
                  sx={{
                    mr: 2,
                    color: theme.palette.primary.main,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    p: 1,
                    borderRadius: '50%',
                    fontSize: 32
                  }}
                />
                <Typography variant="h6" fontWeight={600}>
                  Statut et actions
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box
                sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
              >
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                  Statut
                </Typography>
                <FormControl fullWidth>
                  <Select
                    name="statut"
                    value={devis.statut ? devis.statut.toLowerCase() : 'brouillon'}
                    onChange={handleChange}
                    displayEmpty
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                    renderValue={(value) => (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 10,
                            height: 10,
                            borderRadius: '50%',
                            mr: 1,
                            backgroundColor:
                              value === 'brouillon' ? theme.palette.grey[500] :
                              value === 'envoyé' ? theme.palette.info.main :
                              theme.palette.grey[500]
                          }}
                        />
                        {value.charAt(0).toUpperCase() + value.slice(1)}
                      </Box>
                    )}
                  >
                    <MenuItem value="brouillon">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 10,
                            height: 10,
                            borderRadius: '50%',
                            mr: 1,
                            backgroundColor: theme.palette.grey[500]
                          }}
                        />
                        Brouillon
                      </Box>
                    </MenuItem>
                    <MenuItem value="envoyé">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 10,
                            height: 10,
                            borderRadius: '50%',
                            mr: 1,
                            backgroundColor: theme.palette.info.main
                          }}
                        />
                        Envoyé
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Box>
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 2, color: theme.palette.text.secondary }}>
                  Actions disponibles
                </Typography>
                <Stack spacing={2}>
                  <Button
                    variant="outlined"
                    startIcon={<VisibilityIcon />}
                    fullWidth
                    onClick={() => setTabValue(1)}
                    sx={{
                      borderRadius: 8,
                      py: 1.2,
                      borderColor: theme.palette.primary.main,
                      color: theme.palette.primary.main,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.05),
                      }
                    }}
                  >
                    Prévisualiser
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    fullWidth
                    disabled={!id}
                    onClick={() => id && devisService.generatePdf(id)}
                    sx={{
                      borderRadius: 8,
                      py: 1.2,
                      borderColor: theme.palette.success.main,
                      color: theme.palette.success.main,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.success.main, 0.05),
                      }
                    }}
                  >
                    Télécharger PDF
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<EmailIcon />}
                    fullWidth
                    disabled={!id || devis.statut === 'BROUILLON'}
                    onClick={handleOpenEmailDialog}
                    sx={{
                      borderRadius: 8,
                      py: 1.2,
                      borderColor: theme.palette.info.main,
                      color: theme.palette.info.main,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.info.main, 0.05),
                      }
                    }}
                  >
                    Envoyer par email
                  </Button>
                </Stack>
              </Box>
            </Paper>

            {devis.client && (
              <Paper
                component={motion.div}
                variants={itemVariants}
                elevation={2}
                sx={{
                  p: 3,
                  mb: 3,
                  borderRadius: 2,
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PersonIcon
                    sx={{
                      mr: 2,
                      color: theme.palette.primary.main,
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      p: 1,
                      borderRadius: '50%',
                      fontSize: 32
                    }}
                  />
                  <Typography variant="h6" fontWeight={600}>
                    Informations client
                  </Typography>
                </Box>
                <Divider sx={{ mb: 3 }} />

                <Box
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.default, 0.5),
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        width: 40,
                        height: 40,
                        mr: 2
                      }}
                    >
                      {devis.client.nom ? devis.client.nom.charAt(0).toUpperCase() : 'C'}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {devis.client.nom}
                      </Typography>
                      {devis.client.email && (
                        <Typography variant="body2" color="text.secondary">
                          {devis.client.email}
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" fontWeight={600} color="text.secondary">
                        Adresse
                      </Typography>
                      <Typography variant="body2">
                        {devis.client.adresse}<br />
                        {devis.client.codePostal} {devis.client.ville}<br />
                        {devis.client.pays}
                      </Typography>
                    </Grid>

                    {devis.client.telephone && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" fontWeight={600} color="text.secondary">
                          Téléphone
                        </Typography>
                        <Typography variant="body2">
                          {devis.client.telephone}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              </Paper>
            )}
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <DocumentPreview
          type="devis"
          companyData={companyData}
          document={devis}
          onEmail={handleOpenEmailDialog}
        />
      )}

      <Dialog
        open={openClientDialog}
        onClose={() => setOpenClientDialog(false)}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          }
        }}
      >
        <DialogTitle sx={{ pb: 1, pt: 3 }}>
          <Typography variant="h6" component="span" fontWeight={600}>
            Nouveau Client
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box sx={{
                width: 120,
                height: 120,
                borderRadius: '50%',
                bgcolor: 'grey.200',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 2,
                overflow: 'hidden',
                position: 'relative'
              }}>
                {clientLogoPreview ? (
                  <img
                    src={clientLogoPreview}
                    alt="Logo du client"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  <PersonIcon sx={{ fontSize: 60, color: 'grey.400' }} />
                )}
              </Box>

              <Button
                variant="outlined"
                component="label"
                size="small"
                startIcon={<PhotoCameraIcon />}
                sx={{ borderRadius: 2 }}
              >
                {clientLogoPreview ? 'Changer la photo' : 'Ajouter une photo'}
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={handleClientLogoChange}
                />
              </Button>

              <Box sx={{ mt: 3, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ mr: 1 }}>Client actif</Typography>
                <Switch
                  checked={newClient.actif}
                  onChange={(e) => setNewClient(prev => ({ ...prev, actif: e.target.checked }))}
                  color="primary"
                />
              </Box>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Nom
                  </Typography>
                  <TextField
                    name="nom"
                    placeholder="Nom complet du client ou de l'entreprise"
                    value={newClient.nom}
                    onChange={(e) => setNewClient(prev => ({ ...prev, nom: e.target.value }))}
                    fullWidth
                    required
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Adresse du client
                  </Typography>
                  <TextField
                    name="adresse"
                    placeholder="Adresse complète"
                    value={newClient.adresse}
                    onChange={(e) => setNewClient(prev => ({ ...prev, adresse: e.target.value }))}
                    fullWidth
                    multiline
                    rows={2}
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Code postal
                  </Typography>
                  <TextField
                    name="codePostal"
                    placeholder="Code postal"
                    value={newClient.codePostal}
                    onChange={(e) => setNewClient(prev => ({ ...prev, codePostal: e.target.value }))}
                    fullWidth
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Ville
                  </Typography>
                  <TextField
                    name="ville"
                    placeholder="Ville"
                    value={newClient.ville}
                    onChange={(e) => setNewClient(prev => ({ ...prev, ville: e.target.value }))}
                    fullWidth
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Téléphone
                  </Typography>
                  <TextField
                    name="telephone"
                    placeholder="Numéro de téléphone"
                    value={newClient.telephone}
                    onChange={(e) => setNewClient(prev => ({ ...prev, telephone: e.target.value }))}
                    fullWidth
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                {/* Afficher le sélecteur de vendeur seulement si l'utilisateur n'est pas un vendeur */}
                {currentUser && currentUser.role !== 'VENDEUR' && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Vendeur assigné
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        value={newClient.vendeurId || ''}
                        onChange={(e) => setNewClient(prev => ({ ...prev, vendeurId: e.target.value || null }))}
                        displayEmpty
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                          }
                        }}
                      >
                        <MenuItem value="">
                          <em>Aucun vendeur assigné</em>
                        </MenuItem>
                        {loadingVendeurs ? (
                          <MenuItem disabled>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <CircularProgress size={20} sx={{ mr: 1 }} />
                              Chargement des vendeurs...
                            </Box>
                          </MenuItem>
                        ) : vendeurs.length > 0 ? (
                          vendeurs.map((vendeur) => (
                            <MenuItem key={vendeur._id} value={vendeur._id}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Avatar
                                  src={vendeur.profileImage ? `http://localhost:5000${vendeur.profileImage}` : ''}
                                  sx={{ width: 24, height: 24 }}
                                >
                                  {!vendeur.profileImage && (vendeur.nom ? vendeur.nom.charAt(0) : 'V')}
                                </Avatar>
                                {vendeur.nom || vendeur.email || 'Vendeur sans nom'}
                              </Box>
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem disabled>
                            <em>Aucun vendeur disponible</em>
                          </MenuItem>
                        )}
                      </Select>
                    </FormControl>
                    {vendeurs.length === 0 && !loadingVendeurs && (
                      <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', color: theme.palette.info.main }}>
                        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Aucun vendeur n'est disponible pour l'assignation
                        </Typography>
                      </Box>
                    )}
                  </Grid>
                )}
                {/* Si l'utilisateur est un vendeur, afficher un message informatif */}
                {currentUser && currentUser.role === 'VENDEUR' && (
                  <Grid item xs={12}>
                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', color: theme.palette.info.main, p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1 }}>
                      <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="body2">
                        Ce client sera automatiquement assigné à votre compte vendeur
                      </Typography>
                    </Box>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Email
                  </Typography>
                  <TextField
                    name="email"
                    placeholder="<EMAIL>"
                    value={newClient.email}
                    onChange={(e) => setNewClient(prev => ({ ...prev, email: e.target.value }))}
                    fullWidth
                    type="email"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                    Numéro CIN
                  </Typography>
                  <TextField
                    name="cin"
                    placeholder="Numéro de Carte d'Identité Nationale (8 chiffres)"
                    value={newClient.cin}
                    onChange={(e) => setNewClient(prev => ({ ...prev, cin: e.target.value }))}
                    fullWidth
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: 1.5,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Switch
                      checked={newClient.createUserAccount}
                      onChange={(e) => setNewClient(prev => ({ ...prev, createUserAccount: e.target.checked }))}
                      color="primary"
                    />
                    <Typography variant="body1" sx={{ ml: 1 }}>
                      Créer un compte utilisateur pour ce client
                    </Typography>
                  </Box>
                </Grid>

                {newClient.createUserAccount && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Mot de passe
                    </Typography>
                    <TextField
                      name="motDePasse"
                      placeholder="Mot de passe pour le compte utilisateur"
                      value={newClient.motDePasse}
                      onChange={(e) => setNewClient(prev => ({ ...prev, motDePasse: e.target.value }))}
                      fullWidth
                      type={showPassword ? 'text' : 'password'}
                      variant="outlined"
                      InputProps={{
                        endAdornment: (
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        )
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>
                )}

                {/* Champ contact caché mais nécessaire pour la validation */}
                <Grid item xs={12} sx={{ display: 'none' }}>
                  <TextField
                    name="contact"
                    value={newClient.contact}
                    onChange={(e) => setNewClient(prev => ({ ...prev, contact: e.target.value }))}
                    fullWidth
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setOpenClientDialog(false)}
            variant="outlined"
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.secondary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                backgroundColor: alpha(theme.palette.grey[400], 0.05),
              }
            }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateClient}
            disabled={
              !newClient.nom ||
              !newClient.adresse ||
              !newClient.telephone ||
              !newClient.cin ||
              (newClient.createUserAccount && !newClient.motDePasse)
            }
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
              }
            }}
          >
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openProduitDialog}
        onClose={() => {
          setOpenProduitDialog(false);
          setProduitTabValue(0);
        }}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          }
        }}
      >
        <DialogTitle sx={{ pb: 1, pt: 3 }}>
          <Typography variant="h6" component="span" fontWeight={600}>
            Nouveau Produit
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Tabs
            value={produitTabValue}
            onChange={(e, newValue) => setProduitTabValue(newValue)}
            sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Informations générales" />
            <Tab label="Description détaillée" />
            <Tab label="Gestion des stocks" />
          </Tabs>

          {produitTabValue === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Nom du produit
                    </Typography>
                    <TextField
                      name="nom"
                      placeholder="Nom du produit ou service"
                      value={newProduit.nom}
                      onChange={(e) => setNewProduit((prev) => ({ ...prev, nom: e.target.value }))}
                      fullWidth
                      required
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Description courte
                    </Typography>
                    <TextField
                      name="description"
                      placeholder="Brève description pour les listes et aperçus"
                      value={newProduit.description}
                      onChange={(e) => setNewProduit((prev) => ({ ...prev, description: e.target.value }))}
                      fullWidth
                      multiline
                      rows={2}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Catégorie
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        value={newProduit.category}
                        onChange={(e) => setNewProduit(prev => ({ ...prev, category: e.target.value }))}
                        displayEmpty
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                          }
                        }}
                      >
                        <MenuItem value="Non classé">Non classé</MenuItem>
                        <MenuItem value="Service">Service</MenuItem>
                        <MenuItem value="Produit physique">Produit physique</MenuItem>
                        <MenuItem value="Logiciel">Logiciel</MenuItem>
                        <MenuItem value="Abonnement">Abonnement</MenuItem>
                        <MenuItem value="Formation">Formation</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Prix unitaire
                    </Typography>
                    <TextField
                      name="prix"
                      placeholder="0.00"
                      type="number"
                      value={newProduit.prix}
                      onChange={(e) =>
                        setNewProduit((prev) => ({ ...prev, prix: parseFloat(e.target.value) || 0 }))
                      }
                      fullWidth
                      InputProps={{
                        inputProps: { min: 0, step: 0.01 },
                        startAdornment: (
                          <Typography variant="body1" color={theme.palette.text.secondary} sx={{ mr: 1 }}>
                            TND
                          </Typography>
                        ),
                      }}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                    {newProduit.prix <= 0 && (
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mt: 1,
                        color: theme.palette.warning.main
                      }}>
                        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Le prix doit être supérieur à 0
                        </Typography>
                      </Box>
                    )}
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                      Unité
                    </Typography>
                    <TextField
                      name="unite"
                      placeholder="unité"
                      value={newProduit.unite}
                      onChange={(e) => setNewProduit((prev) => ({ ...prev, unite: e.target.value }))}
                      fullWidth
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                        }
                      }}
                    />
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Box sx={{
                  width: 200,
                  height: 200,
                  bgcolor: theme.palette.grey[100],
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 2,
                  borderRadius: 2,
                  overflow: 'hidden',
                  position: 'relative'
                }}>
                  {imagePreview ? (
                    <img
                      src={imagePreview}
                      alt="Aperçu du produit"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain'
                      }}
                    />
                  ) : (
                    <Typography variant="body2" color={theme.palette.text.secondary}>
                      Aucune image
                    </Typography>
                  )}
                </Box>

                <Button
                  variant="outlined"
                  component="label"
                  size="small"
                  startIcon={<PhotoCameraIcon />}
                  sx={{ borderRadius: 2 }}
                >
                  {imagePreview ? 'Changer l\'image' : 'Ajouter une image'}
                  <input
                    type="file"
                    accept="image/*"
                    hidden
                    onChange={handleImageChange}
                  />
                </Button>
              </Grid>
            </Grid>
          )}

          {produitTabValue === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                  Description détaillée
                </Typography>
                <TextField
                  name="descriptionDetaille"
                  placeholder="Description complète du produit ou service"
                  value={newProduit.descriptionDetaille}
                  onChange={(e) => setNewProduit((prev) => ({ ...prev, descriptionDetaille: e.target.value }))}
                  fullWidth
                  multiline
                  rows={8}
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: 1.5,
                    }
                  }}
                />
                <Typography variant="body2" color={theme.palette.text.secondary} sx={{ mt: 1 }}>
                  Cette description détaillée sera visible sur les fiches produit et les documents détaillés.
                </Typography>
              </Grid>
            </Grid>
          )}

          {produitTabValue === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Switch
                    checked={newProduit.gestionStock}
                    onChange={(e) => setNewProduit(prev => ({ ...prev, gestionStock: e.target.checked }))}
                    color="primary"
                  />
                  <Typography variant="subtitle1" sx={{ ml: 1 }}>
                    Activer la gestion des stocks
                  </Typography>
                </Box>

                {newProduit.gestionStock && (
                  <>
                    <Grid container spacing={3}>
                      <Grid item xs={6}>
                        <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                          Quantité en stock
                        </Typography>
                        <TextField
                          name="quantiteStock"
                          type="number"
                          value={newProduit.quantiteStock}
                          onChange={(e) => setNewProduit((prev) => ({ ...prev, quantiteStock: parseInt(e.target.value) || 0 }))}
                          fullWidth
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                          }}
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                              borderRadius: 1.5,
                            }
                          }}
                        />
                      </Grid>

                      <Grid item xs={6}>
                        <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: theme.palette.text.secondary }}>
                          Seuil d'alerte
                        </Typography>
                        <TextField
                          name="seuilAlerte"
                          type="number"
                          value={newProduit.seuilAlerte}
                          onChange={(e) => setNewProduit((prev) => ({ ...prev, seuilAlerte: parseInt(e.target.value) || 0 }))}
                          fullWidth
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                          }}
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                              borderRadius: 1.5,
                            }
                          }}
                        />
                        <Typography variant="body2" color={theme.palette.text.secondary} sx={{ mt: 1 }}>
                          Vous recevrez une alerte lorsque le stock sera inférieur à ce seuil.
                        </Typography>
                      </Grid>
                    </Grid>
                  </>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => {
              setOpenProduitDialog(false);
              setProduitTabValue(0);
            }}
            variant="outlined"
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.secondary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                backgroundColor: alpha(theme.palette.grey[400], 0.05),
              }
            }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateProduit}
            disabled={!newProduit.nom || newProduit.prix <= 0}
            sx={{
              borderRadius: 8,
              px: 3,
              py: 1,
              boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,118,255,0.23)'
              }
            }}
          >
            Enregistrer
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
          action={
            snackbar.severity === 'success' && (
              <Button
                color="inherit"
                size="small"
                onClick={() => {
                  if (currentUser && currentUser.role === 'VENDEUR') {
                    navigate('/vendeur/devis');
                  } else if (currentUser && currentUser.role === 'ADMIN') {
                    navigate('/admin/devis');
                  } else {
                    navigate('/devis');
                  }
                }}
              >
                Voir la liste
              </Button>
            )
          }
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Email Dialog */}
      <EmailDialog
        open={openEmailDialog}
        onClose={handleCloseEmailDialog}
        onSend={handleSendEmail}
        documentType="devis"
        documentNumber={devis.numéro}
        recipientEmail={devis.client?.email}
      />
    </Box>
  );
};

export default DevisForm;