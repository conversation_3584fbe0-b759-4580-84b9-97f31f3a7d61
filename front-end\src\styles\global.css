/* Reset et styles de base */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    margin: 0;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--background-default);
    color: var(--text-primary);
    line-height: 1.5;
  }
  
  /* Styles pour les liens */
  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  
  a:hover {
    color: var(--primary-dark);
  }
  
  /* Styles pour les boutons */
  button {
    cursor: pointer;
    font-family: inherit;
  }
  
  /* Styles pour les formulaires */
  input,
  textarea,
  select {
    font-family: inherit;
    font-size: inherit;
  }
  
  /* Classes utilitaires */
  .text-center {
    text-align: center;
  }
  
  .text-right {
    text-align: right;
  }
  
  .text-left {
    text-align: left;
  }
  
  .full-width {
    width: 100%;
  }
  
  .mt-1 { margin-top: 0.25rem; }
  .mt-2 { margin-top: 0.5rem; }
  .mt-3 { margin-top: 1rem; }
  .mt-4 { margin-top: 1.5rem; }
  .mt-5 { margin-top: 2rem; }
  
  .mb-1 { margin-bottom: 0.25rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-3 { margin-bottom: 1rem; }
  .mb-4 { margin-bottom: 1.5rem; }
  .mb-5 { margin-bottom: 2rem; }
  
  /* Styles pour les impressions */
  @media print {
    body * {
      visibility: hidden;
    }
    .printable, .printable * {
      visibility: visible;
    }
    .printable {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      background-color: white;
      padding: 20px;
    }
    .no-print {
      display: none !important;
    }
  }
  
  /* Animation pour les transitions */
  .fade-enter {
    opacity: 0;
  }
  .fade-enter-active {
    opacity: 1;
    transition: opacity 300ms ease-in;
  }
  .fade-exit {
    opacity: 1;
  }
  .fade-exit-active {
    opacity: 0;
    transition: opacity 300ms ease-out;
  }