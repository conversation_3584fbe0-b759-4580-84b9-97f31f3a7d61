const express = require('express');
const router = express.Router();
const BonLivraison = require('../models/BonLivraisonModel');
const Livreur = require('../models/LivreurModel');
const Client = require('../models/ClientModel');
const User = require('../models/UserModel');
const { verifyToken, isResponsable, isVendeur } = require('../middleware/authMiddleware');

// Middleware pour vérifier que l'utilisateur peut accéder aux bons de livraison
const canAccessBonLivraison = [verifyToken, (req, res, next) => {
    if (req.userRole === 'RESPONSABLE' || req.userRole === 'VENDEUR' || req.userRole === 'ADMIN') {
        next();
    } else {
        return res.status(403).json({ message: 'Accès non autorisé' });
    }
}];

// GET /api/bon-livraisons - Récupérer tous les bons de livraison
router.get('/bon-livraisons', canAccessBonLivraison, async (req, res) => {
    try {
        let query = {};

        // Si l'utilisateur est un responsable, ne montrer que ses bons de livraison
        if (req.userRole === 'RESPONSABLE') {
            query.responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            // Pour les vendeurs, montrer les bons qu'ils ont créés ou ceux de leur responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                const responsableIds = vendeur.responsables.map(r => r._id);
                query.$or = [
                    { vendeurId: req.userId },
                    { responsableId: { $in: responsableIds } }
                ];
            } else {
                query.vendeurId = req.userId;
            }
        }

        const { statut, livreurId, clientId, dateDebut, dateFin, search } = req.query;

        // Filtres optionnels
        if (statut) {
            query.statut = statut;
        }
        if (livreurId) {
            query.livreurId = livreurId;
        }
        if (clientId) {
            query.clientId = clientId;
        }
        if (dateDebut || dateFin) {
            query.dateLivraison = {};
            if (dateDebut) query.dateLivraison.$gte = new Date(dateDebut);
            if (dateFin) query.dateLivraison.$lte = new Date(dateFin);
        }
        if (search) {
            query.$or = [
                { numero: { $regex: search, $options: 'i' } },
                { 'adresseLivraison.adresse': { $regex: search, $options: 'i' } },
                { notes: { $regex: search, $options: 'i' } }
            ];
        }

        const bonLivraisons = await BonLivraison.find(query)
            .populate('clientId', 'nom email telephone adresse')
            .populate('livreurId', 'nom prenom telephone vehicule')
            .populate('vendeurId', 'nom prenom')
            .populate('responsableId', 'nom prenom')
            .populate('factureId', 'numero')
            .populate('devisId', 'numéro')
            .sort({ dateCreation: -1 });

        res.status(200).json(bonLivraisons);
    } catch (error) {
        console.error('Erreur lors de la récupération des bons de livraison:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération des bons de livraison' });
    }
});

// GET /api/bon-livraisons/:id - Récupérer un bon de livraison par ID
router.get('/bon-livraisons/:id', canAccessBonLivraison, async (req, res) => {
    try {
        const bonLivraison = await BonLivraison.findById(req.params.id)
            .populate('clientId', 'nom email telephone adresse')
            .populate('livreurId', 'nom prenom telephone vehicule')
            .populate('vendeurId', 'nom prenom')
            .populate('responsableId', 'nom prenom')
            .populate('factureId', 'numero')
            .populate('devisId', 'numéro')
            .populate('lignes.produitId', 'nom description');

        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && bonLivraison.responsableId._id.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
        }
        if (req.userRole === 'VENDEUR' && bonLivraison.vendeurId && bonLivraison.vendeurId._id.toString() !== req.userId) {
            // Vérifier si le vendeur appartient au même responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            const responsableIds = vendeur.responsables.map(r => r._id.toString());
            if (!responsableIds.includes(bonLivraison.responsableId._id.toString())) {
                return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
            }
        }

        res.status(200).json(bonLivraison);
    } catch (error) {
        console.error('Erreur lors de la récupération du bon de livraison:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération du bon de livraison' });
    }
});

// POST /api/bon-livraisons - Créer un nouveau bon de livraison
router.post('/bon-livraisons', canAccessBonLivraison, async (req, res) => {
    try {
        const {
            clientId,
            livreurId,
            factureId,
            devisId,
            dateLivraison,
            heureDepart,
            heureArrivee,
            lignes,
            adresseLivraison,
            notes,
            tracking
        } = req.body;

        // Validation des champs obligatoires
        if (!clientId || !livreurId || !dateLivraison || !lignes || lignes.length === 0) {
            return res.status(400).json({
                message: 'Les champs client, livreur, date de livraison et lignes sont obligatoires'
            });
        }

        // Vérifier que le livreur existe et est disponible
        const livreur = await Livreur.findById(livreurId);
        if (!livreur) {
            return res.status(404).json({ message: 'Livreur non trouvé' });
        }
        if (!livreur.estDisponible()) {
            return res.status(400).json({ message: 'Le livreur sélectionné n\'est pas disponible' });
        }

        // Vérifier que le client existe
        const client = await Client.findById(clientId);
        if (!client) {
            return res.status(404).json({ message: 'Client non trouvé' });
        }

        // Déterminer le responsable
        let responsableId;
        if (req.userRole === 'RESPONSABLE') {
            responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                responsableId = vendeur.responsables[0]._id;
            } else {
                return res.status(400).json({ message: 'Vendeur non associé à un responsable' });
            }
        }

        // Générer un numéro automatique
        const numero = await BonLivraison.genererNumero(responsableId);

        const nouveauBonLivraison = new BonLivraison({
            numero,
            clientId,
            livreurId,
            vendeurId: req.userRole === 'VENDEUR' ? req.userId : undefined,
            responsableId,
            factureId,
            devisId,
            dateLivraison,
            heureDepart,
            heureArrivee,
            lignes,
            adresseLivraison: adresseLivraison || {
                adresse: client.adresse,
                ville: client.ville,
                codePostal: client.codePostal
            },
            notes,
            tracking
        });

        const bonLivraisonSauvegarde = await nouveauBonLivraison.save();
        await bonLivraisonSauvegarde.populate([
            { path: 'clientId', select: 'nom email telephone adresse' },
            { path: 'livreurId', select: 'nom prenom telephone vehicule' },
            { path: 'vendeurId', select: 'nom prenom' },
            { path: 'responsableId', select: 'nom prenom' }
        ]);

        res.status(201).json({
            message: 'Bon de livraison créé avec succès',
            bonLivraison: bonLivraisonSauvegarde
        });
    } catch (error) {
        console.error('Erreur lors de la création du bon de livraison:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                message: 'Données invalides',
                errors: Object.values(error.errors).map(e => e.message)
            });
        }
        res.status(500).json({ message: 'Erreur serveur lors de la création du bon de livraison' });
    }
});

// PUT /api/bon-livraisons/:id - Mettre à jour un bon de livraison
router.put('/bon-livraisons/:id', canAccessBonLivraison, async (req, res) => {
    try {
        const bonLivraison = await BonLivraison.findById(req.params.id);

        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && bonLivraison.responsableId.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
        }
        if (req.userRole === 'VENDEUR' && bonLivraison.vendeurId && bonLivraison.vendeurId.toString() !== req.userId) {
            // Vérifier si le vendeur appartient au même responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            const responsableIds = vendeur.responsables.map(r => r._id.toString());
            if (!responsableIds.includes(bonLivraison.responsableId.toString())) {
                return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
            }
        }

        const {
            livreurId,
            dateLivraison,
            heureDepart,
            heureArrivee,
            lignes,
            statut,
            adresseLivraison,
            signatureClient,
            notes,
            observationsLivreur,
            motifEchec,
            tracking,
            photosLivraison
        } = req.body;

        // Vérifier le livreur si changé
        if (livreurId && livreurId !== bonLivraison.livreurId.toString()) {
            const livreur = await Livreur.findById(livreurId);
            if (!livreur) {
                return res.status(404).json({ message: 'Livreur non trouvé' });
            }
            if (!livreur.estDisponible()) {
                return res.status(400).json({ message: 'Le livreur sélectionné n\'est pas disponible' });
            }
            bonLivraison.livreurId = livreurId;
        }

        // Mettre à jour les champs
        if (dateLivraison) bonLivraison.dateLivraison = dateLivraison;
        if (heureDepart !== undefined) bonLivraison.heureDepart = heureDepart;
        if (heureArrivee !== undefined) bonLivraison.heureArrivee = heureArrivee;
        if (lignes) bonLivraison.lignes = lignes;
        if (statut) bonLivraison.statut = statut;
        if (adresseLivraison) bonLivraison.adresseLivraison = { ...bonLivraison.adresseLivraison, ...adresseLivraison };
        if (signatureClient) bonLivraison.signatureClient = signatureClient;
        if (notes !== undefined) bonLivraison.notes = notes;
        if (observationsLivreur !== undefined) bonLivraison.observationsLivreur = observationsLivreur;
        if (motifEchec !== undefined) bonLivraison.motifEchec = motifEchec;
        if (tracking) bonLivraison.tracking = { ...bonLivraison.tracking, ...tracking };
        if (photosLivraison) bonLivraison.photosLivraison = photosLivraison;

        const bonLivraisonMisAJour = await bonLivraison.save();
        await bonLivraisonMisAJour.populate([
            { path: 'clientId', select: 'nom email telephone adresse' },
            { path: 'livreurId', select: 'nom prenom telephone vehicule' },
            { path: 'vendeurId', select: 'nom prenom' },
            { path: 'responsableId', select: 'nom prenom' }
        ]);

        // Mettre à jour les statistiques du livreur si le statut change
        if (statut && (statut === 'LIVREE' || statut === 'ECHEC')) {
            const livreur = await Livreur.findById(bonLivraison.livreurId);
            if (livreur) {
                await livreur.mettreAJourStatistiques(statut === 'LIVREE');
            }
        }

        res.status(200).json({
            message: 'Bon de livraison mis à jour avec succès',
            bonLivraison: bonLivraisonMisAJour
        });
    } catch (error) {
        console.error('Erreur lors de la mise à jour du bon de livraison:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({
                message: 'Données invalides',
                errors: Object.values(error.errors).map(e => e.message)
            });
        }
        res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du bon de livraison' });
    }
});

// DELETE /api/bon-livraisons/:id - Supprimer un bon de livraison
router.delete('/bon-livraisons/:id', canAccessBonLivraison, async (req, res) => {
    try {
        const bonLivraison = await BonLivraison.findById(req.params.id);

        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        // Vérifier les permissions
        if (req.userRole === 'RESPONSABLE' && bonLivraison.responsableId.toString() !== req.userId) {
            return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
        }
        if (req.userRole === 'VENDEUR' && bonLivraison.vendeurId && bonLivraison.vendeurId.toString() !== req.userId) {
            // Vérifier si le vendeur appartient au même responsable
            const vendeur = await User.findById(req.userId).populate('responsables');
            const responsableIds = vendeur.responsables.map(r => r._id.toString());
            if (!responsableIds.includes(bonLivraison.responsableId.toString())) {
                return res.status(403).json({ message: 'Accès non autorisé à ce bon de livraison' });
            }
        }

        // Ne permettre la suppression que si le statut est EN_PREPARATION ou ANNULEE
        if (!['EN_PREPARATION', 'ANNULEE'].includes(bonLivraison.statut)) {
            return res.status(400).json({
                message: 'Impossible de supprimer un bon de livraison en cours ou terminé'
            });
        }

        await BonLivraison.findByIdAndDelete(req.params.id);

        res.status(200).json({ message: 'Bon de livraison supprimé avec succès' });
    } catch (error) {
        console.error('Erreur lors de la suppression du bon de livraison:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la suppression du bon de livraison' });
    }
});

// GET /api/bon-livraisons/statistiques - Récupérer les statistiques des livraisons
router.get('/bon-livraisons/statistiques', canAccessBonLivraison, async (req, res) => {
    try {
        let query = {};

        // Filtrer par responsable si nécessaire
        if (req.userRole === 'RESPONSABLE') {
            query.responsableId = req.userId;
        } else if (req.userRole === 'VENDEUR') {
            const vendeur = await User.findById(req.userId).populate('responsables');
            if (vendeur && vendeur.responsables && vendeur.responsables.length > 0) {
                const responsableIds = vendeur.responsables.map(r => r._id);
                query.$or = [
                    { vendeurId: req.userId },
                    { responsableId: { $in: responsableIds } }
                ];
            } else {
                query.vendeurId = req.userId;
            }
        }

        const { dateDebut, dateFin } = req.query;
        if (dateDebut || dateFin) {
            query.dateLivraison = {};
            if (dateDebut) query.dateLivraison.$gte = new Date(dateDebut);
            if (dateFin) query.dateLivraison.$lte = new Date(dateFin);
        }

        const statistiques = await BonLivraison.aggregate([
            { $match: query },
            {
                $group: {
                    _id: null,
                    totalLivraisons: { $sum: 1 },
                    livraisonsReussies: {
                        $sum: { $cond: [{ $eq: ['$statut', 'LIVREE'] }, 1, 0] }
                    },
                    livraisonsEchouees: {
                        $sum: { $cond: [{ $eq: ['$statut', 'ECHEC'] }, 1, 0] }
                    },
                    livraisonsEnCours: {
                        $sum: { $cond: [{ $eq: ['$statut', 'EN_COURS'] }, 1, 0] }
                    },
                    montantTotalLivre: { $sum: '$montantTotal' },
                    montantMoyen: { $avg: '$montantTotal' }
                }
            }
        ]);

        const stats = statistiques[0] || {
            totalLivraisons: 0,
            livraisonsReussies: 0,
            livraisonsEchouees: 0,
            livraisonsEnCours: 0,
            montantTotalLivre: 0,
            montantMoyen: 0
        };

        // Calculer le taux de réussite
        stats.tauxReussite = stats.totalLivraisons > 0
            ? Math.round((stats.livraisonsReussies / stats.totalLivraisons) * 100)
            : 0;

        res.status(200).json(stats);
    } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la récupération des statistiques' });
    }
});

// PUT /api/bon-livraisons/:id/statut - Mettre à jour uniquement le statut
router.put('/bon-livraisons/:id/statut', canAccessBonLivraison, async (req, res) => {
    try {
        const { statut, motifEchec, observationsLivreur } = req.body;

        if (!statut) {
            return res.status(400).json({ message: 'Le statut est obligatoire' });
        }

        const bonLivraison = await BonLivraison.findById(req.params.id);
        if (!bonLivraison) {
            return res.status(404).json({ message: 'Bon de livraison non trouvé' });
        }

        bonLivraison.statut = statut;
        if (motifEchec) bonLivraison.motifEchec = motifEchec;
        if (observationsLivreur) bonLivraison.observationsLivreur = observationsLivreur;

        await bonLivraison.save();

        // Mettre à jour les statistiques du livreur
        if (statut === 'LIVREE' || statut === 'ECHEC') {
            const livreur = await Livreur.findById(bonLivraison.livreurId);
            if (livreur) {
                await livreur.mettreAJourStatistiques(statut === 'LIVREE');
            }
        }

        res.status(200).json({
            message: 'Statut mis à jour avec succès',
            bonLivraison
        });
    } catch (error) {
        console.error('Erreur lors de la mise à jour du statut:', error);
        res.status(500).json({ message: 'Erreur serveur lors de la mise à jour du statut' });
    }
});

module.exports = router;
