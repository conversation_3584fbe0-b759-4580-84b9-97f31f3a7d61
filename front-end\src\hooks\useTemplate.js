import { useState, useEffect } from 'react';
import { getTemplateSettings } from '../services/templateService';

/**
 * Hook pour gérer les templates de documents
 * @param {string} type - Type de document (Facture/Devis)
 * @returns {Object} - Template et fonctions associées
 */
const useTemplate = (type) => {
  const [template, setTemplate] = useState({
    color: '#f57c00', // BenYounes Web orange color
    layout: 'standard',
    logo: null
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger les paramètres de template depuis la base de données
  useEffect(() => {
    const fetchTemplateSettings = async () => {
      try {
        setLoading(true);
        // Convert type to lowercase for API call
        const documentType = type.toLowerCase() === 'facture' ? 'facture' : 'devis';
        const settings = await getTemplateSettings(documentType);
        if (settings) {
          setTemplate(prev => ({
            ...prev,
            color: settings.color || '#f57c00', // BenYounes Web orange color
            logo: settings.logo || null,
            layout: settings.layout || 'standard',
            type: settings.type || documentType
          }));
        }
      } catch (err) {
        console.error('Erreur lors du chargement des paramètres de template:', err);
        setError('Impossible de charger les paramètres de template');
      } finally {
        setLoading(false);
      }
    };

    fetchTemplateSettings();
  }, [type]);

  // Fonction pour appliquer le style du template
  const applyTemplateStyle = (element) => {
    if (!element) return;

    // Appliquer les styles du template à l'élément
    const styles = {
      '--template-color': template.color || '#f57c00', // BenYounes Web orange color
      fontFamily: 'Inter'
    };

    Object.assign(element.style, styles);
    return styles;
  };

  // Fonction pour générer des styles CSS
  const getTemplateStyles = () => {
    return `
      .template-header {
        background-color: ${template.color || '#f57c00'}; /* BenYounes Web orange color */
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
      }
      .template-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
      }
      .template-table th {
        background-color: ${template.color || '#f57c00'}; /* BenYounes Web orange color */
        color: white;
        padding: 10px;
        text-align: left;
      }
      .template-table td {
        padding: 10px;
        border-bottom: 1px solid #eee;
      }
      ${template.tableStyle === 'striped' ? `
        .template-table tr:nth-child(even) {
          background-color: ${template.secondaryColor || '#f5f5f5'};
        }
      ` : ''}
      ${template.tableStyle === 'bordered' ? `
        .template-table th, .template-table td {
          border: 1px solid #ddd;
        }
      ` : ''}
    `;
  };

  return {
    template,
    loading,
    error,
    applyTemplateStyle,
    getTemplateStyles
  };
};

export default useTemplate;
